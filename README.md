# 相关环境

- <PERSON>dejs 版本: v12.22.10
- vue-cli 版本: v4.5.17

# git 配置

```
git config --global core.autocrlf false
```

# 代码提交规范，参考 git submission specifications

```
1. Submission format
git commit -m <type>[optional scope]: <description>

2. frequently-used type category
type : Used to indicate the type of change we submitted this time. Is it a new function? Or did you modify the test code? Or has the document been updated? Summarize the following 11 types:
• build: The main purpose is to modify the project construction system(for example glup，webpack，rollup Configuration, etc)Submission of
• ci: The main purpose is to modify the project and continue the integration process(for example <PERSON>，<PERSON>，GitLab CI，Circle etc.)Submission of
• docs: Document update
• feat: New features
• fix: bug repair
• perf: performance optimization
• refactor: Refactoring code(There are neither new features nor fixes bug)
• style: Code modification without affecting program logic(Modify blank characters, complete missing semicolons, etc)
• test: Add test cases or update existing tests
• revert: Roll back an earlier commit
• chore: Other types that do not belong to the above types(Daily affairs)
optional scope: An optional modification range. It is used to identify which module in the code is mainly involved in this submission.
description: Describe the main contents of this submission in one sentence and be concise and comprehensive.

For example:
git commit -m 'feat: increase xxx function'
git commit -m 'bug: repair xxx function'n
```
