{"name": "zacg-manage", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:testing": "vue-cli-service build --mode testing", "build:production": "vue-cli-service build --mode production", "build:proTest": "vue-cli-service build --mode proTest", "build:demo": "vue-cli-service build --mode demo", "format-lint": "npm run format && npm run lint", "lint": "vue-cli-service lint", "lint:stylelint": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "format": "prettier . --write", "theme": "node src/styles/theme/theme.js", "cz": "npm run log && git add . && git cz", "log": "conventional-changelog --config ./node_modules/vue-cli-plugin-commitlint/lib/log -i CHANGELOG.md -s -r 0", "deploy:testing": "npm run build:testing && cross-env NODE_ENV=testing node ./deploy", "deploy:production": "npm run build:production && cross-env NODE_ENV=production node ./deploy"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@antv/g6": "^4.7.4", "@logicflow/core": "^1.1.3", "@logicflow/extension": "^1.1.3", "@riophae/vue-treeselect": "^0.4.0", "@tinymce/tinymce-vue": "3.0.1", "awe-dnd": "^0.3.4", "axios": "^0.26.1", "better-scroll": "^2.5.0", "clipboard": "^2.0.10", "core-js": "^3.6.5", "cos-js-sdk-v5": "^1.8.3", "crypto-js": "4.1.1", "dayjs": "^1.11.5", "diff": "5.0.0", "echarts": "^5.4.0", "element-ui": "2.15.8", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "jspdf": "^2.5.1", "moment": "^2.29.4", "nprogress": "0.2.0", "nzh": "^1.0.12", "path-to-regexp": "^6.2.1", "pinyin-pro": "^3.20.4", "qs": "^6.11.0", "relation-graph": "^2.0.22", "screenfull": "5.1.0", "sortablejs": "^1.15.0", "tinymce": "5.1.0", "v-viewer": "1.6.3", "video.js": "^8.3.0", "vue": "^2.6.11", "vue-cropper": "^0.5.10", "vue-echarts": "4.1.0", "vue-json-pretty": "^1.8.2", "vue-router": "^3.2.0", "vuedraggable": "2.24.3", "vuex": "^3.4.0", "xe-utils": "^3.5.7", "xlsx": "^0.18.5"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.17", "@vue/cli-plugin-eslint": "~4.5.17", "@vue/cli-plugin-router": "~4.5.17", "@vue/cli-plugin-vuex": "~4.5.17", "@vue/cli-service": "~4.5.17", "@vue/eslint-config-prettier": "^6.0.0", "autoprefixer": "^8.0.0", "babel-eslint": "^10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "chalk": "^4.1.0", "commitizen": "^4.0.3", "commitlint": "^8.2.0", "compression-webpack-plugin": "^6.1.1", "conventional-changelog-cli": "^2.0.28", "cross-env": "^7.0.3", "element-theme-chalk": "^2.15.6", "element-themex": "^1.0.3", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-vue": "^6.2.2", "husky": "^3.0.9", "lint-staged": "^9.5.0", "ora": "^5.1.0", "postcss": "^8.4.12", "prettier": "^2.2.1", "prettier-webpack-plugin": "^1.2.0", "right-pad": "^1.0.1", "sass": "~1.26.5", "sass-loader": "^8.0.2", "scp2": "^0.5.0", "script-ext-html-webpack-plugin": "^2.1.5", "ssh2": "^1.11.0", "style-resources-loader": "^1.5.0", "stylelint": "^13.13.1", "stylelint-config-html": "^1.0.0", "stylelint-config-prettier": "^9.0.3", "stylelint-config-standard": "^22.0.0", "stylelint-order": "^4.1.0", "stylelint-scss": "^4.2.0", "stylelint-webpack-plugin": "^3.1.1", "svg-sprite-loader": "^6.0.11", "vue-cli-plugin-commitlint": "~1.0.12", "vue-cli-plugin-style-resources-loader": "^0.1.5", "vue-template-compiler": "^2.6.11"}, "config": {"commitizen": {"path": "./node_modules/vue-cli-plugin-commitlint/lib/cz"}}, "gitHooks": {"pre-commit": "lint-staged"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}, "main": "index.js", "repository": "*********************:624667fb9491f3d1689b6ed6/parallel-cloud-platform/parallel-cloud-web/zacg-manage.git", "author": "【名字】 <<EMAIL>>", "license": "MIT", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}