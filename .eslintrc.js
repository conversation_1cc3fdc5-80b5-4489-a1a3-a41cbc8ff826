module.exports = {
  root: true,
  env: {
    node: true
  },
  extends: ['plugin:vue/essential', 'eslint:recommended', '@vue/prettier'],
  parserOptions: {
    parser: 'babel-eslint'
  },
  globals: {
    AMap: true
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'prettier/prettier': 'off',
    'no-cond-assign': ['error', 'always'],
    'no-constant-condition': [
      'error',
      {
        checkLoops: true
      }
    ],
    'no-control-regex': ['error'],
    'no-dupe-args': ['error'],
    'no-dupe-keys': ['error'],
    'no-duplicate-case': ['error'],
    'no-empty': [
      'error',
      {
        allowEmptyCatch: true
      }
    ],
    'no-empty-character-class': ['error'],
    'no-ex-assign': ['error'],
    'no-extra-boolean-cast': ['error'],
    'no-extra-semi': 'off',
    'no-func-assign': ['warn'],
    'no-inner-declarations': ['error'],
    'no-invalid-regexp': [
      'error',
      {
        allowConstructorFlags: []
      }
    ],
    'no-irregular-whitespace': ['error'],
    'no-obj-calls': ['error'],
    'no-regex-spaces': ['error'],
    'no-sparse-arrays': ['error'],
    'no-unexpected-multiline': ['error'],
    'no-unsafe-finally': ['error'],
    'no-unsafe-negation': ['error'],
    'use-isnan': ['error'],
    'default-case': ['error'],
    'dot-notation': ['error'],
    eqeqeq: ['warn'],
    'no-caller': ['error'],
    'no-case-declarations': ['error'],
    'no-empty-function': ['error'],
    'no-empty-pattern': 'off',
    'no-eval': ['error'],
    'no-global-assign': ['error'],
    'no-redeclare': [
      'error',
      {
        builtinGlobals: true
      }
    ],
    'no-self-assign': [
      'error',
      {
        props: true
      }
    ],
    'no-unused-labels': ['error'],
    'no-useless-escape': ['error'],
    radix: 'off',
    'no-delete-var': ['error'],
    'no-undef': ['error'],
    'no-unused-vars': ['error'],
    'no-use-before-define': ['error'],
    'arrow-spacing': [
      'error',
      {
        before: true,
        after: true
      }
    ],
    'no-var': ['error'],
    'object-shorthand': ['error', 'always'],
    'prefer-arrow-callback': [
      'error',
      {
        allowNamedFunctions: false
      }
    ]
  }
}
