import btnDisposeFn from '@/router/modules/btns'

const partyRoutes = [
  {
    path: '/party',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/party/importantNews',
    meta: {
      title: '党建要闻',
      icon: 'contract-setting',
      role: 'partyManage',
      breadcrumb: false
    },
    children: [
      {
        path: 'importantNews',
        name: 'ImportantNews',
        component: resolve =>
          require([
            '@/views/manage/party/importantNews/importantNews-basic'
          ], resolve),
        meta: {
          title: '党建要闻',
          icon: 'contract-setting',
          role: 'partyManage:importantNews',
          btns: btnDisposeFn('partyManage:importantNews', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      }
    ]
  },
  {
    path: '/orgManage',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/orgManage/branchManage',
    meta: {
      title: '组织管理',
      icon: 'communist',
      role: 'partyManage:orgManage',
    },
    children: [
      {
        path: 'entProfile',
        name: 'EntProfile',
        component: resolve =>
          require([
            '@/views/manage/party/entProfile/entProfile-basic'
          ], resolve),
        meta: {
          title: '党委概况',
          icon: 'data',
          role: 'partyManage:orgManage:entProfile',
        }
      },
      {
        path: 'branchManage',
        name: 'BranchManage',
        component: resolve =>
          require([
            '@/views/manage/party/branchManage/branchManage-basic'
          ], resolve),
        meta: {
          title: '支部管理',
          icon: 'data',
          role: 'partyManage:orgManage:branchManage',
          btns: btnDisposeFn('partyManage:orgManage:branchManage', [
            'ADD',
            'SORT',
            'VIEW',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      },
      {
        path: 'partyPragmatic',
        name: 'PartyPragmatic',
        component: resolve =>
          require([
            '@/views/manage/party/partyPragmatic/partyPragmatic-basic'
          ], resolve),
        meta: {
          title: '党建实务',
          icon: 'data',
          role: 'partyManage:orgManage:partyPragmatic',
          btns: btnDisposeFn('partyManage:orgManage:partyPragmatic', [
            'ADD',
            'SORT',
            'VIEW',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      },
      {
        path: 'groupActivity',
        name: 'GroupActivity',
        component: resolve =>
          require([
            '@/views/manage/party/groupActivity/groupActivity-basic'
          ], resolve),
        meta: {
          title: '群团活动',
          icon: 'data',
          role: 'partyManage:orgManage:groupActivity',
          btns: btnDisposeFn('partyManage:orgManage:groupActivity', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      }
    ]
  },
  {
    path: '/groupWork',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/groupWork/index',
    meta: {
      title: '群团工作',
      icon: 'icon-menu-course',
      role: 'partyManage:groupWork',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        name: 'GroupWork',
        component: resolve =>
          require([
            '@/views/manage/party/groupWork/groupWork-basic'
          ], resolve),
        meta: {
          title: '群团工作',
          icon: 'icon-menu-course',
          role: 'partyManage:groupWork:list',
          btns: btnDisposeFn('partyManage:groupWork:list', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      }
    ]
  },
  {
    path: '/onlineLearning',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/onlineLearning/index',
    meta: {
      title: '在线学习',
      icon: 'icon-menu-policy',
      role: 'partyManage:onlineLearning',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        name: 'OnlineLearning',
        component: resolve =>
          require([
            '@/views/manage/party/onlineLearning/onlineLearning-basic'
          ], resolve),
        meta: {
          title: '在线学习',
          icon: 'icon-menu-policy',
          role: 'partyManage:onlineLearning:list',
          btns: btnDisposeFn('partyManage:onlineLearning:list', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      }
    ]
  }
]
export default partyRoutes
