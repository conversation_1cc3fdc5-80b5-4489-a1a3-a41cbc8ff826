import btnDisposeFn from '@/router/modules/btns'

const entFinanceBasic = {
  path: '/finance',
  component: resolve => require(['@/layout/lateral/index'], resolve),
  redirect: '/finance/account/account',
  meta: {
    title: '财务管理',
    icon: 'money-circle',
    role: 'ent:financeManage'
  },
  children: [
    {
      path: 'account',
      name: 'FinanceAccount',
      component: resolve => require(['@/layout/EmptyView'], resolve),
      redirect: '/finance/account/account',
      meta: {
        title: '账务管理',
        icon: 'thumb-up',
        role: 'ent:financeManage:manage',
        breadcrumb: false
      },
      children: [
        // {
        //   path: 'account',
        //   name: 'Account',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/account/account-upgrade/account/index.vue'
        //     ], resolve),
        //   meta: {
        //     title: '企业账户',
        //     icon: 'icon-menu-account',
        //     role: 'system:dept:list',
        //     desc: '汇总统计并展示账户相关重要信息',
        //     type: 'primary'
        //   }
        // },
        {
          path: 'purse',
          name: 'Purse',
          component: resolve =>
            require([
              '@/views/enterprise/finance/account/account-upgrade/purse/index.vue'
            ], resolve),
          meta: {
            title: '企业账户',
            icon: 'icon-menu-account',
            role: 'ent:financeManage:manage:purse',
            desc: '汇总统计并展示账户相关重要信息',
            type: 'primary'
          }
        },
        //提现申请
        {
          path: 'withdrawalApply',
          name: 'WithdrawalApply',
          component: resolve =>
            require([
              '@/views/enterprise/finance/drawMoney/drawMoney-basic'
            ], resolve),
          meta: {
            title: '提现申请',
            icon: 'icon-menu-account',
            role: 'ent:financeManage:manage:withdrawalApply',
            btns: btnDisposeFn('ent:financeManage:manage:withdrawalApply', [
              'LEADING_OUT',
              'WITHDRAW_APPLY',
              'VIEW'
            ]),
            desc: '详细记录账户提现记录及进度',
            type: 'primary'
          }
        },
        {
          path: 'initiateApply',
          name: 'InitiateApply',
          component: resolve =>
            require([
              '@/views/enterprise/finance/drawMoney/drawMoney-basic/initiateApply'
            ], resolve),
          meta: {
            title: '发起提现',
            icon: 'thumb-up',
            role: 'ent:financeManage:manage:initiateApply',
            hideSubMenu: true
          }
        },
        {
          path: 'withdrawalApplyDetail',
          name: 'WithdrawalApplyDetail',
          component: resolve =>
            require([
              '@/views/enterprise/finance/drawMoney/drawMoney-basic/detail'
            ], resolve),
          meta: {
            title: '提现详情',
            icon: 'thumb-up',
            role: 'ent:financeManage:manage:withdrawalApplyDetail',
            hideSubMenu: true
          }
        },
        // {
        //   path: 'account',
        //   name: 'Account',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/account/account-basic'
        //     ], resolve),
        //   meta: {
        //     title: '账户信息',
        //     icon: 'icon-menu-account',
        //     role: 'system:dept:list',
        //     desc: '汇总统计并展示账户相关重要信息',
        //     type: 'primary'
        //   }
        // },
        // {
        //   path: 'accountingInformation',
        //   name: 'AccountingInformation',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/account/account-basic/accountingInformation'
        //     ], resolve),
        //   meta: {
        //     title: '账务明细',
        //     icon: 'icon-menu-exchange',
        //     role: 'system:dept:list',
        //     desc: '查看当前企业账户全部账务记录',
        //     type: 'warning'
        //   }
        // },
        // {
        //   path: 'financeApply',
        //   name: 'financeApply',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/account/account-basic/dealInfo'
        //     ], resolve),
        //   meta: {
        //     title: '交易信息',
        //     icon: 'icon-menu-invoice',
        //     role: 'system:dept:list',
        //     desc: '登记线下交易记录信息实现账户余额充值',
        //     type: 'warning'
        //   }
        // },
        // {
        //   path: 'financeDetail',
        //   name: 'financeDetail',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/account/account-basic/dealDetail'
        //     ], resolve),
        //   meta: {
        //     title: '交易登记详情',
        //     icon: 'thumb-up',
        //     role: 'system:dept:list',
        //     hideSubMenu: true
        //   }
        // },
        // {
        //   path: 'marginDeduction',
        //   name: 'MarginDeduction',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/marginDeduction/marginDeduction-basic'
        //     ], resolve),
        //   meta: {
        //     title: '保证金抵扣',
        //     icon: 'icon-menu-invoice',
        //     role: 'system:dept:list',
        //     desc: '查看企业保证金抵扣申请信息',
        //     type: 'warning'
        //   }
        // },
        // {
        //   path: 'marginDeductionDetail',
        //   name: 'MarginDeductionDetail',
        //   component: resolve =>
        //     require([
        //       '@/views/enterprise/finance/marginDeduction/marginDeduction-basic/detail'
        //     ], resolve),
        //   meta: {
        //     title: '保证金抵扣详情',
        //     icon: 'thumb-up',
        //     role: 'system:dept:list',
        //     hideSubMenu: true
        //   }
        // },
        {
          path: 'marginAccount',
          name: 'MarginAccount',
          component: resolve =>
            require([
              '@/views/enterprise/finance/marginAccount/marginAccount-basic'
            ], resolve),
          meta: {
            title: '保证金账户',
            icon: 'icon-menu-bill',
            role: 'ent:financeManage:manage:marginAccount',
            desc: '请及时关注保证金缴存基数变化，确保保证金足额缴纳',
            type: 'warning'
          }
        }
      ]
    },
    {
      path: 'fees',
      name: 'FinanceFees',
      component: resolve => require(['@/layout/EmptyView'], resolve),
      redirect: '/finance/fees/payment',
      meta: {
        title: '缴费管理',
        icon: 'thumb-up',
        role: 'ent:financeManage:fees'
      },
      children: [
        {
          path: 'payment',
          name: 'Payment',
          component: resolve =>
            require([
              '@/views/enterprise/finance/payment/payment-basic'
            ], resolve),
          meta: {
            title: '缴费通知',
            icon: 'icon-menu-notice',
            role: 'ent:financeManage:fees:payment',
            desc: '查看企业全部已出缴费通知单',
            type: 'primary'
          }
        },
        {
          path: 'payment/detail',
          name: 'PaymentDetail',
          component: resolve =>
            require([
              '@/views/enterprise/finance/payment/payment-basic/detail'
            ], resolve),
          meta: {
            title: '缴费通知详情',
            icon: 'thumb-up',
            role: 'ent:financeManage:fees:payment:detail',
            hideSubMenu: true
          }
        },
        {
          path: 'record',
          name: 'Record',
          component: resolve =>
            require([
              '@/views/enterprise/finance/record/record-basic'
            ], resolve),
          meta: {
            title: '缴费记录',
            icon: 'icon-menu-record',
            role: 'ent:financeManage:fees:record',
            desc: '汇总查看企业当前全部缴费记录信息',
            type: 'success'
          }
        },
        {
          path: 'notice',
          name: 'Notice',
          component: resolve =>
            require([
              '@/views/enterprise/finance/notice/notice-basic'
            ], resolve),
          meta: {
            title: '缴费通知单详情',
            icon: 'thumb-up',
            role: 'ent:financeManage:fees:notice',
            hideSubMenu: true
          }
        },
        {
          path: 'refund',
          name: 'Refund',
          component: resolve =>
            require([
              '@/views/enterprise/finance/refund/refund-basic'
            ], resolve),
          meta: {
            title: '退费信息',
            icon: 'thumb-up',
            role: 'ent:financeManage:fees:refund',
            hideSubMenu: true
          }
        }
      ]
    }
    // {
    //   path: 'invoiceCenter',
    //   name: 'InvoiceCenter',
    //   component: resolve => require(['@/layout/EmptyView'], resolve),
    //   redirect: '/finance/invoiceCenter/index',
    //   meta: {
    //     title: '发票管理',
    //     icon: 'thumb-up',
    //     role: 'system:dept:list'
    //   },
    //   children: [
    //     {
    //       path: 'index',
    //       name: 'InvoiceCenterIndex',
    //       component: resolve =>
    //         require([
    //           '@/views/enterprise/finance/finance/finance-basic'
    //         ], resolve),
    //       meta: {
    //         title: '发票中心',
    //         icon: 'icon-menu-apply',
    //         role: 'system:dept:list',
    //         desc: '面向园区提交开票申请信息',
    //         type: 'success'
    //       }
    //     },
    //     {
    //       path: 'invoiceDetail',
    //       name: 'InvoiceDetail',
    //       component: resolve =>
    //         require([
    //           '@/views/enterprise/finance/finance/finance-basic/financeDetail'
    //         ], resolve),
    //       meta: {
    //         title: '发票详情',
    //         icon: 'thumb-up',
    //         role: 'system:dept:invoiceDetail',
    //         hideSubMenu: true
    //       }
    //     },
    //   ]
    // }
    // {
    //   path: 'ticket',
    //   name: 'FinanceTicket',
    //   component: resolve => require(['@/layout/EmptyView'], resolve),
    //   redirect: '/finance/ticket/list',
    //   meta: {
    //     title: '开票管理',
    //     icon: 'thumb-up',
    //     role: 'system:dept:list'
    //   },
    //   children: [
    //     {
    //       path: 'list',
    //       name: 'TicketList',
    //       component: resolve =>
    //         require([
    //           '@/views/enterprise/finance/finance/finance-basic'
    //         ], resolve),
    //       meta: {
    //         title: '开票申请',
    //         icon: 'icon-menu-apply',
    //         role: 'system:dept:list',
    //         desc: '面向园区提交开票申请信息',
    //         type: 'success'
    //       }
    //     },
    //     {
    //       path: 'ticketApply',
    //       name: 'TicketApply',
    //       component: resolve =>
    //         require([
    //           '@/views/enterprise/finance/finance/finance-basic/financeApply'
    //         ], resolve),
    //       meta: {
    //         title: '开票申请',
    //         icon: 'thumb-up',
    //         role: 'system:dept:list',
    //         hideSubMenu: true
    //       }
    //     },
    //     {
    //       path: 'ticketDetail',
    //       name: 'TicketDetail',
    //       component: resolve =>
    //         require([
    //           '@/views/enterprise/finance/finance/finance-basic/financeDetail'
    //         ], resolve),
    //       meta: {
    //         title: '开票详情',
    //         icon: 'thumb-up',
    //         role: 'system:dept:TicketDetail',
    //         hideSubMenu: true
    //       }
    //     },
    //
    //     {
    //       path: 'billingHistory',
    //       name: 'BillingHistory',
    //       component: resolve =>
    //         require([
    //           '@/views/enterprise/finance/finance/finance-basic/billingHistory'
    //         ], resolve),
    //       meta: {
    //         title: '开票历史',
    //         icon: 'icon-menu-ticket',
    //         role: 'system:dept:list',
    //         desc: '查看并管理企业全部已开票信息',
    //         type: 'warning'
    //       }
    //     }
    //   ]
    // }
  ]
}

//导出路由
export default entFinanceBasic
