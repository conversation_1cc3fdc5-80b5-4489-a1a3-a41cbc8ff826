/*
 * @Descripttion: 财务管理路由表
 * @Author: 田柱
 * @Date: 2021-04-14 11:31:40
 * @LastEditTime: 2022-05-10 17:34:46
 */
import btnDisposeFn from './btns'

const financialRoutes = [
  {
    path: '/account',
    name: 'Account',
    redirect: '/account/accountList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '账户管理',
      desc: '园多多级账户信息查询和数据展示',
      icon: 'account',
      role: 'financial:account'
    },
    children: [
      {
        path: 'accountList',
        name: 'AccountList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/account/accountingInformation'
          ], resolve),
        meta: {
          title: '账户管理',
          icon: 'account',
          role: 'financial:account:accountList'
        }
      }
      // {
      //   path: 'transactionInfoDetails',
      //   name: 'TransactionInfoDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/financialInformation-basic/details'
      //     ], resolve),
      //   meta: {
      //     title: '登记详情',
      //     icon: 'file-powerpoint',
      //     role: 'system:user:update'
      //   }
      // }
    ]
  },
  //  钱包管理
  {
    path: '/wallet',
    name: 'Wallet',
    redirect: '/wallet/walletList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '钱包管理',
      desc: '企业虚拟钱包余额查询和明细展示',
      icon: 'wallet-1',
      role: 'financial:wallet'
    },
    children: [
      {
        path: 'walletList',
        name: 'WalletList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/walletList/walletList-basic/list/index'
          ], resolve),
        meta: {
          title: '企业钱包',
          icon: 'wallet-1',
          breadcrumb: false,
          role: 'financial:wallet:list',
          btns: btnDisposeFn('financial:wallet:list', ['LEADING_OUT', 'DETAIL'])
        }
      },
      {
        path: 'walletList/walletDetails',
        name: 'WalletDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/walletList/walletList-basic/details/index'
          ], resolve),
        meta: {
          title: '钱包详情',
          icon: 'file-powerpoint',
          role: 'financial:wallet:walletDetails'
        }
      },
      {
        path: 'transactionInfo',
        name: 'TransactionInfo',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/transactionInfo'
          ], resolve),
        meta: {
          title: '交易登记',
          icon: 'file-powerpoint',
          role: 'financial:payment:transactionInfo',
          btns: btnDisposeFn('financial:payment:transactionInfo', [
            'LEADING_OUT',
            'SCAN_CODE',
            'VIEW'
          ])
        }
      },
      {
        path: 'transactionInfo/transactionInfoDetails',
        name: 'TransactionInfoDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/financialInformation-basic/details'
          ], resolve),
        meta: {
          title: '登记详情',
          icon: 'file-powerpoint',
          role: 'financial:payment:transactionInfoDetails',
          btns: btnDisposeFn('financial:payment:transactionInfoDetails', [
            'AFFIRM'
          ])
        }
      }
    ]
  },
  {
    path: '/payment',
    name: 'Payment',
    redirect: '/payment/contractReceivables',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '缴费管理',
      desc: '合同应收账单的全过程管理',
      icon: 'bill',
      role: 'financial:payment'
    },
    children: [
      {
        path: 'contractReceivables',
        name: 'ContractReceivables',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/contractReceivables-basic/index'
          ], resolve),
        meta: {
          title: '合同应收',
          icon: 'file-powerpoint',
          role: 'financial:payment:contractReceivables',
          btns: btnDisposeFn('financial:payment:contractReceivables', [
            'APARTMENT'
          ])
        }
      },
      {
        path: 'paymentPlan',
        name: 'PaymentPlan',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/paymentPlan-basic/index'
          ], resolve),
        meta: {
          title: '缴费计划',
          icon: 'file-powerpoint',
          role: 'financial:payment:plan',
          btns: btnDisposeFn('financial:payment:plan', ['APARTMENT'])
        }
      },
      // {
      //   path: 'paymentPlanDetails',
      //   name: 'PaymentPlanDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/paymentPlan-basic/detail'
      //     ], resolve),
      //   meta: {
      //     title: '交费计划详情',
      //     icon: 'file-powerpoint',
      //     role: 'system:user:update'
      //   }
      // },
      {
        path: 'accountsReceivable',
        name: 'AccountsReceivable',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/accountsReceivable-basic/index'
          ], resolve),
        meta: {
          title: '应收账单',
          icon: 'icon-menu-bill',
          role: 'financial:payment:accountsReceivable',
          btns: btnDisposeFn('financial:payment:accountsReceivable', [
            'DETAIL',
            'APARTMENT',
            'WATER',
            'ELECTRICITY'
          ]),
          type: 'warning'
        }
      },
      {
        path: 'accountsReceivable/accountsReceivableDetails',
        name: 'AccountsReceivableDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/accountsReceivable-basic/detail'
          ], resolve),
        meta: {
          title: '应收账单详情',
          icon: 'file-powerpoint',
          role: 'financial:payment:accountsReceivableDetails',
          btns: btnDisposeFn('financial:payment:accountsReceivableDetails', [
            'BILL_RECONCILIATION',
            'BILL_ADVICE'
          ])
        }
      },
      {
        path: 'paymentDetails',
        name: 'PaymentDetails',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/paymentDetails-basic/index'
          ], resolve),
        meta: {
          title: '缴费明细',
          icon: 'file-powerpoint',
          role: 'financial:payment:paymentDetails',
          btns: btnDisposeFn('financial:payment:paymentDetails', [
            'APARTMENT',
            'WATER',
            'ELECTRICITY'
          ])
        }
      },
      // {
      //   path: 'onlinePaymentReconciliation',
      //   name: 'OnlinePaymentReconciliation',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/onlinePaymentReconciliation/index'
      //     ], resolve),
      //   meta: {
      //     title: '在线交费对账',
      //     icon: 'file-powerpoint',
      //     role: 'system:user:update'
      //   }
      // },
      // {
      //   path: 'onlinePaymentDetails',
      //   name: 'OnlinePaymentDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/onlinePaymentDetails/index'
      //     ], resolve),
      //   meta: {
      //     title: '在线交费明细',
      //     icon: 'file-powerpoint',
      //     role: 'system:user:update'
      //   }
      // },
      // {
      //   path: 'offlineTransferDetails',
      //   name: 'OfflineTransferDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/offlineTransferDetails/index'
      //     ], resolve),
      //   meta: {
      //     title: '线下转账明细',
      //     icon: 'file-powerpoint',
      //     role: 'system:user:update'
      //   }
      // },
      // {
      //   path: 'overdueBill',
      //   name: 'OverdueBill',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/overdueBill/index'
      //     ], resolve),
      //   meta: {
      //     title: '逾期账单',
      //     icon: 'file-powerpoint',
      //     role: 'system:user:update'
      //   }
      // },
      {
        path: 'transactionInfo',
        name: 'TransactionInfo',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/financialInformation-basic'
          ], resolve),
        meta: {
          title: '转账登记',
          icon: 'file-powerpoint',
          role: 'financial:payment:transactionInfo',
          btns: btnDisposeFn('financial:payment:transactionInfo', ['DETAIL'])
        }
      }
    ]
  },
  //  对账管理
  {
    path: '/reconciliation',
    name: 'Reconciliation',
    redirect: '/reconciliation/reconciliationList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '对账管理',
      desc: '基于缴费明细银行对账数据查询',
      icon: 'file-copy',
      role: 'financial:reconciliation'
    },
    children: [
      {
        path: 'reconciliationList',
        name: 'ReconciliationList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/reconciliation/reconciliation-basic/list/index'
          ], resolve),
        meta: {
          title: '对账管理',
          icon: 'reconciliation',
          role: 'financial:reconciliation:reconciliationList',
          btns: btnDisposeFn('financial:reconciliation:reconciliationList', [
            'DETAIL'
          ])
        }
      }
    ]
  },
  //  归集管理
  {
    path: '/imputation',
    name: 'Imputation',
    redirect: '/imputation/imputationList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '归集管理',
      desc: '资金流各级账户往上划拨',
      icon: 'print',
      role: 'financial:imputation'
    },
    children: [
      {
        path: 'imputationList',
        name: 'ImputationList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/imputation/imputation-basic/list/index'
          ], resolve),
        meta: {
          title: '归集管理',
          icon: 'collection',
          role: 'financial:imputation:imputationList',
          btns: btnDisposeFn('financial:imputation:imputationList', ['DETAIL'])
        }
      }
    ]
  },
  //  凭证管理
  // {
  //   path: '/voucher',
  //   name: 'Voucher',
  //   redirect: '/voucher/voucherList',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   meta: {
  //     title: '凭证管理',
  //     icon: 'view-module',
  //     role: 'financial:voucher'
  //   },
  //   children: [
  //     {
  //       path: 'voucherList',
  //       name: 'VoucherList',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/payment/voucher/voucher-basic/list/index'
  //         ], resolve),
  //       meta: {
  //         title: '凭证记录',
  //         icon: 'certificate',
  //         role: 'financial:voucher:voucherList',
  //         btns: btnDisposeFn('financial:voucher:voucherList', ['LEADING_OUT'])
  //       }
  //     }
  //   ]
  // },
  //  开票管理
  {
    path: '/drawBill',
    name: 'DrawBill',
    redirect: '/drawBill/drawBillList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '开票管理',
      desc: '企业在线申请开票',
      icon: 'invoicing',
      role: 'financial:drawBill'
    },
    children: [
      {
        path: 'drawBillList',
        name: 'DrawBillList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawBill/drawBill-special/list/index'
          ], resolve),
        meta: {
          title: '开票申请',
          icon: 'invoicing',
          role: 'financial:drawBill:drawBillList',
          cache: true,
          btns: btnDisposeFn('financial:drawBill:drawBillList', [
            'DETAIL',
            'DOWNLOAD_INVOICE',
            'INFO_MAINTAIN',
            'MAKE_INVOICE',
            'EDIT'
          ])
        }
      },
      {
        path: 'drawBillList/infoMaintain',
        name: 'InfoMaintain',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawBill/drawBill-special/infoMaintain/index'
          ], resolve),
        hidden: true,
        meta: {
          title: '信息维护',
          role: 'financial:drawBill:infoMaintain',
          icon: 'file-powerpoint'
        }
      },
      {
        path: 'drawBillList/invoiceCreate',
        name: 'InvoiceCreate',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawBill/drawBill-special/invoiceCreate'
          ], resolve),
        hidden: true,
        meta: {
          title: '开票',
          role: 'financial:drawBill:invoiceCreate',
          icon: 'file-powerpoint'
        }
      },
      {
        path: 'drawBillList/drawBillDetails',
        name: 'DrawBillDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawBill/drawBill-special/details/index'
          ], resolve),
        meta: {
          title: '申请详情',
          icon: 'file-powerpoint',
          role: 'financial:drawBill:drawBillDetails',
          btns: btnDisposeFn('financial:drawBill:drawBillDetails', [
            'WITHDRAW',
            'CONFIRM_INVOICING',
            'DOWNLOAD_RESEND',
            'FLUSH_MARK',
            'ANEW_SUBMIT',
            'UPLOAD_TICKET'
          ])
        }
      }
      // {
      //   path: 'billHistoryList',
      //   name: 'BillHistoryList',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/billHistory/billHistory-basic/list/index'
      //     ], resolve),
      //   meta: {
      //     title: '开票历史',
      //     icon: 'file-powerpoint',
      //     role: 'financial:drawBill:billHistoryList',
      //     btns: btnDisposeFn('financial:drawBill:billHistoryList', ['DETAIL'])
      //   }
      // },
      // {
      //   path: 'billHistoryList/billHistoryDetails',
      //   name: 'BillHistoryDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-jtyh/payment/billHistory/billHistory-basic/details/index'
      //     ], resolve),
      //   meta: {
      //     title: '历史详情',
      //     icon: 'file-powerpoint',
      //     role: 'financial:drawBill:billHistoryDetails',
      //     btns: btnDisposeFn('financial:drawBill:billHistoryDetails', ['VIEW'])
      //   }
      // }
    ]
  },
  //  付款管理
  {
    path: '/drawMoney',
    name: 'DrawMoney',
    redirect: '/drawMoney/drawMoneyList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '付款管理',
      desc: '企业在线申请虚拟钱包余额付款',
      icon: 'withdraw',
      role: 'financial:drawMoney'
    },
    children: [
      {
        path: 'drawMoneyList',
        name: 'DrawMoneyList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawMoney/drawMoney-basic/list/index'
          ], resolve),
        meta: {
          title: '付款申请',
          icon: 'file-powerpoint',
          role: 'financial:drawMoney:drawMoneyList',
          btns: btnDisposeFn('financial:drawMoney:drawMoneyList', [
            'LEADING_OUT',
            'DETAIL'
          ])
        }
      },
      {
        path: 'drawMoneyList/drawMoneyDetails',
        name: 'DrawMoneyDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawMoney/drawMoney-basic/details/index'
          ], resolve),
        meta: {
          title: '申请详情',
          icon: 'file-powerpoint',
          role: 'financial:drawMoney:drawMoneyDetails',
          btns: btnDisposeFn('financial:drawMoney:drawMoneyDetails', [
            'AUDIT_TRAIL'
          ])
        }
      },
      {
        path: 'drawMoneyHistoryList',
        name: 'DrawMoneyHistoryList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawMoneyHistory/drawMoneyHistory-basic/list/index'
          ], resolve),
        meta: {
          title: '付款历史',
          icon: 'file-powerpoint',
          role: 'financial:drawMoney:drawMoneyHistoryList',
          btns: btnDisposeFn('financial:drawMoney:drawMoneyHistoryList', [
            'LEADING_OUT',
            'DETAIL'
          ])
        }
      },
      {
        path: 'drawMoneyHistoryList/drawMoneyHistoryDetails',
        name: 'DrawMoneyHistoryDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/payment/drawMoneyHistory/drawMoneyHistory-basic/details/index'
          ], resolve),
        meta: {
          title: '历史详情',
          icon: 'file-powerpoint',
          role: 'financial:drawMoney:DrawMoneyHistoryDetails',
          btns: btnDisposeFn('financial:drawMoney:DrawMoneyHistoryDetails', [
            'AUDIT_TRAIL'
          ])
        }
      }
    ]
  }
  // {
  //   path: '/reconciliation',
  //   name: 'Reconciliation',
  //   hidden: true,
  //   redirect: '/reconciliation/yesterdayReconciliation',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   meta: {
  //     title: '对账管理',
  //     icon: 'backward',
  //     role: 'system:user:update'
  //   },
  //   children: [
  //     {
  //       path: 'yesterdayReconciliation',
  //       name: 'YesterdayReconciliation',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/reconciliation/yesterdayReconciliation/index'
  //         ], resolve),
  //       meta: {
  //         title: '昨日对账(核销)',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     },
  //     {
  //       path: 'historyReconciliation',
  //       name: 'HistoryReconciliation',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/reconciliation/historyReconciliation/index'
  //         ], resolve),
  //       meta: {
  //         title: '历史对账(核销)',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/refundManagement',
  //   name: 'Refund',
  //   hidden: true,
  //   redirect: '/refundManagement/index',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   meta: {
  //     title: '退款管理',
  //     icon: 'chevron-up',
  //     role: 'system:user:update'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'RefundReview',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/refundManagement/refundExamine/refundFee/index'
  //         ], resolve),
  //       meta: {
  //         title: '退费审核',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     },
  //     {
  //       path: 'refundBalance',
  //       name: 'WithdrawalAudit',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/refundManagement/refundExamine/refundBalance/index'
  //         ], resolve),
  //       meta: {
  //         title: '付款审核',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     },
  //     {
  //       path: 'refundBalanceDetails',
  //       name: 'RefundBalanceDetails',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/refundManagement/refundExamine/refundBalance/refundBalanceDetails'
  //         ], resolve),
  //       meta: {
  //         title: '退余额详情',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     },
  //     {
  //       path: 'checkOutSettlement',
  //       name: 'CheckOutSettlement',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/refundManagement/refundExamine/refundFee/checkOutSettlement'
  //         ], resolve),
  //       meta: {
  //         title: '退费审核详情',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     },
  //     {
  //       path: 'accountNoFinancial',
  //       name: 'refundManagementAccountNoFinancial',
  //       hidden: true,
  //       component: () =>
  //         import(
  //           '@/views/manage/financial/financial-jtyh/refundManagement/refundExamine/accountNoFinancial/index'
  //         ),
  //       meta: {
  //         title: '应收账单',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update',
  //         hidden: true
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/financialManagement',
  //   name: 'FundManagement',
  //   hidden: true,
  //   redirect: '/financialManagement/cashSweep',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   meta: {
  //     title: '资金管理',
  //     icon: 'folder-open',
  //     role: 'system:user:update'
  //   },
  //   children: [
  //     {
  //       path: 'collectionResults',
  //       name: 'CollectionResults',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/fundManagement/collectionResults'
  //         ], resolve),
  //       meta: {
  //         title: '归集结果',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     },
  //     {
  //       path: 'cashSweep',
  //       name: 'CashSweep',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/fundManagement/cashSweep'
  //         ], resolve),
  //       meta: {
  //         title: '资金归集',
  //         icon: 'file-powerpoint',
  //         role: 'system:user:update'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/collectionVoucher',
  //   name: 'CollectionVoucher',
  //   hidden: true,
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   meta: {
  //     title: '退款管理',
  //     icon: 'root-list',
  //     role: 'system:user:update'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'CollectionVoucher',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-jtyh/collectionVoucher'
  //         ], resolve),
  //       meta: {
  //         title: '凭证管理',
  //         icon: 'bulletpoint',
  //         role: 'system:user:update'
  //       }
  //     }
  //   ]
  // }
]

export default financialRoutes
