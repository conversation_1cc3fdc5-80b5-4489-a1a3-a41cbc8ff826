import btnDisposeFn from '@/router/modules/btns'

const unifySupportRoutes = [
  {
    path: '/dept',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/dept/index',
    meta: {
      title: '部门管理',
      icon: 'department',
      role: 'system:departmentManage',
    },
    children: [
      {
        path: 'index',
        name: 'WorkspaceDept',
        component: resolve => require(['@/views/manage/system/dept'], resolve),
        meta: {
          title: '部门管理',
          icon: 'department',
          role: 'system:departmentManage:dept',
          cache: true,
          btns: btnDisposeFn('park:departmentManage:dept', [
            'ADD',
            'REVISE',
            'UNFOLD_FOLD',
            'DELETE'
          ])
        }
      },
      {
        path: 'rank',
        name: 'RankManagement',
        hidden: true,
        component: resolve => require(['@/views/manage/system/rank'], resolve),
        meta: {
          title: '职级管理',
          icon: 'view-list',
          role: 'system:departmentManage:rank'
        }
      }
    ]
  },
  {
    path: '/role',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/role/index',
    meta: {
      title: '角色管理',
      icon: 'icon-role',
      role: 'system:roleManage'
    },
    children: [
      {
        path: 'index',
        name: 'WorkspaceRole',
        component: resolve => require(['@/views/manage/system/role'], resolve),
        meta: {
          title: '角色管理',
          icon: 'icon-role',
          role: 'system:roleManage:role',
          cache: true,
          btns: btnDisposeFn('system:roleManage:role', [
            'MENU_PERMISSIONS',
            'REVISE',
            'DATA_PERMISSIONS',
            'ADD',
            'DELETE'
          ])
        }
      },
    ]
  },
  {
    path: '/user',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/user/index',
    meta: {
      title: '用户管理',
      icon: 'icon-setting',
      role: 'system:userManage'
    },
    children: [
      {
        path: 'index',
        name: 'WorkspaceUser',
        component: resolve => require(['@/views/manage/system/user'], resolve),
        meta: {
          title: '用户管理',
          icon: 'user-setting',
          role: 'system:userManage:user',
          cache: true,
          btns: btnDisposeFn('system:userManage:user', [
            'ADD',
            'REVISE',
            'DELETE',
            'RESET_PASSWORD',
            'ASSIGN_ROLES'
          ])
        }
      }
    ]
  },
  {
    path: '/enterprise',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/enterprise/index',
    meta: {
      title: '企业管理',
      icon: 'icon-menu-enterprise',
      role: 'system:enterpriseManage'
    },
    children: [
      {
        path: 'index',
        name: 'EnterpriseManage',
        component: resolve =>
          require(['@/views/manage/system/enterprise'], resolve),
        meta: {
          title: '企业管理',
          icon: 'icon-menu-enterprise',
          role: 'system:enterpriseManage:enterprise',
          cache: true,
          btns: btnDisposeFn('system:enterpriseManage:enterprise', [
            'EDIT_ENTERPRISE_NAME',
            'EDIT_MANAGE_NAME',
            'RESET_PASSWORD',
            'ADD'
          ])
        }
      }
    ]
  },
  {
    path: '/parkCommerce',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/parkCommerce/index',
    meta: {
      title: '园区商业',
      icon: 'icon-menu-enterprise',
      role: 'system:parkCommerce'
    },
    children: [
      {
        path: 'index',
        name: 'ParkCommerce',
        component: resolve =>
          require(['@/views/manage/system/parkCommerce'], resolve),
        meta: {
          title: '园区商业',
          icon: 'icon-menu-enterprise',
          role: 'system:parkCommerce:list',
          cache: true
        }
      }
    ]
  },
  {
    path: '/modelDesign',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/modelDesign/index',
    meta: {
      title: '流程设计',
      icon: 'icon-flow',
      role: 'system:modelDesign'
    },
    children: [
      {
        path: 'index',
        name: 'ModelDesign',
        component: resolve =>
          require(['@/views/manage/system/modelDesign'], resolve),
        meta: {
          title: '流程设计',
          icon: 'icon-flow',
          role: 'system:modelDesign:list',
          cache: true,
          btns: btnDisposeFn('system:modelDesign:list', ['MODEL'])
        }
      },
      {
        path: 'modelDesignDetails',
        name: 'ModelDesignDetails',
        component: resolve =>
          require([
            '@/views/manage/system/modelDesign/modelDesignDetails'
          ], resolve),
        hidden: true,
        meta: {
          title: '流程设计详情',
          icon: 'view-module',
          role: 'system:modelDesign:modelDesignDetails'
        }
      }
    ]
  },
  {
    path: '/workspaceContract',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/workspaceContract/index',
    meta: {
      title: '合同配置',
      icon: 'contract-setting',
      role: 'system:contractDisposition'
    },
    children: [
      {
        path: 'index',
        name: 'WorkspaceContract',
        component: resolve =>
          require(['@/views/manage/system/contract'], resolve),
        meta: {
          title: '合同配置',
          icon: 'contract-setting',
          role: 'system:contractDisposition:contract',
          cache: true,
          btns: btnDisposeFn('system:contractDisposition:contract', [
            'ADD',
            'EDIT',
            'DELETE',
            'RECORD'
          ])
        }
      }
    ]
  },
  {
    path: '/portalDisposition',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/portalDisposition/index',
    meta: {
      title: '门户配置',
      icon: 'portal-setting',
      role: 'system:portalDisposition'
    },
    children: [
      {
        path: 'index',
        name: 'PortalSpecial',
        component: resolve =>
          require(['@/views/manage/system/portal/portal-special'], resolve),
        meta: {
          title: '门户配置',
          icon: 'portal-setting',
          role: 'system:portalDisposition:list',
          type: 'success',
          cache: true
        }
      }
    ]
  }
]
export default unifySupportRoutes
