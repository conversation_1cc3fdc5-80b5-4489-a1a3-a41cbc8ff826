import btnDisposeFn from '@/router/modules/btns'

const manageRoutes = [
  {
    path: '/shuttle',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/shuttle/index',
    meta: {
      title: '接驳车管理',
      icon: 'house-manage',
      role: 'shuttle:manage',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        name: 'ShuttleStatistical',
        component: resolve =>
          require([
            '@/views/manage/operationService/shuttle/statistical/statistical-basic'
          ], resolve),
        meta: {
          title: '乘车统计',
          icon: 'data',
          role: 'shuttle:manage:statistical',
          cache: true
        }
      }
    ]
  },
  {
    path: '/investmentManage',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/investmentManage/index',
    meta: {
      title: '招商管理',
      icon: 'order-ascending',
      role: 'operation:investment',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        name: 'Investment',
        component: resolve =>
          require([
            '@/views/manage/operationService/investment/investment-basic'
          ], resolve),
        meta: {
          title: '招商管理',
          icon: 'order-ascending',
          role: 'operation:investment:list',
          cache: true
        }
      }
    ]
  },
  {
    path: '/demand',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/demand/index',
    meta: {
      title: '科创需求',
      icon: 'icon-msg',
      role: 'demand:manage',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        name: 'Demand',
        component: resolve =>
          require([
            '@/views/manage/operationService/demand/demand-basic'
          ], resolve),
        meta: {
          title: '科创需求',
          icon: 'icon-msg',
          role: 'demand:manage:list',
          cache: true
        }
      }
    ]
  },
  // {
  //   path: '/activity',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/activity/index',
  //   meta: {
  //     title: '活动管理',
  //     icon: 'activity',
  //     role: 'operation:activity'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'ActivityBasic',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/operationService/activity/activity-basic'
  //         ], resolve),
  //       meta: {
  //         title: '活动管理',
  //         icon: 'activity',
  //         role: 'operation:activity:list',
  //         btns: btnDisposeFn('operation:activity:list', [
  //           'ADD',
  //           'VIEW',
  //           'LEADING_OUT',
  //           'RECALL',
  //           'EDIT',
  //           'DELETE',
  //           'SORT'
  //         ]),
  //         cache: true
  //       }
  //     }
  //   ]
  // },
  {
    path: '/event',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/event/index',
    meta: {
      title: '活动管理',
      icon: 'activity',
      role: 'operation:event',
      breadcrumb: false
    },
    children: [
      {
        path: 'index',
        name: 'EventMange',
        component: resolve =>
          require([
            '@/views/manage/operationService/event/event-basic/list'
          ], resolve),
        meta: {
          title: '活动管理',
          icon: 'activity',
          role: 'operation:event:list',
          btns: btnDisposeFn('operation:event:list', [
            'ADD',
            'SIGN_SETTING',
            'VIEW',
            'EVENT_CODE',
            'EXPORT_SIGN',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      },
      {
        path: 'index/templateSetting',
        name: 'EventTemplateSetting',
        component: resolve =>
          require([
            '@/views/manage/operationService/event/event-basic/templateSetting'
          ], resolve),
        hidden: true,
        meta: {
          title: '报名模板设置',
          icon: 'activity',
          role: 'operation:event:templateSetting',
          btns: btnDisposeFn('operation:event:templateSetting', [
            'ADD',
            'SORT',
            'EDIT',
            'DELETE'
          ])
        }
      },
      {
        path: 'index/normalCreate',
        name: 'NormalEventCreate',
        component: resolve =>
          require([
            '@/views/manage/operationService/event/event-basic/normalCreate'
          ], resolve),
        hidden: true,
        meta: {
          title: '新增普通活动',
          icon: 'activity',
          noTagView: true,
          role: 'operation:event:list:normalCreate'
        }
      },
      {
        path: 'index/topicCreate',
        name: 'TopicEventCreate',
        component: resolve =>
          require([
            '@/views/manage/operationService/event/event-basic/topicCreate'
          ], resolve),
        hidden: true,
        meta: {
          title: '新增专题活动',
          noTagView: true,
          icon: 'activity',
          role: 'operation:event:list:topicCreate'
        }
      },
      {
        path: 'index/detail',
        name: 'EventDetail',
        component: resolve =>
          require([
            '@/views/manage/operationService/event/event-basic/detail'
          ], resolve),
        hidden: true,
        meta: {
          title: '活动详情',
          icon: 'activity',
          role: 'operation:event:detail'
        }
      },
      {
        path: 'index/topicDetail',
        name: 'TopicEventDetail',
        component: resolve =>
          require([
            '@/views/manage/operationService/event/event-basic/topicDetail'
          ], resolve),
        hidden: true,
        meta: {
          title: '专题详情',
          icon: 'activity',
          role: 'operation:event:list:topicDetail',
          btns: btnDisposeFn('operation:event:list:topicDetail', [
            'ADD',
            'SORT',
            'EXPORT_SIGN',
            'VIEW',
            'EDIT',
            'DELETE'
          ])
        }
      }
    ]
  },
  {
    path: '/infomation',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/infomation/index',
    meta: {
      title: '公告通知',
      desc: '面向企业、公众或部门及时发送相关通知',
      icon: 'icon-msg',
      role: 'park:announcement'
    },
    children: [
      {
        path: 'announcementControl',
        name: 'AnnouncementControl',
        component: resolve =>
          require([
            '@/views/manage/operationService/information/information-special/announcementControl'
          ], resolve),
        meta: {
          title: '公告通知',
          icon: 'icon-msg',
          breadcrumb: false,
          role: 'park:announcementControl:list',
          btns: btnDisposeFn('park:announcementControl:list', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE',
            'SHARE',
            'PUBLISH',
            'SORT',
            'ANNOUNCEMENT_TYPE'
          ]),
          cache: true
        }
      },
      {
        path: 'announcementControl/announcementType',
        name: 'AnnouncementType',
        component: resolve =>
          require([
            '@/views/manage/operationService/information/information-special/announcementType'
          ], resolve),
        hidden: true,
        meta: {
          title: '公告类型',
          icon: 'icon-msg',
          role: 'park:announcementType:list',
          cache: true
        }
      }
    ]
  },
  {
    path: '/capitalNews',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/capitalNews/index',
    meta: {
      title: '资讯新闻',
      desc: '最新资讯动态和相关新闻在线发布',
      icon: 'icon-news',
      role: 'operation:capitalNews'
    },
    children: [
      {
        path: 'index',
        name: 'CapitalNewsControl',
        component: resolve =>
          require([
            '@/views/manage/operationService/capitalNews/capitalNews-basic/announcementControl'
          ], resolve),
        meta: {
          title: '资讯新闻',
          icon: 'icon-news',
          role: 'operation:capitalNewsControl:list',
          btns: btnDisposeFn('operation:capitalNewsControl:list', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE',
            'SHARE',
            'PUBLISH',
            'SORT',
            'INFORMATION_TYPE'
          ]),
          cache: true
        }
      },
      {
        path: 'capitalNewsType',
        name: 'CapitalNewsType',
        component: resolve =>
          require([
            '@/views/manage/operationService/capitalNews/capitalNews-basic/announcementType'
          ], resolve),
        hidden: true,
        meta: {
          title: '资讯类型',
          icon: 'notification-filled',
          role: 'operation:capitalNewsType:list',
          cache: true
        }
      }
    ]
  },
  {
    path: '/policy',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/policy/index',
    meta: {
      title: '政策中心',
      desc: '海量政策线索一键发布、匹配、推送',
      icon: 'policy',
      role: 'operation:policy'
    },
    children: [
      {
        path: 'index',
        name: 'PolicyCenter',
        component: resolve =>
          require([
            '@/views/manage/operationService/policy/policy-special/policyCenter'
          ], resolve),
        meta: {
          title: '政策中心',
          icon: 'policy',
          iconShort: 'icon-menu-policy', // 快捷菜单
          role: 'operation:policy:center',
          btns: btnDisposeFn('operation:policy:center', [
            'RELEASE_POLICY',
            'VIEW',
            'EDIT',
            'DELETE',
            'SHOW',
            'HIDE',
            'DOWNLOAD',
            'ADD',
            'THEMATIC_SETTING',
            'PUBLISH'
          ]),
          type: 'danger',
          cache: true
        }
      }
      // {
      //   path: 'policyAdvice',
      //   name: 'PolicyAdvice',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyAdvice'
      //     ], resolve),
      //   meta: {
      //     title: '政策咨询',
      //     icon: 'policy',
      //     role: 'opeartion:policyAdvice:list',
      //     cache: true
      //   }
      // },
      // {
      //   path: 'policyDeclaration',
      //   name: 'PolicyDeclaration',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyDeclaration/list'
      //     ], resolve),
      //   meta: {
      //     title: '政策申报',
      //     icon: 'policy',
      //     role: 'opeartion:policyDeclaration:list',
      //     btns: btnDisposeFn('opeartion:policyDeclaration:list', ['VIEW']),
      //     cache: true
      //   }
      // },
      // {
      //   path: 'policyDeclarationDetail',
      //   name: 'PolicyDeclarationDetail',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyDeclaration/detail'
      //     ], resolve),
      //   meta: {
      //     title: '申报详情',
      //     icon: 'policy',
      //     role: 'opeartion:policyDeclaration:detail',
      //     btns: btnDisposeFn('opeartion:policyDeclaration:detail', [
      //       'VIEW',
      //       'DOWNLOAD',
      //       'AUDIT'
      //     ])
      //   }
      // },
      // {
      //   path: 'policyTools',
      //   name: 'PolicyTools',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyTools/list'
      //     ], resolve),
      //   meta: {
      //     title: '政策工具',
      //     icon: 'policy',
      //     role: 'opeartion:policyTools:list',
      //     btns: btnDisposeFn('opeartion:policyTools:list', [
      //       'ADD',
      //       'VIEW',
      //       'EDIT',
      //       'DELETE'
      //     ]),
      //     cache: true
      //   }
      // },
      // {
      //   path: 'policyToolsDetail',
      //   name: 'PolicyToolsDetail',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyTools/detail'
      //     ], resolve),
      //   meta: {
      //     title: '工具详情',
      //     icon: 'policy',
      //     role: 'opeartion:policyToolsDetail:detail'
      //   }
      // }
    ]
  },
  {
    path: '/visit',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/visit/index',
    meta: {
      title: '问卷调查',
      icon: 'visit',
      role: 'park:visit'
    },
    children: [
      {
        path: 'index',
        name: 'VisitBasic',
        component: resolve =>
          require([
            '@/views/manage/operationService/visit/visit-basic'
          ], resolve),
        meta: {
          title: '问卷调查',
          icon: 'visit',
          role: 'park:visit:list',
          btns: btnDisposeFn('park:visit:list', ['ADD', 'VIEW']),
          cache: true
        }
      }
    ]
  },
  {
    path: '/problem',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/problem/index',
    meta: {
      title: '问题反馈',
      desc: '积极响应并解决企业或公众的相关诉求',
      icon: 'problem',
      role: 'operation:problem'
    },
    children: [
      {
        path: 'index',
        name: 'ProblemBasic',
        component: resolve =>
          require([
            '@/views/manage/operationService/problem/problem-basic'
          ], resolve),
        meta: {
          title: '问题办理',
          icon: 'problem',
          iconShort: 'icon-menu-problem', // 快捷菜单
          role: 'operation:problem:list',
          btns: btnDisposeFn('operation:problem:list', ['VIEW', 'LEADING_OUT']),
          type: 'warning',
          cache: true
        }
      },
      {
        path: 'index/problemDetail',
        name: 'HatchProblemDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/operationService/problem/problem-basic/detail'
          ], resolve),
        meta: {
          title: '问题办理详情',
          icon: 'user-avatar',
          role: 'operation:problem:problemDetail',
          btns: btnDisposeFn('operation:problem:problemDetail', [
            'QUESTION_INTERACTION',
            'ADD_RECORD',
            'REPLY',
            'TRANSACT_END',
            'ISSUE'
          ])
        }
      },
      {
        path: 'typeManage',
        name: 'ProblemTypeManage',
        component: resolve =>
            require([
              '@/views/manage/operationService/problem/typeManage'
            ], resolve),
        meta: {
          title: '类型管理',
          icon: 'problem',
          role: 'operation:problem:typeManage',
          btns: btnDisposeFn('operation:problem:typeManage', [
            'ADD',
            'SORT',
            'EDIT',
            'DELETE'
          ]),
          cache: true
        }
      }
    ]
  },
  {
    path: '/sportCenter',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/sportCenter/fieldBoard',
    meta: {
      title: '运动中心',
      desc: '公共场地统一申请、预定管理',
      icon: 'site',
      role: 'operation:sportCenter'
    },
    children: [
      {
        path: 'fieldBoard',
        name: 'FieldBoard',
        component: resolve =>
          require([
            '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldBoard/list'
          ], resolve),
        meta: {
          title: '场地看板',
          type: 'warning',
          cache: true,
          role: 'operation:sportCenter:fieldBoard'
        }
      },
      {
        path: 'fieldOrder',
        name: 'FieldOrder',
        component: resolve =>
          require([
            '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldOrder/list'
          ], resolve),
        meta: {
          title: '场地订单',
          type: 'warning',
          cache: true,
          role: 'operation:sportCenter:fieldOrder',
          btns: btnDisposeFn('operation:sportCenter:fieldOrder', [
            'INTERNAL_APPOINTMENT',
            'LEADING_OUT',
            'VIEW',
            'CANCEL_ORDER'
          ])
        }
      },
      {
        path: 'fieldManage',
        name: 'FieldManage',
        component: resolve =>
          require([
            '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldManage/list'
          ], resolve),
        meta: {
          title: '场地管理',
          type: 'warning',
          cache: true,
          role: 'operation:sportCenter:fieldManage',
          btns: btnDisposeFn('operation:sportCenter:fieldManage', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE',
            'EMPLACE'
          ])
        }
      },
      {
        path: 'fieldManage/fieldSetting',
        name: 'FieldSetting',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldManage/setting'
          ], resolve),
        meta: {
          title: '场地设置',
          type: 'warning',
          role: 'operation:sportCenter:fieldSetting'
        }
      },
      {
        path: 'fieldBlacklist',
        name: 'FieldBlacklist',
        component: resolve =>
          require([
            '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldBlacklist/list'
          ], resolve),
        meta: {
          title: '黑名单',
          type: 'warning',
          cache: true,
          role: 'operation:sportCenter:fieldBlacklist',
          btns: btnDisposeFn('operation:sportCenter:fieldBlacklist', [
            'EMPLACE',
            'ADD_NAME',
            'REMOVE'
          ])
        }
      }
    ]
  },
  {
    path: '/field',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/field/index',
    meta: {
      title: '会议室',
      desc: '会议室统一申请、预定管理',
      icon: 'icon-menu-leave',
      role: 'operation:field'
    },
    children: [
      {
        path: 'index',
        name: 'ScheduleCalendar',
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/scheduleCalendar'
          ], resolve),
        meta: {
          title: '会议室看板',
          icon: 'site',
          iconShort: 'icon-menu-field', // 快捷菜单
          role: 'operation:field:scheduleCalendar',
          btns: btnDisposeFn('operation:field:ScheduleCalendar'),
          type: 'warning',
          cache: true
        }
      },
      {
        path: 'fieldApply',
        name: 'FiledApply',
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/fieldApply/index'
          ], resolve),
        meta: {
          title: '会议室申请',
          icon: 'site',
          type: 'warning',
          role: 'operation:field:fieldApply',
          btns: btnDisposeFn('operation:field:fieldApply', [
            'LEADING_OUT',
            'VIEW',
            'CANCEL',
            'INTERNAL_APPOINTMENT'
          ])
        }
      },
      {
        path: 'fieldApply/fieldApplyDetails',
        name: 'FiledApplyDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/fieldApply/details'
          ], resolve),
        meta: {
          title: '会议室详情',
          icon: 'minus-rectangle',
          role: 'operation:field:fieldApplyDetails',
          btns: btnDisposeFn('operation:field:fieldApplyDetails')
        }
      },
      {
        path: 'fieldControl',
        name: 'FiledControl',
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/fieldControl/index'
          ], resolve),
        meta: {
          title: '会议室管理',
          icon: 'minus-rectangle',
          role: 'operation:field:fieldControl',
          btns: btnDisposeFn('operation:field:fieldControl', [
            'ADD',
            'VIEW',
            'EDIT',
            'DELETE',
            'SWITCH',
            'BASE_INSTALLATION'
          ])
        }
      },
      {
        path: 'fieldControl/fieldFacility',
        name: 'FieldFacility',
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/fieldFacility/index'
          ], resolve),
        hidden: true,
        meta: {
          title: '会议室设施',
          icon: 'minus-rectangle',
          role: 'operation:field:fieldFacility'
        }
      },
      {
        path: 'fieldControl/fieldServe',
        name: 'FieldServe',
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/fieldServe/index'
          ], resolve),
        hidden: true,
        meta: {
          title: '场地服务',
          icon: 'minus-rectangle',
          role: 'operation:field:fieldServe'
        }
      },
      {
        path: 'fieldControl/fieldPersonnel',
        name: 'FieldPersonnel',
        component: resolve =>
          require([
            '@/views/manage/operationService/field/field-special/fieldPersonnel/index'
          ], resolve),
        hidden: true,
        meta: {
          title: '场地人员',
          icon: 'minus-rectangle',
          role: 'operation:field:fieldPersonnel',
          btns: btnDisposeFn('operation:field:fieldPersonnel', [
            'EDIT',
            'DELETE',
            'ADD',
            'SORT'
          ])
        }
      }
    ]
  },
  {
    path: '/serviceApply',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/serviceApply/visitor',
    meta: {
      title: '服务申请',
      desc: '公共场地统一申请、预定管理',
      icon: 'staff',
      role: 'service:apply',
      breadcrumb: false
    },
    children: [
      {
        path: 'visitor',
        name: 'VisitorApply',
        component: resolve =>
          require([
            '@/views/manage/operationService/serviceApply/visitor/visitor-basic/index'
          ], resolve),
        meta: {
          title: '访客申请',
          icon: 'staff',
          role: 'service:apply:list',
          type: 'warning',
          btns: btnDisposeFn('service:apply:list', ['LEADING_OUT']),
          cache: true
        }
      },
      {
        path: 'parkingFee',
        name: 'ParkingFee',
        component: resolve =>
          require([
            '@/views/manage/operationService/serviceApply/parkingFee/parkingFee-basic/index'
          ], resolve),
        meta: {
          title: '车位包月',
          icon: 'staff',
          role: 'service:parkingFee:list',
          btns: btnDisposeFn('service:parkingFee:list', [
            'LEADING_OUT',
            'AUDIT'
          ]),
          type: 'warning',
          cache: true
        }
      }
    ]
  },
  {
    path: '/clock',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/clock/index',
    meta: {
      title: '周年打卡',
      desc: '海量政策线索一键发布、匹配、推送',
      icon: 'policy',
      role: 'operation:clock'
    },
    children: [
      {
        path: 'index',
        name: 'Clock',
        component: resolve =>
          require([
            '@/views/manage/operationService/clock'
          ], resolve),
        meta: {
          title: '打卡列表',
          icon: 'policy',
          iconShort: 'icon-menu-policy',
          role: 'operation:clock:list',
          type: 'danger',
          cache: true
        }
      }
      // {
      //   path: 'policyAdvice',
      //   name: 'PolicyAdvice',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyAdvice'
      //     ], resolve),
      //   meta: {
      //     title: '政策咨询',
      //     icon: 'policy',
      //     role: 'opeartion:policyAdvice:list',
      //     cache: true
      //   }
      // },
      // {
      //   path: 'policyDeclaration',
      //   name: 'PolicyDeclaration',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyDeclaration/list'
      //     ], resolve),
      //   meta: {
      //     title: '政策申报',
      //     icon: 'policy',
      //     role: 'opeartion:policyDeclaration:list',
      //     btns: btnDisposeFn('opeartion:policyDeclaration:list', ['VIEW']),
      //     cache: true
      //   }
      // },
      // {
      //   path: 'policyDeclarationDetail',
      //   name: 'PolicyDeclarationDetail',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyDeclaration/detail'
      //     ], resolve),
      //   meta: {
      //     title: '申报详情',
      //     icon: 'policy',
      //     role: 'opeartion:policyDeclaration:detail',
      //     btns: btnDisposeFn('opeartion:policyDeclaration:detail', [
      //       'VIEW',
      //       'DOWNLOAD',
      //       'AUDIT'
      //     ])
      //   }
      // },
      // {
      //   path: 'policyTools',
      //   name: 'PolicyTools',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyTools/list'
      //     ], resolve),
      //   meta: {
      //     title: '政策工具',
      //     icon: 'policy',
      //     role: 'opeartion:policyTools:list',
      //     btns: btnDisposeFn('opeartion:policyTools:list', [
      //       'ADD',
      //       'VIEW',
      //       'EDIT',
      //       'DELETE'
      //     ]),
      //     cache: true
      //   }
      // },
      // {
      //   path: 'policyToolsDetail',
      //   name: 'PolicyToolsDetail',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/policy/policy-special/policyTools/detail'
      //     ], resolve),
      //   meta: {
      //     title: '工具详情',
      //     icon: 'policy',
      //     role: 'opeartion:policyToolsDetail:detail'
      //   }
      // }
    ]
  },
]
export default manageRoutes
