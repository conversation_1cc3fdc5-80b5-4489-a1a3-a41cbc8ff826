/*
 * @Descripttion: 财务管理路由表
 * @Author: 田柱
 * @Date: 2021-04-14 11:31:40
 * @LastEditTime: 2022-05-10 17:34:46
 */
import btnDisposeFn from './btns'

const financialRoutes = [
  {
    path: '/account',
    name: 'Account',
    redirect: '/account/accountList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '账户管理',
      desc: '园多多级账户信息查询和数据展示',
      icon: 'account',
      role: 'financial:account'
    },
    children: [
      {
        path: 'accountList',
        name: 'AccountList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/account/accountingInformationOld'
          ], resolve),
        meta: {
          title: '园区账户',
          icon: 'account',
          role: 'financial:account:parkAccount'
        }
      },
      {
        path: 'accountListBeta',
        name: 'AccountListBeta',
        component: resolve =>
            require([
              '@/views/manage/financial/financial-jtyh/account/accountingInformation'
            ], resolve),
        meta: {
          title: '园区账户beta',
          icon: 'account',
          role: 'financial:account:parkAccount:beta'
        }
      },
      {
        path: 'entAccount',
        name: 'EntAccount',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/entAccount'
          ], resolve),
        meta: {
          title: '租金账户',
          icon: 'file-powerpoint',
          role: 'financial:account:entAccount',
          cache: true
        }
      },
      {
        path: 'entAccountDetail',
        name: 'EntAccountDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/entAccount/detail'
          ], resolve),
        meta: {
          title: '租金账户详情',
          icon: 'file-powerpoint',
          role: 'financial:account:entAccountDetail'
        }
      },
      {
        path: 'depositCollection',
        name: 'DepositCollection',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/depositCollection/depositCollection-basic/index'
          ], resolve),
        meta: {
          title: '保证金账户',
          icon: 'account',
          role: 'financial:account:marginAccount',
          cache: true
        }
      },
      // {
      //   path: 'marginAccount',
      //   name: 'MarginAccount',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/depositCollection/depositCollection-basic/index'
      //     ], resolve),
      //   meta: {
      //     title: '保证金账户',
      //     icon: 'account',
      //     role: 'financial:account:marginAccount',
      //     cache: true
      //   }
      // },
      {
        path: 'marginAccountDetail',
        name: 'MarginAccountDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-jtyh/marginAccount/detail'
          ], resolve),
        meta: {
          title: '保证金账户详情',
          icon: 'account',
          role: 'financial:account:marginAccountDetail'
        }
      },
      {
        path: 'marginDeduction',
        name: 'MarginDeduction',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/marginDeduction/list'
          ], resolve),
        meta: {
          title: '企业账户抵扣',
          icon: 'account',
          role: 'financial:account:marginDeduction',
          btns: btnDisposeFn('financial:account:marginDeduction', [
            'APPLY','VIEW'
          ]),
          cache: true
        }
      },
      {
        path: 'marginDeduction/detail',
        name: 'MarginDeductionDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/marginDeduction/detail'
          ], resolve),
        meta: {
          title: '企业账户抵扣详情',
          icon: 'account',
          role: 'financial:account:marginDeductionDetail',
          btns: btnDisposeFn('financial:account:marginDeductionDetail', [
            'AUDIT'
          ]),
        }
      }
      // {
      //   path: 'transactionInfo',
      //   name: 'TransactionInfo',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/transactionInfo'
      //     ], resolve),
      //   meta: {
      //     title: '交易登记',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:transactionInfo',
      //     btns: btnDisposeFn('financial:payment:transactionInfo', [
      //       'LEADING_OUT',
      //       'SCAN_CODE',
      //       'VIEW'
      //     ])
      //   }
      // },
      // {
      //   path: 'transactionInfo/transactionInfoDetails',
      //   name: 'TransactionInfoDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/financialInformation-basic/details'
      //     ], resolve),
      //   meta: {
      //     title: '登记详情',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:transactionInfoDetails',
      //     btns: btnDisposeFn('financial:payment:transactionInfoDetails', [
      //       'AFFIRM'
      //     ])
      //   }
      // }
    ]
  },
  {
    path: '/journal',
    name: 'Journal',
    redirect: '/journal/journalList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '流水管理',
      desc: '园多多级账户信息查询和数据展示',
      icon: 'merchants',
      role: 'financial:journal'
    },
    children: [
      {
        path: 'journalList',
        name: 'JournalList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/journal'
          ], resolve),
        meta: {
          icon: 'merchants',
          title: '流水认领',
          role: 'financial:journal:journalList',
          btns: btnDisposeFn('financial:journal:journalList', [
            'VIEW',
            'LEADING_IN'
          ]),
          cache: true
        }
      },
      {
        path: 'journalDetail',
        name: 'JournalDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/journal/detail'
          ], resolve),
        meta: {
          icon: 'merchants',
          title: '流水记录详情',
          role: 'financial:journal:journalDetail'
        }
      }
    ]
  },

  {
    path: '/payment',
    name: 'Payment',
    redirect: '/payment/contractReceivables',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '缴费管理',
      desc: '合同应收账单的全过程管理',
      icon: 'bill',
      role: 'financial:payment'
    },
    children: [
      // {
      //   path: 'overdueRecords',
      //   name: 'OverdueRecords',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/overdueRecords'
      //     ], resolve),
      //   meta: {
      //     title: '逾期记录',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:overdueRecords',
      //     btns: btnDisposeFn('financial:payment:overdueRecords', [
      //       'LEADING_OUT',
      //       'SCAN_CODE',
      //       'VIEW'
      //     ])
      //   }
      // },
      {
        path: 'contractReceivables',
        name: 'ContractReceivables',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/contractReceivables-basic/index'
          ], resolve),
        meta: {
          title: '合同应收',
          icon: 'file-powerpoint',
          role: 'financial:payment:contractReceivables',
          btns: btnDisposeFn('financial:payment:contractReceivables', [
            'APARTMENT'
          ]),
          cache: true
        }
      },
      {
        path: 'paymentPlan',
        name: 'PaymentPlan',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/paymentPlan-basic/index'
          ], resolve),
        meta: {
          title: '缴费计划',
          icon: 'file-powerpoint',
          role: 'financial:paymentPlan',
          btns: btnDisposeFn('financial:paymentPlan', ['APARTMENT']),
          cache: true
        }
      },
      // {
      //   path: 'paymentPlanDetails',
      //   name: 'PaymentPlanDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/paymentPlan-basic/detail'
      //     ], resolve),
      //   meta: {
      //     title: '交费计划详情',
      //     icon: 'file-powerpoint',
      //     role: 'financial:paymentPlanDetails'
      //   }
      // },
      {
        path: 'accountsReceivable',
        name: 'AccountsReceivable',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/list'
          ], resolve),
        meta: {
          title: '应收账单',
          icon: 'icon-menu-bill',
          role: 'financial:payment:accountsReceivable',
          btns: btnDisposeFn('financial:payment:accountsReceivable', [
            'DETAIL',
            'APARTMENT',
            'LIQUIDATED_DAMAGES',
            'WATER',
            'ELECTRICITY',
            'MAKE_INVOICE'
          ]),
          type: 'warning',
          cache: true
        }
      },
      {
        path: 'accountsReceivable/accountsReceivableDetails',
        name: 'AccountsReceivableDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/detail'
          ], resolve),
        meta: {
          title: '应收账单详情',
          icon: 'file-powerpoint',
          role: 'financial:payment:accountsReceivableDetails',
          btns: btnDisposeFn('financial:payment:accountsReceivableDetails', [
            'BILL_RECONCILIATION',
            'BILL_ADVICE'
          ])
        }
      },

      {
        path: 'paymentDetails',
        name: 'PaymentDetails',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/paymentDetails-basic/index'
          ], resolve),
        meta: {
          title: '缴费明细',
          icon: 'file-powerpoint',
          role: 'financial:payment:paymentDetails',
          btns: btnDisposeFn('financial:payment:paymentDetails', [
            'APARTMENT',
            'WATER',
            'ELECTRICITY'
          ]),
          cache: true
        }
      },
      // {
      //   path: 'onlinePaymentReconciliation',
      //   name: 'OnlinePaymentReconciliation',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/onlinePaymentReconciliation/index'
      //     ], resolve),
      //   meta: {
      //     title: '在线交费对账',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:onlinePaymentReconciliation'
      //   }
      // },
      // {
      //   path: 'onlinePaymentDetails',
      //   name: 'OnlinePaymentDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/onlinePaymentDetails/index'
      //     ], resolve),
      //   meta: {
      //     title: '在线交费明细',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:onlinePaymentDetails'
      //   }
      // },
      // {
      //   path: 'offlineTransferDetails',
      //   name: 'OfflineTransferDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/offlineTransferDetails/index'
      //     ], resolve),
      //   meta: {
      //     title: '线下转账明细',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:offlineTransferDetails'
      //   }
      // },
      {
        path: 'overdueBill',
        name: 'OverdueBill',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/overdueBill/index'
          ], resolve),
        meta: {
          title: '逾期账单',
          icon: 'file-powerpoint',
          role: 'financial:payment:overdueBill'
        }
      },
      {
        path: 'overdueRecord',
        name: 'OverdueRecord',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/overdueRecord/list'
          ], resolve),
        meta: {
          title: '逾期记录',
          icon: 'file-powerpoint',
          role: 'financial:overdueRecord',
          cache: true
        }
      },
      {
        path: 'overdueRecord/overdueDetails',
        name: 'OverdueDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/overdueRecord/detail'
          ], resolve),
        meta: {
          title: '逾期详情',
          icon: 'file-powerpoint',
          role: 'financial:overdueDetails'
        }
      }
      // {
      //   path: 'transactionInfo',
      //   name: 'TransactionInfo',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/financialInformation-basic'
      //     ], resolve),
      //   meta: {
      //     title: '转账登记',
      //     icon: 'file-powerpoint',
      //     role: 'financial:payment:transactionInfo',
      //     btns: btnDisposeFn('financial:payment:transactionInfo', ['DETAIL'])
      //   }
      // },
    ]
  },
  //  对账管理
  // {
  //     path: '/reconciliation',
  //     name: 'Reconciliation',
  //     redirect: '/reconciliation/reconciliationList',
  //     component: resolve => require(['@/layout/default/index'], resolve),
  //     meta: {
  //         title: '对账管理',
  //         icon: 'file-copy',
  //         role: 'financial:reconciliation'
  //     },
  //     children: [
  //         {
  //             path: 'reconciliationList',
  //             name: 'ReconciliationList',
  //             component: resolve =>
  //                 require([
  //                     '@/views/manage/financial/financial-basic/payment/reconciliation/reconciliation-basic/list/index'
  //                 ], resolve),
  //             meta: {
  //                 title: '对账记录',
  //                 icon: 'reconciliation',
  //                 role: 'financial:reconciliation:reconciliationList',
  //                 btns: btnDisposeFn('financial:reconciliation:reconciliationList', [
  //                     'DETAIL'
  //                 ])
  //             }
  //         }
  //     ]
  // },
  //  归集管理
  // {
  //     path: '/imputation',
  //     name: 'Imputation',
  //     redirect: '/imputation/imputationList',
  //     component: resolve => require(['@/layout/default/index'], resolve),
  //     meta: {
  //         title: '归集管理',
  //         icon: 'print',
  //         role: 'financial:imputation'
  //     },
  //     children: [
  //         {
  //             path: 'imputationList',
  //             name: 'ImputationList',
  //             component: resolve =>
  //                 require([
  //                     '@/views/manage/financial/financial-basic/payment/imputation/imputation-basic/list/index'
  //                 ], resolve),
  //             meta: {
  //                 title: '归集结果',
  //                 icon: 'collection',
  //                 role: 'financial:imputation:imputationList',
  //                 btns: btnDisposeFn('financial:imputation:imputationList', ['DETAIL'])
  //             }
  //         }
  //     ]
  // },
  //  凭证管理
  // {
  //   path: '/voucher',
  //   name: 'Voucher',
  //   redirect: '/voucher/voucherList',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   meta: {
  //     title: '凭证管理',
  //     icon: 'view-module',
  //     role: 'financial:voucher'
  //   },
  //   children: [
  //     {
  //       path: 'voucherList',
  //       name: 'VoucherList',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/financial/financial-basic/payment/voucher/voucher-basic/list/index'
  //         ], resolve),
  //       meta: {
  //         title: '凭证记录',
  //         icon: 'certificate',
  //         role: 'financial:voucher:voucherList',
  //         btns: btnDisposeFn('financial:voucher:voucherList', ['LEADING_OUT'])
  //       }
  //     }
  //   ]
  // },
  //  开票管理
  {
    path: '/drawBill',
    name: 'DrawBill',
    redirect: '/drawBill/drawBillList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '开票管理',
      desc: '企业在线申请开票',
      icon: 'invoicing',
      role: 'financial:drawBill'
    },
    children: [
      {
        path: 'drawBillList',
        name: 'DrawBillList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/drawBill/drawBill-basic/list/index'
          ], resolve),
        meta: {
          title: '开票记录',
          icon: 'invoicing',
          role: 'financial:drawBill:drawBillList',
          cache: true,
          btns: btnDisposeFn('financial:drawBill:drawBillList', [
            'VIEW',
            'DOWNLOAD_INVOICE',
            'INFO_MAINTAIN',
            'MAKE_INVOICE',
            'EDIT',
            'RECALL',
            'UPLOAD_INVOICE',
          ])
        }
      },
      {
        path: 'drawBillList/infoMaintain',
        name: 'InfoMaintain',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/drawBill/drawBill-basic/infoMaintain/index'
          ], resolve),
        hidden: true,
        meta: {
          title: '信息维护',
          role: 'financial:drawBill:infoMaintain',
          icon: 'file-powerpoint',
          btns: btnDisposeFn('financial:drawBill:infoMaintain', [
            'SELL_INFO_MAINTAIN',
            'BUY_INFO_MAINTAIN',
            'RECEIVE_INFO_MAINTAIN'
          ])
        }
      },
      {
        path: 'drawBillList/invoiceCreate',
        name: 'InvoiceCreate',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/drawBill/drawBill-basic/invoiceCreate'
          ], resolve),
        hidden: true,
        meta: {
          title: '开票',
          role: 'financial:drawBill:invoiceCreate',
          icon: 'file-powerpoint'
        },
        btns: btnDisposeFn('financial:drawBill:invoiceCreate', [
          'PRE_INVOICE',
          'REAL_INVOICE'
        ])
      },
      {
        path: 'drawBillList/drawBillDetails',
        name: 'DrawBillDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/drawBill/drawBill-basic/details/index'
          ], resolve),
        meta: {
          title: '开票申请详情',
          icon: 'file-powerpoint',
          role: 'financial:drawBill:drawBillDetails',
          btns: btnDisposeFn('financial:drawBill:drawBillDetails', [
            'WITHDRAW',
            'CONFIRM_INVOICING',
            'DOWNLOAD_RESEND',
            'FLUSH_MARK',
            'ANEW_SUBMIT',
            'UPLOAD_TICKET',
            'UPLOAD_INVOICE'
          ])
        }
      }
      // {
      //   path: 'billHistoryList',
      //   name: 'BillHistoryList',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/billHistory/billHistory-basic/list/index'
      //     ], resolve),
      //   meta: {
      //     title: '开票历史',
      //     icon: 'file-powerpoint',
      //     role: 'financial:drawBill:billHistoryList',
      //     btns: btnDisposeFn('financial:drawBill:billHistoryList', ['DETAIL'])
      //   }
      // },
      // {
      //   path: 'billHistoryList/billHistoryDetails',
      //   name: 'BillHistoryDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/billHistory/billHistory-basic/details/index'
      //     ], resolve),
      //   meta: {
      //     title: '历史详情',
      //     icon: 'file-powerpoint',
      //     role: 'financial:drawBill:billHistoryDetails',
      //     btns: btnDisposeFn('financial:drawBill:billHistoryDetails', ['VIEW'])
      //   }
      // }
    ]
  },
  //  付款管理
  {
    path: '/drawMoney',
    name: 'DrawMoney',
    redirect: '/drawMoney/drawMoneyList',
    component: resolve => require(['@/layout/default/index'], resolve),
    meta: {
      title: '付款管理',
      desc: '企业在线申请虚拟钱包余额付款',
      icon: 'withdraw',
      role: 'financial:drawMoney'
    },
    children: [
      {
        path: 'drawMoneyList',
        name: 'DrawMoneyList',
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/drawMoney/drawMoney-basic/list/index'
          ], resolve),
        meta: {
          title: '付款申请',
          icon: 'withdraw',
          role: 'financial:drawMoney:drawMoneyList',
          btns: btnDisposeFn('financial:drawMoney:drawMoneyList', [
            'LEADING_OUT',
            'DETAIL'
          ]),
          cache: true
        }
      },
      {
        path: 'drawMoneyList/drawMoneyDetails',
        name: 'DrawMoneyDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/financial/financial-basic/payment/drawMoney/drawMoney-basic/details/index'
          ], resolve),
        meta: {
          title: '申请详情',
          icon: 'file-powerpoint',
          role: 'financial:drawMoney:drawMoneyDetails',
          btns: btnDisposeFn('financial:drawMoney:drawMoneyDetails', [
            'AUDIT_TRAIL'
          ])
        }
      }
      // {
      //   path: 'drawMoneyHistoryList',
      //   name: 'DrawMoneyHistoryList',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/drawMoneyHistory/drawMoneyHistory-basic/list/index'
      //     ], resolve),
      //   meta: {
      //     title: '付款历史',
      //     icon: 'file-powerpoint',
      //     role: 'financial:drawMoney:drawMoneyHistoryList',
      //     btns: btnDisposeFn('financial:drawMoney:drawMoneyHistoryList', [
      //       'LEADING_OUT',
      //       'DETAIL'
      //     ]),
      //     cache: true
      //   }
      // },
      // {
      //   path: 'drawMoneyHistoryList/drawMoneyHistoryDetails',
      //   name: 'DrawMoneyHistoryDetails',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/financial/financial-basic/payment/drawMoneyHistory/drawMoneyHistory-basic/details/index'
      //     ], resolve),
      //   meta: {
      //     title: '历史详情',
      //     icon: 'file-powerpoint',
      //     role: 'financial:drawMoney:DrawMoneyHistoryDetails',
      //     btns: btnDisposeFn('financial:drawMoney:DrawMoneyHistoryDetails', [
      //       'AUDIT_TRAIL'
      //     ])
      //   }
      // }
    ]
  }
]

export default financialRoutes
