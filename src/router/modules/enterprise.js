/*
 * @Descripttion: 企业端路由表
 * @Author: 田柱
 * @Date: 2022-7-25 11:42:03
 * @LastEditTime: 2022-7-25 11:42:03
 */
import entFinanceBasicRoutes from './EntFinanceBasic'
import entFinanceJtyhRoutes from './EntFinanceJtyh'
import btnDisposeFn from '@/router/modules/btns'
const financial =
  process.env.VUE_APP_FINANCE_TYPE === 'jtyh'
    ? entFinanceJtyhRoutes
    : entFinanceBasicRoutes

const enterpriseRoutes = [
  {
    path: '/dashboard',
    component: resolve => require(['@/layout/lateral/index'], resolve),
    redirect: '/dashboard/index',
    meta: {
      title: '企业首页',
      icon: 'home'
    },
    children: [
      {
        path: 'index',
        name: 'EnterpriseDashboard',
        component: resolve =>
          require([
            '@/views/enterprise/dashboard/dashboard-basic/index'
          ], resolve),
        meta: {
          title: '企业首页',
          icon: 'home',
          allow: true
        }
      }
    ]
  },
  {
    path: '/work',
    component: resolve => require(['@/layout/lateral/index'], resolve),
    redirect: '/work/handle/enter',
    meta: {
      title: '业务办理',
      icon: 'hourglass',
      role: 'ent:businessTransact'
    },
    children: [
      {
        path: 'handle',
        name: 'WorkHandle',
        component: resolve => require(['@/layout/EmptyView'], resolve),
        redirect: '/work/handle/enter',
        meta: {
          title: '业务办理',
          icon: 'thumb-up',
          role: 'ent:businessTransact:manage',
          breadcrumb: false
        },
        children: [
          {
            path: 'enter',
            name: 'EnterpriseEnter',
            component: resolve =>
              require(['@/views/enterprise/work/enter/enter-basic'], resolve),
            meta: {
              title: '入园申请',
              icon: 'icon-menu-enter',
              role: 'ent:businessTransact:manage:enter',
              btns: btnDisposeFn('ent:businessTransact:manage:enter', [
                'VIEW'
              ]),
              desc: '提交入园相关材料，快速办理入园流程',
              type: 'success'
            }
          },
          {
            path: 'enterApply',
            name: 'EnterpriseEnterApply',
            component: resolve =>
              require([
                '@/views/enterprise/work/enter/enter-basic/apply'
              ], resolve),
            meta: {
              title: '入园申请',
              icon: 'icon-color-course',
              role: 'ent:businessTransact:manage:enter:create',
              hideSubMenu: true
            }
          },
          {
            path: 'enterDetails',
            name: 'EnterpriseEnterDetails',
            // component: resolve =>
            //   require([
            //     '@/views/enterprise/work/enter/enter-basic/apply/components/details'
            //   ], resolve),
            component: resolve =>
              require([
                '@/views/enterprise/work/enter/enter-basic/settledDetails'
              ], resolve),
            meta: {
              title: '入园申请详情',
              icon: 'icon-color-course',
              role: 'ent:businessTransact:manage:enter:detail',
              hideSubMenu: true
            }
          },
          {
            path: 'contract',
            name: 'EnterpriseContract',
            component: resolve =>
              require([
                '@/views/enterprise/work/contract/contract-basic'
              ], resolve),
            meta: {
              title: '合同管理',
              icon: 'icon-menu-contract',
              role: 'ent:businessTransact:manage:contract',
              desc: '查看当前企业与园区签订的所有历史合同',
              type: 'primary'
            }
          },
          {
            path: 'contract/detail',
            name: 'EnterpriseContractDetail',
            component: resolve =>
              require([
                '@/views/enterprise/work/contract/contract-basic/contract-detail'
              ], resolve),
            meta: {
              title: '合同详情',
              icon: 'icon-color-policy',
              role: 'ent:businessTransact:manage:contract:detail',
              hideSubMenu: true
            }
          },
          {
            path: 'adjust',
            name: 'EnterpriseWork',
            component: resolve =>
              require([
                '@/views/enterprise/work/replace/replace-special'
              ], resolve),
            meta: {
              title: '调房申请',
              icon: 'icon-menu-replace',
              role: 'ent:businessTransact:manage:adjust',
              btns: btnDisposeFn('ent:businessTransact:manage:adjust', [
                'ADJUST_APPLY'
              ]),
              desc: '在线提交房间变更申请，一键办理所有业务',
              type: 'success'
            }
          },
          {
            path: 'adjust/create',
            name: 'EnterpriseReplaceCreate',
            component: resolve =>
              require([
                '@/views/enterprise/work/replace/replace-special/create'
              ], resolve),
            meta: {
              title: '调房申请',
              icon: 'icon-color-policy',
              role: 'ent:businessTransact:manage:adjust:create',
              hideSubMenu: true
            }
          },
          {
            path: 'adjust/detail',
            name: 'EnterpriseReplaceDetail',
            component: resolve =>
              require([
                '@/views/enterprise/work/replace/replace-special/detail'
              ], resolve),
            meta: {
              title: '调房申请详情',
              icon: 'icon-color-policy',
              role: 'ent:businessTransact:manage:adjust:detail',
              hideSubMenu: true
            }
          },
          {
            path: 'leave',
            name: 'EnterpriseLeaveBasic',
            component: resolve =>
              require(['@/views/enterprise/work/leave/leave-special'], resolve),
            meta: {
              title: '离园申请',
              icon: 'icon-menu-leave',
              role: 'ent:businessTransact:manage:leave',
              btns: btnDisposeFn('ent:businessTransact:manage:leave', [
                'LEAVE_APPLY'
              ]),
              desc: '在线提交离园申请，一键办理所有业务',
              type: 'primary'
            }
          },
          {
            path: 'leave/create',
            name: 'EnterpriseLeaveCreate',
            component: resolve =>
              require([
                '@/views/enterprise/work/leave/leave-special/create'
              ], resolve),
            meta: {
              title: '离园申请',
              icon: 'icon-color-policy',
              role: 'ent:businessTransact:manage:leave:create',
              hideSubMenu: true
            }
          },
          {
            path: 'leave/detail',
            name: 'EnterpriseLeaveDetail',
            component: resolve =>
              require([
                '@/views/enterprise/work/leave/leave-special/detail'
              ], resolve),
            meta: {
              title: '离园详情',
              icon: 'icon-color-policy',
              role: 'ent:businessTransact:manage:leave:detail',
              hideSubMenu: true
            }
          }
          // {
          //   path: 'information',
          //   name: 'InformationSpecial',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/work/information/information-special'
          //     ], resolve),
          //   meta: {
          //     title: '信息填报',
          //     icon: 'icon-menu-replace',
          //     role: 'system:dept:list',
          //     desc: '响应园区信息填报，及时获取更多优质服务',
          //     type: 'success'
          //   }
          // },
          // {
          //   path: 'informationDetail',
          //   name: 'InformationDetail',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/work/information/information-special/informationDetail'
          //     ], resolve),
          //   meta: {
          //     title: '填报详情',
          //     icon: 'icon-menu-replace',
          //     role: 'system:dept:list',
          //     type: 'success',
          //     hideSubMenu: true
          //   }
          // },
          // {
          //   path: 'partyManagement',
          //   name: 'PartyManagement',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/work/party/party-basic/partyManagement'
          //     ], resolve),
          //   meta: {
          //     title: '党建管理',
          //     icon: 'contract',
          //     role: 'system:dept:list',
          //     desc: '党建管理一站式，提升党组织管理效能',
          //     type: 'success'
          //   }
          // }
        ]
      }
    ]
  },
  {
    path: '/company',
    component: resolve => require(['@/layout/lateral/index'], resolve),
    redirect: '/company/manage/index',
    meta: {
      title: '公司管理',
      icon: 'print',
      role: 'ent:companyManage'
    },
    children: [
      {
        path: 'manage',
        name: 'EnterpriseManage',
        component: resolve => require(['@/layout/EmptyView'], resolve),
        redirect: '/company/manage/index',
        meta: {
          title: '公司管理',
          icon: 'thumb-up',
          role: 'ent:companyManage:manage',
          breadcrumb: false
        },
        children: [
          {
            path: 'index',
            name: 'StaffBasic',
            component: resolve =>
              require([
                '@/views/enterprise/company/staff/staff-basic'
              ], resolve),
            meta: {
              title: '员工管理',
              icon: 'icon-menu-user',
              role: 'ent:companyManage:manage:staffManage',
              btns: btnDisposeFn('ent:companyManage:manage:staffManage', [
                'EDIT'
              ]),
              desc: '提交的认证信息，或自主登记员工信息',
              type: 'primary'
            }
          },
          {
            path: 'userManage',
            name: 'UserManage',
            component: resolve =>
              require([
                '@/views/enterprise/company/userManage/userManage-basic'
              ], resolve),
            meta: {
              title: '用户管理',
              icon: 'icon-menu-user',
              role: 'ent:companyManage:manage:userManage',
              btns: btnDisposeFn('ent:companyManage:manage:userManage', [
                'ADD_ENT_ACCOUNT',
                'MOVED_OUT',
                'POSITION_ADJUST',
                'RESET_PASSWORD'
              ]),
              desc: '新增或将在职员工升级为企业账号，不同企业职位具备不同的权限范围',
              type: 'primary'
            }
          }
          // {
          //   path: 'vehicle',
          //   name: 'VehicleBasic',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/vehicle/vehicle-basic'
          //     ], resolve),
          //   meta: {
          //     title: '车辆管理',
          //     icon: 'icon-menu-car',
          //     role: 'system:dept:list',
          //     desc: '查看并管理企业下员工车辆、企业车辆',
          //     type: 'primary'
          //   }
          // },
          // {
          //   path: 'visit',
          //   name: 'VisitBasic',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/visit/visit-basic'
          //     ], resolve),
          //   meta: {
          //     title: '走访调查',
          //     icon: 'icon-color-field',
          //     role: 'system:dept:list',
          //     desc: '参与填写园区相关走访调查问卷，积极反馈相关需求',
          //     type: 'primary'
          //   }
          // },
          // {
          //   path: 'visitDetail',
          //   name: 'VisitDetail',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/visit/visit-basic/detail'
          //     ], resolve),
          //   meta: {
          //     title: '详情',
          //     icon: 'icon-color-field',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // },
          // {
          //   path: 'visitState',
          //   name: 'VisitState',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/visit/visit-basic/state'
          //     ], resolve),
          //   meta: {
          //     title: '提交成功',
          //     icon: 'icon-color-field',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // },
          // {
          //   path: 'publicHouse',
          //   name: 'PublicHouse',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/publicHouse/publicHouse-basic'
          //     ], resolve),
          //   meta: {
          //     title: '公租房申请',
          //     icon: 'icon-color-rent',
          //     role: 'system:dept:list',
          //     desc: '为企业员工在线申请公租房',
          //     type: 'primary'
          //   }
          // },
          // {
          //   path: 'publicHouseCreate',
          //   name: 'PublicHouseCreate',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/publicHouse/publicHouse-basic/applyCreate'
          //     ], resolve),
          //   meta: {
          //     title: '公租房申请',
          //     icon: 'icon-color-rent',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // },
          // {
          //   path: 'applyDetail',
          //   name: 'ApplyDetail',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/publicHouse/publicHouse-basic/applyDetail'
          //     ], resolve),
          //   meta: {
          //     title: '租房详情',
          //     icon: 'icon-color-rent',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // },
          // {
          //   path: 'renewDetail',
          //   name: 'RenewDetail',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/publicHouse/publicHouse-basic/renewDetail'
          //     ], resolve),
          //   meta: {
          //     title: '续约详情',
          //     icon: 'icon-color-rent',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // },
          // {
          //   path: 'quitHouseDetail',
          //   name: 'QuitHouseDetail',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/company/publicHouse/publicHouse-basic/quitHouseDetail'
          //     ], resolve),
          //   meta: {
          //     title: '退房详情',
          //     icon: 'icon-color-rent',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // }
        ]
      }
      // {
      //   path: 'safety',
      //   name: 'EnterpriseSafety',
      //   component: resolve => require(['@/layout/EmptyView'], resolve),
      //   redirect: '/company/safety/index',
      //   meta: {
      //     title: '安全生产',
      //     icon: 'thumb-up',
      //     role: 'system:dept:list',
      //     breadcrumb: false
      //   },
      //   children: [
      //     {
      //       path: 'index',
      //       name: 'SecurityCouncil',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/securityCouncil/securityCouncil-basic'
      //         ], resolve),
      //       meta: {
      //         title: '安委会',
      //         icon: 'security',
      //         role: 'system:dept:list',
      //         desc: '组建安全委员会有序安全生产',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'addSecurityCouncil',
      //       name: 'AddSecurityCouncil',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/securityCouncil/securityCouncil-basic/addDevice'
      //         ], resolve),
      //       meta: {
      //         title: '安委会',
      //         icon: 'security',
      //         role: 'system:dept:list',
      //         desc: '组建安全委员会有序安全生产',
      //         type: 'primary',
      //         hideSubMenu: true
      //       }
      //     },
      //     {
      //       path: 'contingencyPlan',
      //       name: 'ContingencyPlan',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/contingencyPlan/contingencyPlan-basic'
      //         ], resolve),
      //       meta: {
      //         title: '应急预案',
      //         icon: 'contingency',
      //         role: 'system:dept:list',
      //         desc: '登记安全预案做好风险防备',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'contingencyPlanDetail',
      //       name: 'ContingencyPlanDetail',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/contingencyPlan/contingencyPlan-basic/detail'
      //         ], resolve),
      //       meta: {
      //         title: '应急预案详情',
      //         icon: 'contingency',
      //         role: 'system:dept:list',
      //         hideSubMenu: true,
      //         desc: '登记安全预案做好风险防备',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'addContingencyPlan',
      //       name: 'AddContingencyPlan',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/contingencyPlan/contingencyPlan-basic/addDevice'
      //         ], resolve),
      //       meta: {
      //         title: '添加',
      //         icon: 'contingency',
      //         role: 'system:dept:list',
      //         hideSubMenu: true,
      //         desc: '登记安全预案做好风险防备',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'dailyDrills',
      //       name: 'DailyDrills',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/dailyDrills/dailyDrills-basic'
      //         ], resolve),
      //       meta: {
      //         title: '日常演练',
      //         icon: 'daily',
      //         role: 'system:dept:list',
      //         desc: '长伴安全演练有限保障',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'dailyDrillsAdd',
      //       name: 'DailyDrillsAdd',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/dailyDrills/dailyDrills-basic/addDevice'
      //         ], resolve),
      //       meta: {
      //         title: '添加',
      //         icon: 'daily',
      //         hideSubMenu: true,
      //         role: 'system:dept:list',
      //         desc: '长伴安全演练有限保障',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'dailyDrillsDetail',
      //       name: 'DailyDrillsAddDetail',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/dailyDrills/dailyDrills-basic/detail'
      //         ], resolve),
      //       meta: {
      //         title: '演练详情',
      //         icon: 'daily',
      //         hideSubMenu: true,
      //         role: 'system:dept:list',
      //         desc: '长伴安全演练有限保障',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'equipment',
      //       name: 'Equipment',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/equipment/equipment-basic'
      //         ], resolve),
      //       meta: {
      //         title: '设备管理',
      //         icon: 'equipment',
      //         role: 'system:dept:list',
      //         desc: '及时更新设备是每个企业安全生产的首要责任',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'addDevice',
      //       name: 'AddDevice',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/equipment/equipment-basic/addDevice'
      //         ], resolve),
      //       meta: {
      //         title: '添加管理',
      //         icon: 'icon-menu-car',
      //         role: 'system:dept:list',
      //         type: 'primary',
      //         hideSubMenu: true
      //       }
      //     },
      //     {
      //       path: 'equipmentDetail',
      //       name: 'EquipmentDetail',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/equipment/equipment-basic/detail.vue'
      //         ], resolve),
      //       meta: {
      //         title: '设备详情',
      //         icon: 'icon-menu-car',
      //         role: 'system:dept:list',
      //         desc: '及时更新设施设备是每个企业安全生产的首要责任',
      //         type: 'primary',
      //         hideSubMenu: true
      //       }
      //     },
      //     {
      //       path: 'securityChecks',
      //       name: 'SecurityChecks',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/securityChecks/securityChecks-basic'
      //         ], resolve),
      //       meta: {
      //         title: '安全检查 ',
      //         icon: 'safe',
      //         role: 'system:dept:list',
      //         desc: '积极响应园区下发安全检查任务,保障安全生产',
      //         type: 'primary'
      //       }
      //     },
      //     {
      //       path: 'checkDetail',
      //       name: 'CheckDetail',
      //       component: resolve =>
      //         require([
      //           '@/views/enterprise/company/securityChecks/securityChecks-basic/detail.vue'
      //         ], resolve),
      //       meta: {
      //         title: '检查详情',
      //         icon: 'icon-menu-car',
      //         role: 'system:dept:list',
      //         desc: '积极响应园区下发安全检查任务,保障安全生产',
      //         type: 'primary',
      //         hideSubMenu: true
      //       }
      //     }
      //   ]
      // }
    ]
  },
  financial,
  {
    path: '/hatch',
    component: resolve => require(['@/layout/lateral/index'], resolve),
    redirect: '/hatch/interact/policy',
    meta: {
      title: '孵化互动',
      icon: 'server',
      role: 'ent:hatch'
    },
    children: [
      {
        path: 'interact',
        name: 'HatchInteract',
        component: resolve => require(['@/layout/EmptyView'], resolve),
        redirect: '/hatch/interact/policy',
        meta: {
          title: '孵化互动',
          icon: 'thumb-up',
          role: 'ent:hatch:interact',
          breadcrumb: false
        },
        children: [
          {
            path: 'policy',
            name: 'EnterprisePolicy',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/policy/policy-basic'
              ], resolve),
            meta: {
              title: '政策速递',
              icon: 'icon-menu-policy',
              role: 'ent:hatch:interact:policy',
              desc: '掌握一手政策，了解市场动态',
              type: 'danger'
            }
          },
          {
            path: 'policy/detail',
            name: 'EnterprisePolicyDetail',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/policy/policy-basic/detail'
              ], resolve),
            meta: {
              title: '政策详情',
              icon: 'thumb-up',
              role: 'ent:hatch:interact:policy:detail',
              hideSubMenu: true
            }
          },
          {
            path: 'activity',
            name: 'EnterpriseActivity',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/activity/activity-basic'
              ], resolve),
            meta: {
              title: '园区活动',
              icon: 'icon-menu-activity',
              role: 'ent:hatch:interact:activity',
              desc: '企业辅导、投融资对接、联合团建……',
              type: 'primary'
            }
          },
          {
            path: 'activity/detail',
            name: 'EnterpriseActivityDetail',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/activity/activity-basic/detail'
              ], resolve),
            meta: {
              title: '活动详情',
              icon: 'icon-color-policy',
              role: 'ent:hatch:interact:activity:detail',
              hideSubMenu: true
            }
          },
          {
            path: 'activity/topicDetail',
            name: 'EnterpriseActivityTopicDetail',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/activity/activity-basic/topicDetail'
              ], resolve),
            meta: {
              title: '专题详情',
              icon: 'icon-color-policy',
              role: 'system:dept:list',
              hideSubMenu: true
            }
          },
          // {
          //   path: 'course',
          //   name: 'EnterpriseCourse',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/hatch/course/course-basic'
          //     ], resolve),
          //   meta: {
          //     title: '园区课程',
          //     icon: 'icon-menu-course',
          //     role: 'system:dept:list',
          //     desc: '随时随地利用闲暇时间学习充电，为未来的胜利铺路',
          //     type: 'success'
          //   }
          // },
          // {
          //   path: 'course/detail',
          //   name: 'EnterpriseCourseDetail',
          //   component: resolve =>
          //     require([
          //       '@/views/enterprise/hatch/course/course-basic/detail'
          //     ], resolve),
          //   meta: {
          //     title: '课程详情',
          //     icon: 'icon-color-policy',
          //     role: 'system:dept:list',
          //     hideSubMenu: true
          //   }
          // },
          {
            path: 'field',
            name: 'EnterpriseField',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/field/field-special'
              ], resolve),
            meta: {
              title: '场地预定',
              icon: 'icon-menu-field',
              role: 'ent:hatch:interact:field',
              desc: '园区公共场地资源在线预定使用',
              type: 'warning'
            }
          },
          {
            path: 'field/detail',
            name: 'EnterpriseFieldDetail',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/field/field-special/detail'
              ], resolve),
            meta: {
              title: '场地详情',
              icon: 'icon-color-field',
              role: 'ent:hatch:interact:field:detail',
              hideSubMenu: true
            }
          },
          {
            path: 'field/record',
            name: 'EnterpriseFieldRecord',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/field/field-special/record'
              ], resolve),
            meta: {
              title: '预定记录',
              icon: 'icon-color-field',
              role: 'ent:hatch:interact:field:record',
              hideSubMenu: true
            }
          },
          {
            path: 'problem',
            name: 'EnterpriseProblem',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/problem/problem-basic'
              ], resolve),
            meta: {
              title: '问题反馈',
              icon: 'icon-menu-problem',
              role: 'ent:hatch:interact:problem',
              btns: btnDisposeFn('ent:hatch:interact:problem', [
                'FEEDBACK',
                'VIEW'
              ]),
              desc: '遇到的任何问题向我们提供反馈',
              type: 'warning'
            }
          },
          {
            path: 'problem/create',
            name: 'EnterpriseProblemCreate',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/problem/problem-basic/create'
              ], resolve),
            meta: {
              title: '问题反馈创建',
              icon: 'icon-color-problemy',
              role: 'ent:hatch:interact:problem:create',
              hideSubMenu: true
            }
          },
          {
            path: 'problem/detail',
            name: 'EnterpriseProblemDetail',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/problem/problem-basic/detail'
              ], resolve),
            meta: {
              title: '问题反馈详情',
              icon: 'icon-color-problem',
              role: 'ent:hatch:interact:problem:detail',
              btns: btnDisposeFn('ent:hatch:interact:problem:detail', [
                'REPLY'
              ]),
              hideSubMenu: true
            }
          },
          {
            path: 'announcement',
            name: 'Announcement',
            component: resolve =>
              require(['@/views/enterprise/hatch/announcement/index'], resolve),
            meta: {
              title: '消息通知',
              icon: 'icon-menu-activity',
              role: 'ent:hatch:interact:announcement',
              desc: '给企业发送的所有公告、消息',
              type: 'primary'
            }
          },
          {
            path: 'capitalNews',
            name: 'CapitalNews',
            component: resolve =>
              require([
                '@/views/enterprise/hatch/capitalNews/capitalNews-basic'
              ], resolve),
            meta: {
              title: '企业资讯',
              icon: 'icon-menu-policy',
              role: 'ent:hatch:interact:capitalNews',
              desc: '给企业发送的所有资讯',
              type: 'primary'
            }
          }
        ]
      }
    ]
  },
  // {
  //   path: '/services',
  //   component: resolve => require(['@/layout/lateral/index'], resolve),
  //   redirect: '/services/company/mechanism',
  //   meta: {
  //     title: '企业服务',
  //     icon: 'chart-bubble'
  //   },
  //   children: [
  //     {
  //       path: 'company',
  //       name: 'CompanyServices',
  //       component: resolve => require(['@/layout/EmptyView'], resolve),
  //       redirect: '/services/company/mechanism',
  //       meta: {
  //         title: '企业服务',
  //         icon: 'thumb-up',
  //         role: 'system:dept:list',
  //         breadcrumb: false
  //       },
  //       children: [
  //         {
  //           path: 'mechanism',
  //           name: 'EnterpriseMechanism',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/mechanism/mechanism-basic'
  //             ], resolve),
  //           meta: {
  //             title: '服务机构',
  //             icon: 'icon-menu-mechanism',
  //             role: 'system:dept:list',
  //             desc: '打破企业间的隔阂，加强业务往来',
  //             type: 'primary'
  //           }
  //         },
  //         {
  //           path: 'mechanism/detail',
  //           name: 'EnterpriseMechanismDetail',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/mechanism/mechanism-basic/detail'
  //             ], resolve),
  //           meta: {
  //             title: '机构详情',
  //             icon: 'thumb-up',
  //             role: 'system:dept:list',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'product',
  //           name: 'EnterpriseProduct',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/product/product-basic'
  //             ], resolve),
  //           meta: {
  //             title: '机构产品',
  //             icon: 'icon-menu-product',
  //             role: 'system:dept:list',
  //             desc: '探索无限商机和服务',
  //             type: 'warning'
  //           }
  //         },
  //         {
  //           path: 'product/detail',
  //           name: 'EnterpriseProductDetail',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/product/product-basic/detail'
  //             ], resolve),
  //           meta: {
  //             title: '产品详情',
  //             icon: 'thumb-up',
  //             role: 'system:dept:list',
  //             hideSubMenu: true
  //           }
  //         }
  //       ]
  //     },
  //     {
  //       path: 'internet',
  //       name: 'IndustrialInternet',
  //       component: resolve => require(['@/layout/EmptyView'], resolve),
  //       redirect: '/services/internet/corporateHome',
  //       meta: {
  //         title: '产业互联网',
  //         icon: 'thumb-up',
  //         role: 'system:dept:list',
  //         breadcrumb: false
  //       },
  //       children: [
  //         {
  //           path: 'serveHall',
  //           name: 'ServeHall',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/serveHall/list'
  //             ], resolve),
  //           meta: {
  //             title: '服务大厅',
  //             icon: 'icon-menu-clover',
  //             role: 'system:dept:list',
  //             desc: '充分挖掘产业潜力,实现资源链通',
  //             type: 'primary'
  //           }
  //         },
  //         {
  //           path: 'corporateHome',
  //           name: 'CorporateHome',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/corporateHome/list'
  //             ], resolve),
  //           meta: {
  //             title: '企业主页',
  //             icon: 'icon-menu-home',
  //             role: 'system:dept:list',
  //             desc: '构建靓丽的一道企业风景线',
  //             type: 'primary',
  //             cache: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeInfoCreate',
  //           name: 'CorporateHomeInfoCreate',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/corporateHome/create/infoCreate'
  //             ], resolve),
  //           meta: {
  //             title: '添加企业简介',
  //             icon: 'icon-menu-home',
  //             role: 'system:dept:list',
  //             desc: '登记企业简介做好风险防备',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeProductCreate',
  //           name: 'CorporateHomeProductCreate',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/corporateHome/create/productCreate'
  //             ], resolve),
  //           meta: {
  //             title: '添加企业产品',
  //             icon: 'icon-menu-home',
  //             role: 'system:dept:list',
  //             desc: '登记企业产品做好风险防备',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeProductDetail',
  //           name: 'CorporateHomeProductDetail',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/detail/productDetail'
  //             ], resolve),
  //           meta: {
  //             title: '产品详情',
  //             role: 'system:dept:list',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeServeCreate',
  //           name: 'CorporateHomeServeCreate',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/corporateHome/create/serveCreate'
  //             ], resolve),
  //           meta: {
  //             title: '添加企业服务',
  //             icon: 'icon-menu-home',
  //             role: 'system:dept:list',
  //             desc: '登记企业服务做好风险防备',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeServeDetail',
  //           name: 'CorporateHomeServeDetail',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/detail/serveDetail'
  //             ], resolve),
  //           meta: {
  //             title: '服务详情',
  //             role: 'system:dept:list',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeDemandCreate',
  //           name: 'CorporateHomeDemandCreate',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/corporateHome/create/demandCreate'
  //             ], resolve),
  //           meta: {
  //             title: '添加企业需求',
  //             icon: 'icon-menu-home',
  //             role: 'system:dept:list',
  //             desc: '登记企业需求做好风险防备',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'corporateHomeDemandDetail',
  //           name: 'CorporateHomeDemandDetail',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/detail/demandDetail'
  //             ], resolve),
  //           meta: {
  //             title: '需求详情',
  //             role: 'system:dept:list',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'enterpriseCard',
  //           name: 'EnterpriseCard',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/enterpriseCard/list'
  //             ], resolve),
  //           meta: {
  //             title: '企业名片',
  //             icon: 'icon-menu-card',
  //             role: 'system:dept:list',
  //             desc: '筑牢企业人脉不可忽略的方式',
  //             type: 'primary'
  //           }
  //         },
  //         {
  //           path: 'enterpriseCardCreate',
  //           name: 'EnterpriseCardCreate',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/enterpriseCard/create'
  //             ], resolve),
  //           meta: {
  //             title: '新增名片',
  //             icon: 'icon-menu-card',
  //             role: 'system:dept:list',
  //             desc: '筑牢企业人脉不可忽略的方式',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'deliveryDetails',
  //           name: 'DeliveryDetails',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/enterpriseCard/deliveryDetails'
  //             ], resolve),
  //           meta: {
  //             title: '投递详情',
  //             icon: 'icon-menu-card',
  //             role: 'system:dept:list',
  //             desc: '构建靓丽的一道企业风景线',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         },
  //         {
  //           path: 'consultDetails',
  //           name: 'ConsultDetails',
  //           component: resolve =>
  //             require([
  //               '@/views/enterprise/services/industryInternet/industryInternet-basic/enterpriseCard/consultDetails'
  //             ], resolve),
  //           meta: {
  //             title: '咨询详情',
  //             icon: 'icon-menu-card',
  //             role: 'system:dept:list',
  //             desc: '构建靓丽的一道企业风景线',
  //             type: 'primary',
  //             hideSubMenu: true
  //           }
  //         }
  //       ]
  //     }
  //   ]
  // },
  {
    path: '/owner',
    component: resolve => require(['@/layout/lateral/index'], resolve),
    redirect: '/owner/index',
    meta: {
      title: '业主切换',
      icon: 'home'
    },
    hidden: true,
    children: [
      {
        path: 'index',
        name: 'EnterParkOwnerSwitch',
        component: resolve => require(['@/views/enterprise/owner'], resolve),
        meta: {
          title: '业主切换',
          icon: 'icon-color-course',
          hideSubMenu: true
        }
      }
    ]
  }
]

export default enterpriseRoutes
