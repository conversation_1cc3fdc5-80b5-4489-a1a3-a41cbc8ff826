/*
 * @Descripttion: 路由暴露权限
 * @Author: 田柱
 * @Date: 2021-04-14 16:57:47
 * @LastEditTime: 2021-09-15 15:46:13
 */

/* 管理端 */
// 资产运营
import assetOperationRoutes from './assetOperation'
// 产业服务
import operationServiceRoutes from './operationService'
// 企业管理
import enterpriseMangeRoutes from './enterpriseMange'
// 财务管理
const financialRoutes =
  process.env.VUE_APP_FINANCE_TYPE === 'jtyh'
    ? require('./jtyhFinancial').default
    : require('./financial').default
// 统一支撑
import unifySupportRoutes from './unifySupport'
// 智慧党群
import partyRoutes from './party'

/* 404 */
import notFoundRoute from './notFound'

import { _genRouterMap } from '../routerFun'
//资产运营
const assetOperationRoutesMap = _genRouterMap(assetOperationRoutes)
//产业服务
const operationServiceRoutesMap = _genRouterMap(operationServiceRoutes)
// 企业管理
const enterpriseMangeRoutesMap = _genRouterMap(enterpriseMangeRoutes)
//财务管理
const financialRoutesMap = _genRouterMap(financialRoutes)
// 统一支撑
const unifySupportRoutesMap = _genRouterMap(unifySupportRoutes)
// 智慧党群
const partyRoutesMap = _genRouterMap(partyRoutes)

/* 企业端 */
import enterpriseRoutes from './enterprise'

export default {
  // 管理平台
  manage: {
    assetOperation: {
      name: '资产运营',
      iconName: '',
      routes: assetOperationRoutes,
      routesMap: assetOperationRoutesMap,
      routerArray: Object.keys(assetOperationRoutesMap)
    },
    opeartionService: {
      name: '产业服务',
      iconName: '',
      routes: operationServiceRoutes,
      routesMap: operationServiceRoutesMap,
      routerArray: Object.keys(operationServiceRoutesMap)
    },
    enterpriseManage: {
      name: '企业管理',
      iconName: '',
      routes: enterpriseMangeRoutes,
      routesMap: enterpriseMangeRoutesMap,
      routerArray: Object.keys(enterpriseMangeRoutesMap)
    },
    financial: {
      name: '财务管理',
      iconName: '',
      routes: financialRoutes,
      routesMap: financialRoutesMap,
      routerArray: Object.keys(financialRoutesMap)
    },
    party: {
      name: '智慧党群',
      iconName: '',
      routes: partyRoutes,
      routesMap: partyRoutesMap,
      routerArray: Object.keys(partyRoutesMap)
    },
    unifySupport: {
      name: '统一支撑',
      iconName: '',
      routes: unifySupportRoutes,
      routesMap: unifySupportRoutesMap,
      routerArray: Object.keys(unifySupportRoutesMap)
    }
  },
  // 企业端
  enterprise: enterpriseRoutes.concat(notFoundRoute)
}
