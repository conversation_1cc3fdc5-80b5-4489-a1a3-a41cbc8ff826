/*
 * @Descripttion: 工作台路由表
 * @Author: 田柱
 * @Date: 2021-04-14 11:31:40
 * @LastEditTime: 2022-05-10 17:34:46
 */

const workspaceRoutes = {
  path: `/dashboard`,
  name: `Dashboard`,
  component: resolve => require(['@/layout/default/index'], resolve),
  redirect: `/dashboard/index`,
  meta: {
    title: '工作台',
    icon: 'dashboard',
    allow: true
  },
  children: [
    {
      path: 'index',
      name: `WorkspaceDashboard`,
      component: resolve => require(['@/views/manage/dashboard'], resolve),
      meta: {
        title: '工作台',
        icon: 'dashboard',
        allow: true,
        cache: true
      }
    }
  ]
}

export default workspaceRoutes
