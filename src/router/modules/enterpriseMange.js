import btnDisposeFn from '@/router/modules/btns'

const enterpriseMangeRoutes = [
  {
    path: '/business',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/business/index',
    meta: {
      title: '企业库',
      desc: '历史入园企业全量信息管理',
      icon: 'library',
      role: 'enterprise:business',
      isTools: true // 企业搜索工具
    },
    children: [
      {
        path: 'index',
        name: 'EnterpriseList',
        component: resolve =>
          require([
            '@/views/manage/business/enterprise/enterprise-basic/enterpriseList'
          ], resolve),
        meta: {
          title: '企业库',
          icon: 'library',
          iconShort: 'icon-menu-enterprise', // 快捷菜单
          role: 'enterprise:business:enterpriseList',
          btns: btnDisposeFn('enterprise:business:enterpriseList', [
            'ADD_REMARKS',
            'ENTER_RELEVANCE',
            'ENTER_RENAMING',
            'RENEW_CONTACTS',
            'QUALIFICATION_HONOR_REGISTER'
          ]),
          type: 'warning',
          breadcrumb: false
        }
      },
      {
        path: 'enterpriseDetails',
        name: 'EnterpriseDetails',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/business/enterprise/enterprise-basic/companyDetails'
          ], resolve),
        meta: {
          title: '企业详情',
          icon: 'logo-codepen',
          role: 'enterprise:business:enterpriseDetails',
          btns: btnDisposeFn('enterprise:business:enterpriseDetails', [
            'ADD_REMARKS',
            'ENTER_RELEVANCE',
            'ENTER_RENAMING',
            'RENEW_CONTACTS',
            'ADD_TAGS'
          ])
        }
      },
      {
        path: 'index/qualificationHonor',
        name: 'QualificationHonor',
        hidden: true,
        component: resolve =>
          require(['@/views/manage/business/qualificationHonor'], resolve),
        meta: {
          title: '资质荣誉登记',
          icon: 'logo-codepen'
        }
      }
    ]
  },
  {
    path: '/regionalEconomy',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/regionalEconomy/index',
    meta: {
      title: '企业数据大脑',
      desc: '中安创谷科技园企业数据大脑',
      icon: 'chat-poll-fill',
    },
    children: [
      {
        path: 'index',
        name: 'DataLedger',
        component: resolve =>
          require(['@/views/manage/house/regionalEconomy/dataLedger'], resolve),
        meta: {
          title: '企业数据大脑',
          icon: 'chat-poll-fill',
          role: 'enterprise:regionalEconomy:dataLedger',
          type: 'success',
          btns: btnDisposeFn('enterprise:regionalEconomy:dataLedger', [
            'BASIC_INFO',
            'B_I_LEADING_OUT',
            'CHANGE_INFO',
            'C_I_LEADING_OUT',
            'INVEST_INFO',
            'I_I_LEADING_OUT',
            'BRANCH_INFO',
            'B_R_I_LEADING_OUT',
            'QUALIFICATIONS_INFO',
            'Q_I_LEADING_OUT',
            'SOFTWARE_COPYRIGHT',
            'S_C_LEADING_OUT',
            'ENTERPRISE_PATENTS',
            'E_P_LEADING_OUT',
            'TRADEMARK_INFO',
            'T_I_LEADING_OUT',
            'OPERATING_INFO_QUARTERLY',
            'O_I_Q_LEADING_OUT',
            'BUSINESS_INFO_MONTHLY',
            'B_I_M_LEADING_OUT',
            'SOCIAL_SECURITY_INFO',
            'S_S_I_LEADING_OUT',
            'JUDGMENT_DEBTOR',
            'J_D_LEADING_OUT',
            'DISHONEST_PERSONS',
            'D_P_LEADING_OUT',
            //目录
            'BUSINESS_INFO_CATALOGUE',
            'QUALIFICATIONS_CATALOGUE',
            'INTELLECTUAL_PROPERTY_CATALOGUE',
            'OPERATING_CATALOGUE',
            'SOCIAL_SECURITY_CATALOGUE',
            'ENTERPRISE_RISK_CATALOGUE',
          ])
        }
      },
    ]
  },

  // {
  //   path: '/enterpriseOverview',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/enterpriseOverview/index',
  //   meta: {
  //     title: '企业概览',
  //     desc: '在园企业经营发展情况数据概览',
  //     icon: 'vertical-line',
  //     role: 'enterprise:overview',
  //     isTools: true // 企业搜索工具
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'EnterpriseOverviewBasic',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/business/enterpriseOverview/enterpriseOverview-basic'
  //         ], resolve),
  //       meta: {
  //         title: '企业概览',
  //         icon: 'vertical-line',
  //         role: 'enterprise:overview:list',
  //         btns: btnDisposeFn('enterprise:overview:list', [
  //           'MANAGE',
  //           'DATA_SWITCH',
  //           'INDICATOR_EDIT'
  //         ])
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/dynamicHigh',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/dynamicHigh/index',
  //   meta: {
  //     title: '企业检索',
  //     desc: '复杂条件搜索在园企业并一键导出',
  //     icon: 'search-app',
  //     role: 'enterprise:dynamicHigh',
  //     isTools: true // 企业搜索工具
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'DynamicHigh',
  //       // hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/business/enterpriseDynamic/enterpriseDynamic-basic/highSearch'
  //         ], resolve),
  //       meta: {
  //         title: '企业检索',
  //         icon: 'search-app',
  //         role: 'enterprise:dynamicHigh:list'
  //       }
  //     },
  //     {
  //       path: 'dynamicResult',
  //       name: 'DynamicResult',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/business/enterpriseDynamic/enterpriseDynamic-basic/dynamicResult'
  //         ], resolve),
  //       meta: {
  //         title: '检索结果',
  //         icon: 'search-app'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/enterpriseDynamic',
  //   name: 'EnterpriseDynamic',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/enterpriseDynamic/index',
  //   meta: {
  //     title: '企业舆情',
  //     desc: '在园企业的社会舆情信息监测和观察',
  //     icon: 'sentiment',
  //     role: 'enterprise:enterpriseDynamic',
  //     isTools: true // 企业搜索工具
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'EnterpriseDynamicBoard',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/business/enterpriseDynamic/enterpriseDynamic-basic/board'
  //         ], resolve),
  //       meta: {
  //         title: '企业舆情',
  //         icon: 'sentiment',
  //         role: 'enterprise:enterpriseDynamic:list'
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/staff',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/staff/index',
  //   meta: {
  //     title: '企业员工',
  //     desc: '在园企业员工信息的集中管理',
  //     icon: 'icon-staff',
  //     role: 'enterprise:staff',
  //     isTools: true // 企业搜索工具
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'StaffBasic',
  //       component: resolve =>
  //         require(['@/views/manage/business/staffs/enterprise/index'], resolve),
  //       meta: {
  //         title: '企业员工',
  //         icon: 'icon-staff',
  //         iconShort: 'icon-menu-user',
  //         role: 'enterprise:staff:list',
  //         btns: btnDisposeFn('enterprise:staff:list', ['BLACKLIST']),
  //         type: 'primary',
  //         cache: true
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/information',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/information/index',
  //   meta: {
  //     title: '企业填报',
  //     desc: '在线发布信息填报任务并集中管理',
  //     icon: 'info-filing',
  //     role: 'enterprise:information',
  //     isTools: true // 企业搜索工具
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'Information',
  //       component: resolve =>
  //         require(['@/views/manage/business/information'], resolve),
  //       meta: {
  //         title: '企业填报',
  //         icon: 'info-filing',
  //         breadcrumb: false,
  //         role: 'enterprise:information:list',
  //         btns: btnDisposeFn('enterprise:information:list', [
  //           'PUBLISH_FILLING_TASKS'
  //         ]),
  //         type: 'success',
  //         cache: true
  //       }
  //     },
  //     {
  //       path: 'index/informationDetail',
  //       name: 'InformationDetail',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/business/information/informationDetail'
  //         ], resolve),
  //       meta: {
  //         title: '填报详情',
  //         icon: 'logo-codepen',
  //         role: 'enterprise:information:informationDetail',
  //         btns: btnDisposeFn('enterprise:information:informationDetail', [
  //           'REMINDER'
  //         ])
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/signingLedger',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/signingLedger/index',
  //   meta: {
  //     title: '母子公司',
  //     icon: 'institute',
  //     role: 'assets:replace:signingLedger'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'SigningLedgerManage',
  //       component: resolve =>
  //         require(['@/views/manage/business/signingLedger'], resolve),
  //       meta: {
  //         title: '母子公司',
  //         icon: 'institute',
  //         role: 'assets:replace:signingLedger',
  //         btns: btnDisposeFn('assets:replace:signingLedger', [
  //           'ADD',
  //           'EDIT',
  //           'VIEW',
  //           'DELETE'
  //         ]),
  //         type: 'success',
  //         cache: true,
  //         breadcrumb: false
  //       }
  //     }
  //   ]
  // }
]

export default enterpriseMangeRoutes
