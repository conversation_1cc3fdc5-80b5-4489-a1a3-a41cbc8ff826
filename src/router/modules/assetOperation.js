import btnDisposeFn from '@/router/modules/btns'

const assetOperationRoutes = [
  {
    path: '/houseManage',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/houseManage/index',
    meta: {
      title: '房源管理',
      desc: '物理空间数据的一站式管理',
      icon: 'house-manage',
      role: 'assets:houseManage'
    },
    children: [
      {
        path: 'index',
        name: 'AssetsHouseMaintainBasic',
        component: resolve =>
          require(['@/views/manage/house/maintain/maintain-basic'], resolve),
        meta: {
          title: '房源库',
          icon: 'icon-menu-house',
          role: 'assets:houseManage:maintain',
          type: 'success',
          btns: btnDisposeFn('assets:houseManages:maintain', [
            'EDIT',
            'ADD',
            'LEADING_OUT',
            'DELETE',
            'LEADING_IN',
            'SORT',
            'VIEW',
            'MOVED_OUT'
          ])
        }
      },
      {
        path: 'board',
        name: 'AssetsBoardSpecial',
        component: resolve =>
          require(['@/views/manage/house/board/board-special'], resolve),
        meta: {
          title: '房源看板',
          icon: 'bulletpoint',
          role: 'assets:houseManage:board',
          btns: btnDisposeFn('assets:houseManage:board', ['HEALTH', 'MARK'])
        }
      },
      {
        path: 'houseList',
        name: 'HouseListAccount',
        component: resolve =>
          require([
            '@/views/manage/house/houseList/houseList-account'
          ], resolve),
        meta: {
          title: '房源台账',
          icon: 'root-list',
          role: 'assets:houseManage:houseList',
          cache: true,
          btns: btnDisposeFn('assets:houseManage:houseList', [
            'LEADING_OUT'
          ])
        }
      }
      // {
      //   path: 'houseList/houseDetailsAccount',
      //   name: 'AssetsHouseDetailsAccount',
      //   hidden: true,
      //   component: resolve =>
      //     require([
      //       '@/views/manage/house/houseList/house-details-account'
      //     ], resolve),
      //   meta: {
      //     title: '房源详情',
      //     icon: 'view-module',
      //     role: 'assets:houseManage:houseDetailsAccount',
      //     btns: btnDisposeFn('assets:houseManage:houseDetailsAccount', [
      //       'HEALTH',
      //       'MARK',
      //       'VIEW'
      //     ])
      //   }
      // }
    ]
  },
  {
    path: '/investment',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/investment/index',
    meta: {
      title: '招商管理',
      desc: '招商项目从线索到落地的全过程跟踪',
      icon: 'feedback',
      role: 'assets:investment'
    },
    children: [
      // {
      //   path: 'index',
      //   name: 'ProjectPool',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/house/investment/projectPool/projectPool-basic'
      //     ], resolve),
      //   meta: {
      //     title: '招商项目库',
      //     icon: 'icon-menu-project',
      //     role: 'assets:investment:project',
      //     btns: btnDisposeFn('assets:investment:project', [
      //       'ADD',
      //       'EMPLACE',
      //       'LEADING_OUT',
      //       'LEADING_IN',
      //       'EDIT',
      //       'DELETE',
      //       'TRANSACT_PARK'
      //     ])
      //   }
      // },
      {
        path: 'index',
        name: 'ProjectPool',
        component: resolve =>
          require([
            '@/views/manage/house/investment/projectPool/projectPool-special'
          ], resolve),
        meta: {
          title: '项目管理',
          icon: 'icon-menu-project',
          role: 'assets:investment:projectPool',
          btns: btnDisposeFn('assets:investment:projectPool', [
            'ADD',
            'EMPLACE',
            'LEADING_OUT',
            'LEADING_IN',
            'EDIT',
            'DELETE',
            'TRANSACT_PARK',
            'PROJECT_MANAGER',
            'AUDIT',
            'FOLLOW_EDIT',
            'FOLLOW_DELETE'
          ])
        }
      },
      // {
      //   path: 'incubator',
      //   name: 'Incubator',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/house/investment/incubator/incubator-basic'
      //     ], resolve),
      //   meta: {
      //     title: '孵化器管理',
      //     icon: 'icon-menu-project',
      //     role: 'assets:investment:incubator',
      //     btns: btnDisposeFn('assets:investment:incubator', [
      //       'VIEW',
      //       'EDIT',
      //       'DELETE',
      //       'ADD_ENT',
      //       'ADD'
      //     ])
      //   }
      // },
      // {
      //   path: 'carrierManagement',
      //   name: 'CarrierManagement',
      //   component: resolve =>
      //     require([
      //       '@/views/manage/house/investment/carrierManagement/carrierManagement-basic'
      //     ], resolve),
      //   meta: {
      //     title: '载体管理',
      //     icon: 'icon-menu-project',
      //     role: 'assets:investment:carrierManagement',
      //     btns: btnDisposeFn('assets:investment:carrierManagement', [
      //       'VIEW',
      //       'EDIT',
      //       'DELETE',
      //       'ADD'
      //     ])
      //   }
      // },
      {
        path: 'contactLedger',
        name: 'ContactLedgerManage',
        component: resolve =>
          require([
            '@/views/manage/house/investment/contactLedger/contactLedger-basic'
          ], resolve),
        meta: {
          title: '项目名片',
          icon: 'icon-menu-project',
          role: 'assets:investment:contactLedger',
          btns: btnDisposeFn('assets:investment:contactLedger', [
            'LEADING_OUT',
            'VIEW'
          ])
        }
      }
    ]
  },
  {
    path: '/enterPark',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/enterPark/intentCustomerList',
    meta: {
      title: '入园管理',
      desc: '企业提交信息直至合同签约的过程管理',
      icon: 'icon-statistics',
      role: 'assets:enterpark'
    },
    children: [
      {
        path: 'intentCustomerList',
        name: 'IntentCustomerList',
        component: resolve =>
          require([
            '@/views/manage/house/settleIn/project-special/intentCustomer/list'
          ], resolve),
        props: { view: 0 },
        meta: {
          title: '入园项目',
          icon: 'icon-menu-project',
          role: 'assets:enterPark:intentCustomerList:list',
          type: 'success',
          cache: true
          // btns: btnDisposeFn('assets:enterPark:intentCustomerList:list', [
          //   'LEADING_OUT',
          //   'PROJECT_ENTRY'
          // ])
        }
      },
      {
        path: 'projectCreate',
        name: 'ProjectCreate',
        component: resolve =>
          require([
            '@/views/manage/house/settleIn/project-special/intentCustomer/projectCreate'
          ], resolve),
        props: { view: 0 },
        hidden: true,
        meta: {
          title: '项目创建',
          icon: 'icon-menu-project',
          role: 'assets:enterPark:projectCreate:create',
          type: 'success',
          cache: true
          // btns: btnDisposeFn('assets:enterPark:projectCreate:create', [])
        }
      },
      {
        path: 'intentCustomerDetail',
        name: 'IntentCustomerDetail',
        component: resolve =>
          require([
            '@/views/manage/house/settleIn/project-special/intentCustomer/detail'
          ], resolve),
        props: { view: 0 },
        hidden: true,
        meta: {
          title: '项目详情',
          icon: 'icon-menu-project',
          role: 'assets:enterPark:projectDetail:detail',
          type: 'success',
          btns: btnDisposeFn('assets:enterPark:projectDetail:detail', [
            'REVISE_RECORD',
            'ADD_RECORD'
          ])
        }
      },
      {
        path: 'simulateEnterPark',
        name: 'SettleInProposedBasic',
        component: resolve =>
          require([
            '@/views/manage/house/settleIn/project-basic/projectList/proposed.vue'
          ], resolve),
        props: { view: 1 },
        meta: {
          title: '入园办理',
          icon: 'icon-menu-enter',
          role: 'assets:enterPark:simulate:list',
          type: 'success',
          btns: btnDisposeFn('assets:enterPark:simulate:list', [
            'PROJECT_ENTRY',
            'VIEW'
          ])
        }
      },
      {
        path: 'settledCreate',
        name: 'SettledCreate',
        component: resolve =>
          require([
            '@/views/manage/house/settleIn/project-special/intentCustomer/settledContainer/settledCreate'
          ], resolve),
        hidden: true,
        meta: {
          title: '项目创建',
          icon: 'app',
          noTagView: true
          // role: 'assets:enterpark:settledCreate'
        }
      }
    ]
  },
  {
    path: '/contract',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/contract/index',
    meta: {
      title: '合同管理',
      desc: '在线签订合同并对合同全生命周期管理',
      icon: 'icon-contract',
      role: 'assets:contract'
    },
    children: [
      {
        path: 'index',
        name: 'ContractSpecial',
        component: resolve =>
          require(['@/views/manage/house/contract/contract-special'], resolve),
        meta: {
          title: '合同库',
          icon: 'icon-contract',
          role: 'assets:contract:list',
          type: 'primary',
          cache: true,
          btns: btnDisposeFn('assets:contract:list', [
            'ADD',
            'EDIT',
            'LEADING_OUT',
            'SORT',
            'INITIATE',
            'LEADING_IN',
            'VIEW',
            'RECALL_PROCESS',
            'RENEWAL',
            'SIGN',
            'CONTRACT_CANCEL',
            'DELETE'
          ])
        }
      },
      {
        path: 'contractCreate',
        name: 'ContractCreate',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/house/contract/contract-special/create'
          ], resolve),
        meta: {
          title: '合同登记',
          icon: 'view-module',
          role: 'assets:contract:contractCreate',
          noTagView: true,
          btns: btnDisposeFn('assets:contract:contractCreate', [
            'CANCEL',
            'PRESERVE',
            'SUBMIT_PROCESS',
            'DETAIL',
            'CHOOSE_HOUSE',
            'RENEW',
            'ADD',
            'TOTAL_CALCULATION'
          ])
        }
      },
      {
        path: 'index/contractDetails',
        name: 'ContractDetailsBasic',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/house/contract/contract-special/contract-detail'
          ], resolve),
        meta: {
          title: '合同详情',
          icon: 'view-module',
          role: 'assets:contract:contractDetails',
          btns: btnDisposeFn('assets:contract:contractDetails', [
            'INITIATE',
            'EDIT',
            'VIEW',
            'RECALL_PROCESS',
            'RENEWAL',
            'SIGN',
            'CONTRACT_CANCEL',
            'DELETE',
            'AUDIT_TRAIL',
            'REMEDIATE'
          ])
        }
      }
    ]
  },
  {
    path: '/rentOut',
    component: resolve => require(['@/layout/default/index'], resolve),
    redirect: '/rentOut/replace',
    meta: {
      title: '调房离园',
      desc: '一键为企业办理增加房间、减少房间事项',
      icon: 'adjustable',
      role: 'assets:rentOut'
    },
    children: [
      {
        path: 'replace',
        name: 'ReplaceApply',
        component: resolve =>
          require(['@/views/manage/house/replace/replace-basic'], resolve),
        meta: {
          title: '租赁调整',
          icon: 'adjustable',
          role: 'assets:rentOut:replace',
          type: 'success',
          cache: true,
          btns: btnDisposeFn('assets:rentOut:replace', [
            'LEADING_OUT',
            'REGISTER_TRANSFERS',
            'VIEW'
          ])
        }
      },
      {
        path: 'replace/replaceDetail',
        name: 'ReplaceDetail',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/house/replace/replace-basic/detail'
          ], resolve),
        meta: {
          title: '调整详情',
          icon: 'view-module',
          role: 'assets:rentOut:replaceDetail',
          btns: btnDisposeFn('assets:rentOut:replaceDetail', ['AUDIT']),
          cache: true
        }
      },

      {
        path: 'contractCreate',
        name: 'ContractCreate',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/house/replace/replace-basic/checkOutContract'
          ], resolve),
        meta: {
          title: '合同登记',
          icon: 'view-module',
          role: 'assets:contract:contractCreate',
          noTagView: true,
          btns: btnDisposeFn('assets:contract:contractCreate', [
            'CANCEL',
            'PRESERVE',
            'SUBMIT_PROCESS',
            'DETAIL',
            'CHOOSE_HOUSE',
            'RENEW',
            'ADD',
            'TOTAL_CALCULATION'
          ])
        }
      },
      {
        path: 'contractDetails',
        name: 'ContractDetailsBasic',
        hidden: true,
        component: resolve =>
          require([
            '@/views/manage/house/replace/replace-basic/checkOutContractDetail'
          ], resolve),
        meta: {
          title: '合同详情',
          icon: 'view-module',
          role: 'assets:contract:contractDetails',
          btns: btnDisposeFn('assets:contract:contractDetails', [
            'INITIATE',
            'EDIT',
            'VIEW',
            'RECALL_PROCESS',
            'RENEWAL',
            'SIGN',
            'CONTRACT_CANCEL',
            'DELETE',
            'AUDIT_TRAIL',
            'REMEDIATE'
          ])
        }
      },
      {
        path: 'leavePark',
        name: 'LeaveHandleList',
        component: resolve =>
          require(['@/views/manage/house/leave/leave-handle/list'], resolve),
        meta: {
          title: '退租离园',
          icon: 'icon-menu-leave',
          role: 'assets:rentOut:leavePark',
          type: 'primary',
          cache: true
          // btns: btnDisposeFn('assets:rentOut:leavePark', [])
        }
      },
      {
        path: 'leavePark/leaveParkDetail',
        name: 'LeaveParkDetail',
        hidden: true,
        component: resolve =>
          require(['@/views/manage/house/leave/leave-handle/detail'], resolve),
        meta: {
          title: '离园详情',
          icon: 'view-module',
          role: 'assets:rentOut:leaveParkDetail'
          // btns: btnDisposeFn('assets:rentOut:leaveParkDetail', [])
        }
      }
    ]
  },
  // {
  //   path: '/rentalHousing',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/rentalHousing/applyHouse',
  //   meta: {
  //     title: '公寓住宅',
  //     desc: '配套公寓在线全流程管理',
  //     icon: 'building',
  //     role: 'asset:rentalHousing'
  //   },
  //   children: [
  //     {
  //       path: 'applyHouse',
  //       name: 'ApplyHouse',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/applyHouse'
  //         ], resolve),
  //       meta: {
  //         title: '公寓申请',
  //         icon: 'icon-menu-building',
  //         role: 'asset:rentalHousing:applyHouse',
  //         type: 'primary',
  //         btns: btnDisposeFn('asset:rentalHousing:applyHouse', ['VIEW'])
  //       }
  //     },
  //     {
  //       path: 'applyHouse/applyHouseDetail',
  //       name: 'ApplyHouseDetail',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/applyHouse/applyDetail'
  //         ], resolve),
  //       meta: {
  //         title: '租房申请详情',
  //         icon: 'icon-menu-building',
  //         role: 'asset:rentalHousing:applyHouseDetail',
  //         type: 'primary',
  //         btns: btnDisposeFn('asset:rentalHousing:applyHouseDetail', ['AUDIT'])
  //       }
  //     },
  //     {
  //       path: 'applyHouse/applyRenewDetail',
  //       name: 'ApplyRenewDetail',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/applyHouse/renewDetail'
  //         ], resolve),
  //       meta: {
  //         title: '续约申请详情',
  //         icon: 'icon-menu-building',
  //         role: 'asset:rentalHousing:applyRenewDetail',
  //         type: 'primary',
  //         btns: btnDisposeFn('asset:rentalHousing:applyRenewDetail', ['AUDIT'])
  //       }
  //     },
  //     {
  //       path: 'applyHouse/applyQuitDetail',
  //       name: 'ApplyQuitDetail',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/applyHouse/quitHouseDetail'
  //         ], resolve),
  //       meta: {
  //         title: '退房申请详情',
  //         icon: 'icon-menu-building',
  //         role: 'asset:rentalHousing:applyRenewDetail',
  //         type: 'primary',
  //         btns: btnDisposeFn('asset:rentalHousing:applyRenewDetail', [
  //           'AUDIT',
  //           'ADD_ITEM',
  //           'VIEW',
  //           'GO_DISPOSE',
  //           'EDIT',
  //           'DELETE',
  //           'METER_READING_PARK',
  //           'VERIFICATION',
  //           'REMIND_BUSINESSES'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'contractSpecial/renewContractCreate',
  //       name: 'RenewContractCreate',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/applyHouse/renewContractCreate'
  //         ], resolve),
  //       meta: {
  //         title: '合同登记',
  //         icon: 'icon-menu-building',
  //         type: 'primary'
  //       }
  //     },
  //     {
  //       path: 'contractSpecial',
  //       name: 'RentalHousingContractSpecial',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/contract/contract-basic'
  //         ], resolve),
  //       meta: {
  //         title: '合同管理',
  //         icon: 'icon-menu-contract',
  //         role: 'asset:rentalHousing:contractSpecial',
  //         type: 'primary',
  //         cache: true,
  //         btns: btnDisposeFn('asset:rentalHousing:contractSpecial', [
  //           'ADD',
  //           'EDIT',
  //           'LEADING_OUT',
  //           'SORT',
  //           'INITIATE',
  //           'LEADING_IN',
  //           'VIEW',
  //           'RECALL_PROCESS',
  //           'RENEWAL',
  //           'SIGN',
  //           'TERMINATION',
  //           'CONTRACT_CANCEL',
  //           'DELETE'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'contractSpecial/contractCreate',
  //       name: 'RentalHousingContractCreate',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/contract/contract-basic/create'
  //         ], resolve),
  //       hidden: true,
  //       meta: {
  //         title: '合同登记',
  //         icon: 'view-module',
  //         role: 'asset:rentalHousing:contractCreate',
  //         btns: btnDisposeFn('asset:rentalHousing:contractCreate', [
  //           'CANCEL',
  //           'PRESERVE',
  //           'SUBMIT_PROCESS',
  //           'CHOOSE_HOUSE',
  //           'RENEW',
  //           'TOTAL_CALCULATION'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'contractSpecial/contractDetails',
  //       name: 'RentalHousingContractDetails',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/contract/contract-basic/contract-detail'
  //         ], resolve),
  //       meta: {
  //         title: '合同详情',
  //         icon: 'view-module',
  //         role: 'asset:rentalHousing:contractDetails',
  //         btns: btnDisposeFn('asset:rentalHousing:contractDetails', [
  //           'INITIATE',
  //           'EDIT',
  //           'VIEW',
  //           'RECALL_PROCESS',
  //           'RENEWAL',
  //           'SIGN',
  //           'TERMINATION',
  //           'CONTRACT_CANCEL',
  //           'DELETE',
  //           'AUDIT_TRAIL',
  //           'REMEDIATE'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'notify',
  //       name: 'Notify',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/notify/list'
  //         ], resolve),
  //       meta: {
  //         title: '消息通知',
  //         icon: 'icon-menu-leave',
  //         role: 'asset:rentalHousing:notify',
  //         type: 'primary',
  //         cache: true,
  //         btns: btnDisposeFn('asset:rentalHousing:notify', [
  //           'ADD',
  //           'VIEW',
  //           'EDIT',
  //           'DELETE',
  //           'PUBLISH',
  //           'SORT',
  //           'MESSAGE_TYPE'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'notify/notifyType',
  //       name: 'NotifyType',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/notifyType'
  //         ], resolve),
  //       hidden: true,
  //       meta: {
  //         title: '消息类型',
  //         icon: 'icon-menu-leave',
  //         role: 'asset:rentalHousing:notifyType',
  //         type: 'primary',
  //         cache: true
  //       }
  //     },
  //     {
  //       path: 'cohabitant',
  //       name: 'Cohabitant',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/cohabitant'
  //         ], resolve),
  //       meta: {
  //         title: '入住人库',
  //         icon: 'icon-menu-leave',
  //         role: 'asset:rentalHousing:cohabitant',
  //         type: 'primary',
  //         cache: true,
  //         btns: btnDisposeFn('asset:rentalHousing:cohabitant', [
  //           'VIEW',
  //           'LEADING_OUT'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'cohabitantDetail',
  //       name: 'CohabitantDetail',
  //       hidden: true,
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/cohabitant/detail'
  //         ], resolve),
  //       meta: {
  //         title: '详情',
  //         icon: 'icon-menu-leave',
  //         role: 'asset:rentalHousing:cohabitantDetail',
  //         type: 'primary',
  //         cache: true,
  //         btns: btnDisposeFn('asset:rentalHousing:cohabitantDetail', [
  //           'ADD_FACILITIES'
  //         ])
  //       }
  //     },
  //     {
  //       path: 'apartmentBoard',
  //       name: 'ApartmentBoard',
  //       component: resolve =>
  //         require([
  //           '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board'
  //         ], resolve),
  //       meta: {
  //         title: '公寓看板',
  //         icon: 'icon-menu-contract',
  //         role: 'asset:rentalHousing:apartmentBoard',
  //         type: 'primary',
  //         btns: btnDisposeFn('asset:rentalHousing:apartmentBoard', [
  //           'HEALTH',
  //           'MARK'
  //         ])
  //       }
  //     }
  //   ]
  // },
  // {
  //   path: '/reitsReport',
  //   component: resolve => require(['@/layout/default/index'], resolve),
  //   redirect: '/reitsReport/index',
  //   meta: {
  //     title: 'REITs管理',
  //     desc: '报告在线生成、查看',
  //     icon: 'statistics-line',
  //     role: 'asset:ritesReport'
  //   },
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'Report',
  //       component: resolve =>
  //         require(['@/views/manage/house/report/report-basic'], resolve),
  //       meta: {
  //         title: 'REITs管理',
  //         icon: 'statistics-line',
  //         role: 'asset:reitsReport:report',
  //         btns: btnDisposeFn('asset:reitsReport:report', ['ADD_REPORTS'])
  //       }
  //     },
  //     {
  //       path: 'details',
  //       name: 'ReportContainer',
  //       component: resolve =>
  //         require(['@/views/manage/house/report/report-basic/detail'], resolve),
  //       hidden: true,
  //       meta: {
  //         title: '报告详情',
  //         icon: 'view-list',
  //         role: 'asset:reitsReport:details',
  //         btns: btnDisposeFn('asset:reitsReport:details', ['EXPORT_PDF'])
  //       }
  //     }
  //   ]
  // }
]
export default assetOperationRoutes
