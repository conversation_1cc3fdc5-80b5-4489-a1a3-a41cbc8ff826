import Vue from 'vue'
import VueRouter from 'vue-router'

import Layout from '@/layout/default/index'

Vue.use(VueRouter)

/* eslint-disable */
export var constantRoutes = [
  {
    path: '/redirect',
    name: 'Redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        hidden: true,
        component: () => import('@/views/constant/redirect/index')
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    name: 'Layout',
    redirect: '/dashboard/index',
    hidden: true
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/constant/login'),
    hidden: true
  },
  {
    path: '/icons',
    name: 'Icons',
    component: () => import('@/views/constant/icons'),
    hidden: true
  },
  {
    path: '/housingImgViewer',
    name: 'HousingImgViewer',
    component: () => import('@/views/constant/housingImgViewer'),
    hidden: true
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/constant/404/index'),
    hidden: true
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/constant/403/index'),
    hidden: true
  }
]

const router = new VueRouter({
  mode: 'hash',
  routes: constantRoutes,
  scrollBehavior: () => ({ y: 0 })
})

// 解决ElementUI导航栏中的vue-router在3.0版本以上重复点菜单报错问题
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

export default router
