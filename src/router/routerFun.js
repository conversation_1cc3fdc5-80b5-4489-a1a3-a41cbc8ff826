/*
 * @Descripttion: 路由处理方法
 * @Author: 田柱
 * @Date: 2021-04-14 16:56:03
 * @LastEditTime: 2021-04-17 15:04:55
 */

/**
 * @name 生成path -> role 路由字典
 * @param routerMap {Array} 路由表
 * @return {Object} 返回路由权限映射关系
 */

import { deepClone } from '@/utils/tools'

let obj = Object.create(null)

function _gen(routes, type) {
  routes.forEach(item => {
    const children = item.children
    if (children && children.length > 0) {
      children.forEach(v => {
        v.path = `${item.path}/${v.path}`
        if (type === 'route') {
          obj[v.name] = v
        } else {
          obj[v.path] = v.meta.role
        }
      })
      _gen(children, type)
    }
  })
}

// 转换路由
// type: role   path => role
// type: route  name => route
function _genRouterMap(routerMap, type) {
  obj = Object.create(null)
  if (routerMap && routerMap.length > 0) {
    routerMap = deepClone(routerMap)
    _gen(routerMap, type || 'role')
  } else {
    return {}
  }

  return obj
}

export { _genRouterMap }
