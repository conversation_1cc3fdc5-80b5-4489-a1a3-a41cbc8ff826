/*
 * @Author: 田柱
 * @Date: 2022-04-18 11:37:24
 * @LastEditTime: 2022-04-18 11:39:00
 * @Description: 通用接口
 */

import request from '@/utils/request'

//  附件上传地址
export const uploadUrl = `${process.env.VUE_APP_URL_PREFIX}/main-attachment/upload`

// 分片附件上传地址
export const shardUploadUrl = `${process.env.VUE_APP_URL_PREFIX}/main-attachment/upload_sharding`

//断点续传，获取已上传的文件块接口
export const resumeUrl = `${process.env.VUE_APP_URL_PREFIX}/main-attachment/get_sharding_count`

// 附件删除
export function deleteAttach(id) {
  return request({ url: `/main-attachment/delete?id=${id}`, method: 'get' })
}

// 发送手机验证码
export function getSmsCaptcha(params) {
  return request({
    url: '/system/enterprise/user/sendRegisterCode',
    method: 'get',
    params
  })
}

// 校验手机验证码
export function validateSmsCaptcha(data) {
  return request({
    url: '/system/enterprise/user/resetPasswordCheck',
    method: 'post',
    data,
    hideLoading: true
  })
}

// 获取字典
export function getByDictType(code) {
  return request({
    url: `/system/dict-data/getByDictType?dictType=${code}`,
    method: 'get',
    hideLoading: true
  })
}

// 根据租户字典获取
export function getByTenantDictType(code) {
  return request({
    url: `/dict/tenantDictData/getByDictType?dictType=${code}`,
    method: 'get',
    hideLoading: true
  })
}

// 新增字典
export function addDict(data) {
  return request({
    url: `/dict/tenantDictData/create`,
    method: 'post',
    data
  })
}
// 查询字典
export function getDict(dictType) {
  return request({
    url: `/dict/tenantDictData/getPublicDict?dictType=${dictType}`,
    method: 'get'
  })
}

// 新增资讯
export function createNoticeType(data) {
  return request({
    url: `/hatch/notice_type/create`,
    method: 'post',
    data
  })
}

// 获取管理员
export function getUser() {
  return request({
    url: `/system/user/listAllSimple?userType=01`,
    method: 'get'
  })
}

// 获取角色
export function getRole() {
  return request({
    url: `/system/role/list-all-simple`,
    method: 'get'
  })
}

// 获取 中国 - 省
export function getProvice() {
  return request({
    url: `/regions/province/allList`,
    method: 'get',
    hideLoading: true
  })
}

// 获取 中国 - 省 - 市
export function getCity(provinceId) {
  return request({
    url: `/regions/city/allList?provinceId=${provinceId}`,
    method: 'get',
    hideLoading: true
  })
}

// 获取 中国 - 省 - 区
export function getCountry(cityId) {
  return request({
    url: `/regions/country/allList?cityId=${cityId}`,
    method: 'get',
    hideLoading: true
  })
}

// 识别营业执照
export function businessLicense(attachId) {
  return request({
    url: `/ocr/businessLicense?attachId=${attachId}`,
    method: 'get'
  })
}

// 附件下载
export function downloadAttachment(attachId) {
  return request({
    url: `/main-attachment/download/${attachId}`,
    method: 'get'
  })
}

// 离线任务分页列表
export function fileOfflinePage(params) {
  return request({
    url: `/file/offline/page`,
    method: 'get',
    params
  })
}
// 获取文件信息
export function attachmentDetail(params) {
  return request({
    url: `/main-attachment/get`,
    method: 'get',
    params
  })
}
// 页面埋点
export function extendBehaviorRecord(data) {
  return request({
    url: `/extend/behavior/record`,
    method: 'post',
    data
  })
}
// 获取字公共典
export function getDictTypeList(type) {
  return request({
    url: `/common/tenant-dict-type/get_list?type=${type}`,
    method: 'get'
  })
}
// 获取部门列表
export function deptList(params) {
  return request({
    url: '/system/dept/list_no_admin',
    method: 'get',
    params
  })
}
// 获取部门人员
export function getUserListByDept(params) {
  return request({
    url: `/system/user/getUserByDept`,
    method: 'get',
    params
  })
}
// 获取园区
export function getParkAll(params) {
  return request({
    url: `/housing/park/listAll`,
    method: 'get',
    params
  })
}

// 获取腾讯云初始化配置
export function getCosGenKey(params) {
  return request({
    url: `/cos/upload/gen_key`,
    method: 'get',
    params
  })
}
// 上传完成获取信息
export function cosUploadComplete(data) {
  return request({
    url: `/cos/upload/upload_complete`,
    method: 'post',
    data
  })
}

// 修改合同编号
export function modifyContractNo(data) {
  return request({
    url: `/contract/refactor/record/modify_contractNo`,
    method: 'post',
    data
  })
}

export function getContacts(params) {
  return request({
    url: `/enterprise/ent/info/get_contact_list?entId=${params}`,
    method: 'get',
  })
}
