<template>
  <basic-card>
    <div class="app-container">
      <el-form
        :model="queryParams"
        ref="queryForm"
        v-show="showSearch"
        :inline="true"
      >
        <el-form-item label="角色名称" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入角色名称"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="角色标识" prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入角色标识"
            clearable
            size="small"
            style="width: 240px"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="角色状态"
            clearable
            size="small"
            style="width: 240px"
          >
            <el-option
              v-for="dict in statusDictData"
              :key="parseInt(dict.value)"
              :label="dict.label"
              :value="parseInt(dict.value)"
            />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="创建时间">
        <el-date-picker
          v-model="dateRange"
          size="small"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item> -->
        <el-form-item>
          <el-button type="primary" size="small" @click="handleQuery">
            <svg-icon icon-class="search" />
            <span>搜索</span>
          </el-button>
          <el-button size="small" @click="resetQuery"
            ><svg-icon icon-class="refresh" /> <span>重置</span></el-button
          >
        </el-form-item>
      </el-form>

      <!-- <el-row :gutter="10" class="table-button">
        <el-col :span="1.5">
          <el-button
            type="primary"
            size="mini"
            @click="handleAdd"
            v-permission="['system:role:create']"
          >
            <svg-icon icon-class="add" />
            <span>新增</span>
          </el-button>
        </el-col>
        <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          :loading="exportLoading"
          v-permission="['system:role:export']"
          >导出</el-button
        >
      </el-col>
      </el-row> -->

      <div class="flex justify-content-end m-b-16">
        <el-button
          type="primary"
          size="small"
          @click="handleAdd"
          v-permission="routeButtonsPermission.ADD"
        >
          <svg-icon icon-class="add" />
          <span>{{ routeButtonsTitle.ADD }}</span>
        </el-button>
      </div>

      <el-table v-loading="loading" border :data="roleList">
        <el-table-column label="角色编号" prop="id" />
        <el-table-column
          label="角色名称"
          prop="name"
          :show-overflow-tooltip="true"
        />
        <el-table-column
          label="角色标识"
          prop="code"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="角色类型" prop="type">
          <template slot-scope="scope">
            <dict-tag :type="roleType" :value="scope.row.type" />
          </template>
        </el-table-column>
        <el-table-column label="显示顺序" prop="sort" />
        <el-table-column label="状态" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="0"
              :inactive-value="1"
              @change="handleStatusChange(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="scope">
            <el-link
              size="mini"
              type="primary"
              class="m-r-10"
              @click="handleUpdate(scope.row)"
              v-permission="routeButtonsPermission.REVISE"
              :disabled="scope.row.type === 1"
              >{{ routeButtonsTitle.REVISE }}</el-link
            >
            <el-link
              size="mini"
              type="success"
              class="m-r-10"
              @click="handleMenu(scope.row)"
              v-permission="routeButtonsPermission.MENU_PERMISSIONS"
              >{{ routeButtonsTitle.MENU_PERMISSIONS }}</el-link
            >
            <!--            <el-link-->
            <!--              size="mini"-->
            <!--              type="warning"-->
            <!--              class="m-r-10"-->
            <!--              @click="handleDataScope(scope.row)"-->
            <!--              v-permission="routeButtonsPermission.DATA_PERMISSIONS"-->
            <!--              >{{ routeButtonsTitle.DATA_PERMISSIONS }}</el-link-->
            <!--            >-->
            <el-link
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              v-permission="routeButtonsPermission.DELETE"
              >{{ routeButtonsTitle.DELETE }}</el-link
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-content-end">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNo"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </div>

      <!-- 添加或修改角色配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="500px"
        append-to-body
      >
        <el-form
          class="el-form-wrapper"
          ref="form"
          :model="form"
          :rules="rules"
        >
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入角色名称" />
          </el-form-item>
          <el-form-item label="角色标识" prop="code">
            <el-input v-model="form.code" placeholder="请输入角色标识" />
          </el-form-item>
          <el-form-item label="角色顺序" prop="sort">
            <el-input-number
              v-model="form.sort"
              controls-position="right"
              :min="0"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              placeholder="请输入内容"
              :maxlength="200"
              show-word-limit
              :rows="10"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 分配角色的数据权限对话框 -->
      <el-dialog
        title="分配数据权限"
        :visible.sync="openDataScope"
        width="500px"
        append-to-body
      >
        <el-form class="el-form-wrapper" :model="form" label-width="80px">
          <el-form-item label="角色名称">
            <el-input v-model="form.name" :disabled="true" />
          </el-form-item>
          <el-form-item label="角色标识">
            <el-input v-model="form.code" :disabled="true" />
          </el-form-item>
          <el-form-item label="权限范围">
            <el-select v-model="form.dataScope">
              <el-option
                v-for="item in dataScopeDictData"
                :key="parseInt(item.value)"
                :label="item.label"
                :value="parseInt(item.value)"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item
            label="数据权限"
            v-show="form.dataScope === SysDataScopeEnum.DEPT_CUSTOM"
          >
            <el-checkbox
              :checked="!form.deptCheckStrictly"
              @change="handleCheckedTreeConnect($event, 'dept')"
              >父子联动(选中父节点，自动选择子节点)</el-checkbox
            >
            <el-checkbox
              v-model="deptExpand"
              @change="handleCheckedTreeExpand($event, 'dept')"
              >展开/折叠</el-checkbox
            >
            <el-checkbox
              v-model="deptNodeAll"
              @change="handleCheckedTreeNodeAll($event, 'dept')"
              >全选/全不选</el-checkbox
            >
            <el-tree
              class="tree-border"
              :data="deptOptions"
              show-checkbox
              default-expand-all
              ref="dept"
              node-key="id"
              :check-strictly="form.deptCheckStrictly"
              empty-text="加载中，请稍后"
              :props="defaultProps"
            ></el-tree>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitDataScope">确 定</el-button>
          <el-button @click="cancelDataScope">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 分配角色的菜单权限对话框 -->
      <menu-role
        :visible.sync="openMenu"
        :form="form"
        @update="getList"
        :menus="menus"
      />
    </div>
  </basic-card>
</template>

<script>
import {
  addRole,
  changeRoleStatus,
  delRole,
  exportRole,
  getRole,
  listRole,
  updateRole
} from '../api/role'
import { assignRoleDataScope } from '../api/permission'
import { listSimpleDepts } from '../api/dept'
import { CommonStatusEnum, SystemDataScopeEnum } from '../utils/constants'
import Pagination from '../components/Pagination'
import { addDateRange, parseTime, resetForm, handleTree } from '../utils/tools'
import { getByDictType } from '@/api/common'
import downloads from '@/utils/download'
import DictTag from '../components/DictTag'
import MenuRole from '@/views/manage/system/role/MenuRole'
import { listMenus } from '@/views/manage/system/api/menu'
import dayjs from 'dayjs'
// import XEUtiles from 'xe-utils'
export default {
  name: 'WorkspaceRole',
  components: { MenuRole, Pagination, DictTag },
  data() {
    return {
      menus: {
        menu: [],
        all: []
      },
      list: [],
      current: 0,
      parseTime,
      resetForm,
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 角色表格数据
      roleList: [],
      allMenuOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 是否显示弹出层（数据权限）
      openDataScope: false,
      // 是否显示弹出层（菜单权限）
      openMenu: false,
      menuExpand: false,
      menuNodeAll: false,
      deptExpand: true,
      deptNodeAll: false,
      allIds: [],
      // 日期范围
      dateRange: [],
      // 菜单列表
      menuOptions: [],
      // 部门列表
      deptOptions: [], // 部门属性结构
      depts: [], // 部门列表
      rowChecked: true,
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        name: undefined,
        code: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      defaultProps: {
        label: 'name',
        children: 'children'
      },
      // 表单校验
      rules: {
        name: [
          { required: true, message: '角色名称不能为空', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '角色标识不能为空', trigger: 'blur' }
        ],
        sort: [{ required: true, message: '角色顺序不能为空', trigger: 'blur' }]
      },

      // 枚举
      SysCommonStatusEnum: CommonStatusEnum,
      SysDataScopeEnum: SystemDataScopeEnum,
      // 数据字典
      roleTypeDictData: this.getByDictType(
        'system_role_type',
        'roleTypeDictData'
      ),
      statusDictData: this.getByDictType('common_status', 'statusDictData'),
      dataScopeDictData: this.getByDictType(
        'system_data_scope',
        'dataScopeDictData'
      ),
      roleType: this.getByDictType('system_role_type', 'roleType')
    }
  },
  // created() {
  //   this.getList()
  // },
  methods: {
    // 获取性别字典
    getByDictType(type, key) {
      getByDictType(type).then(response => {
        this[key] = response
      })
    },
    /** 查询角色列表 */
    getList() {
      this.loading = true
      listRole(
        addDateRange(this.queryParams, [
          this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
          this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
        ])
      ).then(response => {
        this.roleList = response.list
        this.total = response.total
        this.loading = false
      })
    },
    // 角色状态修改
    handleStatusChange(row) {
      // 此时，row 已经变成目标状态了，所以可以直接提交请求和提示
      let text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
      this.$confirm(`确认要${text}"${row.name}"角色吗?`)
        .then(() => {
          return changeRoleStatus(row.id, row.status)
        })
        .then(() => {
          this.$toast.success(text + '成功')
        })
        .catch(() => {
          // 异常时，需要将 row.status 状态重置回之前的
          row.status =
            row.status === CommonStatusEnum.ENABLE
              ? CommonStatusEnum.DISABLE
              : CommonStatusEnum.ENABLE
        })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮（数据权限）
    cancelDataScope() {
      this.openDataScope = false
      this.reset()
    },
    // 表单重置
    reset() {
      if (this.$refs.menu !== undefined) {
        this.$refs.menu.setCheckedKeys([])
      }
      this.menuExpand = false
      this.menuNodeAll = false
      this.deptExpand = true
      this.deptNodeAll = false
      this.form = {
        id: undefined,
        name: undefined,
        code: undefined,
        sort: 0,
        deptIds: [],
        menuIds: [],
        dataScope: undefined,
        deptCheckStrictly: false,
        menuCheckStrictly: true,
        remark: undefined
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 树权限（展开/折叠）
    handleCheckedTreeExpand(value, type) {
      if (type === 'menu') {
        let treeList = this.menuOptions
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.menu.store.nodesMap[treeList[i].id].expanded = value
        }
      } else if (type === 'dept') {
        let treeList = this.deptOptions
        for (let i = 0; i < treeList.length; i++) {
          this.$refs.dept.store.nodesMap[treeList[i].id].expanded = value
        }
      }
    },
    // 树权限（全选/全不选）
    handleCheckedTreeNodeAll(value, type) {
      if (type === 'menu') {
        this.$refs.menu.setCheckedNodes(value ? this.menuOptions : [])
      } else if (type === 'dept') {
        // this.$refs.dept.setCheckedNodes(value ? this.deptOptions: []);
        this.$refs.dept.setCheckedNodes(value ? this.depts : [])
      }
    },
    // 树权限（父子联动）
    handleCheckedTreeConnect(value, type) {
      if (type === 'menu') {
        this.form.menuCheckStrictly = value
      } else if (type === 'dept') {
        this.form.deptCheckStrictly = !value
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加角色'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id
      getRole(id).then(response => {
        this.form = response
        this.open = true
        this.title = '修改角色'
      })
    },
    /** 分配菜单权限操作 */
    handleMenu(row) {
      this.reset()
      const id = row.id
      this.title = '分配菜单权限'
      // 处理了 form 的角色 name 和 code 的展示
      this.form.id = id
      this.form.name = row.name
      this.form.code = row.code
      this.openMenu = true
    },
    /** 分配数据权限操作 */
    handleDataScope(row) {
      this.reset()
      // 处理了 form 的角色 name 和 code 的展示
      this.form.id = row.id
      this.form.name = row.name
      this.form.code = row.code
      // 打开弹窗
      this.openDataScope = true
      // 获得部门列表
      listSimpleDepts().then(response => {
        // 处理 deptOptions 参数
        this.deptOptions = []
        this.deptOptions.push(...handleTree(response, 'id'))
        this.depts = response
        // this.deptIds = response.data.map(x => x.id);
        // 获得角色拥有的数据权限
        getRole(row.id).then(response => {
          this.form.dataScope = response.dataScope
          this.$refs.dept.setCheckedKeys(response.dataScopeDeptIds || [], false)
        })
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateRole(this.form).then(() => {
              this.$toast.success('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addRole(this.form).then(() => {
              this.$toast.success('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 提交按钮（数据权限） */
    submitDataScope() {
      if (this.form.id !== undefined) {
        assignRoleDataScope({
          roleId: this.form.id,
          dataScope: this.form.dataScope,
          dataScopeDeptIds:
            this.form.dataScope !== SystemDataScopeEnum.DEPT_CUSTOM
              ? []
              : this.$refs.dept.getCheckedKeys()
        }).then(() => {
          this.$toast.success('修改成功')
          this.openDataScope = false
          this.getList()
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除角色编号为"' + ids + '"的数据项?')
        .then(() => {
          return delRole(ids)
        })
        .then(() => {
          this.getList()
          this.$toast.success('删除成功')
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm('是否确认导出所有角色数据项?')
        .then(() => {
          this.exportLoading = true
          return exportRole(queryParams)
        })
        .then(response => {
          downloads.excel(response, dayjs().format('YYYY-MM-DD') + '角色数据.xls')
          this.exportLoading = false
        })
    },
    getMenus() {
      listMenus().then(res => {
        this.menus = res || {
          menu: [],
          all: []
        }
      })
    }
  },
  activated() {
    this.getList()
    this.getMenus()
  }
}
</script>

<style lang="scss" scoped>
.el-form-wrapper {
  width: 100%;
}
:deep(.tabs-content) {
  flex-wrap: wrap;
}
:deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.el-select {
  width: 100%;
}
::v-deep .el-dialog {
  .el-dialog__body {
    max-height: 88vh;
    overflow-y: auto;
    padding: 20px;
  }
  .el-dialog__footer {
    padding: 10px 26px 20px;
  }
}
</style>
