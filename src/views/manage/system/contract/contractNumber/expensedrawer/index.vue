<template>
  <div>
    <driven-form
      ref="drive-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
    <div class="gz flex justify-content-between m-t-24 m-b-16">
      <div>费用规则</div>
      <div class="m-r-20">
        <el-button class="el-dropdown-link" @click="visible = true"
          ><svg-icon icon-class="add" />添 加</el-button
        >
      </div>
    </div>
    <div class="bg-base p-24">
      <div class="ipt p-l-32 p-b-12">
        <div class="m-b-16 font-size-14">房租费</div>
        <el-form ref="number" inline>
          <template v-for="(item, index) in dataMergeList">
            <el-form-item v-if="index !== 0" :key="'merge' + index">
              <el-input
                style="width: 40px"
                placeholder="+"
                :disabled="true"
              ></el-input>
            </el-form-item>
            <el-form-item :key="'merge' + 'son' + index">
              <el-input
                @change="getContractPreview"
                v-model="item.feeName"
                style="width: 100px"
                disabled
              ></el-input>
              <div class="number">{{ getFeeUnit(item.feeUnit) }}</div>
            </el-form-item>
          </template>

          <br />
          <template v-for="(item, index) in dataSingleList">
            <el-form-item :key="'single' + index">
              <div class="m-b-8">{{ item.feeName }}</div>
              <el-input
                @change="getContractPreview"
                v-model="item.feeName"
                style="width: 100px"
                disabled
              ></el-input>
              <div class="number">{{ getFeeUnit(item.feeUnit) }}</div>
            </el-form-item>
            <br :key="'single' + 'son' + index" />
          </template>
        </el-form>
      </div>
      <div class="preview bg-white p-15">
        合同总价预览
        <span class="m-l-10">{{ costRulePreview }}</span>
      </div>
    </div>
    <div>
      <dialog-cmp
        title="添加费用类型"
        :visible.sync="visible"
        @confirmDialog="confirmDialog"
      >
        <driven-form
          v-if="visible"
          ref="drive-form-cost"
          v-model="fromModelCost"
          :formConfigure="formConfigureCost"
        />
      </dialog-cmp>
    </div>
  </div>
</template>

<script>
import descriptor from './descriptor/descriptor'
import { getPark } from '@/views/manage/house/houseList/houseList-basic/api'
import { getTenantDictData } from '@/views/manage/house/contract/contract-basic/api/create'
import { getFeeUnit } from '../utils/status'
import { createFeeRules } from '@/views/manage/system/contract/contractNumber/expensedrawer/api'
import { deepClone } from '@/utils/tools'

export default {
  name: 'ExpenseDrawer',
  mixins: [descriptor],
  data() {
    return {
      getFeeUnit,
      visible: false,
      fromModelCost: {},
      fromModel: {},
      dataMergeList: [
        {
          feeName: '房租总价',
          isMerge: 1,
          feeUnit: 1
        }
      ],
      dataSingleList: [
        {
          feeName: '保证金',
          isMerge: 0,
          feeUnit: 1
        }
      ],
      costRulePreview: ''
    }
  },
  created() {
    this.getPark()
    this.getTenantDictData()
    this.getContractPreview()
  },
  methods: {
    initData(val) {
      let data = deepClone(val)
      data.contractTempleteId = data.contractTempleteId.split(',')
      data.contractType = data.contractType.split(',')
      let { parkId, contractTempleteId, contractType } = data
      contractTempleteId = contractTempleteId.map(item => {
        return Number(item)
      })
      contractType = contractType.map(item => {
        return Number(item)
      })
      this.fromModel = { parkId, contractTempleteId, contractType }

      let dataMergeList = []
      let dataSingleList = []
      for (const item of data.feeTypeRespVOS) {
        if (item.isMerge) {
          dataMergeList.push(item)
        } else {
          dataSingleList.push(item)
        }
      }
      this.dataMergeList = dataMergeList
      this.dataSingleList = dataSingleList
    },
    // 获取所有园区
    getPark() {
      getPark().then(res => {
        this.formConfigure.descriptors.parkId.options = this.packList = res.map(
          item => {
            return { label: item.park, value: item.id }
          }
        )
      })
    },
    // 获取合同模板
    getTenantDictData() {
      getTenantDictData('contract_templete').then(res => {
        this.formConfigure.descriptors.contractTempleteId.options = res.map(
          item => {
            return { label: item.label, value: item.value }
          }
        )
      })
    },
    getContractPreview() {
      let temp = '房租费'
      if (this.dataMergeList.length > 0) {
        temp = temp + '（'
        for (const i in this.dataMergeList) {
          if (i > 0) {
            temp = temp + '、'
          }
          temp = temp + this.dataMergeList[i].feeName
        }
        temp = temp + '）'
      }

      for (const item of this.dataSingleList) {
        temp = temp + ' + ' + item.feeName
      }
      this.costRulePreview = temp
    },
    confirmDialog() {
      this.$refs['drive-form-cost'].validate(valid => {
        if (valid) {
          const { isMerge } = this.fromModelCost
          if (isMerge) {
            this.dataMergeList.push({
              ...this.fromModelCost
            })
          } else {
            this.dataSingleList.push({
              ...this.fromModelCost
            })
          }
          this.fromModelCost = {}
          this.visible = false
          this.getContractPreview()
        }
      })
    },
    saveData(callback) {
      this.$refs['drive-form'].validate(valid => {
        if (valid) {
          let params = deepClone(this.fromModel)
          params.contractTempleteId = [222].join(',')
          params.contractType = params.contractType.join(',')
          params.feeTypeAddVOList = []
          for (const item of this.dataMergeList) {
            params.feeTypeAddVOList.push(item)
          }
          for (const item of this.dataSingleList) {
            params.feeTypeAddVOList.push(item)
          }
          createFeeRules(params).then(() => {
            this.$toast.success('保存成功')
            setTimeout(() => {
              if (callback) {
                callback()
              }
            }, 3000)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.number {
  width: 100px;
  text-align: center;
}
.gz {
  height: 34px;
  line-height: 34px;
}
.ipt {
  border-bottom: 1px solid #dcdcdc;
}
.preview {
  height: 60px;
  line-height: 30px;
  border: 1px solid #000;
}
.jh {
  width: 30px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 1px solid #bdbdbd;
}
</style>
