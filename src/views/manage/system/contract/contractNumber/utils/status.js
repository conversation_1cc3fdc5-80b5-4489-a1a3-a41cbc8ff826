export function getType(val) {
  switch (val) {
    case 0:
      return 'Blank@'
    case 1:
      return 'Fixed@'
    case 2:
      return 'Park@'
    case 3:
      return 'Symbol@'
    case 4:
      return 'Date@'
    case 5:
      return 'Random@'
    default:
      return '-'
  }
}

export function getContractType(h, val) {
  switch (val) {
    case 1:
      return '新签租房'
    case 2:
      return '续签租房'
    case 3:
      return '售房合同'
    case 4:
      return '不限'
    default:
      return '-'
  }
}

export function getFeeUnit(val) {
  switch (val) {
    case 1:
      return '元'
    case 5:
      return '元/月'
    case 10:
      return '元/月/平米'
    default:
      return '-'
  }
}
