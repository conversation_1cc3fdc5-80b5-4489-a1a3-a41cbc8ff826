export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          // park: {
          //   form: 'select',
          //   label: '选择园区',
          //   hidden: true,
          //   options: [],
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请选择园区'
          //     }
          //   ],
          //   events: {
          //     change: this.getContractType
          //   }
          // },
          // contractType: {
          //   form: 'select',
          //   label: '合同类型',
          //   hidden: true,
          //   options: [
          //     { label: '不限', value: 0 },
          //     { label: '新签租房', value: 1, disabled: false },
          //     { label: '续签租房', value: 2, disabled: false }
          //   ],
          //   rule: [
          //     {
          //       required: true,
          //       type: 'array',
          //       message: '请选择合同类型'
          //     }
          //   ],
          //   props: {
          //     multiple: true
          //   }
          // },
          contractStatus: {
            form: 'select',
            label: '合同模板',
            options: [],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择合同模板'
              }
            ]
          }
        }
      },
      formConfigureRule: {
        descriptors: {
          type: {
            form: 'select',
            label: '组件类型',
            disabled: false,
            options: [
              // {
              //   label: '固定标识',
              //   value: 'Fixed@'
              // },
              // {
              //   label: '园区缩写',
              //   value: 'Park@'
              // },
              // {
              //   label: '分隔符',
              //   value: 'Symbol@'
              // },
              // {
              //   label: '年月日',
              //   value: 'Date@'
              // },
              // {
              //   label: '输入框',
              //   value: 'Blank@'
              // },
              // {
              //   label: '随机数',
              //   value: 'Random@'
              // },
              // {
              //   label: '项目期数',
              //   value: 'PjNumber@'
              // }
            ],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择组件类型'
              }
            ]
          },
          content: {
            form: 'input',
            label: '组件内容',
            disabled: false,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入固定标识'
              }
            ]
          },
          contentSelect: {
            form: 'select',
            label: '组件内容',
            options: [
              {
                label: '-',
                value: '-'
              },
              {
                label: '/',
                value: '/'
              },
              {
                label: '*',
                value: '*'
              },
              {
                label: ':',
                value: ':'
              }
            ],
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入组件内容'
              }
            ]
          },
          control: {
            form: 'input',
            label: '组件控制',
            disabled: false,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入'
              }
            ]
          },
          controlSelect: {
            form: 'select',
            label: '组件控制',
            options: [
              {
                label: 'yyyyMMdd',
                value: 'yyyyMMdd'
              },
              {
                label: 'ddMMyyyy',
                value: 'ddMMyyyy'
              },
              {
                label: 'yyyyMM',
                value: 'yyyyMM'
              },
              {
                label: 'yyyy',
                value: 'yyyy'
              }
            ],
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择'
              }
            ]
          }
        }
      }
    }
  }
}
