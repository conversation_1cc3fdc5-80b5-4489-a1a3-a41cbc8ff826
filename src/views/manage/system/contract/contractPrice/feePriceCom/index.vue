<template>
  <div class="fee-price-container">
    <driven-form
      v-if="list && list.length"
      :key="drivenFormKey"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
      labelPosition="left"
    />
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import { deepClone } from '@/utils/tools'

export default {
  name: 'ContractFeePriceCom',
  props: {
    value: {
      required: true
    },
    comList: {
      type: Array,
      default: () => []
    }
  },
  mixins: [descriptorMixins],
  data() {
    return {
      fromModel: {},
      drivenFormKey: Math.random(),
      list: []
    }
  },
  watch: {
    comList: {
      handler(val) {
        this.list = val
        this.formatterHandle()
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // v-model所需格式
    modelFormatter(val) {
      const obj = {}
      for (const key in val) {
        if (Array.isArray(val[key])) {
          obj[key] = !!val[key].length
          obj[`${key}Switch`] = val[`${key}Switch`]
        }
      }
      const arr = []
      const copyObj = deepClone(obj)
      for (const objKey in obj) {
        for (const copyKey in copyObj) {
          if (copyKey.includes(objKey) && copyKey !== objKey) {
            arr.push({
              code: objKey,
              contain: !!val[objKey].length,
              free: copyObj[copyKey]
            })
          }
        }
      }
      this.$emit('input', arr)
    },
    // 表单回显格式
    formatterHandle() {
      if (this.value.length) {
        this.value.forEach(item => {
          if (!(item.code in this.fromModel)) {
            this.$set(
              this.fromModel,
              item.code,
              item.contain ? [item.code] : []
            )
          }
          if (!(`${item.value}Switch` in this.fromModel)) {
            this.$set(this.fromModel, `${item.code}Switch`, item.free)
          }
        })
      }
      this.formConfigure.descriptors = {}
      this.list.forEach(item => {
        const descriptor = {
          label: '',
          form: 'checkbox',
          span: 12,
          rule: [
            {
              required: false,
              type: 'array',
              message: `请选择${item.label}`
            }
          ],
          options: [item],
          events: {
            change: () => {
              this.formatterHandle()
            }
          }
        }
        const descriptorSwitch = {
          label: '免租期是否包含',
          form: 'switch',
          span: 12,
          rule: [{ type: 'boolean' }],
          disabled: item.freeDisable
            ? item.freeDisable
            : this.fromModel[item.value]
            ? !this.fromModel[item.value].length
            : !this.fromModel[item.value],
          events: {
            change: () => {
              this.modelFormatter(this.fromModel)
            }
          }
        }
        if (!(item.value in this.fromModel)) {
          this.$set(this.fromModel, item.value, [])
        }
        if (!(`${item.value}Switch` in this.fromModel)) {
          this.$set(this.fromModel, `${item.value}Switch`, false)
        }
        this.formConfigure.descriptors[item.value] = descriptor
        this.formConfigure.descriptors[`${item.value}Switch`] = descriptorSwitch
      })
      this.drivenFormKey = Math.random()
      this.modelFormatter(this.fromModel)
    }
  }
}
</script>

<style scoped lang="scss">
.fee-price-container {
  :deep(.el-form) {
    .custom-tips {
      display: none;
    }
    &.el-form--label-left .el-form-item__label {
      float: left !important;
    }
    .el-row .el-col:nth-child(odd) {
      .el-form-item__label {
        width: 0 !important;
      }
      .el-form-item__content {
        margin-left: 0 !important;
      }
    }
  }
}
</style>
