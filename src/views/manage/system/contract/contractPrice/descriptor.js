import FeePriceCom from './feePriceCom'

export default {
  components: { FeePriceCom },
  data() {
    return {
      formConfigure: {
        labelWidth: '120px',
        descriptors: {
          templateIds: {
            form: 'select',
            label: '选择合同模板',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择合同模板，支持选择多个'
              }
            ],
            options: [],
            attrs: {
              multiple: true
            }
          },
          list: {
            form: 'component',
            label: '包含费用',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请选择包含费用'
              }
            ],
            render: () => {
              return (
                <fee-price-com
                  ref="feePriceCom"
                  v-model={this.fromModel.list}
                  comList={this.comList}
                />
              )
            }
          }
        }
      }
    }
  }
}
