<template>
  <div class="min-h100">
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :tableData="tableData"
      row-key="id"
      :tree-props="{
        children: 'children',
        hasChildren: 'hasChildren'
      }"
    />
  </div>
</template>

<script>
import ColumnMixins from './column'
import { getFeeList } from '@/views/manage/system/api/contract'

export default {
  name: 'ExpenseLibrary',
  mixins: [ColumnMixins],
  data() {
    return {
      tableData: []
    }
  },
  created() {
    this.getFeeList()
  },
  activated() {
    this.getFeeList()
  },
  methods: {
    getFeeList() {
      getFeeList().then(res => {
        this.tableData = res || []
      })
    }
  }
}
</script>

<style scoped></style>
