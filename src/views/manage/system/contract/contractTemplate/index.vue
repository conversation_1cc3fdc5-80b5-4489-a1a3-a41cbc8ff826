<template>
  <div class="min-h100">
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :tableData="tableData"
    >
      <template v-slot:operate-right>
        <el-button
          v-permission="routeButtonsPermission.ADD"
          type="primary"
          size="small"
          @click="drawerVisible = true"
        >
          <svg-icon icon-class="add" />
          {{ routeButtonsTitle.ADD }}
        </el-button>
      </template>
    </drive-table>
    <basic-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
  </div>
</template>

<script>
import ColumnMixins from './column'
import DescriptorMixins from './descriptor'
import {
  contractTemplateCreate,
  contractTemplateDelete,
  contractTemplateDetail,
  contractTemplateUpdate,
  getContractTemplateList,
  getContractType,
  getSelectBsType,
  getPark
} from '@/views/manage/system/api/contract'

export default {
  name: 'ContractTemplate',
  data() {
    return {
      drawerTitle: '新增',
      drawerVisible: false,
      fromModel: {},
      tableData: []
    }
  },
  mixins: [ColumnMixins, DescriptorMixins],
  watch: {
    drawerVisible(val) {
      if (!val) {
        this.drawerTitle = this.$options.data().drawerTitle
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  created() {
    this.getSelectBsType()
    this.getPark()
    this.getContractType()
    this.getContractTemplateList()
  },
  activated() {
    this.getContractTemplateList()
  },
  methods: {
    getSelectBsType() {
      getSelectBsType().then(res => {
        this.formConfigure.descriptors.businessType.options = res.map(item => {
          return {
            value: item.key,
            label: item.value
          }
        })
      })
    },
    // 文件模板查看
    attachViewHandle(row) {
      window.open(row.attachMap.contractTemplate[0].path, '_blank')
    },
    // 获取列表
    getContractTemplateList() {
      getContractTemplateList().then(res => {
        this.tableData = res || []
      })
    },
    // 获取合同类型
    getContractType() {
      getContractType().then(res => {
        const list = res || []
        this.formConfigure.descriptors.contractType.options = list.map(item => {
          return {
            value: item.key,
            label: item.label
          }
        })
      })
    },
    getPark() {
      getPark().then(res => {
        const list = res || []
        this.formConfigure.descriptors.parkId.options = list.map(item => {
          return {
            value: item.key,
            label: item.label
          }
        })
      })
    },
    // 新增编辑提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(async valid => {
        if (!valid) return false
        const { id } = this.fromModel
        const params = {
          ...this.fromModel,
          attachIds: this.fromModel.attachIds.map(item => item.id)
        }
        if (id) {
          contractTemplateUpdate(params).then(() => {
            this.operateTips('编辑')
          })
        } else {
          contractTemplateCreate(params).then(() => {
            this.operateTips('新增')
          })
        }
      })
    },
    operateTips(tips) {
      this.$message.success(`${tips}成功`)
      this.getContractTemplateList()
      this.drawerVisible = false
    },
    // 编辑
    async editHandle(row) {
      try {
        this.drawerTitle = '编辑'
        const detail = await contractTemplateDetail(row.id)
        this.fromModel = {
          ...detail,
          attachIds:
            JSON.stringify(detail.attachMap) === '{}'
              ? []
              : detail.attachMap.contractTemplate
        }
        this.drawerVisible = true
      } catch (e) {
        console.log(e)
      }
    },
    // 删除
    deleteHandle(row) {
      this.$confirm('确定删除此合同模板', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        contractTemplateDelete(row.id).then(() => {
          this.operateTips('删除')
        })
      })
    }
  }
}
</script>

<style scoped></style>
