import { parseTime } from '@/utils/tools'
import { noData } from '@/filter'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '计费规则',
          prop: 'name'
        },
        {
          label: '水费单价',
          prop: 'wtPrice'
        },
        {
          label: '电费电价',
          prop: 'elPrice'
        },
        {
          label: '操作',
          prop: 'operation',
          width: 150,
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="text"
                  onClick={() => {
                    this.editHandle(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.EDIT}
                >
                  {this.routeButtonsTitle.EDIT}
                </el-button>
                <el-button
                  type="text"
                  onClick={() => {
                    this.deleteHandle(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DELETE}
                >
                  {this.routeButtonsTitle.DELETE}
                </el-button>
                <el-button
                  type="text"
                  onClick={() => {
                    this.recordHandle(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.RECORD}
                >
                  {this.routeButtonsTitle.RECORD}
                </el-button>
              </div>
            )
          }
        }
      ],
      recordTableColumn: [
        {
          label: '水费单价',
          prop: 'wtPrice'
        },
        {
          label: '电费电价',
          prop: 'elPrice'
        },
        {
          label: '附件',
          prop: 'attachMap',
          render: (h, scope) => {
            const { infoAttach = [] } = scope.row.attachMap
            return (
              <div>
                {infoAttach.length ? (
                  infoAttach.map(item =>
                    h(
                      'el-link',
                      {
                        class: 'color-primary',
                        style: {
                          display: 'block'
                        },
                        on: {
                          click: () => {
                            window.open(item.path, '_blank')
                          }
                        }
                      },
                      item.name
                    )
                  )
                ) : (
                  <span>{noData('')}</span>
                )}
              </div>
            )
          }
        },
        {
          label: '更新时间',
          prop: 'updateTime',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                {parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}')}
              </div>
            )
          }
        },
        {
          label: '操作人',
          prop: 'userName',
          align: 'center'
        }
      ]
    }
  }
}
