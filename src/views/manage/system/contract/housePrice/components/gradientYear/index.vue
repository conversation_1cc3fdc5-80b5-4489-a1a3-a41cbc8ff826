<template>
  <div class="pos-relative">
    <div class="add"><el-link type="primary" @click="add">新增</el-link></div>
    <el-form ref="driven-form" :model="fromModel">
      <template v-for="(row, inx) in fromModel.gradientList">
        <el-form-item
          :prop="'gradientList.' + inx + '.value'"
          :key="inx"
          v-if="inx < 10"
          :rules="[
            {
              required: true,
              message: '请输入收费标准',
              trigger: ['blur', 'change']
            },
            { validator: validatorFn, trigger: ['blur', 'change'] }
          ]"
        >
          <div class="flex align-items-center">
            <el-input placeholder="请输入" v-model="row.value">
              <template slot="prepend">
                <span class="color-info">第{{ inx + 1 }}年</span>
              </template>
              <template slot="append">
                <span class="color-info">{{ unit }}</span>
              </template>
            </el-input>
            <div
              class="m-l-16 pointer"
              v-if="inx > 0"
              @click="deleteHandle(inx)"
            >
              <svg-icon icon-class="delete" />
            </div>
          </div>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
const validatorFn = (rule, value, callback) => {
  const reg = /^([1-9]\d{0,7}|0)(\.\d{1,2})?$/
  if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的收费标准'))
  }
}
export default {
  name: 'GradientYear',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    unit: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      validatorFn,
      fromModel: {
        gradientList: [
          {
            value: ''
          }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val.length > 0) {
          this.fromModel.gradientList = val
        } else {
          this.fromModel.gradientList = [
            {
              value: ''
            }
          ]
        }
      },
      immediate: true,
      deep: true
    },
    fromModel: {
      handler(val) {
        this.$emit('input', val.gradientList)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    add() {
      this.fromModel.gradientList.push({
        value: ''
      })
    },
    deleteHandle(index) {
      this.fromModel.gradientList.splice(index, 1)
    }
  }
}
</script>

<style scoped lang="scss">
.add {
  position: absolute;
  right: 0;
  top: -40px;
}
</style>
