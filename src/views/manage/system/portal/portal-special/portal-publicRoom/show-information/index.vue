<template>
  <div class="show-information-container p-16 h100">
    <!--   基本信息-->
    <div>
      <div class="item-title font-strong">基本信息</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        设置相关主要内容，便于浏览者快速了解，
        <el-button @click="picExample(1)" type="text" class="font-size-12">
          查看图例
        </el-button>
      </div>
      <div class="m-t-16 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.basicInfoFlag"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <!--      描述表格-->
      <div class="m-t-16 p-l-8">
        <table style="width: 100%" border="1" Cellspacing="0" Cellpadding="0">
          <tr>
            <th>办理主体</th>
            <td>
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.handlingEntity"
                  maxlength="28"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('handlingEntity')"
                  @click="showEye('handlingEntity')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('handlingEntity')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
            <th>办理类型</th>
            <td>
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.handlingType"
                  maxlength="20"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('handlingType')"
                  @click="showEye('handlingType')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('handlingType')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
          </tr>
          <tr>
            <th>服务对象</th>
            <td>
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.serviceObject"
                  maxlength="28"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('serviceObject')"
                  @click="showEye('serviceObject')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('serviceObject')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
            <th>材料清单</th>
            <td>
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-button @click="basicVisible = true" type="text"
                  >查看</el-button
                >
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('attach')"
                  @click="showEye('attach')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('attach')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
          </tr>
          <tr>
            <th>办理地点</th>
            <td :colspan="3">
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.handlingAddress"
                  maxlength="50"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('handlingAddress')"
                  @click="showEye('handlingAddress')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('handlingAddress')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
          </tr>
          <tr>
            <th>办理时间</th>
            <td :colspan="3">
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.handlingTime"
                  maxlength="50"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('handlingTime')"
                  @click="showEye('handlingTime')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('handlingTime')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
          </tr>
          <tr>
            <th>咨询方式</th>
            <td :colspan="3">
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.informationMethods"
                  maxlength="30"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('informationMethods')"
                  @click="showEye('informationMethods')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('informationMethods')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
          </tr>
          <tr>
            <th>办理形式</th>
            <td :colspan="3">
              <div
                class="table-item flex justify-content-between align-items-center"
              >
                <el-input
                  v-model="basicObg.handlingForm"
                  maxlength="50"
                  placeholder="点击添加..."
                ></el-input>
                <svg-icon
                  class="icon-item pointer"
                  v-if="displayFields.includes('handlingForm')"
                  @click="showEye('handlingForm')"
                  icon-class="browse"
                />
                <svg-icon
                  class="icon-item pointer"
                  @click="displayFields.push('handlingForm')"
                  v-else
                  icon-class="browse-off"
                />
              </div>
            </td>
          </tr>
        </table>
      </div>
    </div>
    <!--   资格条件-->
    <div class="m-t-24">
      <div class="item-title font-strong">资格条件</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        详细描述可申请公租房的资格条件，
        <el-button @click="picExample(2)" type="text" class="font-size-12">
          查看图例
        </el-button>
      </div>
      <div class="m-t-16 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.prerequisitesFlag"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <!--      内容分组-->
      <div class="flex justify-content-between align-items-center m-t-10 p-l-8">
        <div class="font-size-14 line-height-32">内容分组</div>
        <div class="flex align-items-center">
          <el-button
            :disabled="!disabled"
            type="text"
            @click="isSortable = !isSortable"
          >
            {{ isSortable ? '退出排序' : '排序管理' }}
          </el-button>
          <el-button type="text" @click="openDialog(1)"
            ><svg-icon icon-class="add" /> 添加分组</el-button
          >
        </div>
      </div>
      <div
        ref="packetContent"
        v-if="prerequisitesJson && prerequisitesJson.length > 0"
        class="p-l-8"
      >
        <div v-for="(item, index) in prerequisitesJson" :key="index">
          <div class="table-title m-t-16">
            <div>
              {{ item.groupName }}
            </div>
            <div>
              <el-button
                type="text"
                class="color-danger"
                @click="moveOut(1, index)"
                >移出</el-button
              >
              <el-button type="text" @click="openDialog(2, index)"
                >新增</el-button
              >
            </div>
          </div>
          <!--         <div class="pos-relative m-t-16" style="min-height: 60px;">-->
          <el-table
            :data="item.itemList"
            :show-header="false"
            stripe
            border
            maxHeight="360px"
            class="m-t-16"
            style="width: 100%; border-radius: 3px"
          >
            <!-- 空数据状态 -->
            <template slot="empty">
              <div class="empty-content" style="height: 360px">
                <empty-data />
              </div>
            </template>
            <el-table-column prop="description" label=""> </el-table-column>
            <el-table-column prop="operate" label="操作" width="80">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  class="color-danger"
                  @click="moveOut(item.itemList, scope)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <!--         </div>-->
        </div>
      </div>
      <div v-else>
        <empty-data />
      </div>
    </div>
    <!--   申请材料-->
    <div class="m-t-24">
      <div class="item-title font-strong">申请材料</div>
      <div class="p-l-12 font-size-12 color-info">
        详细描述申请公租房需要的相关材料，
        <el-button @click="picExample(3)" type="text" class="font-size-12">
          查看图例
        </el-button>
      </div>
      <div class="flex justify-content-between align-items-center m-t-10 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.applicationMaterialsFlag"
          inactive-text="显示"
        >
        </el-switch>
        <div class="flex align-items-center">
          <el-button
            :disabled="!disabled1"
            type="text"
            @click="sortableHandle(!isSortable1)"
          >
            {{ isSortable1 ? '退出排序' : '排序管理' }}
          </el-button>
          <el-button type="text" @click="addSheet"
            ><svg-icon icon-class="add" /> 添加材料</el-button
          >
        </div>
      </div>
      <!--         <div class="pos-relative m-t-16" style="min-height: 60px;">-->
      <drive-table
        ref="drive-table-Materials"
        class="m-t-16 p-l-8"
        maxHeight="360px"
        :is-sortable="isSortable1"
        :table-data="tableList"
        :columns="tableColumnMaterials"
        :sort-fn="sortMethods"
      >
      </drive-table>
      <!--         </div>-->
    </div>
    <!--   办理地点-->
    <div class="m-t-24">
      <div class="item-title font-strong">办理地点</div>
      <div class="p-l-12 font-size-12 color-info">
        线下办理公租房业务的具体地理位置，
        <el-button @click="picExample(4)" type="text" class="font-size-12">
          查看图例
        </el-button>
      </div>
      <div class="flex justify-content-between align-items-center m-t-10 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.processingLocationFlag"
          inactive-text="显示"
        >
        </el-switch>
        <div class="flex align-items-center">
          <el-button
            :disabled="!disabled2"
            type="text"
            @click="isSortable2 = !isSortable2"
          >
            {{ isSortable2 ? '退出排序' : '排序管理' }}
          </el-button>
          <el-button type="text" @click="openAddress"
            ><svg-icon icon-class="add" /> 添加地点</el-button
          >
        </div>
      </div>
      <!--      办理地点内容-->
      <div
        ref="address"
        v-if="processingLocationJson && processingLocationJson.length > 0"
        class="m-t-10 p-l-8"
      >
        <div
          class="address-list"
          v-for="(item, index) in processingLocationJson"
          :key="index"
        >
          <div
            class="address-title pointer"
            @click="showHandler(`addressContent${index}`)"
          >
            <div>{{ item.title }}</div>
            <div class="flex align-items-center">
              <el-button
                type="text"
                class="color-danger"
                @click.stop="delAddress(index)"
                >删除</el-button
              >
              <el-button type="text" @click.stop="editAddress(item, index)"
                >编辑</el-button
              >
            </div>
          </div>
          <div :id="`addressContent${index}`" class="address-content">
            <div class="address-item">
              <div class="flex align-items-center font-size-14">
                <div style="width: 100px">地址</div>
                <div class="color-s">{{ item.address }}</div>
              </div>
              <div class="flex align-items-center font-size-14 m-t-16">
                <div style="width: 100px">咨询电话</div>
                <div class="color-s">{{ item.contact }}</div>
              </div>
              <div class="flex align-items-center font-size-14 m-t-16">
                <div style="width: 100px">工作时间</div>
                <div class="color-s">{{ item.workTime }}</div>
              </div>
              <div class="flex align-items-center font-size-14 m-t-16">
                <div style="width: 100px">地图位置</div>
                <div class="color-s">{{ item.location }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <empty-data />
      </div>
    </div>
    <!--   办理流程-->
    <div class="m-t-24">
      <div class="item-title font-strong">办理流程</div>
      <div class="p-l-12 font-size-12 color-info">
        具体描述公租房申请的相关办事流程，
        <el-button @click="picExample(5)" type="text" class="font-size-12">
          查看图例
        </el-button>
      </div>
      <div class="m-t-10 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.handlingProcessFlag"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <div class="p-l-8">
        <driven-form
          class="m-t-10"
          ref="driven-form"
          label-position="top"
          v-model="fromModel"
          :formConfigure="formConfigure"
          :key="handlingProcedures"
        />
      </div>
    </div>
    <!--   项目展示-->
    <div class="m-t-24">
      <div class="item-title font-strong">项目展示</div>
      <div class="p-l-12 font-size-12 color-info">
        展示相关公租房项目的风采图册，
        <el-button @click="picExample(6)" type="text" class="font-size-12">
          查看图例
        </el-button>
      </div>
      <div class="m-t-10 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.projectPresentationFlag"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <!--      内容分组-->
      <div class="flex justify-content-between align-items-center m-t-10 p-l-8">
        <div class="font-size-14 line-height-32">内容分组</div>
        <div class="flex align-items-center">
          <el-button
            :disabled="!disabled3"
            type="text"
            @click="isSortable3 = !isSortable3"
          >
            {{ isSortable3 ? '退出排序' : '排序管理' }}
          </el-button>
          <el-button
            :disabled="projectList.length > 7"
            type="text"
            @click="addProject(1)"
            ><svg-icon icon-class="add" /> 添加分组</el-button
          >
        </div>
      </div>
      <div
        ref="projectShow"
        v-if="projectList && projectList.length > 0"
        class="p-l-8 m-t-10"
      >
        <div
          class="project-content m-b-16"
          v-for="(item, index) in projectList"
          :key="index"
        >
          <div class="project-title">
            <div class="line-height-32">{{ item.groupName }}</div>
            <div>
              <el-button
                type="text"
                class="color-danger"
                @click="moveProject(index)"
                >移出</el-button
              >
              <el-button type="text" @click="addProject(2, index)"
                >新增</el-button
              >
            </div>
          </div>
          <div
            class="project-list line-height-32"
            v-for="(val, idx) in item.styleAttach"
            :key="idx"
          >
            <div
              class="color-primary pointer line-1"
              style="max-width: 800px"
              @click="openPic(val)"
            >
              {{ val.name }}
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <empty-data />
      </div>
    </div>
    <!--   配租公示-->
    <!--    <div class="m-t-24">-->
    <!--      <div class="item-title font-strong">配租公示</div>-->
    <!--      <div class="p-l-12 font-size-12 color-info">-->
    <!--        展示系统自动生成的相关配租公示-->
    <!--        <el-button type="text" class="font-size-12"> 查看图例 </el-button>-->
    <!--      </div>-->
    <!--      <div class="m-t-10 p-l-8">-->
    <!--        <el-switch v-model="showObj.rentalAllocationFlag" inactive-text="显示">-->
    <!--        </el-switch>-->
    <!--      </div>-->
    <!--    </div>-->

    <!--    基本信息材料清单弹窗-->
    <dialog-cmp
      title="材料清单"
      :visible.sync="basicVisible"
      width="30%"
      @confirmDialog="basicDialog"
    >
      <driven-form
        v-if="basicVisible"
        label-position="top"
        ref="driven-form-basic"
        v-model="fromBasic"
        :formConfigure="formConfigureBasic"
      />
    </dialog-cmp>
    <!--    资格条件弹窗-->
    <dialog-cmp
      :title="title"
      :visible.sync="statusVisible"
      width="30%"
      @confirmDialog="statusDialog"
    >
      <driven-form
        v-if="statusVisible"
        label-position="top"
        ref="driven-form-status"
        v-model="fromStatus"
        :formConfigure="formConfigureStatus"
      />
    </dialog-cmp>
    <!--    申请材料弹窗-->
    <dialog-cmp
      :title="titleMaterials"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form-sheet"
        v-model="fromMaterial"
        :formConfigure="formConfigureMaterial"
      />
    </dialog-cmp>
    <!--    办理地址弹窗-->
    <dialog-cmp
      :title="addressTitle"
      :visible.sync="addressVisible"
      width="35%"
      @confirmDialog="addressDialog"
    >
      <driven-form
        v-if="addressVisible"
        ref="driven-form-address"
        v-model="fromAddress"
        :formConfigure="formConfigureAddress"
      />
    </dialog-cmp>
    <!-- 地图位置地图选择弹窗 -->
    <dialog-cmp
      title="地图位置"
      :visible.sync="mapVisible"
      noPadding
      width="50%"
      @confirmDialog="mapChoiceConfirm"
    >
      <div class="map-choice-container">
        <map-choice v-if="mapVisible" @tapMap="tapMap" :lnglat="lnglat || []" />
      </div>
    </dialog-cmp>
    <!--    项目展示弹窗-->
    <dialog-cmp
      :title="projectTitle"
      :visible.sync="projectVisible"
      width="30%"
      @confirmDialog="projectDialog"
    >
      <driven-form
        v-if="projectVisible"
        label-position="top"
        ref="driven-form-project"
        v-model="fromProject"
        :formConfigure="formConfigureProject"
      />
    </dialog-cmp>
    <!--    查看图例-->
    <img-viewer ref="imgViewer" />
  </div>
</template>

<script>
import ColumnMixins from '@/views/manage/system/portal/portal-special/portal-publicRoom/show-information/column'
import Sortable from 'sortablejs'
import descriptorMixins from '@/views/manage/system/portal/portal-special/portal-publicRoom/show-information/descriptor'
import ImgViewer from '@/components/ImgViewer'
import {
  getApartmentInformation,
  getApartmentInformationCreate,
  getApartmentSelect
} from '@/views/manage/system/portal/portal-special/api'
import MapChoice from '@/components/MapChoice'

export default {
  name: 'ShowInformation',
  components: { MapChoice, ImgViewer },
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      basicObg: {
        handlingEntity: '', // 办理主体
        handlingType: '', // 办理类型
        serviceObject: '', // 服务对象
        handlingAddress: '', // 办理地点
        handlingTime: '', // 办理时间
        informationMethods: '', // 咨询方式
        handlingForm: '', // 办理形式
        attach: [] // 材料清单
      }, // 基本信息表单
      displayFields: [
        'attach',
        'handlingAddress',
        'handlingEntity',
        'handlingForm',
        'handlingTime',
        'handlingType',
        'informationMethods',
        'serviceObject'
      ], // 基本信息小眼睛
      showObj: {
        basicInfoFlag: true, // 基本信息显示
        prerequisitesFlag: true, // 资格条件
        applicationMaterialsFlag: true, // 申请材料是否显示
        processingLocationFlag: true, // 办理地点是否显示
        handlingProcessFlag: true, // 办理流程是否显示
        projectPresentationFlag: true, // 项目展示是否显示
        rentalAllocationFlag: true // 配租公示是否显示
      },
      prerequisitesJson: [], // 资格条件
      tableList: [], // 申请材料
      processingLocationJson: [], // 办理地点
      fromModel: {}, // 办理流程
      projectList: [], // 项目展示

      handlingProcedures: Math.random(),
      isSortable: false, // 排序-资格条件
      isSortable1: false, // 排序-申请材料
      isSortable2: false, // 排序-申请材料
      isSortable3: false, // 排序-项目展示
      visible: false, // 申请材料弹窗
      basicVisible: false, // 基本信息弹窗
      statusVisible: false, // 资格条件弹窗
      addressVisible: false, // 办理地点弹窗
      mapVisible: false, // 地图弹窗
      projectVisible: false, // 项目展示弹窗
      fromMaterial: {
        materialsForm: 1
      }, // 申请材料表单
      fromBasic: {}, // 基本信息表单
      fromStatus: {}, // 资格条件表单
      fromAddress: {}, // 办理地点表单
      fromProject: {}, // 项目展示表单
      addressIdx: '',
      mapData: {}, // 地图
      title: '添加分组', // 资格条件
      addressTitle: '添加地点', // 办理地点
      titleMaterials: '添加材料', // 申请材料
      projectTitle: '添加分组', // 项目展示
      current: '',
      project: '',
      idx: '',
      id: '',
      styleAttach: [],
      picUrl: '',
      materialIdx: ''
    }
  },
  watch: {
    isSortable(val) {
      val ? this.sortHandler() : this.destroyDrop()
    },
    isSortable2(val) {
      val ? this.sortAddress() : this.destroyDrop()
    },
    isSortable3(val) {
      val ? this.sortProject() : this.destroyDrop()
    }
  },
  computed: {
    lnglat() {
      const { locationLng, locationLat } = this.fromAddress
      return [locationLng, locationLat]
    },
    disabled() {
      return this.prerequisitesJson && this.prerequisitesJson.length > 0
    },
    disabled1() {
      return this.tableList && this.tableList.length > 0
    },
    disabled2() {
      return (
        this.processingLocationJson && this.processingLocationJson.length > 0
      )
    },
    disabled3() {
      return this.projectList && this.projectList.length > 0
    }
  },
  mounted() {
    this.getApartmentSelect()
    this.getApartmentInformation()
  },
  methods: {
    sortMethods(ids) {
      return new Promise(resolve => {
        console.log(ids)
        resolve()
      })
    },
    picExample(val) {
      if (val === 1) {
        this.picUrl = require('../images/basic-show.png')
        this.$refs.imgViewer.show([this.picUrl])
      }
      if (val === 2) {
        this.picUrl = require('../images/prerequisites.png')
        this.$refs.imgViewer.show([this.picUrl])
      }
      if (val === 3) {
        this.picUrl = require('../images/material.png')
        this.$refs.imgViewer.show([this.picUrl])
      }
      if (val === 4) {
        this.picUrl = require('../images/place-pic.png')
        this.$refs.imgViewer.show([this.picUrl])
      }
      if (val === 5) {
        this.picUrl = require('../images/process-pic.png')
        this.$refs.imgViewer.show([this.picUrl])
      }
      if (val === 6) {
        this.picUrl = require('../images/project-pic.png')
        this.$refs.imgViewer.show([this.picUrl])
      }
    },
    // 小眼睛显示隐藏
    showEye(str) {
      this.displayFields.forEach((item, index) => {
        if (item === str) {
          this.displayFields.splice(index, 1)
        }
      })
    },
    // 基本信息材料清单提交
    basicDialog() {
      this.$refs['driven-form-basic'].validate(valid => {
        if (valid) {
          this.basicObg.attach[0] = this.fromBasic.attach[0].id
          this.basicVisible = false
        }
      })
    },

    // 打来资格条件内容分组弹窗
    openDialog(val, idx) {
      this.fromStatus = {}
      this.idx = idx
      this.current = val
      if (val === 1) {
        this.title = '添加分组'
        this.formConfigureStatus.descriptors.description.hidden = true
        this.formConfigureStatus.descriptors.groupName.hidden = false
      } else {
        this.title = '添加内容'
        this.formConfigureStatus.descriptors.description.hidden = false
        this.formConfigureStatus.descriptors.groupName.hidden = true
      }
      this.statusVisible = true
    },
    // 资格条件表单提交
    statusDialog() {
      this.$refs['driven-form-status'].validate(valid => {
        if (valid) {
          if (this.current === 1) {
            this.prerequisitesJson.push({
              groupName: this.fromStatus.groupName,
              itemList: []
            })
            this.$toast.success('添加分组成功')
          } else {
            this.prerequisitesJson.forEach((item, index) => {
              if (index === this.idx) {
                item.itemList.push({
                  description: this.fromStatus.description
                })
              }
            })
            this.$toast.success('添加内容成功')
          }
          this.statusVisible = false
        }
      })
    },
    // 资格条件移出
    moveOut(val, scope) {
      if (val === 1) {
        this.$confirm('确定移出该分组？').then(() => {
          this.prerequisitesJson.splice(scope, 1)
          this.$toast.success('移出成功')
        })
      } else {
        this.$confirm('确定删除该分组内容？').then(() => {
          val.splice(scope.$index, 1)
          this.$toast.success('删除成功')
        })
      }
    },

    // 申请材料弹窗材料类型
    async getApartmentSelect() {
      const res = await getApartmentSelect({ type: 'materialsType' })
      const data = await getApartmentSelect({ type: 'source' })
      const materialsForm = await getApartmentSelect({ type: 'materialsForm' })
      this.formConfigureMaterial.descriptors.materialsType.options = res
      this.formConfigureMaterial.descriptors.source.options = data
      this.formConfigureMaterial.descriptors.materialsForm.options =
        materialsForm
    },
    // 打开申请材料弹窗
    addSheet() {
      this.materialIdx = ''
      this.titleMaterials = '添加材料'
      this.fromMaterial = {
        materialsForm: 1
      }
      this.visible = true
    },
    // 删除申请材料
    delHandler(scope) {
      this.$confirm('确定删除该材料？').then(() => {
        this.tableList.splice(scope.$index, 1)
        this.$toast.success('删除成功')
      })
    },
    // 下载
    downHandler(val) {
      if (val && Object.keys(val).length > 0) {
        window.open(val[0].path)
      } else {
        this.$toast.warning('暂未上传')
      }
    },
    // 编辑
    editMaterial(scope) {
      this.materialIdx = scope.$index
      this.titleMaterials = '编辑材料'
      this.fromMaterial = { ...scope.row }
      this.visible = true
    },
    // 申请材料弹窗提交
    confirmDialog() {
      this.$refs['driven-form-sheet'].validate(valid => {
        if (valid) {
          if (this.materialIdx !== '') {
            this.tableList.forEach((item, index) => {
              if (index === this.materialIdx) {
                for (let k in item) {
                  item[k] = this.fromMaterial[k]
                }
              }
            })
            this.$toast.success('编辑材料成功')
          } else {
            this.tableList.push(this.fromMaterial)
            this.$toast.success('添加材料成功')
          }
          this.visible = false
        }
      })
    },

    // 地图确定
    tapMap(e) {
      this.mapData = e
    },
    // 地图确认按钮
    mapChoiceConfirm() {
      let { lnglat, address } = this.mapData
      const [locationLng, locationLat] = lnglat
      this.$set(this.fromAddress, 'location', address)
      this.$set(this.fromAddress, 'locationLng', locationLng)
      this.$set(this.fromAddress, 'locationLat', locationLat)
      this.mapVisible = false
    },
    // 打开办理地点弹窗
    openAddress() {
      this.addressIdx = ''
      this.addressTitle = '添加地点'
      this.fromAddress = {}
      this.addressVisible = true
    },
    // 办理地点编辑
    editAddress(val, idx) {
      this.addressIdx = idx
      this.addressTitle = '编辑地点'
      this.addressVisible = true
      this.fromAddress = JSON.parse(JSON.stringify(val))
    },
    // 办理地点删除
    delAddress(idx) {
      this.$confirm('确定删除该办理地点？').then(() => {
        this.processingLocationJson.splice(idx, 1)
        this.$toast.success('删除成功')
      })
    },
    // 办理地点表单提交
    addressDialog() {
      this.$refs['driven-form-address'].validate(valid => {
        if (valid) {
          if (this.addressIdx === '') {
            this.processingLocationJson.push(this.fromAddress)
            this.$toast.success('添加成功')
          } else {
            this.processingLocationJson.forEach((item, index) => {
              if (index === this.addressIdx) {
                for (let k in item) {
                  item[k] = this.fromAddress[k]
                }
              }
            })
            this.$toast.success('编辑成功')
          }
          this.addressVisible = false
        }
      })
    },

    // 打开项目展示弹窗
    addProject(val, idx) {
      this.fromProject = {}
      this.idx = idx
      this.project = val
      if (val === 1) {
        this.projectTitle = '添加分组'
        this.formConfigureProject.descriptors.styleAttach.hidden = true
        this.formConfigureProject.descriptors.attach.hidden = false
        this.formConfigureProject.descriptors.groupName.hidden = false
      } else {
        this.projectTitle = '添加幻灯片'
        this.formConfigureProject.descriptors.styleAttach.hidden = false
        this.formConfigureProject.descriptors.attach.hidden = true
        this.formConfigureProject.descriptors.groupName.hidden = true
      }
      this.projectVisible = true
    },
    // 项目展示提交按钮
    projectDialog() {
      this.$refs['driven-form-project'].validate(valid => {
        if (valid) {
          if (this.project === 1) {
            this.projectList.push({ ...this.fromProject, styleAttach: [] })
            this.$toast.success('添加分组成功')
          } else {
            this.projectList.forEach((item, index) => {
              if (index === this.idx) {
                item.styleAttach.push(this.fromProject.styleAttach[0])
              }
            })
            this.$toast.success('添加幻灯片成功')
          }
          this.projectVisible = false
        }
      })
    },
    // 项目展示移出
    moveProject(idx) {
      this.$confirm('确定移出该项目？').then(() => {
        this.projectList.splice(idx, 1)
        this.$toast.success('移出成功')
      })
    },
    // 查看幻灯片
    openPic(val) {
      window.open(val.path)
    },

    // 拖拽排序-资格条件
    sortHandler() {
      const tbody = this.$refs.packetContent
      const _this = this
      this.sort_table = Sortable.create(tbody, {
        animation: 180,
        delay: 0,
        chosenClass: 'sortable-chosen', // 选中classname
        onUpdate: event => {
          const oldIndex = event.oldIndex,
            newIndex = event.newIndex,
            $child = tbody.children[newIndex],
            $oldChild = tbody.children[oldIndex]
          // 先删除移动的节点
          tbody.removeChild($child)
          // 再插入移动的节点到原有节点，还原了移动的操作
          if (newIndex > oldIndex) {
            tbody.insertBefore($child, $oldChild)
          } else {
            tbody.insertBefore($child, $oldChild.nextSibling)
          }
          // 更新items数组
          const item = _this.prerequisitesJson.splice(oldIndex, 1)
          _this.prerequisitesJson.splice(newIndex, 0, item[0])
        }
      })
    },
    // 拖拽排序-办理地点
    sortAddress() {
      const tbody = this.$refs.address
      const _this = this
      this.sort_table = Sortable.create(tbody, {
        animation: 180,
        delay: 0,
        chosenClass: 'sortable-address', // 选中classname
        onUpdate: event => {
          const oldIndex = event.oldIndex,
            newIndex = event.newIndex,
            $child = tbody.children[newIndex],
            $oldChild = tbody.children[oldIndex]
          // 先删除移动的节点
          tbody.removeChild($child)
          // 再插入移动的节点到原有节点，还原了移动的操作
          if (newIndex > oldIndex) {
            tbody.insertBefore($child, $oldChild)
          } else {
            tbody.insertBefore($child, $oldChild.nextSibling)
          }
          // 更新items数组
          const item = _this.placeList.splice(oldIndex, 1)
          _this.placeList.splice(newIndex, 0, item[0])
        }
      })
    },
    // 拖拽排序-项目展示
    sortProject() {
      const tbody = this.$refs.projectShow
      const _this = this
      this.sort_table = Sortable.create(tbody, {
        animation: 180,
        delay: 0,
        chosenClass: 'sortable-project', // 选中classname
        onUpdate: event => {
          const oldIndex = event.oldIndex,
            newIndex = event.newIndex,
            $child = tbody.children[newIndex],
            $oldChild = tbody.children[oldIndex]
          // 先删除移动的节点
          tbody.removeChild($child)
          // 再插入移动的节点到原有节点，还原了移动的操作
          if (newIndex > oldIndex) {
            tbody.insertBefore($child, $oldChild)
          } else {
            tbody.insertBefore($child, $oldChild.nextSibling)
          }
          // 更新items数组
          const item = _this.projectList.splice(oldIndex, 1)
          _this.projectList.splice(newIndex, 0, item[0])
        }
      })
    },
    // 销毁拖拽排序
    destroyDrop() {
      this.sort_table && this.sort_table.destroy()
      this.sort_table = null
    },
    // 拖拽排序开启关闭-申请材料
    sortableHandle(val) {
      this.isSortable1 = val
      this.listLength = this.tableList.length
      if (val)
        this.$refs['drive-table-Materials'] &&
          this.$refs['drive-table-Materials'].initDrag(this.tableList)
    },
    // 幻灯模块展开折叠
    showHandler(id) {
      let slide = document.getElementById(id)
      if (slide.style.maxHeight === '200px') {
        slide.style.maxHeight = '0'
      } else {
        slide.style.maxHeight = '200px'
      }
    },

    // 保存更改
    async saveSubmit() {
      let materialsList = JSON.parse(JSON.stringify(this.tableList))
      materialsList.forEach(item => {
        item.attach =
          (item.attach &&
            item.attach.map(item => {
              return item.id
            })) ||
          []
      })
      let projectList = JSON.parse(JSON.stringify(this.projectList))
      projectList.forEach(item => {
        item.attach = item.attach.map(val => {
          return val.id
        })
        item.styleAttach =
          (item.styleAttach &&
            item.styleAttach.map(val => {
              return val.id
            })) ||
          []
      })
      // 办理流程
      let handlingProcessJson = this.fromModel.handlingProcessJson
      let processAttach =
        (this.fromModel.processAttach &&
          this.fromModel.processAttach.map(item => {
            return item.id
          })) ||
        []
      let data = {
        processAttach,
        handlingProcessJson,
        processingLocationJson: this.processingLocationJson,
        prerequisitesJson: this.prerequisitesJson,
        displayFields: this.displayFields,
        ...this.basicObg,
        ...this.showObj,
        materialsList,
        projectList,
        id: this.id
      }
      // console.log(data)
      await getApartmentInformationCreate(data)
      this.getApartmentInformation()
      this.$toast.success('保存更改成功')
    },
    // 获取信息展示全部数据
    async getApartmentInformation() {
      const res = await getApartmentInformation()
      // console.log(res)
      let {
        handlingEntity,
        handlingType,
        serviceObject,
        handlingAddress,
        handlingTime,
        informationMethods,
        handlingForm,
        attach,
        displayFields,
        basicInfoFlag, // 基本信息显示
        prerequisitesFlag, // 资格条件
        applicationMaterialsFlag, // 申请材料是否显示
        processingLocationFlag, // 办理地点是否显示
        handlingProcessFlag, // 办理流程是否显示
        projectPresentationFlag, // 项目展示是否显示
        rentalAllocationFlag, // 配租公示是否显示
        materialsList, // 申请材料
        prerequisitesJson, // 资格条件
        processingLocationJson, // 办理地点
        processAttach, // 流程图
        handlingProcessJson, // 流程释义
        projectList // 项目展示
      } = res

      // 基本信息
      if (attach && Object.keys(attach).length > 0) {
        this.fromBasic.attach = attach.attachIds
      }
      this.basicObg = {
        handlingEntity,
        handlingType,
        serviceObject,
        handlingAddress,
        handlingTime,
        informationMethods,
        handlingForm,
        attach: []
      }
      this.displayFields = displayFields
      this.showObj = {
        basicInfoFlag, // 基本信息显示
        prerequisitesFlag, // 资格条件
        applicationMaterialsFlag, // 申请材料是否显示
        processingLocationFlag, // 办理地点是否显示
        handlingProcessFlag, // 办理流程是否显示
        projectPresentationFlag, // 项目展示是否显示
        rentalAllocationFlag // 配租公示是否显示
      }

      // 资格条件
      this.prerequisitesJson = prerequisitesJson
      // 办理地点
      this.processingLocationJson = processingLocationJson
      // 办理流程
      if (processAttach && Object.keys(processAttach).length > 0) {
        this.fromModel.processAttach = processAttach.processAttach
      }
      this.fromModel.handlingProcessJson = handlingProcessJson

      // 申请材料
      materialsList.forEach(item => {
        item.attach = (item.attach && item.attach?.attachIds) || []
      })
      this.tableList = materialsList

      // 项目展示
      if (projectList && projectList.length > 0) {
        projectList.forEach(item => {
          item.attach = item.attach.attachIds
          item.styleAttach = item.styleAttach.styleAttach || []
        })
      }
      this.projectList = projectList

      this.id = res.id
    },
    // 全部重置
    resettingAll() {
      this.basicObg = this.$options.data().basicObg
      this.displayFields = this.$options.data().displayFields
      this.showObj = this.$options.data().showObj
      this.prerequisitesJson = [] // 资格条件
      this.tableList = [] // 申请材料
      this.processingLocationJson = [] // 办理地点
      this.fromModel = {} // 办理流程
      this.projectList = [] // 项目展示
      this.handlingProcedures = Math.random()
    }
  }
}
</script>

<style lang="scss" scoped>
.map-choice-container {
  width: 100%;
  height: 500px;
}
.show-information-container {
  .color-s {
    color: rgba(0, 0, 0, 0.6);
  }
  .item-title {
    border-left: 4px solid #ed7b2f;
    font-size: 14px;
    line-height: 16px;
    padding-left: 8px;
  }

  .project-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 4px 16px;
    border-radius: 3px;
    font-size: 14px;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    @include background_color_mix(--color-primary, #fff, 95%);
  }
  .project-list {
    padding: 4px 32px;
    font-size: 14px;
    border-radius: 3px;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    @include background_color_mix(--color-primary, #fff, 95%);
  }
  .table-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 16px;
    border-radius: 3px;
    font-size: 14px;
    @include background_color_mix(--color-primary, #fff, 95%);
  }
  .sortable-chosen {
    .table-title {
      border-top: 2px solid #ed7b2f !important;
    }
  }

  .sortable-address {
    .address-title {
      border-top: 2px solid #ed7b2f !important;
    }
  }

  .sortable-project {
    .project-title {
      border-top: 2px solid #ed7b2f !important;
    }
  }
  table {
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    font-size: 14px;
  }

  th {
    width: 15%;
    @include background_color_mix(--color-primary, #ffffff, 96%);
    padding: 10px;
    font-weight: 400;
    text-align: left;
  }

  td {
    width: 35%;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    word-break: break-all;
    padding: 10px 10px 11px 10px;
    line-height: 1.6em;
    word-wrap: break-word;
  }
  .table-item {
    .icon-item {
      display: none;
    }
    &:hover {
      .icon-item {
        display: block;
      }
    }
  }

  .address-list {
    border: 1px solid;
    @include border_color(--border-color-lighter);
    border-bottom: none;
    .address-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      @include background_color_mix(--color-primary, #ffffff, 96%);
      line-height: 32px;
      font-size: 14px;
      padding: 0 16px;
    }
    .address-content {
      max-height: 0;
      transition: linear 0.3s;
      overflow: hidden;
      .address-item {
        padding: 16px;
      }
    }
    &:last-child {
      border-bottom: 1px solid;
      @include border_color(--border-color-lighter);
    }
  }
}

::v-deep {
  table .el-input__inner {
    border: 1px solid transparent;
    padding-left: 0;
  }
  table .el-input.is-disabled .el-input__inner {
    background-color: transparent;
    border-color: transparent;
  }
}
</style>
<style lang="scss">
.show-information-container {
  .sortable-chosen {
    .el-table__cell {
      border-top: none !important;
      background: transparent;
    }
  }
}
</style>
