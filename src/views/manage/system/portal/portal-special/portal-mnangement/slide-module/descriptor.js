export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          styleAttachIds: {
            form: 'component',
            label: '风采图册',
            rule: [
              {
                required: true,
                message: '请上传风采图册',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'styleAttachIds'
              },
              mulity: true,
              maxSize: 10,
              limit: 9
            }
            // customTips: () => {
            //   return <div>请上传格式为png/jpg/jpeg的图片</div>
            // }
          }
        }
      }
    }
  }
}
