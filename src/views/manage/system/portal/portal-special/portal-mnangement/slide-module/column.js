export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'park',
          label: '园区名称',
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  {scope.row.park}
                </div>
                <div v-show={scope.row.flag}>{scope.row.park}</div>
              </div>
            )
          }
        },
        {
          prop: 'operate',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  已选择
                </div>
                <div
                  v-show={scope.row.flag}
                  class={'pointer'}
                  onClick={() => {
                    this.chooseHandler(scope.row)
                  }}
                >
                  选择
                </div>
              </div>
            )
          }
        }
      ]
    }
  }
}
