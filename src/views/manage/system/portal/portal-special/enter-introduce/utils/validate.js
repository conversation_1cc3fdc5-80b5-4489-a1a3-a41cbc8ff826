// 邮件
const emailNum = /^\w+@[a-z0-9]+\.[a-z]{2,4}$/
export function validateEmail(rule, value, callback) {
  if (!value) {
    callback()
  } else {
    if (emailNum.test(value)) {
      callback()
    } else {
      callback(new Error('邮箱不正确'))
    }
  }
}

export function validatePhone(rule, value, callback) {
  if (value === '') {
    callback(new Error(`请输入电话`))
  } else {
    if (/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value)) {
      callback()
    } else {
      callback(new Error(`电话号码不正确`))
    }
  }
}
