export default {
  data() {
    return {
      // 公告模块
      tableColumn: [
        {
          prop: 'title',
          label: '标题'
        },
        {
          prop: 'operate',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-button
                onClick={() => {
                  this.delTopMove(scope)
                }}
                type={'text'}
                class={'color-danger'}
              >
                移出
              </el-button>
            )
          }
        }
      ],
      // 公告抽屉
      tableColumnNotice: [
        {
          prop: 'title',
          label: '标题',
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  {scope.row.title}
                </div>
                <div v-show={scope.row.flag}>{scope.row.title}</div>
              </div>
            )
          }
        },
        {
          prop: 'operate',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  已选择
                </div>
                <div
                  v-show={scope.row.flag}
                  class={'pointer'}
                  onClick={() => {
                    this.chooseHandler(scope.row)
                  }}
                >
                  选择
                </div>
              </div>
            )
          }
        }
      ],
      // 资讯抽屉
      tableInformationList: [
        {
          prop: 'title',
          label: '标题',
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  {scope.row.title}
                </div>
                <div v-show={scope.row.flag}>{scope.row.title}</div>
              </div>
            )
          }
        },
        {
          prop: 'operate',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  已选择
                </div>
                <div
                  v-show={scope.row.flag}
                  class={'pointer'}
                  onClick={() => {
                    this.chooseFn(scope.row)
                  }}
                >
                  选择
                </div>
              </div>
            )
          }
        }
      ],
      // 政策抽屉
      tablePolicyList: [
        {
          prop: 'policyName',
          label: '标题',
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  {scope.row.policyName}
                </div>
                <div v-show={scope.row.flag}>{scope.row.policyName}</div>
              </div>
            )
          }
        },
        {
          prop: 'operate',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <div v-show={!scope.row.flag} class={'color-info'}>
                  已选择
                </div>
                <div
                  v-show={scope.row.flag}
                  class={'pointer'}
                  onClick={() => {
                    this.chooseFun(scope.row)
                  }}
                >
                  选择
                </div>
              </div>
            )
          }
        }
      ]
    }
  }
}
