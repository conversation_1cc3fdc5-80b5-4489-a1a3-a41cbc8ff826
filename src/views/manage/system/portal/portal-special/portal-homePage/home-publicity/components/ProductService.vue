<template>
  <div>
    <drive-table
      :columns="productServiceColumns"
      :table-data="tableData"
      :key="tableKey"
    />
    <dialog-cmp
      title="产品服务"
      :visible.sync="visible"
      width="500px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        v-model="fromData"
        :formConfigure="productServiceFormConfigure"
        label-position="top"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
export default {
  name: 'ProductService',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      tableData: [],
      visible: false,
      fromData: {},
      editIndex: 0,
      tableKey: 'product-service-' + Math.random()
    }
  },
  watch: {
    value: {
      handler(val) {
        this.tableData = val
      },
      deep: true,
      immediate: true
    },
    visible(val) {
      if (!val) {
        this.fromData = {}
      }
    }
  },
  methods: {
    operateSuccess(text) {
      this.tableKey = 'product-service-' + Math.random()
      this.$toast.success(text + '成功')
      this.$emit('input', this.tableData)
    },
    deleteHandle(index) {
      this.$confirm('确认删除产品服务？').then(() => {
        this.tableData.splice(index, 1)
        this.operateSuccess('删除')
      })
    },
    editHandle(row, index) {
      this.fromData = {
        ...row
      }
      this.editIndex = index
      this.visible = true
    },
    addHandle() {
      this.visible = true
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const { id } = this.fromData
        if (!id) {
          this.tableData.push({
            ...this.fromData,
            id: 'product-service-' + Date.now()
          })
        } else {
          this.tableData[this.editIndex] = this.fromData
        }
        this.visible = false
        this.operateSuccess(id ? '编辑' : '新增')
      })
    }
  }
}
</script>

<style scoped></style>
