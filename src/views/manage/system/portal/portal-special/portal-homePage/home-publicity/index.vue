<template>
  <div class="home-publicity-container p-16 h100">
    <!--    宣传模块1-->
    <div>
      <div class="item-title font-strong">宣传模块1</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        配置宣传展示的图文模块一，
        <el-button type="text" class="font-size-12" @click="iconExample(0)">
          查看图例
        </el-button>
      </div>
      <div class="m-t-16 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.moduleOne"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <div class="font-size-14 m-t-16 p-l-8">
        {{ moduleOneName }}
        <svg-icon @click="openTitle(0)" class="pointer" icon-class="edit-1" />
      </div>
      <!--      数据展示-->
      <div class="p-l-8 m-t-8 font-size-14">
        <div class="flex justify-content-between align-items-center">
          <div>数据展示</div>
          <el-button type="text" @click="openShowData" :disabled="addDisabled"
            >新增</el-button
          >
        </div>
        <drive-table
          ref="drive-table"
          maxHeight="360px"
          :columns="tableColumn"
          :table-data="moduleOneData"
        >
        </drive-table>
      </div>
      <!--      相册展示-->
      <div class="font-size-14 m-t-16 p-l-8">
        <div class="m-b-16">相册展示</div>
        <div class="flex justify-content-between">
          <div
            class="pic-item"
            v-for="(item, index) in picList"
            :key="index"
            style="flex: 1"
          >
            <div>内容{{ index + 1 }}</div>
            <driven-form
              label-position="top"
              ref="driven-form-dialog"
              v-model="item.fromModel"
              :formConfigure="formConfigure"
              :key="publicImg"
            />
          </div>
        </div>
        <driven-form
          label-position="top"
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigureUrl"
          :key="publicUrl"
        />
      </div>
    </div>
    <!--    宣传模块2-->
    <div>
      <div class="item-title font-strong">宣传模块2</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        配置宣传展示的图文模块二，
        <el-button type="text" class="font-size-12" @click="iconExample(1)">
          查看图例
        </el-button>
      </div>
      <div class="m-t-16 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.moduleTwo"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <div
        class="flex justify-content-between align-items-center font-size-14 m-t-8 p-l-8"
      >
        <div>
          {{ moduleTwoName }}
          <svg-icon @click="openTitle(1)" class="pointer" icon-class="edit-1" />
        </div>
        <el-button :disabled="disabled" type="text" @click="addListHandler"
          >新增</el-button
        >
      </div>
      <!--      内容区域-->
      <div class="font-size-14 m-t-16 p-l-8">
        <div class="publicity-list">
          <div
            class="publicity-item"
            v-for="(item, index) in dataList"
            :key="index"
          >
            <div class="flex justify-content-between align-items-center">
              <div class="line-height-32">内容{{ index + 1 }}</div>
              <el-button
                v-if="index !== 0"
                type="text"
                class="color-danger"
                @click="moveOut(index)"
                >移出</el-button
              >
            </div>
            <driven-form
              label-position="top"
              ref="driven-form"
              v-model="item.fromModel"
              :formConfigure="formConfigure2"
              :key="publicTwo"
            />
          </div>
          <div class="publicity-item"></div>
        </div>
      </div>
    </div>
    <!--    宣传模块3-->
    <div class="m-b-24">
      <div class="item-title font-strong">宣传模块3</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        配置展示推荐企业的相关信息，
        <el-button type="text" class="font-size-12" @click="iconExample(4)">
          查看图例
        </el-button>
      </div>
      <div class="m-t-16 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="recommendEnterprise.moduleEnt"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <div class="m-t-16 p-l-8">
        <div class="font-size-14 m-b-16">宣传标题</div>
        <el-input
          v-model="recommendEnterprise.moduleEntName"
          :maxlength="10"
          placeholder="请输入宣传标题"
        ></el-input>
      </div>
      <div class="m-t-16 p-l-8 font-size-14">
        <div class="flex justify-content-between align-items-center">
          <div>服务专区</div>
          <el-button type="text" @click="serviceAddHandle"
            ><svg-icon icon-class="add" /> 新增</el-button
          >
        </div>
        <div class="flex m-t-8">
          <div
            v-for="(item, index) in recommendEnterprise.tabs"
            :key="index"
            class="recommend-enterprise-tab-item"
            :class="{ active: recommendEnterprise.tabCurrent === item.value }"
            @click="tabItemClick(item)"
          >
            <span>{{ item.label }}</span>
            <svg-icon
              icon-class="edit-1"
              class-name="m-l-8"
              @click.stop="editTab(item, index)"
            />
            <svg-icon
              icon-class="delete"
              class-name="m-l-8"
              @click.stop="deleteTab(item, index)"
            />
          </div>
        </div>
      </div>
      <div
        class="m-t-16 p-l-8 font-size-14"
        v-if="recommendEnterprise.tabs.length"
      >
        <div class="flex justify-content-between align-items-center">
          <div>企业列表</div>
          <el-button type="text" @click="addEnterpriseHandle"
            ><svg-icon icon-class="add" /> 新增</el-button
          >
        </div>
        <div class="m-t-8">
          <drive-table
            :columns="recommendEnterpriseTableColumn"
            :table-data="recommendEnterprise.recommendEnterpriseTableData"
            :key="recommendEnterprise.recommendEnterpriseTableKey"
          />
        </div>
      </div>
    </div>
    <!--    宣传模块4-->
    <div class="m-b-24">
      <div class="item-title font-strong">宣传模块4</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        配置展示的合作企业，
        <el-button type="text" class="font-size-12" @click="iconExample(2)">
          查看图例
        </el-button>
      </div>
      <div class="m-t-16 p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.moduleThree"
          inactive-text="显示"
        >
        </el-switch>
      </div>
      <div class="m-t-16 p-l-8">
        <div class="font-size-14 m-b-16">宣传标题</div>
        <el-input
          v-model="moduleThreeName"
          :maxlength="10"
          placeholder="请输入宣传标题"
        ></el-input>
      </div>
      <!--      表格区域-->
      <div class="m-t-16 p-l-8 font-size-14">
        <div class="flex justify-content-between align-items-center">
          <div>推荐企业</div>
          <el-button type="text" @click="openHandler"
            ><svg-icon icon-class="add" /> 新增</el-button
          >
        </div>
        <drive-table
          :show-header="false"
          ref="drive-table-enter"
          :table-data="tableList"
          height="352px"
          :columns="tableColumnEnter"
        >
        </drive-table>
      </div>
    </div>
    <!--    宣传模块5-->
    <div>
      <div class="item-title font-strong">宣传模块5</div>
      <div class="p-l-12 font-size-12 color-info m-b-10">
        配置展示的友情链接，
        <el-button type="text" class="font-size-12" @click="iconExample(3)">
          查看图例
        </el-button>
      </div>
      <div class="flex justify-content-between align-items-center p-l-8">
        <el-switch
          class="m-r-24"
          v-model="showObj.moduleFour"
          inactive-text="显示"
        >
        </el-switch>
        <el-button type="text" @click="openLink">新增</el-button>
      </div>
      <!--      表格区域-->
      <div class="m-t-16 p-l-8">
        <drive-table
          :show-header="false"
          ref="drive-table-enter"
          :table-data="moduleFourJson"
          height="352px"
          :columns="tableColumnLink"
        >
        </drive-table>
      </div>
    </div>
    <!--    服务专区新增编辑-->
    <dialog-cmp
      :title="recommendEnterprise.type === 1 ? '新增服务专区' : '编辑服务专区'"
      :visible.sync="recommendEnterprise.visible"
      width="30%"
      @confirmDialog="serveAreaConfirmDialog"
    >
      <driven-form
        v-if="recommendEnterprise.visible"
        ref="driven-form-serve-area"
        v-model="recommendEnterprise.fromData"
        :formConfigure="serveAreaFormConfigure"
        label-position="top"
      />
    </dialog-cmp>
    <!--    服务专区企业列表新增编辑-->
    <basic-drawer
      :title="
        recommendEnterprise.drawerType === 1 ? '新增推荐企业' : '编辑推荐企业'
      "
      :visible.sync="recommendEnterprise.drawerVisible"
      @confirmDrawer="recommendEnterpriseConfirmDialog"
    >
      <driven-form
        class="recommend-enterprise"
        v-if="recommendEnterprise.drawerVisible"
        ref="driven-form-recommend-enterprise"
        label-position="top"
        v-model="recommendEnterprise.drawerFromData"
        :formConfigure="recommendEnterpriseFormConfigure"
      />
    </basic-drawer>
    <!--    数据展示-->
    <dialog-cmp
      title="新增数据"
      :visible.sync="dataVisible"
      width="30%"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="dataVisible"
        label-position="top"
        ref="driven-form-data"
        v-model="fromData"
        :formConfigure="formConfigureData"
      />
    </dialog-cmp>
    <!--    添加推荐企业-->
    <basic-drawer
      title="选择企业"
      :visible.sync="visible"
      @confirmDrawer="visible = false"
    >
      <div class="font-size-14 m-b-16">数据来源于企业库列表</div>
      <drive-table
        ref="drive-table1"
        :table-data="addList"
        height="calc(100vh - 360px)"
        :columns="tableColumn1"
      >
      </drive-table>
    </basic-drawer>
    <!--    宣传模块5弹窗-->
    <dialog-cmp
      title="新增链接"
      :visible.sync="linkVisible"
      width="30%"
      @confirmDialog="linkDialog"
    >
      <driven-form
        v-if="linkVisible"
        label-position="top"
        ref="driven-form-link"
        v-model="fromLink"
        :formConfigure="formConfigureLink"
      />
    </dialog-cmp>
    <!--    编辑标题弹窗-->
    <dialog-cmp
      title="编辑标题"
      :visible.sync="titleVisible"
      width="30%"
      @confirmDialog="titleDialog"
    >
      <driven-form
        v-if="titleVisible"
        label-position="top"
        ref="driven-form-title"
        v-model="formTitle"
        :formConfigure="formConfigureTitle"
      />
    </dialog-cmp>
    <!--    查看图例-->
    <img-viewer ref="imgViewer" />
  </div>
</template>

<script>
import ColumnMixins from '@/views/manage/system/portal/portal-special/portal-homePage/home-publicity/column'
import descriptorMixins from '@/views/manage/system/portal/portal-special/portal-homePage/home-publicity/descriptor'
import {
  getEnterListAll,
  getEnterpriseList,
  getHomePublicizeAll,
  getHomePublicizeCreate,
  getIndustryList
} from '@/views/manage/system/portal/portal-special/api'
import ImgViewer from '@/components/ImgViewer'

export default {
  name: 'HomePublicity',
  mixins: [ColumnMixins, descriptorMixins],
  components: {
    ImgViewer
  },
  data() {
    return {
      showObj: {
        moduleOne: true, // 模块一显示
        moduleTwo: true, // 模块二显示
        moduleThree: true, // 模块三显示
        moduleFour: true // 模块四显示
      },
      formTitle: {
        moduleOneName: '园区风采', // 模块一名称
        moduleTwoName: '配套载体' // 模块二名称
      },
      moduleOneName: '园区风采',
      moduleTwoName: '配套载体',
      moduleOneData: [], // 数据展示
      picList: [
        {
          fromModel: {}
        },
        {
          fromModel: {}
        },
        {
          fromModel: {}
        }
      ], // 相册展示
      dataList: [
        {
          fromModel: {}
        }
      ], // 宣传模块2
      moduleThreeName: '', // 宣传模块4 - 宣传标题
      tableList: [], // 宣传模块4
      moduleFourJson: [], // 宣传模块5
      fromModel: {}, // 视频url

      visible: false, // 推荐企业
      addList: [], // 推荐企业
      dataVisible: false, // 宣传模块1 - 数据展示
      fromData: {}, // 数据展示
      linkVisible: false, // 宣传模块5
      fromLink: {}, // 宣传模块5
      titleVisible: false, // 编辑标题
      id: '',
      publicUrl: Math.random(),
      publicImg: Math.random(),
      publicTwo: Math.random(),
      recommendEnterprise: {
        moduleEnt: true,
        moduleEntName: '推荐企业',
        tabCurrent: '',
        editIndex: 0,
        tabs: [],
        type: 1,
        visible: false,
        fromData: {},
        recommendEnterpriseTableKey: Math.random(),
        recommendEnterpriseTableData: [],
        tableData: [
          {
            tabKey: '',
            tableData: []
          }
        ],
        drawerVisible: false,
        drawerType: 1,
        drawerFromData: {},
        drawerEditIndex: 0
      }
    }
  },
  computed: {
    disabled() {
      return this.dataList && this.dataList.length > 7
    },
    addDisabled() {
      return this.moduleOneData && this.moduleOneData.length > 2
    }
  },
  watch: {
    'recommendEnterprise.tabs': {
      handler(val) {
        this.recommendEnterpriseFormConfigure.descriptors.areaUnique.options =
          val
        this.recommendEnterpriseTableData()
      },
      deep: true,
      immediate: true
    },
    'recommendEnterprise.drawerVisible'(val) {
      if (!val) {
        this.recommendEnterprise.drawerType = 1
        this.recommendEnterprise.drawerFromData = {}
      }
    },
    'recommendEnterprise.visible'(val) {
      if (!val) {
        this.recommendEnterprise.type = 1
        this.recommendEnterprise.fromData = {}
      }
    }
  },
  mounted() {
    this.getHomePublicizeAll()
    this.getEnterpriseList()
    this.getIndustryList()
  },
  methods: {
    recommendEnterpriseTableData() {
      const row = this.recommendEnterprise.tableData.find(
        item => item.tabKey === this.recommendEnterprise.tabCurrent
      )
      this.recommendEnterprise.recommendEnterpriseTableData =
        row && row.tableData ? row.tableData : []
      this.recommendEnterprise.recommendEnterpriseTableKey = Math.random()
    },
    getEnterpriseList() {
      getEnterpriseList().then(res => {
        this.recommendEnterpriseFormConfigure.descriptors.enterpriseName.options =
          res.map(item => {
            return {
              label: item.label,
              value: item.key,
              industry: item.industry
            }
          })
      })
    },
    getIndustryList() {
      getIndustryList().then(res => {
        this.recommendEnterpriseFormConfigure.descriptors.industry.options =
          res.map(item => {
            return {
              label: item.label,
              value: item.key
            }
          })
      })
    },
    areaUniqueChange(e) {
      const row = this.recommendEnterprise.tabs.find(item => item.value === e)
      this.recommendEnterprise.drawerFromData.areaName = row.label
    },
    enterpriseChange(e) {
      const row =
        this.recommendEnterpriseFormConfigure.descriptors.enterpriseName.options.find(
          item => item.value === e
        )
      this.$set(
        this.recommendEnterprise.drawerFromData,
        'industry',
        row ? row.industry : ''
      )
    },
    // 企业列表新增编辑提交
    recommendEnterpriseConfirmDialog() {
      this.$refs['driven-form-recommend-enterprise'].validate(valid => {
        if (!valid) return false
        const { id, areaUnique } = this.recommendEnterprise.drawerFromData
        const params = {
          tabKey: this.recommendEnterprise.tabCurrent,
          tableData: [this.recommendEnterprise.drawerFromData]
        }
        const row = this.recommendEnterprise.tableData.find(
          item => item.tabKey === this.recommendEnterprise.tabCurrent
        )
        if (!id) {
          if (areaUnique === this.recommendEnterprise.tabCurrent) {
            row.tableData.push(this.recommendEnterprise.drawerFromData)
          } else {
            const row1 = this.recommendEnterprise.tableData.find(
              item => item.tabKey === areaUnique
            )
            if (row1) {
              row1.tableData.push(this.recommendEnterprise.drawerFromData)
            } else {
              this.recommendEnterprise.tableData.push(params)
            }
          }
        } else {
          if (areaUnique === this.recommendEnterprise.tabCurrent) {
            row.tableData[this.recommendEnterprise.drawerEditIndex] =
              this.recommendEnterprise.drawerFromData
          } else {
            const row1 = this.recommendEnterprise.tableData.find(
              item => item.tabKey === areaUnique
            )
            row1.tableData.push(this.recommendEnterprise.drawerFromData)
            row.tableData.splice(this.recommendEnterprise.drawerEditIndex, 1)
          }
        }
        this.recommendEnterpriseTableData()
        this.recommendEnterprise.drawerVisible = false
      })
    },
    // 企业列表编辑
    editHandle(row, index) {
      this.recommendEnterprise.drawerType = 2
      this.recommendEnterprise.drawerEditIndex = index
      this.recommendEnterprise.drawerFromData = {
        ...row,
        id: row.id ? row.id : 'enterprise-list-' + Date.now()
      }
      this.recommendEnterprise.drawerVisible = true
    },
    // 企业列表移出
    deleteHandle(index) {
      this.$confirm('确认移出当前企业？').then(() => {
        const row = this.recommendEnterprise.tableData.find(
          item => item.tabKey === this.recommendEnterprise.tabCurrent
        )
        row.tableData.splice(index, 1)
        this.recommendEnterpriseTableData()
        this.$toast.success('移出成功')
      })
    },
    // 企业列表新增
    addEnterpriseHandle() {
      this.$set(
        this.recommendEnterprise.drawerFromData,
        'areaUnique',
        this.recommendEnterprise.tabCurrent
      )
      this.areaUniqueChange(this.recommendEnterprise.tabCurrent)
      this.recommendEnterprise.drawerType = 1
      this.recommendEnterprise.drawerVisible = true
    },
    // 服务专区删除
    deleteTab(item, index) {
      this.$confirm(
        '确认删除服务专区？专区删除后，专区下的企业信息也会被删除？'
      ).then(() => {
        this.recommendEnterprise.tabs.splice(index, 1)
        if (item.value === this.recommendEnterprise.tabCurrent) {
          this.recommendEnterprise.tabCurrent = this.recommendEnterprise.tabs
            .length
            ? this.recommendEnterprise.tabs[0].value
            : ''
        }
        this.$toast.success('删除成功')
      })
    },
    // 服务专区编辑
    editTab(item, index) {
      this.recommendEnterprise.fromData = {
        id: item.value,
        name: item.label
      }
      this.recommendEnterprise.editIndex = index
      this.recommendEnterprise.type = 2
      this.recommendEnterprise.visible = true
    },
    // 服务专区新增编辑确定
    serveAreaConfirmDialog() {
      this.$refs['driven-form-serve-area'].validate(valid => {
        if (!valid) return false
        const { id } = this.recommendEnterprise.fromData
        if (!id) {
          this.recommendEnterprise.tabs.push({
            label: this.recommendEnterprise.fromData.name,
            value: 'serve-area-' + Date.now()
          })
          if (this.recommendEnterprise.tabs.length === 1) {
            this.recommendEnterprise.tabCurrent =
              this.recommendEnterprise.tabs[0].value
          }
        } else {
          this.recommendEnterprise.tabs[this.recommendEnterprise.editIndex] = {
            label: this.recommendEnterprise.fromData.name,
            value: this.recommendEnterprise.fromData.id
          }
        }
        if (
          this.recommendEnterprise.tableData.length === 1 &&
          !this.recommendEnterprise.tableData[0].tabKey
        ) {
          this.recommendEnterprise.tableData[0].tabKey =
            this.recommendEnterprise.tabCurrent
        }
        this.recommendEnterprise.visible = false
      })
    },
    // 服务专区切换
    tabItemClick(row) {
      this.recommendEnterprise.tabCurrent = row.value
      this.recommendEnterpriseTableData()
    },
    // 服务专区新增
    serviceAddHandle() {
      this.recommendEnterprise.type = 1
      this.recommendEnterprise.visible = true
    },
    iconExample(val) {
      let titleUrl = ''
      if (val === 0) {
        titleUrl = require('../image/module01.png')
        this.$refs.imgViewer.show([titleUrl])
      }
      if (val === 1) {
        titleUrl = require('../image/module02.png')
        this.$refs.imgViewer.show([titleUrl])
      }
      if (val === 2) {
        titleUrl = require('../image/module03.png')
        this.$refs.imgViewer.show([titleUrl])
      }
      if (val === 3) {
        titleUrl = require('../image/module04.png')
        this.$refs.imgViewer.show([titleUrl])
      }
      if (val === 4) {
        titleUrl = require('../image/module05.png')
        this.$refs.imgViewer.show([titleUrl])
      }
    },
    // 宣传模块1 - 打开数据展示弹窗
    openShowData() {
      this.fromData = {}
      this.dataVisible = true
    },
    // 删除
    delHandler(scope) {
      this.$confirm('确定删除该数据？').then(() => {
        this.moduleOneData.splice(scope.$index, 1)
        this.$toast.success('移出成功')
      })
    },
    // 宣传模块1 - 数据展示
    confirmDialog() {
      this.$refs['driven-form-data'].validate(valid => {
        if (valid) {
          this.moduleOneData.push({
            ...this.fromData
          })
          this.$toast.success('新增成功')
          this.dataVisible = false
        }
      })
    },

    // 宣传模块2 - 新增
    addListHandler() {
      this.dataList.push({
        fromModel: {}
      })
      this.$toast.success('新增成功')
    },
    // 宣传模块2 - 移出
    moveOut(idx) {
      this.$confirm('确定移出该内容？').then(() => {
        this.dataList.splice(idx, 1)
        this.$toast.success('移出成功')
      })
    },

    // 推荐企业选择
    chooseHandler(row) {
      if (this.tableList.length > 29)
        return this.$toast.warning('最多添加30个重点企业')
      row.flag = false
      this.tableList.push(row)
      this.$toast.success('已选择')
    },
    // 添加
    async openHandler() {
      const res = await getEnterListAll()
      this.addList = res.map(item => {
        return { enterpriseName: item.enterpriseName, id: item.id, flag: true }
      })

      this.addList.forEach(item => {
        this.tableList.forEach(val => {
          if (item.id === val.id) {
            item.flag = false
          }
        })
      })
      this.visible = true
    },
    // 移出
    moveHandler(scope) {
      this.$confirm('确定移出该企业？').then(() => {
        this.tableList.splice(scope.$index, 1)
        this.addList.forEach(item => {
          if (scope.row.id === item.id) {
            item.flag = true
          }
        })
        this.$toast.success('移出成功')
      })
    },

    // 宣传模块5
    openLink() {
      this.fromLink = {}
      this.linkVisible = true
    },
    // 弹窗提交
    linkDialog() {
      this.$refs['driven-form-link'].validate(valid => {
        if (valid) {
          this.moduleFourJson.push({
            ...this.fromLink
          })
          this.linkVisible = false
        }
      })
    },
    // 删除
    delUrlHandler(scope) {
      this.$confirm('确定移出该链接？').then(() => {
        this.moduleFourJson.splice(scope.$index, 1)
        this.$toast.success('移出成功')
      })
    },

    // 打开标题弹窗
    openTitle(val) {
      this.formTitle.moduleOneName = this.moduleOneName
      this.formTitle.moduleTwoName = this.moduleTwoName
      if (val === 0) {
        this.formConfigureTitle.descriptors.moduleOneName.disabled = false
        this.formConfigureTitle.descriptors.moduleTwoName.disabled = true
      } else {
        this.formConfigureTitle.descriptors.moduleOneName.disabled = true
        this.formConfigureTitle.descriptors.moduleTwoName.disabled = false
      }
      this.titleVisible = true
    },
    // 标题弹窗提交
    titleDialog() {
      this.$refs['driven-form-title'].validate(valid => {
        if (valid) {
          this.moduleOneName = this.formTitle.moduleOneName
          this.moduleTwoName = this.formTitle.moduleTwoName
          this.titleVisible = false
        }
      })
    },

    // 保存更改
    async saveSubmit() {
      // 相册展示
      let albumJson = JSON.parse(JSON.stringify(this.picList))
      albumJson = albumJson.map(item => {
        return item.fromModel
      })
      albumJson.forEach(item => {
        if (item.attach && item.attach.length > 0) {
          item.attach = [item.attach[0].id] || []
        }
      })
      // 视频URL
      let videoAttach = []
      if (this.fromModel.videoAttach && this.fromModel.videoAttach.length > 0) {
        videoAttach = [this.fromModel.videoAttach[0].id]
      }
      // 宣传模块2
      let moduleTwoData = JSON.parse(JSON.stringify(this.dataList))
      moduleTwoData = moduleTwoData.map(item => {
        return item.fromModel
      })
      moduleTwoData.forEach(item => {
        if (item.attach && item.attach.length > 0) {
          item.attach = [item.attach[0].id] || []
        }
      })
      // 宣传模块3
      let list = []
      this.recommendEnterprise.tableData.forEach(item => {
        list = [...list, ...item.tableData]
      })
      list.forEach(item => {
        item.attach = item.attach.map(row => row.id)
        if (item.attachMap) {
          delete item.attachMap
        }
        if (item.id) {
          delete item.id
        }
      })
      const recommendEnterpriseObj = {
        moduleEnt: this.recommendEnterprise.moduleEnt,
        moduleEntName: this.recommendEnterprise.moduleEntName,
        services: this.recommendEnterprise.tabs,
        homeEnterpriseInfos: list
      }
      // 宣传模块4 - 推荐企业
      let recommendEntId = []
      recommendEntId = this.tableList.map(item => {
        return item.id || ''
      })
      let data = {
        moduleOneName: this.moduleOneName,
        moduleTwoName: this.moduleTwoName,
        ...this.showObj,
        albumJson,
        videoAttach,
        moduleOneData: this.moduleOneData,
        moduleTwoData,
        moduleThreeName: this.moduleThreeName,
        recommendEntId,
        moduleFourJson: this.moduleFourJson,
        id: this.id,
        ...recommendEnterpriseObj
      }

      await getHomePublicizeCreate(data)
      this.getHomePublicizeAll()
      this.$toast.success('保存更改成功')
    },
    // 获取全部数据
    async getHomePublicizeAll() {
      const res = await getHomePublicizeAll()
      if (!res.id) return
      this.id = res.id || ''
      let {
        moduleOneData,
        albumJson,
        videoAttach,
        moduleTwoData,
        moduleThreeName,
        recommendEntList,
        moduleFourJson,
        moduleOne,
        moduleTwo,
        moduleThree,
        moduleFour,
        moduleOneName,
        moduleTwoName,
        moduleEnt,
        moduleEntName,
        services,
        getHomeEnterpriseInfos
      } = res
      this.recommendEnterprise.moduleEnt = moduleEnt
      this.recommendEnterprise.moduleEntName = moduleEntName
      this.recommendEnterprise.tabs = services
      if (services && services.length) {
        this.recommendEnterprise.tabCurrent = services[0].value
      }
      getHomeEnterpriseInfos.forEach(item => {
        item.tableData &&
          item.tableData.forEach(row => {
            row.attach = row?.attachMap?.logo || []
          })
      })
      this.recommendEnterprise.tableData = getHomeEnterpriseInfos
      // 数据展示
      this.moduleOneData = moduleOneData || []
      // 相册展示
      albumJson &&
        albumJson.forEach(item => {
          item.fromModel = {
            attach:
              item.attachMap && Object.keys(item.attachMap).length > 0
                ? item.attachMap.attachIds
                : [],
            name: item.name
          }
        })
      if (albumJson && albumJson.length > 0) {
        this.picList = albumJson.map(item => {
          return { fromModel: item.fromModel }
        })
      }
      // 视频URL
      this.fromModel = {
        videoAttach:
          videoAttach && Object.keys(videoAttach).length > 0
            ? videoAttach.videoAttach
            : []
      }
      // 宣传模块2
      moduleTwoData &&
        moduleTwoData.forEach(item => {
          item.fromModel = {
            attach:
              item.attachMap && Object.keys(item.attachMap).length > 0
                ? item.attachMap.attachIds
                : [],
            name: item.name,
            value: item.value
          }
        })
      if (moduleTwoData && moduleTwoData.length > 0) {
        this.dataList =
          moduleTwoData &&
          moduleTwoData.map(item => {
            return { fromModel: item.fromModel }
          })
      }
      // 宣传模块4
      this.moduleThreeName = moduleThreeName
      // 推荐企业
      this.tableList = recommendEntList || []
      // 宣传模块5
      this.moduleFourJson = moduleFourJson || []
      // 显示
      this.showObj = {
        moduleOne, // 模块一显示
        moduleTwo, // 模块二显示
        moduleThree, // 模块三显示
        moduleFour // 模块四显示
      }
      this.moduleOneName = moduleOneName
      this.moduleTwoName = moduleTwoName
      this.formTitle = {
        moduleOneName, // 模块一名称
        moduleTwoName // 模块二名称
      }
    },
    // 全部重置
    resettingAll() {
      this.showObj = this.$options.data().showObj
      this.formTitle = this.$options.data().formTitle
      this.moduleOneName = '园区风采' // 模块一名称
      this.moduleTwoName = '配套载体' // 模块二名称
      this.moduleOneData = [] // 数据展示
      this.picList = this.$options.data().picList
      this.dataList = this.$options.data().dataList // 宣传模块2
      this.moduleThreeName = '' // 宣传模块4 - 宣传标题
      this.tableList = [] // 宣传模块4
      this.moduleFourJson = [] // 宣传模块5
      this.fromModel = {} // 视频url
      this.publicUrl = Math.random()
      this.publicImg = Math.random()
      this.publicTwo = Math.random()
    }
  }
}
</script>

<style lang="scss" scoped>
.home-publicity-container {
  .item-title {
    border-left: 4px solid #ed7b2f;
    font-size: 14px;
    line-height: 16px;
    padding-left: 8px;
  }
  .pic-item {
    flex: 1;
    margin-right: 24px;
    &:last-child {
      margin-right: 0;
    }
  }

  .publicity-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .publicity-item {
      width: 32%;
    }
  }
  .recommend-enterprise-tab-item {
    cursor: pointer;
    & + .recommend-enterprise-tab-item {
      margin-left: 32px;
    }
    &.active {
      @include font_color(--color-primary);
      position: relative;
      &:after {
        display: block;
        content: '';
        width: calc(100% - 48px);
        height: 2px;
        @include background_color(--color-primary);
        position: absolute;
        bottom: -6px;
      }
    }
    .svg-icon {
      @include font_color(--color-black);
      &:hover {
        @include font_color(--color-primary);
      }
    }
  }
}

::v-deep {
  .custom-tips {
    display: none;
  }
  .recommend-enterprise {
    .el-form-item__label {
      display: flex;
      align-items: center;
      & > div {
        width: 100%;
      }
    }
    .empty-content {
      padding: 10px 0;
    }
  }
}
</style>
