<template>
  <div class="logic-flow-view">
    <FlowDesigner ref="designer" v-model="flowData" @on-save="handleSave" />
  </div>
</template>

<script>
import FlowDesigner from '../SnakerFlowDesigner/src/snakerflow/index.js'
import { updateModel, getModel } from '../api'
export default {
  name: 'Home',
  data() {
    return {
      flowData: {},
      isIFrame: window.parent !== window
    }
  },
  components: {
    FlowDesigner
  },
  mounted() {
    // 监听消息-第三方使用
    this.initMessageListener()
    this.getModel()
  },
  methods: {
    // 初始化消息监听
    initMessageListener() {
      if (this.isIFrame) {
        window.addEventListener('message', e => {
          if (e.data && ['in', 'out'].includes(e.data.type)) {
            // 收到消息，要回复一下
            if (window.parent) {
              window.parent.postMessage(
                {
                  success: true,
                  type: 'in' // 接收到消息in,传消息out
                },
                '*'
              )
            }
            if (e.data.json) {
              this.$refs.designer.importJson(e.data.json)
            } else if (e.data.xml) {
              this.$refs.designer.importXml(e.data.xml)
            }
          }
        })
      }
    },

    // 获取详情
    getModel() {
      this.$refs.designer.importXml('')
      const { id } = this.$route.query
      getModel(id).then(res => {
        this.$refs.designer.importXml(res.modelXml)
      })
    },

    handleSave(data) {
      this.$confirm('确定保存流程？').then(() => {
        const { id } = this.$route.query
        const params = {
          id,
          modelXml: data.xml
        }
        updateModel(params).then(() => {
          this.$toast.success('保存成功')
          this.$router.go(-1)
        })
      })
    }
  }
}
</script>
<style scoped>
.logic-flow-view {
  height: 100vh;
  position: relative;
}
</style>
