<template>
  <basic-card>
    <div class="app-container">
      <!-- 搜索工作栏 -->
      <el-row :gutter="20">
        <!--部门数据-->
        <el-col :span="4" :xs="24">
          <div class="head-container">
            <el-input
              v-model="deptName"
              placeholder="请输入部门名称"
              clearable
              size="small"
              prefix-icon="el-icon-search"
              style="margin-bottom: 20px"
            />
          </div>
          <div class="head-container">
            <el-tree
              node-key="id"
              :data="deptOptions"
              :props="defaultProps"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              ref="tree"
              default-expand-all
              @node-click="handleNodeClick"
              highlight-current
            />
          </div>
        </el-col>
        <!--用户数据-->
        <el-col :span="20" :xs="24">
          <el-form
            :model="queryParams"
            ref="queryForm"
            size="small"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="用户姓名" prop="nickname">
              <el-input
                v-model="queryParams.nickname"
                placeholder="请输入用户姓名"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="手机号码" prop="mobile">
              <el-input
                v-model="queryParams.mobile"
                placeholder="请输入手机号码"
                clearable
                style="width: 240px"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="用户状态"
                clearable
                style="width: 240px"
              >
                <el-option
                  v-for="dict in statusDictData"
                  :key="parseInt(dict.value)"
                  :label="dict.label"
                  :value="parseInt(dict.value)"
                />
              </el-select>
            </el-form-item>
            <!-- <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </el-form-item> -->
            <el-form-item>
              <el-button type="primary" size="small" @click="handleQuery">
                <svg-icon icon-class="search" />
                <span>搜索</span></el-button
              >
              <el-button @click="resetQuery" size="small">
                <svg-icon icon-class="refresh" />
                <span>重置</span></el-button
              >
            </el-form-item>
          </el-form>

          <!-- <el-row :gutter="10" class="table-button">
            <el-col :span="1.5">
              <el-button
                type="primary"
                size="small"
                @click="handleAdd"
                v-permission="['system:user:create']"
                >新增</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="info"
                size="small"
                @click="handleImport"
                v-permission="['system:user:import']"
                >导入</el-button
              >
            </el-col>
            <el-col :span="1.5">
            <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              :loading="exportLoading"
              v-permission="['system:user:export']"
              >导出</el-button
            >
          </el-col>
          </el-row> -->

          <div class="flex justify-content-end m-b-16">
            <el-button
              type="primary"
              size="small"
              @click="handleAdd"
              v-permission="routeButtonsPermission.ADD"
            >
              <svg-icon icon-class="add" />
              <span>{{ routeButtonsTitle.ADD }}</span>
            </el-button>
            <!-- <el-button
              type="success"
              size="small"
              @click="handleImport"
              v-permission="['system:user:import']"
            >
              <svg-icon icon-class="view-module" /> <span>导入</span></el-button
            > -->
          </div>

          <el-table border v-loading="loading" :data="userList">
            <el-table-column
              label="用户编号"
              align="center"
              key="id"
              prop="id"
              v-if="columns[0].visible"
            />
            <el-table-column
              label="登录名称"
              align="center"
              key="username"
              prop="username"
              v-if="columns[1].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="用户姓名"
              align="center"
              key="nickname"
              prop="nickname"
              v-if="columns[2].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="部门"
              align="center"
              key="deptName"
              prop="dept.name"
              v-if="columns[3].visible"
              :show-overflow-tooltip="true"
            />
            <el-table-column
              label="手机号码"
              align="center"
              key="mobile"
              prop="mobile"
              v-if="columns[4].visible"
              width="120"
            />
            <el-table-column
              label="状态"
              key="status"
              v-if="columns[5].visible"
              align="center"
            >
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.status"
                  :active-value="0"
                  :inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              v-if="columns[6].visible"
              width="160"
            >
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="公司名称"
              align="center"
              key="enterpriseName"
              prop="enterpriseName"
              v-if="columns[7].visible"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span>{{ scope.row.enterpriseName || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="120"
              class-name="small-padding fixed-width"
            >
              <template slot-scope="scope">
                <el-link
                  size="mini"
                  type="success"
                  class="m-r-10"
                  @click="handleUpdate(scope.row)"
                  v-permission="routeButtonsPermission.REVISE"
                  >{{ routeButtonsTitle.REVISE }}
                </el-link>
                <el-dropdown
                  @command="
                    command => handleCommand(command, scope.$index, scope.row)
                  "
                >
                  <span class="el-dropdown-link">
                    <el-link
                      type="primary"
                      v-permission="[
                        ...routeButtonsPermission.DELETE,
                        ...routeButtonsPermission.RESET_PASSWORD,
                        ...routeButtonsPermission.ASSIGN_ROLES
                      ]"
                      >更多 ></el-link
                    >
                  </span>
                  <el-dropdown-menu slot="dropdown" class="p-dropdown-wrapper">
                    <template
                      v-if="
                        scope.row.id !== 1 && scope.row.systemUserType !== '02'
                      "
                    >
                      <el-dropdown-item
                        command="handleDelete"
                        size="mini"
                        type="text"
                        icon="el-icon-delete"
                        v-permission="routeButtonsPermission.DELETE"
                      >
                        <span>{{ routeButtonsTitle.DELETE }}</span>
                      </el-dropdown-item>
                    </template>
                    <el-dropdown-item
                      command="handleResetPwd"
                      size="mini"
                      type="text"
                      icon="el-icon-key"
                      v-permission="routeButtonsPermission.RESET_PASSWORD"
                      >{{ routeButtonsTitle.RESET_PASSWORD }}
                    </el-dropdown-item>
                    <el-dropdown-item
                      command="handleRole"
                      size="mini"
                      type="text"
                      icon="el-icon-circle-check"
                      v-permission="routeButtonsPermission.ASSIGN_ROLES"
                      >{{ routeButtonsTitle.ASSIGN_ROLES }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNo"
            :limit.sync="queryParams.pageSize"
            @pagination="getList"
          />
        </el-col>
      </el-row>

      <!-- 添加或修改参数配置对话框 -->
      <el-dialog
        :title="title"
        :visible.sync="open"
        width="700px"
        append-to-body
      >
        <el-form
          v-if="open"
          ref="form"
          class="el-form-wrapper"
          :model="form"
          :rules="rules"
          label-width="80px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户姓名" prop="nickname">
                <el-input
                  v-if="form.id !== undefined"
                  v-model.trim="form.nickname"
                  placeholder="请输入用户姓名"
                />
                <el-select
                  v-else
                  remote
                  clearable
                  reserve-keyword
                  :remote-method="remoteMethod"
                  @change="nickNameChange"
                  :loading="nickNameLoading"
                  v-model.trim="form.nickname"
                  filterable
                  placeholder="请输入用户姓名"
                >
                  <el-option
                    v-for="item in nickNameOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span class="float-left">{{ item.label }}</span>
                    <span class="float-right">{{
                      filterText(item.value)
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="归属部门" prop="deptId">
                <treeselect
                  :key="key"
                  v-model="form.deptId"
                  :options="deptSelectOptions"
                  :show-count="true"
                  :clearable="false"
                  placeholder="请选择归属部门"
                  :normalizer="normalizer"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="手机号码" prop="mobile">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入手机号码"
                  maxlength="11"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="form.email"
                  placeholder="请输入邮箱"
                  maxlength="50"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item
                v-if="form.id === undefined"
                label="登录名称"
                prop="username"
              >
                <el-input
                  v-model.trim="form.username"
                  placeholder="请输入登录名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                v-if="form.id === undefined"
                label="用户密码"
                prop="password"
              >
                <el-input
                  v-model="form.password"
                  placeholder="请输入用户密码"
                  type="password"
                  show-password
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="用户性别">
                <el-select v-model="form.sex" placeholder="请选择">
                  <el-option
                    v-for="dict in sexDictData"
                    :key="parseInt(dict.value)"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" hidden>
              <el-form-item label="岗位">
                <el-select v-model="form.postIds" multiple placeholder="请选择">
                  <el-option
                    v-for="item in postOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="职级">
                <el-select v-model="form.postIds" multiple placeholder="请选择">
                  <el-option
                    v-for="item in listDeptOption"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label="备注">
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  placeholder="请输入内容"
                  :maxlength="200"
                  show-word-limit
                  :rows="7"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 用户导入对话框 -->
      <el-dialog
        :title="upload.title"
        :visible.sync="upload.open"
        width="400px"
        append-to-body
      >
        <el-upload
          ref="upload"
          :limit="1"
          accept=".xlsx, .xls"
          :headers="upload.headers"
          :action="upload.url + '?updateSupport=' + upload.updateSupport"
          :disabled="upload.isUploading"
          :on-progress="handleFileUploadProgress"
          :on-success="handleFileSuccess"
          :auto-upload="false"
          drag
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div class="el-upload__tip text-center" slot="tip">
            <div class="el-upload__tip" slot="tip">
              <el-checkbox v-model="upload.updateSupport" />
              是否更新已经存在的用户数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板
            </el-link>
          </div>
        </el-upload>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </el-dialog>

      <!-- 分配角色 -->
      <el-dialog
        title="分配角色"
        :visible.sync="openRole"
        width="500px"
        append-to-body
      >
        <el-form class="el-form-wrapper" :model="form" label-width="80px">
          <el-form-item label="登录名称">
            <el-input v-model="form.username" :disabled="true" />
          </el-form-item>
          <el-form-item label="用户姓名">
            <el-input v-model="form.nickname" :disabled="true" />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="form.roleIds" multiple placeholder="请选择">
              <el-option
                v-for="item in roleOptions"
                :key="parseInt(item.id)"
                :label="item.name"
                :value="parseInt(item.id)"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitRole">确 定</el-button>
          <el-button @click="cancelRole">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </basic-card>
</template>

<script>
import {
  addUser,
  changeUserStatus,
  delUser,
  exportUser,
  getUser,
  importTemplate,
  listUser,
  resetUserPwd,
  updateUser
} from '../api/user'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

import { listSimpleDepts, listDept } from '../api/dept'
import { listSimplePosts } from '../api/post'

import { CommonStatusEnum } from '../utils/constants'
import { assignUserRole, listUserRoles } from '../api/permission'
import { listSimpleRoles } from '../api/role'
import { getBaseHeader } from '@/utils/request'
import { addDateRange, handleTree, parseTime, resetForm } from '../utils/tools'
// import { getConfigKey } from '../api/config'
import Pagination from '../components/Pagination'
import downloads from '@/utils/download'
import { getByDictType } from '@/api/common'
import { Password_Regexp } from '@/utils/validate'
import { pinyin } from 'pinyin-pro'
import dayjs from 'dayjs'
export default {
  name: 'WorkspaceUser',
  components: { Treeselect, Pagination },
  data() {
    return {
      nickNameLoading: false,
      nickNameOptions: [], // 用户姓名下拉
      // 遮罩层
      loading: true,
      // 导出遮罩层
      exportLoading: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: '',
      // 部门树选项
      deptOptions: [],
      // 添加用户归属部门
      deptSelectOptions: [],
      // 是否显示弹出层
      open: false,
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 状态数据字典
      statusOptions: [],
      // 性别状态字典
      sexOptions: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {
        username: '',
        nickname: '',
        deptId: ''
      },
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: getBaseHeader(),
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/admin-api/system/user/import'
      },
      // 查询参数
      queryParams: {
        pageNo: 1,
        pageSize: 10,
        nickname: undefined,
        mobile: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `用户编号`, visible: true },
        { key: 1, label: `登录名称`, visible: true },
        { key: 2, label: `用户姓名`, visible: true },
        { key: 3, label: `部门`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true },
        { key: 7, label: `公司名称`, visible: true }
      ],
      key: 0,
      // 表单校验
      rules: {
        username: [
          {
            required: true,
            message: '登录名称不能为空',
            trigger: ['blur', 'change']
          }
        ],
        nickname: [
          {
            required: true,
            message: '用户姓名不能为空',
            trigger: ['blur', 'change']
          }
        ],
        deptId: [
          {
            required: true,
            message: '请选择归属部门',
            trigger: ['blur', 'change']
          }
        ],
        password: [
          { required: true, message: '用户密码不能为空', trigger: 'blur' },
          {
            pattern: Password_Regexp,
            message: '请输入由数字、字母、~!@#$%^&+*组成的8～16位密码',
            trigger: 'blur'
          }
        ],
        email: [
          {
            type: 'email',
            message: "'请输入正确的邮箱地址",
            trigger: ['blur', 'change']
          }
        ],
        mobile: [
          {
            required: true,
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: '请输入正确的手机号码',
            trigger: 'blur'
          }
        ]
      },
      // 是否显示弹出层（角色权限）
      openRole: false,
      addDateRange,
      parseTime,
      resetForm,
      // 枚举
      SysCommonStatusEnum: CommonStatusEnum,
      // 数据字典
      statusDictData: this.getByDictType('common_status', 'statusDictData'),
      sexDictData: this.getByDictType('system_user_sex', 'sexDictData'),
      listDeptOption: []
    }
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val)
    },
    open(val) {
      if (!val) {
        this.nickNameOptions = []
      }
    },
    'form.deptId'(val) {
      if (val) {
        this.$refs.form.validateField('deptId')
      }
    }
  },
  created() {
    this.getTreeselect(true)
  },
  activated() {
    this.getListDept()
    this.getList()
    if (this.executeActivated) {
      this.getTreeselect(false)
    }
    // getConfigKey('sys.user.init-password').then(response => {
    //   this.initPassword = response.msg
    // })
  },
  methods: {
    nickNameChange(val) {
      if (val) {
        if (!this.form.id) {
          this.form.username = this.filterText(this.form.nickname)
        }
        this.partition()
      } else {
        this.form.username = ''
      }
    },
    partition() {
      let str = this.form.nickname
      let index = str.indexOf('_')
      this.form.nickname = index !== -1 ? str.substring(index + 1) : str
    },
    remoteMethod(val) {
      if (val !== '') {
        this.generationData(val)
      } else {
        this.nickNameOptions = []
      }
    },
    generationData(val) {
      this.nickNameLoading = true
      const data = [
        {
          value: 'za_',
          label: 'za_'
        },
        {
          value: 'staff_',
          label: 'staff_'
        }
      ]
      this.nickNameOptions = data.map(item => {
        return {
          value: item.value + val,
          label: val
        }
      })
      this.nickNameLoading = false
    },
    //过滤文字
    filterText(str) {
      return pinyin(str, { toneType: 'none', type: 'array' }).join('')
    },
    async getListDept() {
      const res = await listDept()
      this.listDeptOption = res.deptRank
    },
    // 获取性别字典
    getByDictType(type, key) {
      getByDictType(type).then(response => {
        this[key] = response
      })
    },

    // 更多操作
    handleCommand(command, index, row) {
      switch (command) {
        case 'handleUpdate':
          this.handleUpdate(row) //修改客户信息
          break
        case 'handleDelete':
          this.handleDelete(row) //红号变更
          break
        case 'handleResetPwd':
          this.handleResetPwd(row)
          break
        case 'handleRole':
          this.handleRole(row)
          break
        default:
          break
      }
    },
    /** 查询用户列表 */
    getList() {
      this.loading = true
      listUser(
        this.addDateRange(this.queryParams, [
          this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
          this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
        ])
      ).then(response => {
        this.userList = response.list
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询部门下拉树结构 + 岗位下拉 */
    getTreeselect(first = false) {
      listSimpleDepts().then(response => {
        // 处理 deptOptions 参数
        this.deptOptions = []
        const obj = {
          id: '',
          name: '全部',
          parentId: 0
        }
        this.deptOptions.push(...handleTree([obj, ...response], 'id'))
        this.deptSelectOptions.push(...handleTree(response, 'id'))
        this.$nextTick(() => {
          if (first) {
            this.$refs.tree.setCurrentKey('')
          } else {
            this.$refs.tree.setCurrentKey(this.queryParams.deptId)
          }
        })
      })
      listSimplePosts().then(response => {
        // 处理 postOptions 参数
        this.postOptions = []
        this.postOptions.push(...response)
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.name.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.deptId = data.id
      this.queryParams.pageNo = 1
      this.getList()
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.status === CommonStatusEnum.ENABLE ? '启用' : '停用'
      this.$confirm('确认要' + text + '"' + row.username + '"用户吗?')
        .then(() => {
          return changeUserStatus(row.id, row.status)
        })
        .then(() => {
          this.$toast.success(text + '成功')
        })
        .catch(() => {
          row.status =
            row.status === CommonStatusEnum.ENABLE
              ? CommonStatusEnum.DISABLE
              : CommonStatusEnum.ENABLE
        })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 取消按钮（角色权限）
    cancelRole() {
      this.openRole = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: undefined,
        deptId: undefined,
        username: undefined,
        nickname: undefined,
        password: undefined,
        mobile: undefined,
        email: undefined,
        sex: undefined,
        status: '0',
        remark: undefined,
        postIds: [],
        roleIds: []
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNo = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = []
      this.resetForm('queryForm')
      this.handleQuery()
      this.deptName = undefined
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      // 获得下拉数据
      // this.getTreeselect()
      // 打开表单，并设置初始化
      this.open = true
      this.title = '添加用户'
      this.form.password = this.initPassword
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      // this.getTreeselect()
      const id = row.id
      getUser(id).then(response => {
        this.form = response
        this.open = true
        this.title = '修改用户'
        this.form.password = ''
        response.sex ? (this.form.sex = response.sex) : (this.form.sex = '')
      })
    },
    /** 重置密码按钮操作 */
    handleResetPwd(row) {
      this.$prompt('请输入"' + row.username + '"的新密码', '提示', {
        confirmButtonText: '确定',
        inputPattern: Password_Regexp,
        inputErrorMessage: '请输入由数字、字母组成的8～16位密码',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        resetUserPwd(row.id, value).then(() => {
          this.$toast.success('修改成功，新密码是：' + value)
        })
      })
    },
    /** 分配用户角色操作 */
    handleRole(row) {
      this.reset()
      const id = row.id
      // 处理了 form 的用户 username 和 nickname 的展示
      this.form.id = id
      this.form.username = row.username
      this.form.nickname = row.nickname
      // 打开弹窗
      this.openRole = true
      // 获得角色列表
      listSimpleRoles().then(response => {
        // 处理 roleOptions 参数
        this.roleOptions = []
        this.roleOptions.push(...response)
      })
      // 获得角色拥有的菜单集合
      listUserRoles(id).then(response => {
        // 设置选中
        this.form.roleIds = response
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.form.id !== undefined) {
            updateUser(this.form).then(() => {
              this.$toast.success('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addUser(this.form).then(() => {
              this.$toast.success('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 提交按钮（角色权限） */
    submitRole() {
      if (this.form.id !== undefined) {
        assignUserRole({
          userId: this.form.id,
          roleIds: this.form.roleIds
        }).then(() => {
          this.$toast.success('分配角色成功')
          this.openRole = false
          this.getList()
        })
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$confirm('是否确认删除用户编号为"' + ids + '"的数据项?')
        .then(() => {
          return delUser(ids)
        })
        .then(() => {
          this.getList()
          this.$toast.success('删除成功')
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.addDateRange(this.queryParams, [
        this.dateRange[0] ? this.dateRange[0] + ' 00:00:00' : undefined,
        this.dateRange[1] ? this.dateRange[1] + ' 23:59:59' : undefined
      ])
      this.$confirm('是否确认导出所有用户数据项?')
        .then(() => {
          this.exportLoading = true
          return exportUser(queryParams)
        })
        .then(response => {
          downloads.excel(response, dayjs().format('YYYY-MM-DD') + '用户数据.xls')
          this.exportLoading = false
        })
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = '用户导入'
      this.upload.open = true
    },
    /** 下载模板操作 */
    importTemplate() {
      importTemplate().then(response => {
        downloads.excel(response, dayjs().format('YYYY-MM-DD') + '用户导入模板.xls')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress() {
      this.upload.isUploading = true
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      if (response.code !== 0) {
        this.$toast.success(response.msg)
        return
      }
      this.upload.open = false
      this.upload.isUploading = false
      this.$refs.upload.clearFiles()
      // 拼接提示语
      let data = response.data
      let text = '创建成功数量：' + data.createUsernames.length
      for (const username of data.createUsernames) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + username
      }
      text += '<br />更新成功数量：' + data.updateUsernames.length
      for (const username of data.updateUsernames) {
        text += '<br />&nbsp;&nbsp;&nbsp;&nbsp;' + username
      }
      text += '<br />更新失败数量：' + Object.keys(data.failureUsernames).length
      for (const username in data.failureUsernames) {
        text +=
          '<br />&nbsp;&nbsp;&nbsp;&nbsp;' +
          username +
          '：' +
          data.failureUsernames[username]
      }
      this.$alert(text, '导入结果', { dangerouslyUseHTMLString: true })
      this.getList()
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit()
    },
    // 格式化部门的下拉框
    normalizer(node) {
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.float-left {
  float: left;
}

.float-right {
  float: right;
  color: #8492a6;
  font-size: 13px;
}

.el-form-wrapper {
  width: 100%;
  padding: 20px 20px 0;
}

.el-select {
  width: 100%;
}

:deep(.vue-treeselect__menu) {
  max-height: 150px !important;
}

:deep(.el-tree) {
  .el-tree-node::after {
    height: 0;
  }
}
</style>
