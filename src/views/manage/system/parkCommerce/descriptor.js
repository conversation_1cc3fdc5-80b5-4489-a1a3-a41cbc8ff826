const validatePhone = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(`请输入联系方式`))
  } else {
    if (/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(+value)) {
      callback()
    } else {
      callback(new Error(`请输入正确的联系方式`))
    }
  }
}
export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          coverAttachIds: {
            form: 'component',
            label: '封面',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传封面',
                trigger: ['change', 'blur']
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <span class="color-info font-size-14">
                  建议上传388*144大小的图片
                </span>
              )
            },
            props: {
              uploadData: {
                type: 'coursePic'
              },
              accept: 'image/*'
            }
          },
          title: {
            form: 'input',
            label: '标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入标题'
              },
              {
                max: 60,
                message: '字符长度不能超过60个字符'
              }
            ]
          },
          parkId: {
            form: 'select',
            label: '园区',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择园区'
              }
            ],
            options: []
          },
          contact: {
            form: 'input',
            label: '联系人',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人'
              },
              {
                max: 20,
                message: '字符长度不能超过20个字符'
              }
            ]
          },
          phone: {
            form: 'input',
            label: '联系方式',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                validator: validatePhone
              }
            ]
          },
          publishDate: {
            form: 'date',
            label: '发布时间',
            rule: {
              required: true,
              message: '请选择发布时间',
              type: 'string'
            },
            props: {
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          },
          commerceType: {
            form: 'select',
            label: '商业类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择商业类型'
              }
            ],
            options: []
          },
          address: {
            form: 'input',
            label: '地址',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入地址'
              },
              {
                max: 60,
                message: '字符长度不能超过60个字符'
              }
            ]
          },
          weekdayTime: {
            form: 'input',
            label: '工作日营业时间',
            rule: [
              {
                type: 'string',
                message: '请输入工作日营业时间'
              }
            ],
            attrs: {
              max: 30
            }
          },
          saturdayTime: {
            form: 'input',
            label: '周六营业时间',
            rule: [
              {
                type: 'string',
                message: '请输入营业周六时间'
              }
            ],
            attrs: {
              max: 30
            }
          },
          weekendTime: {
            form: 'input',
            label: '周末营业时间',
            rule: [
              {
                type: 'string',
                message: '请输入营业周末时间'
              }
            ],
            attrs: {
              max: 30
            }
          },
          text: {
            form: 'component',
            label: '发布内容',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入发布内容'
              }
            ],
            componentName: 'Tinymce'
          }
        }
      }
    }
  }
}
