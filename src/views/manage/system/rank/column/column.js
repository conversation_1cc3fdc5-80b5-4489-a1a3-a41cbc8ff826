export default {
  data() {
    return {
      tableColumn: [
        {
          label: '职级ID',
          prop: 'id'
        },
        {
          label: '职级名称',
          prop: 'name'
        },
        {
          label: '职级顺序',
          prop: 'sort'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '操作',
          prop: 'operation',
          hidden: true,
          render: (h, scope) => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    class="m-r-4"
                    onclick={() => {
                      this.addRank = true
                      this.parentId = scope.row.id
                    }}
                  >
                    编辑
                  </el-button>
                }
                <span>|</span>
                {
                  <el-button
                    type="text"
                    class="m-r-4 m-l-4"
                    onclick={() => {
                      this.addRank = true
                      this.parentId = scope.row.id
                      this.add = 1
                    }}
                  >
                    添加下级
                  </el-button>
                }
                <span>|</span>
                {
                  <el-button
                    type="text"
                    class="m-l-4"
                    onclick={() => {
                      this.delHandler(scope.row)
                    }}
                  >
                    删除
                  </el-button>
                }
              </div>
            )
          }
        }
      ]
    }
  }
}
