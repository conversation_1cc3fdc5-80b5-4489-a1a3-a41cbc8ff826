export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'logo',
          label: '企业logo',
          render: (h, scope) => {
            const logoAttach = scope.row.logoAttach || {}
            const party = logoAttach.party || []
            const logo = party.length ? party[0].path : ''
            return (
              <div style={'width: 170px;height:68px'}>
                <el-image class={'wh100'} fit={'cover'} src={logo}>
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            )
          }
        },
        {
          prop: 'name',
          label: '支部名称',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'entName',
          label: '关联企业'
        },
        {
          prop: 'estDate',
          label: '支部成立时间'
        },
        {
          prop: 'contact',
          label: '联系电话'
        },
        {
          prop: 'status',
          label: '状态',
          search: {
            type: 'select',
            options: [
              { label: '显示', value: 1 },
              { label: '不显示', value: 0 }
            ]
          },
          render: (h, scope) => {
            return <div>{
              scope.row.status ? <basic-tag isDot type="success" label="显示" /> :
                <basic-tag isDot type="warning" label="不显示" />
            }</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.VIEW}
                  type={'primary'}
                  onClick={() => this.previewEvent(scope.row)}
                >{this.routeButtonsTitle.VIEW}</el-link>
                <el-dropdown onCommand={(e) => this.commandHandle(e, scope.row)}>
                  <el-link
                    type="primary"
                    class={'m-l-8'}
                    v-permission={[
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >更多</el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command={'edit'} v-permission={this.routeButtonsPermission.EDIT}>
                      <el-link type="success">{this.routeButtonsTitle.EDIT}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item command={'publish'} v-permission={this.routeButtonsPermission.EDIT}>
                      {
                        scope.row.status ? <el-link type="primary">不显示</el-link> :
                          <el-link type="primary">显示</el-link>
                      }
                    </el-dropdown-item>
                    <el-dropdown-item command={'delete'} v-permission={this.routeButtonsPermission.DELETE}>
                      <el-link type="danger">{this.routeButtonsTitle.DELETE}</el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ]
    }
  }
}
