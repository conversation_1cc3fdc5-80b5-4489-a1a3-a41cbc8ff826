<template>
  <div class="preview-wrapper pos-relative">
    <div class="position-tag pos-absolute line-height-24 m-b-15">
      <el-tag :type="info.status ? 'success' : 'warning'">{{
        info.status ? '显示' : '不显示'
      }}</el-tag>
    </div>
    <div class="preview-left p-t-24">
      <div
          class="font-size-16 color-black font-strong line-height-24 preview-title"
      >
        {{ info.name }}
      </div>
      <div
          class="m-b-16 m-t-10 font-size-12 flex align-items-center justify-content-center color-text-regular"
      >
        <div>成立时间：{{ info.estDate | noData }}</div>
        <div class="m-l-16">联系电话：{{ info.contact | noData }}</div>
        <div class="m-l-16">关联企业：{{ info.entName | noData }}</div>
      </div>
      <div class="m-b-16 font-size-16">党员概况</div>
      <div
          class="rich-text color-black font-size-14 line-height-22 overflow-hidden"
      >{{ info.personView | noData }}</div>
      <div class="m-t-32 font-size-16">支部概况</div>
      <div
          v-html="$options.filters.richTextFilter(info.branchView)"
          class="rich-text color-black font-size-14 line-height-22 overflow-hidden"
      ></div>
    </div>
  </div>
</template>

<script>
import { richTextFilter } from '@/filter'

export default {
  name: 'BranchPreview',
  props: {
    info: {
      type: Object,
      default: () => ({})
    }
  },
  filters: { richTextFilter }
}
</script>

<style lang="scss" scoped>
.preview-wrapper {
  .preview-left {
    overflow: hidden;
    .preview-title {
      text-align: center;
    }

    .primary-tag {
      border-radius: 12px;
    }
  }

  .position-tag {
    left: 0;
    top: 0;
  }

  :deep(.el-tag--warning) {
    border-radius: 0 100px 100px 0;
  }
}
</style>
