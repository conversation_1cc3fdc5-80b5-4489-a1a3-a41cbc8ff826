<template>
  <div class="flex min-wh100" ref="entProfileContainer">
    <el-row class="w100">
      <el-col :span="16">
        <basic-card title="党委概况">
          <tinymce ref="tinymce" v-if="height" v-model="content" :height="height" />
          <div class="flex justify-content-end p-t-16">
            <el-button type="info" @click="previewHandle">预览</el-button>
            <el-button type="primary" @click="saveHandle">保存</el-button>
          </div>
        </basic-card>
      </el-col>
      <el-col :span="8" class="p-l-16">
        <basic-card title="内容预览">
          <div class="mobile-preview" :style="{height: height + 46 + 'px'}">
            <el-scrollbar :style="{height: height + 46 - 96 + 'px'}">
              <div class="p-l-16 p-r-16">
                <div class="mobile-preview-header">
                  中安创谷科技园党委概况
                </div>
                <div v-html="previewContent"></div>
              </div>
            </el-scrollbar>
          </div>
        </basic-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { cmsContentCreate, cmsContentDetail } from './api'

export default {
  name: "EntProfile",
  data() {
    return {
      content: '',
      height: 0,
      previewContent: '',
      categoryId: 135
    }
  },
  mounted() {
    this.heightCalc()
    this.cmsContentDetail()
  },
  methods: {
    saveHandle() {
      this.previewHandle()
      const params = {
        categoryId: this.categoryId,
        text: this.previewContent
      }
      cmsContentCreate(params).then(() => {
        this.$toast.success('保存成功')
      })
    },
    cmsContentDetail() {
      cmsContentDetail(this.categoryId).then(res => {
        this.$nextTick(() => {
          this.content = res.text || ''
          this.previewContent = res.text || ''
          setTimeout(() => {
            this.$refs.tinymce.setCharacterCount()
          }, 300)
        })
      })
    },
    heightCalc() {
      const height = this.$refs.entProfileContainer.offsetHeight
      this.height = height - 60 - 46 - 24
    },
    previewHandle() {
      const rel = /style\s*?=\s*?([‘"])[\s\S]*?\1/
      this.previewContent = this.content.replace(
          rel, ''
      ).replace(
          /<div/g, `<div style='color: rgba(0,0,0,0.8) !important;font-size: 14px !important;font-weight: 400 !important;line-height: 20px; !important;text-align: justify !important;background: transparent !important;'`
      ).replace(
          /<p/g, `<p style='color: rgba(0,0,0,0.8) !important;font-size: 14px !important;font-weight: 400 !important;line-height: 20px; !important;text-align: justify !important;background: transparent !important;'`
      ).replace(
          /<img/g, `<img style='width: 100% !important;'`
      )
    }
  }
}
</script>

<style scoped lang="scss">
.mobile-preview {
  max-width: 375px;
  height: 100%;
  background: url("./images/bg.png");
  background-size: 100% 100%;
  margin: 0 auto;
  padding-top: 96px;
  .mobile-preview-header {
    height: 80px;
    background: url("./images/header-bg.png");
    background-size: 100% 100%;
    font-weight: 600;
    font-size: 20px;
    color: #CD2134;
    line-height: 80px;
    text-align: center;
  }
}
::v-deep {
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
      margin-bottom: 0 !important;
    }
    .is-horizontal {
      display: none;
    }
  }
}
</style>
