<template>
  <basic-card>
    <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getActivityList"
    >
      <template v-slot:operate-right>
        <el-button
            v-permission="routeButtonsPermission.ADD"
            type="primary"
            size="small"
            @click="drawerVisible = true"
        >
          <span>{{ routeButtonsTitle.ADD }}</span>
        </el-button>
      </template>
    </drive-table>
    <!-- 新增编辑弹框 -->
    <basic-drawer
        :title="drawerTitle"
        :visible.sync="drawerVisible"
        @confirmDrawer="confirmDrawer"
        :haveOperation="false"
    >
      <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
      />
      <template slot="footer">
        <el-button type="info" @click="drawerVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDrawer">确定</el-button>
      </template>
    </basic-drawer>
    <!-- 详情 -->
    <dialog-cmp
        title="活动详情"
        :visible.sync="dialogVisible"
        width="920px"
        :haveOperation="false"
    >
      <div>
        <group-activity-preview :info="detailInfo" />
      </div>
    </dialog-cmp>
  </basic-card>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
import {
  getActivityList,
  deleteActivity,
  partyActivityCreate,
  partyActivityUpdate,
  getPartyActivityDetail,
  activityPublish
} from './api'
import GroupActivityPreview from './preview'

export default {
  name: "GroupActivity",
  components: { GroupActivityPreview },
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      getActivityList,
      drawerVisible: false,
      fromModel: {
        status: 1,
        detailType: 1
      },
      drawerTitle: '新增',
      detailInfo: {},
      dialogVisible: false
    }
  },
  watch: {
    'fromModel.detailType'(val) {
      this.formConfigure.descriptors.content.hidden = val === 2
      this.formConfigure.descriptors.officialUrl.hidden = val === 1
    },
    drawerVisible(val) {
      if(!val) {
        this.drawerTitle = this.$options.data().drawerTitle
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  activated() {
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    previewEvent(row) {
      getPartyActivityDetail({ id: row.id }).then(res => {
        this.detailInfo = res || {}
        this.dialogVisible = true
      })
    },
    async editHandle(row) {
      try {
        const res = await getPartyActivityDetail({ id: row.id })
        for(const key in this.formConfigure.descriptors) {
          this.$set(this.fromModel, key, res[key])
        }
        this.$set(this.fromModel, 'detailType', res.detailType || 1)
        const coverAttach = res.coverAttach || {}
        const coverParty = coverAttach.party || []
        this.$set(this.fromModel, 'coverIds', coverParty)
        this.fromModel.id = res.id
        this.drawerTitle = '编辑'
        this.drawerVisible = true
      } catch(e) {
        console.error(e)
      }
    },
    publishHandle(row) {
      const { id, status } = row
      const tips = status ? '不显示' : '显示'
      this.$confirm(`确定${tips}该活动？`).then(() => {
        const params = {
          id,
          status: status ? 0 : 1
        }
        activityPublish(params).then(() => {
          this.operationSuccess(tips)
        })
      })
    },
    deleteHandle(row) {
      this.$confirm('确定删除该活动？').then(() => {
        const { id } = row
        deleteActivity(id).then(() => {
          this.operationSuccess('删除')
        })
      })
    },
    commandHandle(e, row) {
      switch(e) {
        case 'edit':
          this.editHandle(row)
          break
        case 'publish':
          this.publishHandle(row)
          break
        case 'delete':
          this.deleteHandle(row)
          break
        default:
          break
      }
    },
    // 提示信息
    operationSuccess(tips) {
      this.$toast.success(tips + '活动成功')
      this.$refs['drive-table'].refreshTable()
      this.drawerVisible = false
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if(!valid) return false
        const { id, coverIds } = this.fromModel
        const params = {
          ...this.fromModel,
          coverIds: coverIds && coverIds.length ? coverIds.map(item => item.id) : []
        }
        if(id) {
          partyActivityUpdate(params).then(() => {
            this.operationSuccess('编辑')
          })
        } else {
          partyActivityCreate(params).then(() => {
            this.operationSuccess('新增')
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
