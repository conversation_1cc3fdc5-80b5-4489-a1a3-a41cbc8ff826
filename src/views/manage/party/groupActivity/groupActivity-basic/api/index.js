import request from '@/utils/request'

// 获取列表分页
export function getActivityList(params) {
  return request({
    url: `/party/activity/page`,
    method: 'get',
    params
  })
}

//删除
export function deleteActivity(id) {
  return request({
    url: `/party/activity/delete?id=${id}`,
    method: 'get'
  })
}
// 发布
export function activityPublish(params) {
  return request({
    url: `/party/activity/release`,
    method: 'get',
    params
  })
}
// 新增
export function partyActivityCreate(data) {
  return request({
    url: `/party/activity/create`,
    method: 'post',
    data
  })
}
// 更新
export function partyActivityUpdate(data) {
  return request({
    url: `/party/activity/update`,
    method: 'post',
    data
  })
}
// 获取详情
export function getPartyActivityDetail(params) {
  return request({
    url: `/party/activity/get`,
    method: 'get',
    params
  })
}
