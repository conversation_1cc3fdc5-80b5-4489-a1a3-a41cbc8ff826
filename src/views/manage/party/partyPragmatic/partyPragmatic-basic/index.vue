<template>
  <basic-card>
    <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :isSortable="isSortable"
        :api-fn="getPracticePage"
        :sort-fn="getPracticeSort"
    >
      <template v-slot:operate-right>
        <el-button
            v-permission="routeButtonsPermission.SORT"
            type="info"
            size="small"
            @click="sortableHandle(!isSortable)"
            :disabled="dragTable.length === 0"
        >
          <span>
            {{ isSortable ? `退出排序` : `排序管理` }}
          </span>
        </el-button>
        <el-button
            v-permission="routeButtonsPermission.ADD"
            type="primary"
            size="small"
            @click="drawerVisible = true"
            :disabled="isSortable"
        >
          <span>{{ routeButtonsTitle.ADD }}</span>
        </el-button>
      </template>
    </drive-table>
    <!-- 新增编辑弹框 -->
    <basic-drawer
        :title="drawerTitle"
        :visible.sync="drawerVisible"
        @confirmDrawer="confirmDrawer"
        :haveOperation="false"
    >
      <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
      />
      <template slot="footer">
        <el-button type="info" @click="drawerVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmDrawer">确定</el-button>
      </template>
    </basic-drawer>
    <!-- 详情 -->
    <dialog-cmp
        title="实务详情"
        :visible.sync="dialogVisible"
        width="920px"
        :haveOperation="false"
    >
      <div>
        <important-news-preview :info="detailInfo" />
      </div>
    </dialog-cmp>
  </basic-card>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
import {
  getPracticePage,
  getPracticeAll,
  getPracticeSort,
  deletePractice,
  partyPracticeCreate,
  partyPracticeUpdate,
  getPartyPracticeDetail, practicePublish
} from './api'
import ImportantNewsPreview from './preview'

export default {
  name: "PartyPragmatic",
  components: { ImportantNewsPreview },
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      getPracticePage,
      getPracticeSort,
      drawerVisible: false,
      fromModel: {
        status: 1
      },
      drawerTitle: '新增',
      isSortable: false,
      listLength: 0, // 列表长度
      dragTable: [], // 拖拽排序数据
      detailInfo: {},
      dialogVisible: false
    }
  },
  watch: {
    drawerVisible(val) {
      if(!val) {
        this.drawerTitle = this.$options.data().drawerTitle
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  activated() {
    this.getPracticeAll()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  deactivated() {
    this.isSortable = false
  },
  methods: {
    previewEvent(row) {
      getPartyPracticeDetail({ id: row.id }).then(res => {
        this.detailInfo = res || {}
        this.dialogVisible = true
      })
    },
    async editHandle(row) {
      try {
        const res = await getPartyPracticeDetail({ id: row.id })
        for(const key in this.formConfigure.descriptors) {
          this.$set(this.fromModel, key, res[key])
        }
        const coverAttach = res.coverAttach || {}
        const coverParty = coverAttach.party || []
        this.$set(this.fromModel, 'coverIds', coverParty)
        const attaches = res.attaches || {}
        const attachesParty = attaches.party || []
        this.$set(this.fromModel, 'attaches', attachesParty)
        this.fromModel.id = res.id
        this.drawerTitle = '编辑'
        this.drawerVisible = true
      } catch(e) {
        console.error(e)
      }
    },
    publishHandle(row) {
      const { id, status } = row
      const tips = status ? '不显示' : '显示'
      this.$confirm(`确定${tips}该实务？`).then(() => {
        const params = {
          id,
          status: status ? 0 : 1
        }
        practicePublish(params).then(() => {
          this.operationSuccess(tips)
        })
      })
    },
    deleteHandle(row) {
      this.$confirm('确定删除该实务？').then(() => {
        const { id } = row
        deletePractice(id).then(() => {
          this.operationSuccess('删除')
        })
      })
    },
    commandHandle(e, row) {
      switch(e) {
        case 'edit':
          this.editHandle(row)
          break
        case 'publish':
          this.publishHandle(row)
          break
        case 'delete':
          this.deleteHandle(row)
          break
        default:
          break
      }
    },
    getPracticeAll() {
      getPracticeAll().then(res => {
        this.dragTable = res || []
      })
    },
    // 拖拽排序开启关闭
    sortableHandle(val) {
      this.isSortable = val
      this.listLength = this.dragTable.length
      if (val)
        this.$refs['drive-table'] &&
        this.$refs['drive-table'].initDrag(this.dragTable)
    },
    // 提示信息
    operationSuccess(tips) {
      this.$toast.success(tips + '实务成功')
      this.getPracticeAll()
      this.$refs['drive-table'].refreshTable()
      this.drawerVisible = false
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if(!valid) return false
        const { id, coverIds, attaches } = this.fromModel
        const params = {
          ...this.fromModel,
          coverIds: coverIds && coverIds.length ? coverIds.map(item => item.id) : [],
          attaches: attaches && attaches.length ? attaches.map(item => item.id) : [],
        }
        if(id) {
          partyPracticeUpdate(params).then(() => {
            this.operationSuccess('编辑')
          })
        } else {
          partyPracticeCreate(params).then(() => {
            this.operationSuccess('新增')
          })
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
