import { urlValid } from '@/utils/validate'

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          title: {
            form: 'input',
            label: '学习标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入学习标题'
              },
              {
                max: 100,
                message: '字符长度不能超过100个字符'
              }
            ]
          },
          coverIds: {
            form: 'component',
            label: '学习封面',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传学习封面'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return <div>请上传不大于10MB的图片</div>
            },
            props: {
              uploadData: {
                type: 'party'
              },
              accept: 'image/*',
              maxLength: 1,
              maxSize: 10,
              limit: 1
            }
          },
          detailType: {
            form: 'radio',
            label: '内容形式',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择内容形式'
              }
            ],
            options: [
              {
                label: '自主填写',
                value: 1
              },
              {
                label: '公众号文章',
                value: 2
              }
            ]
          },
          content: {
            form: 'component',
            label: '学习内容',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入学习内容'
              }
            ],
            componentName: 'tinymce'
          },
          officialUrl: {
            form: 'input',
            label: '地址链接',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入地址链接'
              },
              {
                validator: urlValid
              }
            ],
            customTips: () => {
              return <div>文章地址务必为“中安创谷”公众号的文档</div>
            }
          },
          infoSources: {
            form: 'input',
            label: '信息来源',
            hidden: false,
            rule: [
              {
                type: 'string',
                message: '请输入信息来源'
              },
              {
                max: 100,
                message: '字符长度不能超过100个字符'
              }
            ]
          },
          urlSources: {
            form: 'input',
            label: '原文地址',
            hidden: false,
            rule: [
              {
                type: 'string',
                message: '请输入原文地址'
              },
              {
                validator: urlValid
              }
            ]
          },
          attaches: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div>请上传不大于10MB的附件,附件不得超过三个</div>
              )
            },
            props: {
              uploadData: {
                type: 'party'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          },
          status: {
            form: 'radio',
            label: '发布设置',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择发布设置'
              }
            ],
            options: [
              {
                label: '立即发布',
                value: 1
              },
              {
                label: '定时发布',
                value: 0
              }
            ]
          },
          publishTime: {
            form: 'date',
            label: '发布时间',
            hidden: true,
            rule: {
              required: true,
              message: '请选择发布时间',
              type: 'string'
            },
            props: {
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss'
            }
          }
        }
      }
    }
  }
}
