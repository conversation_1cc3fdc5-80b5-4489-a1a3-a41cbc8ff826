export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'cover',
          label: '学习封面',
          render: (h, scope) => {
            const coverAttach = scope.row.coverAttach || {}
            const party = coverAttach.party || []
            const cover = party.length ? party[0].path : ''
            return (
              <div style={'width: 180px;height:100px'}>
                <el-image class={'wh100'} fit={'cover'} src={cover}>
                  <div slot="error" class="image-slot">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
            )
          }
        },
        {
          prop: 'title',
          label: '学习标题',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'createTime',
          label: '创建时间'
        },
        {
          prop: 'status',
          label: '发布状态',
          search: {
            type: 'select',
            options: [
              { label: '已发布', value: 1 },
              { label: '未发布', value: 0 }
            ]
          },
          render: (h, scope) => {
            return <div>{
              scope.row.status ? <basic-tag isDot type="success" label="已发布" /> :
                <basic-tag isDot type="warning" label="未发布" />
            }</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.VIEW}
                  type={'primary'}
                  onClick={() => this.previewEvent(scope.row)}
                >{this.routeButtonsTitle.VIEW}</el-link>
                <el-dropdown onCommand={(e) => this.commandHandle(e, scope.row)}>
                  <el-link
                    type="primary"
                    class={'m-l-8'}
                    v-permission={[
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >更多</el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command={'edit'} v-permission={this.routeButtonsPermission.EDIT}>
                      <el-link type="success">{this.routeButtonsTitle.EDIT}</el-link>
                    </el-dropdown-item>
                    <el-dropdown-item command={'publish'} v-permission={this.routeButtonsPermission.EDIT}>
                      {
                        scope.row.status ? <el-link type="primary">取消发布</el-link> :
                          <el-link type="primary">发布</el-link>
                      }
                    </el-dropdown-item>

                    <el-dropdown-item command={'delete'} v-permission={this.routeButtonsPermission.DELETE}>
                      <el-link type="danger">{this.routeButtonsTitle.DELETE}</el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ]
    }
  }
}
