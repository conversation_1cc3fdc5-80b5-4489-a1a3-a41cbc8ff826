import request from '@/utils/request'

// 获取列表分页
export function getWorkList(params) {
  return request({
    url: `/party/work/page`,
    method: 'get',
    params
  })
}
// 发布
export function workPublish(params) {
  return request({
    url: `/party/work/release`,
    method: 'get',
    params
  })
}

//删除
export function deleteWork(id) {
  return request({
    url: `/party/work/delete?id=${id}`,
    method: 'get'
  })
}
// 新增
export function createWork(data) {
  return request({
    url: `/party/work/create`,
    method: 'post',
    data
  })
}
// 更新
export function updateWork(data) {
  return request({
    url: `/party/work/update`,
    method: 'post',
    data
  })
}
// 获取详情
export function getWorkDetail(params) {
  return request({
    url: `/party/work/get`,
    method: 'get',
    params
  })
}
