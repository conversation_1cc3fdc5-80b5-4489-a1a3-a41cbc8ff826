<template>
  <basic-drawer
    title="报名详情"
    :visible.sync="drawerVisible"
    :haveFooter="false"
  >
    <div class="sign-up-wrapper">
      <ul class="sign-up-details">
        <li class="flex activity-info font-size-14 line-height-22 m-b-20">
          <div class="color-text-regular label m-r-16">活动名称</div>
          <div class="color-text-primary">
            {{ signUpData.activityName }}
          </div>
        </li>
        <li
          v-if="signUpData.address"
          class="flex activity-info font-size-14 line-height-22 m-b-20"
        >
          <div class="color-text-regular label m-r-16">活动地址</div>
          <div class="color-text-primary">
            {{ signUpData.address }}
          </div>
        </li>
        <li class="flex activity-info font-size-14 line-height-22 m-b-20">
          <div class="color-text-regular label m-r-16">活动时间</div>
          <div class="color-text-primary">
            {{ signUpData.startTime }} ~ {{ signUpData.endTime }}
          </div>
        </li>
      </ul>

      <div
        class="flex align-items-center justify-content-between m-t-20 m-b-13"
      >
        <span class="color-text-primary font-size-16">报名表</span>
        <el-button type="primary" size="mini" @click="exportActivitySignUp">
          <svg-icon icon-class="download" />
          <span>导出</span>
        </el-button>
      </div>
      <!-- 报名表 -->
      <drive-table
        :columns="tableColumn"
        :table-data="signUpData.signList || []"
      />
    </div>

    <!-- 查看附件详情 -->
    <dialog-cmp
      title="附件详情"
      :visible.sync="visibleSee"
      width="30%"
      :haveOperation="false"
    >
      <div>
        <Uploader v-model="viewData" type="avatar" mulity onlyForView />
      </div>
    </dialog-cmp>
  </basic-drawer>
</template>

<script>
import downloads from '@/utils/download'
import { exportActivitySignUp } from './api'
export default {
  name: 'SignUpDetails',
  data() {
    return {
      drawerVisible: false,
      visibleSee: false,
      viewData: [],
      tableColumn: [
        {
          type: 'index',
          label: '序号'
        },
        {
          prop: 'name',
          label: '联系人姓名'
        },
        {
          prop: 'mobile',
          label: '联系方式'
        },
        {
          prop: 'entName',
          label: '所属公司',
          render: (h, scope) => {
            return (
              <span>
                {scope.row.entName || scope.row.enterpriseName || '--'}
              </span>
            )
          }
        },
        {
          prop: 'position',
          label: '职位'
        },
        {
          prop: 'carNo',
          label: '车牌号码'
        },
        {
          prop: 'signStatusStr',
          label: '签到状态'
        }
        // {
        //   prop: 'attach',
        //   label: '附件',
        //   width: 120,
        //   render: (h, scope) => {
        //     return (
        //       <div>
        //         {Object.keys(scope.row.attach).length > 0 ? (
        //           <span
        //             class="color-primary pointer"
        //             onClick={() => {
        //               this.seeAttachDetails(scope.row)
        //             }}
        //           >
        //             查看附件
        //           </span>
        //         ) : (
        //           <span>暂无附件</span>
        //         )}
        //       </div>
        //     )
        //   }
        // }
      ],
      signUpData: {}
    }
  },
  watch: {
    drawerVisible(val) {
      if (!val) {
        this.signUpData = {}
      }
    }
  },
  methods: {
    // 查看附件详情
    seeAttachDetails(scope) {
      if (Object.keys(scope.attach).length > 0) {
        this.viewData = scope.attach.pic
        this.visibleSee = true
      } else {
        this.$message.warning('暂无附件')
      }
    },
    open(res) {
      this.signUpData = res
      this.drawerVisible = true
    },

    // 导出
    exportActivitySignUp() {
      downloads.requestDownload(
        exportActivitySignUp(this.signUpData.id),
        'excel'
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.sign-up-wrapper {
  .sign-up-details {
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-base);
    .activity-info {
      .label {
        flex: 0 0 60px;
      }
    }
  }
}
</style>
