import {
  getCluetatus,
  getDisplayStatus
} from '@/views/manage/operationService/policy/policy-special/policyCenter/utils/state'

export default {
  data() {
    return {
      // 政策清单
      tableColumnList: [
        {
          prop: 'policyName',
          label: '政策名称',
          search: {
            type: 'input'
          },
          minWidth: 180,
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onclick={() => {
                  this.openHandler(scope.row)
                }}
              >
                {scope.row.policyName}
              </el-link>
            )
          }
        },
        {
          prop: 'publishingUnit',
          label: '发布单位',
          search: {
            type: 'select',
            options: []
          }
        },
        // {
        //   prop: 'declarationStatus',
        //   label: '申报状态',
        //   hidden: true,
        //   search: {
        //     type: 'select',
        //     options: []
        //   }
        // },
        {
          prop: 'display',
          label: '显示状态',
          hidden: true,
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'publishingTime',
          label: '发布时间',
          search: {
            type: 'date',
            valueFormat: 'yyyy-MM-dd'
          }
        },
        // {
        //   prop: 'policyLevelName',
        //   label: '政策等级',
        // },
        // {
        //   prop: 'consultationStr',
        //   label: '咨询',
        //   width: 150
        // },
        // {
        //   prop: 'declarationStatusStr',
        //   label: '申报',
        //   width: 150
        // },
        {
          prop: 'displayStr',
          label: '显示状态',
          render: (h, scope) => {
            return <div>{getDisplayStatus(h, scope.row.display)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  class={'m-r-8'}
                  type={'primary'}
                  onclick={() => {
                    this.openHandler(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.VIEW}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>

                <el-dropdown>
                  <el-link
                    type="primary"
                    v-permission={[
                      ...this.routeButtonsPermission.SHOW,
                      ...this.routeButtonsPermission.HIDE,
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >
                    更多
                  </el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-show={!scope.row.display}>
                      <el-link
                        type="success"
                        onClick={() => {
                          this.displayHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.VIEW}
                      >
                        {this.routeButtonsTitle.SHOW}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-show={scope.row.display}>
                      <el-link
                        type="warning"
                        onClick={() => {
                          this.displayHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.HIDE}
                      >
                        {this.routeButtonsTitle.HIDE}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="primary"
                        onClick={() => {
                          this.editHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.HIDE}
                      >
                        {this.routeButtonsTitle.EDIT}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="danger"
                        onClick={() => {
                          this.delListHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.DELETE}
                      >
                        {this.routeButtonsTitle.DELETE}
                      </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ],
      // 政策文件
      tableColumnFile: [
        {
          prop: 'fileName',
          label: '文件名称',
          search: {
            type: 'input'
          },
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onClick={() => {
                  this.openFileHandler(scope.row)
                }}
              >
                {scope.row.fileName}
              </el-link>
            )
          }
        },
        {
          prop: 'fileFormat',
          label: '文件格式',
          width: 150
        },
        {
          prop: 'fileSize',
          label: '文件大小',
          width: 150
        },
        {
          prop: 'download',
          label: '下载量',
          width: 80
        },
        {
          prop: 'uploadTime',
          label: '上传时间',
          width: 150
        },
        {
          prop: 'creator',
          label: '上传人',
          width: 150
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onClick={() => {
                  this.downHandler(scope.row)
                }}
                v-permission={this.routeButtonsPermission.DOWNLOAD}
              >
                {this.routeButtonsTitle.DOWNLOAD}
              </el-link>
            )
          }
        }
      ],
      // 政策专题
      tableColumnTopic: [
        {
          prop: 'name',
          label: '专题名称'
        },
        {
          prop: 'count',
          label: '政策数'
        },
        {
          prop: 'lastUpdated',
          label: '最近更新时间'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  class={'m-r-8'}
                  type={'primary'}
                  onClick={() => {
                    this.topicHandler(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.VIEW}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>

                <el-dropdown>
                  <el-link
                    type="primary"
                    v-permission={[
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >
                    更多
                  </el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <el-link
                        type="primary"
                        onClick={() => {
                          this.editTopicHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.HIDE}
                      >
                        {this.routeButtonsTitle.EDIT}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="danger"
                        onClick={() => {
                          this.delHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.DELETE}
                      >
                        {this.routeButtonsTitle.DELETE}
                      </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ],
      // 专题列表
      tableColumnDetail: [
        {
          prop: 'policyName',
          label: '政策名称',
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onclick={() => {
                  this.openHandler(scope.row)
                }}
              >
                {scope.row.policyName}
              </el-link>
            )
          }
        },
        {
          prop: 'publishingUnit',
          label: '发布单位'
        },
        {
          prop: 'publishingTime',
          label: '发布时间',
          width: 120
        },
        {
          prop: 'policyLevelName',
          label: '政策等级',
          width: 120
        },
        {
          prop: 'consultationStr',
          label: '咨询',
          width: 150
        },
        {
          prop: 'declarationStatusStr',
          label: '申报',
          width: 150
        },
        {
          prop: 'displayStr',
          label: '显示状态',
          width: 120,
          render: (h, scope) => {
            return <div>{getDisplayStatus(h, scope.row.display)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  class={'m-r-8'}
                  type={'primary'}
                  onclick={() => {
                    this.openHandler(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.VIEW}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>

                <el-dropdown>
                  <el-link
                    type="primary"
                    v-permission={[
                      ...this.routeButtonsPermission.SHOW,
                      ...this.routeButtonsPermission.HIDE,
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >
                    更多
                  </el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item v-show={!scope.row.display}>
                      <el-link
                        type="success"
                        onClick={() => {
                          this.displayHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.VIEW}
                      >
                        {this.routeButtonsTitle.SHOW}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item v-show={scope.row.display}>
                      <el-link
                        type="warning"
                        onClick={() => {
                          this.displayHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.HIDE}
                      >
                        {this.routeButtonsTitle.HIDE}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="primary"
                        onClick={() => {
                          this.editHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.HIDE}
                      >
                        {this.routeButtonsTitle.EDIT}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="danger"
                        onClick={() => {
                          this.delListHandler(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.DELETE}
                      >
                        {this.routeButtonsTitle.DELETE}
                      </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ],
      // 政策线索
      tableColumnClue: [
        {
          prop: 'materialTitle',
          label: '政策标题',
          search: {
            type: 'input'
          }
        },
        // {
        //   prop: 'levelName',
        //   label: '政策级别',
        //   width: 150,
        //   hidden: true
        // },
        {
          prop: 'departmentName',
          label: '发布单位',
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'projectType',
          label: '政策类别',
          search: {
            type: 'select',
            options: []
          },
          width: 150
        },
        {
          prop: 'releaseTime',
          label: '发文时间',
          width: 150
        },
        {
          prop: 'status',
          label: '发布状态',
          search: {
            type: 'select',
            options: [
              {
                label: '未入库',
                value: 0
              },
              {
                label: '已入库',
                value: 1
              }
            ]
          },
          width: 150,
          render: (h, scope) => {
            return <div>{getCluetatus(h, scope.row.status)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onClick={() => {
                  this.detailClueHandler(scope.row)
                }}
                v-permission={this.routeButtonsPermission.VIEW}
              >
                {this.routeButtonsTitle.VIEW}
              </el-link>
            )
          }
        }
      ]
    }
  }
}
