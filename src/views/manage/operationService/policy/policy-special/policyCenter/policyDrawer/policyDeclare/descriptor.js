import Vue from 'vue' // vue框架
// 内容
import TipsTinymce from '../../components/tipsTinymce'
Vue.component('TipsTinymce', TipsTinymce)

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          declareTime: {
            form: 'dateRange',
            label: '申报时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择申报时间'
              }
            ]
          },
          fillingConditions: {
            form: 'component',
            label: '申报条件',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入申报条件'
              }
            ],
            componentName: 'TipsTinymce'
          },
          fillingMaterials: {
            form: 'component',
            label: '申报材料',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入申报材料'
              }
            ],
            componentName: 'TipsTinymce'
          },
          fillingPortal: {
            form: 'input',
            label: '申报入口',
            rule: [
              {
                required: false,
                type: 'string',
                message: '可不填，默认在平台内部完成申报'
              },
              {
                validator: (rule, value, callback) => {
                  if (!value) return callback()
                  const urlReg = /^(https?|ftp):\/\/[^\s/$.?#].[^\s]*\??.*$/
                  if (!urlReg.test(value))
                    return callback(new Error('请输入有效的网址'))
                  callback()
                }
              }
            ]
          }
        }
      }
    }
  }
}
