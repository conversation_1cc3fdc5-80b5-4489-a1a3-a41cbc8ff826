<template>
  <div>
    <driven-form
      ref="driven-form"
      label-position="top"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
    <!--    添加推送企业-->
    <dialog-cmp
      title="添加推送企业"
      :visible.sync="dialogVisible"
      width="60%"
      :haveOperation="false"
    >
      <div class="m-b-16">
        <el-input
          placeholder="请输入关键字搜索想要发布的政策"
          v-model="policy"
          clearable
        ></el-input>
      </div>
      <drive-table
        ref="drive-table-file"
        :table-data="tableList"
        height="calc(100vh - 360px)"
        :columns="tableColumn"
      >
      </drive-table>
    </dialog-cmp>
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import ColumnMixins from './column'

export default {
  name: 'PolicyPush',
  props: {
    checked: {
      type: Boolean,
      default: false
    }
  },
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      dialogVisible: false,
      policy: '',
      tableList: [],
      fromModel: {
        tableList: [
          {
            name: '测试企业'
          }
        ],
        pushOption: []
      },
      isPass: false
    }
  },
  created() {
    this.$emit('init', this)
  },
  methods: {
    openHandler() {
      this.dialogVisible = true
    },
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return this.fromModel
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.tableListKey) {
  .el-form-item__label {
    display: flex;
    div {
      width: 100%;
    }
  }
}
</style>
