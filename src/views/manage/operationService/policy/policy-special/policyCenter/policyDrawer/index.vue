<template>
  <basic-drawer
    :title="title"
    size="65%"
    :visible.sync="visible"
    @confirmDrawer="confirmDrawer"
  >
    <template v-if="visible">
      <!--    tab切换-->
      <basic-tab
        ref="basicTab"
        :tabs-data="tabsData"
        :current="current"
        @tabsChange="tabsChange"
      >
        <template v-slot:right>
          <el-popover placement="bottom-start" width="250" trigger="click">
            <div class="checked-list">
              <div
                class="checked-item pointer p-8"
                v-for="(item, index) in moduleData"
                :key="index"
                @click="checkedHandler(item.value)"
              >
                <div
                  class="flex justify-content-between align-items-center font-size-14 line-height-20"
                >
                  <div>{{ item.label }}</div>
                  <el-checkbox
                    label=" "
                    v-model="item.checked"
                    @change="checkedHandler(item.value)"
                  ></el-checkbox>
                </div>
                <div class="font-size-12 color-info line-height-20">
                  {{ item.tips }}
                </div>
              </div>
            </div>
            <!--            <el-link slot="reference" type="primary">模块管理</el-link>-->
          </el-popover>
        </template>
      </basic-tab>
      <!--    政策原文-->
      <policy-text
        ref="policyText"
        @init="componentInit"
        :checked="checkedObj.policyText"
        @getNewTable="getNewTable"
        v-show="current === 0"
      />
      <!--    政策解读-->
      <policy-interpretation
        ref="interpretation"
        :checked="checkedObj.interpretation"
        @init="componentInit"
        v-show="current === 1"
        :edit-data="editData"
      />
      <!--    政策推送-->
      <policy-push
        ref="policyPush"
        :checked="checkedObj.policyPush"
        @init="componentInit"
        v-show="current === 3"
      />
      <!--    政策咨询-->
      <policy-consult
        ref="policyConsult"
        :checked="checkedObj.policyConsult"
        @init="componentInit"
        v-show="current === 4"
      />
      <!--    政策申报-->
      <policy-declare
        ref="policyDeclare"
        :checked="checkedObj.policyDeclare"
        @init="componentInit"
        v-show="current === 5"
      />
    </template>
  </basic-drawer>
</template>

<script>
import BasicTab from '../components/BasicTab'
import PolicyText from '@/views/manage/operationService/policy/policy-special/policyCenter/policyDrawer/policyText'
import PolicyInterpretation from '@/views/manage/operationService/policy/policy-special/policyCenter/policyDrawer/policyInterpretation'
import PolicyPush from '@/views/manage/operationService/policy/policy-special/policyCenter/policyDrawer/policyPush'
import PolicyConsult from '@/views/manage/operationService/policy/policy-special/policyCenter/policyDrawer/policyConsult'
import PolicyDeclare from '@/views/manage/operationService/policy/policy-special/policyCenter/policyDrawer/policyDeclare'
import {
  getPolicyCreate,
  getPolicyUpdate,
  getPolicyUpdateDetail
} from '@/views/manage/operationService/policy/policy-special/api'
export default {
  name: 'PolicyDrawer',
  components: {
    PolicyDeclare,
    PolicyConsult,
    PolicyPush,
    PolicyInterpretation,
    PolicyText,
    BasicTab
  },
  data() {
    return {
      title: '发布政策',
      visible: false,
      checkedList: [
        {
          label: '政策原文',
          tips: '',
          checked: true,
          hidden: true,
          value: 0,
          key: 'policyText'
        },
        {
          label: '政策解读',
          tips: '为该政策添加更多要点内容',
          checked: false,
          value: 1,
          key: 'interpretation'
        },
        // {
        //   label: '政策推送',
        //   tips: '为该政策添加推送内容和企业',
        //   checked: false,
        //   value: 3,
        //   key: 'policyPush'
        // },
        {
          label: '政策咨询',
          tips: '为该政策添加咨询引导',
          checked: false,
          value: 4,
          key: 'policyConsult'
        },
        {
          label: '政策申报',
          tips: '为该政策添加申报通知内容',
          checked: false,
          value: 5,
          key: 'policyDeclare'
        }
      ],
      current: 0,
      vms: [],
      updateDetail: {}, // 编辑详情
      id: '',
      editData: {}
    }
  },
  computed: {
    checkedObj() {
      const obj = {}
      this.checkedList.forEach(item => {
        obj[item.key] = item.checked
      })
      return obj
    },
    moduleData() {
      return this.checkedList.filter(item => !item.hidden)
    },
    tabsData() {
      return this.checkedList.filter(item => item.checked)
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.vms = []
        this.checkedList = this.$options.data().checkedList
        this.current = 0
      }
    }
  },
  methods: {
    // 专题刷新
    getNewTable() {
      this.$emit('getNewTable')
    },
    // 政策清单 - 编辑
    async editHandler(row) {
      this.title = '编辑政策'
      const res = await getPolicyUpdateDetail(row.id)
      let { modelIds, id, info, unscramble, consultation, filling } = res
      this.id = id
      this.editData = res
      this.checkedList.forEach(item => {
        modelIds &&
          modelIds.forEach(val => {
            if (item.value === val) {
              item.checked = true
            }
          })
      })
      this.updateDetail = res
      this.visible = true
      this.$nextTick(() => {
        this.$refs.policyText && this.$refs.policyText.editHandler(info)
        this.$refs.interpretation &&
          this.$refs.interpretation.editHandler(unscramble)
        this.$refs.policyConsult &&
          this.$refs.policyConsult.editHandler(consultation)
        this.$refs.policyDeclare &&
          this.$refs.policyDeclare.editHandler(filling)
      })
    },
    // 打开抽屉
    publishHandler() {
      this.title = '发布政策'
      this.id = ''
      this.checkedList = this.$options.data().checkedList
      this.current = 0
      this.visible = true
    },
    // 提交
    confirmDrawer() {
      let list = []
      let obj = {}
      this.vms.forEach((item, index) => {
        if (item.checked) {
          const vm = this.vms[index]
          list.push(vm?.validateHandle())
        }
      })
      Promise.all(list).then(() => {
        for (let i = 0; i < this.vms.length; i++) {
          const vm = this.vms[i]
          if (vm.getData()) {
            obj = {
              ...obj,
              ...vm.getData()
            }
          }
        }
        let modelIds = []
        this.checkedList.forEach(item => {
          if (item.checked) modelIds.push(item.value)
        })
        let data = {
          ...obj,
          modelIds,
          id: this.id
        }
        this.getPolicyCreate(data)
      })
    },
    async getPolicyCreate(data) {
      if (this.id) {
        await getPolicyUpdate(data)
        this.$toast.success('编辑政策成功')
      } else {
        await getPolicyCreate(data)
        this.$toast.success('发布政策成功')
      }
      this.getNewTable()
      this.visible = false
    },
    componentInit(vm) {
      this.vms.push(vm)
    },
    // tab切换
    tabsChange(e) {
      this.current = e
    },
    // 模块管理
    checkedHandler(val) {
      this.checkedList.forEach(item => {
        if (val === item.value) {
          item.checked = !item.checked
          if (this.current === item.value) this.current = 0
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.checked-list {
  .checked-item {
    margin-bottom: 8px;
    @include background_color_mix(--color-primary, #ffffff, 96%);
    border-radius: 3px;
    opacity: 1;
  }
  .checked-item:last-child {
    margin-bottom: 0;
  }
}
</style>
