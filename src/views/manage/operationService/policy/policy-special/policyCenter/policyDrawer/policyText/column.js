export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'materialTitle',
          label: '政策标题',
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onClick={() => {
                  this.choosePolicy(scope.row)
                }}
              >
                {scope.row.materialTitle}
              </el-link>
            )
          }
        },
        {
          prop: 'sourceUrl',
          label: '来源网站',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-show={scope.row.sourceUrl}
                  type={'primary'}
                  onClick={() => {
                    this.toSourceUrl(scope.row)
                  }}
                >
                  {scope.row.sourceUrl}
                </el-link>
                <div v-show={!scope.row.sourceUrl}>--</div>
              </div>
            )
          }
        },
        {
          prop: 'departmentName',
          label: '发布单位'
        },
        {
          prop: 'projectType',
          label: '政策类别',
          width: 150
        }
      ]
    }
  }
}
