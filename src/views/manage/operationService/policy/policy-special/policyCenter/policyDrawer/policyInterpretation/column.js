export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'policyName',
          label: '政策标题'
        },
        {
          prop: 'publishingUnit',
          label: '信息来源'
        },
        {
          prop: 'publishingTime',
          label: '发布时间'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-link
                type={'danger'}
                onClick={() => {
                  this.moveHandler(scope)
                }}
              >
                移出
              </el-link>
            )
          }
        }
      ],
      tableColumnDialog: [
        {
          prop: 'policyName',
          label: '政策标题'
        },
        {
          prop: 'publishingUnit',
          label: '发布单位'
        },
        {
          prop: 'publishingTime',
          label: '发布时间',
          width: 120
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-show={scope.row.flag}
                  type={'primary'}
                  onClick={() => {
                    this.chooseHandler(scope.row)
                  }}
                >
                  选择
                </el-link>
                <el-link
                  v-show={!scope.row.flag}
                  type={'primary'}
                  onClick={() => {
                    this.cancelHandler(scope.row)
                  }}
                >
                  取消选择
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
