const validatePhone = (rule, value, callback) => {
  if (value === '') {
    callback(new Error(`请输入电话`))
  } else {
    if (/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value)) {
      callback()
    } else {
      callback(new Error(`电话号码不正确`))
    }
  }
}

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          consultation: {
            form: 'checkbox',
            label: '可提供的咨询方式',
            options: [
              {
                label: '电话咨询',
                value: 1
              },
              {
                label: '微信咨询',
                value: 2
              },
              {
                label: '现场咨询',
                value: 3
              },
              {
                label: '走访辅导',
                value: 4
              }
            ],
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择可提供的咨询方式'
              }
            ]
          },
          consultationPeriods: {
            form: 'input',
            label: '可接受的咨询时段',
            attrs: {
              maxLength: 60
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入可接受的咨询时段'
              }
            ]
          },
          phoneContact: {
            form: 'input',
            label: '联系人',
            attrs: {
              maxLength: 20
            },
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <div style={'margin-left: -24px;'}>电话咨询</div>
                  <div>联系人</div>
                </div>
              )
            }
          },
          contactNumber: {
            form: 'input',
            label: '联系电话',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系电话'
              },
              {
                validator: validatePhone
              }
            ]
          },
          wxContract: {
            form: 'input',
            label: '联系人',
            attrs: {
              maxLength: 20
            },
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <div style={'margin-left: -24px;'}>微信咨询</div>
                  <div>联系人</div>
                </div>
              )
            }
          },
          wxId: {
            form: 'input',
            label: '微信号',
            span: 12,
            hidden: true,
            attrs: {
              maxLength: 20
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入微信号'
              }
            ]
          },
          address: {
            form: 'input',
            label: '区域位置',
            hidden: true,
            attrs: {
              maxLength: 100
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入区域位置'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <div style={'margin-left: -24px;'}>现场咨询</div>
                  <div>区域位置</div>
                </div>
              )
            }
          },
          remark: {
            form: 'input',
            label: '备注内容',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入备注内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true
            }
          }
        }
      }
    }
  }
}
