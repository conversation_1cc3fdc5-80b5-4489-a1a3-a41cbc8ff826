export default {
  data() {
    return {
      tableColumn: [
        {
          label: '工具名称',
          prop: 'name'
        },
        {
          label: '简介',
          prop: 'desc',
          showOverflowTooltip: true
        },
        {
          label: '匹配规则',
          prop: 'matchRule',
          showOverflowTooltip: true
        },
        {
          label: '浏览量',
          prop: 'viewsNum',
          align: 'center'
        },
        {
          label: '创建时间',
          prop: 'createTime',
          align: 'center'
        },
        {
          label: '状态',
          prop: 'status',
          align: 'center',
          render: (h, scope) => {
            return (
              <el-switch
                v-model={scope.row.status}
                onChange={() => {
                  this.statusChange(scope.row)
                }}
              ></el-switch>
            )
          }
        },
        {
          label: '操作',
          prop: 'operation',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  v-permission={this.routeButtonsPermission.VIEW}
                  onClick={() => {
                    this.detailHandle(scope.row)
                  }}
                  type={'text'}
                  class={'m-r-8 font-size-14'}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-button>
                <el-dropdown trigger="click">
                  <span
                    class="color-primary pointer"
                    v-permission={[
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >
                    更多
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item
                      v-permission={this.routeButtonsPermission.EDIT}
                    >
                      <div
                        onClick={() => {
                          this.editHandle(scope.row)
                        }}
                      >
                        {this.routeButtonsTitle.EDIT}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-permission={this.routeButtonsPermission.DELETE}
                    >
                      <div
                        class={'color-danger'}
                        onClick={() => {
                          this.deleteHandle(scope.row)
                        }}
                      >
                        {this.routeButtonsTitle.DELETE}
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ]
    }
  }
}
