export default {
  data() {
    return {
      tableColumn: [
        {
          label: '材料名称',
          prop: 'title',
          showOverflowTooltip: true,
          minWidth: '200px'
        },
        {
          label: '文件类型',
          prop: 'type',
          align: 'center'
        },
        {
          label: '操作',
          prop: 'operation',
          align: 'center',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  v-permission={this.routeButtonsPermission.VIEW}
                  onClick={() => {
                    this.filePreviewHandle(scope.row)
                  }}
                  type={'text'}
                  className={'color-primary'}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-button>
                <el-button
                  v-permission={this.routeButtonsPermission.DOWNLOAD}
                  onClick={() => {
                    this.fileDownloadHandle(scope.row)
                  }}
                  type={'text'}
                  className={'color-primary'}
                >
                  {this.routeButtonsTitle.DOWNLOAD}
                </el-button>
              </div>
            )
          }
        }
      ]
    }
  }
}
