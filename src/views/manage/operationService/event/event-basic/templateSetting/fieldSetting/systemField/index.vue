<template>
  <div class="system-field-container">
    <div class="field-header-label">选择内置字段</div>
    <div class="border-wrapper m-t-8 system-field-wrapper">
      <div class="field-wrapper-header">
        <el-checkbox
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
            v-model="allChecked"
        >全选</el-checkbox>
      </div>
      <div class="p-t-10 p-b-10 p-l-16 p-r-16">
        <el-checkbox-group v-model="checkList" @change="handleCheckedChange">
          <div
            class="system-field-item"
            v-for="item in systemFields"
            :key="item.id"
          >
            <el-checkbox class="w100" :label="item.id" @change="itemCheckboxChange($event, item)">
              <span class="item-required">{{ item.required ? '*' : '' }}</span>
              <span class="item-label" v-tooltip="item.label"></span>
            </el-checkbox>
          </div>
        </el-checkbox-group>
      </div>
    </div>
  </div>
</template>

<script>
import customField from '../customField'
import { deepClone } from '@/utils/tools'

export default {
  name: "SystemField",
  data() {
    return {
      systemFields: [],
      allChecked: true,
      checkList: [],
      isIndeterminate: false
    }
  },
  methods: {
    initData(fields) {
      let customFields = deepClone(customField)
      this.systemFields = customFields.filter(item => item.system)
      this.checkList = this.systemFields.map(item => item.id)
      if(fields && fields.length) {
        const sysList = fields.filter(item => item.system === true)
        this.checkList = sysList.map(item => item.id)
      }
      this.handleCheckedChange(this.checkList)
    },
    // 删除某一个
    deleteItemHandle(row) {
      const index = this.checkList.findIndex(item => item === row.id)
      this.checkList.splice(index, 1)
      this.handleCheckedChange(this.checkList)
    },
    itemCheckboxChange(val, row) {
      this.$emit('systemCheckboxChange', val, row)
    },
    handleCheckAllChange(val) {
      this.checkList = []
      if(val) {
        this.systemFields.forEach(item => {
          this.checkList.push(item.id)
          this.itemCheckboxChange(val, item)
        })
      } else {
        this.systemFields.forEach(item => {
          this.itemCheckboxChange(val, item)
        })
      }
      this.isIndeterminate = false
    },
    handleCheckedChange(val) {
      let checkedCount = val.length
      this.allChecked = checkedCount === this.systemFields.length
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.systemFields.length
    }
  }
}
</script>

<style scoped lang="scss">
.system-field-container {
  width: 240px;
  flex-shrink: 0;
  .system-field-wrapper {
    height: calc(100% - 22px - 8px);
    .system-field-item {
      font-weight: 400;
      font-size: 14px;
      height: 28px;
      & + .system-field-item {
        margin-top: 6px;
      }
      .item-required {
        color: #E34D59;
        width: 10px;
        display: inline-block;
      }
      .item-label {
        display: inline-block;
        width: 100%;
      }
    }
  }
}
</style>
