export default {
  data() {
    return {
      formConfigure: {
        labelPosition: 'top',
        descriptors: {
          label: {
            form: 'input',
            label: '政策级别',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入政策级别'
              }
            ],
            attrs: {
              maxLength: 15
            }
          }
          // remark: {
          //   form: 'input',
          //   label: '备注',
          //   rule: [
          //     {
          //       type: 'string',
          //       message: '请输入备注'
          //     }
          //   ],
          //   props: {
          //     type: 'textarea'
          //   }
          // }
        }
      }
    }
  }
}
