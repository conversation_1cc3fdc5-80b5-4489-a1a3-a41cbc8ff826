<template>
<div>
  <el-alert
      style="margin-top: -41px"
      title="活动如果需要报名，请完成报名信息的填写"
      type="warning"
      show-icon
      :closable="false"
  >
  </el-alert>
  <driven-form
      class="m-t-8"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="rightFormConfigure"
      label-position="top"
  />
</div>
</template>

<script>
import DescriptorMixin from './descriptor'
import { eventInfoFieldList, eventInfoGetFieldInfo } from '../api'
import { deepClone } from '@/utils/tools'

export default {
  name: "NormalEventRightForm",
  mixins: [DescriptorMixin],
  data() {
    return {
      fromModel: {
        smsFlag: false
      },
      tableColumn: [],
      tableData: []
    }
  },
  inject: ['NormalEventCreate'],
  watch: {
    'fromModel.smsFlag': {
      handler(val) {
        this.rightFormConfigure.descriptors.smsMinute.hidden = !val
      },
      immediate: true
    },
    'fromModel.signFlag': {
      handler(val) {
        const filter = ['signFlag', 'smsMinute']
        for(const key in this.rightFormConfigure.descriptors) {
          if(!filter.includes(key)) {
            this.rightFormConfigure.descriptors[key].hidden = !val
          }
        }
        if(!val) {
          this.rightFormConfigure.descriptors.smsMinute.hidden = true
          this.$set(this.fromModel, 'smsFlag', false)
        }
      },
      immediate: true
    }
  },
  created() {
    this.eventInfoFieldList()
    document.addEventListener('visibilitychange', this.handleVisibilityChange)

  },
  beforeDestroy() {
    document.removeEventListener('visibilitychange', this.handleVisibilityChange)
  },
  methods: {
    handleVisibilityChange() {
      if(!document.hidden) {
        this.eventInfoFieldList()
      }
    },
    goTemplateHandle() {
      this.$emit('goTemplateHandle')
    },
    initData(row) {
      for(const key in this.rightFormConfigure.descriptors) {
        this.$set(this.fromModel, key, row[key])
      }
      this.$set(this.fromModel, 'limitPerson', row.limitPerson ? row.limitPerson.toString() : '')
      const qrcodeMap = row.qrcodeMap || {}
      this.$set(this.fromModel, 'qrcodeIds', qrcodeMap.event || [])
      this.templateChange(row.templateId)
    },
    eventInfoFieldList() {
      eventInfoFieldList().then(res => {
        this.rightFormConfigure.descriptors.templateId.options = res.map(item => {
          return {
            label: item.title,
            value: item.id
          }
        })
      })
    },
    templateChange(e) {
      this.tableColumn = []
      this.tableData = []
      if(!e) return false
      eventInfoGetFieldInfo({ id: e }).then(res => {
        const fieldForm = res.fieldForm || ''
        if(!fieldForm) return false
        const fields = JSON.parse(fieldForm)
        fields.forEach(item => {
          const select = item.form === 'radio' || item.form === 'checkbox'
          const date = item.form === 'date'
          const uploader = item.form === 'uploader'
          const dateTime = item.type === 'dateTime'
          this.tableColumn.push({
            prop: 'value',
            label: item.label,
            minWidth: (select || date) ? '180px' : item.label.length * 30 + 20 + 'px',
            renderHeader: () => {
              return (<div>
                {
                  item.required && <span style={'color:#e34d59'}>*</span>
                }
                <span>{item.label}</span>
              </div>)
            },
            render: () => {
              return (
                  <div>
                    { select ?
                        <el-select
                            style={'width: 150px !important'}
                            placeholder={'请选择'}
                            v-model={item.value}
                        >
                          {
                            item.options.map(row =>
                                <el-option label={row.name} value={row.id}></el-option>
                            )
                          }
                        </el-select> : date ?
                                <el-date-picker
                                    style={'width: 150px !important'}
                                    v-model={item.value}
                                    type={dateTime ? 'datetime' : 'date'}
                                    placeholder={dateTime ? '请选择时间' : '请选择日期'}
                                    format={dateTime ? 'yyyy-MM-dd HH:mm' : 'yyyy-MM-dd HH:mm:ss'}
                                >
                                </el-date-picker> :
                            <span>{uploader ? '请上传' : '请输入'}</span>
                    }
                  </div>
              )
            }
          })
        })
        this.tableData = deepClone(fields)
        this.tableData.length = 1
      })
    },
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['driven-form'].validate(valid => {
          if(valid) {
            resolve(this.fromModel)
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.templateIdKey) {
  .right-tips {
    position: absolute;
    right: 12px;
  }
}
:deep(.smsMinuteKey) {
  .el-form-item__content {
    margin-top: -20px;
  }
}
</style>
