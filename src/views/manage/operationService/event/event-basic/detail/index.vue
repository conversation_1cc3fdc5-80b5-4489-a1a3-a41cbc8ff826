<template>
  <basic-card>
    <div class="detail-header">
      <div class="header-cover">
        <img class="wh100" :src="cover" alt="">
        <div class="status-wrapper">
          <basic-tag :type="processStatusType" :label="detailInfo.processStatusStr"></basic-tag>
        </div>
        <div class="type-wrapper" v-tooltip="typeLabel"></div>
      </div>
      <div class="header-right">
        <div class="header-title" v-tooltip="noData(detailInfo.title)"></div>
        <el-row class="category-wrapper m-t-16 flex align-items-center">
          <el-col :span="8" class="calendar-wrapper flex align-items-center">
            <svg-icon icon-class="calendar"/>
            <span class="content-item">{{ detailInfo.startTime | noData }} 至 {{ detailInfo.endTime | noData }} 开展活动</span>
          </el-col>
          <el-col :span="16" class="catalogue-wrapper flex align-items-center">
            <svg-icon icon-class="location"/>
            <span class="content-item" v-tooltip="noData(detailInfo.address)"></span>
          </el-col>
        </el-row>
        <el-row class="category-wrapper m-t-16 flex align-items-center" v-if="detailInfo.signFlag">
          <el-col :span="8" class="time-wrapper flex align-items-center">
            <svg-icon icon-class="time"/>
            <span class="content-item">{{ detailInfo.signLimitTime | noData }} 报名截止</span>
          </el-col>
          <el-col :span="6" class="time-wrapper flex align-items-center">
            <svg-icon icon-class="user"/>
            <span class="content-item">已报名{{ detailInfo.signCount | noData }}人</span>
          </el-col>
          <el-col :span="10" class="time-wrapper flex align-items-center">
            <svg-icon icon-class="control-platform"/>
            <span class="content-item">活动范围：{{ detailInfo.signRangeStr | noData }}</span>
          </el-col>
        </el-row>
      </div>
    </div>
    <div class="m-t-42" v-if="detailInfo.eveDetail">
      <div class="line-height-22 m-b-16">活动详情</div>
      <div class="rich-text" v-html="$options.filters.richTextFilter(detailInfo.eveDetail)"></div>
    </div>
  </basic-card>
</template>

<script>
import { noData } from '@/filter'
import { eventInfoGet } from '../api'

export default {
  name: "EventDetail",
  data() {
    return {
      noData,
      detailInfo: {}
    }
  },
  computed: {
    cover() {
      const coverMap = this.detailInfo.coverMap || {}
      const event = coverMap.event || []
      return event.length ? event[0].path : ''
    },
    typeLabel() {
      return `${noData(this.detailInfo.kindStr)} — ${noData(this.detailInfo.typeStr)}`
    },
    processStatusType() {
      const processStatus = this.detailInfo.processStatus
      const obj = {
        '-1': 'danger',
        0: 'primary',
        1: 'success',
        2: 'info'
      }
      return obj[processStatus]
    }
  },
  created() {
    this.eventInfoGet()
  },
  methods: {
    eventInfoGet() {
      const id = this.$route.query.id
      if(!id) return false
      eventInfoGet({ id }).then(res => {
        this.detailInfo = res || {}
      })
    }
  }
}
</script>

<style scoped lang="scss">
.detail-header {
  display: flex;
  align-items: center;
  .header-cover {
    width: 220px;
    height: 110px;
    border-radius: 6px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    .status-wrapper {
      position: absolute;
      left: -4px;
      top: 6px;
      :deep(.el-tag) {
        padding-left: 12px;
      }
    }
    .type-wrapper {
      position: absolute;
      right: 0;
      bottom: 6px;
      padding: 0 8px;
      font-size: 12px;
      max-width: 180px;
      line-height: 22px;
      background: rgba(0, 0, 0, 0.7);
      @include font_color(--color-white);
    }
  }
  .header-right {
    width: calc(100% - 280px);
    padding-left: 16px;
    .header-title {
      width: 100%;
      font-weight: 500;
      font-size: 16px;
      color: rgba(0,0,0,0.9);
      line-height: 22px;
    }
    .category-wrapper {
      .content-item {
        font-weight: 350;
        font-size: 12px;
        color: rgba(0,0,0,0.6);
        line-height: 20px;
        padding-left: 4px;
      }
      .svg-icon {
        flex-shrink: 0;
        font-size: 14px;
      }
      .calendar-wrapper {
        .svg-icon {
          color: #054ce8;
        }
      }
      .catalogue-wrapper {
        .svg-icon {
          color: #00A870;
        }
      }
      .time-wrapper {
        .svg-icon {
          color: #ed7b2f;
        }
      }
    }
  }
}
</style>
