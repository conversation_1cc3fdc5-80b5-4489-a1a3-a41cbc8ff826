import DictPopover from '../components/DictPopover'
import dayjs from 'dayjs'

export default {
  components: { DictPopover },
  data() {
    return {
      formConfigure: {
        descriptors: {
          title: {
            form: 'input',
            label: '活动名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入活动名称'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          kind: {
            form: 'select',
            label: '活动大类',
            span: 12,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择活动大类'
              }
            ],
            options: []
          },
          type: {
            form: 'select',
            label: '活动类型',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择活动类型'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <div class="line-height-30 p-t-42">
                  <dict-popover
                    name={'活动类型'}
                    parentId={this.fromModel.kind || 0}
                    onRefreshDict={() => {
                      this.eventInfoDictList(this.fromModel.kind)
                    }}
                  />
                </div>
              )
            }
          },
          address: {
            form: 'input',
            label: '详细地址',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入详细地址'
              }
            ],
            attrs: {
              maxLength: 100
            }
          },
          naAddress: {
            form: 'input',
            label: '导航地址',
            rule: [
              {
                type: 'string',
                message: '请选择导航地址'
              }
            ],
            attrs: {
              readonly: true
            },
            customRight: () => {
              return (
                <div class="line-height-30 p-t-42">
                  <el-button
                    disabled={!this.fromModel.location}
                    onClick={() => this.clearNaAddressHandle()}
                  >
                    清空
                  </el-button>
                </div>
              )
            },
            events: {
              focus: () => {
                this.mapVisible = true
              }
            }
          },
          eventTime: {
            form: 'dateRange',
            label: '活动时间',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择活动时间'
              },
              {
                validator: (rule, value, callback) => {
                  if(!value || !value.length) return callback(new Error('请选择活动时间'))
                  const startTime = dayjs(this.detailInfo.startTime).valueOf()
                  const endTime = dayjs(this.detailInfo.endTime).valueOf()
                  const selectStartTime = dayjs(value[0]).valueOf()
                  const selectEndTime = dayjs(value[1]).valueOf()
                  if(selectStartTime < startTime || selectStartTime > endTime || selectEndTime < startTime || selectEndTime > endTime) {
                    return callback(new Error('活动时间不能超出专题活动时间范围'))
                  }
                  callback()
                }
              }
            ],
            props: {
              type: 'datetimerange',
              format: 'yyyy-MM-dd HH:mm',
              'value-format': 'yyyy-MM-dd HH:mm'
            }
          },
          eveDetail: {
            form: 'component',
            label: '活动详情',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入活动详情'
              }
            ],
            componentName: 'Tinymce'
          },
          coverIds: {
            form: 'component',
            label: '活动封面',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传活动封面'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return <div>建议上传388*144大小的图片</div>
            },
            props: {
              uploadData: {
                type: 'event'
              },
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 30
            }
          }
        }
      }
    }
  }
}
