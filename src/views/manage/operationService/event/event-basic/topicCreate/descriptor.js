import DictPopover from '../components/DictPopover'
import dayjs from 'dayjs'
import { integerValid } from '@/utils/validate'

export default {
  components: { DictPopover },
  data() {
    return {
      leftFormConfigure: {
        descriptors: {
          title: {
            form: 'input',
            label: '专题名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入专题名称'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          kind: {
            form: 'select',
            label: '活动大类',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择活动大类'
              }
            ],
            options: [],
            events: {
              change: this.kindChange
            },
            // customRight: () => {
            //   return (
            //     <div class="line-height-30 p-t-42">
            //       <dict-popover
            //         name={'活动大类'}
            //         type="category_type"
            //         onRefreshDict={() => {
            //           this.eventInfoDictList(0)
            //         }}
            //       />
            //     </div>
            //   )
            // }
          },
          type: {
            form: 'select',
            label: '活动类型',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择活动类型'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <div class="line-height-30 p-t-42">
                  {
                    this.fromModel.kind &&
                    <dict-popover
                      name={'活动类型'}
                      parentId={this.fromModel.kind || 0}
                      onRefreshDict={() => {
                        this.eventInfoDictList(this.fromModel.kind)
                      }}
                    />
                  }
                </div>
              )
            }
          },
          address: {
            form: 'input',
            label: '详细地址',
            rule: [
              {
                type: 'string',
                message: '请输入详细地址'
              }
            ],
            attrs: {
              maxLength: 100
            },
            customTips: () => {
              return (
                <div>如果活动地址统一，建议填写</div>
              )
            }
          },
          naAddress: {
            form: 'input',
            label: '导航地址',
            rule: [
              {
                type: 'string',
                message: '请选择导航地址'
              }
            ],
            attrs: {
              readonly: true
            },
            customRight: () => {
              return (
                <div class="line-height-30 p-t-42">
                  <el-button
                    disabled={!this.fromModel.location}
                    onClick={() => this.clearNaAddressHandle()}
                  >
                    清空
                  </el-button>
                </div>
              )
            },
            events: {
              focus: () => {
                this.mapVisible = true
              }
            }
          },
          eventTime: {
            form: 'dateRange',
            label: '专题活动时间',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择专题活动时间'
              },
              {
                validator: (rule, value, callback) => {
                  if (value) {
                    if (value && value.length > 1) {
                      let data1 = dayjs(value[1]).valueOf()
                      let flag = this.TopicEventCreate.$refs.rightForm.fromModel.signFlag
                      let signDeadline = this.TopicEventCreate.$refs.rightForm.fromModel.signLimitTime
                      let data2 = dayjs(signDeadline).valueOf()
                      if (
                        flag &&
                        signDeadline !== undefined &&
                        signDeadline !== ''
                      ) {
                        if (data1 < data2) {
                          callback(
                            new Error('活动结束时间不能小于报名截止时间')
                          )
                        } else {
                          callback()
                        }
                      } else {
                        callback()
                      }
                    } else {
                      callback()
                    }
                  } else {
                    callback()
                  }
                },
                trigger: ['change', 'blur']
              }
            ],
            props: {
              type: 'datetimerange',
              format: 'yyyy-MM-dd HH:mm',
              'value-format': 'yyyy-MM-dd HH:mm'
            }
          },
          eveDetail: {
            form: 'input',
            label: '专题简介',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入专题简介'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 500,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 20 }
            }
          },
          coverIds: {
            form: 'component',
            label: '专题封面',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传专题封面'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return <div>建议上传388*144大小的图片</div>
            },
            props: {
              uploadData: {
                type: 'event'
              },
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 30
            }
          },
          topWechat: {
            form: 'radio',
            label: '置顶到小程序',
            options: [
              {
                label: '置顶',
                value: true
              },
              {
                label: '不置顶',
                value: false
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择是否置顶到小程序'
              }
            ]
          }
        }
      },
      rightFormConfigure: {
        descriptors: {
          signFlag: {
            form: 'radio',
            label: '是否需要报名',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择是否需要报名'
              }
            ],
            options: [
              { label: '是', value: true },
              { label: '否', value: false },
            ]
          },
          signRange: {
            form: 'select',
            label: '活动范围',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择活动范围'
              }
            ],
            hidden: true,
            options: [
              { label: '个人普通用户可申请', value: 1 },
              { label: '企业管理员用户可申请', value: 2 },
              { label: '企业员工用户可申请', value: 3 }
            ],
            attrs: {
              multiple: true
            }
          },
          limitPerson: {
            form: 'input',
            label: '报名限制人数',
            hidden: true,
            placeholder: '请输入本次活动限制的最大报名人数',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入本次活动限制的最大报名人数'
              },
              {
                validator: integerValid
              }
            ],
            attrs: {
              maxLength: 5
            },
            customRight: () => {
              return (
                <div class={'line-height-32 p-t-42'}>
                  <span class="font-size-14 color-dark">人</span>
                </div>
              )
            }
          },
          signLimitTime: {
            form: 'date',
            label: '报名截止时间',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择报名截止时间'
              },
              {
                validator: (rule, value, callback) => {
                  if (value) {
                    let data1 = dayjs(value).valueOf()
                    let flag = this.fromModel.signFlag
                    let eventTime = this.TopicEventCreate.$refs.leftForm.fromModel.eventTime
                    if(!eventTime || !eventTime.length) return callback()
                    let startData = dayjs(eventTime[0]).valueOf()
                    if (flag) {
                      if (data1 > startData) {
                        callback(new Error('报名截止时间不能大于活动开始时间'))
                      } else {
                        callback()
                      }
                    } else {
                      callback()
                    }
                  } else {
                    callback()
                  }
                },
                trigger: ['change', 'blur']
              }
            ],
            props: {
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm',
              'value-format': 'yyyy-MM-dd HH:mm'
            }
          },
          limitCount: {
            form: 'component',
            label: '活动数报名限制',
            hidden: true,
            rule: [
              {
                type: 'string',
                message: '请输入活动数报名限制'
              },
              {
                validator: integerValid
              }
            ],
            render: () => {
              return (
                <div class={'flex align-items-center'}>
                  <span style={'flex-shrink: 0'}>单个用户最多可报名</span>
                  <el-input
                    style={'width: 150px'}
                    class={'m-l-4 m-r-4'}
                    placeholder={'默认不限制'}
                    v-model={this.fromModel.limitCount}
                    maxLength={4}
                  >
                    <span slot="append" class="color-black">
                      项
                    </span>
                  </el-input>
                  <span style={'flex-shrink: 0'}>活动</span>
                </div>
              )
            }
          },
          templateId: {
            form: 'select',
            label: '报名信息模版',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择报名信息模版'
              }
            ],
            options: [],
            events: {
              change: this.templateChange
            },
            customLabel: () => {
              return (<div class={'w100 flex justify-content-between align-items-center'}>
                <div>报名信息模版</div>
                <div class={'right-tips flex justify-content-between align-items-center'}>
                  <span class={'color-info'}>没有可用的报名表？</span>
                  <el-link type={'primary'} onClick={() => this.goTemplateHandle()}>去设置</el-link>
                </div>
              </div>)
            },
            customTips: () => {
              return (
                <div>
                  {
                    this.fromModel.templateId &&
                    <drive-table
                      columns={this.tableColumn}
                      tableData={this.tableData}
                    ></drive-table>
                  }
                </div>
              )
            }
          },
          smsFlag: {
            form: 'switch',
            label: '活动短信提醒',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'boolean'
              }
            ]
          },
          smsMinute: {
            form: 'component',
            label: '',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请设置短信提醒时间'
              },
            ],
            hidden: true,
            render: () => {
              return (
                <div class={'flex align-items-center'}>
                  <span style={'flex-shrink: 0'}>活动开始前</span>
                  <el-select
                    style={'width: 150px !important'}
                    class={'m-l-4 m-r-4'}
                    placeholder={'请选择'}
                    v-model={this.fromModel.smsMinute}
                  >
                    <el-option label={'1分钟'} value={1}></el-option>
                    <el-option label={'3分钟'} value={3}></el-option>
                    <el-option label={'5分钟'} value={5}></el-option>
                    <el-option label={'30分钟'} value={30}></el-option>
                    <el-option label={'1小时'} value={60}></el-option>
                    <el-option label={'2小时'} value={120}></el-option>
                  </el-select>
                  <span style={'flex-shrink: 0'}>短信提醒报名成功人员参加活动</span>
                </div>
              )
            }
          },
          qrcodeIds: {
            form: 'component',
            label: '活动群二维码',
            hidden: true,
            rule: [
              {
                type: 'array',
                message: '请上传活动群二维码'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return <div>上传已创建的活动微信群二维码，方便报名成功人员扫码进群</div>
            },
            props: {
              uploadData: {
                type: 'event'
              },
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 30
            }
          },
          qrCodeInfo: {
            form: 'input',
            label: '二维码备注信息',
            hidden: true,
            rule: [
              {
                type: 'string',
                message: '请输入二维码备注信息'
              }
            ],
            attrs: {
              maxLength: 50
            }
          }
        }
      }
    }
  }
}
