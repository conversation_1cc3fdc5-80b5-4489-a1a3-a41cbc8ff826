<template>
  <basic-card :title="$route.query.id ? '编辑专题活动' : '新增专题活动'">
    <el-row class="normal-container">
      <el-col :span="12" class="p-r-16">
        <topic-event-left-form ref="leftForm" />
      </el-col>
      <el-col :span="12" class="p-l-16">
        <topic-event-right-form ref="rightForm" @goTemplateHandle="goTemplateHandle" />
      </el-col>
    </el-row>
    <div class="btn-wrapper">
      <el-button type="info" @click="goBackHandle">取消</el-button>
      <el-button type="primary" @click="submitHandle">确定</el-button>
    </div>
  </basic-card>
</template>

<script>
import TopicEventLeftForm from './LeftForm'
import TopicEventRightForm from './RightForm'
import { eventInfoCreate, eventInfoGet, eventInfoUpdate } from '../api'

export default {
  name: "TopicEventCreate",
  components: { TopicEventLeftForm, TopicEventRightForm },
  provide() {
    return {
      TopicEventCreate: this
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    goTemplateHandle() {
      let routeUrl = this.$router.resolve({
        path: '/event/index/templateSetting'
      })
      window.open(routeUrl.href, '_blank')
    },
    initData() {
      const id = this.$route.query.id
      if(!id) return false
      eventInfoGet({ id }).then(res => {
        this.$refs.leftForm.initData(res)
        this.$refs.rightForm.initData(res)
      })
    },
    goBackHandle() {
      this.$router.go(-1)
    },
    requestHandle(row) {
      const coverIds = row.coverIds || []
      const qrcodeIds = row.qrcodeIds || []
      const params = {
        ...row,
        coverIds: coverIds.length ? coverIds.map(item => item.id) : [],
        qrcodeIds: qrcodeIds.length ? qrcodeIds.map(item => item.id) : [],
        scale: 2 // 1普通活动；2专题活动
      }
      if(this.$route.query.id) {
        params.id = this.$route.query.id
        eventInfoUpdate(params).then(() => {
          this.$toast.success('编辑成功')
          this.goBackHandle()
        })
      } else {
        eventInfoCreate(params).then(() => {
          this.$toast.success('创建成功')
          this.goBackHandle()
        })
      }
    },
    submitHandle() {
      Promise.all([
        this.$refs.leftForm.validateHandle(),
        this.$refs.rightForm.validateHandle()
      ]).then(res => {
        const params = {
          ...res[0],
          ...res[1]
        }
        this.requestHandle(params)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.normal-container {
  padding-bottom: 65px;
}
.btn-wrapper {
  height: 65px;
  position: fixed;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 999;
  @include background_color(--color-white);
  border-top-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  display: flex;
  align-items: center;
  justify-content: end;
  padding-right: 32px;
}
</style>
