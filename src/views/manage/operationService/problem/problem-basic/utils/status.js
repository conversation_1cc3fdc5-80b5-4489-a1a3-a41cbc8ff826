export function getProblemStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag isDot type="warning" label="待办理" />
    case 2:
      return <basic-tag isDot type="success" label="办理中" />
    case 3:
      return <basic-tag isDot type="primary" label="手动办结" />
    case 4:
      return <basic-tag isDot type="primary" label="系统自动办结" />
    case 5:
      return <basic-tag isDot type="primary" label="已回复" />
    default:
      return <basic-tag isDot type="warning" label="-" />
  }
}

export function getProblemStatusLabel(val) {
  switch (val) {
    case 1:
      return '待办理'
    case 2:
      return '办理中'
    case 3:
      return '手动办结'
    case 4:
      return '系统自动办结'
    case 5:
      return '已回复'
    default:
      return '-'
  }
}

export function getProblemTypeLabel(val) {
  switch (val) {
    case '1':
      return '工作学习'
    case '2':
      return '交通出行'
    case '3':
      return '物业管理'
    case '4':
      return '企业服务'
    default:
      return '-'
  }
}

export const typeOptions = [
  {
    label: '工作学习',
    value: 1
  },
  {
    label: '交通出行',
    value: 2
  },
  {
    label: '物业管理',
    value: 3
  },
  {
    label: '企业服务',
    value: 4
  }
]
