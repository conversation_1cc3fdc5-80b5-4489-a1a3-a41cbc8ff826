<template>
  <dialog-cmp
    :title="title"
    :visible.sync="comVisible"
    width="30%"
    @close="close"
    @confirmDialog="confirmDialog"
  >
    <driven-form
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
  </dialog-cmp>
</template>

<script>
export default {
  name: 'IssuedCmp',
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      comVisible: false,
      fromModel: {},
      formConfigure: {
        descriptors: {
          reply3: {
            form: 'input',
            label: '事项描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入事项描述'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 2,
              maxlength: 200,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            }
          },
          attachList: {
            form: 'component',
            label: '相关图片',
            rule: [
              {
                type: 'array',
                message: '请上传图片信息'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'park'
              },
              accept: 'image/*',
              mulity: true,
              limit: 3
            },
            customTips: () => {
              return (
                <div class="font-size-12 line-height-20">
                  请上传格式为png/jpg不大于10MB的图片，图片不得超过三个
                </div>
              )
            }
          }
        }
      }
    }
  },
  methods: {
    close() {
      this.$emit('update:visible', false)
    },
    confirmDialog() {
      this.$emit('confirmDialog')
    }
  },
  watch: {
    visible(val) {
      this.comVisible = val
    }
  }
}
</script>

<style scoped lang="scss"></style>
