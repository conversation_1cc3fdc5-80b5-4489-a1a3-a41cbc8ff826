<template>
  <div>
    <el-table
      :data="tableData"
      border
      :key="tableKey"
    >
      <el-table-column prop="parkName" label="园区"></el-table-column>
      <el-table-column prop="deptUserId" label="人员">
        <template slot-scope="scope">
          <el-cascader
            class="w100"
            v-model="scope.row.deptUserId"
            placeholder="请选择"
            @change="changeHandle"
            :props="props"
            clearable
          ></el-cascader>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import {deptList, getUserListByDept} from "@/api/common"

export default {
  name: "PersonCharge",
  data() {
    return {
      props: {
        lazy: true,
        lazyLoad: async(node, resolve) => {
          const { level } = node
          let nodes = []
          try {
            switch (level) {
              case 0:
                nodes = await deptList()
                break
              case 1:
                nodes = await getUserListByDept({ deptId: node.data.value })
                break
              default:
                nodes = []
            }
          } catch (e) {
            nodes = []
          }
          const resolveNodes = nodes.map(item => {
            const node = {
              value: item.id,
              label: item.name || item.nickname,
              leaf: level >= 1
            }
            return node
          })

          resolve(resolveNodes)
        }
      },
      tableData: [],
      tableKey: Math.random()
    }
  },
  methods: {
    initData(data) {
      this.tableData = data
      this.tableKey = this.$options.data().tableKey
    },
    changeHandle() {
      this.tableData.forEach(item => {
        item.manageId = item.deptUserId && item.deptUserId.length ? item.deptUserId[1] : ''
      })
      this.$emit('input', this.tableData)
    }
  }
}
</script>

<style scoped>

</style>