
export default {
  data() {
    return {
      tableColumn: [
        {
          label: '问题类型',
          prop: 'questionType'
        },
        {
          label: '问题负责人',
          prop: 'list',
          render: (h, scope) => {
            return (
              scope.row.list.map(item => (
                <div>{item.parkName} / {item.deptName} / {item.manageName}</div>
              ))
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
               <el-link
                 v-permission={this.routeButtonsPermission.EDIT}
                 type="primary"
                 onclick={() => {
                  this.editHandle(scope.row)
                 }}
                 class="m-r-15"
               >
                 {this.routeButtonsTitle.EDIT}
              </el-link>
              <el-link
                v-permission={this.routeButtonsPermission.DELETE}
                type="danger"
                onclick={() => {
                  this.deleteHandle(scope.row)
                }}
              >
                {this.routeButtonsTitle.DELETE}
              </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
