<template>
  <basic-card>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :table-data="tableData"
      :isSortable="isSortable"
      :sort-fn="sortQuestionRecordType"
      @saveDrag="getQuestionRecordTypeList"
    >
      <template v-slot:operate-right>
        <el-button
          v-permission="routeButtonsPermission.SORT"
          type="info"
          @click="sortableHandle(!isSortable)"
          :disabled="tableData.length < 2"
        >
          {{ isSortable ? `退出${routeButtonsTitle.SORT}` : routeButtonsTitle.SORT }}
        </el-button>
        <el-button
          v-permission="routeButtonsPermission.ADD"
          type="primary"
          @click="addHandle"
        >
          {{ routeButtonsTitle.ADD }}
        </el-button>
      </template>
    </drive-table>
    <dialog-cmp
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="820px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="dialogVisible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
        labelPosition="top"
      />
    </dialog-cmp>
  </basic-card>
</template>

<script>
import ColumnMixins from "./column"
import DescriptorMixins from './descriptor'
import {
  deleteQuestionRecordType,
  getPark,
  getQuestionRecordTypeList,
  sortQuestionRecordType,
  updateQuestionRecordType
} from "./api"
import {deepClone} from "@/utils/tools";

export default {
  name: "ProblemTypeManage",
  mixins: [ColumnMixins, DescriptorMixins],
  data() {
    return {
      sortQuestionRecordType,
      isSortable: false,
      tableData: [],
      dialogTitle: '新增',
      dialogVisible: false,
      fromModel: {
        sonList: []
      },
      parkList: []
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.dialogTitle = this.$options.data().dialogTitle
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  activated() {
    this.getPark()
    this.getQuestionRecordTypeList()
  },
  methods: {
    // 拖拽排序开启关闭
    sortableHandle(val) {
      this.isSortable = val
      if (val)
        this.$refs['drive-table'] &&
        this.$refs['drive-table'].initDrag(this.tableData)
    },
    getQuestionRecordTypeList() {
      getQuestionRecordTypeList().then(res => {
        this.tableData = res || []
      })
    },
    getPark() {
      getPark().then(res => {
        this.parkList = res.map(item => {
          return {
            parkName: item.label,
            parkId: item.key,
            deptUserId: []
          }
        })
      })
    },
    addHandle() {
      this.fromModel.sonList = deepClone(this.parkList)
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.personCharge?.initData(this.fromModel.sonList)
      })
    },
    editHandle(row) {
      const { list = [] } = deepClone(row)
      list.forEach(item => {
        item.deptUserId = item.deptId && item.manageId ? [item.deptId, item.manageId] : []
      })
      this.fromModel = {
        ...row,
        sonList: list
      }
      const parks = deepClone(this.parkList)
      parks.forEach(item => {
        const row = list.find(val => val.parkId === item.parkId)
        if (row && JSON.stringify(row) !== '{}') {
          item.deptUserId = row.deptUserId
        }
      })
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.personCharge?.initData(parks)
      })
    },
    deleteHandle(row) {
      this.$confirm('确定删除此类型？').then(() => {
        deleteQuestionRecordType({ id: row.id }).then(() => {
          this.$toast.success('删除成功')
          this.getQuestionRecordTypeList()
        })
      })
    },
    confirmDialog() {
      this.$refs["driven-form"].validate(valid => {
        if (!valid) return false
        const { id, sonList = [] } = this.fromModel
        const row = sonList.find(item => item.deptUserId && item.deptUserId.length)
        if (!row) return this.$toast.warning('至少需完成一项园区人员选择')
        const params = {
          ...this.fromModel,
          sonList: sonList.filter(item => item.manageId)
        }
        updateQuestionRecordType(params).then(() => {
          this.$toast.success(id ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.getQuestionRecordTypeList()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  width: 100%;
  display: flex;
  .custom-label {
    width: 100%;
  }
}
</style>