<template>
  <div class="flex min-h100">
    <basic-card>
      <basic-tab
        :tabs-data="tabsData"
        :current="extralQuerys.inStatus"
        @tabsChange="tabsChange"
        :disabled="reqLoading"
      />
      <drive-table
        ref="driveTable"
        :columns="tableColumn"
        :api-fn="getVisitList"
        :extral-querys="extralQuerys"
        @getTotal="reqLoading = false"
      >
        <template v-slot:operate-right>
          <el-button
            v-permission="routeButtonsPermission.ADD"
            type="primary"
            size="small"
            @click="addVisit"
          >
            <svg-icon icon-class="add" />
            <span>{{ routeButtonsTitle.ADD }}</span>
          </el-button>
        </template>
      </drive-table>
    </basic-card>

    <!-- 走访管理问卷弹窗 -->
    <el-drawer
      title="我是标题"
      :visible.sync="drawer"
      size="100%"
      append-to-body
      destroy-on-close
      :withHeader="false"
    >
      <visit-detail v-if="drawer" :id="id" :tab="tab" :viewType="viewType" />
    </el-drawer>
    <dialog-cmp
      title="复制问卷"
      :visible.sync="visible"
      @closeDialog="visible = false"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixin from './column'
import {
  getVisitList,
  deleteVisitQuestion,
  releaseVisitQuestion,
  checkTopping,
  interviewTopping,
  copyPaper
} from './api'
import VisitDetail from './visitDetailSpecial'
import { endConfirm, releaseConfirm } from './visitDetailSpecial/util'
import BasicTab from '@/components/BasicTab'

export default {
  name: 'VisitBasic',
  mixins: [ColumnMixin],
  components: { BasicTab, VisitDetail },
  provide() {
    return {
      visitManage: this
    }
  },
  data() {
    return {
      visible: false,
      formConfigure: {
        descriptors: {
          paperName: {
            form: 'input',
            label: '问卷名称',
            rule: [
              {
                type: 'string',
                required: true,
                message: '请输入问卷名称'
              }
            ],
            attrs: {
              maxlength: 30
            }
          }
        }
      },
      fromModel: {},
      tabsData: [
        { label: '起草中', value: 1 },
        { label: '发布中', value: 2 },
        { label: '已结束', value: 3 }
      ],
      extralQuerys: {
        inStatus: 1
      },
      reqLoading: false,
      getVisitList,
      drawer: false,
      id: -1,
      tab: 'design',
      viewType: '' // see 查看  edit 编辑
    }
  },
  methods: {
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          copyPaper(this.fromModel).then(() => {
            this.$message.success('复制成功')
            this.visible = false
            this.$refs.driveTable?.refreshTable()
          })
        }
      })
    },
    copyVisit(id) {
      this.visible = true
      this.fromModel = {
        paperName: '',
        paperId: id
      }
    },
    notTopHandle(id) {
      const params = {
        paperId: id,
        topping: 0
      }
      this.toppingHandle(params, '取消置顶')
    },
    toppingHandle(params, text) {
      interviewTopping(params).then(() => {
        this.$toast.success(text + '成功')
        this.refreshTable()
      })
    },
    async topHandle(id) {
      const isCheck = await checkTopping()
      const params = {
        paperId: id,
        topping: 1
      }
      if (isCheck) {
        this.$confirm('已存在其他置顶，确认置顶将取消其他置顶？', '提示').then(
          async () => {
            this.toppingHandle(params, '置顶')
          }
        )
      } else {
        this.toppingHandle(params, '置顶')
      }
    },
    tabsChange(e) {
      this.reqLoading = true
      this.extralQuerys.inStatus = e
      this.$refs.driveTable?.refreshResetTable()
    },
    // 添加问卷
    addVisit() {
      this.tab = 'design'
      this.viewType = ''
      this.openDrawer()
    },
    // 打开抽屉
    openDrawer() {
      this.$nextTick(() => {
        this.drawer = true
      })
    },
    // 关闭抽屉
    closeDrawer() {
      this.drawer = false
    },
    // 查看、编辑
    editVisit(id, type) {
      this.id = id
      this.tab = 'design'
      this.viewType = type
      this.openDrawer()
    },
    // 查看分析
    analysisVisit(id) {
      this.id = id
      this.viewType = 'see'
      this.tab = 'analysis'
      this.openDrawer()
    },
    endVisit(id) {
      endConfirm().then(() => {
        releaseVisitQuestion(id, 3).then(() => {
          this.$toast.success('问卷结束成功')
          this.refreshTable()
        })
      })
    },
    // 发布问卷
    releaseVisit(id) {
      releaseConfirm().then(() => {
        releaseVisitQuestion(id, 2).then(() => {
          this.$toast.success('问卷发布成功')
          this.refreshTable()
        })
      })
    },
    // 删除问卷
    delVisit(id) {
      this.$confirm('删除问卷后将丢失历史已收集数据，是否继续?', '删除问卷', {
        type: 'error'
      }).then(() => {
        deleteVisitQuestion(id).then(async () => {
          this.$toast.success('删除问卷成功')
          await this.$nextTick()
          this.$refs.driveTable?.refreshResetTable()
        })
      })
    },
    refreshTable() {
      this.$refs.driveTable.refreshTable()
    }
  },
  watch: {
    drawer(value) {
      if (!value) {
        this.refreshTable()
        this.id = -1
      }
    }
  },
  activated() {
    if (this.executeActivated) {
      this.$refs.driveTable && this.$refs.driveTable.refreshTable()
    }
  }
}
</script>

<style lang="scss" scoped></style>
