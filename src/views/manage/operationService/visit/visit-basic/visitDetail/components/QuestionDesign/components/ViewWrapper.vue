<template>
  <div
    class="question-view-wrapper view-edit-wrapper"
    :class="{ disabled: disabled }"
  >
    <!-- 可拖拽区域 -->
    <div class="drag-move">
      <img src="../../../images/dots-horizontal.png" alt="" />
    </div>
    <!-- 可查看区域 -->
    <div class="view-content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'ViewWrapper',
  props: {
    disabled: Boolean
  }
}
</script>

<style lang="scss" scoped>
.question-view-wrapper {
  padding: 24px 48px;
  border: 1px solid transparent;
  cursor: pointer;
  &:hover {
    box-shadow: 0 0 8px rgba(31, 35, 41, 0.05);
    border-color: #dee0e3;
  }
  &.disabled {
    .drag-move {
      display: none;
    }
    &:hover {
      box-shadow: 0 0 0 rgba(31, 35, 41, 0.05);
      border-color: transparent;
    }
  }
}
</style>
