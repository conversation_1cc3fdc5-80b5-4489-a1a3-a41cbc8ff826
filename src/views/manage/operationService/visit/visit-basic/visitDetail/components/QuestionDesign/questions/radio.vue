<template>
  <div class="question-radio-wrapper" @click.stop="handleClick">
    <!-- 非编辑状态 -->
    <view-wrapper v-if="!isEdit" :disabled="disabled">
      <div class="radio-wrapper">
        <!-- 标题区域 -->
        <div class="view-name-require">
          <!-- 名称 -->
          <div class="name">{{ dataset.name || nameTemp }}</div>
          <!-- 必填非必填标识 -->
          <div class="tag">
            <el-tag :type="isRequired(dataset.required).type" size="mini">
              {{ isRequired(dataset.required).text }}
            </el-tag>
          </div>
        </div>
        <!-- 选项区域 -->
        <div class="options-area">
          <div
            class="option"
            v-for="option in dataset.options"
            :key="option.id"
          >
            <el-radio :label="option.id" readonly>{{
              option.label || labelTemp
            }}</el-radio>
          </div>
        </div>
      </div>
    </view-wrapper>
    <!-- 编辑状态 -->
    <edit-wrapper @del="del" v-else>
      <div class="radio-edit-wrapper">
        <!-- 名称和必填非必填 -->
        <div class="edit-name-require">
          <!-- 名称 -->
          <div class="name">
            <el-input
              ref="autoFocus"
              v-model.trim="dataset.name"
              type="textarea"
              resize="none"
              autosize
              placeholder="请输入问题"
            ></el-input>
          </div>
          <!-- 必填非必填 -->
          <div class="required">
            <el-select
              class="required-select"
              v-model="dataset.required"
              placeholder=""
              size="small"
            >
              <el-option label="必填项" :value="1"> </el-option>
              <el-option label="非必填" :value="0"> </el-option>
            </el-select>
          </div>
        </div>
        <!-- 填写选项 -->
        <div class="edit-options" @click.stop>
          <!-- 选项可拖拽区域 -->
          <div class="options-area">
            <!-- 可拖拽选项 -->
            <draggable v-model="dataset.options" v-bind="dragOptions">
              <transition-group>
                <div
                  class="option"
                  handle=".drop-icon"
                  v-for="option in dataset.options"
                  :key="option.id"
                >
                  <!-- 可拖拽区域按钮 -->
                  <div class="drop-icon"></div>
                  <!-- radio展示 -->
                  <div class="box-area">
                    <el-radio :value="0" readonly></el-radio>
                  </div>
                  <!-- 输入框 -->
                  <div class="input-area">
                    <el-input
                      v-model="option.label"
                      type="textarea"
                      placeholder="请输入"
                      resize="none"
                      rows="1"
                      autosize
                    ></el-input>
                  </div>
                  <!-- 删除按钮 -->
                  <div class="delete-icon" @click="delOption(option.id)"></div>
                </div>
              </transition-group>
            </draggable>
            <!-- 添加选项区域 -->
            <div class="add-option option">
              <!-- radio展示 -->
              <div class="box-area">
                <el-radio :value="0" readonly></el-radio>
              </div>
              <!-- 添加选项 -->
              <span @click="addOption">添加选项</span>
            </div>
          </div>
        </div>
      </div>
    </edit-wrapper>
  </div>
</template>

<script>
import ViewWrapper from '../components/ViewWrapper'
// 编辑的混入方法
import editStateMixin from '../mixins/editState'
// 必填非必填混入方法
import requireStateMixin from '../mixins/requireState'
// 选项操作混入
import optionStateMixin from '../mixins/optionState'

export default {
  name: 'QuestionRadio',
  components: { ViewWrapper },
  mixins: [editStateMixin, requireStateMixin, optionStateMixin],
  data() {
    const id = this.id
    return {
      // radio数据
      dataset: {
        id,
        type: 'radio',
        name: '',
        required: 1,
        options: [
          { label: '', id: 0 },
          { label: '', id: 1 }
        ]
      },
      nameTemp: '单选题',
      labelTemp: '选项'
    }
  }
}
</script>

<style lang="scss" scoped>
.question-radio-wrapper {
  .radio-wrapper {
    position: relative;
  }
  .radio-edit-wrapper {
    position: relative;
  }
}
</style>
