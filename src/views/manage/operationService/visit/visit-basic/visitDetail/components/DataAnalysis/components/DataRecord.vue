<template>
  <div class="data-record">
    <!-- 表格区域 -->
    <option-table
      :props="tableProps"
      :tableData="tableData"
      @rowClick="rowClick"
      showArrow
      pagination
    />

    <!-- 文件填写详情弹窗区域 -->
    <el-drawer
      :title="answers.paperName"
      :visible.sync="drawer"
      :modal="false"
      destroy-on-close
      size="680px"
    >
      <div class="answer-detail-wrapper wh100">
        <div class="info">
          <div class="name-time">
            <div class="name">{{ answers.entName }}</div>
            <div class="time">{{ answers.createTime }}</div>
          </div>
          <p class="user">
            <span>填写人:</span>
            <span>{{ answers.answerName }}</span>
            <span>{{ answers.positionStr }}</span>
            <span>{{ answers.phoneNumber }}</span>
          </p>
        </div>
        <div class="answer-content">
          <el-scrollbar ref="scrollBar" class="wh100">
            <div class="answer-list">
              <div
                class="list"
                v-for="(answer, index) in answers.interviewQuestionVos"
                :key="answer.questionId"
              >
                <div class="num">{{ completeNumber(index) }}.</div>
                <div class="question-answer">
                  <div class="question">{{ answer.qsTitle }}</div>
                  <template
                    v-if="
                      answer.qsType === 'radio' || answer.qsType === 'checkbox'
                    "
                  >
                    <div
                      class="answer"
                      v-for="option in answer.options"
                      :key="option.optionId"
                    >
                      <span v-if="option.checkValue">{{
                        option.opDescribe
                      }}</span>
                    </div>
                  </template>
                  <template v-else>
                    <div class="answer">
                      {{ answer.content }}
                    </div>
                  </template>
                </div>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import OptionTable from './OptionTable.vue'
import { getAnalysisRecord, getQuestionAnswerDetail } from '../../../../api'

export default {
  name: 'DataRecord',
  components: { OptionTable },
  inject: ['paperId'],
  data() {
    return {
      drawer: false,
      tableData: [],
      tableProps: [
        { label: '企业名称', prop: 'entName' },
        { label: '姓名', prop: 'answerName' },
        { label: '填写时间', prop: 'recordTime' }
      ],
      answers: {},
      questionType: {
        radio: '单选题',
        checkbox: '多选题',
        completion: '问答题'
      }
    }
  },
  computed: {
    visitId() {
      return this.paperId()
    }
  },
  methods: {
    rowClick(row) {
      this.getQuestionAnswerDetail(row.recordId)
      this.$nextTick(() => {
        this.drawer = true
      })
    },
    completeNumber(index) {
      ++index
      return index < 10 ? `0${index}` : index
    },
    getAnalysisRecord() {
      if (this.visitId > -1) {
        getAnalysisRecord(this.visitId).then(res => {
          this.tableData = res.list
        })
      }
    },
    getQuestionAnswerDetail(recordId) {
      getQuestionAnswerDetail(recordId).then(res => {
        this.answers = res
      })
    }
  },
  created() {
    this.getAnalysisRecord()
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
  .el-scrollbar__view {
    height: 100%;
  }
}
:deep(.el-drawer__wrapper) {
  .el-drawer__header {
    margin-bottom: 8px;
    span {
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #1f2329;
    }
  }
  .info {
    margin: 0 24px;
    padding: 16px 0;
    color: #1f2329;
    font-size: 14px;
    line-height: 22px;
    border-bottom: 1px solid #dee0e3;
    height: 85px;
    .name-time {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      .name {
        color: #f4873c;
      }
    }
    .user {
      span {
        display: inline-block;
        margin-right: 8px;
      }
    }
  }
  .answer-content {
    height: calc(100% - 85px);
    .answer-list {
      padding: 16px 24px;
      font-size: 16px;
      line-height: 24px;
      .list {
        display: flex;
        margin-bottom: 16px;
        .num {
          color: #8f959e;
          padding-right: 8px;
        }
        .question-answer {
          .question {
            color: #646a73;
            margin-bottom: 8px;
          }
          .answer {
            padding: 2px 8px 2px 0;
          }
        }
      }
    }
  }
}
</style>
