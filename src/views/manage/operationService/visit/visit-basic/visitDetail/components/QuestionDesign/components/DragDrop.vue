<template>
  <div
    class="drag-dop"
    :class="active && 'active'"
    @dragleave="dragleave($event)"
    @dragover="dragover($event)"
    @drop="drop($event)"
  >
    <div class="content">
      <div class="click">
        <img v-if="!active" src="../../../images/click.png" alt="" />
        <img v-else src="../../../images/click-active.png" alt="" />
        <span>点击右侧题型</span>
      </div>
      <span class="or">或</span>
      <div class="draggle">
        <img v-if="!active" src="../../../images/move.png" alt="" />
        <img v-else src="../../../images/move-active.png" alt="" />
        <span>拖拽题型到此区域</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DragDrop',
  data() {
    return {
      active: false
    }
  },
  methods: {
    // 拖放离开区域
    dragleave() {
      this.active = false
    },
    // 类型拖放的经过时
    dragover(e) {
      // 一定要执行preventDefault(),否则drop事件不会被触发
      e.preventDefault()
      this.active = true
    },
    // 类型拖放放置时
    drop() {
      this.active = false
      // 派发到编辑区
      this.$emit('drop', this.type)
    },
    // 绑定拖放事件，临时存储问题类型拖放的类型
    onTypeStart() {
      this.$bus.$on('typeStart', this.typeStartEvent)
    },
    // 解绑类型拖放事件
    offTypeStart() {
      this.$bus.$off('typeStart', this.typeStartEvent)
    },
    // 类型拖放绑定的事件
    typeStartEvent(data) {
      this.type = data
    }
  },
  created() {
    this.onTypeStart()
  },
  destroyed() {
    this.offTypeStart()
  }
}
</script>

<style lang="scss" scoped>
.drag-dop {
  width: 100%;
  padding: 36px 20px;
  border: 1px dashed #dee0e3;
  color: #646a73;
  font-size: 14px;
  &.active {
    border-color: #f4873c;
    color: #f4873c;
  }
  // 阻止子元素干扰父级的drag事件
  & * {
    pointer-events: none;
  }
  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    .click,
    .draggle {
      display: flex;
      align-items: center;
      img {
        margin-right: 4px;
      }
    }
    .or {
      margin: 0 8px;
    }
  }
}
</style>
