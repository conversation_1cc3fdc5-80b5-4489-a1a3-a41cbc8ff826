<template>
  <v-echarts :options="chartData" autoresize></v-echarts>
</template>

<script>
import VEcharts from 'vue-echarts'
import 'echarts/lib/chart/bar'
import 'echarts/lib/component/tooltip'

export default {
  name: '<PERSON><PERSON><PERSON>',
  components: { VEcharts },
  props: {
    dataset: Array
  },
  data() {
    return {
      chartData: {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: 'rgba(51, 112, 255, 0.05)'
            }
          },
          formatter: '{b}: {c}'
        },
        grid: {
          top: '10%',
          bottom: '10%',
          left: '4%',
          right: '1%'
        },
        xAxis: {
          type: 'category',
          data: [],
          boundaryGap: true,
          alignWithLabel: true,
          axisLine: {
            lineStyle: {
              color: '#a0a0a0'
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#a0a0a0'
            }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            lineStyle: {
              color: ['#e8e8e8']
            }
          }
        },
        series: [
          {
            data: [],
            type: 'bar',
            barMaxWidth: 50
          }
        ],
        color: ['#3d77ff']
      }
    }
  },
  watch: {
    dataset: {
      handler(data) {
        const names = []
        const values = []
        data.forEach(item => {
          const { name, value } = item
          names.push(name)
          values.push(value)
        })
        this.$set(this.chartData.xAxis, 'data', names)
        this.$set(this.chartData.series[0], 'data', values)
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.echarts {
  width: 100% !important;
  height: 100% !important;
}
</style>
