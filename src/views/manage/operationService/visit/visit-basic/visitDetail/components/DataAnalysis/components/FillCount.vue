<template>
  <div class="fill-count">
    <!-- 数量区域 -->
    <div class="count-area">
      <div class="icon"></div>
      <p>
        <span class="count">{{ count }}</span>
        <span class="desc">份回收记录</span>
      </p>
    </div>
    <!-- 导出区域 -->
    <div v-if="isRelease" class="export-area" @click="canExport">
      <div class="icon"></div>
      <span>导出填写记录</span>
    </div>
  </div>
</template>

<script>
import { checkExportStatus, exportVisitAnswerUrl } from '../../../../api'

export default {
  name: 'FillCount',
  inject: ['paperId', 'questionDesignRecord'],
  props: {
    count: {
      type: Number,
      default: 0
    },
    id: Number
  },
  computed: {
    // 判断是否发布
    isRelease() {
      const questionDesignRecord = this.questionDesignRecord()
      return questionDesignRecord && questionDesignRecord.inStatus === 0
    }
  },
  methods: {
    // 判断是否可以导出
    canExport() {
      checkExportStatus(this.paperId()).then(() => {
        this.exportRecord()
      })
    },
    // 导出问卷填写记录
    exportRecord() {
      if (this.timer) clearTimeout(this.timer)
      const link = document.createElement('a')
      link.href = exportVisitAnswerUrl(this.paperId())
      link.setAttribute('download', '问卷导出')
      link.style.display = 'none'
      document.body.appendChild(link)
      this.timer = setTimeout(() => {
        link.click()
        document.body.removeChild(link)
      }, 66)
    }
  }
}
</script>

<style lang="scss" scoped>
.fill-count {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .count-area,
  .export-area {
    display: flex;
    align-items: center;
  }
  .count-area {
    .icon {
      width: 40px;
      height: 40px;
      background-color: #f0f4ff;
      background-image: url('../../../images/count.png');
      background-repeat: no-repeat;
      background-size: 18px 22px;
      background-position: 50% 50%;
      border-radius: 20px;
      margin-right: 8px;
    }
    p {
      display: flex;
      align-items: flex-end;
      font-size: 16px;
      color: #1f2329;
      .count {
        font-size: 24px;
        font-weight: 600;
        margin-right: 4px;
      }
      .desc {
        position: relative;
        top: -3px;
      }
    }
  }
  .export-area {
    font-size: 14px;
    cursor: pointer;
    .icon {
      width: 16px;
      height: 16px;
      background: url('../../../images/excel.png') no-repeat;
      margin-right: 4px;
    }
  }
}
</style>
