import EditWrapper from '../components/EditWrapper.vue'

export default {
  components: { EditWrapper },
  props: {
    edit: Boolean, // 是否为编辑状态
    id: Number, // 模块id
    data: Object, // 问题数据
    disabled: Boolean
  },
  data() {
    return {
      isEdit: false // 编辑状态
    }
  },
  inject: ['main'],
  methods: {
    // 文本/n替换成<br>
    textToHtml(text) {
      const brText  = text.replace(/\n/g, '<br>')
      return brText.replace(/ /g, '&nbsp;')
    },
    // 点击最外层题目组件的外层
    handleClick() {
      if (this.disabled) return
      // 打开状态忽略
      // if (this.isEdit) {
      //   return;
      // }
      // 关闭全部的组件编辑状态
      this.$bus.$emit('closeEdit')
      // 打开自身的编辑状态
      this.openEdit()
    },
    // 关闭编辑状态
    closeEdit() {
      this.isEdit = false
    },
    // 打开编辑状态
    openEdit() {
      if (this.disabled) return
      this.main.tabParameterHandle(this.data)
      this.isEdit = true
      this.focus()
    },
    // 获取焦点
    focus() {
      // input自动获取焦点
      this.$nextTick(() => {
        const autoFocus = this.$refs.autoFocus
        autoFocus && autoFocus.focus()
      })
    },
    // 删除回调
    del() {
      this.$emit('del', this.id)
    },
    // 格式化数据
    getData() {
      const data = JSON.parse(JSON.stringify(this.dataset))
      data.name = data.name || this.nameTemp
      data.options = data.options.map(option => {
        const obj = {}
        obj.attachList = option.attachList || []
        obj.label = option.label || this.labelTemp
        obj.id = option.id
        obj.remark = option.remark || ''
        obj.isRemark = !!option.remark
        obj.other = option.other || ''
        obj.showOther = option.showOther
        return obj
      })
      console.log('data-----', data)
      return data
    }
  },
  watch: {
    // isEdit初始化为props的edit时
    // 会出现值无法更新问题
    edit: {
      handler(val) {
        // 异步处理
        setTimeout(() => {
          this.isEdit = val
          if (val) this.main.tabParameterHandle(this.data)
        }, 0)
      },
      immediate: true
    },
    data: {
      handler(val) {
        // 异步处理
        setTimeout(() => {
          if (val) {
            this.dataset = val
          }
        }, 0)
      },
      immediate: true
    }
  },
  created() {
    // 初始化绑定closeEdit的bus方法
    this.$bus.$on('closeEdit', () => {
      this.closeEdit()
    })
  },
  destroy() {
    // 销毁时解除绑定
    this.$bus.$off('closeEdit', () => {
      this.closeEdit()
    })
  }
}
