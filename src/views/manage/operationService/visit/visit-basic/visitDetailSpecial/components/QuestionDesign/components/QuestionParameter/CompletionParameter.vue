<template>
  <div class="parameter-item limit-content-container">
    <div class="m-b-12">输入内容限制</div>
    <el-select
      v-model="contentType"
      placeholder="请选择"
      @change="updateParent"
    >
      <el-option
        v-for="item in limitContentOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
      </el-option>
    </el-select>
    <!--    <div v-if="contentType === 'numeric' || contentType === 'wordCount'">-->
    <!--      <el-checkbox class="m-t-12" v-model="setNum"-->
    <!--        >设置最大和最小值</el-checkbox-->
    <!--      >-->
    <!--      <template v-if="setNum">-->
    <!--        <el-input-->
    <!--          class="m-t-12"-->
    <!--          placeholder="请输入数值"-->
    <!--          v-model.number="minNum"-->
    <!--          type="number"-->
    <!--          @input="minNumInput"-->
    <!--        >-->
    <!--          <template slot="prepend">最小值</template>-->
    <!--        </el-input>-->
    <!--        <el-input-->
    <!--          class="m-t-12"-->
    <!--          placeholder="请输入数值"-->
    <!--          v-model.number="maxNum"-->
    <!--          type="number"-->
    <!--          @input="maxNumInput"-->
    <!--        >-->
    <!--          <template slot="prepend">最大值</template>-->
    <!--        </el-input>-->
    <!--      </template>-->
    <!--    </div>-->
    <!--    <div v-if="contentType" class="m-t-12">-->
    <!--      <el-checkbox v-model="setErrorMsg">自定义错误提示</el-checkbox>-->
    <!--      <el-input-->
    <!--        v-if="setErrorMsg"-->
    <!--        class="m-t-12"-->
    <!--        placeholder="当内容不符时，向用户展示的提示"-->
    <!--        v-model="errorMsg"-->
    <!--      />-->
    <!--    </div>-->
  </div>
</template>

<script>
export default {
  name: 'CompletionParameter',
  data() {
    return {
      contentType: '', // 限制内容
      limitContentOptions: [
        {
          label: '不限制',
          value: ''
        },
        {
          label: '数值',
          value: 'numeric'
        },
        // {
        //   label: '字数',
        //   value: 'wordCount'
        // },
        {
          label: '电子邮箱',
          value: 'email'
        },
        {
          label: '网址',
          value: 'url'
        }
      ],
      setNum: false, // 设置最大最小值
      minNum: '', // 最小值
      maxNum: '', // 最大值
      setErrorMsg: false, // 自定义错误提示
      errorMsg: '' // 错误提示语
    }
  },
  methods: {
    initData(data) {
      this.contentType = data.contentType || ''
    },
    updateParent(val) {
      const params = {
        contentType: val
      }
      this.$emit('updateParent', params)
    },
    minNumInput(value) {
      if (value < 0 && this.contentType === 'wordCount')
        this.minNum = Math.abs(value)
    },
    maxNumInput(value) {
      if (value < 0 && this.contentType === 'wordCount')
        this.maxNum = Math.abs(value)
    }
  }
}
</script>

<style scoped lang="scss">
.limit-content-container {
  :deep(.el-select) {
    width: 100%;
  }
  :deep(.el-input-group__prepend) {
    color: rgba(0, 0, 0, 0.65);
  }
}
</style>
