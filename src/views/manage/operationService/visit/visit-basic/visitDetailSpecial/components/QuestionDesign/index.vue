<template>
  <div class="visit-question wh100">
    <!-- 问题类型选择 -->
    <div v-if="!releaseOrEnd" class="question-types">
      <question-types />
    </div>
    <!-- 问题编辑区 -->
    <el-scrollbar ref="scrollBar" class="wh100">
      <div class="question-edit">
        <div class="question-area">
          <question-edit
            ref="questionEdit"
            @closeEdit="closeEdit"
            @listHandle="listHandle"
          />
        </div>
      </div>
    </el-scrollbar>
    <!-- 问题参数设置 -->
    <div v-if="!releaseOrEnd" class="question-parameter" id="questionParameter">
      <question-parameter
        :visible.sync="questionParameter"
        ref="questionParameter"
      />
    </div>
  </div>
</template>

<script>
import QuestionTypes from './components/QuestionTypes.vue'
import QuestionEdit from './components/QuestionEdit.vue'
import QuestionParameter from './components/QuestionParameter'

export default {
  name: 'QuestionDesign',
  provide() {
    return {
      main: this
    }
  },
  data() {
    return {
      questionParameter: false
    }
  },
  inject: ['questionDesignRecord'],
  components: { QuestionParameter, QuestionTypes, QuestionEdit },
  computed: {
    // 已发布或结束
    releaseOrEnd() {
      const questionDesignRecord = this.questionDesignRecord()
      return (
        questionDesignRecord &&
        (questionDesignRecord.inStatus === 2 ||
          questionDesignRecord.inStatus === 3)
      )
    }
  },
  methods: {
    // 题目参数设置更新
    listHandle(val) {
      this.$refs.questionParameter?.updateData(val)
    },
    closeEdit() {
      this.questionParameter = false
    },
    editRequired(params) {
      this.$refs.questionEdit.editParameter(params)
    },
    // 获取题目参数设置
    tabParameterHandle(data) {
      this.questionParameter = false
      if (!data) return
      const isFirst = this.$refs.questionEdit.lists.length === 1
      const timer = isFirst ? 0 : 300
      setTimeout(async () => {
        this.questionParameter = true
        await this.$nextTick()
        await this.$refs.questionParameter.initData(data)
      }, timer)
    },
    // 获取编辑区的问题编辑数据
    getQuestionEditList() {
      return this.$refs.questionEdit.getFormateDataset()
    },
    // 获取编辑区的列表数据
    getLists() {
      return this.$refs.questionEdit.getLists()
    },
    // 向编辑区追加数据
    // 为题目类型区域点击事件做中转
    pushQuestionEditList(data) {
      return this.$refs.questionEdit.lists.push(data)
    },
    // 更新scrollbar
    // 解决高度增加无滚动条问题
    updateScrollBar() {
      this.$nextTick(() => {
        this.scrollBar.update()
      })
    },
    // 滚动到底部
    scrollToBottom() {
      this.scrollBar.wrap.scrollTop = this.scrollBar.wrap.scrollHeight
    }
  },
  mounted() {
    this.scrollBar = this.$refs.scrollBar
  }
}
</script>

<style lang="scss">
.w100 {
  width: 100%;
}
.h100 {
  height: 100%;
}
.wh100 {
  width: 100%;
  height: 100%;
}

.p-l-48 {
  padding-left: 48px;
}

.p-r-48 {
  padding-right: 48px;
}

.visit-question {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  max-height: 100%;
  overflow: hidden;
  position: relative;
  &.pointer-none {
    pointer-events: none;
  }
  .el-scrollbar__wrap {
    overflow-x: hidden !important;
    .el-scrollbar__view {
      height: 100%;
    }
  }

  .question-edit {
    //width: calc(100% - 286px);
    width: 100%;
    margin: 0 auto;
    flex: 1;
    background-color: #eff0f1;
    min-height: 100%;
    display: flex;
    justify-content: center;
    padding: 20px 24px;
    .question-area {
      width: 100%;
      height: 100%;
      max-width: 760px;
      background: #fff;
      padding: 0 0 24px 0;
      // 编辑区拖拽时样式调整
      .type-item-wrapper {
        margin-bottom: 0;
        border: none;
        border-radius: 0;
        background-color: #f6f8f9;
      }
    }
  }
  .question-types {
    flex: 0 0 286px;
    background-color: #fff;
    height: 100%;
    padding: 16px 20px;
    .types-wrapper {
      font-size: 14px;
      user-select: none;
    }
  }
  .question-parameter {
    width: 286px;
    height: 100%;
    flex: 0 0 286px;
    //position: absolute;
    //right: 0;
    //z-index: 999;
    .types-wrapper {
      font-size: 14px;
      user-select: none;
    }
  }

  // 问题选型的css样式
  // 为了编辑区样式不丢失
  .type-item-wrapper {
    padding-left: 16px;
    padding-right: 16px;
    margin-bottom: 12px;
    height: 36px;
    border-radius: 4px;
    border: 1px solid rgba(31, 35, 41, 0.15);
    cursor: pointer;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:hover {
      background-color: #f5f6f7;
      .plus {
        display: inline-block;
      }
    }
    .icon-label {
      display: flex;
      align-items: center;
      .icon {
        width: 16px;
        height: 16px;
      }
      span {
        margin-left: 8px;
      }
    }
    .plus {
      width: 16px;
      height: 16px;
      color: #646a73;
      display: none;
    }
  }
}

// 可拖拽区域公共样式
.view-edit-wrapper {
  position: relative;
  &:hover {
    .drag-move {
      display: flex;
    }
  }
  .drag-move {
    position: absolute;
    width: 100%;
    height: 24px;
    top: 0;
    left: 0;
    cursor: move;
    justify-content: center;
    align-items: center;
    display: none;
    z-index: 1;
  }
}

// 非编辑情况下公共样式
.view-name-require {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  .name {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    word-wrap: break-word;
    word-break: break-all;
  }
  .tag {
    margin-left: 8px;
  }
}

// 美化显示的radio、checkbox
.options-area {
  .option {
    word-wrap: break-word;
    word-break: break-all;
    margin-top: 12px;
    .el-select {
      width: 100%;
    }
    .option-label {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .label-item {
        padding: 10px 12px;
        background: #f5f6f7;
        border-radius: 4px;
        margin-right: 12px;
        margin-bottom: 12px;
        color: #1f2329;
        font-size: 14px;
      }
    }
    .el-radio__inner,
    .el-checkbox__inner {
      width: 18px;
      height: 18px;
      border-color: #8f959e;
      cursor: default;
      &:focus,
      &:active {
        border-color: #8f959e;
      }
    }
    .el-radio__label,
    .el-checkbox__label {
      padding-left: 8px;
      white-space: break-spaces;
    }
  }
}

// radio checkbox编辑情况下通用样式
.edit-name-require {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .name {
    flex: 1;
  }
  .required {
    //flex: 0 0 100px;
    margin-left: 16px;
    padding: 0 10px;
    background-color: #f5f6f7;
    height: 30px;
    line-height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    overflow: hidden;
    font-size: 14px;
    .el-select.required-select {
      .el-input__inner {
        padding: 0 0 0 12px;
        background-color: #f5f6f7;
        border: 1px solid #f5f6f7;
        color: #1f2329;
        height: 30px;
        line-height: 30px;
        display: flex;
        align-items: center;
        border-radius: 3px;
        overflow: hidden;

        &:focus,
        &:active {
          border-color: #f4873c;
        }
        .el-select__caret {
          color: #1f2329;
          line-height: 30px;
        }
      }
    }
  }
}
.edit-options {
  .options-area {
    .option {
      word-wrap: break-word;
      word-break: break-all;
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      margin-left: -48px;
      margin-right: -48px;
      padding: 0 48px;
      &:hover {
        .drop-icon {
          display: block;
        }
      }
      .drop-icon {
        display: none;
        position: absolute;
        width: 48px;
        height: 30px;
        background-image: url('../../images/dots.png');
        background-repeat: no-repeat;
        background-size: 24px 24px;
        background-position: 50% 50%;
        left: 0;
        top: 0;
        cursor: move;
      }
      .box-area {
        flex: 0 0 26px;
        padding-right: 8px;
        .el-radio__label {
          padding: 0;
        }
      }
      .input-area {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 12px;
        position: relative;
        top: 4px;
        .upload {
          display: none;
          height: 16px;
          width: 16px;
          margin-top: -2px;
          &.disabled {
            opacity: 0.5;
          }
        }
        &:hover {
          .upload {
            display: flex;
            &:hover {
              cursor: pointer;
            }
          }
        }
      }
      .delete-icon {
        flex: 0 0 16px;
        height: 16px;
        background: url('../../images/delete.png') no-repeat;
        cursor: pointer;
        &:hover {
          background: url('../../images/delete-active.png') no-repeat;
        }
      }
    }
    .add-option {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      cursor: pointer;
      span {
        font-size: 14px;
        color: #f4873c;
      }
    }
  }
}

.ghost {
  opacity: 0.5 !important;
}
</style>
