<template>
  <div class="question-completion-wrapper" @click.stop="handleClick">
    <!-- 非编辑状态 -->
    <view-wrapper v-if="!isEdit" :disabled="disabled">
      <div class="completion-wrapper">
        <!-- 标题区域 -->
        <div class="view-name-require">
          <!-- 名称 -->
          <div class="name">{{ dataset.name || nameTemp }}</div>
          <!-- 必填非必填标识 -->
          <div class="tag">
            <el-tag :type="isRequired(dataset.required).type" size="mini">
              {{ isRequired(dataset.required).text }}
            </el-tag>
          </div>
        </div>
        <!-- 输入框区域 -->
        <div class="input-area">
          <el-input
            :placeholder="dataset.placeholderContact || nameTemp"
            readonly
            size="small"
          ></el-input>
        </div>
        <div class="input-area m-t-8">
          <el-input
            :placeholder="dataset.placeholder || placeholderTemp"
            readonly
            size="small"
          ></el-input>
        </div>
      </div>
    </view-wrapper>
    <!-- 编辑状态 -->
    <edit-wrapper @del="del" v-else>
      <div class="completion-edit-wrapper">
        <!-- 名称和必填非必填 -->
        <div class="edit-name-require">
          <!-- 名称 -->
          <div class="name">
            <el-input
              ref="autoFocus"
              v-model.trim="dataset.name"
              type="textarea"
              resize="none"
              autosize
              placeholder="请输入问题"
              maxLength="200"
            ></el-input>
          </div>
          <!-- 必填非必填 -->
          <div class="required">
            <img src="../../../images/contact.svg" alt="" />
            <span class="m-l-4">{{ nameTemp }}</span>
            <!--            <el-select-->
            <!--              class="required-select"-->
            <!--              v-model="dataset.required"-->
            <!--              placeholder=""-->
            <!--              size="small"-->
            <!--            >-->
            <!--              <el-option label="必填项" :value="1"> </el-option>-->
            <!--              <el-option label="非必填" :value="0"> </el-option>-->
            <!--            </el-select>-->
          </div>
        </div>
        <!-- 填写区域 -->
        <!-- 输入框区域 -->
        <div class="edit-input-wrapper" @click.stop>
          <div class="input-area">
            <el-input
              v-model="dataset.placeholderContact"
              placeholder="默认提示文字为“请输入”，可点击修改"
              size="small"
              maxLength="200"
            ></el-input>
          </div>
          <div class="input-area m-t-8">
            <el-input
              v-model="dataset.placeholder"
              placeholder="默认提示文字为“请输入”，可点击修改"
              size="small"
              maxLength="200"
            ></el-input>
          </div>
        </div>
      </div>
    </edit-wrapper>
  </div>
</template>

<script>
import ViewWrapper from '../components/ViewWrapper'
// 编辑的混入方法
import editStateMixin from '../mixins/editState'
import requireStateMixin from '../mixins/requireState'

export default {
  name: 'QuestionContact',
  components: { ViewWrapper },
  mixins: [editStateMixin, requireStateMixin],
  data() {
    const id = this.id
    return {
      dataset: {
        id,
        type: 'contact',
        name: '',
        required: 1,
        placeholder: '',
        placeholderContact: ''
      },
      nameTemp: '联系人',
      placeholderTemp: '请输入'
    }
  },
  methods: {
    // 格式化数据
    getData() {
      const data = JSON.parse(JSON.stringify(this.dataset))
      data.name = data.name || this.nameTemp
      data.placeholder = data.placeholder || this.placeholderTemp
      return data
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style lang="scss" scoped>
.question-completion-wrapper {
  .completion-wrapper {
    position: relative;
  }
  .edit-input-wrapper {
    margin-top: 8px;
    :deep(.el-input__inner) {
      padding-bottom: 0 !important;
      cursor: text;
      &:focus,
      &:active {
        border-color: #f4873c !important;
      }
    }
  }
}
.input-area {
  :deep(.el-input__inner) {
    height: 40px;
    line-height: 40px;
    background-color: #f5f6f7;
    border: 1px solid #f5f6f7 !important;
    padding: 0 16px !important;
    border-radius: 3px !important;
    cursor: pointer;
    &:focus,
    &:active {
      border-color: #f5f6f7;
    }
    &::placeholder {
      color: #8f959e;
    }
  }
}
</style>
