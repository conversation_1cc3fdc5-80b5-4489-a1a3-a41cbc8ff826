<template>
  <div>
    <div class="parameter-item">
      <div>选项样式</div>
      <el-radio-group v-model="qsStyle" @change="updateParent">
        <el-radio label="radio" border>
          <div class="radio-name">勾选</div>
          <div class="radio-type">
            <div
              class="radio-type-wrapper flex align-items-center w100"
              v-for="item in 2"
              :key="item"
            >
              <div class="radio-type-round"></div>
              <div class="radio-type-style"></div>
            </div>
          </div>
        </el-radio>
        <el-tooltip
          v-if="hasAnnex"
          effect="dark"
          content="选项中添加了图片，删除图片后才可选择此样式"
          placement="top"
        >
          <el-radio label="select" border :disabled="hasAnnex">
            <div class="radio-name">下拉</div>
            <div class="radio-type">
              <div class="radio-type-style"></div>
            </div>
          </el-radio>
        </el-tooltip>
        <el-radio v-else label="select" border :disabled="hasAnnex">
          <div class="radio-name">下拉</div>
          <div class="radio-type">
            <div class="radio-type-style"></div>
          </div>
        </el-radio>
        <el-tooltip
          v-if="hasAnnex"
          effect="dark"
          content="选项中添加了图片，删除图片后才可选择此样式"
          placement="top"
        >
          <el-radio label="label" border :disabled="hasAnnex">
            <div class="radio-name">标签</div>
            <div class="radio-type">
              <div
                class="radio-type-wrapper flex align-items-center w100"
                v-for="item in 3"
                :key="item"
              >
                <div class="radio-type-style"></div>
              </div>
            </div>
          </el-radio>
        </el-tooltip>
        <el-radio v-else label="label" border :disabled="hasAnnex">
          <div class="radio-name">标签</div>
          <div class="radio-type">
            <div
              class="radio-type-wrapper flex align-items-center w100"
              v-for="item in 3"
              :key="item"
            >
              <div class="radio-type-style"></div>
            </div>
          </div>
        </el-radio>
      </el-radio-group>
    </div>
    <div class="parameter-item limit-num-container">
      <div class="m-b-12">限制用户可选数量</div>
      <div class="flex align-items-center">
        <div class="item-label m-r-8">最少</div>
        <el-select
          v-model="chooseCount"
          placeholder="请选择"
          @change="updateParent"
        >
          <el-option
            v-for="item in leastOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="flex align-items-center m-t-12">
        <div class="item-label m-r-8">最多</div>
        <el-select
          v-model="chooseCountEnd"
          placeholder="请选择"
          @change="updateParent"
        >
          <el-option
            v-for="item in mostOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CheckboxParameter',
  data() {
    return {
      qsStyle: 'radio', // 选项样式
      chooseCount: '', // 至少可选数量
      chooseCountEnd: '', // 最多可选数量
      leastOptions: [],
      mostOptions: [],
      hasAnnex: false
    }
  },
  methods: {
    initData(data) {
      const options = data.options || []
      this.chooseCount = data.chooseCount || ''
      this.chooseCountEnd = data.chooseCountEnd || ''
      this.hasAnnex = options.some(item => {
        return !!item?.attachList?.length
      })
      this.qsStyle = data.qsStyle
      this.leastOptions = [{ label: '不限制', value: '' }]
      this.mostOptions = [{ label: '不限制', value: '' }]
      data.options.forEach((item, index) => {
        this.leastOptions.push({
          label: `${index + 1}项`,
          value: index + 1
        })
        this.mostOptions.push({
          label: `${index + 1}项`,
          value: index + 1
        })
      })
      if (this.chooseCount !== '') {
        this.mostOptions = this.mostOptions.filter(
          item => item.value === '' || item.value >= this.chooseCount
        )
      }
      if (this.chooseCountEnd !== '') {
        this.leastOptions = this.leastOptions.filter(
          item => item.value === '' || item.value <= this.chooseCountEnd
        )
      }
    },
    updateParent() {
      const params = {
        qsStyle: this.qsStyle,
        chooseCount: this.chooseCount,
        chooseCountEnd: this.chooseCountEnd
      }
      this.$emit('updateParent', params)
    }
  }
}
</script>

<style scoped lang="scss">
.limit-num-container {
  .item-label {
    flex-shrink: 0;
    color: rgba(0, 0, 0, 0.65);
  }
}
</style>
