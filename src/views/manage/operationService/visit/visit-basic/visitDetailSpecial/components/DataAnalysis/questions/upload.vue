<template>
  <div class="analysis-checkbox">
    <!-- 头部信息及图表切换 -->
    <div class="analysis-info-wrapper">
      <analysis-info
        :index="index"
        type="图片上传题"
        :name="analysis.qsTitle"
        :count="analysis.qsFillInCount"
        hiddenTab
      />
    </div>
    <div v-if="!noAnswer">
      <!-- 表格区域 -->
      <div class="analysis-table-wrapper">
        <option-table :props="tableProps" :tableData="tableData" />
      </div>
    </div>

    <!-- 无数据情况下 -->
    <div class="no-data" v-else>
      <no-answer />
    </div>
    <img-viewer ref="viewer" />
  </div>
</template>

<script>
import AnalysisInfo from '../components/AnalysisInfo.vue'
import OptionTable from '../components/OptionTable.vue'
import NoAnswer from '../components/NoAnswer.vue'
import ImgViewer from '@/components/ImgViewer'

export default {
  name: 'AnalysisCheckbox',
  components: {
    AnalysisInfo,
    OptionTable,
    NoAnswer,
    ImgViewer
  },
  props: {
    index: Number,
    analysis: Object
  },
  data() {
    return {
      noAnswer: false,
      tableData: [],
      tableProps: [
        {
          label: '填写内容',
          prop: 'option',
          render: (h, scope) => {
            return scope.row.option.map((item, index) => {
              return (
                <el-image
                  class="image pointer"
                  style="width: 136px"
                  src={item.path}
                  fit="contain"
                  onClick={() => {
                    this.previewFile(scope.row.option, index)
                  }}
                />
              )
            })
          }
        }
      ]
    }
  },
  watch: {
    analysis: {
      handler(value) {
        if (value) {
          const answers = value.attachMap || []
          if (answers.length === 0) {
            this.noAnswer = true
            return
          }
          this.tableData = answers.map(answer => {
            return {
              option: answer.Tinymce
            }
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 预览图片
    previewFile(list, index) {
      const pics = list.map(item => item.path)
      this.$refs.viewer.show(pics, index)
    }
  }
}
</script>

<style lang="scss" scoped>
.analysis-checkbox {
  color: #1f2329;
}
.chart-area {
  height: 340px;
}
</style>
