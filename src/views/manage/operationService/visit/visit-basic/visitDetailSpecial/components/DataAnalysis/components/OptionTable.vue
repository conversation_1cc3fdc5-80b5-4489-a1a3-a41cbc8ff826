<template>
  <div>
    <el-table
      :data="dataset"
      :row-class-name="showArrow ? 'pointer' : ''"
      @row-click="rowClick"
      style="width: 100%"
    >
      <el-table-column
        v-for="prop in props"
        :key="prop.prop"
        :prop="prop.prop"
        :label="prop.label"
      >
        <template slot-scope="scope">
          <drive-table-render
            :scope="scope"
            :render="prop.render"
            v-if="prop.render"
          />
          <span v-else>{{ noData(scope.row[prop.prop]) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="" width="64" v-if="showArrow">
        <template slot-scope="scope">
          <img
            v-if="scope.row.showArrow"
            width="16px"
            src="../../../images/arrow.png"
            alt=""
          />
        </template>
      </el-table-column>
    </el-table>
    <!--    <div class="pagination" v-if="pagination">-->
    <!--      <el-pagination-->
    <!--        background-->
    <!--        layout="prev, pager, next"-->
    <!--        :page-size="PAGE_SIZE"-->
    <!--        :total="datasetTemp.length"-->
    <!--        hide-on-single-page-->
    <!--        @current-change="pageChange"-->
    <!--      >-->
    <!--      </el-pagination>-->
    <!--    </div>-->
  </div>
</template>

<script>
import DriveTableRender from '@/components/DriveTable/src/render.vue'
import { noData } from '@/filter'
export default {
  name: 'OptionTable',
  components: { DriveTableRender },
  props: {
    tableData: Array,
    props: Array,
    pagination: Boolean,
    showArrow: Boolean
  },
  data() {
    return {
      noData,
      dataset: [],
      datasetTemp: [],
      PAGE_SIZE: 10,
      pageNumber: 1
    }
  },
  methods: {
    pageChange(num) {
      this.dataset = this.datasetTemp.slice(
        (num - 1) * this.PAGE_SIZE,
        num * this.PAGE_SIZE
      )
    },
    rowClick(row) {
      if (this.showArrow && row.showArrow) {
        this.$emit('rowClick', row)
      }
    }
  },
  watch: {
    tableData: {
      handler(dataset) {
        if (this.pagination) {
          this.dataset = dataset.slice(0, this.PAGE_SIZE)
          this.dataset = dataset
        } else {
          this.dataset = dataset
        }
      },
      deep: true,
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.el-table {
  border-bottom-color: #dee0e3;
  font-size: 14px;
  :deep(thead) {
    color: #646a73;
    th {
      font-weight: normal;
    }
  }
  :deep(th.el-table__cell) {
    background-color: #f5f6f7 !important;
    border-color: transparent !important;
  }
  :deep(td.el-table__cell) {
    border-color: #dee0e3;
  }
  :deep(.el-table__cell) {
    padding: 13px !important;
  }

  :deep(.el-table__row.pointer) {
    cursor: pointer;
  }
}

.pagination {
  text-align: right;
  :deep(.el-pagination) {
    margin-top: 8px;
  }
}
</style>
