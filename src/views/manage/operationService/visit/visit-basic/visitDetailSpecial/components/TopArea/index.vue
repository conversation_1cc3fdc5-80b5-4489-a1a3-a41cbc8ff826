<template>
  <div class="top-area h100">
    <!-- 返回及名称区域 -->
    <div class="back-name">
      <div class="back" @click="close">
        <img class="wh100" src="../../images/goback.png" alt="" />
      </div>
      <span class="name">{{ title }}</span>
    </div>
    <!-- 切换卡区域 -->
    <div class="tabs h100">
      <el-tabs v-model="activeTab" :before-leave="beforeLeave">
        <el-tab-pane label="问卷设计" name="design"></el-tab-pane>
        <el-tab-pane label="问卷设置" name="setting"></el-tab-pane>
        <el-tab-pane
          label="数据分析"
          v-if="isAnalysis"
          name="analysis"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <!-- 操作区 -->
    <div class="operate h100">
      <el-popover
        v-if="inStatus === 2"
        placement="top-start"
        width="500"
        trigger="click"
      >
        <div class="flex">
          <div class="share-left">
            <div class="w100 share-name font-strong m-b-8 line-1 color-black">
              {{ title }}
            </div>
            <img :src="shareInfo.code" class="share-code" alt="" />
            <div class="share-name font-size-12">打开微信扫一扫</div>
          </div>
          <div class="share-right m-l-20 p-t-24">
            <div class="font-size-16 font-strong color-black">分享问卷</div>
            <div class="font-size-14">
              把链接或二维码发给你的用户，邀请他们来填写
            </div>
            <div
              class="share-url m-t-8"
              @click="copyHandle(shareInfo.url, $event)"
            >
              <span class="url-wrapper">{{ shareInfo.url }}</span>
              <el-button type="primary" class="m-l-8">复制</el-button>
            </div>
            <el-button class="m-t-8" @click="downloadCode(shareInfo.code)"
              >下载二维码</el-button
            >
          </div>
        </div>
        <el-button class="m-r-20" type="success" size="small" slot="reference"
          >分享
        </el-button>
      </el-popover>
      <el-button
        v-loading="loading"
        v-if="inStatus !== 3 && !isSee"
        type="primary"
        size="small"
        @click="saveQuestions"
      >
        保存
      </el-button>
      <el-button
        v-if="inStatus !== 2 && inStatus !== 3 && !isSee"
        type="success"
        size="small"
        @click="releaseQuestions"
      >
        保存并发布
      </el-button>
      <el-button
        v-if="inStatus === 2 && !isSee"
        type="warning"
        size="small"
        @click="endQuestions"
      >
        结束
      </el-button>
      <el-button v-if="inStatus === 3" type="info" size="small" disabled
        >已结束</el-button
      >
      <el-button v-if="inStatus === 2" type="info" size="small" disabled
        >已发布</el-button
      >
      <!-- 关闭按钮 -->
      <div class="close" @click="close"></div>
    </div>
  </div>
</template>

<script>
import {
  savaVisitQuestion,
  releaseVisitQuestion,
  getQrCodeImg
} from '../../../api'
import { endConfirm, noQuestionsRelease, releaseConfirm } from '../../util'
import { deepClone } from '@/utils/tools'
import handleClipboard from '@/utils/clipboard'

export default {
  name: 'TopArea',
  props: {
    tab: {
      type: String,
      default: 'design'
    }
  },
  data() {
    return {
      loading: false,
      activeTab: this.tab,
      title: '未命名问卷',
      cacheQuestions: {},
      flag: false,
      shareInfo: {}
    }
  },
  inject: [
    'questionDesignRecord',
    'visitManage',
    'questionSetting',
    'paperId',
    'visitDetail',
    'type',
    'tabChange'
  ],
  computed: {
    isAnalysis() {
      const { managerUser = true } = this.questionSetting()
      return managerUser
    },
    // 查看
    isSee() {
      return this.visitDetail.viewType === 'see'
    },
    visitId() {
      return this.paperId()
    },
    // 判断是否有数据
    isCompleteRequest() {
      return this.questionDesignRecord()
    },
    // 问卷状态1起草中 2发布中 3已结束
    inStatus() {
      const questionDesignRecord = this.questionDesignRecord()
      return questionDesignRecord && questionDesignRecord.inStatus
    }
  },
  methods: {
    // 下载二维码
    downloadCode(code) {
      if (!code) return false
      let base64Str = code
      let aLink = document.createElement('a')
      aLink.style.display = 'none'
      aLink.href = base64Str
      aLink.download = 'code.jpg'
      document.body.appendChild(aLink)
      aLink.click()
      document.body.removeChild(aLink)
    },
    // 获取分享信息
    getQrCodeImg() {
      getQrCodeImg(this.visitId).then(res => {
        this.shareInfo = res || {}
      })
    },
    // 复制
    copyHandle(text, e) {
      handleClipboard(text, e)
    },
    beforeLeave(activeName, oldActiveName) {
      if (oldActiveName === 'setting') {
        let flag = this.$parent.$refs.taskSet.validate()
        return flag
      } else {
        return true
      }
    },
    // 关闭问卷
    close() {
      if (this.inStatus !== 2 || this.inStatus !== 3) {
        if (
          JSON.stringify(this.cacheQuestions) ===
          JSON.stringify(this.formatterQuestions())
        ) {
          this.visitManage.closeDrawer()
        } else {
          this.$confirm('关闭后将丢失已经填写的内容，是否继续？', '提醒', {
            type: 'warning'
          }).then(() => {
            this.visitManage.closeDrawer()
          })
        }
      } else {
        this.visitManage.closeDrawer()
      }
    },
    // 保存问卷
    formatterQuestions() {
      const questionDesignData =
        this.$parent.$refs.questionDesign.getQuestionEditList()
      // 格式化传输的字段，考虑后续对接其他类型项目
      const { title, describe, questions } = questionDesignData
      // 多类型问题数据转换
      const interviewQuestionVos = questions.map(opt => {
        const {
          name,
          type,
          required,
          qsStyle,
          chooseCount,
          chooseCountEnd,
          contentType,
          dateType,
          minText,
          maxText
        } = opt
        const suggestName = `${minText || '不可能'},${maxText || '极有可能'}`
        const obj = {
          qsTitle: name,
          qsType: type,
          required,
          qsStyle,
          chooseCount,
          chooseCountEnd,
          contentType,
          dateType,
          suggestName
        }
        if (type === 'checkbox' || type === 'radio') {
          return {
            ...obj,
            options: opt.options.map(option => ({
              opDescribe: option.label,
              optionAttach: option.attachList.map(item => item.id),
              remark: option.remark,
              other: option.other,
              showOther: option.showOther
            }))
          }
        } else if (type === 'completion') {
          return {
            ...obj,
            placeholder: opt.placeholder
          }
        } else if (type === 'contact') {
          return {
            ...obj,
            placeholder: opt.placeholder,
            placeholderContact: opt.placeholderContact
          }
        } else {
          return {
            ...obj,
            placeholder: opt.placeholder
          }
        }
      })

      const setting = this.$parent.$refs.taskSet.getSetting()
      const subCyc = deepClone(setting.subCyc || [])
      subCyc.forEach(item => {
        if (Array.isArray(item.month)) {
          item.month = item.month.toString()
        }
        if (Array.isArray(item.week)) {
          item.week = item.week.toString()
        }
      })
      const data = {
        type: this.type(),
        paperName: title,
        describeStr: describe,
        interviewQuestionVos,
        ...setting,
        attaches: setting.attaches.map(item => item.id),
        subCyc: JSON.stringify(subCyc)
      }

      // 判断是否有id
      if (this.visitId > -1) {
        data.id = this.visitId
      }
      return data
    },
    // 保存问卷接口
    saveQuestionsApi() {
      return new Promise((resolve, reject) => {
        // 没用，嵌套的太深了，不删了，当个例子
        if (!this.$parent.$refs.taskSet.validate()) {
          reject()
        }
        const formatterQuestions = this.formatterQuestions()
        if (formatterQuestions) {
          savaVisitQuestion(formatterQuestions).then(res => {
            // 缓存题目
            this.cacheQuestions = { ...formatterQuestions, paperId: res.id }
            resolve({ data: res, formatterQuestions })
          })
        }
      })
    },
    // 保存问卷
    saveQuestions() {
      console.log('saveQuestions')
      if (!this.$parent.$refs.taskSet.validate()) {
        if (this.activeTab !== 'setting') {
          this.$confirm('未填写完整内容，请完善？', '提醒', {
            type: 'warning'
          }).then(() => {
            this.activeTab = 'setting'
            this.$emit('tabChange', 'setting')
          })
        }
        return
      }
      this.loading = true
      this.saveQuestionsApi()
        .then(() => {
          this.$message.success({ message: '保存成功', duration: 1000 })
          this.visitManage.closeDrawer()
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 结束问卷
    endQuestions() {
      endConfirm().then(() => {
        this.saveQuestionsApi().then(res => {
          this.releaseQuestionsApi(res.data.id, 3)
        })
      })
    },
    // 发布问卷
    releaseQuestions() {
      if (!this.$parent.$refs.taskSet.validate()) {
        if (this.activeTab !== 'setting') {
          this.$confirm('未填写完整内容，请完善？', '提醒', {
            type: 'warning'
          }).then(() => {
            this.activeTab = 'setting'
            this.$emit('tabChange', 'setting')
          })
        }
        return
      }
      releaseConfirm().then(() => {
        this.saveQuestionsApi().then(res => {
          if (res.formatterQuestions.interviewQuestionVos.length < 1) {
            noQuestionsRelease().then(() => {
              this.releaseQuestionsApi(res.data.id, 2)
            })
          } else {
            this.releaseQuestionsApi(res.data.id, 2)
          }
        })
      })
    },
    // 发布问卷api
    releaseQuestionsApi(id, inStatus = 2) {
      releaseVisitQuestion(id, inStatus).then(() => {
        const message = inStatus === 3 ? '结束成功' : '发布成功'
        this.$message.success({ message, duration: 1000 })
        this.visitManage.closeDrawer()
      })
    },
    // 刷新详情数据
    refreshDetail(id) {
      this.VisitDetail.getQuestionDetail(id)
    },
    // 监听标题变化
    onTitleChange() {
      this.$bus.$on('titleChange', this.changeTitle)
    },
    // 取消监听标题变化
    offTitleChange() {
      this.$bus.$off('titleChange', this.changeTitle)
    },
    // 改变标题
    changeTitle(title) {
      this.title = title
    }
  },
  watch: {
    activeTab: {
      handler(value) {
        this.$emit('tabChange', value)
      }
      // immediate: true
    },
    isCompleteRequest: {
      handler(complete) {
        if (complete) {
          this.$nextTick(() => {
            // 缓存题目
            setTimeout(() => {
              this.cacheQuestions = this.formatterQuestions()
            }, 20)
          })
        }
      },
      immediate: true
    }
  },
  created() {
    this.onTitleChange()
  },
  mounted() {
    this.$nextTick(() => {
      // 缓存题目
      this.cacheQuestions = this.formatterQuestions()
    })
  },
  destroyed() {
    this.offTitleChange()
  }
}
</script>

<style lang="scss" scoped>
.top-area {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .back-name {
    display: flex;
    align-items: center;
    flex: 0 0 30%;
    overflow: hidden;
    .back {
      flex: 0 0 24px;
      height: 24px;
      cursor: pointer;
      border-radius: 3px;
      img {
        position: relative;
        top: 1px;
      }
      &:hover {
        background-color: #eff0f1;
      }
    }
    .name {
      flex: 1;
      padding-left: 12px;
      font-size: 16px;
      color: #1f2329;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }
  .tabs {
    flex: 0 0 272px;
    display: flex;
    justify-content: center;
    :deep(.el-tabs) {
      height: 100%;
      .el-tabs__header {
        margin: 0;
        .el-tabs__nav-wrap::after {
          display: none;
        }
        .el-tabs__active-bar {
          background-color: #f4873c;
        }
        .el-tabs__item {
          height: 65px;
          line-height: 65px;
          &.is-active,
          &:hover {
            color: #f4873c;
          }
          &.is-active {
            font-weight: 600;
          }
        }
      }
    }
  }
  .operate {
    flex: 0 0 30%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    :deep(.el-button + .el-button) {
      margin-left: 20px;
    }

    .close {
      width: 20px;
      height: 20px;
      margin-left: 20px;
      cursor: pointer;
      border-radius: 3px;
      background-image: url('../../images/delete.png');
      background-repeat: no-repeat;
      background-position: 50% 50%;
      &:hover {
        background-image: url('../../images/delete-active.png');
        background-color: #eff0f1;
      }
    }
  }
}
.share-left {
  width: 120px;
  flex-shrink: 0;
  .share-name {
    text-align: center;
  }
  .share-code {
    width: 100%;
    height: 120px;
  }
}
.share-right {
  .share-url {
    width: 100%;
    display: flex;
    justify-content: space-between;
    background: #f5f6f7;
    border-radius: 4px;
    padding-left: 12px;
    overflow: hidden;
    cursor: pointer;
    height: 40px;
    .url-wrapper {
      width: 100%;
      height: 40px;
      display: flex;
      flex-wrap: nowrap;
      line-height: 40px;
    }
    :deep(.el-button) {
      height: 40px;
      padding: 8px 20px;
    }
  }
}
</style>
