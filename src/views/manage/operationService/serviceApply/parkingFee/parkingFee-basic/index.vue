<template>
  <basic-card class="pos-relative">
    <div class="pos-absolute refresh-btn flex align-items-center">
      <el-switch v-model="refreshModel" @change="refreshHandle" />
      <span class="tips-time font-size-14 line-height-20 p-l-8"
        >自动刷新({{ timeCount }}s)</span
      >
    </div>
    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
      :disabled="reqLoading"
    />
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :extralQuerys="extralQuerys"
      :api-fn="getCarSpacePage"
      :search-querys-hook="searchQueryHook"
      @getTotal="getTotal"
    >
      <template v-slot:operate-right>
        <el-button
          type="primary"
          v-permission="routeButtonsPermission.LEADING_OUT"
          @click="exportExcel"
          >{{ routeButtonsTitle.LEADING_OUT }}</el-button
        >
      </template>
    </drive-table>

    <dialog-cmp
      title="审核"
      :visible.sync="visible"
      @closeDialog="visible = false"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </dialog-cmp>
  </basic-card>
</template>

<script>
import TableColumnMixin from './column'
import { getCarSpacePage, getExportExcel, getCount, postExamine } from './api'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'
import BasicTab from '@/views/manage/operationService/field/field-special/components/BasicTab'
import dayjs from 'dayjs'

export default {
  name: 'ParkingFee',
  components: { BasicTab },
  mixins: [TableColumnMixin],
  data() {
    return {
      fromModel: {
        status: 1,
        sendMsg: false
      },
      formConfigure: {
        descriptors: {
          status: {
            form: 'radio',
            label: '审核结果',
            options: [
              {
                label: '通过',
                value: 1
              },
              {
                label: '拒绝',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择审核结果'
              }
            ]
          },
          comment: {
            form: 'input',
            label: '备注',
            rule: [
              {
                type: 'string',
                required: false,
                message: '请输入备注内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 5,
              maxlength: 200,
              showWordLimit: true
            }
          },
          sendMsg: {
            form: 'switch',
            label: '发送短信',
            rule: [{ type: 'boolean' }]
          }
        }
      },
      visible: false,
      current: 10,
      reqLoading: false,
      list: [
        {
          label: '全部',
          value: 10,
          sum: ''
        },
        {
          label: '待审核',
          value: 0,
          sum: ''
        },
        {
          label: '已通过',
          value: 1,
          sum: ''
        },
        {
          label: '已拒绝',
          value: 2,
          sum: ''
        }
      ],
      refreshModel: false,
      timer: null,
      timeCount: 5,
      getCarSpacePage,
      extralQuerys: {}
    }
  },
  mounted() {
    this.getCountFn()
  },
  methods: {
    // 重置搜索参数
    searchQueryHook(e) {
      let createTimeStartTime, createTimeEndTime
      let [beginApplyDate = '', endApplyDate = ''] = e.applyTime || []
      if (beginApplyDate && endApplyDate) {
        createTimeStartTime = beginApplyDate
        createTimeEndTime = endApplyDate
      }
      delete e.applyTime
      return {
        ...e,
        createTimeStartTime,
        createTimeEndTime
      }
    },
    getTotal() {
      this.getCountFn()
      this.reqLoading = false
    },
    getCountFn() {
      getCount().then(res => {
        const counts = {
          totalCount: 0,
          defaultCount: 0,
          passCount: 0,
          rejectCount: 0,
          ...res
        }
        this.list.forEach((item, index) => {
          item.sum = counts[Object.keys(counts)[index]]
        })
      })
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          postExamine(this.fromModel).then(() => {
            this.$toast.success('审核成功')
            this.visible = false
            this.$refs['drive-table'].refreshTable()
            this.getCountFn()
            this.fromModel = {
              status: 1,
              comment: '',
              sendMsg: false
            }
          })
        }
      })
    },
    audit(row) {
      this.visible = true
      this.fromModel = {
        id: row.id,
        status: 1,
        comment: '',
        sendMsg: false
      }
    },
    tabsChange(e) {
      this.current = e
      this.reqLoading = true
      this.extralQuerys.status = e === 10 ? '' : e
      this.$refs['drive-table'].resetPageNoRefreshTable()
    },
    // 清除轮询
    clearIntervalHandle() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timeCount = 5
        this.timer = null
      }
    },
    // 开启轮询
    startHandle() {
      this.timer = setInterval(() => {
        this.timeCount -= 1
        if (this.timeCount <= 0) {
          clearInterval(this.timer)
          this.timeCount = 5
          this.$refs['drive-table']?.refreshTable()
          this.clearIntervalHandle()
          this.startHandle()
          this.getCountFn()
        }
      }, 1000)
    },
    refreshHandle(val) {
      this.clearIntervalHandle()
      if (!val) return false
      this.$refs['drive-table']?.refreshTable()
      this.startHandle()
    },
    exportExcel() {
      const params = {
        ...this.$refs['drive-table'].querys,
        ...this.extralQuerys
      }
      let url = getExportExcel() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(
        url,
        'excel',
        dayjs().format('YYYY-MM-DD') + '-车位包月申请明细.xls'
      )
    },
    attachClick(pic) {
      if (!pic.length) return this.$toast.warning('暂无材料')
      window.open(pic[0].path, '_blank')
    }
  },
  activated() {
    if (this.executeActivated) {
      this.getCountFn()
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  }
}
</script>

<style scoped lang="scss">
.refresh-btn {
  right: 24px;
  top: 18px;
}
:deep(.driven-table-search) {
  .el-form-item__label {
    flex: 0 0 96px !important;
  }
}
</style>
