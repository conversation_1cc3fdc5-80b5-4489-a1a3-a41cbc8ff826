<template>
  <div class="min-h100 flex">
    <basic-card>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getInformationList"
        :search-querys-hook="searchQueryHook"
      >
        <template v-slot:operate-right>
          <el-button
            type="primary"
            size="small"
            v-permission="routeButtonsPermission.ADD"
            @click="addHander"
          >
            <svg-icon icon-class="add" />
            <span>{{ routeButtonsTitle.ADD }}公告</span>
          </el-button>
        </template>
      </drive-table>
    </basic-card>

    <!-- 新增编辑弹框 -->
    <basic-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
      :haveOperation="false"
    >
      <div v-if="drawerVisible">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
      </div>
      <template slot="footer">
        <el-button type="info" @click="drawerVisible = false">取消</el-button>
        <el-button type="success" @click="confirmDrawer(1)"
          >保存为草稿</el-button
        >
        <el-button type="primary" @click="confirmDrawer(2)"
          >保存并发布</el-button
        >
      </template>
    </basic-drawer>

    <!-- 资讯详情 -->
    <dialog-cmp
      title="公告详情"
      :visible.sync="visible"
      width="920px"
      :haveOperation="false"
    >
      <div>
        <preview :informationData="informationData" />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
import descriptorMixins from './descriptor'
import preview from './preview'
import {
  getInformationList,
  createInformation,
  getInformationDetails,
  updateInformation,
  deleteInformation,
  changeTopping,
  changeStatus,
  getIsShow
} from './api'
import { getTenant } from '@/utils/auth'
import { getByTenantDictType } from '@/api/common'

export default {
  name: 'InformationBasic',
  mixins: [ColumnMixins, descriptorMixins],
  components: {
    preview
  },
  data() {
    return {
      getInformationList,
      drawerVisible: false, // 新增编辑弹框
      drawerTitle: '新增公告', // 新增编辑弹框名称
      fromModel: {}, // 新增编辑表单数据
      visible: false, // 预览资讯详情
      informationData: {}, // 资讯详情数据
      tenantId: getTenant()
    }
  },
  created() {
    this.getInformationType()
  },
  mounted() {
    // this.getIsShowHander()
  },
  methods: {
    getIsShowHander() {
      getIsShow(this.tenantId, 4).then(res => {
        this.fromModel.isShow = +res
        if (res === 2) {
          this.formConfigure.descriptors.isShow.hidden = true
          this.formConfigure.descriptors.mainTitle.hidden = true
          this.formConfigure.descriptors.subheading.hidden = true
        }
      })
    },
    addHander() {
      this.fromModel = {}
      this.drawerVisible = true
      this.drawerTitle = '新增公告'
      console.log('this.fromModel', this.fromModel)
    },
    // 重置搜索参数
    searchQueryHook(e) {
      let [beginCreateTime = '', endCreateTime = ''] = e.createTime || []
      if (beginCreateTime && endCreateTime) {
        beginCreateTime = beginCreateTime + ' ' + '00:00:00'
        endCreateTime = endCreateTime + ' ' + '23:59:59'
      }
      delete e.createTime
      return {
        ...e,
        beginCreateTime,
        endCreateTime
      }
    },

    // 新增编辑资讯提交
    confirmDrawer(status) {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          console.log(this.fromModel)
          const { id, attachList } = this.fromModel
          const params = {
            ...this.fromModel,
            status
          }

          if (attachList && attachList.length > 0) {
            params.attachIds = attachList.map(item => item.id)
          }

          if (!id) {
            // 新增资讯
            createInformation(params).then(() => {
              this.operationSuccess('新增')
            })
          } else {
            updateInformation(params).then(() => {
              this.operationSuccess('编辑')
            })
          }
        }
      })
    },

    // 编辑政策详情
    editInformation(row) {
      getInformationDetails(row.id).then(res => {
        this.drawerTitle = '编辑公告'
        const { attachMap = {} } = res
        this.fromModel = {
          ...res,
          attachList: attachMap.informationAttach
        }
        this.drawerVisible = true
      })
    },

    // 置顶切换
    changeTopping(row) {
      const { id, topping } = row
      const tips = topping ? '取消置顶' : '置顶'
      this.$confirm(`确定${tips}该公告？`).then(() => {
        changeTopping(id).then(() => {
          this.$toast.success(`${tips}公告成功`)
          this.$refs['drive-table'].refreshTable()
        })
      })
    },

    // 下架上架状态
    changeStatus(row) {
      const { id, status } = row
      const tips = status === 1 ? '上架' : '下架'
      this.$confirm(`确定${tips}该公告？`).then(() => {
        changeStatus(id).then(() => {
          this.$toast.success(`${tips}公告成功`)
          this.$refs['drive-table'].refreshTable()
        })
      })
    },

    // 新增提示信息
    operationSuccess(tips) {
      this.$toast.success(tips + '公告成功')
      this.drawerVisible = false
      this.$refs['drive-table'].refreshTable()
    },

    // 删除公告
    deleteInformation(row) {
      this.$confirm('确定删除该公告？').then(() => {
        const { id } = row
        deleteInformation(id).then(() => {
          this.$toast.success('删除公告成功')
          this.$refs['drive-table'].refreshTable()
        })
      })
    },

    // 查看
    previewEvent(row) {
      getInformationDetails(row.id).then(res => {
        this.informationData = res
        this.visible = true
      })
    },

    // 获取资讯类型
    getInformationType() {
      getByTenantDictType('information_type').then(res => {
        const levelList = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.tableColumn[1].search.options = levelList
        this.formConfigure.descriptors.type.options = levelList
      })
    }
  },
  watch: {
    'fromModel.isShow': {
      handler(val) {
        if (val === 1) {
          this.formConfigure.descriptors.mainTitle.hidden = false
          this.formConfigure.descriptors.subheading.hidden = false
        } else {
          this.formConfigure.descriptors.mainTitle.hidden = true
          this.formConfigure.descriptors.subheading.hidden = true
        }
      },
      immediate: true,
      deep: true
    }
  },
  activated() {
    this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
  }
}
</script>

<style lang="scss" scoped></style>
