import DictPopover from '@/components/DictPopover'

export default {
  components: {
    DictPopover
  },
  data() {
    return {
      formConfigure: {
        descriptors: {
          nameList: {
            form: 'input',
            label: '公告类型',
            rule: [
              {
                required: true,
                type: 'string',
                message: '支持批量添加公告类型,每行一个'
              }
            ],
            props: {
              type: 'textarea'
            }
          }
        }
      }
    }
  }
}
