import DictPopover from '@/components/DictPopover'

export default {
  components: {
    DictPopover
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          title: {
            form: 'input',
            label: '公告名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入公告名称'
              }
            ],
            attrs: {
              maxlength: 50,
              showWordLimit: true
            }
          },
          type: {
            form: 'select',
            label: '公告类型',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择公告类型'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <dict-popover
                  type="information_type"
                  onRefreshDict={() => {
                    this.getInformationType()
                  }}
                />
              )
            }
          },
          content: {
            form: 'component',
            label: '公告内容',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入公告内容'
              }
            ],
            componentName: 'tinymce'
          },
          attachList: {
            form: 'component',
            label: '公告附件',
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div class="line-height-20">
                  <div>请上传不大于10MB的附件</div>
                  <div>附件不得超过三个</div>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          },
          isShow: {
            form: 'radio',
            label: '置顶到小程序',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择是否置顶到小程序'
              }
            ],
            options: [
              {
                label: '置顶',
                value: 1
              },
              {
                label: '不置顶',
                value: 2
              }
            ]
          },
          mainTitle: {
            form: 'input',
            label: '主标题',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主标题'
              }
            ]
          },
          subheading: {
            form: 'input',
            label: '副标题',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入副标题'
              }
            ]
          }
        }
      }
    }
  }
}
