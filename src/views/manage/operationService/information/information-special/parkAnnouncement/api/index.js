import request from '@/utils/request'

// 获取园区公告列表
export function getNoticeList(params) {
  return request({
    url: `/hatch/notice/page`,
    method: 'get',
    params: {
      ...params,
      status: true,
      targetAudience: 0
    }
  })
}
// 获取园区公告详情
export function getDetail(id) {
  return request({
    url: `/hatch/notice/get?id=${id}`,
    method: 'get'
  })
}
// 获取所有公告类型
export function getNoticeTypeAll() {
  return request({
    url: `/hatch/notice_type/list_all`,
    method: 'get'
  })
}
// 获得场地分享信息
export function getNoticeShareInfo(id) {
  return request({
    url: `/hatch/notice/get_share_info/${id}`,
    method: 'get'
  })
}
