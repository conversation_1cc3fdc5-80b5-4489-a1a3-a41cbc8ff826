<template>
  <div class="course-title-container flex">
    <div class="course-img pos-relative">
      <el-image
        style="
          width: 160px;
          height: 90px;
          border-radius: 3px;
          border: 1px solid #ed7b2f14;
        "
        :preview-src-list="[item.titleUrl]"
        :src="
          item.titleUrl
            ? item.titleUrl
            : require('../images/course-default.png')
        "
      />
      <div v-if="item.courseModeStr" class="course-info">
        {{ item.courseModeStr }}
      </div>
      <div v-if="item.courseModeStr" class="course-litter"></div>
    </div>
    <div class="course-right m-l-8">
      <div class="font-size-16 line-height-23" style="font-weight: 500">
        {{ item.title }}
      </div>
      <div class="flex m-t-8">
        <el-tag
          v-show="index < 3"
          v-for="(item, index) in item.labels"
          :key="index"
          type="primary"
          class="m-r-8"
          >{{ item }}</el-tag
        >
      </div>
      <div
        class="line-height-22 m-t-8 font-size-14 color-info"
        v-if="
          item.approach === 1 &&
          item.personStr !== '报名未开始' &&
          item.personStr !== '报名已结束'
        "
      >
        <span>人员限制 {{ item.personStr }}</span>
      </div>
      <!--      <div class="line-height-22 m-t-8 font-size-14 color-info" v-else>-->
      <!--        <span>{{ item.personStr | noData }}</span>-->
      <!--      </div>-->
    </div>
  </div>
</template>

<script>
export default {
  name: 'CourseTitle',
  props: {
    item: {
      type: Object,
      default: null
    }
  }
}
</script>

<style lang="scss" scoped>
.course-title-container {
  padding-left: 16px;
  .title-tags {
    width: 64px;
    height: 24px;
    font-size: 12px;
    line-height: 24px;
    text-align: center;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
  }
  .course-img {
    .course-info {
      position: absolute;
      left: -3px;
      top: 4px;
      width: 40px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      font-size: 12px;
      color: #e7e7e7;
      @include background_color(--color-primary);
      border-radius: 0 3px 3px 0;
      opacity: 1;
    }
    .course-litter {
      position: absolute;
      left: -3px;
      top: 32px;
      border-top: 2px solid #0038b3;
      border-bottom: 2px solid transparent;
      border-left: 2px solid transparent;
      border-right: 2px solid #0038b3;
    }
    .park-leave {
      @include background_color(--color-info);
    }
    .leave-litter {
      border-top: 2px solid #4a4b4a;
      border-right: 2px solid #4a4b4a;
    }
  }
  img {
    width: 160px;
    height: 90px;
  }
}
</style>
