<template>
  <div class="preview-wrapper pos-relative">
    <div class="position-tag pos-absolute line-height-24">
      <el-tag type="warning">{{ courseData.courseType }}</el-tag>
    </div>
    <div class="flex">
      <div class="preview-left p-t-24">
        <div
          class="font-size-16 color-black font-strong line-height-24 preview-title"
        >
          {{ courseData.courseName }}
        </div>

        <div
          class="m-t-10 m-b-10 flex align-items-center justify-content-center"
        >
          <el-tag
            v-for="(item, index) in courseLabel"
            :key="index"
            class="primary-tag"
            :class="getMargin(index)"
            >{{ item }}</el-tag
          >
        </div>

        <div
          class="m-b-16 font-size-12 flex align-items-center justify-content-center color-text-regular"
        >
          <div class="m-r-8">阅读数：{{ courseData.lookNum }}</div>
          <div>发布时间：{{ courseData.createTime }}</div>
        </div>

        <div
          v-html="$options.filters.richTextFilter(courseData.courseContent)"
          class="color-black font-size-14 line-height-22"
        ></div>
      </div>

      <div class="preview-right p-t-8 p-l-16">
        <div class="m-b-24">
          <div
            class="preview-right-title font-size-14 color-text-primary m-b-16"
          >
            课程封面
          </div>
          <div>
            <files-list
              v-if="picList && picList.length > 0"
              :files="picList"
              onlyForView
            />
            <empty-data v-else />
          </div>
        </div>

        <div class="m-b-24">
          <div
            class="preview-right-title font-size-14 color-text-primary m-b-16"
          >
            附件下载
          </div>
          <div>
            <files-list
              v-if="attachList && attachList.length > 0"
              :files="attachList"
              onlyForView
            />
            <empty-data v-else />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FilesList from '@/components/Uploader/files'
import { richTextFilter } from '@/filter'

export default {
  name: 'policyPreview',
  components: {
    FilesList
  },
  props: {
    courseData: {
      type: Object,
      default: null
    }
  },
  filters: { richTextFilter },
  data() {
    return {}
  },
  computed: {
    courseLabel() {
      return this.courseData.courseLabel
        ? this.courseData.courseLabel.split(',')
        : ''
    },

    attachList() {
      return this.courseData.attachMap.courseAttach || []
    },

    picList() {
      return this.courseData.attachMap.coursePic || []
    }
  },
  methods: {
    getMargin(index) {
      return index < this.courseLabel.length - 1 ? 'm-r-24' : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-wrapper {
  .preview-left {
    flex: 0 0 640px;
    padding-right: 15px;
    box-sizing: border-box;
    border-right-width: 1px;
    border-style: solid;
    overflow: hidden;
    @include border_color(--border-color-base);
    .preview-title {
      text-align: center;
    }

    .primary-tag {
      border-radius: 12px;
    }
  }

  .preview-right {
    flex: 1;
  }
  .position-tag {
    left: 0;
    top: 0;
  }

  :deep(.el-tag--warning) {
    border-radius: 0 100px 100px 0;
  }
}
</style>
