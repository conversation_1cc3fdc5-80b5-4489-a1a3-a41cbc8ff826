<template>
  <div class="min-h100 flex">
    <basic-card>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getNoticeList"
        :height="height"
        :search-querys-hook="searchQueryHook"
      >
      </drive-table>
    </basic-card>

    <!-- 详情 -->
    <dialog-cmp
      title="资讯详情"
      :visible.sync="visible"
      width="920px"
      :haveOperation="false"
    >
      <div>
        <preview
          :isShow="false"
          :qrData="qrData"
          :id="detailID"
          :informationData="informationData"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
import { getNoticeList, getDetail, getNoticeTypeAll } from './api'
import { getTenant } from '@/utils/auth'
import preview from './preview.vue'

export default {
  name: 'ParkAnnouncement',
  components: { preview },
  mixins: [ColumnMixins],
  data() {
    return {
      height: 'calc(100vh - 300px)',
      getNoticeList,
      qrTitle: '分享',
      signShow: false,
      qrVisible: false,
      qrLoading: false,
      tenantId: getTenant(),
      detailID: '',
      qrData: {},
      visible: false,
      informationData: {}
    }
  },
  mounted() {
    this.getInformationType()
  },
  methods: {
    // 重置搜索参数
    searchQueryHook(e) {
      let [beginCreateTime = '', endCreateTime = ''] = e.createTime || []
      if (beginCreateTime && endCreateTime) {
        beginCreateTime = beginCreateTime + ' ' + '00:00:00'
        endCreateTime = endCreateTime + ' ' + '23:59:59'
      }
      delete e.createTime
      return {
        ...e,
        beginCreateTime,
        endCreateTime
      }
    },

    // 查看
    previewEvent(row) {
      this.detailID = row.id
      getDetail(row.id).then(res => {
        this.informationData = res
        this.visible = true
      })
    },

    // 获取资讯类型
    getInformationType() {
      getNoticeTypeAll().then(res => {
        const levelList = res.map(item => {
          return { label: item.value, value: item.key }
        })
        this.tableColumn[1].search.options = levelList
      })
    }
  },
  activated() {
    this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
  }
}
</script>

<style lang="scss" scoped></style>
