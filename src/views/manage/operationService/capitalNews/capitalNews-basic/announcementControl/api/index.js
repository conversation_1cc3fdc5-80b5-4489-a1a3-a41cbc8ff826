import request from '@/utils/request'

// 获取资讯列表
export function getNoticeList(params) {
  return request({
    url: `/hatch/information/manage/page`,
    method: 'get',
    params
  })
}

// 获取资讯管理排序接口
export function getNoticeAll(params) {
  return request({
    url: `/hatch/information/manage/list`,
    method: 'get',
    params
  })
}

// 资讯管理排序保存
export function getNoticeSort(data) {
  return request({
    url: `/hatch/information/manage/sort`,
    method: 'post',
    data,
    isFormData: true
  })
}

// 新增资讯
export function createNotice(data) {
  return request({
    url: `/hatch/information/manage/create`,
    method: 'post',
    data
  })
}

// 获取资讯详情
export function getInNoticeDetails(params) {
  return request({
    url: `/hatch/information/manage/get`,
    method: 'get',
    params
  })
}

// 更新资讯
export function updateNotice(data) {
  return request({
    url: `/hatch/information/manage/update`,
    method: 'put',
    data
  })
}

//删除资讯
export function deleteNotice(id) {
  return request({
    url: `/hatch/information/manage/delete?id=${id}`,
    method: 'delete'
  })
}

// 发布-取消发布
export function changePublish(id) {
  return request({
    url: `/hatch/information/manage/publish?id=${id}`,
    method: 'get'
  })
}
// 获得分享信息
export function getNoticeShareInfo(id) {
  return request({
    url: `/hatch/information/manage/get_share_info/${id}`,
    method: 'get'
  })
}
//资讯类型下拉
export function getNoticeListAll() {
  return request({
    url: `/hatch/information/type/list_all`,
    method: 'get'
  })
}
// 新增资讯类型
export function createNoticeType(data) {
  return request({
    url: `/hatch/information/type/create`,
    method: 'post',
    data
  })
}
