<template>
  <div class="min-h100 bg-white">
    <basic-card title="黑名单管理">
      <drive-table
        ref="drive-table"
        :api-fn="getBlackRecordPage"
        :columns="tableColumn"
      >
        <template v-slot:operate-right>
          <div>
            <el-button
              v-permission="routeButtonsPermission.EMPLACE"
              type="info"
              @click="rulesHandle"
              >规则{{ routeButtonsTitle.EMPLACE }}</el-button
            >
            <el-button
              v-permission="routeButtonsPermission.ADD_NAME"
              type="primary"
              @click="openHandle"
              >{{ routeButtonsTitle.ADD_NAME }}</el-button
            >
          </div>
        </template>
      </drive-table>
    </basic-card>
    <!--添加黑名单-->
    <basic-drawer
      :size="700"
      title="添加名单"
      :visible.sync="visible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
    <!--规则设置-->
    <basic-drawer
      :size="700"
      title="规则设置"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawerRule"
    >
      <div v-if="drawerVisible">
        <div class="font-size-14 m-b-16 font-strong">限制行为</div>
        <div class="m-b-24">
          <driven-form
            class="rule-form-wrapper"
            ref="driven-form-move"
            v-model="fromRules"
            :formConfigure="formConfigureRule"
          />
        </div>
        <div class="font-size-14 m-b-16 font-strong">限制规则</div>
        <driven-form
          ref="driven-form-rule"
          v-model="fromData"
          :formConfigure="formConfigureSize"
        />
      </div>
    </basic-drawer>
  </div>
</template>

<script>
import ColumnMixin from './column'
import Descriptors from './descriptor'
import {
  addBlackRecord,
  createBlackRule,
  getBlackRecordPage,
  getBlackRule,
  getBlackType,
  getByDictType,
  getRemoveBlack
} from '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldBlacklist/api'

export default {
  name: 'FieldBlacklist',
  mixins: [ColumnMixin, Descriptors],
  data() {
    return {
      getBlackRecordPage,
      visible: false,
      drawerVisible: false,
      fromModel: {},
      fromData: {},
      fromRules: {}
    }
  },
  watch: {
    'fromRules.cancelDays'(val) {
      this.formConfigureRule.descriptors.cancelCount.rule[0].required = !!val
      this.$nextTick(() => {
        this.$refs['driven-form-move'].validateField('cancelCount')
      })
    },
    'fromRules.cancelCount'(val) {
      this.formConfigureRule.descriptors.cancelDays.rule[0].required = !!val
      this.$nextTick(() => {
        this.$refs['driven-form-move'].validateField('cancelDays')
      })
    },
    'fromRules.unArriveDays'(val) {
      this.formConfigureRule.descriptors.unArriveCount.rule[0].required = !!val
      this.$nextTick(() => {
        this.$refs['driven-form-move'].validateField('unArriveCount')
      })
    },
    'fromRules.unArriveCount'(val) {
      this.formConfigureRule.descriptors.unArriveDays.rule[0].required = !!val
      this.$nextTick(() => {
        this.$refs['driven-form-move'].validateField('unArriveDays')
      })
    }
  },
  activated() {
    this.getBlackType()
    this.getByDictType()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    openHandle() {
      this.fromModel = {}
      this.visible = true
    },
    // 添加黑名单 - 提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        let data = {
          ...this.fromModel
        }
        addBlackRecord(data).then(() => {
          this.$toast.success('添加成功')
          this.visible = false
          this.$refs['drive-table'].refreshTable()
        })
      })
    },
    rulesHandle() {
      this.fromData = {}
      this.getBlackRule()
    },
    // 黑名单-获取黑名单规则
    async getBlackRule() {
      const res = await getBlackRule()
      let {
        cancelCount,
        cancelDays,
        unArriveCount,
        unArriveDays,
        limitDays,
        id
      } = res
      this.fromData = {
        limitDays: limitDays ? String(limitDays) : ''
      }
      this.fromRules = {
        cancelCount: cancelCount ? String(cancelCount) : '',
        cancelDays: cancelDays ? String(cancelDays) : '',
        unArriveCount: unArriveCount ? String(unArriveCount) : '',
        unArriveDays: unArriveDays ? String(unArriveDays) : '',
        id
      }
      this.drawerVisible = true
    },
    // 规则设置
    confirmDrawerRule() {
      this.$refs['driven-form-move'].validate(val => {
        if (!val) return false
        this.$refs['driven-form-rule'].validate(valid => {
          if (!valid) return false
          let data = {
            ...this.fromData,
            ...this.fromRules
          }
          createBlackRule(data).then(() => {
            this.$toast.success('设置成功')
            this.drawerVisible = false
            this.$refs['drive-table'].refreshTable()
          })
        })
      })
    },
    // 移除
    delHandle(row) {
      this.$confirm('确定移除黑名单？').then(() => {
        getRemoveBlack(row.id).then(() => {
          this.$toast.success('移除成功')
          this.$refs['drive-table'].refreshTable()
        })
      })
    },
    // 黑名单-获取黑名单加入方式下拉
    async getBlackType() {
      const res = await getBlackType()
      this.tableColumn[1].search.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 获取违规原因
    async getByDictType() {
      const res = await getByDictType()
      this.formConfigure.descriptors.reason.options = res.map(item => {
        return { label: item.label, value: item.label }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.rule-form-wrapper {
  :deep(label) {
    display: none;
  }
}
</style>
