<template>
  <div>
    <div v-if="list && list.length > 0">
      <div v-for="item in list" :key="item.placeId">
        <div>{{ item.placeName }}</div>
        <div v-for="(row, rowIdx) in item.sessionList" :key="rowIdx">
          <span class="m-r-8">{{ row.fieldDate }}</span>
          <span>{{ row.startTime }}-{{ row.endTime }}</span>
        </div>
      </div>
    </div>
    <div v-else>暂无数据</div>
  </div>
</template>

<script>
export default {
  name: 'PlaceItem',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped></style>
