import Vue from 'vue'
import PlaceItem from '@/views/manage/operationService/sportCenter/sportCenter-basic/fieldOrder/components/placeItem'
Vue.component('PlaceItem', PlaceItem)

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'stadiumName',
          label: '场地名称',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'applyUserName',
          label: '申请用户',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'contactName',
          label: '联系人'
        },
        {
          prop: 'contactPhone',
          label: '联系电话'
        },
        {
          prop: 'resNumber',
          label: '预约人数'
        },
        {
          prop: 'number',
          label: '预定场次',
          width: 180,
          render: (h, { row }) => {
            return <place-item list={row.place} />
          }
        },
        {
          prop: 'applyTime',
          label: '申请时间',
          search: {
            type: 'daterange'
          },
          sortable: 'custom'
        },
        {
          prop: 'status',
          label: '申请状态',
          render: (h, scope) => {
            const obj = {
              1: 'primary',
              2: 'success',
              3: 'danger',
              4: 'info'
            }
            return (
              <div>
                <basic-tag
                  type={obj[scope.row.status]}
                  label={scope.row.statusStr}
                ></basic-tag>
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          // eslint-disable-next-line no-unused-vars
          render: (h, { row }) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.detailHandle(row)
                  }}
                  v-permission={this.routeButtonsPermission.VIEW}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
                <el-link
                  v-show={row.status === 1 || row.status === 2}
                  type="primary"
                  class={'m-l-4'}
                  onClick={() => {
                    this.cancelHandle(row)
                  }}
                  v-permission={this.routeButtonsPermission.CANCEL_ORDER}
                >
                  {this.routeButtonsTitle.CANCEL_ORDER}
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
