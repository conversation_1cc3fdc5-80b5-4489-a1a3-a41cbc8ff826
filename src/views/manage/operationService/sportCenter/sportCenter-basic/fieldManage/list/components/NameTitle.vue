<template>
  <div class="course-title-container flex">
    <div class="course-img pos-relative">
      <el-image style="width: 220px; height: 140px" :src="cover" />
      <div class="tag-tips" v-if="!item.configFlag">
        <el-tag type="info">请设置场地信息</el-tag>
      </div>
    </div>
    <div class="course-right m-l-8">
      <div class="font-strong font-size-16 line-height-23 line-2">
        {{ item.name | noData }}
      </div>
      <div
        class="line-height-22 m-t-8 font-size-14 color-info flex align-items-center"
      >
        <svg-icon icon-class="field-location" style="flex-shrink: 0" />
        <div class="line-2">{{ item.address | noData }}</div>
      </div>
      <div class="line-height-22 m-t-8 font-size-14 color-info">
        {{ item.facilitiesShowName }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NameTitle',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    cover() {
      const { attach = {} } = this.item
      const field = attach.field || []
      return field.length ? field[0].path : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.course-title-container {
  padding-left: 16px;
  img {
    width: 220px;
    height: 140px;
  }
  .tag-tips {
    position: absolute;
    left: 0;
    top: 0;
    :deep(.el-tag--small) {
      border-radius: 0;
    }
  }
}
</style>
