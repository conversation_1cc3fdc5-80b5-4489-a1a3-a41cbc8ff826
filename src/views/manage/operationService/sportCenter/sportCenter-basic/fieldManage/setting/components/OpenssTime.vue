<template>
  <div class="time-container">
    <basic-tab
      ref="basicTab"
      :tabs-data="tabsData"
      :current="current"
      @tabsChange="tabsChange"
    >
      <template slot="right">
        <el-button
          v-if="!isSingle"
          type="primary"
          @click="
            visible = true
            editId = ''
          "
          >新增场次</el-button
        >
      </template>
    </basic-tab>

    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :table-data="tableData"
    />

    <BasicDialog
      :title="editId ? '编辑场次' : '新增场次'"
      :visible.sync="visible"
      @confirmDialog="confirmDialog"
    >
      <el-form
        label-position="top"
        v-if="visible"
        :model="formModel"
        :rules="rules"
        ref="ruleForm"
        label-width="100px"
        class="demo-ruleForm"
      >
        <el-form-item style="margin-bottom: 0" label="场次时段" />
        <div class="w100 flex">
          <el-form-item prop="staTime">
            <el-time-select
              placeholder="起始时间"
              v-model="formModel.staTime"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '24:00',
                maxTime: formModel.endTime
              }"
              @change="timeChange"
            >
            </el-time-select>
          </el-form-item>
          <span class="m-t-10 color-info m-r-6 m-l-6">~</span>
          <el-form-item prop="endTime">
            <el-time-select
              placeholder="结束时间"
              v-model="formModel.endTime"
              :picker-options="{
                start: '00:00',
                step: '00:30',
                end: '23:59',
                minTime: formModel.staTime
              }"
              @change="timeChange"
            >
            </el-time-select>
          </el-form-item>
        </div>
        <el-form-item label="是否开放预约">
          <el-switch v-model="formModel.openFlag"> </el-switch>
        </el-form-item>
        <el-form-item label="同步添加到" v-if="!editId">
          <el-checkbox-group v-model="checkList">
            <el-checkbox
              v-for="item in tabsData"
              :key="'checkbox-' + item.value"
              :label="item.value"
              :disabled="item.value === current"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </BasicDialog>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import BasicDialog from '@/components/BasicDialog'
import dayjs from 'dayjs'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

export default {
  name: 'OpenssTime',
  components: { BasicTab, BasicDialog },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    isSingle: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formModel: {
        staTime: '',
        endTime: '',
        openFlag: true
      },
      visible: false,
      tableColumn: [
        {
          type: 'index',
          width: 80,
          label: '场次'
        },
        {
          prop: 'times',
          label: '场次时段',
          render: (h, { row }) => {
            return (
              <div>
                {!row.staTime ? (
                  <div class="color-info">点击编辑添加开放时间段</div>
                ) : (
                  <div>
                    {row.staTime} ~ {row.endTime}
                  </div>
                )}
              </div>
            )
          }
        },
        {
          prop: 'openFlag',
          label: '开放预约',
          render: (h, { row }) => {
            return <el-switch v-model={row.openFlag}></el-switch>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-8"
                  onClick={() => {
                    this.editFn(scope.row)
                  }}
                >
                  编辑
                </el-link>
                {!this.isSingle && (
                  <el-link
                    type="danger"
                    onClick={() => {
                      this.delFn(scope.row)
                    }}
                  >
                    删除
                  </el-link>
                )}
              </div>
            )
          }
        }
      ],
      current: 'monday',
      tabsData: [
        {
          label: '周一',
          value: 'monday'
        },
        {
          label: '周二',
          value: 'tuesday'
        },
        {
          label: '周三',
          value: 'wednesday'
        },
        {
          label: '周四',
          value: 'thursday'
        },
        {
          label: '周五',
          value: 'friday'
        },
        {
          label: '周六',
          value: 'saturday'
        },
        {
          label: '周日',
          value: 'sunday'
        }
      ],
      rules: {
        staTime: [
          {
            required: true,
            message: '请选择场次时段起始时间',
            trigger: ['blur', 'change']
          },
          {
            validator: this.timeSelectValidator
          }
        ],
        endTime: [
          {
            required: true,
            message: '请选择场次时段结束时间',
            trigger: ['blur', 'change']
          },
          {
            validator: this.timeSelectValidator
          }
        ]
      },
      editId: '',
      checkList: []
    }
  },
  methods: {
    timeChange() {
      this.$refs.ruleForm.validate()
    },
    timeSelectValidator(rule, value, callback) {
      const isHalfHour = time => time && time.endsWith(':30')
      const startTimeHalf = isHalfHour(this.formModel.staTime)
      const endTimeHalf = isHalfHour(this.formModel.endTime)

      if ((startTimeHalf || endTimeHalf) && startTimeHalf !== endTimeHalf) {
        callback(new Error('起始时间和结束时间间隔需整时'))
      } else {
        callback()
      }
    },
    tabsChange(val) {
      this.current = val
    },
    editFn(val) {
      this.visible = true
      this.editId = val.id
      this.formModel = {
        ...val
      }
    },
    delFn(val) {
      this._value[this.current] = this._value[this.current].filter(
        item => item.id !== val.id
      )
    },
    confirmDialog() {
      this.$refs.ruleForm.validate(valid => {
        if (!valid) return

        if (this.editId) {
          for (let i = 0; i < this.checkList.length; i++) {
            if (this.checkList[i] === this.current) {
              const isFlag = this.checkTimeOverlap(
                this._value[this.checkList[i]],
                this.formModel
              )
              if (!isFlag)
                return this.$message.warning('时间段已经存在或重叠！')
            }
          }
          for (let i = 0; i < this.checkList.length; i++) {
            this._value[this.checkList[i]] = this._value[this.checkList[i]].map(
              item => {
                if (item.id === this.editId) {
                  item = {
                    ...this.formModel
                  }
                }
                return item
              }
            )
          }
        } else {
          for (let i = 0; i < this.checkList.length; i++) {
            const isFlag = this.checkTimeOverlap(
              this._value[this.checkList[i]],
              this.formModel
            )
            if (!isFlag) return this.$message.warning('时间段已经存在或重叠！')
          }
          for (let i = 0; i < this.checkList.length; i++) {
            this._value[this.checkList[i]].push({
              ...this.formModel,
              id: this.generateRandomID() + i
            })
          }
        }
        this.visible = false
      })
    },
    generateRandomID() {
      return (
        new Date().getTime() + '-' + Math.random().toString(36).substr(2, 9)
      )
    },
    formatDate(time) {
      const date = dayjs().format('YYYY-MM-DD')
      return dayjs(`${date} ${time}`, 'YYYY-MM-DD HH:mm')
    },
    checkTimeOverlap(metadata, newEntry) {
      const newStartTime = this.formatDate(newEntry.staTime)
      const newEndTime = this.formatDate(newEntry.endTime)

      if (!newStartTime.isValid() || !newEndTime.isValid()) {
        return this.$message.warning('时间格式不正确，请检查输入！')
      }

      for (let entry of metadata) {
        if (entry.id !== this.editId) {
          const metaStartTime = this.formatDate(entry.staTime)
          const metaEndTime = this.formatDate(entry.endTime)

          // 检查新条目是否完全被现有时间段包含或者有重叠
          const isContained =
            newStartTime.isSameOrAfter(metaStartTime) &&
            newEndTime.isSameOrBefore(metaEndTime)
          const isOverlap =
            newStartTime.isBefore(metaEndTime) &&
            newEndTime.isAfter(metaStartTime)

          if (isContained || isOverlap) {
            return false
          }
        }
      }
      return true
    }
  },
  computed: {
    tableData() {
      return this.value[this.current]
    },
    _value: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  watch: {
    current: {
      handler(val) {
        if (!this.checkList.includes(val)) {
          this.checkList.push(val)
        }
      },
      deep: true,
      immediate: true
    },
    isSingle(val) {
      this.tableColumn[1].label = val ? '开放时间' : '场次时段'
    },
    visible(val) {
      if (!val) {
        this.editId = ''
        this.formModel = {
          staTime: '',
          endTime: '',
          openFlag: true
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-date-editor.el-input) {
  width: auto;
}
</style>
