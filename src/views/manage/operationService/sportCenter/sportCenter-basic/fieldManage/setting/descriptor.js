import ChargeMethod from './components/ChargeMethod'
import { integerValid, validateContact } from '@/utils/validate'
import PreSettingInfo from './components/PreSettingInfo'

export default {
  components: { ChargeMethod, PreSettingInfo },
  data() {
    let _this = this
    return {
      formConfigure: {
        descriptors: {
          name: {
            form: 'input',
            label: '场地名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地名称'
              }
            ],
            attrs: {
              maxLength: 15
            }
          },
          chargeMethod: {
            form: 'component',
            label: '收费方式',
            rule: [
              {
                type: 'string',
                message: '请选择收费方式'
              }
            ],
            render() {
              return (
                <div>
                  <ChargeMethod
                    onInput={e => {
                      _this.chargeMethod = e
                    }}
                    dataList={_this.chargeMethod}
                  />
                </div>
              )
            }
          },
          openStatus: {
            form: 'switch',
            label: '开放预定',
            rule: [{ type: 'boolean' }]
          }
        }
      },
      settingFormConfigure: {
        descriptors: {
          manageIds: {
            form: 'select',
            label: '管理人员',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择管理人员'
              }
            ],
            options: [],
            attrs: {
              filterable: true,
              multiple: true
            }
          },
          contact: {
            form: 'input',
            label: '咨询联系',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入咨询联系'
              },
              {
                validator: validateContact
              }
            ]
          },
          serviceFlag: {
            form: 'switch',
            label: '可选服务',
            rule: [{ type: 'boolean' }],
            customRight: () => {
              return (
                <div class="line-height-32 font-size-14 p-t-42">
                  {this.fromModel.serviceFlag && (
                    <el-link
                      icon={'el-icon-plus'}
                      type={'primary'}
                      onClick={() => this.addServiceDialog()}
                    >
                      添加服务
                    </el-link>
                  )}
                </div>
              )
            }
          },
          serverList: {
            form: 'component',
            label: '',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请添加服务'
              }
            ],
            hidden: true,
            render: () => {
              return (
                <drive-table
                  ref="drive-service-table"
                  columns={this.serviceTableColumn}
                  tableData={this.fromModel.serverList}
                />
              )
            }
          },
          applyNotice: {
            form: 'input',
            label: '预约须知',
            rule: [
              {
                type: 'string',
                message: '请输入预约须知'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 2,
              maxlength: 200,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            }
          },
          limitFlag: {
            form: 'switch',
            label: '预订设置',
            rule: [
              {
                required: true,
                type: 'boolean'
              }
            ]
          },
          preSettingInfo: {
            form: 'component',
            label: '',
            rule: [
              {
                required: true,
                type: 'object',
                message: '请完善预订设置'
              }
            ],
            hidden: true,
            render: () => {
              return (
                <PreSettingInfo
                  ref={'preSettingInfo'}
                  v-model={this.fromModel.preSettingInfo}
                />
              )
            }
          },
          cancelFlag: {
            form: 'switch',
            label: '取消预约',
            rule: [
              {
                required: true,
                type: 'boolean'
              }
            ]
          },
          cancelHour: {
            form: 'component',
            label: '',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请完善预定设置'
              },
              {
                validator: integerValid
              }
            ],
            hidden: true,
            render: () => {
              return (
                <div class={'flex align-items-center'}>
                  <span style={'flex-shrink: 0'}>距预约场次开始时间</span>
                  <el-input
                    style={'width: 150px'}
                    class={'m-l-4 m-r-4'}
                    placeholder={'请输入'}
                    v-model={this.fromModel.cancelHour}
                    maxLength={4}
                  >
                    <span slot="append" class="color-black">
                      小时
                    </span>
                  </el-input>
                  <span style={'flex-shrink: 0'}>内不可取消</span>
                </div>
              )
            }
          },
          examineFlag: {
            form: 'switch',
            label: '预定审核',
            rule: [
              {
                required: true,
                type: 'boolean'
              }
            ]
          }
        }
      },
      serviceFormConfigure: {
        descriptors: {
          showName: {
            form: 'input',
            label: '服务名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入服务名称'
              }
            ],
            attrs: {
              maxLength: 20
            }
          },
          price: {
            form: 'input',
            label: '服务价格',
            rule: [
              {
                type: 'string',
                message: '请输入服务价格',
                validator: 'validateDecimal'
              }
            ],
            customRight: () => {
              return <div class="line-height-32">元/小时</div>
            }
          },
          configDesc: {
            form: 'input',
            label: '公开备注',
            rule: [
              {
                type: 'string',
                message: '请输入公开备注'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 2,
              maxlength: 100,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            }
          }
        }
      }
    }
  }
}
