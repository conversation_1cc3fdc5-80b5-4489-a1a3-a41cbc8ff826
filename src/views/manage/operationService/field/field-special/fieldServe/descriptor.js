import { validateDecimal } from '@/utils/validate'
export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          name: {
            form: 'input',
            label: '场地服务',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地服务'
              },
              {
                max: 15,
                message: '字符长度不能超过15个'
              }
            ]
          },
          venueFees: {
            form: 'radio',
            label: '服务收费',
            options: [
              {
                label: '免费',
                value: 0
              },
              {
                label: '按次收费',
                value: 1
              },
              {
                label: '按时收费',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择服务收费'
              }
            ]
          },
          price: {
            form: 'input',
            label: '收费标准',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入收费标准'
              },
              {
                validator: validateDecimal,
                trigger: 'blur',
                message: '最多保留两位小数'
              },
              {
                validator: (rule, value, callback) => {
                  if (value > 99999999.99) {
                    callback(new Error('不得超出8位数'))
                  } else {
                    callback()
                  }
                }
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">元/次</span>
                </div>
              )
            }
          },
          chargesYuan: {
            form: 'input',
            label: '收费标准',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入收费标准'
              },
              {
                validator: validateDecimal,
                trigger: 'blur',
                message: '最多保留两位小数'
              },
              {
                validator: (rule, value, callback) => {
                  if (value > 99999999.99) {
                    callback(new Error('不得超出8位数'))
                  } else {
                    callback()
                  }
                }
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">元/小时</span>
                </div>
              )
            }
          }
        }
      }
    }
  }
}
