<template>
  <div class="conference-room">
    <div class="head">
      <div class="building-text">时间</div>
      <div class="floor-text">会议室名称</div>
      <div class="line"></div>
    </div>
    <div v-for="(item, i) in tableData" :key="i" class="item">
      <el-popover
        placement="right-start"
        width="320"
        popper-class="field-popover"
        trigger="hover"
      >
        <div class="tooltip">
          <div class="image-box w100">
            <div class="img w100">
              <el-image
                class="w100"
                style="height: 162px"
                v-if="Object.keys(item.placeInfo.coverAttachMap).length > 0"
                :src="
                  item.placeInfo.coverAttachMap.coursePic[0]?.path ||
                  require('../../img.png')
                "
              />
            </div>
            <div class="gratis" v-if="item.placeInfo.venueFees === 0">免费</div>
            <div class="gratis" v-else-if="item.placeInfo.venueFees === 1">
              {{ item.placeInfo.price }}元/小时
            </div>
            <div class="gratis" v-else-if="item.placeInfo.venueFees === 2">
              {{ item.placeInfo.price }}元/每次
            </div>
            <div class="gratis" v-else-if="item.placeInfo.venueFees === 3">
              {{ item.placeInfo.price }}元/半小时
            </div>
          </div>

          <!-- 渲染预定状态和场地信息 -->
          <p class="m-t-10 m-b-8 font-size-14 color flex">
            <span>
              <svg-icon icon-class="field-number" class="font-size-14 m-r-6" />
            </span>
            <span>{{ item.placeInfo.number + '人' }} / </span>
            <span
              :class="
                item.placeInfo.scheduledReview
                  ? 'color-primary'
                  : 'color-success'
              "
              class="p-l-6 p-r-6"
            >
              {{ item.placeInfo.scheduledReview ? '提前预定' : '免预定' }}
            </span>
            <span>
              / {{ item.placeInfo.status === 1 ? '开放' : '关闭' }} /</span
            >
            <span class="p-l-6 p-r-6">{{
              item.placeInfo.placeArea + 'm²'
            }}</span>
          </p>

          <!-- 地址部分 -->
          <p class="font-size-14 m-b-8 color flex">
            <span class="inline-block">
              <svg-icon
                icon-class="field-location"
                class="font-size-14 m-r-6"
              />
            </span>
            <span class="inline-block">{{ item.placeInfo.address }}</span>
          </p>

          <!-- 场地分配情况 -->
          <p class="m-b-8 font-size-14 color flex">
            <span>
              <svg-icon icon-class="field-unfold" class="font-size-14 m-r-6" />
            </span>
            <span>{{ item.placeInfo.allocationStr }}</span>
          </p>

          <!-- 审核人和电话 -->
          <p class="font-size-14 color flex align-items-center">
            <span>
              <svg-icon icon-class="field-user" class="font-size-14 m-r-6" />
            </span>
            <span class="reviewer line-1">{{ item.placeInfo.reviewer }} </span>
            <span class="p-l-6" v-if="item.placeInfo.phone"
              >/ {{ item.placeInfo.phone }}</span
            >
          </p>
        </div>

        <!-- 参考部分显示的简要信息 -->
        <div class="head-content" slot="reference">
          <div class="title line-1">{{ item.placeInfo.name }}</div>
          <p class="brief-info-1">
            <span class="m-r-6">
              <svg-icon icon-class="field-number" class="font-size-14" />
            </span>
            <span class="p-r-6">{{ item.placeInfo.number + '人' }} /</span>
            <span
              :class="
                item.placeInfo.scheduledReview
                  ? 'color-primary'
                  : 'color-success'
              "
            >
              {{ item.placeInfo.scheduledReview ? '提前预定' : '免预定' }}
            </span>
            <span class="p-l-6 p-r-6"
              >/ {{ item.placeInfo.placeArea + 'm²' }}</span
            >
          </p>
          <p class="font-size-14 color flex align-items-center"></p>
          <p class="brief-info-1">
            <span class="inline-block m-r-6">
              <svg-icon icon-class="field-location" class="font-size-14" />
            </span>
            <span class="line-1">{{ item.placeInfo.address }}</span>
          </p>
        </div>
      </el-popover>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConferenceRoom',
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="scss">
.head-content {
  border: 1px solid rgba(231, 231, 231, 0.898);
  padding: 16px;
  height: 125px;
  box-sizing: border-box;
  .title {
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 23px;
    margin-bottom: 8px;
  }

  .brief-info-1 {
    display: flex;
    align-items: start;
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 14px;
    color: #999999;
    line-height: 22px;
  }
}

.el-popover {
  .tooltip {
    width: 100%;
    opacity: 1;
    p {
      line-height: 20px;
      img {
        width: 18px;
        height: 18px;
      }
    }
    .image-box {
      position: relative;
      .gratis {
        position: absolute;
        left: -4px;
        top: 4px;
        width: auto;
        padding-left: 10px;
        padding-right: 10px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        font-size: 12px;
        color: #e7e7e7;
        @include background_color(--color-primary);
        border-radius: 0 3px 3px 0;
        opacity: 1;
        &:before {
          content: '';
          position: absolute;
          left: 0px;
          top: 28px;
          border-top: 2px solid #0038b3;
          border-bottom: 2px solid transparent;
          border-left: 2px solid transparent;
          border-right: 2px solid #0038b3;
        }
      }
    }
  }
}

.conference-room {
  width: 287px;
  border-radius: 3px 3px 3px 3px;
  margin-right: 10px;
}

.head {
  width: 100%;
  height: 40px;
  flex: none;
  background-color: #e7e7e7;
  border-radius: 3px 0 0 0;
  font-size: 12px;
  position: relative;

  .building-text {
    position: absolute;
    top: 7px;
    right: 62px;
    color: #7a7a7a;
  }
  .floor-text {
    position: absolute;
    left: 48px;
    top: 20px;
    color: #7a7a7a;
  }
  .line {
    background-color: #7a7a7a;
    &::after {
      content: '';
      display: block;
      width: 274px;
      height: 1px;
      background-color: #c8c8c8;
      position: absolute;
      bottom: 19px;
      left: 8px;
      transform: rotate(6deg);
    }
  }
}
</style>
