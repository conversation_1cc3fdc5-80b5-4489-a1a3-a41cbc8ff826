import { getFieldExamineStatus } from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'placeInfoName',
          label: '场地名称',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'entName',
          label: '申请用户',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'contact',
          label: '联系人'
        },
        {
          prop: 'phone',
          label: '联系电话'
        },
        {
          prop: 'peopleNum',
          label: '预约人数'
        },
        {
          prop: 'applydDateTime',
          label: '预定时间',
          width: 230,
          render: (h, scope) => {
            const timeSlots = scope.row.applydDateTime
            const timeSlotsArray = timeSlots.split('\n')
            return h(
              'div',
              timeSlotsArray.map((slot, index) => {
                return h('div', { key: index }, [slot])
              })
            )
          }
        },
        {
          prop: 'theme',
          label: '会议主题 ',
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '申请时间',
          search: {
            type: 'daterange',
            options: []
          },
          sortable: true
        },
        {
          prop: 'result',
          label: '申请状态',
          render: (h, scope) => {
            return <div>{getFieldExamineStatus(h, scope.row.result)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 160,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.VIEW}
                  type="primary"
                  class="m-r-12"
                  onClick={() => {
                    this.skipDetails(scope.row)
                  }}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
                {scope.row.result === 1 || scope.row.result === 3 ? (
                  <el-link
                    v-permission={this.routeButtonsPermission.CANCEL}
                    type="primary"
                    class="m-r-12"
                    onClick={() => {
                      this.cancel(scope.row)
                    }}
                  >
                    {this.routeButtonsTitle.CANCEL}预约
                  </el-link>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ]
    }
  }
}
