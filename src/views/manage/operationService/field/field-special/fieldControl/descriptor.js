import DictPopover from '../components/DictPopover'
import Managers from '../components/Managers'
import OpenssTime from '../components/OpenssTime.vue'
import { validateContact, validateDecimal } from '@/utils/validate'
// import TimeLimit from '../components/TimeLimit.vue'

export default {
  components: {
    DictPopover,
    Managers,
    OpenssTime
    // TimeLimit
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          coverAttachIds: {
            form: 'component',
            label: '场地封面',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传场地封面',
                trigger: ['change', 'blur']
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <span class="color-info font-size-14">
                  建议上传388*144大小的图片
                </span>
              )
            },
            props: {
              uploadData: {
                type: 'coursePic'
              },
              accept: 'image/*'
            }
          },
          name: {
            form: 'input',
            label: '场地名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地名称'
              },
              {
                max: 20,
                message: '请输入场地名称、不超过20字'
              }
            ]
          },
          address: {
            form: 'input',
            label: '场地地址',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地地址'
              },
              {
                max: 30,
                message: '请输入场地名称、不超过30字'
              }
            ]
          },
          // venueType: {
          //   form: 'select',
          //   label: '场地类型',
          //   rule: [
          //       {
          //           required: true,
          //           type: 'number',
          //           message: '请选择场地类型'
          //       }
          //   ],
          //   options: [
          //     {
          //       label: '运动中心',
          //       value: 1
          //     },
          //     {
          //         label: '会议室',
          //         value: 2
          //     },
          //   ],
          // },
          parkId: {
            form: 'select',
            label: '所在园区',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择所在园区'
              }
            ],
            options: []
          },
          // targetAudience: {
          //   form: 'checkbox',
          //   label: '预定对象',
          //   options: [
          //     {
          //       label: '企业端用户',
          //       value: 1
          //     },
          //     {
          //       label: '移动端用户',
          //       value: 2
          //     }
          //   ],
          //   rule: [
          //     {
          //       required: true,
          //       type: 'array',
          //       message: '请选择预定对象'
          //     }
          //   ]
          // },
          openingStr: {
            form: 'input',
            label: '场地开放时间',
            rule: [
              {
                required: true,
                type: 'string',
                message: '仅供描述展示使用'
              }
            ],
            attrs: {
              maxlength: 30
            }
          },
          timeInfo: {
            form: 'component',
            label: '可预约场次',
            rule: [
              {
                required: true,
                type: 'object',
                message: '请选择可预约场次'
              }
            ],
            render: () => {
              return (
                <div>
                  <openss-time
                    ref="openssTime"
                    v-model={this.fromModel.timeInfo}
                  />
                </div>
              )
            }
          },
          allocation: {
            form: 'select',
            label: '场地设施',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择场地设施'
              }
            ],
            options: [],
            props: {
              multiple: true
            },
            customRight: () => {
              return (
                <div class="line-height-30">
                  <dict-popover
                    type="course_type"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          placeArea: {
            form: 'input',
            label: '场地面积',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地面积',
                pattern: /^([1-9][\d]{0,7}|0)(\.[\d]{1,2})?$/
              }
            ],
            customRight: () => {
              return <div class="p-t-6">m²</div>
            }
          },
          number: {
            form: 'input',
            label: '可容纳人数',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入可容纳人数'
              },
              {
                pattern: /^[0-9]{1,6}$/,
                message: '请输入可容纳人数、不超过6位'
              }
            ]
          },
          venueFees: {
            form: 'radio',
            label: '场地收费',
            options: [
              {
                label: '免费',
                value: 0
              },
              {
                label: '按次收费',
                value: 1
              },
              {
                label: '按时收费',
                value: 2
              },
              {
                label: '按半时收费',
                value: 3
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择场地收费类型'
              }
            ],
            events: {
              change: () => {
                this.$set(this.fromModel, 'outPrice', '')
              }
            }
          },
          price: {
            form: 'input',
            label: '园内收费标准',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入收费标准'
              },
              {
                validator: validateDecimal
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">元/次</span>
                </div>
              )
            }
          },
          chargesYuan: {
            form: 'input',
            label: '园内收费标准',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入收费标准'
              },
              {
                validator: validateDecimal
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">
                    {this.fromModel.venueFees === 2 ? '元/小时' : '元/半小时'}
                  </span>
                </div>
              )
            }
          },
          outPrice: {
            form: 'input',
            label: '园外收费标准',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入收费标准'
              },
              {
                validator: validateDecimal
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">
                    {this.fromModel.venueFees === 1
                      ? '元/次'
                      : this.fromModel.venueFees === 2
                      ? '元/小时'
                      : '元/半小时'}
                  </span>
                </div>
              )
            }
          },
          remark: {
            form: 'input',
            label: '预约须知',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入预约须知'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 3,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            }
          },
          introduce: {
            form: 'component',
            label: '场地详情',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地详情'
              }
            ],
            componentName: 'Tinymce'
          },
          attachIds: {
            form: 'component',
            label: '场地图片',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传场地图片',
                trigger: ['change', 'blur']
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <span class="color-info font-size-14">
                  文件大小不超过10M、最多上传5个
                </span>
              )
            },
            props: {
              uploadData: {
                type: 'coursePic'
              },
              mulity: true,
              accept: 'image/*',
              maxLength: 5,
              maxSize: 10,
              limit: 5
            }
          },
          status: {
            form: 'radio',
            label: '开放状态',
            options: [
              {
                label: '开启',
                value: 1
              },
              {
                label: '关闭',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择开放状态'
              }
            ]
          },
          contact: {
            form: 'select',
            label: '管理人员',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择管理人员'
              }
            ],
            attrs: {
              filterable: true,
              multiple: true
            },
            options: [],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <managers
                    type="course_label"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                validator: (rule, value, callback) => {
                  validateContact(rule, value, callback, false)
                }
              }
            ]
          },
          scheduledReview: {
            form: 'radio',
            label: '预定审核',
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择预定审核'
              }
            ],
            options: [
              {
                label: '开启',
                value: true
              },
              {
                label: '关闭',
                value: false
              }
            ]
            // events: {
            //   change: () => {
            //     this.formConfigure.descriptors.timeLimit.hidden =
            //       !this.fromModel.scheduledReview
            //   }
            // }
          },
          // timeLimit: {
          //   form: 'radio',
          //   hidden: true,
          //   label: '预定时间限制',
          //   rule: [
          //     {
          //       required: true,
          //       type: 'boolean',
          //       message: '请选择预定时间限制'
          //     }
          //   ],
          //   options: [
          //     {
          //       label: '开启',
          //       value: true
          //     },
          //     {
          //       label: '关闭',
          //       value: false
          //     }
          //   ],
          //   customTips: () => {
          //     return (
          //       this.fromModel.timeLimit && (
          //         <div>
          //           <time-limit ref="timeLimit" />
          //         </div>
          //       )
          //     )
          //   }
          // },
          venueServicesFlag: {
            form: 'radio',
            label: '场地服务',
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择场地服务'
              }
            ],
            options: [
              {
                label: '开启',
                value: true
              },
              {
                label: '关闭',
                value: false
              }
            ],
            events: {
              change: () => {
                this.formConfigure.descriptors.venueServices.hidden =
                  !this.fromModel.venueServicesFlag
              }
            }
          },
          venueServices: {
            form: 'checkbox',
            label: '服务提供',
            hidden: false,
            options: [],
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择服务提供'
              }
            ]
          }
        }
      }
    }
  }
}
