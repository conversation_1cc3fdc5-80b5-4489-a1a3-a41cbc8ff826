import Vue from 'vue'
import CourseTitle from '../components/CourseTitle'
Vue.component('CourseTitle', CourseTitle)
export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'name',
          label: '场地名称',
          width: 500,
          search: {
            type: 'input'
          },
          render: (h, scope) => {
            return (
              <div>
                <CourseTitle key={scope.row.id} item={scope.row} />
              </div>
            )
          }
        },
        {
          prop: 'address',
          label: '场地位置'
        },
        {
          prop: 'number',
          label: '容纳人数'
        },
        {
          prop: 'placeArea',
          label: '场地面积(m²)'
        },
        {
          prop: 'status',
          label: '开放状态',
          search: {
            type: 'select',
            options: [
              {
                label: '开放',
                value: 1
              },
              {
                label: '关闭',
                value: 2
              }
            ]
          },
          render: (h, scope) => {
            return (
              <div>
                {scope.row.status === 1 ? (
                  <span class="color-success">开启</span>
                ) : (
                  <span class="color-danger">关闭</span>
                )}
              </div>
            )
          }
        },
        {
          prop: 'scheduledReview',
          label: '预定审核',
          search: {
            type: 'select',
            options: [
              {
                label: '开放',
                value: true
              },
              {
                label: '关闭',
                value: false
              }
            ]
          },
          render: (h, scope) => {
            return (
              <div>
                {scope.row.scheduledReview ? (
                  <span class="color-success">开启</span>
                ) : (
                  <span class="color-danger">关闭</span>
                )}
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-10"
                  v-permission={this.routeButtonsPermission.VIEW}
                  onClick={() => {
                    this.skipDetails(scope.row)
                  }}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
                <el-dropdown>
                  <el-link
                    type="primary"
                    v-permission={[
                      ...this.routeButtonsPermission.SWITCH,
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                  >
                    更多
                  </el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <el-link
                        type="#909399"
                        underline={false}
                        onClick={() => {
                          this.releaseVisit(scope.row.id, scope.row.status)
                        }}
                        v-permission={this.routeButtonsPermission.SWITCH}
                      >
                        {scope.row.status === 1 ? '关闭' : '开放'}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="#909399"
                        underline={false}
                        v-permission={this.routeButtonsPermission.EDIT}
                        onClick={() => {
                          this.editVisit(scope.row, 'edit')
                        }}
                      >
                        {this.routeButtonsTitle.EDIT}
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="danger"
                        underline={false}
                        onClick={() => {
                          this.delVisit(scope.row)
                        }}
                        v-permission={this.routeButtonsPermission.DELETE}
                      >
                        {this.routeButtonsTitle.DELETE}
                      </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ]
    }
  }
}
