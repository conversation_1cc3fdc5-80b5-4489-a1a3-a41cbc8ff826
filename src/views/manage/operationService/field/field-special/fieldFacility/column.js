import OperationContainer from './operationContainer'
export default {
  components: {
    OperationContainer
  },
  data() {
    return {
      tableColumn: [
        {
          prop: 'name',
          label: '场地设施',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <OperationContainer
                item={scope}
                listLength={this.listLength}
                isSortable={this.isSortable}
                onEditHandler={() => {
                  this.edit(scope.row)
                }}
                onDeleteHandle={() => {
                  this.delete(scope.row)
                }}
                onMoveDown={() => {
                  this.moveDown(scope.$index, scope.row)
                }}
                onMoveUp={() => {
                  this.moveUp(scope.$index, scope.row)
                }}
                onMoveTop={() => {
                  this.moveTop(scope.$index, scope.row)
                }}
                onMoveBottom={() => {
                  this.moveBottom(scope.$index, scope.row)
                }}
              />
            )
          }
        }
      ]
    }
  }
}
