<template>
  <!-- 新增场地弹框 -->
  <basic-drawer
    :size="700"
    :title="drawerTitle"
    :visible.sync="_visible"
    @confirmDrawer="confirmDrawer"
  >
    <div v-if="_visible">
      <driven-form
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
      <!-- 场地看板 -->
      <venue-signage ref="venueSignage" />
    </div>
  </basic-drawer>
</template>

<script>
import Descriptors from './descriptor'
import VenueSignage from '../VenueSignage'
import {
  getMeetingSelect,
  getMeetingInfo,
  internalReservation
} from '../../api'

export default {
  name: 'InternalReservation',
  components: {
    VenueSignage
  },
  mixins: [Descriptors],
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.fromModel = {}
        this.$refs.venueSignage?.resetData()
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  provide() {
    return {
      internalReservation: this
    }
  },
  data() {
    return {
      drawerTitle: '内部预约',
      fromModel: {},
      placeIdStr: ''
    }
  },
  mounted() {
    this.getMeetingSelect()
  },
  methods: {
    changePlaceId(val) {
      if (this.fromModel.selectDate) {
        this.$set(this.fromModel, 'selectDate', '')
      }
      if (val) {
        this.placeIdStr = this.formConfigure.descriptors.placeId.options.find(
          item => item.value === val
        ).label
      }
      this.$refs.venueSignage.resetData()
    },
    changeDate() {
      if (this.fromModel.selectDate && this.fromModel.placeId) {
        this.getMeetingInfo()
      }
    },
    getMeetingInfo() {
      const params = {
        placeId: this.fromModel.placeId,
        date: this.fromModel.selectDate
      }
      getMeetingInfo(params).then(res => {
        const { bookings = [], timeslots = [], venues = [] } = res || {}
        this.$refs.venueSignage.setBookingData(bookings, timeslots, venues)
        this.$refs.venueSignage.changeKanban()
      })
    },
    getMeetingSelect() {
      getMeetingSelect().then(res => {
        this.formConfigure.descriptors.placeId.options = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const infoData = this.$refs.venueSignage.infoData
          const bookings = infoData.some(item => item.sessionId.length > 0)
          if (!bookings) {
            this.$message.warning('请选择预约场次')
            return
          }

          const data = {
            ...this.fromModel,
            sessionList: infoData.map(item => ({
              selectDate: item.selectDate,
              sessionId: item.sessionId.map(row => row.sessionId)
            }))
          }
          internalReservation(data).then(() => {
            this.$message.success('预约成功')
            this._visible = false
            this.$emit('refresh')
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
