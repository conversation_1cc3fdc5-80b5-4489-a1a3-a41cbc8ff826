<template>
  <div class="steps-container flex align-items-center justify-content-between">
    <div
      class="flex steps-wrapper"
      v-for="(item, index) in stepsData"
      :key="index"
    >
      <div class="steps-item flex">
        <div class="left flex align-items-center">
          <div
            v-if="active === index"
            class="circle active-circle font-size-16 flex align-items-center justify-content-center font-strong"
          >
            <span>{{ index + 1 }}</span>
          </div>
          <div
            v-else
            class="circle font-size-16 flex align-items-center justify-content-center font-strong"
            :class="getCircleStyle(index, item.label)"
          >
            <svg-icon
              v-if="active > index"
              :class="getSvgStyle(item.label).class"
              :icon-class="getSvgStyle(item.label).svgClass"
            />
            <span v-else>{{ index + 1 }}</span>
          </div>
        </div>
        <div class="right">
          <div
            class="font-size-16 color-text-placeholder title font-strong"
            :class="getLabelStyle(index, item.label)"
          >
            {{ item.label }}
          </div>
          <div
            v-if="active > index"
            class="font-size-14 color-text-placeholder status"
          >
            {{ applyData[item.prop] || '-' }}
          </div>
          <div v-else class="font-size-14 color-primary status">
            {{ applyData[item.prop] || '-' }}
          </div>
        </div>
      </div>

      <div
        v-if="index < stepsData.length - 1"
        class="steps-line"
        :class="getLineBackground(index)"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterParkApplyBasicSteps',
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      stepsData: [
        {
          label: '申请表提交',
          prop: 'createTime'
        },
        {
          label: '园区审批',
          prop: 'resultText'
        }
      ]
    }
  },
  computed: {
    active() {
      const status = new Map()
      status.set(1, 1)
      status.set(2, 1)
      status.set(3, 3)
      status.set(4, 3)
      status.set(5, 0)
      return status.get(this.applyData.result)
    }
  },
  methods: {
    getCircleStyle(index, label) {
      return this.applyData.result === 4 && label === '园区审批'
        ? 'circle-danger'
        : this.active > index
        ? 'circle-primary'
        : ''
    },

    getSvgStyle(label) {
      const defaultStyle = {
        svgClass: 'check',
        class: 'color-primary'
      }
      return label === '园区审批'
        ? this.applyData.result === 4
          ? {
              svgClass: 'close',
              class: 'color-danger'
            }
          : defaultStyle
        : defaultStyle
    },

    getLineBackground(index) {
      return this.active > index ? 'line-primary' : ''
    },

    getLabelStyle(index, label) {
      const { active, applyData } = this
      return active === index
        ? 'label-primary'
        : active > index
        ? label === '园区审批' && applyData.result === 4
          ? 'text-danger'
          : 'label-text-primary'
        : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.steps-container {
  width: 100%;

  .steps-wrapper {
    flex: 1;

    &:nth-last-child(1) {
      flex: 0 0 130px;
      width: 130px;
    }

    .steps-item {
      flex: 0 0 140px;
      padding-left: 10px;
      .left {
        margin-right: 16px;
        height: 30px;
        line-height: 30px;

        .circle {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid;
          @include font_color(--color-text-placeholder);
          @include border_color(--color-text-placeholder);
        }

        .active-circle {
          @include font_color(--color-white);
          @include background_color(--color-primary);
          @include border_color(--color-primary);
        }

        .circle-primary {
          @include font_color(--color-primary);
          @include border_color(--color-primary);
        }

        .circle-danger {
          @include font_color(--color-danger);
          @include border_color(--color-danger);
        }
      }
      .right {
        .title {
          height: 30px;
          line-height: 30px;
          margin-bottom: 6px;
        }

        .label-text-primary {
          @include font_color(--color-text-primary);
        }

        .label-primary {
          @include font_color(--color-primary);
        }

        .text-danger {
          @include font_color(--color-danger);
        }

        .status {
          line-height: 22px;
        }
      }
    }

    .steps-line {
      flex: 1;
      height: 2px;
      @include background_color(--color-text-placeholder);
      margin: 13px 16px 0;
    }

    .line-primary {
      @include background_color(--color-primary);
    }
  }
}
</style>
