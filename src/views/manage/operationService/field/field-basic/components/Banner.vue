<template>
  <el-carousel height="386px" :interval="6000">
    <el-carousel-item v-for="banner in banners" :key="banner.id">
      <el-image class="wh100" :src="banner.path" fit="cover"></el-image>
    </el-carousel-item>
  </el-carousel>
</template>

<script>
export default {
  name: 'FieldBanner',
  props: {
    banners: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" scoped></style>
