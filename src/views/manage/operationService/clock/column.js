export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '公司名称',
          search: {
            type: 'input'
          },
          minWidth: '180',
          showOverflowTooltip: true
        },
        {
          prop: 'name',
          label: '姓名',
          search: {
            type: 'input'
          },
        },
        {
          prop: 'mobile',
          label: '手机号',
          search: {
            type: 'input'
          },
        },
        {
          prop: 'picResp',
          label: '打卡照片',
          render: (h, { row }) =>
            row.picResp?.pic?.length && (
              <el-link
                type="primary"
                onClick={() => this.goView(row.picResp?.pic)}
              >
                查看
              </el-link>
            )
        },
        {
          prop: 'submitTime',
          label: '打卡时间'
        },
        {
          prop: 'statusStr',
          label: '状态',
          render: (h, { row }) => {
            const type = {
              1: 'warning',
              2: 'success',
              3: 'danger'
            }
            return h(
              'el-tag',
              {
                props: {
                  type: type[row.status]
                }
              },
              row.statusStr
            )
          }
        },
        //  操作
        {
          prop: 'action',
          label: '操作',
          fixed: 'right',
          width: '120',
          render: (h, { row }) => {
            return (
              <div>
                <el-link type="success" disabled={row.status !==1} onClick={() => this.pass(row, 1)}>发送中奖短信</el-link>
                {/*<el-link type="danger" class="m-l-8" disabled={row.status !==1}  onClick={() => this.pass(row,2)}>拒绝</el-link>*/}
              </div>
            )
          }
        }
      ]
    }
  },
}
