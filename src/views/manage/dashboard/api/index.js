import request from '@/utils/request'

// 日程创建
export function programmeCreate(data) {
  return request({
    url: `/programme/create`,
    method: 'post',
    data
  })
}
// 获取日程列表
export function programmeDayList(data) {
  return request({
    url: `/programme/get_day_list`,
    method: 'post',
    data
  })
}
// 获取月列表
export function programmeMonthList(data) {
  return request({
    url: `/programme/get_month_list`,
    method: 'post',
    data
  })
}
// 获取资讯
export function getInformationList(params) {
  return request({
    url: `/hatch/ent/notice/page`,
    method: 'get',
    params,
    isTable: true
  })
}
// 获取公告详情
export function getInformationDetails(id) {
  return request({
    url: `/hatch/notice/get?id=${id}`,
    method: 'get'
  })
}
