<template>
  <div class="dashboard-right">
    <el-scrollbar style="height: 100%">
      <div class="line"></div>
      <div class="matters-container">
        <div class="calendar-wrapper">
          <Calendar
            :time="time"
            ref="calendarRef"
            @update="programmeDayList"
          ></Calendar>
          <div class="line"></div>
        </div>
        <!-- 日程 -->
        <el-scrollbar style="width: 100%; height: 148px">
          <div class="wh100 visit-wrapper">
            <template v-if="programmeList && programmeList.length">
              <div
                class="visit-item"
                v-for="item in programmeList"
                :key="item.id"
                :class="{ disabled: isDisabledHandle(item) }"
              >
                {{ item.time }} {{ item.title }}
              </div>
            </template>
            <div v-else class="empty-programme">暂无日程</div>
          </div>
        </el-scrollbar>
        <div class="add-wrapper">
          <div class="add-schedule" @click="docVisible = true">
            <i class="el-icon-plus"></i> 新增日程
          </div>
          <div class="line"></div>
        </div>
      </div>
      <!--公告 -->
      <Notice />
    </el-scrollbar>
    <!-- 新增日程弹出框 -->
    <dialog-cmp
      title="新增日程"
      :visible.sync="docVisible"
      @confirmDialog="confirmDialog"
      width="400px"
    >
      <el-form
        ref="docFromRef"
        :model="fromModel"
        :rules="formRules"
        label-position="left"
        label-width="60px"
      >
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="fromModel.title"
            placeholder="请输入标题"
            maxLength="20"
          />
        </el-form-item>
        <el-form-item label="时间" prop="time">
          <el-time-picker
            style="width: 100%"
            v-model="fromModel.time"
            format="HH:mm"
            value-format="HH:mm"
            placeholder="请选择时间"
          >
          </el-time-picker>
        </el-form-item>
      </el-form>
    </dialog-cmp>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import { local } from '@/utils/storage'
import Calendar from './components/calendar.vue'
import Notice from './components/notice.vue'
import createCalendarMixins from './create-calendar'
import { programmeCreate, programmeDayList } from '@/views/manage/dashboard/api'
import dayjs from 'dayjs'

export default {
  name: 'DashboardRight',
  components: { Calendar, Notice },
  mixins: [createCalendarMixins],
  data() {
    return {
      // 最近访问数组
      visitRoute: [],
      time: '',
      docVisible: false, //控制新增日程弹框
      fromModel: {
        title: '',
        time: ''
      },
      formRules: {
        title: [
          { required: true, message: '请输入标题', target: ['change', 'blur'] }
        ],
        time: [
          { required: true, message: '请选择时间', target: ['change', 'blur'] }
        ]
      },
      programmeList: [] // 日程列表
    }
  },
  computed: {
    ...mapGetters(['wholeAccessedRoutes']),
    ...mapState({
      userId: state => state.user.id
    })
  },
  watch: {
    docVisible(val) {
      if (val) {
        this.fromModel = this.$options.data().fromModel
        this.$refs.docFromRef && this.$refs.docFromRef.resetFields()
      }
    }
  },
  mounted() {
    this.visitRoute = local.GET_HISTORY_VISIT() || []
    this.programmeDayList()
  },
  created() {
    this.getTime()
  },
  methods: {
    netDiskHandle() {
      this.$router.push('/netdisk/index')
    },
    taskHandle() {
      this.$router.push('/taskObserve/index')
    },
    isDisabledHandle(item) {
      const nowDateStamp = Date.now()
      const itemStamp = dayjs(item.date).valueOf()
      return nowDateStamp > itemStamp
    },
    // 获取列表
    programmeDayList() {
      const time = this.$refs.calendarRef.selectTimeStr
      programmeDayList({ dateDay: time }).then(res => {
        this.programmeList = res || []
      })
    },
    // 创建日程
    confirmDialog() {
      this.$refs.docFromRef.validate(valid => {
        if (!valid) return
        const time = this.$refs.calendarRef.selectTimeStr
        const params = {
          title: this.fromModel.title,
          date: `${time} ${this.fromModel.time}`,
          userId: this.userId
        }
        programmeCreate(params).then(() => {
          this.$toast.success('新增成功')
          this.programmeDayList()
          this.$refs.calendarRef.programmeMonthList()
          this.docVisible = false
        })
      })
    },
    getTime() {
      let today = new Date()
      let year = today.getFullYear()
      let month = today.getMonth() + 1
      let days = today.getDate()
      let newTime = `${year}-${this.formateDate(month)}-${this.formateDate(
        days
      )}`
      this.time = newTime
    },
    formateDate(str) {
      return str < 10 ? '0' + str : str
    },
    // 去详情页面
    skipPage(route, item) {
      const list = [...this.visitRoute]
      const visit = {
        path: item.path + `/${route.path}`,
        meta: route.meta
      }
      if (!list.includes(visit)) {
        list.unshift(visit)
      } else {
        const index = list.findIndex(i => i.meta.title === item.meta.title)
        list.splice(index, 1)
        list.unshift(visit)
      }
      local.SET_HISTORY_VISIT(list.slice(0, 3))
      this.$router.push({
        path: item.path + `/${route.path}`
      })
    },
    //点击新增日程
    addprogramme() {
      this.docVisible = true
    },
    //点击编辑
    handleCommand(command) {
      if (command === 'a') {
        this.docVisible = true
      }
    },
    skipVisitPage(item) {
      this.$router.push({
        path: item.path
      })
    },

    resetMenu(menu) {
      return menu
        ? menu.filter(val => !val.hidden && val.meta.title !== '首页')
        : []
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-right {
  background: #fff;
  width: 403px;
  height: 100%;
  flex-shrink: 0;
  .fast-wrapper {
    padding: 20px 44px 16px 44px;
    .fast-item-wrapper {
      margin-right: 40px;
    }
  }
  .matters-container {
    padding-bottom: 16px;
  }
  .calendar-wrapper {
    padding: 16px 24px 0;
  }
  .line {
    height: 1px;
    background: #ebedf1;
  }
  .calendar-box {
    width: 100%;
    height: 285px;
  }
  .tips-container {
    height: 40px;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 350;
    color: #ed7b2f;
    line-height: 22px;
    display: flex;
    align-items: center;
  }
  .quick-entry-ul {
    display: flex;
    flex-wrap: wrap;
    height: 305px;
    max-height: 522px;
    overflow-y: auto;
    overflow-x: hidden;
    padding-top: 14px;
  }

  .visit-wrapper {
    padding: 8px 24px;
    .visit-item {
      font-size: 14px;
      font-weight: 350;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      margin-bottom: 8px;
      word-break: break-all;
      &:last-child {
        margin-bottom: 0;
      }
      &.disabled {
        color: rgba(0, 0, 0, 0.4);
      }
    }
    .empty-programme {
      text-align: center;
      padding-top: 40px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 14px;
    }
  }
  .add-wrapper {
    padding: 0 24px;
  }
  .add-schedule {
    font-size: 14px;
    font-weight: 350;
    color: #ed7b2f;
    line-height: 22px;
    cursor: pointer;
    margin-bottom: 8px;
    width: max-content;
  }

  .announcement {
    border-top-width: 1px;
    border-style: solid;
    @include border_color(--border-color-base);
  }
}
::v-deep {
  .el-scrollbar {
    .el-scrollbar__view {
      height: 100%;
    }
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .is-horizontal {
      display: none;
    }
  }
}
</style>
