// import {
//   contractTypeOptions,
//   payCycleOptions
// } from '../utils/status.js'
// import dayjs from 'dayjs'

export default {
  data() {
    return {
      formConfigureEnt: {
        labelWidth: '100px',
        descriptors: {
          calendarName: {
            form: 'input',
            label: '日程标题',
            attrs: {
              maxLength: 30
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入日程、会议、活动等标题' //请输入企业名称
              }
            ]
          },
          startEndTime: {
            form: 'datetime',
            label: '路演时间',
            hidden: false,
            rule: {
              required: true,
              message: '请选择时间',
              type: 'string'
            },
            props: {
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm',
              'value-format': 'yyyy-MM-dd HH:mm'
            }
          },
          showDate: {
            form: 'timeRange',
            label: '路演时间',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择路演时间'
              }
            ]
          },
          expectEnterDate: {
            form: 'date',
            label: '期望入住时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择期望入住时间'
              }
            ]
          },
          rentFreeEffective: {
            form: 'input',
            label: '内容描述',
            attrs: {
              maxLength: 300
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入内容描述'
              }
            ],
            props: {
              type: 'textarea'
            }
          }
        }
      }
    }
  }
}
