<template>
  <div class="preview-wrapper pos-relative">
    <div class="position-tag pos-absolute line-height-24">
      <el-tag type="primary">{{ informationData.noticeTypeStr }}</el-tag>
    </div>
    <div class="flex">
      <div class="preview-left p-t-24">
        <div
          class="font-size-16 color-black font-strong line-height-24 preview-title"
        >
          {{ informationData.title }}
        </div>

        <div
          class="m-b-16 m-t-10 font-size-12 flex align-items-center justify-content-center color-text-regular"
        >
          <div class="m-r-8">阅读数：{{ informationData.lookNum }}</div>
          <div>发布时间：{{ informationData.publishTime }}</div>
        </div>

        <div
          v-html="$options.filters.richTextFilter(informationData.content)"
          class="color-black font-size-14 line-height-22 overflow-hidden"
        ></div>
      </div>

      <div class="preview-right p-t-8 p-l-16">
        <div class="m-b-24">
          <div
            class="preview-right-title font-size-14 color-text-primary m-b-16"
          >
            附件下载
          </div>
          <div>
            <files-list
              v-if="informationAttach && informationAttach.length"
              :files="informationAttach"
              onlyForView
            />
            <empty-data v-else />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FilesList from '@/components/Uploader/files'
import { richTextFilter } from '@/filter'

export default {
  name: 'informationPreview',
  props: {
    informationData: {
      type: Object,
      default: null
    }
  },
  filters: { richTextFilter },
  components: {
    FilesList
  },
  data() {
    return {}
  },
  computed: {
    informationAttach() {
      return this.informationData.attachMap.informationAttach || []
    }
  }
}
</script>

<style lang="scss" scoped>
.preview-wrapper {
  .preview-left {
    flex: 0 0 640px;
    padding-right: 15px;
    box-sizing: border-box;
    border-right-width: 1px;
    border-style: solid;
    overflow: hidden;
    @include border_color(--border-color-base);
    .preview-title {
      text-align: center;
    }

    .primary-tag {
      border-radius: 12px;
    }
  }

  .preview-right {
    flex: 1;
  }
  .position-tag {
    left: 0;
    top: 0;
  }

  :deep(.el-tag--warning) {
    border-radius: 0 100px 100px 0;
  }
}
</style>
