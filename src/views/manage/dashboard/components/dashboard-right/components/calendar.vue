<template>
  <div class="calendar-container">
    <div class="calendarHeader">
      <span>{{ currentMonth }}</span>
      <span>
        <i class="el-icon-arrow-left cursor" @click="goPreviousMonth()"></i>
        <i class="el-icon-arrow-right cursor" @click="goNextMonth()"></i>
      </span>
    </div>
    <div class="calendarMain">
      <div class="calendarMainHeader">
        <span v-for="(item, index) in weekDay" :key="index" class="weekItem">
          {{ item.name }}
        </span>
      </div>
      <div class="calendarMainBody">
        <span
          v-for="(item, index) in dateList"
          :key="index"
          class="dataItem cursor"
          @click="handleClick(item, $event)"
        >
          <span
            class="day-item"
            :class="[
              { 'select-time': selectTimeStr === item.timeStr },
              { 'has-work': workList.includes(item.timeStr) },
              { hoverStyle: item.type === 'current' },
              { color666: item.type !== 'current' },
              { checkStyle: toDayTime === item.timeStr },
              { color666: disable },
              { color666: ableClick(item) }
            ]"
            >{{ item.value }}</span
          >
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { programmeMonthList } from '@/views/manage/dashboard/api'

export default {
  props: {
    time: {}, //(yyyy-mm) 当前显示月份（初始化一次 不做后期同步）
    selectTime: {}, // (yyyy-mm-dd) 当前选中的日期（单向数据流）
    disable: {
      //是否禁止选择日期 （用于起始 结束日期的禁止联动）
      type: Boolean,
      default: false
    },
    isdataCheck: {
      //是否禁止某个日期以前的时间不可选（与CheckData参数搭配使用）
      type: Boolean,
      default: false
    },
    CheckData: {
      //yyyy-mm-dd）禁止某个日期以前的时间不可选的日期 （isdataCheck为true时生效）
      type: String,
      default: ''
    }
  },
  data() {
    return {
      newTime: this.time,
      selectValue: this.selectTime,
      weekDay: [
        {
          name: '一',
          code: 'Monday'
        },
        {
          name: '二',
          code: 'Tuesday'
        },
        {
          name: '三',
          code: 'Wednesday'
        },
        {
          name: '四',
          code: 'Thursday'
        },
        {
          name: '五',
          code: 'Friday'
        },
        {
          name: '六',
          code: 'Saturday'
        },
        {
          name: '日',
          code: 'Sunday'
        }
      ],
      dateList: [],
      toDayTime: dayjs().format('YYYY-MM-DD'),
      selectTimeStr: dayjs().format('YYYY-MM-DD'),
      workList: []
    }
  },
  computed: {
    currentMonth() {
      console.log(this.newTime)
      return `${this.newTime.split('-')[0]}年  ${this.newTime.split('-')[1]}月`
    }
  },
  methods: {
    // 跟据月份获取日程
    programmeMonthList() {
      if (!this.newTime) return
      const list = this.newTime.split('-')
      if (!list || !list.length) return
      programmeMonthList({ dateMonth: `${list[0]}-${list[1]}` }).then(res => {
        const arr = res || []
        arr.forEach(item => {
          const itemDate = dayjs(item.dateResp).format('YYYY-MM-DD')
          this.workList.push(itemDate)
        })
      })
    },
    goPreviousYear() {
      let a = this.newTime.split('-')
      a[0] = a[0] - 1
      this.newTime = a.join('-')
      this.dataListNew(this.newTime, this.newTime)
      this.$emit('changeTime', this.newTime)
    },
    goNextYear() {
      let a = this.newTime.split('-')
      a[0] = a[0] * 1 + 1
      this.newTime = a.join('-')
      this.dataListNew(this.newTime, this.newTime)
      this.$emit('changeTime', this.newTime)
    },
    goPreviousMonth() {
      this.newTime = this.previousMonth(this.newTime)
      this.programmeMonthList()
      this.dataListNew(this.newTime, this.newTime)
      this.$emit('changeTime', this.newTime)
    },
    goNextMonth() {
      this.newTime = this.nextMonth(this.newTime)
      this.programmeMonthList()
      this.dataListNew(this.newTime, this.newTime)
      this.$emit('changeTime', this.newTime)
    },
    goNowTime() {
      this.newTime = this.getTime()
      this.dataListNew(this.newTime, this.newTime)
      this.$emit('changeTime', this.newTime, 'nowTime')
    },
    getTime() {
      let today = new Date()
      let year = today.getFullYear()
      let month = today.getMonth() + 1
      let days = today.getDate()
      let newTime = `${year}-${this.formateDate(month)}-${this.formateDate(
        days
      )}`
      return newTime
    },
    formateDate(str) {
      return str < 10 ? '0' + str : str
    },
    // 生成当月长度数组
    listNew(time, type) {
      let x = time.split('-')
      let a = new Date(x[0], x[1], 0).getDate()
      let b = Array.from(new Array(a).keys())
      let c = []
      b.forEach(item => {
        c.push({
          value: item + 1,
          type,
          year: x[0],
          month: x[1],
          day: (item + 1 + '').padStart(2, '0'),
          select: false,
          timeStr: `${x[0]}-${x[1]}-${(item + 1 + '').padStart(2, '0')}`
        })
      })
      return c
    },
    // 计算上个月日期
    previousMonth(time) {
      let a = time.split('-')
      if (a[1] === '01') {
        a[0] = a[0] - 1
        a[1] = '12'
      } else {
        a[1] = (a[1] - 1 + '').padStart(2, '0')
      }
      return a.join('-')
    },
    // 计算下个月日期
    nextMonth(time) {
      let a = time.split('-')
      if (a[1] === '12') {
        a[0] = a[0] * 1 + 1
        a[1] = '01'
      } else {
        a[1] = (a[1] * 1 + 1 + '').padStart(2, '0')
      }
      return a.join('-')
    },
    // 汇总数据 生成日期数组
    dataListNew(startTime, endTime) {
      this.dateList = []
      // 开始时间
      let a = startTime.split('-')
      // 结束时间
      let b = endTime.split('-')
      // 上个月的日期
      let c = this.previousMonth(startTime).split('-')
      let d = this.listNew(this.previousMonth(startTime), 'previous')
      c[2] = d[d.length - 1].value
      let J = new Date(c.join('-')).getDay()
      let k = []
      if (J !== 0) {
        k = d.splice(J * -1)
      }
      // 下个月的日期
      let e = this.nextMonth(endTime).split('-')
      let f = this.listNew(this.nextMonth(endTime), 'next')
      e[2] = f[0].value
      let L = new Date(e.join('-')).getDay()
      let G = []
      G = f.splice(0, 15 - L) // 往后多取一周
      // 拼接上个月的数据
      this.dateList = this.dateList.concat(k)
      // 拼接当前月的数据
      this.dateList = this.dateList.concat(this.listNew(a.join('-'), 'current'))
      // 拼接间隔月的数据
      while (a.join('-') !== b.join('-')) {
        if (a[1] === '12') {
          a[0] = a[0] * 1 + 1
          a[1] = '01'
        } else {
          a[1] = (a[1] * 1 + 1 + '').padStart(2, '0')
        }
        // 拼接下个月的数据
        this.dateList = this.dateList.concat(this.listNew(a.join('-')))
      }
      this.dateList = this.dateList.concat(G)
      this.dateList.length = 42 // 国定7*6
    },
    handleClick(item, e) {
      if (e.target.className.indexOf('color666') > 0) {
        return
      }
      this.selectTimeStr = item.timeStr
      this.$emit('update')
      // this.$emit("startDataHandle", `${item.year}-${item.month}-${item.day}`);
    },
    ableClick(item) {
      if (this.isdataCheck) {
        return (
          Date.parse(new Date(`${item.year}-${item.month}-${item.day}`)) <
          Date.parse(new Date(this.CheckData))
        )
      } else {
        return false
      }
    }
  },
  created() {
    this.dataListNew(this.newTime, this.newTime)
    this.programmeMonthList()
  },
  watch: {
    time(newValue) {
      this.selectValue = newValue
    }
    // disable (newValue) {
    //   //console.log(newValue);
    // },
  }
}
</script>

<style lang="scss" scoped="scoped">
.calendar-container {
  width: 100%;
  padding-bottom: 8px;
  .calendarHeader {
    line-height: 24px;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    .el-icon-arrow-left {
      margin-right: 24px;
    }
  }
}
.calendarMain {
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;
  margin-top: 8px;
}

.lineright {
  width: 5px;
  height: 5px;
  transform: rotate(45deg);
  display: inline-block;
  border-top: 1px solid #000;
  border-right: 1px solid #000;
}

.lineleft {
  width: 5px;
  height: 5px;
  transform: rotate(-135deg);
  display: inline-block;
  border-top: 1px solid #000;
  border-right: 1px solid #000;
}

.calendarMainHeader {
  display: flex;
  justify-content: space-between;
}

.weekItem {
  width: 44px;
  height: 40px;
  line-height: 40px;
  color: rgba(0, 0, 0, 0.6);
  text-align: center;
}

.calendarMainBody {
  display: grid;
  justify-content: space-between;
  grid-template-columns: repeat(7, 44px);
}

.dataItem {
  display: inline-block;
  width: 44px;
  height: 44px;
  line-height: 44px;
  text-align: center;
}

.color666 {
  color: rgba(0, 0, 0, 0.26) !important;
  cursor: not-allowed !important;
}

.cursor {
  cursor: pointer;
}

.has-work {
  color: #ed7b2f;
  position: relative;
  &:after {
    display: block;
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #0052d9;
    position: absolute;
    left: 18px;
    bottom: 2px;
  }
}
.day-item {
  display: inline-block;
  height: 100%;
  width: 100%;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.9);
  border: 1px solid transparent;
  box-sizing: border-box;
}
.hoverStyle:hover {
  background: #fff8f3;
  color: #ed7b2f;
}
.select-time {
  color: #ed7b2f;
  background: #fff;
  border: 1px solid #fff8f3;
}
.checkStyle {
  background-color: #fff8f3;
  color: #ed7b2f;
}
</style>
