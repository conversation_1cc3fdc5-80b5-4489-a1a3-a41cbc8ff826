// 发起 编辑 删除
import { parseTime } from '@/utils/tools'
import { getParkStatus } from '@/views/manage/business/qualificationHonor/utils/status'
export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'name',
          label: '研发中心名称',
          width: 220,
          render: (h, scope) => {
            return (
              <div
                onClick={() => {
                  this.toEnterDetail(scope.row)
                }}
              >
                <el-tooltip placement="top-start">
                  <div slot={'content'} class={'line-height-20'}>
                    {scope.row.name}
                  </div>
                  <div
                    class={'line-1 pointer'}
                    style={scope.row.entId ? 'color: #ED7B2F;' : ''}
                  >
                    {scope.row.name}
                  </div>
                </el-tooltip>
              </div>
            )
          }
        },
        {
          prop: 'relyingUnit',
          label: '依托单位',
          width: 220,
          search: {
            type: 'input'
          },
          render: (h, scope) => {
            return (
              <div>
                <el-tooltip placement="top-start">
                  <div slot={'content'} class={'line-height-20'}>
                    {scope.row.relyingUnit}
                  </div>
                  <div class={'line-1 pointer'}>{scope.row.relyingUnit}</div>
                </el-tooltip>
              </div>
            )
          }
        },
        {
          prop: 'parkState',
          label: '在园状态',
          hidden: true,
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'level',
          label: '级别',
          hidden: true,
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'parkId',
          label: '园区',
          hidden: true,
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'creditCode',
          label: '统一社会信用代码',
          width: 200
        },
        {
          prop: 'parkStateStr',
          label: '在园状态',
          render: (h, scope) => {
            return <div>{getParkStatus(h, scope.row.parkState)}</div>
          }
        },
        {
          prop: 'buildTime',
          label: '成立年份'
        },
        {
          prop: 'levelStr',
          label: '级别'
        },
        {
          prop: 'industry',
          label: '所属行业'
        },
        {
          prop: 'park',
          label: '所在园区'
        },
        {
          prop: 'updateTime',
          label: '更新时间',
          width: 180,
          render: (h, scope) => {
            return <div>{parseTime(scope.row.updateTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type={'text'}
                  onClick={() => {
                    this.editHandler(scope.row)
                  }}
                >
                  编辑
                </el-button>
                <el-button
                  type={'text'}
                  class={'color-danger'}
                  onClick={() => {
                    this.delHandler(scope.row)
                  }}
                >
                  删除
                </el-button>
              </div>
            )
          }
        }
      ]
    }
  }
}
