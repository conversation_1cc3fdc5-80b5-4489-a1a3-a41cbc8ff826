<template>
  <!--  房源导入  -->
  <div>
    <dialog-cmp
      title="研发中心导入"
      :visible.sync="dialogVisible"
      width="508px"
      @confirmDialog="confirmDialog"
    >
      <div class="m-b-8 font-size-14">上传文件</div>
      <!--          <el-button type="primary" size="small">上传文件</el-button>-->
      <div class="pos-relative">
        <driven-form
          class="m-t-4"
          ref="driven-form"
          :disabled="disabled"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
        <div class="download">
          <el-button
            className="m-t-4 m-l-8"
            size="small"
            style="width: 80px; height: 30px"
            @click="download"
            >下载模板</el-button
          >
        </div>
      </div>
      <div>导入须知</div>
      <div>
        <p class="line-height-20 font-size-12 m-t-8">
          为保证业务数据的精准性我们不得不提高对数据导入的要求和标准
        </p>
        <p class="line-height-20 font-size-12 m-t-8 color-info">
          1.请下载模板并准确填写数据后上传文件；
        </p>
        <p class="line-height-20 font-size-12 color-info">
          2.上传文件格式仅支持.xls/.xlsx文件；
        </p>
        <p class="line-height-20 font-size-12 color-info">
          3.单个上传文件大小最大不接超过100M；
        </p>
        <p class="line-height-20 font-size-12 color-info">
          4.每次上传文件数量最多为1个；
        </p>
        <p class="line-height-20 font-size-12 color-info">
          5.依托单位、统一社会信用代码数据具有唯一性、请确保数据输入正确；
        </p>
        <p class="line-height-20 font-size-12 color-info">
          6.数据重复会更新已有数据；
        </p>
        <p class="line-height-20 font-size-12 color-info">
          7.上传的文件有任一一条数据无法导入，则此次数据导入无效；
        </p>
      </div>
    </dialog-cmp>
    <dialog-cmp
      title=""
      :visible.sync="errorVisible"
      width="480px"
      @confirmDialog="errorConfirm"
    >
      <div slot="title">
        <span v-if="result" class="icon success-icon"
          ><svg-icon icon-class="check-circle-filled"
        /></span>
        <span v-else class="icon"
          ><svg-icon icon-class="info-circle-filled"
        /></span>
        <span>{{ result ? '导入成功' : '上传失败' }}</span>
      </div>
      <div class="error-wrapper">
        <div v-if="!result">
          文件数据异常，请修改以下数据后再次进行上传，如下：
        </div>
        <div v-for="item in errorList" :key="item.row">
          第{{ item.row }}行 {{ item.content }}
        </div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import {
  getImportTemplate,
  getParkUpload
} from '@/views/manage/business/qualificationHonor/api'
import downloads from '@/utils/download'
export default {
  name: 'UploadFiles',
  mixins: [descriptorMixins],
  data() {
    return {
      dialogVisible: false,
      disabled: false,
      fromModel: {},
      errorVisible: false,
      errorList: [], // 导入失败提示
      result: true // true成功 false失败
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.fromModel = {}
      }
    }
  },
  methods: {
    initData() {
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
    },
    errorConfirm() {
      this.errorVisible = false
    },
    download() {
      downloads.requestDownload(
        getImportTemplate(),
        'excel',
        '研发中心导入模板'
      )
    },
    confirmDialog() {
      try {
        this.$refs['driven-form'].validate(async valid => {
          if (!valid) return
          if (this.fromModel.file && this.fromModel.file.length) {
            const res = await getParkUpload({
              attachId: this.fromModel.file[0].id
            })
            this.result = res.result
            this.errorList = res.list || []
            if (res.result) {
              this.dialogVisible = false
              if (res.list && res.list.length) return (this.errorVisible = true)
              this.$toast.success(`文件已上传，正在处理中`)
            } else {
              this.errorVisible = true
            }
          }
        })
      } catch (e) {
        console.error(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content {
  margin-left: 0 !important;
}
.download {
  position: absolute;
  left: 88px;
  top: 2px;
}
.icon {
  @include font_color(--color-danger);
  margin-right: 9px;
  font-size: 18px;
  &.success-icon {
    @include font_color(--color-success);
  }
}
.error-wrapper {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 22px;
  padding-left: 30px;
}
</style>
