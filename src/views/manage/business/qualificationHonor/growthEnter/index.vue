<template>
  <div class="growth-enter-container">
    <drive-table
      ref="drive-table"
      :api-fn="getEnterprisePage"
      :columns="tableColumn"
      :extral-querys="extralQuerys"
    >
      <template v-slot:operate-left>
        <div class="font-size-14 line-height-20 font-strong">高成长企业</div>
      </template>
      <template v-slot:operate-right>
        <el-button type="primary" @click="openHandler">新增</el-button>
        <el-button type="primary" @click="leadingIn">企业导入</el-button>
      </template>
    </drive-table>
    <!--    企业导入-->
    <upload-files ref="upload" />
    <!--    抽屉-->
    <basic-drawer
      :title="title"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
  </div>
</template>

<script>
import ColumnMixins from './column'
import UploadFiles from '@/views/manage/business/qualificationHonor/growthEnter/uploadFiles'
import descriptorMixins from '@/views/manage/business/qualificationHonor/growthEnter/descriptor'
import {
  getEnterpriseCreate,
  getEnterpriseDelete,
  getEnterpriseDetail,
  getEnterpriseEffect,
  getEnterprisePage,
  getEnterpriseParks,
  getEnterpriseParkState,
  getEnterpriseUpdate
} from '@/views/manage/business/qualificationHonor/api'
import { parseTime } from '@/utils/tools'

export default {
  name: 'GrowthEnter',
  components: { UploadFiles },
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      parseTime,
      getEnterprisePage,
      drawerVisible: false,
      extralQuerys: {},
      fromModel: {},
      parkList: [],
      park: '',
      id: '',
      title: '新增高成长企业'
    }
  },
  created() {
    this.getEnterpriseEffect()
    this.getEnterpriseParks()
    this.getEnterpriseParkState()
  },
  methods: {
    // 获得在园状态
    async getEnterpriseParkState() {
      const res = await getEnterpriseParkState()
      this.tableColumn[1].search.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 获得园区
    async getEnterpriseParks() {
      const res = await getEnterpriseParks()
      this.parkList = res
      this.formConfigure.descriptors.parkId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.tableColumn[3].search.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 获得生效状态
    async getEnterpriseEffect() {
      const res = await getEnterpriseEffect()
      this.formConfigure.descriptors.status.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.tableColumn[2].search.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 添加
    openHandler() {
      this.id = ''
      this.title = '新增高成长企业'
      this.drawerVisible = true
      this.fromModel = {}
    },
    // 抽屉提交操作
    confirmDrawer() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          this.parkList.forEach(item => {
            if (item.key === this.fromModel.parkId) {
              this.park = item.label
            }
          })
          if (this.id) {
            await getEnterpriseUpdate({
              ...this.fromModel,
              park: this.park,
              id: this.id
            })
            this.$toast.success('更新成功')
          } else {
            await getEnterpriseCreate({ ...this.fromModel, park: this.park })
            this.$toast.success('添加成功')
          }
          this.id = ''
          this.$refs['drive-table'].refreshTable()
          this.drawerVisible = false
        }
      })
    },
    // 导入
    leadingIn() {
      this.$refs.upload.dialogVisible = true
      this.$refs.upload.initData()
    },
    // 编辑按钮
    async editHandler(row) {
      this.title = '编辑高成长企业'
      this.id = row.id
      this.drawerVisible = true
      const res = await getEnterpriseDetail({ id: row.id })
      let certTime = parseTime(res.certTime, '{y}-{m}-{d}')
      this.fromModel = { ...res, certTime }
    },
    // 删除
    delHandler(row) {
      this.$confirm('确定删除该条数据？').then(async () => {
        await getEnterpriseDelete({ id: row.id })
        this.$toast.success('删除成功')
        this.$refs['drive-table'].refreshTable()
      })
    },
    // 跳转企业详情
    toEnterDetail(row) {
      if (!row.entId) return
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: row.entId
        }
      })
    }
  }
}
</script>

<style scoped></style>
