<template>
  <div class="dynamic-result-container min-h100 bg-white flex">
    <div class="dynamic-result-wrapper h100">
      <div class="flex flex-center-between">
        <el-select
          style="width: 320px"
          v-model="selectId"
          placeholder="请选择"
          @change="selectChange"
          clearable
          :disabled="isLoading"
        >
          <el-option
            v-for="item in selectOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          >
          </el-option>
        </el-select>
        <div>
          <el-button type="primary" class="m-l-8" @click="goBackHandler">
            返回搜索
          </el-button>
          <el-button type="success" @click="exportReportHandle"
            >导出报表</el-button
          >
        </div>
      </div>
      <div class="current-wrapper m-t-24">
        <span class="m-r-4">数园云为你找到</span>
        <span class="color-primary">{{ totalCount }}</span>
        <span class="m-l-4">条相关结果</span>
      </div>
      <el-skeleton
        :rows="15"
        :loading="isSkeleton"
        style="height: calc(100% - 94px)"
      >
        <template v-if="list && list.length">
          <div
            class="list-wrapper flex pointer"
            v-for="(item, index) in list"
            :key="index"
            @click="detailHandle(item)"
          >
            <img v-if="item.logoUrl" :src="item.logoUrl" />
            <img v-else src="../images/default-avatar.png" />
            <div class="m-l-16">
              <div class="font-strong line-height-23 line-1">
                {{ item.name }}
              </div>
              <div
                class="font-size-14 line-height-22 m-t-8 flex align-items-center"
              >
                <span class="m-r-4 color-f">{{ item.build | noData }} /</span>
                <span class="m-r-4">{{ item.area | noData }}</span>
                <span class="m-r-4 color-f">m² /</span>
                <div class="color-f line-1 pointer" style="max-width: 200px">
                  {{ item.room | noData }}
                </div>
              </div>
              <p
                class="line-2 font-size-14 color-s line-height-22 m-t-8 pointer"
                v-html="item.description"
              ></p>
            </div>
            <div
              class="park-info color-white font-size-14 line-height-28"
              :class="item.enterStatus !== 1 ? 'park-leave' : ''"
            >
              {{ item.enterStatusStr }}
            </div>
            <div
              class="park-litter"
              :class="item.enterStatus !== 1 ? 'leave-litter' : ''"
            ></div>
          </div>
        </template>
        <empty-data v-else style="height: calc(100% - 94px)" />
      </el-skeleton>
    </div>
  </div>
</template>

<script>
import { local } from '@/utils/storage'
import {
  enterpriseSearchDetail,
  enterpriseSearchEntList,
  enterpriseSearchExportExcel,
  enterpriseSearchSelect
} from '../api'
import downloads from '@/utils/download'
import dayjs from 'dayjs'

export default {
  name: 'DynamicResult',
  data() {
    return {
      selectId: '',
      selectOptions: [],
      isParkVisible: true,
      params: {},
      totalCount: 0,
      list: [],
      isSkeleton: false,
      pageNo: 1,
      pageSize: 10,
      isLoading: false
    }
  },
  created() {
    this.enterpriseSearchSelect()
    this.initData()
  },
  mounted() {
    const appMain = document.querySelector('#appMain')
    appMain.addEventListener('scroll', this.scrollChange, true)
  },
  beforeDestroy() {
    const appMain = document.querySelector('#appMain')
    appMain.removeEventListener('scroll', this.scrollChange, true)
  },
  methods: {
    exportReportHandle() {
      const params = {
        id: this.selectId,
        search: this.params
      }
      downloads.requestDownloadPost(
        enterpriseSearchExportExcel(),
        params,
        'excel',
          dayjs().format('YYYY-MM-DD') + '企业导出.xls'
      )
    },
    initData() {
      this.params = local.GET_HIGH_SEARCH() || {}
      this.enterpriseSearchEntList()
    },
    detailHandle(row) {
      if (!row.entId) return
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: row.entId
        }
      })
    },
    // 监听滚动
    scrollChange(e) {
      const el = e.target
      if (
        Math.ceil(el.scrollTop + el.clientHeight) > el.scrollHeight ||
        Math.ceil(el.scrollTop + el.clientHeight) === el.scrollHeight
      ) {
        this.loadMoreHandle()
      }
    },
    // 获取搜索结果
    enterpriseSearchEntList(more = false) {
      try {
        this.isLoading = true
        if (!more) {
          this.pageNo = 1
          this.isSkeleton = true
        }
        const params = {
          ...this.params,
          pageNo: this.pageNo,
          pageSize: this.pageSize
        }
        enterpriseSearchEntList(params).then(res => {
          this.isLoading = false
          this.totalCount = res?.total || 0
          const list = res.list || []
          this.isSkeleton = false
          if (!more) return (this.list = list)
          this.list = [...this.list, ...list]
        })
      } catch (e) {
        this.isLoading = false
        console.log(e)
      }
    },
    // 触底加载
    loadMoreHandle() {
      if (this.totalCount === this.list.length) return
      this.pageNo++
      this.enterpriseSearchEntList(true)
    },
    // 选项change
    selectChange(e) {
      if (!e) return this.initData()
      enterpriseSearchDetail(e).then(res => {
        this.params = res?.search || {}
        this.enterpriseSearchEntList()
      })
    },
    // 获取下拉选项
    enterpriseSearchSelect() {
      enterpriseSearchSelect().then(res => {
        this.selectOptions = res || []
      })
    },
    goBackHandler() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.dynamic-result-container {
  padding: 24px;
  .color-s {
    color: rgba(0, 0, 0, 0.6);
  }
  .color-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .dynamic-result-wrapper {
    margin: 0 auto;
    width: 857px;
    height: auto;
    .current-wrapper {
      font-size: 14px;
      line-height: 22px;
      margin-bottom: 16px;
    }
    .list-wrapper {
      position: relative;
      width: calc(100% - 24px);
      margin-bottom: 16px;
      margin-left: 4px;
      padding: 27px 16px 27px 32px;
      @include background_color(--color-white);
      border-radius: 3px;
      opacity: 1;
      border: 1px solid #e9f0ff;
      img {
        width: 104px;
        height: 104px;
      }
      .park-info {
        position: absolute;
        left: -4px;
        top: 8px;
        width: 40px;
        height: 28px;
        text-align: center;
        @include background_color(--color-primary);
        border-radius: 0 3px 3px 0;
        opacity: 1;
      }
      .park-litter {
        position: absolute;
        left: -4px;
        top: 36px;
        border-top: 2px solid #ed7b2f;
        border-bottom: 2px solid transparent;
        border-left: 2px solid transparent;
        border-right: 2px solid #ed7b2f;
      }
      .park-leave {
        @include background_color(--color-info);
      }
      .leave-litter {
        border-top: 2px solid #4a4b4a;
        border-right: 2px solid #4a4b4a;
      }
    }
    .list-wrapper:last-child {
      margin-bottom: 2px;
    }
  }
}

::v-deep {
  .el-scrollbar__bar.is-vertical > div {
    width: 4px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
    opacity: 1;
  }
  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }
}
</style>
