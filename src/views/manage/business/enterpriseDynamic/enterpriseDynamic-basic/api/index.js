// 根据企业名获取企业列表
import request from '@/utils/request'

// 企业搜索
export function enterpriseSearch(params) {
  return request({
    url: '/enterprise/info/search',
    method: 'get',
    params
  })
}
// 获取新闻列表
export function getEnterpriseListNews(data) {
  return request({
    url: '/enterprise/info/list_news',
    method: 'post',
    data
  })
}

// 获取新闻详情
export function enterpriseNewsDetail(params) {
  return request({
    url: '/dc/transfer/qcc/company_new_detail/get',
    method: 'get',
    params
  })
}

// 获取最近访问企业
export function enterpriseSearchHit(params) {
  return request({
    url: '/enterprise/info/get_ent_hit',
    method: 'get',
    params
  })
}
// 获得所有园区
export function enterpriseSearchParks() {
  return request({
    url: '/enterprise/info/search/parks',
    method: 'get'
  })
}
// 获得园区分组
export function enterpriseSearchGroups() {
  return request({
    url: '/enterprise/info/search/groups',
    method: 'get'
  })
}
// 获得年份选择(从今年开始)
export function enterpriseSearchYearsNow() {
  return request({
    url: '/enterprise/info/search/years_now',
    method: 'get'
  })
}
// 获得租赁面积
export function enterpriseSearchAreas() {
  return request({
    url: '/enterprise/info/search/areas',
    method: 'get'
  })
}
// 获得在园时长
export function enterpriseSearchParksTime() {
  return request({
    url: '/enterprise/info/search/parks_time',
    method: 'get'
  })
}
// 获得入驻类型
export function enterpriseSearchEnterTypes() {
  return request({
    url: '/enterprise/info/search/enter_types',
    method: 'get'
  })
}
// 获得关联标签
export function enterpriseSearchLabels() {
  return request({
    url: '/enterprise/info/search/labels',
    method: 'get'
  })
}
// 获得行业类型
export function enterpriseSearchIndustry() {
  return request({
    url: '/enterprise/info/search/industry',
    method: 'get'
  })
}
// 获得参保人数
export function enterpriseSearchInsured() {
  return request({
    url: '/enterprise/info/search/insured',
    method: 'get'
  })
}
// 获得成立年限
export function enterpriseSearchBuildTime() {
  return request({
    url: '/enterprise/info/search/build_time',
    method: 'get'
  })
}
// 获得商标数量
export function enterpriseSearchTrade() {
  return request({
    url: '/enterprise/info/search/trade',
    method: 'get'
  })
}
// 获得软著数量
export function enterpriseSearchSoft() {
  return request({
    url: '/enterprise/info/search/soft',
    method: 'get'
  })
}
// 获得专利数量
export function enterpriseSearchPatent() {
  return request({
    url: '/enterprise/info/search/patent',
    method: 'get'
  })
}
// 获得年份选择(从去年开始)
export function enterpriseSearchYearsLast() {
  return request({
    url: '/enterprise/info/search/years_last',
    method: 'get'
  })
}
// 获得净利润
export function enterpriseSearchNetProfit() {
  return request({
    url: '/enterprise/info/search/net_profit',
    method: 'get'
  })
}
// 获得主营业务收入
export function enterpriseSearchMainBusiness() {
  return request({
    url: '/enterprise/info/search/main_business',
    method: 'get'
  })
}
// 获得纳税
export function enterpriseSearchTax() {
  return request({
    url: '/enterprise/info/search/tax',
    method: 'get'
  })
}
// 获得单位面积营收
export function enterpriseSearchPerArea() {
  return request({
    url: '/enterprise/info/search/per_area',
    method: 'get'
  })
}
// 获得单位面积税收
export function enterpriseSearchPerAreaTax() {
  return request({
    url: '/enterprise/info/search/per_area_tax',
    method: 'get'
  })
}
// 获得资质证书下拉
export function enterpriseSearchCertification() {
  return request({
    url: '/enterprise/info/search/certification',
    method: 'get'
  })
}
// 获得科技型企业下拉
export function enterpriseSearchTecSelect() {
  return request({
    url: '/enterprise/info/search/tec_select',
    method: 'get'
  })
}
// 获得统计维度
export function enterpriseSearchDimensions() {
  return request({
    url: '/enterprise/info/search/dimensions',
    method: 'get'
  })
}
// 点击获取数量
export function enterpriseSearchEntCount(data) {
  return request({
    url: '/enterprise/info/search/ent_count',
    method: 'post',
    data
  })
}
// 新增选择
export function enterpriseSearchAdd(data) {
  return request({
    url: '/enterprise/info/search/add',
    method: 'post',
    data
  })
}
// 获取选择
export function enterpriseSearchSelect() {
  return request({
    url: '/enterprise/info/search/select_search',
    method: 'get'
  })
}
// 获取跟据选项id获取条件
export function enterpriseSearchDetail(id) {
  return request({
    url: `/enterprise/info/search/detail/${id}`,
    method: 'get'
  })
}
// 查询搜索结果
export function enterpriseSearchEntList(data) {
  return request({
    url: '/enterprise/info/search/ent_list',
    method: 'post',
    data
  })
}
// 删除选项
export function enterpriseSearchDelete(id) {
  return request({
    url: `/enterprise/info/search/delete/${id}`,
    method: 'get'
  })
}
// 导出报表
export function enterpriseSearchExportExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/enterprise/info/search/export_excel`
}
