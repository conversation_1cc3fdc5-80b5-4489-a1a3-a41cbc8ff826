<template>
  <div class="right-container">
    <browse-enterprise />
    <!--    <common-search />-->
    <common-tool />
  </div>
</template>

<script>
import BrowseEnterprise from './browseEnterprise'
// import CommonSearch from './commonSearch'
import CommonTool from './commonTool'

export default {
  name: 'DynamicRight',
  components: {
    CommonTool,
    // CommonSearch,
    BrowseEnterprise
  }
}
</script>

<style scoped lang="scss">
.right-container {
  width: 270px;
  :deep(.title) {
    font-size: 12px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.9);
    line-height: 20px;
  }
}
</style>
