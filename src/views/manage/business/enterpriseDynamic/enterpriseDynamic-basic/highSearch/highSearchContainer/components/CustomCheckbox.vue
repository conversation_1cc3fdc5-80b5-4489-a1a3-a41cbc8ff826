<template>
  <div
    ref="customCheckboxContainer"
    class="custom-checkbox-container"
    @click.prevent="clickHandle"
  >
    <el-popover
      placement="bottom"
      width="230"
      trigger="click"
      v-model="checkedVisible"
    >
      <component
        :is="componentName"
        v-model="keyword"
        :unit="unit"
        :integer="integer"
        :minus="minus"
        v-bind="$attrs"
      />
      <div class="popover-btn">
        <el-button size="mini" @click="checkedVisible = false">取消</el-button>
        <el-button size="mini" type="primary" @click="confirmHandle"
          >确定</el-button
        >
      </div>
      <el-checkbox slot="reference" :label="customCheck.key">
        <span class="label-content">
          {{ customCheckLabel }}
        </span>
        <svg-icon
          class="arrowhead"
          :class="{ unfold: checkedVisible }"
          icon-class="caret-down-small"
        />
      </el-checkbox>
    </el-popover>
  </div>
</template>

<script>
import KeywordInput from './KeywordInput'
import RangeInput from './RangeInput'
import dayjs from 'dayjs'
export default {
  name: 'CustomCheckbox',
  props: {
    unit: {
      type: String,
      default: ''
    },
    integer: {
      type: Boolean,
      default: false
    },
    minus: {
      type: Boolean,
      default: false
    },
    customCheck: {
      type: Object,
      default: () => ({
        label: '自定义',
        key: ''
      })
    },
    valueFormat: {
      type: String,
      default: ''
    }
  },
  components: {
    KeywordInput,
    RangeInput
  },
  data() {
    return {
      keyword: '',
      checkedVisible: false,
      isChecked: false
    }
  },
  computed: {
    componentName() {
      return this.$attrs.type
    },
    customCheckLabel() {
      return this.customCheck.label || '自定义'
    }
  },
  methods: {
    clickHandle(e) {
      const hasCheckbox = this.$refs?.customCheckboxContainer?.contains(
        e.target
      )
      if (e.target.className === 'el-checkbox__inner' && hasCheckbox) {
        this.checkedVisible = false
        if (!this.isChecked) return (this.checkedVisible = true)
        this.$emit('onChange')
      }
    },
    confirmHandle() {
      const obj = {}
      const key = {
        min: '',
        max: ''
      }
      if (Object.prototype.toString.call(this.keyword) === '[object Object]') {
        const { min, max } = this.keyword
        if (min === '' && max === '') return (this.checkedVisible = false)
        if ((max < min && min && max) || (min === 0 && max === 0))
          return this.$toast.warning('请输入正确的范围')
        if (min && max) {
          if (this.valueFormat) {
            obj.label = `${dayjs(min).format(this.valueFormat)}${
              this.unit
            }-${dayjs(max).format(this.valueFormat)}${this.unit}`
            key.min = dayjs(min).format(this.valueFormat)
            key.max = dayjs(max).format(this.valueFormat)
          } else {
            obj.label = `${min}${this.unit}-${max}${this.unit}`
          }
        }
        if (!min) {
          if (this.valueFormat) {
            obj.label = `${dayjs(max).format(this.valueFormat)}${this.unit}以前`
            key.max = dayjs(max).format(this.valueFormat)
          } else {
            obj.label = `${max}${this.unit}以下`
          }
        }
        if (!max) {
          if (this.valueFormat) {
            obj.label = `${dayjs(min).format(this.valueFormat)}${this.unit}以后`
            key.min = dayjs(min).format(this.valueFormat)
          } else {
            obj.label = `${min}${this.unit}以上`
          }
        }
      } else {
        obj.label = this.keyword
      }
      obj.key = JSON.stringify(this.keyword)
      if (this.valueFormat) obj.key = JSON.stringify(key)
      this.checkedVisible = false
      this.isChecked = !!obj.label
      this.$emit('onConfirm', obj)
    }
  }
}
</script>

<style scoped lang="scss">
.custom-checkbox-container {
  display: inline-block;
}
.popover-btn {
  text-align: right;
  margin-top: 10px;
}
.label-content {
  display: inline-block;
}
.arrowhead {
  transition: all 300ms ease-in;
  &.unfold {
    transform: rotate(180deg);
  }
}
</style>
