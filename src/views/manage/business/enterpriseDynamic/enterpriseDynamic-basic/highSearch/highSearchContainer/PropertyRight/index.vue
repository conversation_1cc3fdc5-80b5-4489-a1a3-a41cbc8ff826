<template>
  <div>
    <module-title title="知识产权" />
    <custom-form-item
      type="CustomRadioGroup"
      label="统计维度"
      :formModel="formModel"
      :radioOptions="dimensionsList"
      prop="dimensions"
      radioProp="dimensions"
    />
    <custom-form-item
      label="商标数量"
      :options="tradeList"
      :formModel="formModel"
      prop="trademark"
      customType="checkbox"
      checkboxType="RangeInput"
      checkboxUnit="个"
      integer
    />
    <custom-form-item
      label="软著数量"
      :options="softList"
      :formModel="formModel"
      prop="soft"
      customType="checkbox"
      checkboxType="RangeInput"
      checkboxUnit="个"
      integer
    />
    <custom-form-item
      label="专利数量"
      :options="patentList"
      :formModel="formModel"
      prop="patent"
      customType="checkbox"
      checkboxType="RangeInput"
      checkboxUnit="个"
      integer
    />
  </div>
</template>

<script>
import ModuleTitle from '../components/ModuleTitle'
import CustomFormItem from '../components/CustomFormItem'
import {
  enterpriseSearchDimensions,
  enterpriseSearchPatent,
  enterpriseSearchSoft,
  enterpriseSearchTrade
} from '../../../api'

export default {
  name: 'PropertyRight',
  props: {
    formModel: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    ModuleTitle,
    CustomFormItem
  },
  data() {
    return {
      dimensionsList: [],
      tradeList: [],
      softList: [],
      patentList: []
    }
  },
  created() {
    this.initEnums()
  },
  methods: {
    initEnums() {
      enterpriseSearchTrade().then(res => {
        this.tradeList = res || []
      })
      enterpriseSearchSoft().then(res => {
        this.softList = res || []
      })
      enterpriseSearchPatent().then(res => {
        this.patentList = res || []
      })
      enterpriseSearchDimensions().then(res => {
        this.dimensionsList = res || []
      })
    }
  }
}
</script>

<style scoped></style>
