<template>
  <div ref="customDateContainer" class="custom-date-container" @click.prevent>
    <el-checkbox
      slot="reference"
      :label="customCheck.key"
      :key="customCheck.key"
    >
      <span class="label-content">{{ customCheckLabel }}</span>
      <svg-icon
        class="arrowhead"
        :class="{ unfold: checkedVisible }"
        icon-class="caret-down-small"
      />
    </el-checkbox>
    <el-date-picker
      class="date-picker"
      popper-class="date-picker-popper"
      v-model="datePick"
      :type="type"
      range-separator=""
      :value-format="valueFormat"
      ref="datePick"
      @change="datePickChange"
      :picker-options="pickerDisabled"
    >
    </el-date-picker>
  </div>
</template>

<script>
export default {
  name: 'CustomDatePicker',
  props: {
    type: {
      type: String,
      default: 'daterange'
    },
    valueFormat: {
      type: String,
      default: 'yyyy-MM-dd'
    },
    customCheck: {
      type: Object,
      default: () => ({
        label: '自定义',
        key: ''
      })
    }
  },
  data() {
    return {
      datePick: [],
      checkedVisible: false,
      isChecked: false,
      pickerDisabled: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        }
      }
    }
  },
  computed: {
    customCheckLabel() {
      return this.customCheck.label || '自定义'
    }
  },
  mounted() {
    document.addEventListener('click', e => {
      this.clickListener(e)
    })
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickListener)
  },
  methods: {
    datePickChange(e) {
      const obj = {
        label: e.join(' 至 '),
        key: JSON.stringify({
          min: e[0],
          max: e[1]
        })
      }
      this.isChecked = !!e.length
      this.$emit('onConfirm', obj)
    },
    datePickerShow() {
      this.$refs.datePick?.focus()
      this.checkedVisible = true
    },
    clickListener(e) {
      this.checkedVisible = false
      const hasCheckbox = this.$refs?.customDateContainer?.contains(e.target)
      if (e.target.className === 'el-checkbox__inner' && hasCheckbox) {
        if (!this.isChecked) return this.datePickerShow()
        this.checkedVisible = false
        this.$emit('onChange')
        return
      }
      if (hasCheckbox) this.datePickerShow()
    }
  }
}
</script>

<style scoped lang="scss">
.custom-date-container {
  display: inline-block;
}
.label-content {
  display: inline-block;
}
.arrowhead {
  transition: all 300ms ease-in;
  &.unfold {
    transform: rotate(180deg);
  }
}
.date-picker {
  width: 0;
  padding: 0;
  border: 0;
  z-index: -1;
}
</style>
<style lang="scss">
.date-picker-popper {
  margin-left: -68px;
  margin-top: 0 !important;
}
</style>
