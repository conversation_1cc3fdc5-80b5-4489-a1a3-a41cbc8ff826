<template>
  <el-row class="high-footer-container">
    <el-col :xs="24" :sm="24" :md="20" :lg="18" :xl="16">
      <div class="footer-body" v-if="selectList && selectList.length">
        <div class="title">已选条件</div>
        <div class="content">
          <div
            class="label-item"
            v-for="(item, index) in selectList"
            :key="index"
          >
            <span class="item-name" v-tooltip="formatterName(item)"
              >{{ item.label }}：{{ formatterName(item) }}</span
            >
            <svg-icon
              class="close-icon"
              icon-class="close"
              @click="deleteHandle(index, item)"
            />
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <div class="bottom-left">
          <div v-if="selectList && selectList.length">
            已为您找到<span class="color-primary p-l-4 p-r-4">{{
              totalCount
            }}</span
            >条相关结果
          </div>
        </div>
        <div class="bottom-right">
          <el-button
            type="info"
            @click="dialogVisible = true"
            :disabled="!selectList.length"
            >保存选项</el-button
          >
          <el-button
            type="info"
            @click="resetHandle"
            :disabled="!selectList.length"
            >重置选项</el-button
          >
          <el-button
            type="primary"
            @click="searchHandle"
            :disabled="!selectList.length"
            >查询</el-button
          >
        </div>
      </div>
    </el-col>
    <dialog-cmp
      title="保存选项"
      :visible.sync="dialogVisible"
      width="420px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="dialogVisible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      >
      </driven-form>
      <div>
        <div class="label-name">已存在的选项</div>
        <div class="label-container">
          <template v-if="selectOptions && selectOptions.length">
            <div
              class="label-item"
              v-for="(item, index) in selectOptions"
              :key="index"
            >
              <span>{{ item.label }}</span>
              <svg-icon
                class="close-icon"
                icon-class="close"
                @click="optionsDeleteHandle(item)"
              />
            </div>
          </template>
          <empty-data v-else />
        </div>
      </div>
    </dialog-cmp>
  </el-row>
</template>

<script>
import descriptorMixins from './descriptor'
import {
  enterpriseSearchAdd,
  enterpriseSearchDelete,
  enterpriseSearchEntCount,
  enterpriseSearchSelect
} from '../../api'
import { deepClone } from '@/utils/tools'
import { local } from '@/utils/storage'

export default {
  name: 'HighSearchFooter',
  data() {
    return {
      totalCount: 0,
      selectList: [],
      dialogVisible: false,
      fromModel: {},
      selectOptions: []
    }
  },
  mixins: [descriptorMixins],
  inject: ['HighSearch'],
  watch: {
    dialogVisible(val) {
      if (!val) this.fromModel = {}
    },
    selectList: {
      handler(val) {
        if (val && val.length) {
          this.enterpriseSearchEntCount()
        } else {
          this.totalCount = 0
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.enterpriseSearchSelect()
  },
  methods: {
    // 表单change事件
    changeHandle(row) {
      if (!row.children || !row.children.length) {
        this.selectList.forEach((item, index) => {
          if (item.prop === row.prop) {
            this.deleteHandle(index, item)
          }
        })
        return
      }
      const has = this.selectList.some(item => {
        return item.prop === row.prop
      })
      if (!has) {
        this.customFormatModel(row, true)
        this.selectList.push(row)
        return
      }
      this.selectList.forEach((item, index) => {
        if (item.prop === row.prop) {
          const hasKey = row.children.some(val => {
            return val.key !== '' && val.select !== false
          })
          if (!hasKey) return this.deleteHandle(index, item)
          this.customFormatModel(row, true)
          this.$set(this.selectList, index, row)
        }
      })
    },
    // dropdown取消事件
    changeDropdownHandle(prop) {
      this.selectList.forEach((item, index) => {
        if (item.prop === prop) {
          this.deleteHandle(index, item)
        }
      })
    },
    // 自定义最大值最小值数据赋值
    customFormatModel(item, isAdd = true) {
      this.$set(
        this.HighSearch.$refs.highSearchContainer.formModel,
        `${item.prop}Start`,
        ''
      )
      this.$set(
        this.HighSearch.$refs.highSearchContainer.formModel,
        `${item.prop}End`,
        ''
      )
      const select = item.children.find(
        val => val.custom && val.select === false
      )
      if (select) return
      const data = item.children.find(val => val.custom && val.key)
      if (data && isAdd) {
        const obj = JSON.parse(data.key)
        this.$set(
          this.HighSearch.$refs.highSearchContainer.formModel,
          `${item.prop}Start`,
          obj.min
        )
        this.$set(
          this.HighSearch.$refs.highSearchContainer.formModel,
          `${item.prop}End`,
          obj.max
        )
      }
    },
    // 删除
    deleteHandle(index, item) {
      this.customFormatModel(item, false)
      this.selectList.splice(index, 1)
      this.$set(
        this.HighSearch.$refs.highSearchContainer.formModel,
        item.prop,
        Array.isArray(
          this.HighSearch.$refs.highSearchContainer.formModel[item.prop]
        )
          ? []
          : ''
      )
      if (item.prop.includes('-')) {
        const [min, max] = item.prop.split('-')
        this.$set(this.HighSearch.$refs.highSearchContainer.formModel, min, '')
        this.$set(this.HighSearch.$refs.highSearchContainer.formModel, max, '')
      }
    },
    // 查询数量
    enterpriseSearchEntCount() {
      const params = this.formatterParams()
      enterpriseSearchEntCount(params).then(res => {
        this.totalCount = res || 0
      })
    },
    formatterName(item) {
      const arr = []
      item.children.forEach(row => {
        row.key !== '' && arr.push(row.label)
      })
      return `${item.label}：${arr.toString()}`
    },
    // 参数处理
    formatterParams() {
      const params = deepClone(
        this.HighSearch.$refs.highSearchContainer.formModel
      )
      for (const key in params) {
        if (params[key] === '') delete params[key]
        if (Array.isArray(params[key])) {
          params[key].forEach((item, index) => {
            if (
              typeof item === 'string' &&
              (item.includes('min') || item.includes('max'))
            ) {
              params[key].splice(index, 1)
            }
          })
        }
      }
      return params
    },
    optionsDeleteHandle(row) {
      enterpriseSearchDelete(row.key).then(() => {
        this.$toast.success('删除成功')
        this.enterpriseSearchSelect()
      })
    },
    // 获取下拉选项
    enterpriseSearchSelect() {
      enterpriseSearchSelect().then(res => {
        this.selectOptions = res || []
      })
    },
    // 保存选项
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return
        const params = {
          search: this.formatterParams(),
          ...this.fromModel
        }
        enterpriseSearchAdd(params).then(() => {
          this.$toast.success('操作成功')
          this.dialogVisible = false
          this.enterpriseSearchSelect()
        })
      })
    },
    // 查询
    searchHandle() {
      local.SET_HIGH_SEARCH(this.formatterParams())
      this.$router.push('/dynamicHigh/dynamicResult')
    },
    // 重置
    resetHandle() {
      this.$set(
        this.HighSearch.$refs.highSearchContainer,
        'formModel',
        this.HighSearch.$refs.highSearchContainer.$options.data().formModel
      )
      this.selectList = []
    }
  }
}
</script>

<style scoped lang="scss">
.high-footer-container {
  @include background_color(--color-white);
  width: calc(100% - 54px);
  display: flex;
  justify-content: center;
  position: fixed;
  bottom: 0;
  left: 56px;
  z-index: 9999;
  border: 1px solid;
  @include border_color(--border-color-light);
  .footer-body {
    padding: 24px 0;
    display: flex;
    .title {
      font-size: 14px;
      font-weight: 350;
      @include font_color_mix(--color-black, #fff, 10%);
      line-height: 22px;
      padding-right: 8px;
      flex-shrink: 0;
    }
    .content {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      max-height: 97px;
      overflow-y: auto;
      .label-item {
        width: max-content;
        max-width: 334px;
        border-radius: 3px;
        border: 1px solid;
        @include border_color(--color-primary);
        padding: 2px 8px;
        font-size: 12px;
        @include font_color(--color-primary);
        display: flex;
        justify-content: space-between;
        margin-right: 8px;
        margin-bottom: 8px;
        .item-name {
          width: 100%;
          line-height: 16px;
          cursor: pointer;
        }
        .close-icon {
          font-size: 16px;
          flex-shrink: 0;
          cursor: pointer;
        }
      }
    }
  }
  .footer-bottom {
    padding: 16px 0;
    border-top: 1px solid;
    @include border_color(--border-color-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    .bottom-left {
      font-size: 14px;
      font-weight: 350;
      @include font_color_mix(--color-black, #fff, 10%);
      line-height: 22px;
    }
  }
}
::v-deep {
  .label-name {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
  }
  .label-container {
    display: flex;
    flex-wrap: wrap;
    .label-item {
      background: #fef3e6;
      border-radius: 3px;
      font-size: 12px;
      font-weight: 400;
      color: #ed7b2f;
      padding: 2px 8px;
      display: flex;
      align-items: center;
      margin-top: 8px;
      margin-right: 4px;
      .close-icon {
        font-size: 14px;
        margin-left: 3px;
        cursor: pointer;
      }
    }
  }
}
</style>
