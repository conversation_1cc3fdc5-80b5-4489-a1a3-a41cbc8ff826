export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'boardTime',
          label: '年份'
        },
        {
          prop: 'amount',
          label: '主营业务收入(万元)'
        },
        {
          label: '操作',
          prop: 'operation',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="text"
                  onClick={() => {
                    this.editHandle(scope.row)
                  }}
                >
                  编辑
                </el-button>
                <el-button
                  type="text"
                  onClick={() => {
                    this.deleteHandle(scope.row)
                  }}
                >
                  删除
                </el-button>
              </div>
            )
          }
        }
      ]
    }
  }
}
