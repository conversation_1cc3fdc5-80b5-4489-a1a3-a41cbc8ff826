export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          file: {
            form: 'component',
            label: '',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传文件信息'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'maintain'
              },
              uploaderText: '上传文件',
              uploaderType: 'primary',
              accept:
                'application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
              showDelete: this.disabled,
              maxLength: 1,
              limit: 1,
              maxSize: 100
            }
          }
        }
      }
    }
  }
}
