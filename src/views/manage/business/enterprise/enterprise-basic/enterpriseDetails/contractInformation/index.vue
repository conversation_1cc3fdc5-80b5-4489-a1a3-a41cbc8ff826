<template>
  <!-- 合同信息 -->
  <div
    :id="'anchorPoint' + idxId"
    class="contract-information bg-white m-t-8 p-24 font-size-14"
  >
    <div class="line p-b-16 m-b-16 font-size-14">合同信息</div>
    <div class="flex justify-content-between">
      <div>
        <div class="m-b-14 font-size-14">基本信息</div>
        <div class="contract-table m-b-24">
          <table style="width: 100%" border="1" Cellspacing="0" Cellpadding="0">
            <tr align="left">
              <th>合同编号</th>
              <td>{{ data.contractNo | noData }}</td>
              <th>付款方式</th>
              <td>{{ getType(data.payCycle) | noData }}</td>
            </tr>
            <tr align="left">
              <th>合同起止时间</th>
              <td>{{ data.startTime | noData }}-{{ data.endTime | noData }}</td>
              <th>入驻时间(天)</th>
              <td>{{ data.days | noData }}</td>
            </tr>
            <tr align="left">
              <th>入驻方式</th>
              <td>
                {{
                  data.enterType === 1
                    ? '租赁入驻'
                    : data.enterType === 2
                    ? '购房入驻'
                    : data.enterType === 3
                    ? '工商入驻'
                    : '其他'
                }}
              </td>
              <th>房屋面积(m²)</th>
              <td>{{ data.actArea | noData }}</td>
            </tr>
            <tr align="left">
              <th>所在园区</th>
              <td>{{ data.parkName | noData }}</td>
              <th>楼栋房号</th>
              <td>{{ data.buildingRoomName | noData }}</td>
            </tr>
          </table>
        </div>
        <div class="m-b-14 font-size-14">常用缴费信息</div>
        <div class="contract-common m-b-24">
          <table
            style="width: 100%; height: 100px"
            border="1"
            Cellspacing="0"
            Cellpadding="0"
          >
            <tr align="left">
              <th>缴费账号</th>
              <td style="width: 386px">16916519845611561</td>
              <th>缴费户名</th>
              <td>合肥高新股份</td>
            </tr>
            <tr align="left">
              <th>开户行</th>
              <td :colspan="3">交通银行安徽自贸试验区合肥片区分行</td>
            </tr>
          </table>
        </div>
        <div class="m-b-14">
          <span class="m-r-14 font-size-14">缴费情况</span
          ><span class="color-warning">(总计：7次)</span>
        </div>
        <div class="contract-common m-b-24">
          <table
            style="width: 100%; height: 150px"
            border="1"
            Cellspacing="0"
            Cellpadding="0"
          >
            <tr align="left">
              <th>按时缴费</th>
              <td>1次</td>
              <th>提前缴费次数</th>
              <td>1次</td>
            </tr>
            <tr align="left">
              <th>逾期缴费次数</th>
              <td>1次</td>
              <th>最长提前天数</th>
              <td>5天</td>
            </tr>
            <tr align="left">
              <th>最长逾期天数</th>
              <td :colspan="3">1次</td>
            </tr>
          </table>
        </div>
      </div>
      <div class="line-bz" style="height: 560px; overflow: scroll">
        <div
          class="flex justify-content-start"
          v-for="(item, index) in dataList"
          :key="index"
          @click="tabsLine(index)"
        >
          <div>
            <div
              class="isDone"
              :class="current === index ? 'active-Done' : ''"
            ></div>
            <div class="isLine"></div>
          </div>
          <div class="pointer m-l-16">
            <div class="m-b-16 font-size-14">
              {{ item.startTime | noData }}-{{ item.endTime | noData }}
            </div>
            <img
              :class="current === index ? 'active-img' : ''"
              src="../../images/unknown.png"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSelectContractList } from '../api/details'
import { getType } from './utils/utils'
export default {
  name: 'ContractInformation',
  data() {
    return {
      getType,
      dataList: [
        { name: '18年合同' },
        { name: '18年合同' },
        { name: '18年合同' },
        { name: '18年合同' },
        { name: '18年合同' }
      ],
      idxId: 0,
      entId: this.$route.query.id,
      data: {},
      current: 0
    }
  },
  created() {
    this.initData(0)
  },
  methods: {
    tabsLine(e) {
      this.current = e
      this.initData(e)
    },
    async initData(idx) {
      const res = await getSelectContractList({ entId: this.entId })
      this.dataList = res
      res.forEach((item, index) => {
        if (index === idx) {
          this.data = item
        }
      })
    },
    anchorPointFun() {
      let dom = document.getElementById(`anchorPoint${this.idxId}`)
      this.goDomFun(dom)
    },
    goDomFun(dom) {
      dom.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })
      dom.classList.add('anchorAnimation')
      setTimeout(() => {
        dom.classList.remove('anchorAnimation')
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.contract-information {
  height: 710px;
  .line {
    border-bottom: 1px solid #ebedf1;
  }
  .active-img {
    border: 1px solid #ed7b2f;
  }
  .active-Done {
    @include background_color(--color-primary);
  }
  .isDone {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dcdcdc;
  }
  .isLine {
    width: 2px;
    height: 130px;
    margin-left: 3px;
    border-radius: 3px;
    background-color: #dcdcdc;
  }
  .contract-table {
    width: 1200px;
    height: 200px;
  }
  .contract-common {
    width: 1200px;
    height: 100px;
  }
  table {
    height: 200px;
    border: 1px solid #e7e7e7;
    font-size: 14px;
  }
  th {
    width: 200px;
    @include font_color(--color-info);
    background-color: #f0f2f5;
    padding-left: 24px;
  }
  td {
    width: 400px;
    padding-left: 24px;
    padding-right: 24px;
    line-height: 1.5em;
  }
  .line-bz {
    width: 240px;
    padding-left: 24px;
    margin-top: 30px;
    margin-right: 80px;
  }
  ::-webkit-scrollbar {
    width: 2px;
    height: 4px;
  }
}
.anchorAnimation {
  animation-name: anchorKeyframes;
  animation-direction: 2.5s;
  animation-fill-mode: both;
}
@keyframes anchorKeyframes {
  0% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  25% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  50% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  75% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  100% {
    background-color: rgba(93, 174, 255, 0.1);
  }
}
//::-webkit-scrollbar-thumb {
//  background-color: rgba(255, 255, 255, 0.9);
//}
</style>
