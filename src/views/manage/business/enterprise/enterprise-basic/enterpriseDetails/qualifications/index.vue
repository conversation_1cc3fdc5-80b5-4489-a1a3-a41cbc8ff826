<template>
  <!-- 荣誉资质 -->
  <div
    :id="'anchorPoint' + idxId"
    class="qualifications bg-white m-t-8 p-24 font-size-14"
  >
    <div class="m-b-16">荣誉资质</div>
    <div class="qualifications-table">
      <drive-table
        ref="drive-table"
        :height="164"
        :columns="tableColumn"
        :table-data="list"
      />
    </div>
  </div>
</template>

<script>
import ColumnMixins from './column/column'
import { getSelectQualificationHonor } from '../api/details'
export default {
  name: 'Qualifications',
  mixins: [ColumnMixins],
  data() {
    return {
      list: [],
      idxId: 0,
      entId: this.$route.query.id
    }
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      let res = await getSelectQualificationHonor({
        id: this.entId
      })
      this.list = res
    },
    anchorPointFun() {
      let dom = document.getElementById(`anchorPoint${this.idxId}`)
      this.goDomFun(dom)
    },
    goDomFun(dom) {
      dom.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      })
      dom.classList.add('anchorAnimation')
      setTimeout(() => {
        dom.classList.remove('anchorAnimation')
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.qualifications {
  border-radius: 3px;
  opacity: 1;
}
.anchorAnimation {
  animation-name: anchorKeyframes;
  animation-direction: 2.5s;
  animation-fill-mode: both;
}
@keyframes anchorKeyframes {
  0% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  25% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  50% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  75% {
    background-color: rgba(93, 174, 255, 0.1);
  }
  100% {
    background-color: rgba(93, 174, 255, 0.1);
  }
}
</style>
