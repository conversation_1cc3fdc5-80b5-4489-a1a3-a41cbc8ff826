<template>
  <!-- 联系人 -->
  <div class="flex justify-content-between m-t-8">
    <div class="gs-card" style="flex: 1">
      <div class="garden-wrapper garden-wrapper-custom">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary title">
                常用联系人
              </div>
              <div class="font-size-14 text-black info">
                <span class="info-text">{{ data.contact | noData }}</span>
                <span class="info-img">
                  <svg-icon icon-class="phone-fill" class="color-primary" />
                </span>
                <span class="info-lx">{{ data.phone | noData }}</span>
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary title">
                总经理或项目负责人
              </div>
              <div class="font-size-14 text-black info">
                <span class="info-text">{{ data.manager | noData }}</span>
                <span class="info-img">
                  <svg-icon icon-class="phone-fill" class="color-primary" />
                </span>
                <span class="info-lx">{{ data.managerPhone | noData }}</span>
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary title">
                法定代表人
              </div>
              <div class="font-size-14 text-black info">
                <span class="info-text">{{ data.legalPerson | noData }}</span>
                <span class="info-img">
                  <svg-icon icon-class="phone-fill" class="color-primary" />
                </span>
                <span class="info-lx">{{
                  data.legalPersonPhone | noData
                }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
    <!--    ss-->
    <div class="info-card m-l-8">
      <div class="garden-wrapper garden-wrapper-custom">
        <el-row :gutter="20">
          <el-col :span="12">
            <div>
              <div class="basic-info">基本信息</div>
              <div class="m-b-8">
                <span class="basic-label">工商注册日期</span>
                <span class="basic-prop">{{ data.registerDate | noData }}</span>
              </div>
              <div>
                <span class="basic-label">经营场所面积</span>
                <span class="basic-prop">{{ data.area | noData }}平方米</span>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="employees">
              <div class="m-b-8">
                <span class="basic-label">企业员工数</span>
                <span class="basic-prop">{{ data.staffCount | noData }}人</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
export default {
  name: 'LeaveParkApply',
  data() {
    return {
      NumFormat,
      entId: this.$route.query.id,
      data: {}
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.gs-card {
  border-radius: 3px;
  height: 136px;
  background-color: #fff !important;
  padding: 39px 24px;

  .garden-wrapper {
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-base);

    .label {
      width: 76.01px;
      text-align-last: right;
      margin-right: 16px;
    }

    .title {
      font-size: 16px;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 2px;
      margin-bottom: 22px;
      color: rgba(0, 0, 0, 0.9) !important;
    }

    .info {
      .info-text {
        height: 14px;
        font-size: 14px;
        font-weight: 400;
        color: #666;
        line-height: 14px;
      }
      .info-lx {
        height: 14px;
        font-size: 14px;
        font-weight: 400;
        color: #ed7b2f;
        line-height: 14px;
      }
      .info-img {
        width: 20px;
        height: 20px;
        margin-left: 16px;
        margin-right: 4px;
        vertical-align: top;
      }
    }

    &.no-border {
      border: none;
    }

    &.healthy-wrapper {
      padding-top: 24px;
      padding-bottom: 0;
    }

    .member-table {
      padding: 0 6px;
    }

    .enterprise-report {
      width: 176px;
      text-align: right;
    }
  }

  .garden-wrapper-custom {
    border-bottom: none;
  }
}

.info-card {
  width: 552px;
  border-radius: 3px;
  height: 136px;
  background-color: #fff !important;
  padding: 24px 24px;

  .basic-info {
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    margin-bottom: 16px;
  }
  .basic-label {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
    line-height: 22px;
    margin-right: 8px;
  }
  .basic-prop {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
  }
  .employees {
    margin-top: 37px;
  }
  .lab {
    width: 70px;
    text-align: right;
    display: inline-block;
  }
  .lab-prop {
    color: #ed7b2f;
    width: 99px;
  }
}
</style>
