<template>
  <!-- 企业评分 -->
  <div
    class="radar-echart bg-white m-l-8 pos-relative flex justify-content-between"
  >
    <!--     <div>企业评分</div>-->
    <div ref="myDiv" class="radar" />
    <span class="line"></span>
    <div class="radar-text font-size-18">
      <div class="m-b-8 color-warning" style="font-weight: bold">61.3</div>
      <div class="font-size-12 color-info m-b-16">企业评分</div>
      <div class="m-b-8 color-primary" style="font-weight: bold">C</div>
      <div class="font-size-12 color-info m-b-16">企业评级</div>
      <div class="m-b-8 color-success" style="font-weight: bold">9/210</div>
      <div class="font-size-12 color-info m-b-16">企业排名</div>
    </div>
  </div>
</template>
<script>
// 回顾以前如何使用echarts
// 1. 在html的script的src里面引入echarts.js
// 2. 挂载的容器，并且给id
// 3. 实例化一个echarts实例 let echart = echarts.init(document.getElementById(id))
// 4. 绘制 （给配置  echart.setOption(配置)  ）
import * as Echarts from 'echarts' // 从echarts的5版本开始，没有默认导出  (导入所有的echarts配置， 按需的导入配置)
// 导入基础配置
import RadarData from './components/radar-data'
export default {
  name: 'Radar',
  data() {
    return {}
  },
  mounted() {
    // 实例化一个echarts实例对象
    this.echarts = Echarts.init(this.$refs.myDiv)
    // 绘制的是基础结构，没有核心数据
    this.echarts.setOption(RadarData)
    // 调用
    this.initData()
  },
  methods: {
    initData() {
      // 发生ajax请求获取数据
      // setTimeout(() => {
      // resData 是服务器端返回的数据
      let resData = [
        {
          value: [37, 60.3, 94.1, 78.5, 42.1],
          name: '安徽中安创谷科技园有限公司'
        }
      ]
      // 重回绘制(重新调用setOption)
      RadarData.series[0].data = resData
      this.echarts.setOption(RadarData)
      // }, 3000)
    }
  }
}
</script>
<style lang="scss" scoped>
.radar-echart {
  width: 552px;
  padding: 24px;
}
.radar {
  width: 300px;
  height: 200px;
}
.line {
  // position: absolute;
  // right: 209px;
  // top: 114px;
  width: 1px;
  height: 104px;
  display: block;
  margin-top: 80px;
  background-color: #e9f0ff;
}
.radar-text {
  // position: absolute;
  // right: 100px;
  // top: 88px;
  margin: 68px 80px 0;
  text-align: center;
}
</style>
