import dayjs from 'dayjs'
export default {
  data() {
    return {
      tableColumn: [
        {
          label: '软件全名',
          prop: 'name',
          render: (h, scope) => {
            return <div class="color-primary">{scope.row.name}</div>
          }
        },
        {
          label: '版本号',
          prop: 'versionNo'
        },
        {
          label: '登记号',
          prop: 'registerNo'
        },
        {
          label: '著作权登记批准日期',
          prop: 'registerAperDate',
          render: (h, scope) => {
            return (
              <div>
                {dayjs(scope.row.registerAperDate).format('YYYY-MM-DD')}
              </div>
            )
          }
        }
      ],
      tableColumn1: [
        {
          label: '专利名称',
          prop: 'title',
          render: (h, scope) => {
            return <div class="color-primary">{scope.row.title}</div>
          }
        },
        {
          label: '公开（公告）号',
          prop: 'publicationNumber'
        },
        {
          label: '公开日',
          prop: 'publicationDate',
          render: (h, scope) => {
            return (
              <div>{dayjs(scope.row.publicationDate).format('YYYY-MM-DD')}</div>
            )
          }
        },
        {
          label: '类型',
          prop: 'kindCode'
        },
        {
          label: '法律状态',
          prop: 'legalStatusDesc'
        }
      ]
    }
  }
}
