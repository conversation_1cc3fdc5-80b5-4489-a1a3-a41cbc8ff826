<template>
  <!-- 主营项目 -->
  <div class="xm-card m-t-8">
    <div
      class="garden-wrapper garden-wrapper-custom flex justify-content-between"
    >
      <div style="width: 261px">
        <div class="m-b-16 font-size-14">项目概况</div>
        <div class="main p-r-24">
          <div class="font-size-15 label">{{ data.technical | noData }}</div>
          <div class="font-size-15 label m-b-14 label-color">所属技术领域</div>
          <div class="font-size-15 label">{{ data.projectType | noData }}</div>
          <div class="font-size-15 label label-color">项目类型</div>
        </div>
      </div>

      <div class="m-l-24" style="flex: 1">
        <div class="p-l-4">
          <div class="m-b-16 font-size-14">项目描述</div>
          <div class="main-d">
            {{ data.introduction | noData }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeaveParkApply',
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      data: {}
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
::v-deep .el-col-4 {
  width: 14.66667%;
}
.xm-card {
  padding: 24px;
  border-radius: 3px;
  background-color: #fff;
  .garden-wrapper {
    .main {
      border-right-width: 1px;
      border-style: solid;
      line-height: 24px;
      border-color: #e9f0ff !important;
    }

    .label-color {
      color: rgba(0, 0, 0, 0.4);
    }

    .main-r {
      width: 210px;
      height: 22px;
      font-size: 15px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.4);
      line-height: 22px;
    }
    .main-d {
      font-size: 15px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
    }
  }

  .garden-wrapper-custom {
    .label {
      text-align: center;
      width: 100%;
    }
  }
}
</style>
