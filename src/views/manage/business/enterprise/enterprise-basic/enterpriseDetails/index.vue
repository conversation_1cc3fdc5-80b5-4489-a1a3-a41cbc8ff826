<template>
  <div class="main-wrapper min-h100">
    <div class="flex justify-content-between">
      <!-- 企业信息 -->
      <detail-information ref="information" @contact="initData" />
      <!-- 企业评分 -->
      <!--      <detail-score ref="score" />-->
    </div>
    <!-- 联系人 -->
    <contacts ref="contacts" />
    <!-- 主营项目 -->
    <project-overview ref="overview" />
    <!-- 企业动态 -->
    <enterprise-dynamics ref="dynamics" />
    <!-- tab切换 -->
    <enterprise-tab ref="tab" @tabsChange="tabsChange" />
    <!-- 工商信息 -->
    <business-information ref="business" />
    <!-- 股权信息 -->
    <stock-right ref="stock" />
    <!-- 合同信息 -->
    <contract-information ref="contract" />
    <!-- 荣誉资质 -->
    <qualifications ref="qualifications" />
    <!-- 知识产权 -->
    <property-right ref="right" />
  </div>
</template>

<script>
import DetailInformation from './detailInformation'
// import DetailScore from './detailScore'
import EnterpriseTab from './enterpriseTab'
import BusinessInformation from './businessInformation'
import StockRight from './stockRight'
import ContractInformation from './contractInformation'
import Qualifications from './qualifications'
import PropertyRight from './propertyRight'
import Contacts from './contacts'
import ProjectOverview from './projectOverview'
import EnterpriseDynamics from './enterpriseDynamics'
import { getSelectBasicDetail } from './api/details'
export default {
  name: 'enterpriseDetails',
  components: {
    DetailInformation,
    // DetailScore,
    EnterpriseTab,
    BusinessInformation,
    StockRight,
    ContractInformation,
    Qualifications,
    PropertyRight,
    Contacts,
    ProjectOverview,
    EnterpriseDynamics
  },
  data() {
    return {
      entId: this.$route.query.id,
      data: {}
    }
  },
  created() {
    this.initData()
  },
  methods: {
    tabsChange(val) {
      if (val === 0) {
        this.$refs.business.idxId = val
        this.$nextTick(() => {
          this.$refs.business.anchorPointFun()
        })
      } else if (val === 1) {
        this.$refs.stock.idxId = val
        this.$nextTick(() => {
          this.$refs.stock.anchorPointFun()
        })
      } else if (val === 2) {
        this.$refs.contract.idxId = val
        this.$nextTick(() => {
          this.$refs.contract.anchorPointFun()
        })
      } else if (val === 3) {
        this.$refs.qualifications.idxId = val
        this.$nextTick(() => {
          this.$refs.qualifications.anchorPointFun()
        })
      } else if (val === 4) {
        this.$refs.right.idxId = val
        this.$nextTick(() => {
          this.$refs.right.anchorPointFun()
        })
      }
    },
    async initData() {
      let res = await getSelectBasicDetail({ id: this.entId })
      this.$refs.contacts.data = res
      this.$refs.overview.data = res
    }
  }
}
</script>

<style lang="scss" scoped>
.main-wrapper {
  width: 1200px;
  margin: 0 auto;
}
</style>
