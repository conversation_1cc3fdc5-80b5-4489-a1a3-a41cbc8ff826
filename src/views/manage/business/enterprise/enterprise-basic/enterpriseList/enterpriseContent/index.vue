<template>
  <div
    class="subject-wrapper bg-white flex justify-content-between overflow-hidden"
    style="height: calc(100% - 73px)"
  >
    <el-scrollbar class="scrollbar">
      <div class="subject-lt bg-white">
        <div class="m-b-25">
          <el-form ref="form" :model="form" :rules="rules">
            <el-form-item label="入园时间" label-width="68px">
              <div class="ipt">
                <el-date-picker
                  style="width: 206px"
                  v-model="form.admission"
                  type="daterange"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  @change="getTime"
                  validate-event
                >
                </el-date-picker>
                <svg-icon class="color-info" icon-class="calendar" />
              </div>
            </el-form-item>
            <div class="m-b-16"></div>
            <!--            <el-form-item label="租赁面积" label-width="68px" prop="minArea">-->
            <!--              <div class="ipt">-->
            <!--                <el-input-->
            <!--                  class="elIpt p-l-4"-->
            <!--                  v-model="form.minArea"-->
            <!--                  style="width: 88px"-->
            <!--                  placeholder="最小面积"-->
            <!--                  @input.native="handleQuery"-->
            <!--                ></el-input>-->
            <!--                - -->
            <!--                <el-input-->
            <!--                  class="elIpt"-->
            <!--                  v-model="form.maxArea"-->
            <!--                  style="width: 103px"-->
            <!--                  placeholder="最大面积"-->
            <!--                  @input.native="handleQuery"-->
            <!--                >-->
            <!--                </el-input>-->
            <!--                <svg-icon class="color-info" icon-class="layers" />-->
            <!--              </div>-->
            <!--            </el-form-item>-->
            <div class="flex align-items-center">
              <div class="font-size-14 m-r-12">租赁面积</div>
              <div class="ipt flex align-items-center">
                <el-form-item prop="minArea">
                  <el-input
                    class="elIpt p-l-4"
                    v-model="form.minArea"
                    style="width: 88px"
                    placeholder="最小面积"
                    @input.native="handleQuery"
                  ></el-input>
                </el-form-item>
                -
                <el-form-item prop="maxArea">
                  <el-input
                    class="elIpt"
                    v-model="form.maxArea"
                    style="width: 103px"
                    placeholder="最大面积"
                    @input.native="handleQuery"
                  >
                  </el-input>
                </el-form-item>
                <svg-icon
                  class="color-info font-size-14 m-l-8"
                  icon-class="layers"
                />
              </div>
            </div>
            <!--            <el-form-item label="企业评分" label-width="68px">-->
            <!--              <el-slider-->
            <!--                style="width: 225px"-->
            <!--                v-model="form.score"-->
            <!--                :marks="marks"-->
            <!--                @change="changeScore"-->
            <!--              ></el-slider>-->
            <!--            </el-form-item>-->
          </el-form>
        </div>
        <div>
          <div class="flex justify-content-between font-size-14">
            <div>入驻类型</div>
            <div class="m-r-16 pointer" @click="visible = !visible">
              <svg-icon v-if="visible" icon-class="chevron-up" />
              <svg-icon v-else icon-class="chevron-down" />
            </div>
          </div>
          <div
            class="subject-type m-t-8 p-14 font-size-14"
            v-if="visible"
            style="overflow: scroll"
          >
            <div
              v-for="(item, index) in defaultProps"
              :key="item.value"
              class="flex justify-content-between tabs-item font-size-14 p-l-24 p-r-13"
              :class="index === current ? 'active' : ''"
              @click="tabsChange(index, item.value)"
            >
              <div>{{ item.name | noData }}</div>
              <div class="font-size-12 color-info">
                {{ item.count | noData }}
              </div>
            </div>
          </div>
        </div>
        <div class="m-t-24">
          <div class="flex justify-content-between font-size-14">
            <div>技术领域</div>
            <div class="m-r-16 pointer" @click="visible1 = !visible1">
              <svg-icon v-if="visible1" icon-class="chevron-up" />
              <svg-icon v-else icon-class="chevron-down" />
            </div>
          </div>
          <div
            class="subject-tree m-t-8 p-14"
            v-if="visible1"
            style="overflow: scroll"
          >
            <el-form ref="formData" :model="formData1">
              <el-checkbox-group
                class="technology"
                v-model="formData1.type"
                v-for="(item, index) in technicals"
                :key="index"
              >
                <el-checkbox :label="item.value" name="type" @change="changeFn">
                  <template>
                    <div
                      style="width: 240px"
                      class="flex justify-content-between"
                    >
                      <div>{{ item.name | noData }}</div>
                      <div class="font-size-12 color-info">
                        {{ item.count | noData }}
                      </div>
                    </div>
                  </template>
                </el-checkbox>
              </el-checkbox-group>
            </el-form>
          </div>
        </div>
        <div class="m-t-24">
          <div class="flex justify-content-between font-size-14">
            <div>企业标签</div>
            <div class="m-r-16 pointer" @click="visible2 = !visible2">
              <svg-icon v-if="visible2" icon-class="chevron-up" />
              <svg-icon v-else icon-class="chevron-down" />
            </div>
          </div>
          <div
            class="subject-tree m-t-8 p-14"
            v-if="visible2"
            style="overflow: scroll"
          >
            <el-form ref="formData" :model="formData">
              <el-checkbox-group
                class="enterprise"
                v-model="formData.type"
                v-for="(item, index) in labelIds"
                :key="index"
              >
                <el-checkbox
                  :label="item.value"
                  name="type"
                  @change="changeFun"
                >
                  <template>
                    <div
                      style="width: 240px"
                      class="flex justify-content-between"
                    >
                      <div>{{ item.name | noData }}</div>
                      <div class="font-size-12 color-info">
                        {{ item.count | noData }}
                      </div>
                    </div>
                  </template>
                </el-checkbox>
              </el-checkbox-group>
            </el-form>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <el-scrollbar class="scrollbar" style="flex: 1" ref="box">
      <div v-if="total === 0" style="margin-top: 200px">
        <empty-data />
      </div>
      <div v-else class="subject-rt bg-white">
        <div
          class="subject-content flex justify-content-between m-b-24"
          v-for="(item, index) in newList"
          :key="index"
        >
          <div
            class="zy color-white font-size-12"
            :class="item.enterStatus === 1 ? 'zy' : 'zy1'"
          >
            {{ getParkStatus(item.enterStatus) }}
          </div>
          <div :class="item.enterStatus === 1 ? 'sj' : 'sj1'"></div>
          <div class="flex justify-content-start" style="flex: 1">
            <div
              class="logo m-t-16 m-l-8"
              :class="{ leave: item.enterStatus === 2 }"
            >
              <img v-if="item.logoUrl" :src="item.logoUrl" alt="" />
              <img v-else src="../../images/default-avatar.png" alt="" />
            </div>
            <div class="m-t-18 m-l-16 font-size-14" style="flex: 1">
              <div
                class="flex justify-content-between align-items-center m-r-16 m-b-8"
              >
                <div
                  class="text-yc font-size-16 pointer"
                  style="width: 300px"
                  @click="addDetails(item.id)"
                >
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.enterpriseName"
                    placement="bottom"
                  >
                    <span class="address">{{
                      item.enterpriseName | noData
                    }}</span>
                  </el-tooltip>
                </div>
                <div class="flex align-items-center">
                  <div class="m-r-4 flex align-items-center">
                    <span
                      class="m-r-4 font-size-20 pointer"
                      v-if="item.relationCount !== 0"
                      @click="association(item.id)"
                    >
                      <el-tooltip
                        effect="dark"
                        content="关联企业"
                        placement="top"
                      >
                        <svg-icon class="color-primary" icon-class="link-m" />
                      </el-tooltip>
                    </span>
                    <span class="m-r-16" v-if="item.relationCount !== 0">{{
                      item.relationCount
                    }}</span>
                  </div>
                  <div class="m-r-4 flex align-items-center">
                    <span
                      class="m-r-4 font-size-20 pointer"
                      @click="enterpriseRemarks(item.id)"
                      v-if="item.remarksCount !== 0"
                    >
                      <el-tooltip
                        effect="dark"
                        content="企业备注"
                        placement="top"
                      >
                        <svg-icon
                          icon-class="chat-poll-fill"
                          class="color-warning"
                        />
                      </el-tooltip>
                    </span>
                    <span v-if="item.remarksCount !== 0">{{
                      item.remarksCount
                    }}</span>
                  </div>
                </div>
              </div>
              <div class="flex m-b-8 color-info">
                <span class="bhh">{{ item.building | noData }} / </span>
                <span class="color-black bhh">{{
                  NumFormat(item.area) | noData
                }}</span>
                <span class="bhh"> m² / </span>
                <span class="text-yc">{{ item.room | noData }}</span>
              </div>
              <div class="yq flex m-b-8 color-info">
                <span class="bhh">{{ item.enterTime | noData }} 入园 / </span>
                <span class="bhh">
                  <el-tooltip
                    effect="dark"
                    :content="item.parkName"
                    placement="top"
                  >
                    <span class="parkBh pointer">{{
                      item.parkName | noData
                    }}</span>
                  </el-tooltip>
                </span>
                <!--                /-->
                <span class="color-primary pointer" v-if="false">
                  <el-tooltip
                    effect="dark"
                    :content="item.contractNumber"
                    placement="top"
                  >
                    <span
                      class="bh"
                      :class="item.contractNumber === '' ? '' : 'btm'"
                      >{{ item.contractNumber | noData }}</span
                    >
                  </el-tooltip>
                </span>
              </div>
              <div>
                <!-- 该企业暂未获得资质荣誉 -->
                <el-tag
                  type="info"
                  v-if="item.labelStr === null || item.labelStr[0] === ''"
                >
                  该企业暂未获得资质荣誉
                </el-tag>
                <el-tag
                  v-else
                  class="m-r-8 m-b-8"
                  v-for="(item, index) in item.labelStr"
                  v-show="index < 5"
                  :key="index"
                  >{{ item | noData }}</el-tag
                >
              </div>
            </div>
          </div>
          <div class="flex justify-content-between">
            <div class="subject-text font-size-12 p-t-20" style="width: 167px">
              <el-tooltip
                v-if="item.technical"
                class="item"
                effect="dark"
                :content="item.technical"
                placement="bottom"
              >
                <div class="m-b-4 line-1 pointer">
                  {{ item.technical | noData }}
                </div>
              </el-tooltip>
              <div v-else class="m-b-4 line-1 pointer">
                {{ item.technical | noData }}
              </div>
              <div class="color-info m-b-16">所属技术领域</div>
              <el-tooltip
                v-if="item.projectType"
                class="item"
                effect="dark"
                :content="item.projectType"
                placement="bottom"
              >
                <div class="m-b-4 line-1 pointer">
                  {{ item.projectType | noData }}
                </div>
              </el-tooltip>
              <div v-else class="m-b-4 line-1 pointer">
                {{ item.projectType | noData }}
              </div>
              <div class="color-info">项目类型</div>
            </div>
            <div
              class="subject-yj font-size-14 p-l-24 m-t-16 inline-block m-r-16"
            >
              <!--              <div class="m-b-8">企业预警</div>-->
              <!--              <div class="color-info">-->
              <!--                <div class="flex">-->
              <!--                  <span class="m-t-6"><basic-tag isDot type="warning" /></span>-->
              <!--                  <span class="m-r-8 bhh">合同交费逾期</span>-->
              <!--                  <span class="color-warning bhh">{{-->
              <!--                    getOverdue(item.overdue) | noData-->
              <!--                  }}</span>-->
              <!--                </div>-->
              <!--                <div class="flex">-->
              <!--                  <span class="primary m-t-6"><basic-tag isDot /></span>-->
              <!--                  <span class="m-r-8 bhh">企业活跃姿态</span>-->
              <!--                  <span class="bhh" style="color: #99b9ff">{{-->
              <!--                    getActivityDegree(item.activityDegree) | noData-->
              <!--                  }}</span>-->
              <!--                </div>-->
              <!--                <div class="flex">-->
              <!--                  <span class="success m-t-6"><basic-tag isDot /></span>-->
              <!--                  <span class="m-r-8 bhh">企业持续发展</span>-->
              <!--                  <span class="gz color-primary bhh">{{-->
              <!--                    getKeepDevelop(item.keepDevelop) | noData-->
              <!--                  }}</span>-->
              <!--                </div>-->
              <!--              </div>-->
            </div>
            <!--            <div class="m-t-16">-->
            <!--              <div class="pf font-size-14 m-b-8">企业评分</div>-->
            <!--              <div>-->
            <!--                &lt;!&ndash; 定义修改svg &ndash;&gt;-->
            <!--                <div style="width: 0; height: 0">-->
            <!--                  <svg width="100%" height="100%">-->
            <!--                    <defs>-->
            <!--                      <linearGradient-->
            <!--                        id="write"-->
            <!--                        x1="0%"-->
            <!--                        y1="0%"-->
            <!--                        x2="5%"-->
            <!--                        y2="50%"-->
            <!--                      >-->
            <!--                        <stop-->
            <!--                          offset="0%"-->
            <!--                          style="stop-color: #fff"-->
            <!--                          stop-opacity="0.8"-->
            <!--                        ></stop>-->
            <!--                        <stop-->
            <!--                          offset="100%"-->
            <!--                          style="stop-color: #0052d9"-->
            <!--                          stop-opacity="0.8"-->
            <!--                        ></stop>-->
            <!--                      </linearGradient>-->
            <!--                    </defs>-->
            <!--                  </svg>-->
            <!--                </div>-->
            <!--                <div class="progress">-->
            <!--                  <el-progress-->
            <!--                    :width="80"-->
            <!--                    :hidden="60"-->
            <!--                    type="circle"-->
            <!--                    :percentage="item.score || 0"-->
            <!--                    :format="dataScope"-->
            <!--                  ></el-progress>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </div>-->
            <div class="span pointer">
              <el-dropdown>
                <span class="sl el-dropdown-link color-info">
                  <svg-icon icon-class="vertical-point" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <!--                  <el-dropdown-item-->
                  <!--                    ><div @click="addTags(item.id, item.enterpriseName)">-->
                  <!--                      添加标签-->
                  <!--                    </div></el-dropdown-item-->
                  <!--                  >-->
                  <el-dropdown-item
                    v-permission="routeButtonsPermission.ADD_REMARKS"
                    ><div @click="addRemarks(item.id, item.enterpriseName)">
                      {{ routeButtonsTitle.ADD_REMARKS }}
                    </div></el-dropdown-item
                  >
                  <el-dropdown-item
                    v-permission="routeButtonsPermission.ENTER_RELEVANCE"
                    ><div @click="relation(item.id, item.enterpriseName)">
                      {{ routeButtonsTitle.ENTER_RELEVANCE }}
                    </div></el-dropdown-item
                  >
                  <el-dropdown-item
                    v-permission="routeButtonsPermission.ENTER_RENAMING"
                    ><div @click="renaming(item.id, item.enterpriseName)">
                      {{ routeButtonsTitle.ENTER_RENAMING }}
                    </div></el-dropdown-item
                  >
                  <el-dropdown-item
                    v-permission="routeButtonsPermission.RENEW_CONTACTS"
                    ><div @click="contacts(item.id, item.enterpriseName)">
                      {{ routeButtonsTitle.RENEW_CONTACTS }}
                    </div></el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
      <!--      <div v-if="foot" class="foot color-info">已经是最后一条了...</div>-->
    </el-scrollbar>
    <!-- 添加标签弹框 -->
    <enterprise-tags ref="tags" @addTags="addTag" />
    <!-- 添加备注信息 -->
    <remarks ref="remarks" @refresh="refresh" />
    <!-- 企业关联 -->
    <association ref="relation" @relation="relations" />
    <!-- 企业关联列表 -->
    <enterprise-association
      ref="association"
      @cancelRelation="cancelRelation"
    />
    <!-- 企业更名 -->
    <renaming ref="renaming" @renaming="getRenaming" />
    <!-- 更多联系人 -->
    <contacts ref="contacts" />
    <!-- 企业备注信息 -->
    <enterprise-remarks ref="enterpriseRemarks" />
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import EnterpriseTags from '../enterpriseTags'
import Remarks from '../remarks'
import EnterpriseAssociation from '../enterpriseAssociation'
import Contacts from '../contacts'
import Renaming from '../renaming'
import EnterpriseRemarks from '../enterpriseRemarks'
import Association from '../association'
import EmptyData from '@/components/EmptyData'
import { getParkStatus } from './utils/status'
import {
  getEnterpriseList,
  getQueryCondition,
  getTags,
  getSelectRelationEntList,
  getEnterpriseInfo,
  editSelectRemarks,
  editSelectRelationEnterprise,
  getSelectRelation
} from '../api/list'
import dayjs from 'dayjs'
import { validateDecimal } from '@/utils/validate'
export default {
  name: 'EnterpriseContent',
  components: {
    EnterpriseTags,
    Remarks,
    EnterpriseAssociation,
    Renaming,
    Contacts,
    EnterpriseRemarks,
    Association,
    EmptyData
  },
  data() {
    return {
      getParkStatus,
      NumFormat,
      rules: {
        minArea: [
          {
            validator: validateDecimal
          }
        ],
        maxArea: [
          {
            validator: validateDecimal
          }
        ]
      },
      formData1: { type: [] },
      formData: { type: [] },
      form: {
        admission: '',
        minArea: '',
        maxArea: ''
      },
      // 企业评分数据
      marks: {
        0: {
          style: {
            color: '#000'
          },
          label: '0'
        },
        50: {
          style: {
            color: '#000'
          },
          label: '50'
        },
        100: {
          style: {
            color: '#000'
          },
          label: '100'
        }
      },
      defaultProps: [],
      visible: true,
      visible1: true,
      visible2: true,
      technicals: [],
      labelIds: [],
      newList: [],
      total: 0,
      current: '',
      data: {},
      pageNo: 1,
      pageSize: 10,
      tagsList: [],
      parkId: '',
      foot: false,
      tabVal: '',
      score: '',
      start: '',
      end: '',
      timer: null
    }
  },
  created() {
    this.initData()
    this.getDataList()
  },
  mounted() {
    this.$refs.box.wrap.addEventListener('scroll', this.lazyLoading, true)
  },
  methods: {
    dataScope(percentage) {
      return percentage
    },
    async tabsChange(e, val) {
      this.tabVal = val
      if (e !== this.current) {
        if (this.parkId) {
          this.parkFn()
        } else {
          this.noParkFn()
        }
        this.current = e
        return
      }
      this.pageNo = 1
      this.tabVal = ''
      this.current = ''
      if (this.parkId) {
        this.parkFn()
      } else {
        this.noParkFn()
      }
    },
    async changeFn() {
      if (this.parkId) {
        this.parkFn()
      } else {
        this.noParkFn()
      }
      this.pageNo = 1
    },
    async changeFun() {
      if (this.parkId) {
        this.parkFn()
      } else {
        this.noParkFn()
      }
      this.pageNo = 1
    },
    async getTime(e) {
      if (e && e.length > 0) {
        this.start = dayjs(e[0]).format('YYYY-MM-DD 00:00:00')
        this.end = dayjs(e[1]).format('YYYY-MM-DD 00:00:00')
        if (this.parkId) {
          this.parkFn()
        } else {
          this.noParkFn()
        }
      } else {
        this.start = ''
        this.end = ''
        if (this.parkId) {
          this.parkFn()
        } else {
          this.noParkFn()
        }
      }
    },
    handleQuery() {
      if (this.timer !== null) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            if (this.parkId) {
              this.parkFn()
            } else {
              this.noParkFn()
            }
          }
        })
        // let areaStr = /^[0-9]+(\.[0-9]{1,2})?$/
        // if (
        //   areaStr.test(this.form.minArea) &&
        //   areaStr.test(this.form.maxArea)
        // ) {
        //   if (this.parkId) {
        //     this.parkFn()
        //   } else {
        //     this.noParkFn()
        //   }
        // }
        // if (!this.form.minArea && !this.form.maxArea) {
        //   if (this.parkId) {
        //     this.parkFn()
        //   } else {
        //     this.noParkFn()
        //   }
        // }
      }, 500)
    },
    async parkFn() {
      let res = await getEnterpriseList({
        pageNo: 1,
        pageSize: 10,
        labelIds: this.formData.type,
        technicalFields: this.formData1.type,
        enterType: this.tabVal,
        areaStart: this.form.minArea,
        areaEnd: this.form.maxArea,
        parkId: this.parkId,
        enterTimeStart: this.start,
        enterTimeEnd: this.end,
        score: this.score
      })
      this.newList = res.list
      this.total = res.total
    },
    // async changeScore(e) {
    //   this.score = e
    //   if (this.parkId) {
    //     this.parkFn()
    //   } else {
    //     this.noParkFn()
    //   }
    // },
    async noParkFn() {
      let res = await getEnterpriseList({
        pageNo: 1,
        pageSize: 10,
        areaStart: this.form.minArea,
        areaEnd: this.form.maxArea,
        score: this.score,
        labelIds: this.formData.type,
        technicalFields: this.formData1.type,
        enterType: this.tabVal,
        enterTimeStart: this.start,
        enterTimeEnd: this.end
      })
      this.newList = res.list
      this.total = res.total
    },
    addDetails(id) {
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id
        }
      })
    },
    async initData() {
      // 获取企业查询接口
      let data = await getQueryCondition()
      this.defaultProps = data.enterTypes
      this.technicals = data.technicals
      this.labelIds = data.labelIds
    },
    async getDataList() {
      // 获取企业列表
      let res = await getEnterpriseList({
        pageNo: this.pageNo,
        pageSize: this.pageSize
      })
      res.list.forEach(item => {
        item.contractNumber = item.contractNumber.replaceAll(',', '')
      })
      this.newList = this.newList.concat(res.list)
      this.total = res.total
    },
    // 滚动加载
    lazyLoading(e) {
      const scrollTop = e.target.scrollTop
      const windowHeight = e.target.offsetHeight
      const scrollHeight = e.target.scrollHeight
      if (scrollTop + windowHeight === scrollHeight) {
        if (this.newList.length === this.total) return
        this.pageNo++
        this.getDataList()
      }
    },
    // 企业标签
    async addTags(id, val) {
      this.$refs.tags.addTags = true
      let res = await getTags({ id })
      this.$refs.tags.dataList1 = res
      this.$refs.tags.entId = id
      this.$refs.tags.name = val
    },
    async addTag() {
      // 获取企业列表
      let res = await getEnterpriseList({
        pageNo: 1,
        pageSize: 10
      })
      this.newList = res.list
      this.total = res.total
      this.current = ''
      this.formData1.type = []
      this.formData.type = []
    },
    // 企业备注
    addRemarks(id, val) {
      this.$refs.remarks.addRemarks = true
      this.$refs.remarks.entId = id
      this.$refs.remarks.name = val
      this.$nextTick(() => {
        this.$refs.remarks.formData = {
          radio: 1,
          desc: ''
        }
        this.$refs.remarks.initData()
      })
    },
    async refresh() {
      // 获取企业列表
      let res = await getEnterpriseList({
        pageNo: 1,
        pageSize: 10,
        parkId: this.parkId
      })
      this.newList = res.list
      this.total = res.total
      this.current = ''
      this.formData1.type = []
      this.formData.type = []
    },
    // 企业关联
    async relation(id, val) {
      const data = await getSelectRelation({ id })
      this.$refs.relation.dataList = data
      this.$refs.relation.visible = true
      this.$refs.relation.entId = id
      let res = await getSelectRelationEntList({ id })
      this.$refs.relation.list = res
      this.$refs.relation.name = val
    },
    async relations() {
      let res = await getEnterpriseList({
        pageNo: 1,
        pageSize: 10,
        parkId: this.parkId
      })
      this.newList = res.list
      this.total = res.total
      this.current = ''
      this.formData1.type = []
      this.formData.type = []
    },
    // 企业更名
    async renaming(id, val) {
      this.$refs.renaming.visible = true
      this.$refs.renaming.entId = id
      let res = await getEnterpriseInfo({ id })
      this.getInfo = res
      this.$refs.renaming.formData.code = res.creditCode
      this.$refs.renaming.formData.name = res.enterpriseName
      this.$refs.renaming.name = val
    },
    async getRenaming() {
      let res = await getEnterpriseList({
        pageNo: 1,
        pageSize: 10,
        parkId: this.parkId
      })
      this.newList = res.list
      this.total = res.total
      this.current = ''
      this.formData1.type = []
      this.formData.type = []
    },
    // 更新联系人
    async contacts(id, val) {
      this.$refs.contacts.visible = true
      this.$refs.contacts.formData.entId = id
      let res = await getEnterpriseInfo({ id })
      this.$refs.contacts.formData.legalPerson = res.legalPerson
      this.$refs.contacts.formData.legalPersonPhone = res.legalPersonPhone
      this.$refs.contacts.formData.contact = res.contact
      this.$refs.contacts.formData.phone = res.phone
      this.$refs.contacts.formData.manager = res.manager
      this.$refs.contacts.formData.managerPhone = res.managerPhone
      this.$refs.contacts.name = val
    },
    // 企业关联列表
    async association(id) {
      this.$refs.association.visible = true
      this.$refs.association.entId = id
      let res = await editSelectRelationEnterprise({ id })
      if (res.enterTime !== null) {
        res.enterTime = dayjs(res.enterTime).format('YYYY-MM-DD')
      }
      this.$refs.association.list = []
      this.$refs.association.dataList = []
      this.$refs.association.list.push(res)
      res.relationEnterprises.forEach(item => {
        if (item.enterTime !== null) {
          item.enterTime = dayjs(item.enterTime).format('YYYY-MM-DD')
        }
      })
      this.$refs.association.dataList = res.relationEnterprises
    },
    // 企业备注信息
    async enterpriseRemarks(id) {
      this.$refs.enterpriseRemarks.visible = true
      let res = await editSelectRemarks({ id })
      this.$refs.enterpriseRemarks.dataList = res
    },
    cancelRelation(e) {
      this.association(e)
      this.relations()
    }
  }
}
</script>

<style lang="scss" scoped>
.subject-wrapper {
  padding-bottom: 24px;
  .subject-lt {
    width: 346px;
    overflow: auto;
    padding: 24px 0 0 24px;
  }
  .subject-rt {
    flex: 1;
    padding: 24px 24px 0 24px;
  }
  .foot {
    height: 20px;
    line-height: 20px;
    font-weight: 700;
    text-align: center;
  }
  .subject-type {
    position: relative;
    width: 305px;
    max-height: 228px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    opacity: 1;
    .tabs-item {
      height: 40px;
      line-height: 40px;
      position: relative;
      cursor: pointer;

      &.active {
        @include font_color(--color-primary);
        @include background-color(--background-color-light);
        opacity: 1;
        border-radius: 3px;
      }
    }
    .tabs-item:hover {
      background-color: #e9f0ff;
    }
  }
  .subject-tree {
    width: 305px;
    max-height: 228px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    opacity: 1;
  }
  ::-webkit-scrollbar {
    width: 0;
  }
  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.9);
  }
  .subject-content {
    position: relative;
    height: 168px;
    border-radius: 3px;
    padding: 16px;
    opacity: 1;
    border: 1px solid #e9f0ff;
    .zy {
      position: absolute;
      left: -4px;
      top: 8px;
      width: 40px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      background-color: #ed7b2f;
      border-radius: 0 3px 3px 0;
      opacity: 1;
      z-index: 99;
    }
    .zy1 {
      position: absolute;
      left: -4px;
      top: 8px;
      width: 40px;
      height: 28px;
      line-height: 28px;
      text-align: center;
      background-color: #8a8b8a;
      border-radius: 0 3px 3px 0;
      opacity: 1;
      z-index: 999;
    }
    .sj {
      position: absolute;
      left: -4px;
      top: 36px;
      width: 0;
      height: 0;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      border-top: 2px solid #ba431b;
      border-right: 2px solid #ba431b;
    }
    .sj1 {
      position: absolute;
      left: -4px;
      top: 36px;
      width: 0;
      height: 0;
      border-left: 2px solid #fff;
      border-bottom: 2px solid #fff;
      border-top: 2px solid #4a4b4a;
      border-right: 2px solid #4a4b4a;
    }
    .logo {
      width: 104px;
      height: 104px;
      &.leave {
        filter: grayscale(100%);
      }
    }
    .parkBh {
      //display: inline-block;
      //max-width: 100px;
      //overflow: hidden;
      //text-overflow: ellipsis;
      //white-space: nowrap;
    }
    .bh {
      display: inline-block;
      max-width: 385px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      // border-bottom: 1px solid #ED7B2F;
    }
    .tb {
      position: absolute;
      right: 0;
      top: 0;
    }
    .subject-text {
      //height: 104px;
      line-height: 1.5em;
      text-align: center;
      padding-left: 10px;
      border-left: 1px solid #e9f0ff;
      //border-right: 1px solid #e9f0ff;
    }
    .subject-yj {
      height: 104px;
      line-height: 1.5em;
    }
    .gz {
      border-bottom: 1px solid #ed7b2f;
    }
    .sl {
      display: block;
      width: 28px;
      height: 136px;
      line-height: 136px;
      text-align: center;
    }
    .span:hover {
      width: 28px;
      height: 136px;
      line-height: 136px;
      text-align: center;
      background-color: #f3f3f3;
      border-radius: 3px;
      opacity: 1;
    }
    .pf {
      text-align: center;
    }
    ::v-deep .el-progress-circle {
      svg > path:nth-child(2) {
        stroke: url(#write);
      }
    }
    ::v-deep .primary .dot[data-v-fbad3f82] {
      background-color: #99b9ff !important;
    }
    ::v-deep .success .dot[data-v-fbad3f82] {
      background-color: #48c79c !important;
    }
  }
  ::v-deep .elIpt input {
    border: none;
  }
  ::v-deep .ipt .el-date-editor {
    border: none;
  }
  .ipt {
    width: 232px;
    border: 1px solid #dcdcdc;
    border-radius: 3px;
    opacity: 1;
  }
  .text-yc {
    display: inline-block;
    width: 137px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .bhh {
    display: inline-block;
    white-space: nowrap;
  }
  ::v-deep .el-date-editor .el-range__icon {
    display: none;
  }
  :deep(.el-form-item) {
    margin-bottom: 0 !important;
  }
  img {
    width: 104px;
    height: 104px;
    border-radius: 5px;
    border: 1px solid #ed7b2f14;
  }
  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0;
  }
  :deep(.bhh .el-tooltip) {
    outline: none;
  }
  .technology {
    height: 40px;
    line-height: 40px;
  }
  .technology:hover {
    height: 40px;
    background-color: #e9f0ff;
  }
  .enterprise {
    height: 40px;
    line-height: 40px;
  }
  .enterprise:hover {
    background-color: #e9f0ff;
  }
  .btm {
    border-bottom: 1px solid #ed7b2f !important;
  }
}
</style>
