<template>
  <div class="w100 tabs-wrapper">
    <div class="tabs">
      <div
        v-for="(item, index) in tabsData"
        :key="index"
        class="font-size-14 color-text-regular m-r-20 tabs-item"
        :class="+item.value === current ? 'active' : ''"
        @click="tabsChange(item.value)"
      >
        <span class="p-r-4">{{ item.label }}</span>
        <span :class="+item.value === current ? 'color-p' : ''">{{
          item.value
        }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicTab',
  props: {
    tabsData: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  mounted() {
    console.log(this.current)
  },
  methods: {
    tabsChange(e) {
      this.$emit('tabsChange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-wrapper {
  margin-bottom: 14px;
  .tabs {
    display: flex;
    //过渡
    transition: all 0.3s;
    overflow-x: hidden;
    overflow-y: hidden;
    white-space: nowrap;
  }
  .tabs:hover {
    transition: all 0.3s;
    overflow-x: auto;
  }
  .color-p {
    color: #128bed;
  }

  .tabs-item {
    height: 38px;
    line-height: 38px;
    position: relative;
    cursor: pointer;

    &.active {
      color: #128bed;
      font-weight: bold;
      &::before {
        content: '';
        width: 100%;
        height: 4px;
        background-color: #128bed;
        position: absolute;
        bottom: -2px;
        left: 0;
      }
    }
  }
}
</style>
