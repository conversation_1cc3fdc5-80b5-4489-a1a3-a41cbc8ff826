<template>
  <div
    class="w100 tabs-wrapper flex align-items-center justify-content-between"
  >
    <div class="flex align-items-center p-l-24">
      <div
        v-for="(item, index) in tabsData"
        :key="index"
        class="font-size-14 color-text-regular p-l-16 p-r-16 tabs-item"
        :class="item.value === current ? 'active' : ''"
        @click="tabsChange(index)"
      >
        {{ item.label }}（{{ item.count }}）
      </div>
    </div>
    <slot name="right"></slot>
  </div>
</template>

<script>
export default {
  name: 'BasicTab',
  props: {
    tabsData: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  methods: {
    tabsChange(e) {
      this.$emit('tabsChange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  margin-bottom: 24px;

  .tabs-item {
    height: 40px;
    line-height: 40px;
    position: relative;
    cursor: pointer;

    &.active {
      @include font_color(--color-primary);
      &::before {
        content: '';
        width: 100%;
        height: 2px;
        @include background-color(--color-primary);
        position: absolute;
        bottom: -2px;
        left: 0;
      }
    }
  }
}
</style>
