<template>
  <div class="enter-warning-container bg-white">
    <div class="flex">
      <div class="main-left">
        <div class="text-n line-height-22 m-b-24">重点指标</div>
        <div class="main-wrapper">
          <!--          <el-row style="margin-bottom: 45px;" class="main-row">-->
          <!--            <el-col class="m-b-16">-->
          <!--              <span class="text-n font-strong">主营业务收入</span>-->
          <!--              <span class="text-f m-l-8">同比(%)</span>-->
          <!--              <span class="color-success m-l-4 m-r-4">36%</span>-->
          <!--              <svg-icon icon-class="down-arrow-radian class="color-success""/>-->
          <!--            </el-col>-->
          <!--            <el-col class="line-height-22">-->
          <!--              <div class="text-f m-b-4">-->
          <!--                <span class="m-r-35">2021年收入(元)</span>-->
          <!--                <span>2022年收入(元)</span>-->
          <!--              </div>-->
          <!--              <div class="color-warning font-strong">-->
          <!--                <span class="m-r-35">95,995,036.00</span>-->
          <!--                <span>95,995,036.00</span>-->
          <!--              </div>-->
          <!--            </el-col>-->
          <!--          </el-row>-->
          <!--          <el-row style="margin-bottom: 45px;" class="main-row">-->
          <!--            <el-col class="m-b-16">-->
          <!--              <span class="text-n font-strong">税收</span>-->
          <!--              <span class="text-f m-l-8">同比(%)</span>-->
          <!--              <span class="color-success m-l-4 m-r-4">36%</span>-->
          <!--              <svg-icon icon-class="down-arrow-radian class="color-success""/>-->
          <!--            </el-col>-->
          <!--            <el-col class="line-height-22">-->
          <!--              <div class="text-f m-b-4">-->
          <!--                <span class="m-r-35">2021年收入(元)</span>-->
          <!--                <span>2022年收入(元)</span>-->
          <!--              </div>-->
          <!--              <div class="color-warning font-strong">-->
          <!--                <span class="m-r-35">95,995,036.00</span>-->
          <!--                <span>95,995,036.00</span>-->
          <!--              </div>-->
          <!--            </el-col>-->
          <!--          </el-row>-->
          <el-row class="main-row">
            <el-col class="m-b-16">
              <span class="text-n font-strong">资质荣誉</span>
            </el-col>
            <el-col class="line-height-22 m-b-13">
              <div class="flex">
                <div style="margin-right: 51px">
                  <div class="text-f m-b-4">累计获得(项)</div>
                  <div class="color-primary font-strong">12</div>
                </div>
                <div>
                  <div class="text-f m-b-4">累计获得(项)</div>
                  <div class="text-f font-strong">0</div>
                </div>
              </div>
            </el-col>
            <el-col class="line-height-22">
              <div class="text-f m-b-4">失效预警(项)</div>
              <div class="flex">
                <div class="color-danger" style="margin-right: 72px">
                  <span class="font-strong m-r-4">11</span>
                  <span>已失效</span>
                </div>
                <div class="color-warning">
                  <span class="font-strong m-r-4">3</span>
                  <span>即将失效</span>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row class="main-row">
            <el-col class="m-b-16">
              <span class="text-n font-strong">知识产权</span>
            </el-col>
            <el-col class="line-height-22">
              <div class="m-b-22">
                <span class="text-f m-r-24">软著(项)</span>
                <span class="font-strong m-r-16">{{
                  objKnowledge.opusCopyCount
                }}</span>
                <span class="text-f m-r-4">同比(%)</span>
                <span class="color-success font-strong m-r-4"
                  >{{ objKnowledge.opusCopyCountChain }}%</span
                >
                <svg-icon
                  icon-class="down-arrow-radian"
                  class="color-success"
                />
              </div>
              <div class="m-b-22">
                <span class="text-f m-r-24">专利(项)</span>
                <span class="font-strong m-r-16">{{
                  objKnowledge.patentCount
                }}</span>
                <span class="text-f m-r-4">同比(%)</span>
                <span class="color-success font-strong m-r-4"
                  >{{ objKnowledge.patentCountChain }}%</span
                >
                <svg-icon
                  icon-class="down-arrow-radian"
                  class="color-success"
                />
              </div>
              <div>
                <span class="text-f m-r-24">商标(项)</span>
                <span class="font-strong m-r-16">{{
                  objKnowledge.tmCount
                }}</span>
                <span class="text-f m-r-4">同比(%)</span>
                <span class="color-success font-strong m-r-4"
                  >{{ objKnowledge.tmCountChain }}%</span
                >
                <svg-icon
                  icon-class="down-arrow-radian"
                  class="color-success"
                />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="main-right">
        <basic-tab
          ref="basicTab"
          :tabs-data="list"
          :current="current"
          @tabsChange="tabsChange"
        />
        <div class="p-l-24 p-r-24">
          <drive-table
            ref="drive-table"
            :table-data="tableList"
            max-height="240px"
            :columns="tableColumn"
          >
          </drive-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import BasicTab from './components/BasicTab'
import ColumnMixins from './column'
import { getIntellectual } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'
export default {
  name: 'EnterWarning',
  props: {
    creditCode: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  components: {
    BasicTab
  },
  data() {
    return {
      list: [
        {
          label: '企业预警',
          value: 0,
          count: 1
        },
        {
          label: '企业消息',
          value: 1,
          count: 2
        },
        {
          label: '企业线索',
          value: 2,
          count: 2
        }
      ],
      current: 0,
      tableList: [
        {
          type: '消息创新',
          details: '必欧瀚生物技术（合肥）有限公司企业 进行股权出置',
          updateTime: '2022-10-23 10:20'
        },
        {
          type: '消息创新',
          details: '必欧瀚生物技术（合肥）有限公司企业 进行股权出置',
          updateTime: '2022-10-23 10:20'
        },
        {
          type: '消息创新',
          details: '必欧瀚生物技术（合肥）有限公司企业 进行股权出置',
          updateTime: '2022-10-23 10:20'
        },
        {
          type: '消息创新',
          details: '必欧瀚生物技术（合肥）有限公司企业 进行股权出置',
          updateTime: '2022-10-23 10:20'
        }
      ],
      objKnowledge: {}
    }
  },
  created() {
    this.getIntellectual()
  },
  methods: {
    async getIntellectual() {
      const res = await getIntellectual(this.creditCode)
      this.objKnowledge = res
    },
    tabsChange(e) {
      this.current = e
    }
  }
}
</script>

<style lang="scss" scoped>
.enter-warning-container {
  margin-bottom: 8px;
  padding: 24px 0 24px 24px;
  border-radius: 3px;
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
  .text-e {
    color: rgba(0, 0, 0, 0.8);
  }
  .text-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .main-left {
    width: 600px;
    font-size: 14px;
    border-right: 1px solid #f0f0f0;
    .main-wrapper {
      display: flex;
      flex-flow: wrap;
      .main-row {
        width: 50%;
      }
    }
  }
  .main-right {
    flex: 1;
  }
}
</style>
