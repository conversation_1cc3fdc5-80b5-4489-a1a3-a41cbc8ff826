import request from '@/utils/request'

// 根据企业id获取企业基本信息
export function getSelectBasicDetail(params) {
  return request({
    url: `/enterprise/info/selectBasicDetail?id=${params}`,
    method: 'get'
  })
}

// 获取企业基本信息
export function getEntInfo(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0,
      investmentList: [],
      holdingCompanyList: []
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/info?creditCode=${params}`,
    method: 'get'
  })
}

// 获取受益所有人
export function getBeneficialOwner(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0,
      breakThroughList: '[]'
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/beneficiary?creditCode=${params}`,
    method: 'get'
  })
}
// 获取间接持股企业
export function getIndirectHolding(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/indirect_holding`,
    method: 'get',
    params
  })
}
// 获取商标信息
export function getNationalList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/trademark/national/list_by`,
    method: 'get',
    params
  })
}
// 获取专利信息
export function getPatentList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/patent/list_by`,
    method: 'get',
    params
  })
}
// 获取作品著作权
export function getCopyrightList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/copyright/list_by`,
    method: 'get',
    params
  })
}
// 获取软著著作权
export function getCopyrightSoftwareList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/copyright/software/list_by`,
    method: 'get',
    params
  })
}
// 获取备案网站
export function getCompanyWebList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/company/web_site/list_by`,
    method: 'get',
    params
  })
}
// 数量统计汇总
export function getCountList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/qcc/count_by_creditCode`,
    method: 'get',
    params
  })
}

// 获取企业工商信息
export function getBusinessInfo(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0,
      area: [
        {
          province: '',
          county: ''
        }
      ],
      industry: [{}],
      partners: [],
      employees: [],
      tagList: []
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/business_info?creditCode=${params}`,
    method: 'get'
  })
}

// 获得企业在园信息
export function getEnterpriseInfo(params) {
  return request({
    url: `/enterprise/info/get_ent_info`,
    method: 'get',
    params
  })
}

// 获取企业入驻信息-企业概览
export function getEnterpriseEntEnterInfo(params) {
  return request({
    url: `/enterprise/info/get_ent_enter_info`,
    method: 'get',
    params
  })
}

// 获取企业备注列表分页
export function getEnterpriseSelectRemarksPage(params) {
  return request({
    url: `/enterprise/info/select_remarks_page`,
    method: 'get',
    params
  })
}

// 在园信息 关联企业
export function getSelectRelationPage(params) {
  return request({
    url: `/enterprise/info/select_relation_page`,
    method: 'get',
    params
  })
}

// 园区端-员工信息列表
export function getEntPage(params) {
  return request({
    url: `/employeeManager/entPage`,
    method: 'get',
    params
  })
}

// 园区端-企业信息-车辆列表
export function getCarManager(params) {
  return request({
    url: `/carManager/get_ent_page`,
    method: 'get',
    params
  })
}

// 企业信息 - 孵化活动
export function getActivitysign(params) {
  return request({
    url: `/hatch/activitysign/ent_page`,
    method: 'get',
    params
  })
}

// 获取企业对外投资
export function getInvestmentThrough(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/eci_investment_through?creditCode=${params}`,
    method: 'get'
  })
}

// 获取企业变更信息
export function getChangeRecord(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/change_record`,
    method: 'get',
    params
  })
}

// 获取企业分支机构
export function getBranchInfo(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/branch`,
    method: 'get',
    params
  })
}
// 获取企业实际控制人
export function getActualControlInfo(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0,
      controllerData: {
        paths: '[]'
      }
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/actual_control_info?creditCode=${params}`,
    method: 'get'
  })
}

// 获取企业标签
export function getSelectEnterpriseLabels(params) {
  return request({
    url: `/enterprise/info/selectEnterpriseLabels`,
    method: 'get',
    params
  })
}

// 获取知识产权
export function getIntellectual(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/v1/enterprise/basic/intellectual?creditCode=${params}`,
    method: 'get'
  })
}

// 获得企业资质荣誉统计
export function getHonor(params) {
  return request({
    url: `/enterprise/info/get_statistics_qualification_honor?code=${params}`,
    method: 'get'
  })
}

// 获取企业股权穿透信息
export function getSelectEquityThrough(params) {
  return request({
    url: `/enterprise/info/selectEquityThrough?id=${params}`,
    method: 'get'
  })
}

// 获取诉讼核查信息
export function getLitigation(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0,
      theExecutedList: [],
      untrustworthyList: [],
      sumptuaryCheckList: [],
      limitExitList: [],
      bankruptcyList: [],
      endExecuteCaseList: [],
      judgmentDocList: [],
      courtNoticeList: [],
      courtAnnoList: [],
      deliveryNoticeList: [],
      inquiryAssessList: [],
      judicialSaleList: [],
      equityFreezeList: [],
      caseFilingList: []
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/legal/litigation?creditCode=${params}`,
    method: 'get'
  })
}
// 获取司法核查信息
export function getJustice(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/legal/justice?creditCode=${params}`,
    method: 'get'
  })
}

//企业年报
export function getAnnualInfo(params) {
  return new Promise(resolve => {
    resolve()
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/annual/report/list_by`,
    method: 'get',
    params
  })
}
//企业发展
export function getUnionFirst(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0,
      companyFinancingList: [],
      coreMemberList: [],
      companyProductList: [],
      compatProductRecommendList: [],
      bangDanList: []
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/develop/union_first?creditCode=${params}`,
    method: 'get'
  })
}

//荣誉信息
export function getHonorInfo(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/honor/qualification/list_by`,
    method: 'get',
    params
  })
}

//企业公告a股调用
export function getIpoAnnouncement(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/ipo_announcement/list_by`,
    method: 'get',
    params
  })
}

//企业公告新三板调用
export function getIpoNeeqAnnouncement(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/ipo_neeq_announcement/list_by`,
    method: 'get',
    params
  })
}

//新闻舆情
export function getNewInfo(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/company/new/list_by`,
    method: 'get',
    params
  })
}
// 获取新闻详情
export function enterpriseNewsDetail(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: '/dc/transfer/qcc/company_new_detail/get',
    method: 'get',
    params
  })
}

// 经营信息1
export function getBusinessFirst(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/operate/union_first`,
    method: 'get',
    params
  })
}
// 经营信息2
export function getBusinessSecond(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/operate/union_second`,
    method: 'get',
    params
  })
}
// 经营信息-招投标
export function tenderCheckList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/tender/check/list_by`,
    method: 'get',
    params
  })
}
// 经营信息-企业证书
export function certificationList(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/eci/certification/list_by`,
    method: 'get',
    params
  })
}

// 经营风险
export function getRiskBusiness(params) {
  return new Promise(resolve => {
    resolve({
      list: [],
      total: 0
    })
  })
  // eslint-disable-next-line no-unreachable
  return request({
    url: `/dc/transfer/qcc/risk/business?creditCode=${params}`,
    method: 'get'
  })
}
