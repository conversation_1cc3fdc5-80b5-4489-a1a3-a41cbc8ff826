<template>
  <!-- 作品著作权 -->
  <div class="stock bg-white">
    <div class="m-b-20 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <span class="color-p">{{ count }}</span>
    </div>

    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :isNeedPagination="true"
      :extral-querys="extralQuerys"
      :api-fn="getCopyrightList"
      :scrollTop="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column/column'
import { getCopyrightList } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'
export default {
  name: 'CopyrightWorks',
  mixins: [ColumnMixins],
  props: {
    title: {
      type: String,
      default: '作品著作权'
    },
    titleId: {
      type: String,
      default: 'CopyrightWorks'
    },
    creditCode: {
      type: String,
      default: ''
    },
    count: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      extralQuerys: {
        creditCode: this.creditCode
      },
      getCopyrightList
    }
  },
  methods: {
    serialHandle(index) {
      const pageSize = this.$refs['drive-table'].PAGE_SIZE
      const pageNumber = this.$refs['drive-table'].PAGE_NUMBER
      return pageSize * (pageNumber - 1) + index + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.stock {
  margin-bottom: 40px;
}
::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
