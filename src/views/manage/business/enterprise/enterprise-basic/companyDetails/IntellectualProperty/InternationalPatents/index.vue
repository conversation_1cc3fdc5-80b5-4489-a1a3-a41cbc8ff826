<template>
  <!-- 国际专利 -->
  <div class="stock bg-white">
    <div class="m-b-4 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <el-tooltip
        class="item"
        effect="light"
        content="最新公示: "
        placement="bottom-start"
      >
        <div slot="content">
          <div class="m-b-6">
            最新公示: 来源企业年报、招股书和企业上市公告等
          </div>
          <div>工商登记: 来源国家企业信用信息公示系统</div>
        </div>
        <svg-icon icon-class="error-circle" class="color-info font-size-15" />
      </el-tooltip>
    </div>
    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
    ></basic-tab>
    <drive-table
      ref="drive-table"
      :columns="tableColumnData"
      :table-data="tableData"
    >
    </drive-table>
  </div>
</template>

<script>
import BasicTab from '../../../components/BasicTab'
import ColumnMixins from './column/column'
// import {deepClone} from '@/utils/tools'
export default {
  name: 'InternationalPatents',
  mixins: [ColumnMixins],
  components: {
    BasicTab
  },
  props: {
    title: {
      type: String,
      default: '国际专利'
    },
    titleId: {
      type: String,
      default: 'InternationalPatents'
    }
  },
  data() {
    return {
      list: [
        {
          label: '最新公示',
          value: 0
        },
        {
          label: '工商登记',
          value: 1
        },
        {
          label: '历史主要人员',
          value: 2
        }
      ],
      current: 0,
      tableData1: [
        {
          serialNumber: '1',
          name: '刘庆峰',
          sex: '男',
          education: '本科',
          position: '董事长,法定代表人',
          salary: '560万',
          shareholdingRatio: '7.24%',
          shareholdingNum: '168,253,267',
          beneficiaryShares: '8.9%',
          termOffice: '2009-04-13 至 2026-01-15',
          announcementDate: '2020-01-15'
        }
      ],
      tableData2: [
        {
          serialNumber: '1',
          name: '刘青峰',
          position: '董事长,法定代表人',
          ratio: '7.24%',
          beneficiaryShares: '8.9%'
        }
      ],
      tableData3: [
        {
          serialNumber: '1',
          name: '王兵',
          position: '董事',
          dateService: '2017-02-21'
        }
      ]
    }
  },

  methods: {
    tabsChange(e) {
      this.current = e
    }
  },
  computed: {
    tableColumnData() {
      if (this.current === 0) {
        return this.tableColumn
      } else if (this.current === 1) {
        return this.tableColumnFirm
      } else {
        return this.tableColumnHistory
      }
    },
    tableData() {
      if (this.current === 0) {
        return this.tableData1
      } else if (this.current === 1) {
        return this.tableData2
      } else {
        return this.tableData3
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.stock {
  margin-bottom: 40px;
}
::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
