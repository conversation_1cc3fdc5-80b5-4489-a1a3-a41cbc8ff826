export default {
  data() {
    return {
      tableColumn: [
        {
          label: '序号',
          width: 60,
          prop: 'serialNumber',
          align: 'center',
          render: (h, scope) => {
            return <div>{this.serialHandle(scope.$index)}</div>
          }
        },
        // {
        //   label: '网站名称',
        //   prop: 'websiteName',
        //   align: 'center',
        // },
        {
          label: '网址',
          prop: 'homeSite',
          align: 'center',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                underline={false}
                onclick={() => this.newOpenHandle(scope.row.homeSite)}
              >
                {scope.row.homeSite}
              </el-link>
            )
          }
        },
        {
          label: '域名',
          prop: 'yuMing',
          align: 'center'
        },
        {
          label: '网站备案/许可证号',
          prop: 'beiAn',
          align: 'center'
        },
        {
          label: '审核日期',
          prop: 'sDate',
          align: 'center'
        }
      ]
    }
  }
}
