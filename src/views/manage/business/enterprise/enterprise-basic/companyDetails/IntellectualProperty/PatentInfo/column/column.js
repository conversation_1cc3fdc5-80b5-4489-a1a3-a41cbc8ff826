import { parseTime } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '序号',
          width: 60,
          prop: 'serialNumber',
          align: 'center',
          render: (h, scope) => {
            return <div>{this.serialHandle(scope.$index)}</div>
          }
        },
        {
          label: '发明名称',
          prop: 'title'
        },
        {
          label: '专利类型',
          prop: 'kindCodeDesc',
          align: 'center'
        },
        {
          label: '法律状态',
          prop: 'legalStatusDesc',
          align: 'center',
          render: (h, scope) => {
            return (
              <basic-tag type="primary" label={scope.row.legalStatusDesc} />
            )
          }
        },
        {
          label: '申请号',
          prop: 'applicationNumber',
          align: 'center'
        },
        {
          label: '申请日期',
          prop: 'applicationDate',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>{parseTime(scope.row.applicationDate, '{y}-{m}-{d}')}</div>
            )
          }
        },
        {
          label: '公开(公告)号',
          prop: 'publicationNumber',
          align: 'center'
        },
        {
          label: '公开(公告)日期',
          prop: ' publicationDate',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>{parseTime(scope.row.publicationDate, '{y}-{m}-{d}')}</div>
            )
          }
        },
        {
          label: '发明人',
          prop: 'inventorStringList',
          align: 'center'
          // render: (h, scope) => {
          //   return <div>{this.strFormatter(scope.row.inventorStringList)}</div>
          // }
        }
      ]
    }
  }
}
