export default {
  data() {
    return {
      tableColumn: [
        {
          label: '序号',
          width: 60,
          prop: 'serialNumber',
          align: 'center',
          render: (h, scope) => {
            return <div>{this.serialHandle(scope.$index)}</div>
          }
        },
        {
          label: '作品名称',
          prop: 'name',
          align: 'center'
        },
        {
          label: '首次发表日期',
          prop: 'publishDate',
          align: 'center'
        },
        {
          label: '作完成日期',
          prop: 'finishDate',
          align: 'center'
        },
        {
          label: '登记号',
          prop: 'registerNo',
          align: 'center'
        },
        {
          label: '登记日期',
          prop: 'registerDate',
          align: 'center'
        },
        {
          label: '作品类别',
          prop: 'category',
          align: 'center'
        }
      ]
    }
  }
}
