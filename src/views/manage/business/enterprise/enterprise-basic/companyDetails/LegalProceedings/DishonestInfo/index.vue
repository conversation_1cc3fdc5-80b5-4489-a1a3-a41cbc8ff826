<template>
  <div class="law-judicature-container">
    <div class="law-title m-b-10" :id="titleId">
      <span class="law-title-text">{{ title }}</span>
      <span class="law-title-num">{{ count }}</span>
    </div>
    <div class="ntable">
      <div class="flex justify-content-center align-items-center">
        <span>
          <el-tooltip
            class="item"
            effect="dark"
            content="失信总金额是对失信的执行标的汇总计算，且已对属于同一司法案件的执行标的做了去重处理。"
            placement="bottom-start"
          >
            <span>
              <svg-icon
                slot="reference"
                class="law-title-svg"
                icon-class="info-circle"
              />
            </span>
          </el-tooltip>
        </span>
        <span class="m-r-6"> 涉案总金额: </span>
        <span class="color-danger font-strong m-r-6">
          {{ NumFormat(totalUntrustworthyAmount) }}
        </span>
        元
      </div>
    </div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableData"
      :columns="tableColumn"
      :isNeedPagination="true"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins, { lawList } from './column'
import { deepClone, NumFormat } from '@/utils/tools'
export default {
  name: 'DishonestInfo',
  props: {
    title: {
      type: String,
      default: '失信信息'
    },
    titleId: {
      type: String,
      default: 'DishonestInfo'
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  mounted() {
    this.tableData = deepClone(this.$options.propsData.untrustworthyList)
    this.totalUntrustworthyAmount =
      this.$options.propsData.totalUntrustworthyAmount || 0
  },
  data() {
    return {
      develop: true,
      lawList,
      NumFormat,
      totalUntrustworthyAmount: 0,
      tableData: []
    }
  }
}
</script>

<style lang="scss" scoped>
.law-judicature-container {
  margin-bottom: 30px;
  .law-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
    .law-title-text {
      margin-right: 3px;
    }
    .law-title-num {
      color: #ed7b2f;
      font-size: 16px;
      font-weight: bold;
      margin-right: 3px;
    }
    .law-title-svg {
      color: #d6d6d6;
      font-size: 16px;
      margin-top: 8px;
    }
  }

  .law-analyse {
    background-image: url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.53/images/background-754c6437.png);
    background-repeat: no-repeat;
    background-size: 153px 28px;
    border-radius: 4px;
    border: 1px solid #c4dff5;
    padding: 15px;
    margin-bottom: 20px;
    .law-analyse-title {
      img {
        width: 20px;
        height: 22px;
      }
      .law-develop {
        span,
        .law-svg {
          color: #128bed;
        }
        &:hover span,
        .law-svg {
          @include font_color('--color-primary');
        }
      }
    }
  }
  .ntable {
    width: 100%;
    height: 43px;
    margin: 10px 0;
    font-size: 15px;
    text-align: center;
    padding: 12px 2px 12px 2px;
    background-color: #f2f8fe;
    background-image: url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.383/images/moer-04d863fa.png),
      url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.383/images/moer-04d863fa.png);
    background-size: 189px 189px, 189px 189px;
    background-repeat: no-repeat;
    background-position: -5px -85px, 1005px -35px;

    .law-title-svg {
      color: #d6d6d6;
      font-size: 16px;
      margin-right: 3px !important;
    }
  }
}
</style>
