export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'index',
          label: '序号',
          width: 60,
          render: (h, scope) => {
            return <div>{scope.$index + 1}</div>
          }
        },
        {
          prop: 'biaoDi',
          label: '标的物'
        },
        {
          prop: 'biaoDiOwnerList',
          label: '标的物所有人',
          render: (h, scope) => {
            const data = JSON.parse(scope.row.biaoDiOwnerList)
            return (
              <div>
                {data.map(item => {
                  return (
                    <div>
                      <div>
                        <span className="color-primary p-l-6">{item.Name}</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            )
          }
        },
        // {
        //   prop: 'causeReason',
        //   label: '确定参考价方式',
        // },
        {
          prop: 'evaluationResult',
          label: '询价结果(元)',
          sortMethod: (a, b) => {
            return a.evaluationResult - b.evaluationResult
          }
        },
        {
          prop: 'relatedList',
          label: '关联对象',
          render: (h, scope) => {
            const data = JSON.parse(scope.row.relatedList)
            return (
              <div>
                {data.map(item => {
                  return (
                    <div>
                      <div>
                        <span className="color-primary p-l-6">{item.Name}</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            )
          }
        },
        {
          prop: 'caseNo',
          label: '案号'
        },
        {
          prop: 'courtName',
          label: '法院名称'
        },
        {
          prop: 'publicDate',
          label: '发布日期'
        }
      ]
    }
  }
}

export const lawList = [
  {
    lawCaseName:
      '杭州乐读科技有限公司与科大讯飞股份有限公司不正当竞争纠纷的案件',
    caseStatus: '一审被告',
    causeReason: '不正当竞争纠纷',
    causeNo: '（2022）浙01民初570号',
    caseAmount: '',
    caseProgress: '2023-02-24民事一审',
    court: '浙江省杭州市中级人民法院',
    caseType: '民事案件',
    reason: ''
  },
  {
    lawCaseName: '赵露思与科大讯飞股份有限公司肖像权纠纷的案件',
    caseStatus: '一审被告',
    causeReason: '肖像权纠纷',
    causeNo: '（2023）川7101民初965号',
    caseAmount: '',
    caseProgress: '2023-02-23民事一审',
    court: '成都铁路运输法院',
    caseType: '民事案件',
    reason: '未经许可而使用他人肖像引起的纠纷。'
  },
  {
    lawCaseName:
      '卜杰与刘金平,安徽一叶兰园林建设有限公司,浙江立昂市政园林建设有限公司等建设工程分包合同纠纷的案件',
    caseStatus: '一审被告',
    causeReason: '建设工程分包合同纠纷',
    causeNo: '（2022）皖0191民初11555号',
    caseAmount: '',
    caseProgress: '2022-12-21民事一审',
    court: '安徽省合肥市合肥高新技术产业开发区人民法院',
    caseType: '民事案件',
    reason: '承包人因将其承包的工程再发包给其他承包人所产生的纠纷。'
  }
]
