<template>
  <div class="law-judicature-container">
    <div class="law-title m-b-10" :id="titleId">
      <span class="law-title-text">{{ title }}</span>
      <span class="law-title-num">60</span>
      <el-popover placement="bottom-start" width="400" trigger="hover">
        <p>
          司法案件是结合案件诉讼流程，聚合16类诉讼相关数据，直观呈现案件立案、审理、裁判、执行的整体进程。当前案件列表已对系列案件（相同当事人、相同案由、相同审理进程、相同法院的案件）进行了折叠展示。
        </p>
        <svg-icon
          slot="reference"
          class="law-title-svg"
          icon-class="info-circle"
        />
      </el-popover>
    </div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="lawList"
      :columns="tableColumn"
      :isNeedPagination="true"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins, { lawList } from './column'
import { deepClone } from '@/utils/tools'
export default {
  name: 'JudicialCases',
  props: {
    title: {
      type: String,
      default: '司法案件'
    },
    titleId: {
      type: String,
      default: 'JudicialCases'
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  mounted() {
    this.entInfo = deepClone(this.$options.propsData.businessInfo)
    console.log('s')
  },
  data() {
    return {
      develop: true,
      lawList
    }
  }
}
</script>

<style lang="scss" scoped>
.law-judicature-container {
  margin-bottom: 30px;
  .law-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
    .law-title-text {
      margin-right: 3px;
    }
    .law-title-num {
      color: #ed7b2f;
      font-size: 16px;
      font-weight: bold;
      margin-right: 3px;
    }
    .law-title-svg {
      color: #d6d6d6;
      font-size: 16px;
      margin-top: 8px;
    }
  }

  .law-analyse {
    background-image: url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.53/images/background-754c6437.png);
    background-repeat: no-repeat;
    background-size: 153px 28px;
    border-radius: 4px;
    border: 1px solid #c4dff5;
    padding: 15px;
    margin-bottom: 20px;
    .law-analyse-title {
      img {
        width: 20px;
        height: 22px;
      }
      .law-develop {
        span,
        .law-svg {
          color: #128bed;
        }
        &:hover span,
        .law-svg {
          @include font_color('--color-primary');
        }
      }
    }
  }
}
</style>
