<template>
  <div class="law-Judgment-container">
    <div class="law-title m-b-10" :id="titleId">
      <span class="law-title-text">{{ title }}</span>
      <span class="law-title-num">{{ count }}</span>
    </div>
    <!--    tab-->
    <!--    <basic-tab-->
    <!--        ref="basicTab"-->
    <!--        :tabs-data="list"-->
    <!--        :current="current"-->
    <!--        @tabsChange="tabsChange"-->
    <!--    />-->
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableData"
      :columns="tableColumn"
      :isNeedPagination="true"
      :stripe="false"
    >
    </drive-table>
  </div>
</template>

<script>
// import BasicTab from '../components/BasicTab'
import ColumnMixins, { lawList } from './column'
import { deepClone } from '@/utils/tools'

export default {
  name: 'JudgmentDocuments',
  props: {
    title: {
      type: String,
      default: '裁判文书'
    },
    titleId: {
      type: String,
      default: 'JudgmentDocuments'
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  // components:{
  //   BasicTab
  // },
  mounted() {
    this.tableData = deepClone(this.$options.propsData.judgmentDocList)
  },
  data() {
    return {
      develop: true,
      lawList,
      tableData: []
    }
  }
}
</script>

<style lang="scss" scoped>
.law-Judgment-container {
  margin-bottom: 30px;
  .law-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
    .law-title-text {
      margin-right: 3px;
    }
    .law-title-num {
      color: #ed7b2f;
      font-size: 16px;
      font-weight: bold;
      margin-right: 3px;
    }
    .law-title-svg {
      color: #d6d6d6;
      font-size: 16px;
      margin-top: 8px;
    }
  }

  .law-analyse {
    background-image: url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.53/images/background-754c6437.png);
    background-repeat: no-repeat;
    background-size: 153px 28px;
    border-radius: 4px;
    border: 1px solid #c4dff5;
    padding: 15px;
    margin-bottom: 20px;
    .law-analyse-title {
      img {
        width: 20px;
        height: 22px;
      }
      .law-develop {
        span,
        .law-svg {
          color: #128bed;
        }
        &:hover span,
        .law-svg {
          @include font_color('--color-primary');
        }
      }
    }
  }
  .ntable {
    width: 100%;
    height: 43px;
    margin: 10px 0;
    font-size: 15px;
    text-align: center;
    padding: 12px 2px 12px 2px;
    background-color: #f2f8fe;
    background-image: url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.383/images/moer-04d863fa.png),
      url(//qcc-static.qichacha.com/qcc/pc-web/prod-23.03.383/images/moer-04d863fa.png);
    background-size: 189px 189px, 189px 189px;
    background-repeat: no-repeat;
    background-position: -5px -85px, 1005px -35px;

    .law-title-svg {
      color: #d6d6d6;
      font-size: 16px;
      margin-right: 3px !important;
    }
  }
}
</style>
