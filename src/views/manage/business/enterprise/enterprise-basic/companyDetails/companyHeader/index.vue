<template>
  <div ref="headerContainer" class="company-header-container">
    <div
      ref="headerWrapper"
      class="company-header-wrapper"
      :class="{ 'zoom-top': isZoom }"
    >
      <div class="header-logo" :class="{ disabled: !isInPark }">
        <img
          class="logo-img wh100"
          :src="
            objDetailHeader.imageUrl
              ? objDetailHeader.imageUrl
              : require('../../images/default-avatar.png')
          "
          alt=""
        />
        <div class="logo-tips" :class="isInPark ? 'primary' : 'info'">
          <span
            class="font-size-12 line-height-20 color-white"
            :class="'bg-' + isInPark ? 'primary' : 'info'"
          >
            <!--            {{ isInPark ? '在园' : '离园' }}-->
            {{ parkStatus }}
          </span>
          <div class="angle"></div>
        </div>
      </div>
      <div class="header-content">
        <!--      右侧按钮-->
        <div class="content-title">
          <span
            class="title"
            v-copy="`${noData(objEnter.enterpriseName)}`"
          ></span>
          <div class="title-btn">
            <!--            <span class="update-date m-r-10">-->
            <!--              <i class="el-icon-refresh"></i>-->
            <!--              更新企业数据-->
            <!--            </span>-->
            <el-dropdown  trigger="click">
              <el-button
                type="text"
                class="m-r-10"
                v-permission="[
                  ...routeButtonsPermission.ENTER_RELEVANCE,
                  ...routeButtonsPermission.ENTER_RENAMING,
                  ...routeButtonsPermission.RENEW_CONTACTS
                ]"
              >
                更多<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  v-permission="routeButtonsPermission.ENTER_RELEVANCE"
                >
                  <div
                    class="p-l-12 p-r-12"
                    @click="relation($route.query.id, objEnter.enterpriseName)"
                  >
                    {{ routeButtonsTitle.ENTER_RELEVANCE }}
                  </div>
                </el-dropdown-item>
                <el-dropdown-item
                  v-permission="routeButtonsPermission.ENTER_RENAMING"
                >
                  <div
                    class="p-l-12 p-r-12"
                    @click="renaming($route.query.id, objEnter.enterpriseName)"
                  >
                    {{ routeButtonsTitle.ENTER_RENAMING }}
                  </div>
                </el-dropdown-item>
<!--                <el-dropdown-item-->
<!--                  v-permission="routeButtonsPermission.RENEW_CONTACTS"-->
<!--                >-->
<!--                  <div-->
<!--                    @click="contacts($route.query.id, objEnter.enterpriseName)"-->
<!--                  >-->
<!--                    {{ routeButtonsTitle.RENEW_CONTACTS }}-->
<!--                  </div>-->
<!--                </el-dropdown-item>-->
              </el-dropdown-menu>
            </el-dropdown>
            <el-button
              @click="addRemarks($route.query.id, objEnter.enterpriseName)"
              type="info"
              size="mini"
              v-permission="routeButtonsPermission.ADD_REMARKS"
            >
              <svg-icon icon-class="edit-1" />
              <span>加备注</span>
            </el-button>
            <el-button
              @click="addTags($route.query.id, objEnter.enterpriseName)"
              type="primary"
              size="mini"
              v-permission="routeButtonsPermission.ADD_TAGS"
            >
              <svg-icon icon-class="discount-filled" />
              <span>{{ routeButtonsTitle.ADD_TAGS }}</span>
            </el-button>
          </div>
        </div>
        <!--        标签区域-->
        <div ref="isTagsParent" class="tags-list-parent">
          <div class="inline-block">
            <div ref="isTags" class="tags-list">
              <!--        曾用名-->
              <div
                v-if="
                  objDetailHeader.originalName &&
                  objDetailHeader.originalName.length > 0
                "
                class="pointer"
              >
                <el-popover
                  popper-class="item-popover"
                  placement="bottom"
                  trigger="hover"
                >
                  <div
                    class="name-item p-b-8 p-t-4"
                    v-for="(item, index) in objDetailHeader.originalName"
                    :key="index"
                  >
                    <div class="name line-height-20 color-text-regular">
                      {{ noData(item.name) }}
                    </div>
                    <div class="time line-height-20 color-info">
                      {{ parseTime(item.changeDate, '{y}-{m}-{d}') }}
                    </div>
                  </div>
                  <div slot="reference" class="item-tag former-name">
                    曾用名<i class="el-icon-arrow-down"></i>
                  </div>
                </el-popover>
              </div>
              <!--              系统标签-->
              <div
                :id="'system' + index"
                class="item-tag success"
                v-for="(item, index) in objDetailHeader.tagList"
                :key="item.id"
              >
                {{ noData(item.name) }}
                <svg-icon icon-class="caret-right-small" />
              </div>
              <span
                v-if="
                  customTagList &&
                  customTagList.length > 0 &&
                  objDetailHeader.tagList &&
                  objDetailHeader.tagList.length > 0
                "
                class="split-line m-r-8"
              >
                <i></i>
              </span>
              <!--              自定义标签-->
              <div
                :id="'tag' + index"
                class="item-tag primary"
                v-for="(item, index) in customTagList"
                :key="item.id"
              >
                {{ noData(item.label) }}
                <svg-icon icon-class="caret-right-small" />
              </div>
              <!--        更多标签-->
              <el-popover
                v-if="isMoreTags"
                placement="bottom"
                width="350"
                trigger="hover"
                @show="isTags = false"
                @hide="isTags = true"
              >
                <div
                  v-if="systemTagList.length > 0"
                  class="font-size-14 m-b-8 font-strong"
                >
                  系统标签
                </div>
                <div
                  v-if="systemTagList.length > 0"
                  class="flex color-info line-height-22"
                  style="flex-flow: wrap"
                >
                  <div
                    class="font-size-14 m-r-8 pointer"
                    v-for="item in systemTagList"
                    :key="item.id"
                  >
                    #{{ noData(item.name) }}
                  </div>
                </div>
                <div
                  v-if="filterTagList.length > 0"
                  class="font-size-14 m-b-8 m-t-10 font-strong"
                >
                  自定义标签
                </div>
                <div
                  v-if="filterTagList.length > 0"
                  class="flex color-info line-height-22"
                  style="flex-flow: wrap"
                >
                  <div
                    class="font-size-14 m-r-8 pointer"
                    v-for="item in filterTagList"
                    :key="item.id"
                  >
                    #{{ noData(item.label) }}
                  </div>
                </div>
                <el-button slot="reference" class="item-tag-more" type="text">
                  #更多标签
                  <svg-icon v-if="isTags" icon-class="caret-down-small" />
                  <svg-icon v-else icon-class="caret-up-small" />
                </el-button>
              </el-popover>
            </div>
          </div>
        </div>
        <!--      法人信息-->
        <div class="ent-information">
          <span class="info-label">法定代表人</span>
          <span class="p-l-8">{{ noData(objDetailHeader.operName) }}</span>
          <!--          <span class="color-primary m-l-12">TA有 13 家企业 ></span>-->
          <span class="info-label p-l-16">注册资本</span>
          <span class="color-warning p-l-8">{{
            noData(objDetailHeader.registCapi)
          }}</span>
          <span class="info-label p-l-16">实缴资本</span>
          <span class="color-success p-l-8">{{ noData(objTitle.recCap) }}</span>
          <span class="info-label p-l-16">统一社会信用代码</span>
          <span class="p-l-8">{{ noData(objEnter.creditCode) }}</span>
          <span class="info-label p-l-16">入园时间</span>
          <span class="p-l-8">{{
            parseTime(objTitle.enterDate, '{y}-{m}-{d}')
          }}</span>
          <span v-if="objEnter.leaveDate" class="info-label p-l-16"
            >离园时间</span
          >
          <span v-if="objEnter.leaveDate" class="p-l-8">{{
            parseTime(objEnter.leaveDate, '{y}-{m}-{d}')
          }}</span>
          <span class="info-label p-l-16">在园天数</span>
          <span class="p-l-8">{{ noData(objEnter.day) }}（天）</span>
        </div>
      </div>
      <!--    添加备注弹窗-->
      <remarks ref="remarks" @refresh="refresh" />
      <!--    添加标签弹窗-->
      <enterprise-tags ref="tags" @addTags="addTag" />
      <!--    关联企业弹窗-->
      <!-- 企业关联列表 -->
      <association ref="relation" @relation="refresh" />
      <!--    企业更名弹窗-->
      <renaming ref="renaming" @renaming="refresh" />
      <!--    更新联系人-->
      <contacts ref="contacts" @contact="refresh" />
    </div>
  </div>
</template>

<script>
import {
  getBusinessInfo,
  getEnterpriseEntEnterInfo,
  getSelectEnterpriseLabels
} from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'
import descriptorMixins from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/companyHeader/descriptor'
import Association from '@/views/manage/business/enterprise/enterprise-basic/enterpriseList/association'
import { noData } from '@/filter'
import { parseTime } from '@/utils/tools'
import {
  getEnterpriseInfo,
  getSelectRelation,
  getSelectRelationEntList,
  getTags
} from '@/views/manage/business/enterprise/enterprise-basic/enterpriseList/api/list'
import EnterpriseTags from '@/views/manage/business/enterprise/enterprise-basic/enterpriseList/enterpriseTags'
import Remarks from '@/views/manage/business/enterprise/enterprise-basic/enterpriseList/remarks'
import Renaming from '@/views/manage/business/enterprise/enterprise-basic/enterpriseList/renaming'
import Contacts from '@/views/manage/business/enterprise/enterprise-basic/enterpriseList/contacts'

export default {
  name: 'CompanyHeader',
  data() {
    return {
      creditCode: null,
      objDetailHeader: {},
      isInPark: true,
      isTags: true,
      noData,
      parseTime,
      fromData: {},
      showTags: true,
      isZoom: false, // 滚动缩放
      objEnter: {},
      customTagList: [], // 自定义标签
      isMoreTags: true, // 更多标签
      nodeAll: 0,
      objTitle: {},
      tagIdx: null,
      parkStatus: '',
      filterTagList: [],
      systemTagList: []
    }
  },
  components: { Association, EnterpriseTags, Remarks, Renaming, Contacts },
  mixins: [descriptorMixins],
  inject: ['companyDetailsRoot'],
  mounted() {
    this.getEnterpriseEntEnterInfo()
    addEventListener('scroll', this.handleScroll, true)
    addEventListener('resize', this.getWidthHandle, true)
  },
  beforeDestroy() {
    removeEventListener('scroll', this.handleScroll, true)
    removeEventListener('resize', this.getWidthHandle, true)
  },
  watch: {
    isZoom(val) {
      const appMain = document.querySelector(`#companyDetailsMain`)
      this.$refs.headerWrapper.style.width =
        this.$refs.headerContainer.offsetWidth + 'px'
      if (val) {
        appMain.style.paddingTop = '0'
      } else {
        appMain.style.paddingTop = '0'
      }
    },
    'fromTags.addTags': {
      deep: true,
      handler(val) {
        if (val === 2) {
          this.formConfigureTags.descriptors.tagSelect.hidden = true
          this.formConfigureTags.descriptors.tagInput.hidden = false
        } else {
          this.formConfigureTags.descriptors.tagSelect.hidden = false
          this.formConfigureTags.descriptors.tagInput.hidden = true
        }
      }
    }
  },
  methods: {
    getWidthHandle() {
      this.$refs.headerWrapper.style.width =
        this.$refs.headerContainer.offsetWidth + 'px'
    },
    async getEnterpriseEntEnterInfo() {
      let id = this.$route.query.id
      const res = await getEnterpriseEntEnterInfo({ id })
      this.objTitle = res
    },
    handleScroll() {
      const scrollMain = document.querySelector('#companyDetailsMain')
      this.isZoom = !!scrollMain.scrollTop
      if (this.isZoom) {
        this.$refs.headerContainer.style.height =
          this.$refs.headerWrapper.offsetHeight + 'px'
      } else {
        this.$refs.headerContainer.style.height = 'auto'
      }
      this.companyDetailsRoot && this.companyDetailsRoot.getHeaderHeight()
    },
    // 更新联系人
    async contacts(id, val) {
      this.$refs.contacts.visible = true
      this.$refs.contacts.formData.entId = id
      let res = await getEnterpriseInfo({ id })
      this.$refs.contacts.formData.legalPerson = res.legalPerson
      this.$refs.contacts.formData.legalPersonPhone = res.legalPersonPhone
      this.$refs.contacts.formData.contact = res.contact
      this.$refs.contacts.formData.phone = res.phone
      this.$refs.contacts.formData.manager = res.manager
      this.$refs.contacts.formData.managerPhone = res.managerPhone
      this.$refs.contacts.name = val
    },
    // 企业更名
    async renaming(id, val) {
      this.$refs.renaming.visible = true
      this.$refs.renaming.entId = id
      let res = await getEnterpriseInfo({ id })
      this.getInfo = res
      this.$refs.renaming.formData.code = res.creditCode
      this.$refs.renaming.formData.name = res.enterpriseName
      this.$refs.renaming.name = val
    },
    // 企业标签
    async addTags(id, val) {
      this.$refs.tags.addTags = true
      let res = await getTags({ id })
      this.$refs.tags.dataList1 = res
      this.$refs.tags.entId = id
      this.$refs.tags.name = val
    },
    addTag() {
      this.changeHandle()
    },
    // 企业备注
    addRemarks(id, val) {
      this.$refs.remarks.addRemarks = true
      this.$refs.remarks.entId = id
      this.$refs.remarks.name = val
      this.$nextTick(() => {
        this.$refs.remarks.formData = {
          radio: 1,
          desc: ''
        }
        this.$refs.remarks.initData()
      })
    },
    refresh() {
      this.$emit('getNewParkInformation')
    },
    // 企业关联
    async relation(id, val) {
      const data = await getSelectRelation({ id })
      this.$refs.relation.dataList = data
      this.$refs.relation.visible = true
      this.$refs.relation.entId = id
      let res = await getSelectRelationEntList({ id })
      this.$refs.relation.list = res
      this.$refs.relation.name = val
    },
    initData(info) {
      this.objEnter = info
      if (this.objEnter.enterStatus === 0) {
        this.parkStatus = '待入园'
        this.isInPark = false
      }
      if (this.objEnter.enterStatus === 2) {
        this.parkStatus = '离园'
        this.isInPark = false
      }
      if (this.objEnter.enterStatus === 1) {
        this.parkStatus = '在园'
        this.isInPark = true
      }
      this.creditCode = info.creditCode
      this.changeHandle()
    },
    async changeHandle() {
      this.nodeAll = 0
      let id = this.$route.query.id
      const [res1, res2] = await Promise.all([
        await getBusinessInfo(this.creditCode),
        await getSelectEnterpriseLabels({ id })
      ])
      // this.customTagList = res2.filter(item => {
      //   if (item.type === 2) {
      //     return item
      //   }
      // })
      this.customTagList = res2
      if (res1.tagList && res1.tagList.length > 0) {
        res1.tagList.forEach((item, index) => {
          if (item.name === '曾用名') {
            this.tagIdx = index
          }
        })
        res1.tagList.splice(this.tagIdx, 1)
      }
      this.objDetailHeader = res1
      this.$nextTick(() => {
        this.changeWidth()
      })
    },
    filterTags() {
      this.systemTagList = []
      this.filterTagList = []
      this.objDetailHeader.tagList.forEach((item, index) => {
        let systemId = document.getElementById(`system${index}`)
        if (systemId.style.display !== '') {
          this.systemTagList.push(item)
        }
      })
      this.customTagList.forEach((item, index) => {
        let tagId = document.getElementById(`tag${index}`)
        if (tagId.style.display === 'none') {
          this.filterTagList.push(item)
        }
      })
      if (this.filterTagList && this.filterTagList.length > 0) {
        this.isMoreTags = true
      } else {
        this.isMoreTags = false
      }
    },
    changeWidth() {
      // let isTags = this.$refs.isTags.offsetWidth
      let isTagsParent = this.$refs.isTagsParent.offsetWidth
      this.nodeAll = 0
      // if (isTags >= isTagsParent) {
      //   this.isMoreTags = true
      // } else {
      //   this.isMoreTags = false
      // }
      let nodeList = this.$refs.isTags.childNodes
      this.filterTagList = []
      nodeList.forEach(item => {
        if (item.offsetWidth !== undefined) {
          this.nodeAll += item.offsetWidth
        }
        if (this.nodeAll >= isTagsParent) {
          if (item.style) item.style.display = 'none'
        } else {
          if (item.style) item.style.display = ''
        }
      })
      if (nodeList[nodeList.length - 1].style)
        nodeList[nodeList.length - 1].style.display = ''
      this.filterTags()
    }
  }
}
</script>

<style scoped lang="scss">
.company-header-container {
  background: #fff;
  margin-bottom: 8px;
  border-radius: 3px;
  .company-header-wrapper {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #fff;
    border-bottom: 1px solid #f0f2f5;
    .header-logo {
      width: 90px;
      height: 90px;
      position: relative;
      transition: all 0.3s ease;
      flex-shrink: 0;
      .logo-tips {
        position: absolute;
        display: inline-block;
        left: -4px;
        top: 4px;
        border-top-right-radius: 3px;
        border-bottom-right-radius: 3px;
        span {
          position: relative;
          z-index: 2;
          padding: 4px 8px;
        }
        .angle {
          position: absolute;
          z-index: 1;
          width: 0;
          height: 0;
          left: -4px;
          top: 17px;
          border-top: 4px solid transparent !important;
          border-bottom: 4px solid transparent !important;
          border-left: 4px solid transparent !important;
          border-right-width: 4px;
          border-right-style: solid;
        }
        &.primary {
          @include background-color(--color-primary);
          .angle {
            @include border_color_mix(--color-primary, #000000, 1%);
          }
        }
        &.info {
          @include background-color(--color-info);
          .angle {
            @include border_color_mix(--color-info, #000000, 20%);
          }
        }
      }
      &.disabled {
        .logo-img {
          filter: grayscale(100%);
        }
        .logo-tips {
          background: #eee;
          color: #555;
        }
      }
    }
    .header-content {
      width: 100%;
      flex: 1;
      margin-left: 16px;
      .content-title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .title {
          font-size: 18px;
          line-height: 22px;
          float: left;
          max-width: 570px;
          cursor: pointer;
          font-weight: bold;
        }
        .title-btn {
          display: flex;
          align-items: center;
          .update-date {
            font-size: 12px;
            font-weight: 350;
            color: rgba(0, 0, 0, 0.6);
            line-height: 20px;
            cursor: pointer;
            .el-icon-refresh {
              @include font_color(--color-primary);
            }
          }
        }
      }
      .tags-list-parent {
        width: calc(100% - 345px);
        font-size: 0;
      }
      .tags-list {
        display: flex;
        //flex-flow: wrap;
        //text-overflow: ellipsis;
        white-space: nowrap;
        margin-top: 8px;
        transition: all 0.3s ease;
        overflow: hidden;
        .split-line {
          padding-top: 3px;
          i {
            display: inline-block;
            width: 1px;
            height: 14px;
            @include background_color(--border-color-base);
          }
        }
        .item-tag {
          border-radius: 2px;
          padding: 0 6px;
          font-size: 12px;
          line-height: 20px;
          margin-bottom: 8px;
          margin-right: 8px;
          height: 20px;
          background: rgba(30, 168, 48, 0.09);
          color: #1ea830;
          &.former-name {
            color: #7656b5;
            background: rgba(118, 86, 181, 0.09);
            border: none;
          }
        }
        .danger {
          background: #ffecec;
          color: #ff6060;
        }
        .primary {
          background: #e6efff;
          color: #367dff;
        }

        .success {
          @include background_color_mix(--color-success, #ffffff, 90%);
          @include font_color(--color-success);
        }

        .item-tag-more {
          border-radius: 2px;
          padding: 0 6px;
          font-size: 12px;
          line-height: 20px;
          margin-bottom: 8px;
          margin-right: 8px;
          height: 20px;
          color: #367dff;
          &:hover {
            background: #e6efff;
          }
        }
      }
      .ent-information {
        line-height: 22px;
        font-size: 14px;
        .info-label {
          font-weight: 350;
          color: rgba(0, 0, 0, 0.4);
        }
      }
    }
    &.zoom-top {
      padding: 10px 24px;
      position: fixed;
      top: 96px;
      z-index: 1003;
      .header-logo {
        width: 58px;
        height: 58px;
        transition: all 0.3s ease;
      }
      .header-content .title {
        font-size: 16px;
      }
      .tags-list,
      .tags-list-parent {
        height: 0;
        margin-top: 0;
        transition: all 0.3s ease;
      }
      .ent-information {
        font-size: 12px;
      }
    }
  }
}
.item-more-tag {
  display: flex;
  flex: 1;
  flex-flow: wrap;
}
.item-tags-more {
  height: 24px;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 12px;
  line-height: 20px;
  margin-top: 8px;
  margin-right: 8px;
  background: #fdf2ea;
  border-color: #fbe5d5;
  color: #ed7b2f;
}
</style>
