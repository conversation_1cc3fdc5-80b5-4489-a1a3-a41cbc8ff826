<template>
  <!--  调房申请-->
  <div class="transfer-apply-container">
    <div :id="titleId" class="transfer-title m-b-10">{{ title }}</div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="transferList"
      :columns="tableColumn"
      :isNeedPagination="true"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'TransferApply',
  props: {
    title: {
      type: String,
      default: '调房申请'
    },
    titleId: {
      type: String,
      default: 'TransferApply'
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      transferList: []
    }
  },
  mounted() {
    this.transferList = this.$options.propsData.key1.applyEntList
  },
  methods: {
    toReplaceDetail(row) {
      let routeData = this.$options.propsData.router.resolve({
        path: '/rentOut/replace/replaceDetail',
        query: {
          id: row.id,
          orderId: row.orderId
        }
      })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.transfer-apply-container {
  margin-bottom: 16px;
  .transfer-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}

::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
