<template>
  <!--  员工信息-->
  <div class="employee-info-container">
    <div :id="titleId" class="employee-title m-b-10">{{ title }}</div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :api-fn="getEntPage"
      :columns="tableColumn"
      :isNeedPagination="true"
      :extral-querys="extralQuerys"
      :scroll-top="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'
import { getEntPage } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'

export default {
  name: 'EmployeeInfo',
  props: {
    title: {
      type: String,
      default: '员工信息'
    },
    titleId: {
      type: String,
      default: 'EmployeeInfo'
    },
    id: {
      type: String,
      default: '-1'
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      extralQuerys: {
        entId: this.id
      },
      getEntPage
    }
  }
}
</script>

<style lang="scss" scoped>
.employee-info-container {
  margin-bottom: 16px;
  .employee-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}

::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
