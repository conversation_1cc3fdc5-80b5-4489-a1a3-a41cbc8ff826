<template>
  <!--  入驻信息-->
  <div class="enter-information-container">
    <div :id="titleId" class="enter-title m-b-10">{{ title }}</div>
    <!--    描述列表-->
    <div class="enter-title m-b-10 font-size-14" style="font-weight: 400">
      企业概览
    </div>
    <table style="width: 100%" border="1" Cellspacing="0" Cellpadding="0">
      <tr>
        <th>入驻时间</th>
        <td>{{ parseTime(objTable.enterDate, '{y}-{m}-{d}') }}</td>
        <th>租赁面积（m²）</th>
        <td>{{ noData(objTable.area) }}</td>
      </tr>
      <tr>
        <th>楼栋房号</th>
        <td colspan="3">{{ noData(objTable.buildingRoomNo) }}</td>
      </tr>
      <tr>
        <th>办公地址</th>
        <td>{{ noData(objTable.address) }}</td>
        <th>工商地址</th>
        <td>{{ noData(objTable.businessAddress) }}</td>
      </tr>
      <tr>
        <th>合同数（份）</th>
        <td>{{ noData(objTable.contractCount) }}</td>
        <th>缴费进度</th>
        <td v-if="objTable.contractCount === 0">无需缴费</td>
        <td v-else>
          {{ getPayPercent(objTable.payStatus) }}（{{
            noData(objTable.payPercent)
          }}%）
        </td>
      </tr>
      <tr>
        <th>员工数（人）</th>
        <td>
          {{ noData(objTable.plateEmployeeCount) }}（平台内）|
          {{ noData(objTable.personScope) }}（企业规模）
        </td>
        <th>认证车辆数（辆）</th>
        <td>
          {{ noData(objTable.carCount) }} （配额{{
            noData(objTable.carQuota)
          }}）
        </td>
      </tr>
      <tr>
        <th>常用联系人</th>
        <td>
          {{ noData(objTable.contacts) }}（{{
            noData(objTable.contactsPhone)
          }}）
        </td>
        <th>总经理或项目负责人</th>
        <td>
          {{ noData(objTable.manager) }}（{{ noData(objTable.managerPhone) }}）
        </td>
      </tr>
      <tr>
        <th>法定代表人</th>
        <td :colspan="3">
          {{ noData(objTable.legalPerson) }}（{{
            noData(objTable.legalPersonPhone)
          }}）
        </td>
      </tr>
    </table>
    <!--    企业备注-->
    <enterprise-remark :remark-list="remarkList" :id="id" />
    <!--    关联企业-->
    <affiliates :ent-list="entList" :id="id" @toEnterDetail="toEnterDetail" />
  </div>
</template>

<script>
import Affiliates from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/ParkInfo/Affiliates'
import EnterpriseRemark from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/ParkInfo/EnterpriseRemark'
import { noData } from '@/filter'
import { parseTime } from '@/utils/tools'
import { getPayPercent } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/ParkInfo/EnterInformation/status'

export default {
  name: 'EnterInformation',
  props: {
    title: {
      type: String,
      default: '入驻信息'
    },
    titleId: {
      type: String,
      default: 'EnterInformation'
    },
    id: {
      type: String,
      default: '-1'
    }
  },
  components: {
    Affiliates,
    EnterpriseRemark
  },
  data() {
    return {
      objTable: {},
      noData,
      parseTime,
      getPayPercent,
      remarkList: [],
      entList: []
    }
  },
  mounted() {
    this.objTable = this.$options.propsData.key2
    this.remarkList = this.$options.propsData.key1.entRemarkList.list
    this.entList = this.$options.propsData.key1.entRelationInfoList.list
  },
  methods: {
    toEnterDetail(row) {
      let routeData = this.$options.propsData.router.resolve({
        path: '/business/enterpriseDetails',
        query: {
          id: row.entId
        }
      })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.enter-information-container {
  margin-bottom: 16px;
  .enter-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
  table {
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    font-size: 14px;
  }

  th {
    width: 154px;
    @include background_color_mix(--color-primary, #ffffff, 96%);
    padding: 10px;
    font-weight: 400;
    text-align: left;
  }

  td {
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    word-break: break-all;
    padding: 10px 10px 11px 10px;
    line-height: 1.6em;
    word-wrap: break-word;
  }
}
</style>
