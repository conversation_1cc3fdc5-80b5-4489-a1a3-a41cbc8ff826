<template>
  <!--  入园信息-->
  <div class="park-record-container">
    <div :id="titleId" class="employee-title m-b-10">{{ title }}</div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="recordList"
      :columns="tableColumn"
      :isNeedPagination="true"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins, { recordList } from './column'

export default {
  name: 'ParkEntryRecords',
  props: {
    title: {
      type: String,
      default: '入园信息'
    },
    titleId: {
      type: String,
      default: 'ParkEntryRecords'
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      recordList
    }
  },
  mounted() {
    this.recordList = this.$options.propsData.key1.projectInfoList
  },
  methods: {
    // 跳转
    toParkRecord(row) {
      let routeData = this.$options.propsData.router.resolve({
        path: '/enterPark/intentCustomerDetail',
        query: {
          id: row.id
        }
      })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.park-record-container {
  margin-bottom: 16px;
  .employee-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}

::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
