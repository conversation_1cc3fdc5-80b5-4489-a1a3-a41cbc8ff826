<template>
  <div class="stock bg-white">
    <div class="m-b-10 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <span class="color-p">{{ count }}</span>
    </div>
    <drive-table
      ref="drive-table"
      :table-data="adminLicenseList"
      :columns="tableColumn"
      :scrollTop="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'AdministrativeLicensing',
  props: {
    title: {
      type: String,
      default: '行政许可'
    },
    titleId: {
      type: String,
      default: 'AdministrativeLicensing'
    },
    creditCode: {
      type: String,
      default: ''
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      adminLicenseList: []
    }
  },
  mounted() {
    const propsData = this.$options.propsData
    this.adminLicenseList = propsData.adminLicenseList || []
  }
}
</script>

<style lang="scss" scoped>
.stock {
  margin-bottom: 40px;
}
::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
