<template>
  <div class="stock bg-white">
    <div class="m-b-10 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <span class="color-p">{{ count }}</span>
    </div>
    <drive-table
      ref="drive-table"
      :table-data="creditorRightsList"
      :columns="tableColumn"
      :scrollTop="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'BondInfo',
  props: {
    title: {
      type: String,
      default: '债券信息'
    },
    titleId: {
      type: String,
      default: 'BondInfo'
    },
    creditCode: {
      type: String,
      default: ''
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      creditorRightsList: []
    }
  },
  mounted() {
    const propsData = this.$options.propsData
    this.creditorRightsList = propsData.creditorRightsList || []
  }
}
</script>

<style lang="scss" scoped>
.stock {
  margin-bottom: 40px;
}
::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
