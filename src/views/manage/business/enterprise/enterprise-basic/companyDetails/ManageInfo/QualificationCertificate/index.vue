<template>
  <div class="stock bg-white">
    <div class="m-b-20 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <span class="color-p">{{ count }}</span>
    </div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :isNeedPagination="true"
      :extral-querys="extralQuerys"
      :api-fn="certificationList"
      :scrollTop="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'
import { certificationList } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'
import dayjs from 'dayjs'

export default {
  name: 'QualificationCertificate',
  props: {
    title: {
      type: String,
      default: '资质证书'
    },
    titleId: {
      type: String,
      default: 'QualificationCertificate'
    },
    creditCode: {
      type: String,
      default: ''
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      certificationList,
      extralQuerys: {
        creditCode: this.creditCode
      }
    }
  },
  methods: {
    stateHandle(endDate) {
      return dayjs(new Date()) < dayjs(endDate)
    },
    serialHandle(index) {
      const pageSize = this.$refs['drive-table'].PAGE_SIZE
      const pageNumber = this.$refs['drive-table'].PAGE_NUMBER
      return pageSize * (pageNumber - 1) + index + 1
    }
  }
}
</script>

<style lang="scss" scoped>
.stock {
  margin-bottom: 40px;
}
::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
