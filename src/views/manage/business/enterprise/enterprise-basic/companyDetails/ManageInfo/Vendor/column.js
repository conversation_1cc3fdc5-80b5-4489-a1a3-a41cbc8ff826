export default {
  data() {
    return {
      tableColumn: [
        {
          label: '序号',
          width: 60,
          prop: 'serialNumber',
          align: 'center',
          render: (h, scope) => {
            return <div>{scope.$index + 1}</div>
          }
        },
        {
          label: '供应商',
          prop: 'serialNumber',
          align: 'center',
          render: (h, scope) => {
            return (
              <div class="flex align-items-center">
                <img width="40px" height="40px" src={scope.row.imageUrl} />
                <span class="m-l-10">{scope.row.name}</span>
              </div>
            )
          }
        },
        {
          label: '采购占比',
          prop: 'purchasePercent',
          align: 'center'
        },
        {
          label: '采购金额(万元)',
          prop: 'purchaseAmount',
          align: 'center'
        },
        {
          label: '数据来源',
          prop: 'source',
          align: 'center'
        },
        {
          label: '关联关系',
          prop: 'relationship'
        }
      ]
    }
  }
}
