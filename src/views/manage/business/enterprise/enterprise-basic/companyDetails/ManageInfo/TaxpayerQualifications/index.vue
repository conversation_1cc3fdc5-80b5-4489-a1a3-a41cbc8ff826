<template>
  <div class="stock bg-white">
    <div class="m-b-10 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <span class="color-p">{{ count }}</span>
    </div>
    <drive-table
      ref="drive-table"
      :table-data="generalTaxpayerList"
      :columns="tableColumn"
      :scrollTop="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'TaxpayerQualifications',
  props: {
    title: {
      type: String,
      default: '纳税人资质'
    },
    titleId: {
      type: String,
      default: 'TaxpayerQualifications'
    },
    creditCode: {
      type: String,
      default: ''
    },
    count: {
      type: String,
      default: ''
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      generalTaxpayerList: []
    }
  },
  mounted() {
    const propsData = this.$options.propsData
    this.generalTaxpayerList = propsData.generalTaxpayerList || []
  }
}
</script>

<style lang="scss" scoped>
.stock {
  margin-bottom: 40px;
}
::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
