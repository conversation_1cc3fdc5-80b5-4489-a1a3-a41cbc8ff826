<template>
  <!--  经营异常-->
  <div class="business-abnormal-container">
    <div :id="titleId" class="abnormal-title m-b-10">{{ title }}</div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableList"
      :columns="tableColumn"
      :isNeedPagination="true"
      :extral-querys="extralQuerys"
      :scroll-top="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'BusinessAbnormal',
  mixins: [ColumnMixins],
  props: {
    title: {
      type: String,
      default: '经营异常'
    },
    titleId: {
      type: String,
      default: 'BusinessAbnormal'
    }
  },
  data() {
    return {
      tableList: [
        {
          inclusionDate: '2021-11-30',
          mechanism: '合肥市经济开发区分局芙蓉市场监督管理所',
          reason: '通过登记的住所或者经营场所无法联系的'
        }
      ],
      extralQuerys: {}
    }
  }
}
</script>

<style lang="scss" scoped>
.business-abnormal-container {
  margin-bottom: 16px;

  .abnormal-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}

::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
