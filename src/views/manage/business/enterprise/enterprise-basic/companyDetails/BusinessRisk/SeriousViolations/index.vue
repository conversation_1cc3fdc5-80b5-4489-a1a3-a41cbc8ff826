<template>
  <!--  严重违法-->
  <div class="serious-violations-container">
    <div :id="titleId" class="serious-title m-b-10">{{ title }}</div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableList"
      :columns="tableColumn"
      :isNeedPagination="true"
      :extral-querys="extralQuerys"
      :scroll-top="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'SeriousViolations',
  mixins: [ColumnMixins],
  props: {
    title: {
      type: String,
      default: '严重违法'
    },
    titleId: {
      type: String,
      default: 'SeriousViolations'
    }
  },
  data() {
    return {
      tableList: [
        {
          inclusionDate: '2021-11-30',
          mechanism: '佛山市南海区市场监督管理局',
          reason:
            '相关企业被列入经营异常名录届满3年仍未履行相关义务，根据《企业信息公示暂行条例》第十七条第二款、《严重违法失信企业名单管理暂行办》'
        }
      ],
      extralQuerys: {}
    }
  }
}
</script>

<style lang="scss" scoped>
.serious-violations-container {
  margin-bottom: 16px;

  .serious-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}

::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
