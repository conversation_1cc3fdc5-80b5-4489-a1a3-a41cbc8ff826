<template>
  <!--  行政处罚-->
  <div class="administrative-penalties-container">
    <div :id="titleId" class="penalties-title m-b-10">{{ title }}</div>
    <!--    <div class="item-main-top flex align-items-center justify-content-center">-->
    <!--      <el-popover-->
    <!--        placement="bottom"-->
    <!--        trigger="hover"-->
    <!--        width="500"-->
    <!--        content="罚款总金额是企查查基于大数据和人工智能深度分析得出的处罚总金额（仅计算罚款金额，不包含没收金额、补缴金额等其他金额），仅供参考，并不代表企查查任何明示、暗示之观点或保证。"-->
    <!--      >-->
    <!--        <svg-icon-->
    <!--          slot="reference"-->
    <!--          class="color-info pointer"-->
    <!--          icon-class="info-circle"-->
    <!--        />-->
    <!--      </el-popover>-->
    <!--      <span class="m-l-8">罚款总金额：</span>-->
    <!--      <span class="color-danger m-r-8">182.24</span>-->
    <!--      <span class="color-info">万元</span>-->
    <!--    </div>-->
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableList"
      :columns="tableColumn"
      :isNeedPagination="true"
      :extral-querys="extralQuerys"
      :scroll-top="false"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column'

export default {
  name: 'AdministrativePenalties',
  mixins: [ColumnMixins],
  props: {
    title: {
      type: String,
      default: '行政处罚'
    },
    titleId: {
      type: String,
      default: 'AdministrativePenalties'
    }
  },
  data() {
    return {
      tableList: [],
      extralQuerys: {}
    }
  }
}
</script>

<style lang="scss" scoped>
.administrative-penalties-container {
  margin-bottom: 16px;

  .penalties-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }

  .item-main-top {
    padding: 12px 2px;
    font-size: 14px;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    @include background_color_mix(--color-primary, #ffffff, 96%);
    margin-bottom: 16px;
  }
}

::v-deep {
  .el-pagination {
    border: none;
  }
}
</style>
