export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'targetMan',
          label: '质押人'
        },
        {
          prop: 'enterName',
          label: '质押人参股企业',
          width: 250,
          renderHeader: () => {
            return (
              <div class={'flex align-items-center'}>
                <span class={'m-r-4'}>质押人参股企业</span>
                <el-popover
                  placement="bottom"
                  trigger="hover"
                  content="即股权被质押企业"
                >
                  <svg-icon slot="reference" icon-class="info-circle" />
                </el-popover>
              </div>
            )
          }
        },
        {
          prop: 'pawnee',
          label: '质押权人'
        },
        {
          prop: 'sharesAll',
          label: '质押股份总数（股）'
        },
        {
          prop: 'sharesMarket',
          label: '质押股份市值（元）'
        },
        {
          prop: 'status',
          label: '状态'
        },
        {
          prop: 'announcementDate',
          label: '公告日期'
        },
        {
          prop: 'content',
          label: '内容',
          width: 80,
          render: () => {
            return (
              <el-button
                type={'text'}
                onClick={() => {
                  this.openHandler()
                }}
              >
                详情
              </el-button>
            )
          }
        }
      ]
    }
  }
}
