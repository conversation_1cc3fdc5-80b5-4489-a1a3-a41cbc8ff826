<template>
  <div
    ref="tabNavContainer"
    class="tab-nav-container"
    @mouseleave="showHandle(false)"
  >
    <div ref="tabNavWrapper" class="tab-nav-wrapper w100">
      <div class="tab-nav-content w100">
        <div class="nav-title flex">
          <div class="nav-wrapper flex">
            <div
              class="nav-item"
              v-for="item in navList"
              :key="item.componentName"
              @mouseover="mouseoverHandle"
            >
              <div
                class="item-title"
                :class="{ active: currentCom === item.componentName }"
                @click="tabInfoHandle(item)"
              >
                {{ item.label }}
                <span class="title-count">{{ item.count }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-body" ref="scrollWrapper">
          <div class="nav-wrapper flex">
            <div
              class="nav-item"
              v-for="item in navList"
              :key="item.componentName"
            >
              <div
                class="item-container"
                v-for="val in item.children"
                :key="val.componentName"
              >
                <el-link
                  :underline="false"
                  class="item-wrapper"
                  :disabled="val.count === 0"
                  @click="clickHandle(item, val)"
                >
                  {{ val.label }}
                  <span
                    class="primary-count"
                    :class="{ 'danger-count': false }"
                    >{{ val.count }}</span
                  >
                </el-link>
              </div>
            </div>
          </div>
          <!--自定义滚动条-->
          <div class="custom-vertical-scrollbar" ref="vertical">
            <div class="custom-vertical-indicator"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { navList } from './navList'
import BScroll from 'better-scroll'
import { scrollToHandle } from './scrollTo'
import { deepClone } from '@/utils/tools'

export default {
  name: 'TabNav',
  data() {
    return {
      navList: [],
      needFixed: false,
      BScroll: null,
      currentCom: navList[0].componentName,
      countInfo: {}
    }
  },
  inject: ['companyDetailsRoot', 'CompanyTabNav'],
  watch: {
    needFixed(val) {
      if (val) {
        this.$nextTick(() => {
          this.BScrollInit()
        })
      } else {
        this.BScrollDestroy()
      }
      this.CompanyTabNav.$refs.sideNav.isShow = val
    }
  },
  mounted() {
    addEventListener('scroll', this.handleScroll, true)
  },
  beforeDestroy() {
    removeEventListener('scroll', this.handleScroll, true)
    this.BScrollDestroy()
  },
  methods: {
    initData(info = {}) {
      this.countInfo = info
      const list = deepClone(navList)
      this.formatterHandle(list)
      this.navList = list
      let idx = this.$route.query.isWay
      if (this.$route.query.isWay) {
        this.tabInfoHandle(list[idx])
      } else {
        this.tabInfoHandle(list[0])
      }
    },
    formatterHandle(list) {
      list.forEach(item => {
        item.count = this.countInfo[item.serveKey]
        if (Array.isArray(item.children) && item.children.length) {
          this.formatterHandle(item.children)
        }
      })
    },
    // 列表dom隐藏
    showHandle(show) {
      const scrollWrapper = this.$refs.scrollWrapper
      scrollWrapper.style.display = show ? 'block' : 'none'
    },
    // 点击跳转
    async clickHandle(parentRow, row) {
      if (this.currentCom === parentRow.componentName)
        return await this.domHandle(row)
      await this.tabInfoHandle(parentRow)
      setTimeout(async () => {
        await this.domHandle(row)
      }, 1000)
    },
    // 页面滚动
    async domHandle(row) {
      await this.$nextTick()
      await scrollToHandle(row)
      this.showHandle(false)
    },
    // 切換
    async tabInfoHandle(row) {
      return new Promise(resolve => {
        this.currentCom = row.componentName
        this.companyDetailsRoot.showComponent(row, () => {
          this.CompanyTabNav.$refs.subNav.init(row)
          this.CompanyTabNav.$refs.sideNav.init(row)
          resolve()
        })
      })
    },
    // 鼠标移入标题
    mouseoverHandle() {
      this.showHandle(true)
      this.$nextTick(() => {
        this.BScrollInit()
      })
    },
    // betterScroll销毁
    BScrollDestroy() {
      if (this.BScroll) {
        this.BScroll.destroy()
        this.BScroll = null
      }
    },
    // betterScroll初始化
    BScrollInit() {
      this.BScrollDestroy()
      this.BScroll = new BScroll(this.$refs.scrollWrapper, {
        probeType: 3,
        mouseWheel: true, // 滚轮滚动
        scrollbar: {
          customElements: [this.$refs.vertical], // 创建自定义滚动条
          fade: false // 关闭滚动条
        }
      })
    },
    // 滚动监听
    handleScroll() {
      const top = this.$refs.tabNavContainer.getBoundingClientRect().top
      const headerHeight = this.companyDetailsRoot.headerHeight
      const space = 56 // 顶部菜单+padding+间距
      const suckeringHeight = headerHeight + space
      this.needFixed = top < suckeringHeight
      const tabNavContainer = this.$refs.tabNavContainer
      const tabNavWrapper = this.$refs.tabNavWrapper
      if (this.needFixed) {
        tabNavWrapper.style.width = tabNavContainer.offsetWidth + 'px'
        tabNavWrapper.style.position = 'fixed'
        tabNavWrapper.style.top = `${suckeringHeight}px`
        tabNavContainer.style.zIndex = '1001'
      } else {
        tabNavWrapper.style.width = '100%'
        tabNavWrapper.style.position = 'initial'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tab-nav-container {
  width: 100%;
  height: 52px;
  position: relative;
  .tab-nav-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    .tab-nav-content {
      position: relative;
    }
    .nav-title {
      width: 100%;
      background: #fff;
      border-bottom: 1px solid #eee;
    }
    .nav-wrapper {
      flex: 1;
      background: #f7f9fb;
      padding: 4px;
      display: flex;
    }
    .nav-item {
      max-width: 197px;
      margin-bottom: -1px;
      flex: 1;
      color: #333;
      .item-title {
        line-height: 44px;
        text-align: center;
        cursor: pointer;
        &:hover {
          @include font_color(--color-primary);
        }
        &.active {
          @include font_color(--color-primary);
          font-weight: bold;
          background: #fff;
          border-radius: 3px;
        }
        .title-count {
          font-size: 12px;
          color: #999;
          font-weight: normal;
        }
      }
      .item-container {
        .item-wrapper {
          width: 100%;
          padding: 10px 10px 10px 15px;
          font-size: 13px;
          position: relative;
          display: flex;
          justify-content: start;
          .primary-count {
            @include font_color('--color-primary');
          }
          .danger-count {
            @include font_color('--color-danger');
          }
          &.is-disabled {
            .primary-count,
            .danger-count {
              @include font_color(--color-text-placeholder);
            }
          }
        }
      }
    }
    .nav-body {
      display: none;
      overflow: hidden;
      background: #fff;
      box-shadow: 0 2px 2px 0 rgb(140 140 140 / 0.3);
      position: absolute;
      top: 52px;
      width: 100%;
      z-index: 1001;
      max-height: calc(100vh - 204px);
      .nav-item {
        height: auto;
        width: 197px;
        padding: 5px 0 5px 50px;
      }
    }
  }
  .custom-vertical-scrollbar {
    width: 4px;
    height: 100%;
    border-radius: 2px;
    background: transparent;
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%) translateZ(0);
    display: block;
    visibility: hidden;
    .custom-vertical-indicator {
      width: 100%;
      height: 120px;
      background: rgba(0, 0, 0, 0.26);
      border-radius: 2px;
    }
  }
  &:hover {
    .nav-body {
      display: block;
      border-top: 1px solid #eee;
      .custom-vertical-scrollbar {
        visibility: visible;
      }
    }
  }
}
</style>
