<template>
  <div class="m-b-20 p-b-20">
    <div class="wh100 font-strong p-b-20" :id="titleId">{{ title }}</div>

    <div v-if="threeDataRemark && threeDataRemark.length > 0">
      <div id="box" ref="equityPenetration" className="equity-penetrant"></div>
    </div>
    <div v-else>
      <empty-data />
    </div>
  </div>
</template>

<script src="./source/d3.js"></script>

<script>
// import data from './data.js'
import { getSelectEquityThrough } from '../../api'
let that = null
export default {
  name: 'EquityPenetrant',
  props: {
    // shareholderData: {
    //   type: Object,
    //   default: () => {}
    // },
    title: {
      type: String,
      default: '股权穿透'
    },
    titleId: {
      type: String,
      default: 'ControlPeople'
    }
  },
  data() {
    return {
      name: null,
      threeData: null,
      threeDataRemark: [],
      shareholderData: {}
    }
  },
  mounted() {
    that = this
    this.selectEquityThroughGet()
  },
  methods: {
    selectEquityThroughGet() {
      getSelectEquityThrough(this.$options.propsData.route.query.id).then(
        res => {
          this.shareholderData = res.equityThrough || {}
        }
      )
    },
    init() {
      var box = document.getElementById('box')
      // console.log('box--', box)
      var containerWidth = box.clientWidth
      var containerHeight = box.clientHeight
      var rootName = '' //根节点的名字
      var rootRectWidth = 0 //根节点rect的宽度
      var downwardLength = 0
      var upwardLength = 0
      var forUpward = true

      var treeChart = function (d3Object) {
        this.d3 = d3Object
        this.directions = ['upward', 'downward']
      }

      treeChart.prototype.drawChart = function () {
        // First get tree data for both directions.
        this.treeData = {}
        var self = this
        // d3.json(`./source/data.json`, function (error, allData) {
        // console.log('self.name--', that.name)
        let allData = that.threeData
        self.directions.forEach(function (direction) {
          self.treeData[direction] = allData[direction]
        })
        rootName = that.name
        rootRectWidth = rootName.length * 15
        //获得upward第一级节点的个数
        upwardLength = allData.upward.children.length
        //获得downward第一级节点的个数
        downwardLength = allData.downward.children.length
        self.graphTree(self.getTreeConfig())
        // })
      }

      treeChart.prototype.getTreeConfig = function () {
        var treeConfig = {
          margin: {
            top: -300,
            right: 0,
            bottom: 0,
            left: 0
          }
        }

        treeConfig.chartWidth =
          containerWidth - treeConfig.margin.right - treeConfig.margin.left
        treeConfig.chartHeight =
          containerHeight - treeConfig.margin.top - treeConfig.margin.bottom
        treeConfig.centralHeight = treeConfig.chartHeight / 2 //中心坐标
        treeConfig.centralWidth = treeConfig.chartWidth / 2 //中心坐标
        treeConfig.linkLength = 100
        treeConfig.duration = 500 //动画时间
        return treeConfig
      }

      treeChart.prototype.graphTree = function (config) {
        var self = this
        var d3 = this.d3
        var linkLength = config.linkLength
        var duration = config.duration
        var hasChildNodeArr = []
        var id = 0
        // 曲线-----------start---
        // var diagonal = d3.svg.diagonal().source(function(d) {
        //      // console.log(d)
        //      return {
        //          "x": d.source.x,
        //          "y": d.source.name == 'origin' ? (forUpward ? d.source.y  :d.source.y+20 ) : (forUpward ? d.source.y-50: d.source.y+40)
        //      };
        //  })
        //  .target(function(d) {
        //      return {
        //          "x": d.target.x,
        //          // "y": d.target.y,
        //          "y": d.target.name == 'origin' ? (forUpward ? d.target.y  :d.target.y ) : (forUpward ? d.target.y: d.target.y-15)
        //      };
        //  })
        //  .projection(function(d) {
        //      return [d.x, d.y];
        //  });
        // 曲线----------end----

        var diagonal = function (obj) {
          //折线
          var s = obj.source
          var t = obj.target
          return (
            'M' +
            s.x +
            ',' +
            s.y +
            'L' +
            s.x +
            ',' +
            (s.y + (t.y - s.y) / 2) +
            'L' +
            t.x +
            ',' +
            (s.y + (t.y - s.y) / 2) +
            'L' +
            t.x +
            ',' +
            t.y
          )
        }

        var zoom = d3.behavior.zoom().scaleExtent([0.5, 3]).on('zoom', redraw)
        var svg = d3
          .select('#box')
          .append('svg')
          .attr('xmlns', 'http://www.w3.org/2000/svg')
          .attr(
            'width',
            config.chartWidth + config.margin.right + config.margin.left
          )
          .attr(
            'height',
            config.chartHeight + config.margin.top + config.margin.bottom
          )
          .on('mousedown', disableRightClick)
          .call(zoom)
          .on('dblclick.zoom', null)
        var treeG = svg
          .append('g')
          .attr('class', 'gbox')
          .attr(
            'transform',
            'translate(' +
              config.margin.left +
              ',' +
              config.margin.top +
              ')scale(1)'
          )

        //箭头(下半部分)
        var markerDown = svg
          .append('marker')
          .attr('id', 'resolvedDown')
          .attr('markerUnits', 'strokeWidth') //设置为strokeWidth箭头会随着线的粗细发生变化
          .attr('markerUnits', 'userSpaceOnUse')
          .attr('viewBox', '0 0 12 12') //坐标系的区域
          .attr('refX', 47) //箭头坐标
          .attr('refY', 6)
          .attr('markerWidth', 12) //标识的大小
          .attr('markerHeight', 12)
          .attr('orient', '90') //绘制方向，可设定为：auto（自动确认方向）和 角度值
          .attr('stroke-width', 2) //箭头宽度
          .append('path')
          .attr('d', 'M2,2 L12,6 L2,10 L4,6 L2,2') //箭头的路径
          .attr('fill', '#38f') //箭头颜色

        //箭头(上半部分)
        var markerUp = svg
          .append('marker')
          .attr('id', 'resolvedUp')
          .attr('markerUnits', 'strokeWidth') //设置为strokeWidth箭头会随着线的粗细发生变化
          .attr('markerUnits', 'userSpaceOnUse')
          .attr('viewBox', '0 0 12 12') //坐标系的区域
          .attr('refX', 50) //箭头坐标
          .attr('refY', 6)
          .attr('markerWidth', 12) //标识的大小
          .attr('markerHeight', 12)
          .attr('orient', '-90') //绘制方向，可设定为：auto（自动确认方向）和 角度值
          .attr('stroke-width', 2) //箭头宽度
          .append('path')
          .attr('d', 'M2,2 L12,6 L2,10 L4,6 L2,2') //箭头的路径
          .attr('fill', '#38f') //箭头颜色

        for (var d in this.directions) {
          var direction = this.directions[d]
          var data = self.treeData[direction]
          data.x0 = config.centralWidth
          data.y0 = config.centralHeight
          data.children.forEach(collapse)
          update(data, data, treeG)
        }

        function update(source, originalData, g) {
          // Set up the upward vs downward separation.
          var direction = originalData['direction']
          var forUpward = direction == 'upward'
          var node_class = direction + 'Node'
          var link_class = direction + 'Link'
          var downwardSign = forUpward ? -1 : 1
          var nodeColor = forUpward ? '#D6D6D6' : '#D6D6D6'

          var isExpand = false
          var statusUp = true
          var statusDown = true
          var nodeSpace = 180
          var tree = d3.layout.tree().sort(sortByDate).nodeSize([nodeSpace, 0])
          var nodes = tree.nodes(originalData)
          var links = tree.links(nodes)
          var offsetX = -config.centralWidth
          //向一边发展的树杈
          nodes.forEach(function (d) {
            d.y = downwardSign * (d.depth * linkLength) + config.centralHeight
            d.x = d.x - offsetX
            if (d.name == 'origin') {
              d.x = config.centralWidth
              d.y += downwardSign * 0 // 上下两树图根节点之间的距离
            }
          })

          var node = g.selectAll('g.' + node_class).data(nodes, function (d) {
            return d.id || (d.id = ++id)
          })
          var nodeEnter = node
            .enter()
            .append('g')
            .attr('class', node_class)
            .attr('transform', function (d) {
              return 'translate(' + source.x0 + ',' + source.y0 + ')'
            })
            .style('cursor', function (d) {
              return d.name == 'origin'
                ? ''
                : d.children || d._children
                ? 'pointer'
                : ''
            })
            .on('click', click)

          nodeEnter
            .append('svg:rect')
            .attr('x', function (d) {
              return d.name == 'origin' ? -(rootRectWidth / 2) : -70
            })
            .attr('y', function (d) {
              return d.name == 'origin' ? -17 : forUpward ? -26 : -30
            })
            .attr('width', function (d) {
              return d.name == 'origin' ? rootRectWidth : 140
            })
            .attr('height', function (d) {
              return d.name == 'origin' ? 34 : 60
            })
            .attr('rx', 2)
            .style('stroke', function (d) {
              return d.name == 'origin' ? '#38f' : '#d0e4ff'
            })
            .style('fill', function (d) {
              return d.name == 'origin' ? '#38f' : '#f9fcff' //节点背景色
            })

          // 画圆
          nodeEnter.append('circle').attr('r', 1e-6)

          // 企业名称第一行
          nodeEnter
            .append('text')
            .attr('class', 'linkname')
            .attr('x', function (d) {
              return d.name == 'origin' ? '0' : '-62'
            })
            .attr('dy', function (d) {
              return d.name == 'origin' ? '.35em' : forUpward ? '-10' : '-14'
            })
            .attr('text-anchor', function (d) {
              return d.name == 'origin' ? 'middle' : 'start'
            })
            .attr('fill', '#191919')
            .text(function (d) {
              if (d.name == 'origin') {
                // return ((forUpward) ? '根节点TOP' : '根节点Bottom');
                return rootName
              }
              // Text for summary nodes.
              if (d.repeated) {
                return '[Recurring] ' + d.name
              }
              //   return d.name
              return d.name.length > 12 ? d.name.substr(0, 12) : d.name
            })
            .style({
              'fill-opacity': 1e-6,
              fill: function (d) {
                if (d.name == 'origin') {
                  return '#fff'
                }
              },
              'font-size': function (d) {
                return d.name == 'origin' ? 14 : 10
              },
              cursor: 'pointer'
            })
            .on('click', function () {
              console.log(1)
            })

          // 企业名称第二行
          nodeEnter
            .append('text')
            .attr('class', 'linkname')
            .attr('x', '-62')
            .attr('dy', function (d) {
              return d.name == 'origin' ? '.35em' : forUpward ? '4' : '0'
            })
            .attr('text-anchor', function () {
              return d.name == 'origin' ? 'middle' : 'start'
            })
            .text(function (d) {
              var textStr = d.name.substr(12, d.name.length)
              var textRes = ''
              textStr.length > 12
                ? (textRes = textStr.substr(0, 10) + '...')
                : (textRes = textStr)

              return textRes
            })
            .style({
              fill: '#191919',
              'font-size': function (d) {
                return d.name == 'origin' ? 14 : 10
              },
              cursor: 'pointer'
            })
            .on('click', function () {
              console.log(1)
            })

          // 认缴出资额
          nodeEnter
            .append('text')
            .attr('x', '-62')
            .attr('dy', function (d) {
              return d.name == 'origin' ? '.35em' : forUpward ? '25' : '20'
            })
            .attr('text-anchor', 'start')
            .attr('class', 'linkname')
            .style('fill', '#999')
            .style('font-size', 9)
            .text(function (d) {
              var str = d.name == 'origin' ? '' : `认缴出资额${d.shouldCapi}`
              return str.length > 13 ? str.substr(0, 13) + '..' : str
            })
          // 股权占比
          nodeEnter
            .append('text')
            .attr('x', '10')
            .attr('dy', function (d) {
              return d.name == 'origin' ? '.35em' : forUpward ? '48' : '-36'
            })
            .attr('text-anchor', 'start')
            .attr('class', 'linkname')
            .style('fill', '#38f')
            .style('font-size', 10)
            .text(function (d) {
              return d.name == 'origin' ? '' : `持股比例${d.fundedRatio}`
            })

          // Transition nodes to their new position.原有节点更新到新位置
          var nodeUpdate = node
            .transition()
            .duration(duration)
            .attr('transform', function (d) {
              return 'translate(' + d.x + ',' + d.y + ')'
            })
          nodeUpdate
            .select('circle')
            .style('fill', '#fff')
            .attr('r', function (d) {
              return d.name == 'origin'
                ? 0
                : hasChildNodeArr.indexOf(d) == -1
                ? 0
                : 6
            })
            .attr('cy', function (d) {
              return d.name == 'origin' ? -20 : forUpward ? -35 : 39
            })
            .style('fill', function (d) {
              return hasChildNodeArr.indexOf(d) != -1 ? '#fff' : ''
              // if (d._children || d.children) { return "#fff"; } else { return "rgba(0,0,0,0)"; }
            })
            .style('stroke', function (d) {
              return hasChildNodeArr.indexOf(d) != -1 ? '#38f' : ''
              // if (d._children || d.children) { return "#8b4513"; } else { return "rgba(0,0,0,0)"; }
            })
            .style('fill-opacity', function (d) {
              if (d.children) {
                return 1
              }
            })
            // Setting summary node style as class as mass style setting is
            // not compatible to circles.
            .style('stroke-width', function (d) {
              if (d.repeated) {
                return 5
              }
            })
          //代表是否展开的+-号
          nodeEnter
            .append('svg:text')
            .attr('class', 'isExpand')
            .attr('x', '0')
            .attr('dy', function (d) {
              return forUpward ? -32 : 42
            })
            .attr('text-anchor', 'middle')
            .style('fill', '#38f')
            .text(function (d) {
              if (d.name == 'origin') {
                return ''
              }
              return hasChildNodeArr.indexOf(d) != -1 ? '+' : ''
              /* if (d._children || d.children) {
                return "+";
              } */
            })

          nodeUpdate.select('text').style('fill-opacity', 1)

          //******************************************最终受益人 start******************************************//
          //提示框
          //   var tsk = nodeEnter
          //     .append('svg:rect')
          //     .attr('x', -60)
          //     .attr('y', function (d) {
          //       return forUpward ? -86 : -68
          //     })
          //     .attr('width', function (d) {
          //       if (d.name == 'origin') {
          //         return 0
          //       } else {
          //         return d.hasHumanholding ? 120 : 0 //如果有最终受益人
          //       }
          //     })
          //     .attr('height', 20)
          //     .style('stroke', function (d) {
          //       return '#1078AF'
          //     })
          //     .style('fill', function (d) {
          //       return '#46A2D2'
          //     })
          //   //三角形
          //   nodeEnter
          //     .append('svg:path')
          //     .attr('fill', '#1078AF')
          //     .attr('d', function (d) {
          //       if (d.name == 'origin') {
          //         return ''
          //       } else {
          //         return d.hasHumanholding
          //           ? forUpward
          //             ? 'M-60 -66 L-40 -66 L-50 -52 Z'
          //             : 'M-60 -48 L-40 -48 L-50 -38 Z'
          //           : '' //如果有最终受益人
          //       }
          //     })
          //   nodeEnter
          //     .append('svg:text')
          //     .attr('x', '-58')
          //     .attr('dy', function (d) {
          //       return forUpward ? '-73' : '-55'
          //     })
          //     .attr('text-anchor', 'start')
          //     .style('fill', '#fff')
          //     .style('font-size', 10)
          //     .text(function (d) {
          //       var str =
          //         '我是最终受益人'.length > 6
          //           ? '我是最终受益人'.substr(0, 6) + '..'
          //           : '我是最终受益人'
          //       return d.hasHumanholding ? '最终受益人:' + str : '' //如果有最终受益人
          //     })
          //******************************************最终受益人 end******************************************//

          // Transition exiting nodes to the parent's new position.
          var nodeExit = node
            .exit()
            .transition()
            .duration(duration)
            .attr('transform', function (d) {
              return 'translate(' + source.x + ',' + source.y + ')'
            })
            .remove()
          nodeExit.select('circle').attr('r', 1e-6)
          nodeExit.select('text').style('fill-opacity', 1e-6)

          var link = g
            .selectAll('path.' + link_class)
            .data(links, function (d) {
              return d.target.id
            })

          link
            .enter()
            .insert('path', 'g')
            .attr('class', link_class)
            .attr('stroke', function (d) {
              return '#D6D6D6'
            })
            .attr('fill', 'none')
            .attr('stroke-width', 0.5)
            .attr('d', function (d) {
              var o = { x: source.x0, y: source.y0 }
              return diagonal({ source: o, target: o })
            })
            .attr('marker-end', function (d) {
              return forUpward ? 'url(#resolvedUp)' : 'url(#resolvedDown)'
            })
            //根据箭头标记的id号标记箭头;
            .attr('id', function (d, i) {
              return 'mypath' + i
            })
          // Transition links to their new position.
          link.transition().duration(duration).attr('d', diagonal)
          // Transition exiting nodes to the parent's new position.
          link
            .exit()
            .transition()
            .duration(duration)
            .attr('d', function (d) {
              var o = { x: source.x, y: source.y }
              return diagonal({ source: o, target: o })
            })
            .remove()
          // Stash the old positions for transition.
          nodes.forEach(function (d) {
            d.x0 = d.x
            d.y0 = d.y
          })

          /**
           * Tree function to toggle on click.
           * @param {Object} d data object for D3 use.
           */
          function click(d) {
            if (forUpward) {
              if (d._children) {
                console.log('股东--ok')
              } else {
                console.log('股东--no')
              }
            } else {
              if (d._children) {
                // console.log('对外投资--ok')
              } else {
                // console.log('对外投资--no')
              }
            }

            console.log(d)
            this.isExpand = !this.isExpand
            if (this.isExpand) {
              d3.select(this)
                .select('.isExpand')
                .text(hasChildNodeArr.indexOf(d) != -1 ? '-' : '')
            } else {
              d3.select(this)
                .select('.isExpand')
                .text(hasChildNodeArr.indexOf(d) != -1 ? '+' : '')
            }

            if (d.name == 'origin') {
              return
            }
            if (d.children) {
              d._children = d.children
              d.children = null
            } else {
              d.children = d._children
              d._children = null
              // expand all if it's the first node
              if (d.name == 'origin') {
                d.children.forEach(expand)
              }
            }
            update(d, originalData, g)
          }
        }

        // Collapse and Expand can be modified to include touched nodes.
        /**
         * Tree function to expand all nodes.
         * @param {Object} d data object for D3 use.
         */
        function expand(d) {
          if (d._children) {
            d.children = d._children
            d.children.forEach(expand)
            d._children = null
          }
        }

        /**
         * Tree function to collapse children nodes.
         * @param {Object} d data object for D3 use.
         */
        function collapse(d) {
          if (d.children && d.children.length != 0) {
            d._children = d.children
            d._children.forEach(collapse)
            d.children = null
            hasChildNodeArr.push(d)
          }
        }

        /**
         * Tree function to redraw and zoom.
         */
        function redraw() {
          treeG.attr(
            'transform',
            'translate(' +
              d3.event.translate +
              ')' +
              ' scale(' +
              d3.event.scale * 1 +
              ')'
          )
          //   'translate(' +
          //     (d3.event.translate[0] + config.margin.left) +
          //     ',' +
          //     (d3.event.translate[1] + config.margin.top) +
          //     ')' +
          //     ' scale(' +
          //     d3.event.scale * 1 +
          //     ')'
        }

        /**
         * Tree functions to disable right click.
         */
        function disableRightClick() {
          // stop zoom
          if (d3.event.button == 2) {
            console.log('No right click allowed')
            d3.event.stopImmediatePropagation()
          }
        }

        function sortByDate(a, b) {
          var aNum = a.name.substr(a.name.lastIndexOf('(') + 1, 4)
          var bNum = b.name.substr(b.name.lastIndexOf('(') + 1, 4)
          return (
            d3.ascending(aNum, bNum) ||
            d3.ascending(a.name, b.name) ||
            d3.ascending(a.id, b.id)
          )
        }
      }

      var d3GenerationChart = new treeChart(d3)
      d3GenerationChart.drawChart()
    },
    screenData(val) {
      let remark = JSON.parse(val.remark)
      this.threeDataRemark = remark || []
      this.threeData = this.recursion(remark)
      this.$nextTick(() => {
        this.init()
      })
    },
    recursion(arr) {
      let obj = {
        downward: {
          direction: 'downward',
          name: 'origin',
          children: []
        },
        upward: {
          direction: 'upward',
          name: 'origin',
          hasChildren: true,
          children: []
        }
      }

      arr.forEach((item, index, arr) => {
        obj.downward.children.push({
          name: item.name,
          shouldCapi: item.shouldCapi,
          fundedRatio: item.fundedRatio,
          hasHumanholding: true,
          hasChildren: true,
          //  children遍历加展开
          children: item.children
        })
        if (item.children instanceof Array) {
          // 有children数组
          this.recursion(item.children)
        }
      })
      // console.log('CDatasad----', obj)
      return obj
    }
  },

  watch: {
    shareholderData: {
      handler: function (val) {
        if (val && JSON.stringify(val) !== '{}') {
          this.name = val.name
          this.screenData(val)
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss">
#box {
  width: 100%;
  height: 500px;
}

#box > svg {
  cursor: all-scroll;
  border: 1px solid #e7e7e7;
}

.centralText {
  font: 23px sans-serif;
  fill: #222;
}

.downwardNode text,
.upwardNode text {
  font: 10px sans-serif;
}

.downwardLink {
  fill: none;
  stroke: #d0e4ff;
  stroke-width: 1px;
  //   opacity: 0.5;
}

.upwardLink {
  fill: none;
  stroke: #d0e4ff;
  stroke-width: 1px;
  //   opacity: 0.5;
}

.linkname {
  display: inline-block;
  width: 100px;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
