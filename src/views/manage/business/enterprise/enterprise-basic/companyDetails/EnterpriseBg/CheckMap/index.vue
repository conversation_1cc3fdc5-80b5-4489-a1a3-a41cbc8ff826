<template>
  <!-- 查查图谱 -->
  <div class="illustrative-main">
    <div class="title" :id="titleId">{{ title }}</div>
    <ul class="flex justify-content-between">
      <li class="item" v-for="(t, i) in data" :key="i">
        <img class="image" :src="t.img" :alt="t.text" />
        <div class="text">{{ t.text }}</div>
      </li>
    </ul>
  </div>
</template>

<script>
export default {
  name: 'CheckMap',
  props: {
    title: {
      type: String,
      default: '查查图谱'
    },
    titleId: {
      type: String,
      default: 'checkMap'
    }
  },
  data() {
    return {
      data: [
        {
          img: require('../images/illustrative-chart/graphs.png'),
          text: '关系图谱'
        },
        {
          img: require('../images/illustrative-chart/graphs1.png'),
          text: '企业图谱'
        },
        {
          img: require('../images/illustrative-chart/graphs2.png'),
          text: '股权穿透图'
        },
        {
          img: require('../images/illustrative-chart/graphs3.png'),
          text: '股权结构图'
        },
        {
          img: require('../images/illustrative-chart/graphs4.png'),
          text: '企业受益股东'
        },
        {
          img: require('../images/illustrative-chart/graphs5.png'),
          text: '企业股权分布'
        },
        {
          img: require('../images/illustrative-chart/graphs6.png'),
          text: '关联方认定图'
        },
        {
          img: require('../images/illustrative-chart/graphs7.png'),
          text: '实际控制人'
        },
        {
          img: require('../images/illustrative-chart/graphs8.png'),
          text: '风险图谱'
        },
        {
          img: require('../images/illustrative-chart/graphs9.png'),
          text: '债务/债权'
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.illustrative-main {
  margin-bottom: 40px;

  .title {
    height: 32px;
    font-size: 16px;
    font-weight: bold;
    line-height: 32px;
    color: #333;
    margin-bottom: 10px;
  }
  .item {
    .image {
      width: 110px;
      height: 68px;
      border: solid 1px transparent;
    }
    .text {
      text-align: center;
      font-size: 14px;
      color: #333;
      margin-top: 10px;
    }
  }

  .item:hover {
    cursor: pointer;
    .image {
      border: solid 1px #128bed;
    }
    .text {
      color: #128bed;
    }
  }
}
</style>
