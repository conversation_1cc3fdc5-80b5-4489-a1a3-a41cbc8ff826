import request from '@/utils/request'

// 获取企业基本信息
export function getSelectBasicDetail(params) {
  return request({
    url: '/enterprise/info/selectBasicDetail',
    method: 'get',
    params
  })
}

// 获取企业动态
export function getSelectDynamic(params) {
  return request({
    url: '/enterprise/info/selectDynamic',
    method: 'get',
    params
  })
}

// 获取企业工商信息
export function getSelectBusinessInfo(params) {
  return request({
    url: '/enterprise/info/selectBusinessInfo',
    method: 'get',
    params
  })
}

// 获取企业股权穿透信息
export function getSelectEquityThrough(params) {
  return request({
    url: `/enterprise/info/selectEquityThrough?id=${params}`,
    method: 'get'
  })
}

// 获取资质荣誉
export function getSelectQualificationHonor(params) {
  return request({
    url: '/enterprise/info/selectQualificationHonor',
    method: 'get',
    params
  })
}

// 获取资质荣誉
export function getSelectPatent(params) {
  return request({
    url: '/enterprise/info/selectPatent',
    method: 'get',
    params
  })
}

// 分页获取软著
export function getSelectSoftwareCopyright(params) {
  return request({
    url: '/enterprise/info/selectSoftwareCopyright',
    method: 'get',
    params
  })
}

// 获取企业详情合同列表
export function getSelectContractList(params) {
  return request({
    url: '/contract/record/selectContractList',
    method: 'get',
    params
  })
}
