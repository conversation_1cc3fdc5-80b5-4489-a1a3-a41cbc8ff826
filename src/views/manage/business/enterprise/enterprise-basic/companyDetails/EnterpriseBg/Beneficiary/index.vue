<template>
  <!-- 对外投资 -->
  <div class="stock bg-white">
    <div class="m-b-20 flex">
      <span class="p-r-8 font-strong" :id="titleId">{{ title }}</span>
      <span class="p-r-6 font-strong color-primary">{{ count }}</span>
      <!--      <el-tooltip class="item" effect="light" content="最新公示: " placement="bottom-start">-->
      <!--        <div slot="content">-->
      <!--          <div class="m-b-6">最新公示: 来源企业年报、招股书和企业上市公告等</div>-->
      <!--          <div>工商登记: 来源国家企业信用信息公示系统</div>-->
      <!--        </div>-->
      <!--        <svg-icon icon-class="error-circle" class="color-info font-size-15"/>-->
      <!--      </el-tooltip>-->
      <el-tooltip placement="bottom-start">
        <div slot="content" class="line-height-20" style="width: 300px">
          受益所有人为依据相关政策文件识别标准，层层深入并最终明确为掌握控制权或者获取收益的一个或多个自然人。
          受益自然人为拥有25%（含）以上股权或疑似拥有合伙权益或收益权的自然人及对法人或非法人组织进行实际控制的多个自然人。
          受益机构为拥有25%（含）以上股权或疑似拥有合伙权益或收益权的法人及对法人或非法人组织进行实际控制的多个法人。
          该结果仅供用户参考，并不代表企查查的任何明示、暗示之观点或保证。
        </div>
        <span>
          <svg-icon
            icon-class="error-circle"
            class="color-info font-size-13 pointer"
          />
        </span>
      </el-tooltip>
    </div>

    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :table-data="tableData"
    >
    </drive-table>
  </div>
</template>

<script>
import ColumnMixins from './column/column'
import { deepClone } from '@/utils/tools'
export default {
  name: 'Beneficiary',
  mixins: [ColumnMixins],

  props: {
    title: {
      type: String,
      default: '最终受益人'
    },
    titleId: {
      type: String,
      default: 'Beneficiary'
    },
    count: {
      type: String,
      default: ''
    }
  },
  mounted() {
    const data = deepClone(this.$options.propsData.beneficialOwner)
    const { breakThroughList } = data
    this.tableData = JSON.parse(breakThroughList)
    this.tableData.forEach((t, i) => (t.serialNumber = i + 1))
  },
  data() {
    return {
      beneficialOwner: null,
      shareholderData: {},
      tableData: []
    }
  },
  methods: {
    // 获取受益所有人
  }
}
</script>

<style lang="scss" scoped>
.commonScrollbarTable ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  display: none;
}
.scrollbarShow ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
  display: block;
}

::v-deep(.el-tooltip__popper.is-light) {
  border: none !important;
  //  阴影
}

.color-i {
  font-weight: bold;
  font-size: 14px;
  color: #666;
}

.color-p {
  font-size: 14px;
  font-weight: bold;
  color: #128bed;
}

.stock {
  margin-bottom: 40px;
  .stock-tree {
    width: 100%;
    height: 500px;
    border: 1px solid #e7e7e7;
  }
  .stock-table {
    width: 100%;
  }
  ::-webkit-scrollbar {
    width: 4px;
  }
}
</style>
