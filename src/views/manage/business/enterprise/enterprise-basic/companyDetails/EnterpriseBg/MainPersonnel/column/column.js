export default {
  data() {
    return {
      tableColumn: [
        {
          label: '序号',
          width: 60,
          prop: 'serialNumber'
        },
        {
          label: '姓名',
          prop: 'name'
        },
        {
          label: '职务',
          prop: 'job',
          render: (h, scope) => {
            return <div>{scope.row.job}</div>
          }
        }
        // {
        //   label: '性别',
        //   prop: 'sex',
        // },
        // {
        //   label: '学历',
        //   prop: 'education'
        // },
        //
        // {
        //   label: '薪酬',
        //   prop: 'salary',
        // },
        // {
        //   label: '持股数',
        //   prop: 'shareholdingNum'
        // },
        // {
        //   label: '持股比例',
        //   prop: 'shareholdingRatio'
        // },
        // {
        //   label: '最终受益股份',
        //   prop: 'beneficiaryShares'
        // },
        // {
        //   label: '本届任期',
        //   prop: 'termOffice'
        // },
        // {
        //   label: '公告日期',
        //   prop: 'announcementDate'
        // }
      ],
      tableColumnFirm: [
        {
          label: '序号',
          width: 60,
          prop: 'serialNumber'
        },
        {
          label: '姓名',
          prop: 'name'
        },
        {
          label: '职务',
          prop: 'position'
        },
        {
          label: '持股比例',
          prop: 'ratio'
        },
        {
          label: '最终受益股份',
          prop: 'beneficiaryShares'
        }
      ],
      tableColumnHistory: [
        {
          label: '序号',
          prop: 'serialNumber'
        },
        {
          label: '姓名',
          prop: 'name'
        },
        {
          label: '职务',
          prop: 'position'
        },
        {
          label: '任职日期',
          prop: 'dateService'
        }
      ]
    }
  }
}
