<template>
  <!--  资质荣誉-->
  <div class="qualifications-container">
    <div class="flex justify-content-between align-items-center">
      <div :id="titleId" class="qualifications-title m-b-10">{{ title }}</div>
      <el-button type="primary" size="mini" class="m-r-10" @click="openHandler"
        ><svg-icon icon-class="add" /> 新增</el-button
      >
    </div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableList"
      :columns="tableHonors"
    >
    </drive-table>
    <!--    资质荣誉弹窗-->
    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formHonors"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from '../column/column'
import descriptorMixins from '../descriptor/descriptor'
import {
  getAptitudeCreate,
  getAptitudeDelete,
  getAptitudeInfo,
  getAptitudeUpdate,
  getAptitudeEntList
} from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/api'

export default {
  name: 'Qualifications',
  mixins: [ColumnMixins, descriptorMixins],
  props: {
    title: {
      type: String,
      default: '资质荣誉'
    },
    titleId: {
      type: String,
      default: 'Qualifications'
    }
  },
  data() {
    return {
      tableList: [],
      visible: false,
      titleStr: '资质荣誉',
      id: '',
      fromModel: {}
    }
  },
  created() {
    this.getAptitudeEntList()
  },
  methods: {
    async getAptitudeEntList() {
      let entId = this.$options.propsData.route.query.id
      const res = await getAptitudeEntList(entId)
      this.tableList = res
    },
    // 打开弹窗
    openHandler() {
      this.visible = true
      this.fromModel = {}
    },
    // 弹框提交按钮
    confirmDialog() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let entId = this.$options.propsData.route.query.id
          if (this.id) {
            await getAptitudeUpdate({
              ...this.fromModel,
              entId,
              dateSource: 2,
              id: this.id
            })
            this.$toast.success('编辑成功')
            this.id = ''
          } else {
            await getAptitudeCreate({
              ...this.fromModel,
              entId,
              dataSource: 2
            })
            this.$toast.success('新增成功')
          }
          this.visible = false
          this.getAptitudeEntList()
        }
      })
    },
    // 编辑
    async editHandler(row) {
      this.id = row.id
      this.visible = true
      const res = await getAptitudeInfo({ id: row.id })
      let {
        dictValue,
        apLevel,
        certificateNo,
        approvedTime,
        grantingUnit,
        description
      } = res
      this.fromModel = {
        dictValue,
        apLevel,
        certificateNo,
        approvedTime,
        grantingUnit,
        description
      }
    },
    // 删除
    delHandler(row) {
      this.$confirm('确定删除该资质荣誉？').then(async () => {
        await getAptitudeDelete(row.id)
        this.$toast.success('删除成功')
        this.getAptitudeEntList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.qualifications-container {
  margin-bottom: 16px;

  .qualifications-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}
</style>
