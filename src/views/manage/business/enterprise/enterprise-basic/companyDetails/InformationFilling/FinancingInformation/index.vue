<template>
  <!--  融资信息-->
  <div class="financing-information-container">
    <div class="flex justify-content-between align-items-center">
      <div :id="titleId" class="financing-title m-b-10">{{ title }}</div>
      <el-button type="primary" size="mini" class="m-r-10" @click="openHandler"
        ><svg-icon icon-class="add" /> 新增</el-button
      >
    </div>
    <!--    表格-->
    <drive-table
      ref="drive-table"
      :table-data="tableList"
      :columns="tableFinancing"
    >
    </drive-table>
    <!--    融资信息弹窗-->
    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formFinancing"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from '../column/column'
import descriptorMixins from '../descriptor/descriptor'
import {
  getFinancingCreate,
  getFinancingDelete,
  getFinancingInfo,
  getFinancingEntList,
  getFinancingUpdate
} from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/api'

export default {
  name: 'FinancingInformation',
  mixins: [ColumnMixins, descriptorMixins],
  props: {
    title: {
      type: String,
      default: '资质荣誉'
    },
    titleId: {
      type: String,
      default: 'Qualifications'
    }
  },
  data() {
    return {
      tableList: [],
      visible: false,
      titleStr: '融资历史',
      id: '',
      fromModel: {}
    }
  },
  created() {
    this.getFinancingEntList()
  },
  methods: {
    async getFinancingEntList() {
      let entId = this.$options.propsData.route.query.id
      const res = await getFinancingEntList(entId)
      this.tableList = res
    },
    // 打开弹窗
    openHandler() {
      this.visible = true
      this.fromModel = {}
    },
    // 弹框提交按钮
    confirmDialog() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let entId = this.$options.propsData.route.query.id
          if (this.id) {
            await getFinancingUpdate({
              ...this.fromModel,
              entId,
              dateSource: 2,
              id: this.id
            })
            this.$toast.success('编辑成功')
            this.id = ''
          } else {
            await getFinancingCreate({
              ...this.fromModel,
              entId,
              dataSource: 2
            })
            this.$toast.success('新增成功')
          }
          this.visible = false
          this.getFinancingEntList()
        }
      })
    },
    // 编辑
    async editHandler(row) {
      this.visible = true
      this.id = row.id
      this.visible = true
      const res = await getFinancingInfo({ id: row.id })
      let { approvedTime, apLevel, apName, certificateNo } = res
      this.fromModel = {
        approvedTime,
        apLevel,
        apName: String(apName),
        certificateNo
      }
    },
    // 删除
    delHandler(row) {
      this.$confirm('确定删除该融资信息？').then(async () => {
        await getFinancingDelete(row.id)
        this.$toast.success('删除成功')
        this.getFinancingEntList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.financing-information-container {
  margin-bottom: 16px;

  .financing-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
}
</style>
