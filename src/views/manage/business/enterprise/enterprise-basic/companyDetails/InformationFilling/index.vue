<template>
  <!--  信息填报-->
  <div class="information-filling-container">
    <!--    基本信息-->
    <basic-information />
    <!--    财务信息-->
    <financial-information />
    <!--    资质荣誉-->
    <qualifications />
    <!--    融资信息-->
    <financing-information />
    <!--    人才情况-->
    <talent-situation />
    <!--    党建信息-->
    <party-building />
  </div>
</template>

<script>
import BasicInformation from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/BasicInformation'
import FinancialInformation from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/FinancialInformation'
import Qualifications from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/Qualifications'
import FinancingInformation from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/FinancingInformation'
import TalentSituation from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/TalentSituation'
import PartyBuilding from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/PartyBuilding'
export default {
  name: 'InformationFilling',
  components: {
    PartyBuilding,
    TalentSituation,
    FinancingInformation,
    Qualifications,
    FinancialInformation,
    BasicInformation
  }
}
</script>

<style scoped></style>
