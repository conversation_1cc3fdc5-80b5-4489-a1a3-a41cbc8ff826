<template>
  <!--  基本信息-->
  <div class="essential-container">
    <dialog-cmp
      title="基本信息"
      :visible.sync="visible"
      width="50%"
      @confirmDialog="confirmDialog"
    >
      <div class="m-b-24 font-size-16">{{ objName.enterpriseName }}</div>
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        label-position="top"
        :formConfigure="formEssential"
      />
      <!--    联系人信息-->
      <div
        class="p-t-24 m-b-24 font-size-16"
        style="border-top: 1px solid #e9f0ff"
      >
        联系人信息
      </div>
      <driven-form
        ref="driven-form-contacts"
        v-model="fromContacts"
        label-position="top"
        :formConfigure="formContacts"
      />
      <!--    企业从业人员情况-->
      <div
        class="p-t-24 m-b-24 font-size-16"
        style="border-top: 1px solid #e9f0ff"
      >
        企业从业人员情况
      </div>
      <driven-form
        ref="driven-form-employees"
        v-model="fromEmployees"
        label-position="top"
        :formConfigure="formEmployees"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import descriptorMixins from '../../descriptor/descriptor'
import {
  getDetailFeatures,
  getDetailIndustry,
  getInfoBasicCreate,
  getPublicDict,
  getTenantDictList
} from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/api'
import { getSelectBasicDetail } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'

export default {
  name: 'Essential',
  mixins: [descriptorMixins],
  data() {
    return {
      visible: false,
      fromModel: {
        taxpayerType: 1,
        graduate: true,
        highTechEnterprises: true,
        technology: true,
        industryCategorySub: ''
      },
      fromContacts: {},
      fromEmployees: {
        juniorCollege: '',
        overseas: '',
        researchNum: '',
        professionalRanks: '',
        freshCollegeStudents: ''
      },
      objForm: {},
      objName: {}
    }
  },
  created() {
    this.getDetailFeatures()
    this.getDetailIndustry()
    this.getTenantDictData()
    this.getSelectBasicDetail()
  },
  methods: {
    async getSelectBasicDetail() {
      const res = await getSelectBasicDetail(
        this.$parent.$options.propsData.route.query.id
      )
      this.objName = res
    },
    async openHandler(e) {
      const res = await getPublicDict({
        dictType: e
      })
      this.formEssential.descriptors.industryCategorySub.options = res.map(
        item => {
          return { label: item.label, value: item.value }
        }
      )
    },
    // 行业分类（一级）
    async getTenantDictData() {
      const res = await getTenantDictList({
        type: 'trade'
      })
      this.formEssential.descriptors.industryCategoryMain.options = res.map(
        item => {
          return { label: item.name, value: item.type }
        }
      )
    },
    // change获取行业分类（二级）
    async typeChange(e) {
      this.fromModel.industryCategorySub = ''
      const res = await getPublicDict({
        dictType: e
      })
      this.formEssential.descriptors.industryCategorySub.options = res.map(
        item => {
          return { label: item.label, value: item.value }
        }
      )
    },
    // 获取企业主要负责人创业特征
    async getDetailFeatures() {
      const res = await getDetailFeatures()
      this.formEssential.descriptors.entProperty.options = res.map(item => {
        return { label: item.value, value: item.key }
      })
    },
    // 获取企业所属技术领域
    async getDetailIndustry() {
      const res = await getDetailIndustry()
      this.formEssential.descriptors.technical.options = res.map(item => {
        return { label: item.value, value: item.key }
      })
    },
    getInfoBasicList() {
      // 基本信息
      let {
        technical,
        entProperty,
        industryCategoryMain,
        industryCategorySub,
        taxpayerType,
        graduate,
        highTechEnterprises,
        technology
      } = this.objForm
      technical = Number(technical)
      this.typeChange(industryCategoryMain)
      this.fromModel = {
        technical,
        entProperty,
        industryCategoryMain,
        industryCategorySub,
        taxpayerType,
        graduate,
        highTechEnterprises,
        technology
      }
      // 联系人信息
      let {
        legalPerson,
        legalPersonPhone,
        legalPersonIdCard,
        contact,
        contactPhone,
        post,
        email
      } = this.objForm
      this.fromContacts = {
        legalPerson,
        legalPersonPhone,
        legalPersonIdCard,
        contact,
        contactPhone,
        post,
        email
      }
      // 企业从业人员情况
      let {
        juniorCollege,
        overseas,
        researchNum,
        professionalRanks,
        freshCollegeStudents
      } = this.objForm
      this.fromEmployees = {
        juniorCollege: String(juniorCollege),
        overseas: String(overseas),
        researchNum: String(researchNum),
        professionalRanks: String(professionalRanks),
        freshCollegeStudents: String(freshCollegeStudents)
      }
    },
    // 表单更新按钮
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        this.$refs['driven-form-contacts'].validate(v => {
          this.$refs['driven-form-employees'].validate(async t => {
            if (valid) {
              if (v) {
                if (t) {
                  let entId = this.$parent.$options.propsData.route.query.id
                  let data = {
                    ...this.fromModel,
                    ...this.fromContacts,
                    ...this.fromEmployees,
                    entId,
                    dataSource: 2
                  }
                  await getInfoBasicCreate({ ...data })
                  this.$toast.success('更新成功')
                  this.visible = false
                  this.$emit('getNewTable')
                }
              }
            }
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-select__tags-text) {
  color: rgba(0, 0, 0, 0.6);
}
</style>
