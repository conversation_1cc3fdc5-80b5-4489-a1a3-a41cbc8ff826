<template>
  <!--  基本信息-->
  <div class="basic-information-container">
    <div class="flex justify-content-between align-items-center">
      <div :id="titleId" class="basic-title m-b-10">{{ title }}</div>
      <div class="font-size-14 flex align-items-center">
        <el-button
          @click="openHandler"
          type="primary"
          style="height: 28px"
          class="m-r-10"
          >更新</el-button
        >
        <el-dropdown v-if="selectList && selectList.length !== 0">
          <el-button type="primary" size="mini"
            >历史记录 <svg-icon icon-class="chevron-down"
          /></el-button>
          <el-dropdown-menu slot="dropdown">
            <el-scrollbar class="p-b-8" style="max-height: 180px">
              <el-dropdown-item
                v-for="(item, index) in selectList"
                :key="index"
              >
                <div @click="changeTable(index)" class="p-r-8">
                  {{ item.name }}
                </div>
              </el-dropdown-item>
            </el-scrollbar>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <!--    企业基本信息-->
    <div class="basic-title m-b-10 font-size-14" style="font-weight: 400">
      企业基本信息
    </div>
    <table style="width: 100%" border="1" Cellspacing="0" Cellpadding="0">
      <tr>
        <th>企业所属技术领域</th>
        <td>{{ objTable.technicalStr | noData }}</td>
        <th>企业主要负责人创业特征</th>
        <td>{{ objTable.entPropertyStr | noData }}</td>
      </tr>
      <tr>
        <th>行业分类（一级）</th>
        <td>{{ objTable.industryCategoryMainStr | noData }}</td>
        <th>行业分类（二级）</th>
        <td>{{ objTable.industryCategorySubStr | noData }}</td>
      </tr>
      <tr>
        <th>企业纳税人类型</th>
        <td>{{ getTaxpayerType(objTable.taxpayerType) | noData }}</td>
        <th>毕业企业</th>
        <td>{{ getGraduate(objTable.graduate) | noData }}</td>
      </tr>
      <tr>
        <th>国家高新技术企业</th>
        <td>{{ getGraduate(objTable.highTechEnterprises) | noData }}</td>
        <th>科技型中小企业</th>
        <td>{{ getGraduate(objTable.technology) | noData }}</td>
      </tr>
    </table>
    <!--    联系人基本信息-->
    <div
      class="basic-title m-b-10 m-t-24 font-size-14"
      style="font-weight: 400"
    >
      联系人基本信息
    </div>
    <table style="width: 100%" border="1" Cellspacing="0" Cellpadding="0">
      <tr>
        <th>法定代表人姓名</th>
        <td>{{ objTable.legalPerson | noData }}</td>
        <th>法定代表人联系方式</th>
        <td>{{ objTable.legalPersonPhone | noData }}</td>
      </tr>
      <tr>
        <th>法定代表人身份证号</th>
        <td :colspan="3">{{ objTable.legalPersonIdCard | noData }}</td>
      </tr>
      <tr>
        <th>常用联系人姓名</th>
        <td>{{ objTable.contact | noData }}</td>
        <th>常用联系人联系方式</th>
        <td>{{ objTable.contactPhone | noData }}</td>
      </tr>
      <tr>
        <th>职务</th>
        <td>{{ objTable.post || '未填写' }}</td>
        <th>邮箱</th>
        <td>{{ objTable.email || '未填写' }}</td>
      </tr>
    </table>
    <!--    弹窗-->
    <essential ref="essential" @getNewTable="getInfoBasicList" />
  </div>
</template>

<script>
import Essential from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/BasicInformation/components/essential'
import { getInfoBasicList } from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/api'
import {
  getGraduate,
  getTaxpayerType
} from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/InformationFilling/utils/status'
export default {
  name: 'BasicInformation',
  components: { Essential },
  props: {
    title: {
      type: String,
      default: '基本信息'
    },
    titleId: {
      type: String,
      default: 'BasicInformation'
    }
  },
  data() {
    return {
      getGraduate,
      getTaxpayerType,
      visible: false,
      objTable: {},
      selectList: []
    }
  },
  created() {
    this.getInfoBasicList()
  },
  methods: {
    async getInfoBasicList() {
      let entId = this.$options.propsData.route.query.id
      const res = await getInfoBasicList(entId)
      console.log(res)
      this.selectList = res
      if (res && res.length > 0) {
        this.objTable = res[0]
      }
    },
    changeTable(idx) {
      this.selectList.forEach((item, index) => {
        if (index === idx) {
          this.objTable = item
        }
      })
      this.$refs.essential.objForm = this.objTable
      this.$refs.essential.getInfoBasicList()
    },
    // 打开弹窗
    openHandler() {
      this.$refs.essential.visible = true
      if (this.selectList && this.selectList.length > 0) {
        this.$refs.essential.objForm = this.objTable
        this.$refs.essential.getInfoBasicList()
        this.$refs.essential.openHandler(
          Number(this.objTable.industryCategoryMain)
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-information-container {
  margin-bottom: 16px;
  .basic-title {
    height: 32px;
    line-height: 32px;
    font-weight: 700;
  }
  table {
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    font-size: 14px;
  }

  th {
    width: 154px;
    @include background_color_mix(--color-primary, #ffffff, 96%);
    padding: 10px;
    font-weight: 400;
    text-align: left;
  }

  td {
    width: 500px;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    word-break: break-all;
    padding: 10px 10px 11px 10px;
    line-height: 1.6em;
    word-wrap: break-word;
  }
}

::v-deep {
  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }
}
</style>
