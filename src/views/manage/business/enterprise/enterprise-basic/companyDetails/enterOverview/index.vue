<template>
  <div class="enter-overview-container bg-white p-16 m-b-8">
    <div class="flex">
      <div class="enter-left">
        <!--        <div class="text-n line-height-22 m-b-24 font-size-14">基本信息</div>-->
        <basic-tab
          ref="basicTab"
          :tabs-data="list"
          :current="current"
          @tabsChange="tabsChange"
        />
        <el-row v-if="current === 0" class="line-height-22 font-size-14">
          <el-col :span="12" class="m-b-35">
            <div class="text-f m-b-4">常用联系人</div>
            <div class="flex">
              <el-tooltip placement="top-start">
                <span slot="content">{{ noData(objTable.contacts) }}</span>
                <span class="m-r-8 enter-name line-1 pointer">{{
                  noData(objTable.contacts)
                }}</span>
              </el-tooltip>
              <span
                class="color-primary"
                v-copy="noData(objTable.contactsPhone)"
                >{{ noData(objTable.contactsPhone) }}</span
              >
            </div>
          </el-col>
          <el-col :span="12" class="m-b-35">
            <div class="text-f m-b-4">经营场所面积(㎡)</div>
            <div>
              <span class="color-primary font-strong font-size-16">{{
                noData(objTable.area)
              }}</span>
            </div>
          </el-col>
          <el-col :span="12" class="m-b-35">
            <div class="text-f m-b-4">总经理或项目负责人</div>
            <div class="flex">
              <el-tooltip placement="top-start">
                <span slot="content">{{ noData(objTable.manager) }}</span>
                <span class="m-r-8 enter-name line-1 pointer">{{
                  noData(objTable.manager)
                }}</span>
              </el-tooltip>
              <span
                class="color-primary"
                v-copy="noData(objTable.managerPhone)"
                >{{ noData(objTable.managerPhone) }}</span
              >
            </div>
          </el-col>
          <el-col :span="12" class="m-b-35">
            <div class="text-f m-b-4">企业员工数(人)</div>
            <div>
              <span class="color-primary font-strong font-size-16">{{
                noData(objTable.plateEmployeeCount)
              }}</span>
              <!--              <span class="text-f m-l-4">(平台内)/257(2022)</span>-->
            </div>
          </el-col>
          <el-col :span="12">
            <div class="text-f m-b-4">法定代表人</div>
            <div class="flex">
              <el-tooltip placement="top-start">
                <span slot="content">{{ noData(objTable.legalPerson) }}</span>
                <span class="m-r-8 enter-name line-1 pointer">{{
                  noData(objTable.legalPerson)
                }}</span>
              </el-tooltip>
              <span
                class="color-primary"
                v-copy="noData(objTable.legalPersonPhone)"
                >{{ noData(objTable.legalPersonPhone) }}</span
              >
            </div>
          </el-col>
          <!--          <el-col :span="12">-->
          <!--            <div class="text-f m-b-4">认证车辆数(辆)</div>-->
          <!--            <div>-->
          <!--              <span class="font-strong font-size-16">{{-->
          <!--                noData(objTable.carCount)-->
          <!--              }}</span>-->
          <!--              <span class="text-f m-l-4"-->
          <!--                >/ {{ noData(objTable.carQuota) }}(配额)</span-->
          <!--              >-->
          <!--            </div>-->
          <!--          </el-col>-->
        </el-row>
        <div v-if="current === 1" class="main-wrapper font-size-14">
          <el-row class="main-row">
            <el-col class="m-b-16">
              <span class="text-n font-strong">资质荣誉</span>
            </el-col>
            <el-col class="line-height-22 m-b-13">
              <div class="flex">
                <div style="margin-right: 51px">
                  <div class="text-f m-b-4">累计获得(项)</div>
                  <div class="color-primary font-strong">
                    {{ objNow.total }}
                  </div>
                </div>
                <div>
                  <div class="text-f m-b-4">当年获得(项)</div>
                  <div class="text-f font-strong">{{ objNow.thisYear }}</div>
                </div>
              </div>
            </el-col>
            <el-col class="line-height-22">
              <div class="text-f m-b-4">失效预警(项)</div>
              <div class="flex">
                <div class="color-danger" style="margin-right: 72px">
                  <span class="font-strong m-r-4">{{ objNow.lapse }}</span>
                  <span>已失效</span>
                </div>
                <div class="color-warning">
                  <span class="font-strong m-r-4">{{
                    objNow.expiringSoon
                  }}</span>
                  <span>即将失效</span>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row class="main-row">
            <el-col class="m-b-16">
              <span class="text-n font-strong">知识产权</span>
            </el-col>
            <el-col class="line-height-22">
              <div class="m-b-22">
                <span class="text-f m-r-24">软著(项)</span>
                <span class="font-strong m-r-16">{{
                  noData(objKnowledge.opusCopyCount)
                }}</span>
                <span class="text-f m-r-4">同比(%)</span>
                <span class="color-success font-strong m-r-4"
                  >{{ noData(objKnowledge.opusCopyCountChain) }}%</span
                >
                <svg-icon
                  v-if="!objKnowledge.opusCopyCountChain === 0"
                  icon-class="down-arrow-radian"
                  class="color-success"
                />
              </div>
              <div class="m-b-22">
                <span class="text-f m-r-24">专利(项)</span>
                <span class="font-strong m-r-16">{{
                  noData(objKnowledge.patentCount)
                }}</span>
                <span class="text-f m-r-4">同比(%)</span>
                <span class="color-success font-strong m-r-4"
                  >{{ noData(objKnowledge.patentCountChain) }}%</span
                >
                <svg-icon
                  v-if="!objKnowledge.patentCountChain === 0"
                  icon-class="down-arrow-radian"
                  class="color-success"
                />
              </div>
              <div>
                <span class="text-f m-r-24">商标(项)</span>
                <span class="font-strong m-r-16">{{
                  noData(objKnowledge.tmCount)
                }}</span>
                <span class="text-f m-r-4">同比(%)</span>
                <span class="color-success font-strong m-r-4"
                  >{{ noData(objKnowledge.tmCountChain) }}%</span
                >
                <svg-icon
                  v-if="!objKnowledge.tmCountChain === 0"
                  icon-class="down-arrow-radian"
                  class="color-success"
                />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="enter-right">
        <div class="font-strong text-n line-height-22 m-b-16">企业概览</div>
        <div class="text-f m-b-8">在园概况</div>
        <div class="text-main">
          <div ref="isDetail" class="isContent text-e line-1">
            <span ref="isPark">
              <span
                v-if="objTable.enterParkInfo"
                v-html="noData(objTable.enterParkInfo)"
              ></span>
              <span v-else class="text-f">企业暂无数据</span>
            </span>
          </div>
          <el-tooltip v-if="isText1" placement="top-start">
            <div
              class="font-size-14 line-height-22"
              slot="content"
              v-html="noData(objTable.enterParkInfo)"
            ></div>
            <span class="color-primary pointer">详情</span>
          </el-tooltip>
        </div>
        <div class="text-f m-b-8">工商面貌</div>
        <div class="text-main">
          <div ref="isDetail" class="isContent text-e line-1">
            <span ref="isFace">
              <span
                v-if="objTable.businessFace"
                v-html="noData(objTable.businessFace)"
              ></span>
              <span v-else class="text-f">企业暂无数据</span>
            </span>
          </div>
          <el-tooltip v-if="isText3" placement="top-start">
            <div
              class="font-size-14 line-height-22"
              slot="content"
              v-html="noData(objTable.businessFace)"
            ></div>
            <span class="color-primary pointer">详情</span>
          </el-tooltip>
        </div>
        <div class="text-f m-b-8">主营项目</div>
        <div class="text-main">
          <div ref="isDetail" class="isContent text-e line-1">
            <span ref="isItem">
              <span
                v-if="objTable.mainProject"
                v-html="objTable.mainProject"
              ></span>
              <span v-else class="text-f">企业暂无数据</span>
            </span>
          </div>
          <el-tooltip
            v-if="isText2"
            placement="top-start"
            width="400"
            trigger="hover"
          >
            <div
              class="font-size-14 line-height-22"
              slot="content"
              v-html="noData(objTable.mainProject)"
            ></div>
            <span class="color-primary pointer">详情</span>
          </el-tooltip>
        </div>
      </div>
    </div>
    <!--    弹窗-->
    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="35%"
      :have-operation="false"
    >
      <p class="text-e line-height-22" v-html="noData(detailStr)"></p>
    </dialog-cmp>
  </div>
</template>

<script>
import {
  getEnterpriseEntEnterInfo,
  getHonor,
  getIntellectual
} from '@/views/manage/business/enterprise/enterprise-basic/companyDetails/api'
import { noData } from '@/filter'
import { parseTime } from '@/utils/tools'
import BasicTab from '@/components/BasicTab'

export default {
  name: 'EnterOverview',
  props: {
    creditCode: {
      type: String,
      default: ''
    }
  },
  components: {
    BasicTab
  },
  data() {
    return {
      objTable: {},
      noData,
      parseTime,
      visible: false,
      detailStr: '',
      title: '在园概况',
      isText1: false,
      isText2: false,
      isText3: false,
      list: [
        {
          label: '基本信息',
          value: 0
        },
        {
          label: '重点指标',
          value: 1
        }
      ],
      current: 0,
      objKnowledge: {},
      objNow: {}
    }
  },
  watch: {
    creditCode() {
      this.getIntellectual()
      this.getHonor()
    }
  },
  created() {
    this.getEnterpriseEntEnterInfo()
  },
  methods: {
    // 资质荣誉
    async getHonor() {
      const res = await getHonor(this.creditCode)
      this.objNow = res
    },
    async getIntellectual() {
      const res = await getIntellectual(this.creditCode)
      this.objKnowledge = res
    },
    async getEnterpriseEntEnterInfo() {
      let id = this.$route.query.id
      const res = await getEnterpriseEntEnterInfo({ id })
      this.objTable = res
      this.$nextTick(() => {
        let isDetail = this.$refs.isDetail.offsetWidth
        let isPark = this.$refs.isPark.offsetWidth
        let isItem = this.$refs.isItem.offsetWidth
        let isFace = this.$refs.isFace.offsetWidth
        if (isPark > isDetail) {
          this.isText1 = true
        } else {
          this.isText1 = false
        }
        if (isItem > isDetail) {
          this.isText2 = true
        } else {
          this.isText2 = false
        }
        if (isFace > isDetail) {
          this.isText3 = true
        } else {
          this.isText3 = false
        }
      })
    },
    openDetail(e) {
      this.visible = true
      if (e === 0) {
        this.title = '在园概况'
        this.detailStr = this.objTable.enterParkInfo
      }
      if (e === 1) {
        this.title = '主营项目'
        this.detailStr = this.objTable.mainProject
      }
      if (e === 2) {
        this.title = '工商面貌'
        this.detailStr = this.objTable.businessFace
      }
    },
    tabsChange(e) {
      this.current = e
      this.getHonor()
      this.getIntellectual()
    }
  }
}
</script>

<style lang="scss" scoped>
.enter-overview-container {
  border-radius: 3px;
  //min-height: 326px;
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
  .text-e {
    color: rgba(0, 0, 0, 0.8);
  }
  .text-f {
    color: rgba(0, 0, 0, 0.4);
  }

  .enter-left {
    width: 600px;
    border-right: 1px solid #f0f0f0;
    .enter-name {
      display: inline-block;
      max-width: 150px;
    }
  }
  .main-wrapper {
    display: flex;
    flex-flow: wrap;
    .main-row {
      width: 50%;
    }
  }
  .enter-right {
    flex: 1;
    min-width: 0;
    line-height: 22px;
    padding-left: 32px;
    font-size: 14px;
    .text-main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #f0f0f0;
      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }
      .isContent {
        width: calc(100% - 28px);
      }
    }
  }
}

:deep(.tabs-wrapper) {
  border: none;
  margin-bottom: 10px;
  .tabs-item {
    height: 30px;
    line-height: initial;
    padding: 0 8px;
    &:first-child {
      margin-right: 10px;
    }
  }
}
</style>
