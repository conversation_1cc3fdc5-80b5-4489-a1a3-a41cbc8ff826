export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          theme: {
            form: 'input',
            label: '填报主题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入填报主题'
              }
            ],
            attrs: {
              maxlength: 35
            }
          },
          type: {
            form: 'select',
            label: '填报类型',
            options: [],
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择填报类型'
              }
            ],
            props: {
              multiple: true
            }
          },
          taskExplain: {
            form: 'input',
            label: '任务说明',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入任务说明'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 500,
              showWordLimit: true
            }
          },
          startEndTime: {
            form: 'dateRange',
            label: '起始时间',
            span: 13,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择起始时间'
              }
            ],
            props: {
              // type: 'datetimerange',
              // format: 'yyyy-MM-dd HH:mm:ss',
              // 'value-format': 'yyyy-MM-dd HH:mm:ss',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              rangeSeparator: '-'
            },
            events: {
              change: this.changeDate
            }
          },
          bufferDate: {
            form: 'date',
            label: '缓冲期延至',
            disabled: false,
            span: 13,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择最晚结束时间（默认缓冲7天）`'
              },
              {
                validator: (rule, value, callback) => {
                  // 缓冲期不能小于结束日期
                  if (value) {
                    let end = this.endTime
                    let endTime = new Date(end).getTime()
                    let val = new Date(value).getTime()
                    if (endTime >= val) {
                      callback(new Error('缓冲期不能小于结束日期'))
                    } else {
                      callback()
                    }
                  }
                }
              }
            ],
            customLabel: () => {
              return (
                <div class={'flex align-items-center'}>
                  <span class={'m-r-8'}>缓冲期延至</span>
                  <el-popover
                    placement="bottom-start"
                    width="200"
                    trigger="hover"
                    content="设置缓冲期便于统计提前、按时、延期填报情况"
                  >
                    <svg-icon
                      slot="reference"
                      class={'color-info'}
                      icon-class="info-circle-filled"
                    />
                  </el-popover>
                </div>
              )
            }
          },
          tags: {
            form: 'radio',
            label: '填报企业',
            span: 13,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择填报企业'
              }
            ],
            options: [
              {
                label: '不限',
                value: 0
              },
              {
                label: '部分',
                value: 1
              }
            ]
          },
          selectEnt: {
            form: 'select',
            label: '按企业标签选择',
            options: [],
            hidden: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择企业标签'
              }
            ],
            props: {
              multiple: true
            }
          }
        }
      }
    }
  }
}
