import { validateCreditCode } from '../../utils/state'
import Vue from 'vue' // vue框架
// 内容
import FormTable from './formTable'
import { validateDecimal } from '@/utils/validate'
Vue.component('FormTable', FormTable)

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          entName: {
            form: 'input',
            label: '企业名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          creditCode: {
            form: 'input',
            label: '统一社会信用代码',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入统一社会信用代码'
              },
              {
                validator: validateCreditCode
              }
            ],
            attrs: {
              maxLength: 20
            }
          },
          entTime: {
            form: 'date',
            label: '入驻时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择入驻时间'
              }
            ]
          },
          contact: {
            form: 'input',
            label: '企业联系人',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入企业联系人'
              }
            ],
            attrs: {
              maxlength: 20
            }
          },
          contactPhone: {
            form: 'input',
            label: '联系方式',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入联系方式'
              }
            ],
            attrs: {
              maxlength: 20
            }
          },
          type: {
            form: 'select',
            label: '孵化进展',
            span: 12,
            options: [
              {
                label: '孵化中',
                value: 1
              },
              {
                label: '已毕业',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择孵化进展'
              }
            ]
          },
          address: {
            form: 'input',
            label: '入驻地点',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入入驻地点'
              }
            ],
            attrs: {
              maxlength: 50
            }
          },
          entRevenueList: {
            form: 'component',
            label: '企业营收和纳税',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请添加企业营收和纳税'
              }
            ],
            componentName: 'FormTable',
            customLabel: () => {
              return (
                <div
                  class="flex justify-content-between align-items-center m-b-8"
                  style={'width: 745px;'}
                >
                  <div class="font-size-14" style={'flex:1;'}>
                    企业营收和纳税
                  </div>
                  <el-button
                    type="text"
                    v-show={this.detailFlag}
                    onClick={() => {
                      this.openCmp()
                    }}
                  >
                    添加
                  </el-button>
                </div>
              )
            },
            events: {
              editCmp: this.editCmp,
              delCmp: this.delCmp
            }
          },
          qualifications: {
            form: 'input',
            label: '相关资质荣誉',
            rule: [
              {
                type: 'string',
                message: '请输入相关资质荣誉'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          coreTechnology: {
            form: 'input',
            label: '核心技术',
            rule: [
              {
                type: 'string',
                message: '请输入核心技术'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 200,
              showWordLimit: true
            }
          },
          mainBusiness: {
            form: 'input',
            label: '主营业务',
            rule: [
              {
                type: 'string',
                message: '请输入主营业务'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 200,
              showWordLimit: true
            }
          },
          attach: {
            form: 'component',
            label: '相关附件',
            span: 12,
            rule: [
              {
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'attachIds'
              },
              mulity: true,
              maxSize: 10,
              limit: 3,
              showDelete: true
            }
          }
        }
      },
      formConfigureEntLog: {
        descriptors: {
          year: {
            form: 'dateRange',
            label: '年份',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择年份'
              },
              {
                validator: (rule, value, callback) => {
                  let showFlag = false
                  if (this.cmpEdit) {
                    let yearList = this.fromModel.entRevenueList.map(item => {
                      return item.year
                    })
                    yearList.splice(this.cmpIdx, 1)
                    yearList.forEach(item => {
                      if (item === value) {
                        showFlag = true
                      }
                    })
                  } else {
                    this.fromModel.entRevenueList.forEach(item => {
                      if (item.year === value) {
                        showFlag = true
                      }
                    })
                  }
                  if (showFlag) {
                    callback(new Error('当前年份记录已存在，不可重复选择'))
                  } else {
                    callback()
                  }
                }
              }
            ],
            props: {
              type: 'year',
              valueFormat: 'yyyy'
            }
          },
          revenue: {
            form: 'input',
            label: '营收（万元）',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入营收（万元）'
              },
              {
                validator: validateDecimal
              }
            ],
            attrs: {
              maxLength: 12
            }
          },
          payTaxes: {
            form: 'input',
            label: '纳税（万元）',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入纳税（万元）'
              },
              {
                validator: validateDecimal
              }
            ],
            attrs: {
              maxLength: 12
            }
          }
        }
      }
    }
  }
}
