export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'name',
          label: '孵化器名称'
        },
        {
          prop: 'address',
          label: '孵化器地址'
        },
        {
          prop: 'inIncubationCount',
          label: '在孵企业数'
        },
        {
          prop: 'graduateCount',
          label: '毕业企业数'
        },
        {
          prop: 'creatorName',
          label: '创建人'
        },
        {
          prop: 'createTime',
          label: '创建时间'
        },
        {
          prop: 'operate',
          label: '操作',
          width: 100,
          render: (h, { row }) => {
            return (
              <div>
                <el-button
                  type={'text'}
                  v-permission={this.routeButtonsPermission.VIEW}
                  onClick={() => {
                    this.openEnter(row)
                  }}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-button>
                <el-dropdown trigger="click">
                  <span
                    v-permission={[
                      ...this.routeButtonsPermission.EDIT,
                      ...this.routeButtonsPermission.DELETE
                    ]}
                    class="color-primary pointer m-l-8"
                  >
                    更多
                  </span>
                  <el-dropdown-menu
                    slot="dropdown"
                    style="width:116px;text-align: center;"
                  >
                    <el-dropdown-item
                      v-permission={this.routeButtonsPermission.EDIT}
                    >
                      <div
                        class="color-warning"
                        onClick={() => {
                          this.editHandle(row)
                        }}
                      >
                        {this.routeButtonsTitle.EDIT}
                      </div>
                    </el-dropdown-item>
                    <el-dropdown-item
                      v-permission={this.routeButtonsPermission.DELETE}
                    >
                      <div
                        class="color-danger"
                        onClick={() => {
                          this.delHandle(row)
                        }}
                      >
                        {this.routeButtonsTitle.DELETE}
                      </div>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ]
    }
  }
}
