<template>
  <div>
    <drive-table ref="drive-table" :table-data="value" :columns="tableColumn">
    </drive-table>
  </div>
</template>

<script>
import ColumnMixin from './column'

export default {
  name: 'FormTable',
  mixins: [ColumnMixin],
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  inject: ['IncubatorEntList'],
  data() {
    return {}
  },
  methods: {
    // 编辑回显
    editCmp(scope) {
      this.$emit('editCmp', scope)
    },
    // 删除
    delCmp(scope) {
      this.$emit('delCmp', scope)
    }
  }
}
</script>

<style scoped></style>
