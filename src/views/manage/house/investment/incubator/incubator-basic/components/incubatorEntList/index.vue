<template>
  <div>
    <basic-card>
      <drive-table
        ref="drive-table"
        :api-fn="getProjectIncubatorEntPage"
        :columns="tableColumn"
        :extral-querys="extralQuerys"
      >
        <template v-slot:operate-left>
          <div
            class="flex align-items-center color-primary pointer"
            @click="openIncubator"
          >
            <svg-icon icon-class="chevron-left" />
            <div class="m-l-4 font-size-14">{{ detailData.name }}</div>
          </div>
        </template>
        <template v-slot:operate-right>
          <el-button
            v-permission="routeButtonsPermission.ADD_ENT"
            type="primary"
            @click="openHandler"
          >
            {{ routeButtonsTitle.ADD_ENT }}
          </el-button>
        </template>
      </drive-table>
    </basic-card>
    <basic-drawer
      :title="title"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
      :haveOperation="detailFlag"
    >
      <driven-form
        v-if="drawerVisible"
        ref="driven-form"
        v-model="fromModel"
        label-position="top"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
    <!--企业营收和纳税弹窗-->
    <dialog-cmp
      :title="titleLog"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form-cmp"
        v-model="fromData"
        label-position="top"
        :formConfigure="formConfigureEntLog"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixins from './descriptor'
import {
  getProjectIncubatorEntCreate,
  getProjectIncubatorEntDelete,
  getProjectIncubatorEntInfo,
  getProjectIncubatorEntPage,
  getProjectIncubatorEntUpdate
} from '@/views/manage/house/investment/incubator/incubator-basic/api'

export default {
  name: 'IncubatorEntList',
  mixins: [ColumnMixin, DescriptorMixins],
  props: {
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      detailFlag: true,
      getProjectIncubatorEntPage,
      visible: false,
      drawerVisible: false,
      title: '添加企业',
      titleLog: '添加企业营收和纳税',
      extralQuerys: {
        recordId: this.detailData.id
      },
      fromModel: {
        entRevenueList: []
      },
      fromData: {},
      cmpIdx: '',
      cmpEdit: false
    }
  },
  provide() {
    return {
      IncubatorEntList: this
    }
  },
  methods: {
    // 返回孵化器
    openIncubator() {
      this.$parent.visible = true
    },
    // 孵化企业 - 查看
    async detailHandle(row) {
      this.title = '查看企业'
      this.detailFlag = false
      const res = await getProjectIncubatorEntInfo(row.id)
      let {
        entName,
        creditCode,
        entTime,
        contact,
        contactPhone,
        type,
        address,
        entRevenueList,
        qualifications,
        coreTechnology,
        mainBusiness,
        attach,
        id
      } = res
      this.fromModel = {
        entName,
        creditCode,
        entTime,
        contact,
        contactPhone,
        type,
        address,
        entRevenueList,
        qualifications,
        coreTechnology,
        mainBusiness,
        attach: attach?.attachIds || [],
        id
      }
      for (const key in this.formConfigure.descriptors) {
        this.$set(this.formConfigure.descriptors[key], 'disabled', true)
      }
      this.formConfigure.descriptors.attach.props.showDelete = false
      this.drawerVisible = true
    },
    // 孵化企业 - 编辑回显
    async editHandle(row) {
      this.title = '编辑企业'
      this.detailFlag = true
      this.formConfigure.descriptors.attach.props.showDelete = true
      for (const key in this.formConfigure.descriptors) {
        this.$set(this.formConfigure.descriptors[key], 'disabled', false)
      }
      const res = await getProjectIncubatorEntInfo(row.id)
      let {
        entName,
        creditCode,
        entTime,
        contact,
        contactPhone,
        type,
        address,
        entRevenueList,
        qualifications,
        coreTechnology,
        mainBusiness,
        attach,
        id
      } = res
      this.fromModel = {
        entName,
        creditCode,
        entTime,
        contact,
        contactPhone,
        type,
        address,
        entRevenueList,
        qualifications,
        coreTechnology,
        mainBusiness,
        attach: attach?.attachIds || [],
        id
      }
      this.drawerVisible = true
    },
    // 孵化企业 - 删除
    delHandle(row) {
      this.$confirm('确定删除该企业？').then(async () => {
        await getProjectIncubatorEntDelete(row.id)
        this.$refs['drive-table'].refreshTable()
        this.$toast.success('删除成功')
      })
    },
    // open - 孵化企业抽屉
    openHandler() {
      this.title = '添加企业'
      this.detailFlag = true
      this.formConfigure.descriptors.attach.props.showDelete = true
      for (const key in this.formConfigure.descriptors) {
        this.$set(this.formConfigure.descriptors[key], 'disabled', false)
      }
      this.fromModel = {
        entRevenueList: []
      }
      this.drawerVisible = true
    },
    // 新增孵化器 - 表单提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let attach = this.fromModel?.attach?.map(item => {
            return item.id
          })
          let data = {
            ...this.fromModel,
            attach,
            recordId: this.detailData.id
          }
          if (this.fromModel.id) {
            await getProjectIncubatorEntUpdate(data)
            this.$toast.success('编辑成功')
          } else {
            await getProjectIncubatorEntCreate(data)
            this.$toast.success('创建成功')
          }
          this.$refs['drive-table'].refreshTable()
          this.drawerVisible = false
        }
      })
    },
    // 企业营收和纳税 - 打开弹窗
    openCmp() {
      if (!this.detailFlag) return false
      this.cmpIdx = ''
      this.cmpEdit = false
      this.titleLog = '添加企业营收和纳税'
      this.fromData = {}
      this.visible = true
    },
    // 企业营收和纳税 - 编辑回显
    editCmp(scope) {
      if (!this.detailFlag) return false
      this.cmpEdit = true
      this.cmpIdx = scope.$index
      this.titleLog = '编辑企业营收和纳税'
      this.fromData = {}
      this.visible = true
      this.fromData = JSON.parse(JSON.stringify(scope.row))
    },
    // 企业营收和纳税 - 删除
    delCmp(scope) {
      if (!this.detailFlag) return false
      this.$confirm('确定删除该数据？').then(() => {
        this.fromModel.entRevenueList.forEach((item, index) => {
          if (index === scope.$index) {
            this.fromModel.entRevenueList.splice(index, 1)
          }
        })
        this.$toast.success('删除成功')
      })
    },
    // 企业营收和纳税 - 弹窗提交
    confirmDialog() {
      this.$refs['driven-form-cmp'].validate(valid => {
        if (valid) {
          if (this.cmpEdit) {
            this.fromModel.entRevenueList[this.cmpIdx] = this.fromData
            this.$toast.success('编辑成功')
          } else {
            this.fromModel.entRevenueList.push(this.fromData)
            this.$toast.success('添加成功')
          }
          this.fromModel.entRevenueList.sort((a, b) => {
            return Number(b.year) - Number(a.year)
          })
          this.visible = false
        }
      })
    }
  }
}
</script>

<style scoped></style>
