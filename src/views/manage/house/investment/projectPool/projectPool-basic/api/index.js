import request from '@/utils/request'

// 获得招商项目设置列表
export function getMerchantConfig(params) {
  return request({
    url: `/merchant/config/list`,
    method: 'get',
    params
  })
}
// 新增编辑招商项目设置
export function merchantConfigCreate(data) {
  return request({
    url: `/merchant/config/create`,
    method: 'post',
    data
  })
}
// 删除招商项目设置
export function merchantConfigDelete(id) {
  return request({
    url: `/merchant/config/delete?id=${id}`,
    method: 'post'
  })
}
// 招商项目设置排序
export function merchantConfigSort(data) {
  return request({
    url: `/merchant/config/sort`,
    method: 'post',
    data,
    isFormData: true
  })
}
// 获得行业类型
export function getIndustry() {
  return request({
    url: '/enterprise/info/search/industry',
    method: 'get'
  })
}
// 获得人员
export function getPerson(params) {
  return request({
    url: '/system/user/listAllSimple',
    method: 'get',
    params
  })
}
// 招商项目创建
export function meRecordCreate(data) {
  return request({
    url: `/me/record/create`,
    method: 'post',
    data
  })
}
// 招商项目编辑
export function meRecordUpdate(data) {
  return request({
    url: `/me/record/update`,
    method: 'post',
    data
  })
}
// 招商项目删除
export function meRecordDelete(id) {
  return request({
    url: `/me/record/delete?id=${id}`,
    method: 'post'
  })
}
// 招商项目锁定解锁
export function meRecordLock(id, sts) {
  return request({
    url: `/me/record/lock?id=${id}&sts=${sts}`,
    method: 'post'
  })
}
// 招商项目列表
export function getMeRecordList(params) {
  return request({
    url: `/me/record/list`,
    method: 'get',
    params
  })
}
// 招商项目详情
export function getMeRecordDetail(params) {
  return request({
    url: `/me/record/detail`,
    method: 'get',
    params
  })
}
// 招商项目基本信息更新并保留记录
export function updateMeRecordBasic(data) {
  return request({
    url: `/me/record/updateBasic`,
    method: 'post',
    data
  })
}
// 日志记录
export function getMerchantLogList(params) {
  return request({
    url: `/merchant/log/list`,
    method: 'get',
    params
  })
}
// 联系人列表
export function getMerchantMeContactList(params) {
  return request({
    url: `/merchant/me-contact/list`,
    method: 'get',
    params
  })
}
// 联系人姓名查询
export function getMerchantMeContactName(params) {
  return request({
    url: `/merchant/me-contact/get_contact_by_name`,
    method: 'get',
    params
  })
}
// 联系人联系方式是否存在
export function getMerchantMeContactPhone(params) {
  return request({
    url: `/merchant/me-contact/get_contact_by_phone`,
    method: 'get',
    params
  })
}
// 新增跟进
export function merchantLogCreateFollow(data) {
  return request({
    url: `/merchant/log/create_follow`,
    method: 'post',
    data
  })
}
// 新增匹配记录
export function merchantLogCreateMate(data) {
  return request({
    url: `/merchant/log/create_mate`,
    method: 'post',
    data
  })
}
// 新增联系人身份
export function merchantMeContactCreate(data) {
  return request({
    url: `/merchant/me-contact/create`,
    method: 'post',
    data
  })
}
// 导入
export function importRecord(attachId) {
  return request({
    url: `/me/record/importRecord?attachId=${attachId}`,
    method: 'post'
  })
}
// 导入模板
export function getImportTemplate() {
  return `${process.env.VUE_APP_URL_PREFIX}/me/record/download`
}
// 导出
export function meRecordExport() {
  return `${process.env.VUE_APP_URL_PREFIX}/me/record/export`
}
// 入园
export function meRecordEnterPark(data) {
  return request({
    url: `/me/record/enter_park`,
    method: 'post',
    data
  })
}
// 通过手机号检查
export function meRecordCheckPhone(params) {
  return request({
    url: `/me/record/check_phone`,
    method: 'get',
    params
  })
}
// 社会信用代码检查
export function meRecordCheckCode(params) {
  return request({
    url: `/wk/compliance_record/check_code`,
    method: 'get',
    params
  })
}
