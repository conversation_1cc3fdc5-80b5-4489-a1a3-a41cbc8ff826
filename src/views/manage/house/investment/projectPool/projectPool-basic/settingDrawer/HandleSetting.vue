<template>
  <dialog-cmp
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="30%"
    @confirmDialog="confirmDialog"
  >
    <driven-form
      v-if="dialogVisible"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
  </dialog-cmp>
</template>

<script>
import DescriptorMixins from './descriptor'
import { merchantConfigCreate } from '../api'

export default {
  name: 'HandleSetting',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dialogTitle: {
      type: String,
      default: '添加'
    },
    current: {
      type: Number,
      default: 1
    }
  },
  mixins: [DescriptorMixins],
  data() {
    return {
      dialogVisible: false,
      fromModel: {}
    }
  },
  inject: ['ProjectPool'],
  watch: {
    current(val) {
      if (val === 3 || val === 5) {
        this.$set(this.fromModel, 'colorLevel', '#ed7b2f')
        this.formConfigure.descriptors.colorLevel.hidden = false
      } else {
        this.formConfigure.descriptors.colorLevel.hidden = true
      }
    },
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.fromModel = {}
        this.$set(this.fromModel, 'colorLevel', '#ed7b2f')
      }
      this.$emit('update:visible', val)
    }
  },
  methods: {
    editHandle(row) {
      this.$set(this.fromModel, 'id', row.id)
      this.$set(this.fromModel, 'typeName', row.typeName)
      this.$set(this.fromModel, 'colorLevel', row.colorLevel || '#ed7b2f')
    },
    // 新增编辑
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return
        const { id } = this.fromModel
        const type = this.current
        const params = {
          ...this.fromModel,
          type
        }
        merchantConfigCreate(params).then(() => {
          this.operateTips(id ? '编辑' : '添加')
        })
      })
    },
    operateTips(text) {
      if (text) this.$toast.success(`${text}成功`)
      this.ProjectPool.initEnums()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped></style>
