<template>
  <div class="w100 card-container" @click="detailHandle">
    <div class="flex justify-content-between align-items-center">
      <el-tooltip
        v-if="item.enterSts === 1 || item.enterSts === 2"
        effect="dark"
        :content="item.enterSts === 1 ? '入园办理中' : '已入园'"
        placement="bottom"
      >
        <div class="card-title font-size-16 line-1">
          <span
            class="status-tips"
            :class="{
              'park-in': item.enterSts === 1,
              'park-already': item.enterSts === 2
            }"
          ></span>
          <span class="m-l-4">{{ item.enterpriseName }}</span>
        </div>
      </el-tooltip>
      <div
        v-else
        class="card-title font-size-16 line-1"
        :class="{
          'park-in': item.enterSts === 1,
          'park-already': item.enterSts === 2
        }"
      >
        <span
          class="status-tips"
          :class="{
            'park-in': item.enterSts === 1,
            'park-already': item.enterSts === 2
          }"
        ></span>
        <span class="m-l-4">{{ item.enterpriseName }}</span>
      </div>
      <div class="flex align-items-center line-height-24">
        <svg-icon v-if="item.lockSts" icon-class="lock" class="font-size-14" />
        <more-operate class="more-operate" :item="item" />
      </div>
    </div>
    <div
      v-if="(item.pjctDiffName || item.pjctSourceName) !== '未分类'"
      class="flex flex-wrap m-t-8"
    >
      <el-tooltip effect="dark" :content="'项目难度'" placement="bottom">
        <basic-tag
          v-if="
            item.pjctDiffName && item.pjctDiffName !== '未分类' && groupId !== 3
          "
          class="m-r-8"
          :class="{ 'dynamic-color': item.diffColor }"
          :style="{
            '--fill-background': item.diffColor
          }"
          type="primary"
          :label="item.pjctDiffName"
        />
      </el-tooltip>
      <el-tooltip effect="dark" :content="'项目来源'" placement="bottom">
        <basic-tag
          v-if="
            item.pjctSourceName &&
            item.pjctSourceName !== '未分类' &&
            groupId !== 1
          "
          type="primary"
          :label="item.pjctSourceName"
        />
      </el-tooltip>
    </div>
    <div
      class="m-t-20 card-bottom flex align-items-center justify-content-between"
    >
      <div class="card-bottom-item flex align-items-center">
        <el-tooltip effect="dark" :content="'最近更新时间'" placement="bottom">
          <div class="flex align-items-center">
            <svg-icon icon-class="calendar" />
            <span class="m-l-4 font-size-14 color-text-secondary">{{
              item.updateTime
            }}</span>
          </div>
        </el-tooltip>
        <el-tooltip effect="dark" :content="'跟进记录'" placement="bottom">
          <div class="card-bottom-item flex align-items-center pos-relative">
            <svg-icon icon-class="guidance" class-name="m-l-16" />
            <span v-if="item.followCount" class="new-tips"></span>
            <span class="m-l-4 font-size-14 color-text-secondary">
              {{ item.followCount > 99 ? '99+' : item.followCount }}
            </span>
          </div>
        </el-tooltip>
        <el-tooltip effect="dark" :content="'项目进展'" placement="bottom">
          <div class="card-bottom-item flex align-items-center">
            <svg-icon icon-class="link" class-name="m-l-16 font-size-14" />
            <span class="m-l-4 font-size-14 color-text-secondary">{{
              pjctProcessNameSub(item.pjctProcessName)
            }}</span>
          </div>
        </el-tooltip>
      </div>
      <el-tooltip
        effect="dark"
        :content="'负责人：' + item.headPersonName"
        placement="bottom"
      >
        <div>
          <img
            class="user-img"
            v-if="item.headPersonAvatar"
            :src="item.headPersonAvatar"
            alt=""
          />
          <div v-else class="user-name">
            {{ item.headPersonWord }}
          </div>
        </div>
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import MoreOperate from './MoreOperate'
export default {
  name: 'Card',
  components: { MoreOperate },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    isLast: {
      type: Boolean,
      default: false
    },
    typeIndex: {
      type: Number,
      default: 0
    },
    selfIndex: {
      type: Number,
      default: 0
    },
    groupId: {
      type: Number,
      default: -1
    }
  },
  methods: {
    pjctProcessNameSub(name) {
      if (!name) return ''
      if (name.length > 6) {
        const subName = name.slice(0, 5)
        return subName + '...'
      } else {
        return name
      }
    },
    detailHandle() {
      const row = {
        ...this.item,
        typeIndex: this.typeIndex,
        selfIndex: this.selfIndex
      }
      this.$emit('detailHandle', row)
    },
    parkInHandle(row) {
      if (row.lockSts) return false
      if (this.item.enterSts === 1 || this.item.enterSts === 2) return false
      this.$emit('parkInHandle', this.item)
    },
    angleTagType(type) {
      if (!type) return 'primary'
      const arr = ['warning', 'success', 'danger']
      return arr[type - 1]
    },
    angleTagText(type) {
      if (!type) return '办理入园'
      const arr = ['入园中', '已入园', '入园拒绝']
      return arr[type - 1]
    }
  }
}
</script>

<style scoped lang="scss">
.card-container {
  border-radius: 4px;
  margin-bottom: 8px;
  padding: 24px;
  @include background_color(--color-white);
  cursor: default;
  display: flex;
  flex-direction: column;
  .tag-tips-mark {
    @include font_color_mix(--color-warning, #fff, 70%);
  }
  .card-title {
    width: fit-content;
    @include font_color_mix(--color-black, #fff, 10%);
    .status-tips {
      display: inline-block;
      width: 6px;
      height: 6px;
      border-radius: 50%;
      margin-bottom: 3px;
      @include background_color(--color-primary);
      &.park-in {
        @include background_color(--color-warning);
      }
      &.park-already {
        @include background_color(--color-success);
      }
    }
  }
  .dynamic-color {
    :deep(.el-tag) {
      background: var(--fill-background);
      color: #fff;
    }
  }
  .card-bottom {
    @include font_color(--color-black);
    .card-bottom-item {
      flex-shrink: 0;
      .new-tips {
        display: inline-block;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        @include background_color(--color-danger);
        position: absolute;
        top: 0;
        left: 30px;
      }
    }
    .user-img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
    }
    .user-name {
      width: 32px;
      height: 32px;
      text-align: center;
      line-height: 32px;
      font-size: 12px;
      @include font_color(--color-white);
      @include background_color(--color-primary);
      border-radius: 50%;
      overflow: hidden;
      flex-shrink: 0;
    }
  }
}
</style>
