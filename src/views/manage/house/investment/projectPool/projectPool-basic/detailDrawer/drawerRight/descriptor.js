import DiffSelect from './DiffSelect'

export default {
  components: { DiffSelect },
  data() {
    return {
      formConfigure: {
        labelWidth: '90px',
        descriptors: {
          industry: {
            form: 'select',
            label: '行业类型',
            dynamicDisabled: true,
            rule: [
              {
                type: 'string',
                message: '请选择行业类型'
              }
            ],
            attrs: {
              filterable: true
            },
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          headPerson: {
            form: 'select',
            label: '负责人',
            dynamicDisabled: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择负责人'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          joinPerson: {
            form: 'select',
            label: '参与人',
            dynamicDisabled: true,
            rule: [
              {
                type: 'array',
                message: '请选择参与人'
              }
            ],
            attrs: {
              multiple: true,
              clearable: true
            },
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          creatorName: {
            form: 'input',
            label: '添加人',
            disabled: true
          },
          createTime: {
            form: 'input',
            label: '添加时间',
            disabled: true
          },
          updateTime: {
            form: 'input',
            label: '更新时间',
            disabled: true
          },
          source: {
            form: 'select',
            label: '项目来源',
            dynamicDisabled: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目来源'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          group: {
            form: 'select',
            label: '项目分组',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目分组'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          diff: {
            form: 'component',
            label: '项目难度',
            dynamicDisabled: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目难度'
              }
            ],
            options: [],
            render: () => {
              return (
                <DiffSelect
                  v-model={this.formModel.diff}
                  options={this.formConfigure.descriptors.diff.options}
                  disabled={this.formConfigure.descriptors.diff.disabled}
                  onChange={() => {
                    this.updateHandle()
                  }}
                />
              )
            }
          },
          process: {
            form: 'select',
            label: '项目进展',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目进展'
              }
            ],
            options: [],
            events: {
              change: e => {
                this.processColorHandle(e)
                this.updateHandle()
              }
            }
          }
        }
      }
    }
  }
}
