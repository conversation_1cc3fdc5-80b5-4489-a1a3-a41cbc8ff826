<template>
  <div class="left-body">
    <el-radio-group v-model="tabModel" @change="radioChange">
      <el-radio-button v-for="item in tabs" :key="item.key" :label="item.key">{{
        item.label
      }}</el-radio-button>
    </el-radio-group>
    <template v-if="tabModel === 1 || tabModel === 2">
      <basic-tab
        :tabsData="tabsData"
        :current="current"
        @tabsChange="tabsChange"
      />
      <div class="timeline-container">
        <template v-if="dataList.length">
          <div
            class="timeline-item"
            v-for="(item, index) in dataList"
            :key="index"
          >
            <div class="timeline-left">
              <div class="item-icon">{{ item.creatorName.charAt(0) }}</div>
              <div class="item-line"></div>
            </div>
            <div class="timeline-right">
              <div class="timeline-header">
                <div class="flex align-items-center">
                  <span class="font-size-14 header-name line-1">{{
                    item.creatorName
                  }}</span>
                  <span class="m-l-12">{{ item.createTime }}</span>
                  <span class="m-l-12">{{ item.typeName }}</span>
                </div>
              </div>
              <div class="timeline-content">
                <div :class="{ 'color-info': item.type === 3 }">
                  {{ item.content }}
                </div>
                <div class="annex-wrapper">
                  <files-list :files="getAttach(item.attachMap)" onlyForView />
                </div>
                <div
                  v-if="tabModel === 1 && item.contactName"
                  class="m-t-10 font-size-12"
                >
                  联系人：{{ item.contactName }}
                </div>
              </div>
            </div>
          </div>
        </template>
        <empty-data v-else class="p-t-32 m-t-32" />
      </div>
    </template>
    <div v-else class="timeline-container contact-timeline-container">
      <template v-if="contactList.length">
        <div
          class="timeline-item"
          v-for="(item, index) in contactList"
          :key="index"
        >
          <div class="timeline-left">
            <div class="item-icon">{{ item.creatorName.charAt(0) }}</div>
            <div class="item-line"></div>
          </div>
          <div class="timeline-right">
            <div class="timeline-header">
              <div class="flex align-items-center">
                <span class="font-size-14 header-name line-1">{{
                  item.contact
                }}</span>
                <span class="m-l-12">{{ item.contactPhone }}</span>
                <span class="m-l-12">{{ item.labelName }}</span>
                <el-link
                  type="primary"
                  :underline="false"
                  class="copy-text font-size-12 m-l-12"
                  @click="copyHandle(item, $event)"
                  >复制</el-link
                >
              </div>
            </div>
            <div class="timeline-content">
              <div>{{ item.specificContent }}</div>
              <div class="m-t-10 font-size-12">
                <span>{{ item.creatorName }}</span>
                <span class="p-l-4 p-r-4">{{ item.createTime }}</span>
                <span>添加</span>
              </div>
            </div>
          </div>
        </div>
      </template>
      <empty-data v-else class="p-t-32 m-t-32" />
    </div>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import { getMerchantMeContactList, getMerchantLogList } from '../../../api'
import FilesList from '@/components/Uploader/files'
import handleClipboard from '@/utils/clipboard'
export default {
  name: 'LeftBody',
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  components: { FilesList, BasicTab },
  inject: ['DetailDrawerLeft', 'ProjectPool'],
  data() {
    return {
      tabModel: 1,
      tabs: [
        {
          label: '项目跟进',
          key: 1
        },
        {
          label: '项目匹配',
          key: 2
        },
        {
          label: '项目联系人',
          key: 3
        }
      ],
      current: 0,
      followUpTabs: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '跟进',
          value: 1
        },
        {
          label: '动态',
          value: 3
        }
      ],
      dataList: [],
      contactList: [] // 联系人日志列表
    }
  },
  computed: {
    tabsData() {
      let type = this.followUpTabs
      if (this.tabModel === 1) {
        type = this.followUpTabs
      } else if (this.tabModel === 2) {
        type = this.matching
      }
      return type
    },
    // 项目匹配
    matching() {
      const list = this.ProjectPool.settingTabs[3].list || []
      const arr = list.map(item => {
        return {
          label: item.typeName,
          value: item.id
        }
      })
      arr.unshift({
        label: '全部',
        value: 0
      })
      return arr
    }
  },
  watch: {
    detailInfo: {
      handler() {
        this.getMerchantLogList()
      },
      deep: true
    }
  },
  methods: {
    copyHandle(item, e) {
      const contactText = item.contact ? `联系人姓名：${item.contact}` : ''
      const contactPhoneText = item.contactPhone
        ? `，联系方式：${item.contactPhone}`
        : ''
      const labelNameText = item.labelName
        ? `，相关身份标签：${item.labelName}`
        : ''
      const text = contactText + contactPhoneText + labelNameText
      handleClipboard(text, e)
    },
    unfoldHandle(row, index) {
      this.$set(this.dataList[index], 'unfold', !row.unfold)
    },
    radioChange(e) {
      this.current = 0
      this.dataList = []
      this.DetailDrawerLeft.$refs.leftFooter.initData(e)
      this.getMerchantLogList()
    },
    tabsChange(e) {
      this.current = e
      this.getMerchantLogList()
    },
    getAttach(attachMap = {}) {
      return attachMap.investment || []
    },
    getMerchantMeContactList() {
      getMerchantMeContactList({ recordId: this.detailInfo.id }).then(res => {
        this.contactList = res || []
      })
    },
    getMerchantLogList() {
      const types = []
      let typeId = ''
      if (this.tabModel === 1) {
        if (this.current === 0) {
          this.followUpTabs.forEach(item => {
            item.value && types.push(item.value)
          })
        } else {
          types.push(this.current)
        }
      } else if (this.tabModel === 2) {
        types.push(this.tabModel)
        typeId = this.current ? this.current : ''
      } else {
        return this.getMerchantMeContactList()
      }
      const params = {
        recordId: this.detailInfo.id,
        types: types.toString(),
        // typeId
      }
      getMerchantLogList(params).then(res => {
        this.dataList = res || []
      })
    }
  }
}
</script>

<style scoped lang="scss">
.left-body {
  margin-top: 20px;
  :deep(.el-radio-button) {
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
    .el-radio-button__inner {
      border: 0;
      border-radius: 4px;
      font-size: 14px;
      color: #292929;
    }
    &.is-active {
      .el-radio-button__inner {
        color: #fff;
      }
    }
    &:not(.is-active):hover {
      .el-radio-button__inner {
        background: #f2f5f7;
      }
    }
  }
  :deep(.tabs-wrapper) {
    margin-top: 8px;
    .tabs-item {
      height: 32px;
      line-height: 32px;
      &::after {
        display: block;
        content: '';
        width: 1px;
        height: 16px;
        background: #e9edf0;
        position: absolute;
        right: 0;
        top: 8px;
      }
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        &::after {
          display: none;
        }
      }
      &.active::before {
        display: none;
      }
    }
  }
  .timeline-container {
    margin-top: 12px;
    .timeline-item {
      position: relative;
      .timeline-left {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        .item-icon {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          overflow: hidden;
          text-align: center;
          line-height: 30px;
          font-size: 12px;
          @include background_color(--color-primary);
          @include font_color(--color-white);
          position: absolute;
          left: 0;
        }
        .item-line {
          width: 1px;
          height: calc(100% - 38px);
          @include background_color(--border-color-base);
          position: absolute;
          top: 30px;
          left: 15px;
        }
      }
      .timeline-right {
        margin-left: 32px;
        padding-right: 10px;
        border-radius: 4px;
        cursor: pointer;
        overflow: hidden;
        transition: all 0.3s ease-in-out;
        &:hover {
          background: #f7f7f7;
        }
        .timeline-header {
          margin-top: 10px;
          margin-left: 10px;
          font-size: 12px;
          color: #adadad;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .header-name {
            max-width: 200px;
          }
          .copy-text {
            display: none;
          }
          &:hover {
            .copy-text {
              display: inline-block;
            }
          }
        }
        .timeline-content {
          font-size: 14px;
          padding: 12px 10px 24px;
          color: #404040;
          line-height: 20px;
          word-wrap: break-word;
          .annex-wrapper {
            padding-top: 10px;
          }
        }
      }
    }
    &.contact-timeline-container {
      .timeline-header {
        color: #404040 !important;
      }
      .timeline-content {
        color: #adadad !important;
      }
    }
  }
}
</style>
