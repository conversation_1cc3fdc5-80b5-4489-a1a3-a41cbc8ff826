<template>
  <el-autocomplete
    class="w100"
    v-model="modelObj.contact"
    :fetch-suggestions="querySearch"
    :debounce="500"
    placeholder="请输入联系人姓名"
    @select="handleSelect"
    @input="handleChange"
    :popper-append-to-body="false"
  >
    <template slot-scope="{ item }">
      <div class="line-1" v-html="item.text"></div>
    </template>
  </el-autocomplete>
</template>

<script>
import { getMerchantMeContactName } from '@/views/manage/house/investment/projectPool/projectPool-basic/api'

export default {
  name: 'AutocompleteCom',
  props: {
    value: {
      required: true
    }
  },
  data() {
    return {
      modelObj: {
        id: '',
        contact: '',
        contactPhone: ''
      }
    }
  },
  watch: {
    value(val) {
      if (!val) return false
      this.modelObj = JSON.parse(val)
    }
  },
  methods: {
    async querySearch(queryString, cb) {
      if (!queryString) return cb([])
      const res = await getMerchantMeContactName({ name: queryString })
      const list = res || []
      list.forEach(item => {
        const text = `联系人姓名：${item.contact}，联系方式：${
          item.contactPhone || '-'
        }，相关身份标签：${item.labelName || '-'}`
        item.text = text.replace(
          queryString,
          `<span class="color-primary font-strong">${queryString}</span>`
        )
      })
      cb(list)
    },
    handleChange(e) {
      this.modelObj.id = ''
      const model = e ? JSON.stringify(this.modelObj) : ''
      this.$emit('input', model)
    },
    handleSelect(item) {
      this.modelObj.id = item.id
      this.modelObj.contact = item.contact
      this.modelObj.contactPhone = item.contactPhone
      this.$emit('input', JSON.stringify(this.modelObj))
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-autocomplete-suggestion) {
  width: 600px !important;
  .el-autocomplete-suggestion__wrap {
    max-height: 240px !important;
  }
}
</style>
