<template>
  <div class="drawer-body-right min-h100">
    <div class="right-title">项目基础信息</div>
    <driven-form
      ref="driven-form"
      v-model="formModel"
      :formConfigure="formConfigure"
      label-position="left"
    />
    <div class="right-statistics">
      <div class="statistics-item">
        <span class="item-label">联系人</span>
        <span class="item-content color-primary"
          >{{ detailInfo.contactCount || 0 }}个</span
        >
      </div>
      <div class="statistics-item">
        <span class="item-label">跟进记录</span>
        <span class="item-content color-primary"
          >{{ detailInfo.followCount || 0 }}条</span
        >
      </div>
      <div class="statistics-item">
        <span class="item-label">项目匹配</span>
        <span class="item-content color-primary"
          >{{ detailInfo.mateCount || 0 }}项</span
        >
      </div>
    </div>
  </div>
</template>

<script>
import DescriptorMixin from './descriptor'

export default {
  name: 'DetailDrawerRight',
  mixins: [DescriptorMixin],
  props: {
    settingTabs: {
      type: Array,
      default: () => []
    },
    industry: {
      type: Array,
      default: () => []
    },
    personList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      formModel: {},
      detailInfo: {}
    }
  },
  inject: ['ProjectPool', 'DetailDrawer'],
  watch: {
    personList: {
      handler(val) {
        this.formConfigure.descriptors.headPerson.options = val || []
        this.formConfigure.descriptors.joinPerson.options = val || []
      },
      deep: true,
      immediate: true
    },
    industry: {
      handler(val) {
        this.formConfigure.descriptors.industry.options = val || []
      },
      deep: true,
      immediate: true
    },
    settingTabs: {
      handler(val) {
        this.formConfigure.descriptors.source.options = val[0].list.map(
          item => {
            return {
              label: item.typeName,
              value: item.id
            }
          }
        )
        this.formConfigure.descriptors.group.options = val[1].list.map(item => {
          return {
            label: item.typeName,
            value: item.id
          }
        })
        this.formConfigure.descriptors.diff.options = val[2].list.map(item => {
          return {
            label: item.typeName,
            value: item.id,
            colorLevel: item.colorLevel
          }
        })
        this.formConfigure.descriptors.process.options = val[4].list.map(
          item => {
            return {
              label: item.typeName,
              value: item.id,
              colorLevel: item.colorLevel
            }
          }
        )
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    processColorHandle(e) {
      const colorLevel =
        this.formConfigure.descriptors.process.options.find(
          item => item.value === e
        )?.colorLevel || '#ed7b2f'
      const dom = document.querySelector('.processKey .el-input__inner')
      dom.style.background = colorLevel || '#ed7b2f'
    },
    initData(row) {
      this.detailInfo = row || {}
      for (const key in this.formConfigure.descriptors) {
        if (this.formConfigure.descriptors[key].dynamicDisabled) {
          this.$set(
            this.formConfigure.descriptors[key],
            'disabled',
            row.lockSts
          )
        }
      }
      this.$set(this.formModel, 'id', row.id)
      this.$set(this.formModel, 'industry', row.industry)
      this.$set(this.formModel, 'headPerson', row.headPerson)
      this.$set(this.formModel, 'joinPerson', row.joinPerson)
      this.$set(this.formModel, 'creatorName', row.creatorName)
      this.$set(this.formModel, 'createTime', row.createTime)
      this.$set(this.formModel, 'updateTime', row.updateTime)
      this.$set(this.formModel, 'source', row.pjctSource)
      this.$set(this.formModel, 'group', row.pjctGroup)
      this.$set(this.formModel, 'diff', row.pjctDiff)
      this.$set(this.formModel, 'process', row.pjctProcess)
      this.processColorHandle(row.pjctProcess)
    },
    updateHandle() {
      const params = {
        ...this.formModel,
        recordId: this.formModel.id
      }
      this.ProjectPool.updateMeRecordBasic(params, () => {
        this.DetailDrawer.$refs.detailDrawerLeft.$refs.leftBody.getMerchantLogList()
        this.DetailDrawer.getMeRecordDetail(this.detailInfo.id)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.drawer-body-right {
  width: 30%;
  padding: 16px 0 0 24px;
  .right-title {
    margin-top: 4px;
    margin-bottom: 16px;
    font-size: 14px;
    color: #3a3c41;
    line-height: 22px;
  }
  .right-statistics {
    border-top: 1px solid;
    @include border_color(--border-color-base);
    padding-top: 10px;
    .statistics-item {
      display: flex;
      color: #7d8089;
      line-height: 40px;
      height: 40px;
      font-size: 14px;
      margin-bottom: 8px;
      .item-label {
        width: 90px;
      }
      .item-content {
        width: calc(100% - 90px);
        padding: 0 15px;
      }
    }
  }
}
:deep(.el-form) {
  .is-required {
    .el-form-item__label {
      &::before {
        display: none;
      }
      &::after {
        display: inline-block;
        content: '*';
        color: #e34d59;
        margin-left: 4px;
      }
    }
  }
  .el-form-item__label {
    color: #7d8089 !important;
    line-height: 40px;
    height: 40px;
  }
  .custom-tips {
    display: none;
  }
  .el-input {
    line-height: 40px;
    min-height: 40px;
    .el-input__inner {
      border-color: transparent;
    }
    .el-input__suffix {
      display: none;
      top: -2px;
    }
    &.is-disabled {
      .el-input__inner {
        background: transparent;
        border-color: transparent !important;
      }
    }
  }
  .el-form-item__content:hover {
    .el-input {
      &:not(.is-disabled) {
        .el-input__inner {
          border-color: #e8e8e8;
        }
        .el-input__suffix {
          display: block;
        }
      }
    }
  }
  .processKey {
    .el-input {
      width: 100px;
      .el-input__inner {
        height: 24px;
        line-height: 24px;
        color: #fff;
        background: #ed7b2f;
        font-size: 12px;
        border: none !important;
      }
      .el-input__suffix {
        display: block;
      }
    }
  }
}
</style>
