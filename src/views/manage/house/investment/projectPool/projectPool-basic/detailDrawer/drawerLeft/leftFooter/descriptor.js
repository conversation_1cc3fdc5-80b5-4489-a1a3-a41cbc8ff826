import AutocompleteCom from './AutocompleteCom'

export default {
  components: { AutocompleteCom },
  data() {
    return {
      followUpConfigure: {
        descriptors: {
          followType: {
            form: 'select',
            label: '跟进方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择跟进方式'
              }
            ],
            options: []
          },
          contact: {
            form: 'select',
            label: '联系人',
            span: 12,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择联系人'
              }
            ],
            attrs: {
              noDataText: '请先添加项目联系人',
              clearable: true
            },
            options: []
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                required: false,
                message: '请上传相关附件',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'investment'
              },
              mulity: true,
              maxSize: 10,
              limit: 3
            }
          },
          content: {
            form: 'input',
            label: '具体内容',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入具体内容'
              }
            ],
            attrs: {
              type: 'textarea',
              maxLength: 500,
              rows: 6
            }
          }
        }
      },
      matchingConfigure: {
        descriptors: {
          mateType: {
            form: 'select',
            label: '匹配类型',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择匹配类型'
              }
            ],
            options: []
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                required: false,
                message: '请上传相关附件',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'investment'
              },
              mulity: true,
              maxSize: 10,
              limit: 3
            }
          },
          content: {
            form: 'input',
            label: '具体内容',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入具体内容'
              }
            ],
            attrs: {
              type: 'textarea',
              maxLength: 500,
              rows: 6
            }
          }
        }
      },
      contactPersonConfigure: {
        descriptors: {
          label: {
            form: 'select',
            label: '身份标签',
            options: [],
            props: {
              multiple: true,
              filterable: true,
              allowCreate: true,
              defaultFirstOption: true
            },
            rule: [
              {
                required: false,
                type: 'array',
                message: '请选择身份标签'
              }
            ]
          },
          contactName: {
            form: 'component',
            label: '联系人姓名',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人姓名'
              }
            ],
            render: () => {
              return <autocomplete-com v-model={this.formModel.contactName} />
            },
            attrs: {
              maxLength: 10
            }
          },
          contactPhone: {
            form: 'input',
            label: '联系方式',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入联系方式'
              }
            ],
            events: {
              blur: () => {
                this.contactPhoneBlur()
              },
              input: e => {
                this.contactPhoneInput(e)
              }
            },
            attrs: {
              maxLength: 20
            },
            customTips: () => {
              return (
                this.hasContactPhone && (
                  <div class={'flex flex-wrap color-warning'}>
                    <span>
                      您输入的联系方式已存在联系人
                      {this.contactInfo.contact}，
                    </span>
                    <el-link
                      class={'font-size-12'}
                      type={'primary'}
                      onClick={() => {
                        this.selectContactHandle()
                      }}
                    >
                      点击选择此人
                    </el-link>
                    <span>或重新输入联系方式</span>
                  </div>
                )
              )
            }
          },
          specificContent: {
            form: 'input',
            label: '联系人简介',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人简介'
              }
            ],
            attrs: {
              type: 'textarea',
              maxLength: 500,
              rows: 6
            }
          }
        }
      }
    }
  }
}
