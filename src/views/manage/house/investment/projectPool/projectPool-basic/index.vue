<template>
  <div class="project-pool-container flex wh100 overflow-hidden">
    <basic-card :is-title="false">
      <div class="header-tabs flex flex-center-between">
        <basic-tab
          :tabsData="tabsData"
          :current="current"
          @tabsChange="tabsChange"
        />
        <el-button
          v-permission="routeButtonsPermission.ADD"
          type="primary"
          @click="addHandle"
        >
          <svg-icon icon-class="add" /> <span>{{ routeButtonsTitle.ADD }}</span>
        </el-button>
      </div>
      <div class="header-tabs flex justify-content-between">
        <div class="flex align-items-center">
          <div class="input-container">
            <el-input
              v-model="enterpriseName"
              placeholder="搜索项目标题（按Enter搜索）"
              @keyup.enter.native="getData"
              clearable
              @change="enterpriseNameChange"
            >
              <i slot="prefix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </div>
          <span class="vertical"></span>
          <el-button
            v-permission="routeButtonsPermission.EMPLACE"
            @click="settingVisible = true"
          >
            <svg-icon icon-class="setting" />
            <span>{{ routeButtonsTitle.EMPLACE }}</span>
          </el-button>
          <el-button
            v-permission="routeButtonsPermission.LEADING_OUT"
            @click="exportHandle"
          >
            <i class="el-icon-download font-size-16"></i>
            <span>{{ routeButtonsTitle.LEADING_OUT }}</span>
          </el-button>
          <el-button
            v-permission="routeButtonsPermission.LEADING_IN"
            @click="$refs.upload.dialogVisible = true"
          >
            <i class="el-icon-upload2 font-size-16"></i>
            <span>批量{{ routeButtonsTitle.LEADING_IN }}</span>
          </el-button>
          <el-button @click="groupClick">
            <svg-icon icon-class="app" />
            <span>{{ currentGroupName || '卡片分类' }}</span>
          </el-button>
          <el-select
            ref="groupSelect"
            v-model="groupId"
            class="group-select"
            popper-class="group-select-popper"
            @change="groupChange"
          >
            <el-option
              v-for="item in settingTabs.filter(
                item => item.value !== 4 && item.value !== 6
              )"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="flex align-items-center">
          <div class="flex font-size-14 color-text-secondary">
            <div class="flex align-items-center">
              <span class="color-block no-park"></span>
              <span>未入园</span>
            </div>
            <div class="flex align-items-center m-l-16">
              <span class="color-block park-in"></span>
              <span>入园办理中</span>
            </div>
            <div class="flex align-items-center m-l-16">
              <span class="color-block park-already"></span>
              <span>已入园</span>
            </div>
          </div>
          <div class="project-total m-l-32">
            当前共有
            <span class="total-num">{{ currentTotal }}</span>
            个项目
          </div>
        </div>
      </div>
      <div
        class="type-container flex"
        @scroll="handleScroll($event)"
        ref="typeContainerRef"
        :key="typeContainerKey"
      >
        <div
          v-for="(item, index) in boardList"
          :key="item.id"
          class="type-item-container"
          :class="{ collapse: item.isCollapse }"
        >
          <div class="type-item-title lock">
            <div class="title-container">
              <div class="title-wrapper flex align-items-center">
                <div class="title-name" :class="{ 'line-1': !item.isCollapse }">
                  {{ item.name }}
                </div>
                <span class="type-item-num color-primary">{{
                  item.total
                }}</span>
              </div>
              <svg-icon
                v-if="!item.isCollapse"
                icon-class="unfold-less"
                @click="collapseHandle(index, item)"
              />
              <svg-icon
                v-else
                icon-class="unfold-more"
                @click="collapseHandle(index, item)"
              />
            </div>
            <el-progress
              v-if="!item.isCollapse"
              :percentage="
                index === boardList.length - 1
                  ? lastPercentage(item.total)
                  : item.percentage
              "
            ></el-progress>
          </div>
          <div class="type-item-body" :id="'typeBox' + item.id" :type="item.id">
            <template v-if="!item.isCollapse">
              <card
                v-for="(row, idx) in item.list"
                :key="row.id"
                :class="{ lock: row.lockSts }"
                :item="row"
                :selfId="row.id"
                :isLast="index === boardList.length - 1"
                :typeIndex="index"
                :selfIndex="idx"
                @detailHandle="detailHandle"
                @parkInHandle="parkInHandle"
                :groupId="groupId"
              />
              <div
                v-permission="routeButtonsPermission.ADD"
                class="lock flex align-items-center font-size-14 color-info pointer"
                @click="itemAddHandle(item.id)"
              >
                <svg-icon icon-class="add" />
                <span class="p-l-4">添加新项目</span>
              </div>
            </template>
          </div>
        </div>
        <div class="type-add-btn font-size-14 color-info">
          <div
            v-permission="routeButtonsPermission.EMPLACE"
            class="type-add-btn-wrapper pointer"
            @click="handleSettingVisible = true"
          >
            <svg-icon icon-class="add" />
            <span class="p-l-4">添加新类型</span>
          </div>
        </div>
      </div>
    </basic-card>
    <create-drawer
      ref="createDrawer"
      :visible.sync="createVisible"
      :title="drawerTitle"
      :settingTabs="settingTabs"
      :industry="industry"
      :personList="personList"
    />
    <setting-drawer :visible.sync="settingVisible" :settingTabs="settingTabs" />
    <detail-drawer
      ref="detailDrawer"
      :visible.sync="detailVisible"
      :settingTabs="settingTabs"
      :industry="industry"
      :personList="personList"
    />
    <upload-files ref="upload" @update="getData" />
    <park-in-dialog ref="parkInDialog" :visible.sync="parkInVisible" />
    <handle-setting
      ref="handleSetting"
      :visible.sync="handleSettingVisible"
      dialogTitle="新增"
      :current="groupId"
    />
  </div>
</template>

<script>
import CreateDrawer from './createDrawer'
import SettingDrawer from './settingDrawer'
import Sortable from 'sortablejs'
import BasicTab from '@/components/BasicTab'
import Card from './components/Card'
import DetailDrawer from './detailDrawer'
import UploadFiles from './uploadFiles'
import ParkInDialog from './parkInDialog'
import {
  getIndustry,
  getMerchantConfig,
  getMeRecordList,
  getPerson,
  meRecordDelete,
  meRecordExport,
  meRecordLock,
  updateMeRecordBasic
} from './api'
import downloads from '@/utils/download'
import HandleSetting from './settingDrawer/HandleSetting'

export default {
  name: 'ProjectPool',
  components: {
    HandleSetting,
    ParkInDialog,
    UploadFiles,
    DetailDrawer,
    BasicTab,
    SettingDrawer,
    CreateDrawer,
    Card
  },
  data() {
    return {
      handleSettingVisible: false,
      enterpriseName: '',
      groupId: 1,
      currentGroupName: '',
      createVisible: false,
      drawerTitle: '新增招商项目',
      settingVisible: false,
      tabsData: [
        {
          label: '全部项目',
          value: 0
        },
        {
          label: '我负责的',
          value: 1
        },
        {
          label: '我参与的',
          value: 2
        }
      ],
      current: 0,
      sortableObj: {},
      detailVisible: false,
      settingTabs: [
        {
          label: '项目来源',
          value: 1,
          code: 'source',
          list: []
        },
        {
          label: '项目分组',
          value: 2,
          code: 'group',
          list: []
        },
        {
          label: '项目难度',
          value: 3,
          code: 'diff',
          list: []
        },
        {
          label: '项目匹配',
          value: 4,
          list: []
        },
        {
          label: '项目进展',
          value: 5,
          code: 'process',
          list: []
        },
        {
          label: '跟进方式',
          value: 6,
          list: []
        }
      ],
      industry: [],
      personList: [],
      boardList: [],
      currentTotal: 0,
      typeContainerKey: Math.random(),
      parkInVisible: false,
      scrollLeft: 0
    }
  },
  computed: {
    currentTypeList() {
      return (
        this.settingTabs.find(item => item.value === this.groupId)?.list || []
      )
    }
  },
  provide() {
    return {
      ProjectPool: this
    }
  },
  async created() {
    if (this.$route.query.open) {
      await this.$nextTick()
      this.drawerTitle = '新增招商项目'
      this.createVisible = true
    }
  },
  mounted() {
    this.initEnums()
    this.groupId = this.settingTabs[0].value
    this.groupChange(this.groupId)
  },
  beforeDestroy() {
    this.destroyDrop()
  },
  methods: {
    handleScroll(event) {
      let el = event.target
      this.scrollLeft = Math.ceil(el.scrollLeft)
    },
    enterpriseNameChange(e) {
      if (!e) this.getData()
    },
    async itemAddHandle(id) {
      this.drawerTitle = '新增招商项目'
      this.createVisible = true
      await this.$nextTick()
      this.$refs.createDrawer.itemAddHandle(this.groupId, id)
    },
    // 入园弹窗
    async parkInHandle(row) {
      this.parkInVisible = true
      await this.$nextTick()
      this.$refs.parkInDialog.initData(row)
    },
    exportHandle() {
      downloads.requestDownload(meRecordExport(), 'excel', '项目列表')
    },
    async addHandle() {
      this.drawerTitle = '新增招商项目'
      this.createVisible = true
      await this.$nextTick()
      this.$refs.createDrawer.addHandle()
    },
    // 编辑
    async editHandle(row) {
      this.drawerTitle = '编辑招商项目'
      this.createVisible = true
      await this.$nextTick()
      this.$refs.createDrawer.editHandle(row)
    },
    // 删除
    deleteHandle(row, cb) {
      meRecordDelete(row.id).then(() => {
        this.$toast.success('删除成功')
        cb && cb()
        this.getData()
      })
    },
    // 锁定解锁
    lockHandle(row, cb) {
      meRecordLock(row.id, !row.lockSts).then(() => {
        cb && cb()
        this.$toast.success('操作成功')
        this.getData()
      })
    },
    // 获取列表
    getMeRecordList(configId) {
      return new Promise(resolve => {
        const params = {
          configId,
          enterpriseName: this.enterpriseName,
          scope: this.current ? this.current : ''
        }
        getMeRecordList(params).then(res => {
          resolve(res)
        })
      })
    },
    // 获取数据
    getData() {
      this.destroyDrop()
      const promiseList = []
      this.currentTypeList.forEach(item => {
        promiseList.push(
          new Promise(resolve => {
            this.getMeRecordList(item.id).then(res => {
              const obj = {
                name: item.typeName,
                id: item.id,
                list: res || [],
                type: item.type,
                total: res.length || 0
              }
              resolve(obj)
            })
          })
        )
      })
      Promise.all(promiseList).then(res => {
        this.typeContainerKey = Math.random()
        this.$nextTick(() => {
          this.$refs.typeContainerRef.scrollLeft = this.scrollLeft
        })
        const ids = []
        this.boardList.forEach(item => {
          if (item.isCollapse) ids.push(item.id)
        })
        const list = res || []
        this.currentTotal = 0
        list.forEach(row => {
          if (ids.includes(row.id)) {
            row.isCollapse = true
          }
          this.currentTotal += row.total
        })
        list.forEach(item => {
          item.percentage = this.getPercentage(item)
        })
        this.boardList = list
        this.initDrop()
      })
    },
    lastPercentage(total) {
      if (!total) return 0
      let percentage = 0
      this.boardList.forEach((item, index) => {
        if (index !== this.boardList.length - 1) percentage += item.percentage
      })
      return 100 - percentage
    },
    getPercentage(row) {
      if (!this.currentTotal || !row || !row.list || !row.list.length) return 0
      return Math.round((row.list.length / this.currentTotal) * 100)
    },
    // 数据字典
    initEnums() {
      const promiseList = []
      this.settingTabs.forEach(item => {
        promiseList.push(
          new Promise(resolve => {
            this.getEnumList(item.value).then(res => {
              resolve(res)
            })
          })
        )
      })
      Promise.all(promiseList).then(() => {
        this.getData()
      })
      getIndustry().then(res => {
        this.industry = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
      getPerson({ userType: '01' }).then(res => {
        this.personList = res.map(item => {
          return {
            label: item.nickname,
            value: item.id
          }
        })
      })
    },
    getEnumList(type) {
      return new Promise(resolve => {
        getMerchantConfig({ type }).then(res => {
          this.settingTabs[type - 1].list = res || []
          resolve(res)
        })
      })
    },
    detailHandle(row) {
      this.detailVisible = true
      this.$nextTick(() => {
        this.$refs.detailDrawer.initData(row)
      })
    },
    collapseHandle(index, item) {
      this.$set(this.boardList[index], 'isCollapse', !item.isCollapse)
      this.itemSortableInit(item, !item.isCollapse)
    },
    groupClick() {
      this.$refs.groupSelect.toggleMenu()
    },
    groupChange(e) {
      this.currentGroupName =
        this.settingTabs.find(item => item.value === e)?.label || ''
      this.getData()
    },
    tabsChange(e) {
      this.current = e
      this.getData()
    },
    destroyDrop() {
      for (const key in this.sortableObj) {
        this.sortableObj[key] && this.sortableObj[key].destroy()
        this.sortableObj[key] = null
      }
    },
    itemSortableInit(item, put = true) {
      this.sortableObj[`typeBox${item.id}`]?.destroy()
      this.sortableObj[`typeBox${item.id}`] = null
      const dom = document.querySelector(`#typeBox${item.id}`)
      this.sortableObj[`typeBox${item.id}`] = new Sortable(dom, {
        group: {
          name: 'shared',
          put // 是否可移入
        },
        animation: 150,
        sort: false,
        filter: '.lock', // 禁止拖动元素的class
        onMove: evt => {
          this.boardList.forEach(row => {
            const itemDom = document.querySelector(
              `#typeBox${row.id}`
            ).parentNode
            itemDom.style.border = '0'
          })
          const dom = evt.to.parentNode
          dom.style.border = '1px dashed #1DA0FB'
        },
        onChoose: evt => {
          const dom = evt.from.parentNode
          dom.style.background = '#FFE8E6'
          const filterList = this.boardList.filter(
            val => val.id !== item.id && !val.isCollapse
          )
          const disabledList = this.boardList.filter(val => val.isCollapse)
          filterList.forEach(row => {
            const itemDom = document.querySelector(
              `#typeBox${row.id}`
            ).parentNode
            itemDom.style.background = '#DDF1FF'
          })
          disabledList.forEach(row => {
            const itemDom = document.querySelector(
              `#typeBox${row.id}`
            ).parentNode
            itemDom.style.background = '#FFE8E6'
          })
        },
        onUnchoose: () => {
          this.boardList.forEach(row => {
            const itemDom = document.querySelector(
              `#typeBox${row.id}`
            ).parentNode
            itemDom.style.background = '#f6f7f9'
          })
        },
        onEnd: evt => {
          this.boardList.forEach(row => {
            const itemDom = document.querySelector(
              `#typeBox${row.id}`
            ).parentNode
            itemDom.style.background = '#f6f7f9'
            itemDom.style.border = '0'
          })
          const pullMode = evt.pullMode
          // 跨列拖拽
          if (pullMode) {
            this.$nextTick(() => {
              const key = this.settingTabs.find(
                item => item.code && item.value === this.groupId
              )?.code
              const params = {
                recordId: evt.item.getAttribute('selfId'),
                [key]: evt.to.getAttribute('type')
              }
              this.updateMeRecordBasic(params)
            })
          }
        }
      })
    },
    updateMeRecordBasic(params, cb) {
      updateMeRecordBasic(params).then(() => {
        cb && cb()
        this.getData()
      })
    },
    initDrop() {
      this.boardList.forEach(item => {
        this.$nextTick(() => {
          this.itemSortableInit(item, true)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.project-pool-container {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  user-select: none;
  .header-tabs {
    border-bottom: 1px solid;
    @include border_color(--border-color-base);
    padding: 0 20px;
    height: 48px;
    line-height: 44px;
    .input-container {
      width: 260px;
      :deep(.el-input__inner) {
        border: none;
      }
    }
    .vertical {
      width: 1px;
      height: 16px;
      @include background_color(--border-color-base);
      margin: 0 16px;
    }
    :deep(.el-button) {
      border: 0;
      font-size: 14px;
    }
    .group-select {
      width: 0;
      padding: 0;
      border: 0;
      z-index: -1;
      overflow: hidden;
    }
    .color-block {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 2px;
      margin-right: 4px;
      &.no-park {
        @include background_color(--color-primary);
      }
      &.park-in {
        @include background_color(--color-warning);
      }
      &.park-already {
        @include background_color(--color-success);
      }
    }
    .project-total {
      font-size: 14px;
      @include font_color(--color-text-secondary);
      .total-num {
        @include font_color(--color-text-regular);
        padding: 0 4px;
      }
    }
  }
  .type-container {
    width: 100%;
    height: calc(100% - 96px);
    background: #f6f7f9;
    padding: 0 16px;
    overflow-x: auto;
    overflow-y: hidden;
    .type-item-container {
      width: 368px;
      height: 100%;
      border-radius: 3px;
      background: #f6f7f9;
      margin-right: 10px;
      flex-shrink: 0;
      transition: all ease-in-out 0.3s;
      position: relative;
      &::after {
        display: inline-block;
        content: '';
        width: 1px;
        height: 100%;
        @include background_color(--border-color-base);
        position: absolute;
        right: -5px;
        top: 0;
      }
      &:last-child {
        margin-right: 0;
      }
      .type-item-title {
        padding: 20px;
        .title-container {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .title-wrapper {
            .title-name {
              max-width: 180px;
            }
            .type-item-num {
              margin-left: 10px;
            }
          }
          .svg-icon {
            transform: rotate(90deg);
            flex-shrink: 0;
            cursor: pointer;
            @include font_color(--color-text-regular);
          }
        }
        :deep(.el-progress) {
          margin-top: 20px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
      .type-item-body {
        padding: 0 10px;
        width: 100%;
        height: calc(100% - 100px);
        overflow-y: auto;
        &::-webkit-scrollbar {
          display: none;
        }
      }
      &.collapse {
        width: 56px;
        .type-item-title {
          .title-container {
            flex-flow: wrap-reverse;
            justify-content: center;
            .title-wrapper {
              width: 20px;
              flex-direction: column;
              text-align: center;
              .type-item-num {
                margin-left: 0;
                margin-top: 8px;
              }
            }
            .svg-icon {
              margin-bottom: 12px;
              font-size: 20px;
            }
          }
        }
      }
    }
  }
  .type-add-btn {
    width: 350px;
    padding: 20px;
    flex-shrink: 0;
    .type-add-btn-wrapper {
      width: max-content;
      display: flex;
      align-items: center;
    }
  }
}
:deep(.tabs-wrapper) {
  border: none;
  margin-bottom: 0;
  .tabs-item {
    height: 48px;
    line-height: 48px;
    &.active::before {
      bottom: 0;
    }
  }
}
:deep(.basic-card) {
  .content {
    padding: 0;
  }
}
</style>
<style lang="scss">
.group-select-popper {
  margin-left: -56px;
  margin-top: 0 !important;
}
</style>
