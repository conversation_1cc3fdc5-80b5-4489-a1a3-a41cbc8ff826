<template>
  <basic-drawer
    :title="title"
    :visible.sync="drawerVisible"
    :have-operation="false"
    size="1000px"
  >
    <!--    <div class="m-b-16" v-if="drawerVisible">-->
    <!--      <el-radio-group v-model="fromModel.type" :disabled="disabled">-->
    <!--        <el-radio-button :label="1">招商项目</el-radio-button>-->
    <!--        <el-radio-button :label="2">孵化项目</el-radio-button>-->
    <!--      </el-radio-group>-->
    <!--    </div>-->
    <driven-form
      v-if="drawerVisible"
      ref="driven-form"
      v-model="fromModel"
      label-position="top"
      :formConfigure="formConfigureList"
    />
    <template v-slot:footer>
      <el-button type="info" @click="drawerVisible = false">取消</el-button>
      <el-button type="primary" @click="confirmDrawer">
        {{ examineStatus === 2 ? '保存' : '提交审批' }}
      </el-button>
    </template>
  </basic-drawer>
</template>

<script>
import DescriptorMixins from './descriptor'
import {
  getCityList,
  getCountryList,
  getDemandEnumList,
  getIndustrySub,
  getNationList,
  meRecordCreate,
  meRecordUpdate
} from '../api'

export default {
  name: 'CreateDrawer',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    settingTabs: {
      type: Array,
      default: () => []
    },
    industry: {
      type: Array,
      default: () => []
    },
    personList: {
      type: Array,
      default: () => []
    },
    provinceId: {
      type: Array,
      default: () => []
    },
    landPark: {
      type: Array,
      default: () => []
    }
    // title: {
    //   type: String,
    //   default: '新增招商项目'
    // }
  },
  mixins: [DescriptorMixins],
  data() {
    return {
      disabled: false,
      title: '新增招商项目',
      drawerVisible: false,
      fromModel: {
        type: 1,
        address: '',
        university: 1,
        abroad: false
      },
      formConfigureList: [],
      isDisplay: 'none',
      rowInfo: {},
      examineStatus: 0
    }
  },
  watch: {
    'fromModel.type': {
      handler(e) {
        if (e) {
          this.formConfigureList =
            e === 1 ? this.formConfigure1 : this.formConfigure
          if (e === 1) {
            if (this.fromModel.id) {
              this.title = '编辑招商项目'
            } else {
              this.title = '新增招商项目'
            }
            this.isDisplay = 'inline-block'
            this.formConfigure1.descriptors.pjctSource.options =
              this.settingTabs[0].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
            this.formConfigure1.descriptors.pjctType.options =
              this.settingTabs[4].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
            this.formConfigure1.descriptors.pjctProcess.options =
              this.settingTabs[2].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
            this.formConfigure1.descriptors.pjctProcess.options.forEach(
              item => {
                if (item.label === '项目落地') {
                  item.disabled = true
                }
              }
            )
            // this.formConfigure1.descriptors.pjctGroup.options =
            //   this.settingTabs[1].list.map(item => {
            //     return {
            //       label: item.typeName,
            //       value: item.id
            //     }
            //   })
            // this.formConfigure1.descriptors.pjctDiff.options =
            //   this.settingTabs[2].list.map(item => {
            //     return {
            //       label: item.typeName,
            //       value: item.id
            //     }
            //   })
            this.formConfigure1.descriptors.pjctIndustry.options =
              this.settingTabs[5].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
          } else {
            if (this.fromModel.id) {
              this.title = '编辑招商项目'
            } else {
              this.title = '新增招商项目'
            }
            this.isDisplay = 'none'
            this.formConfigure.descriptors.pjctSource.options =
              this.settingTabs[0].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
            // this.formConfigure.descriptors.pjctGroup.options =
            //   this.settingTabs[1].list.map(item => {
            //     return {
            //       label: item.typeName,
            //       value: item.id
            //     }
            //   })
            // this.formConfigure.descriptors.pjctDiff.options =
            //   this.settingTabs[2].list.map(item => {
            //     return {
            //       label: item.typeName,
            //       value: item.id
            //     }
            //   })
            this.formConfigure.descriptors.pjctType.options =
              this.settingTabs[4].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
            this.formConfigure.descriptors.pjctProcess.options =
              this.settingTabs[2].list.map(item => {
                return {
                  label: item.typeName,
                  value: item.id
                }
              })
          }
        }
      },
      deep: true,
      immediate: true
    },
    landPark: {
      handler(val) {
        this.formConfigure1.descriptors.parkId.options = val || []
      },
      deep: true,
      immediate: true
    },
    personList: {
      handler(val) {
        this.formConfigure.descriptors.headPerson.options = val || []
        this.formConfigure.descriptors.joinPerson.options = val || []
        this.formConfigure1.descriptors.headPerson.options = val || []
        this.formConfigure1.descriptors.joinPerson.options = val || []
      },
      deep: true,
      immediate: true
    },
    industry: {
      handler(val) {
        this.formConfigure.descriptors.industry.options = val || []
        this.formConfigure1.descriptors.industry.options = val || []
      },
      deep: true,
      immediate: true
    },
    provinceId: {
      handler(val) {
        this.formConfigure1.descriptors.provinceId.options = val || []
      },
      deep: true,
      immediate: true
    },
    visible(val) {
      this.drawerVisible = val
    },
    drawerVisible(val) {
      if (!val) {
        this.fromModel = this.$options.data().fromModel
        this.$emit('update:visible', val)
        this.formConfigure1.descriptors.otherIndustry.hidden = true
        this.formConfigure1.descriptors.otherIndustrySub.hidden = true
        this.formConfigure1.descriptors.otherDemand.hidden = true
        this.formConfigure1.descriptors.otherPjctRoad.hidden = true
        this.formConfigure1.descriptors.pjctSource.hidden = true
        this.formConfigure1.descriptors.otherPjctSource.hidden = true
        this.formConfigure1.descriptors.pjctIndustry.hidden = true
        this.formConfigure1.descriptors.otherPjctIndustry.hidden = true
      }
    }
  },
  inject: ['ProjectPool'],
  created() {
    this.getNationList()
    this.getDemandEnumList()
  },
  methods: {
    abroadChange(val) {
      if (val) {
        this.formConfigure1.descriptors.provinceId.hidden = true
        this.formConfigure1.descriptors.cityId.hidden = true
        this.formConfigure1.descriptors.countryId.hidden = true
        this.formConfigure1.descriptors.nationId.hidden = false
        this.formConfigure1.descriptors.address.span = 18
        this.formConfigure1.descriptors.nationId.label = '项目所在地'
      } else {
        this.formConfigure1.descriptors.provinceId.hidden = false
        this.formConfigure1.descriptors.cityId.hidden = false
        this.formConfigure1.descriptors.countryId.hidden = false
        this.formConfigure1.descriptors.nationId.hidden = true
        this.formConfigure1.descriptors.address.span = 9
        this.formConfigure1.descriptors.nationId.label = ''
      }
      if (this.fromModel.id) {
        this.$set(this.fromModel, 'provinceId', this.rowInfo.provinceId)
        this.$set(this.fromModel, 'cityId', this.rowInfo.cityId)
        this.$set(this.fromModel, 'countryId', this.rowInfo.countryId)
        this.$set(this.fromModel, 'nationId', this.rowInfo.nationId)
        if (this.rowInfo.abroad) {
          const address = val ? this.rowInfo.address : ''
          this.$set(this.fromModel, 'address', address)
        } else {
          const address = val ? '' : this.rowInfo.address
          this.$set(this.fromModel, 'address', address)
        }
      } else {
        this.$set(this.fromModel, 'address', '')
      }
    },
    itemAddHandle(groupId, id) {
      this.addHandle()
      if (groupId === 1) {
        this.$set(this.fromModel, 'pjctSource', id)
      }
      // if (this.fromModel.type === 1) {
      //   if (groupId === 2) {
      //     this.$set(this.fromModel, 'pjctGroup', id)
      //   }
      //   if (groupId === 3) {
      //     this.$set(this.fromModel, 'pjctDiff', id)
      //   }
      // }
      if (groupId === 5) {
        this.$set(this.fromModel, 'pjctProcess', id)
      }
    },
    addHandle() {
      this.disabled = false
      // if (this.fromModel.type === 1) {
      //   this.$set(
      //     this.fromModel,
      //     'pjctGroup',
      //     this.formConfigure.descriptors.pjctGroup.options[0]?.value
      //   )
      //   this.$set(
      //     this.fromModel,
      //     'pjctProcess',
      //     this.formConfigure.descriptors.pjctProcess.options[0]?.value
      //   )
      // } else {
      this.$set(this.fromModel, 'headPerson', this.$store.getters.userInfo.userId)
      this.$set(this.fromModel, 'joinPerson', [
        this.$store.getters.userInfo.userId
      ])
      this.$set(
        this.fromModel,
        'pjctProcess',
        this.formConfigure1.descriptors.pjctProcess.options[0]?.value
      )
      this.$set(this.formConfigure1.descriptors.pjctProcess, 'disabled', false)
      // }
    },
    async editHandle(row) {
      this.examineStatus = row.examineStatus
      this.$set(this.fromModel, 'abroad', row.abroad)
      this.abroadChange(row.abroad)
      this.rowInfo = row || {}
      this.fromModel.type = row.type
      const res = await getIndustrySub(row.industry)
      this.formConfigure1.descriptors.industrySub.options = res.map(item => {
        return { label: item.label, value: item.value }
      })
      this.disabled = true
      this.$set(
        this.formConfigure1.descriptors.pjctProcess,
        'disabled',
        row.isPjctProcess
      )
      const { attachMap = {} } = row
      const investment = attachMap.investment
      const policyInfoAttach = attachMap.policyInfoAttach
      const attaches = investment || policyInfoAttach || []
      // if (row.type === 1) {
      for (const key in this.formConfigure1.descriptors) {
        this.$set(this.fromModel, key, row[key])
      }
      const city = await getCityList(row.provinceId)
      this.formConfigure1.descriptors.cityId.options = city.map(item => {
        return { label: item.name, value: item.cityId }
      })
      const country = await getCountryList(row.cityId)
      this.formConfigure1.descriptors.countryId.options = country.map(item => {
        return { label: item.name, value: item.countryId }
      })
      this.formConfigure1.descriptors.industrySub.disabled = false
      // } else {
      //   for (const key in this.formConfigure1.descriptors) {
      //     this.$set(this.fromModel, key, row[key])
      //   }
      //   this.$set(this.fromModel, 'university', row.university)
      // }
      this.$set(this.fromModel, 'id', row.id)
      this.$set(this.fromModel, 'attaches', attaches)
      // 行业类型其他是否显示
      const industryOptions =
        this.formConfigure1.descriptors.industry.options || []
      const industryOther = industryOptions.find(
        item => item.value === row.industry
      )
      this.formConfigure1.descriptors.otherIndustry.hidden = !(
        industryOther && industryOther.label === '其他'
      )
      this.$set(this.fromModel, 'otherIndustry', row.otherIndustry)
      // 细分领域其他是否显示
      const industrySubOptions =
        this.formConfigure1.descriptors.industrySub.options || []
      const industrySubOther = industrySubOptions.find(
        item => item.value === row.industrySub
      )
      this.formConfigure1.descriptors.otherIndustrySub.hidden = !(
        industrySubOther && industrySubOther.label === '其他'
      )
      this.$set(this.fromModel, 'otherIndustrySub', row.otherIndustrySub)
      // 需求类型其他是否显示
      const demandTypeOptions =
        this.formConfigure1.descriptors.demandType.options || []
      const demandTypeOther = demandTypeOptions.find(
        item => item.label === '其他'
      )
      this.formConfigure1.descriptors.otherDemand.hidden =
        !row.demandType.includes(demandTypeOther.value)
      this.$set(this.fromModel, 'otherDemand', row.otherDemand)
      // 招商途径其他是否显示
      const pjctRoadOptions =
        this.formConfigure1.descriptors.pjctRoad.options || []
      const pjctRoadOther = pjctRoadOptions.find(
        item => item.value === row.pjctRoad
      )
      this.formConfigure1.descriptors.otherPjctRoad.hidden = !(
        pjctRoadOther && pjctRoadOther.label === '其他'
      )
      this.$set(this.fromModel, 'otherPjctRoad', row.otherPjctRoad)
      // 项目来源是否显示
      this.formConfigure1.descriptors.pjctSource.hidden = (
          pjctRoadOther && pjctRoadOther.label === '其他'
      )
      this.$set(this.fromModel, 'pjctSource', row.pjctSource)
      // 项目来源其他是否显示
      const pjctSourceOptions =
        this.formConfigure1.descriptors.pjctSource.options || []
      const pjctSourceOther = pjctSourceOptions.find(
        item => item.value === row.pjctSource
      )
      this.formConfigure1.descriptors.otherPjctSource.hidden = !(
        pjctSourceOther && pjctSourceOther.label === '其他'
      )
      this.$set(this.fromModel, 'otherPjctSource', row.otherPjctSource)
      // 推荐至是否显示
      this.formConfigure1.descriptors.pjctIndustry.hidden = (
          pjctRoadOther && pjctRoadOther.label === '其他'
      ) || row.pjctRoad === 1
      this.$set(this.fromModel, 'pjctIndustry', row.pjctIndustry)
      // 推荐至其他是否显示
      const pjctIndustryOptions =
        this.formConfigure1.descriptors.pjctIndustry.options || []
      const pjctIndustryOther = pjctIndustryOptions.find(
        item => item.value === row.pjctIndustry
      )
      this.formConfigure1.descriptors.otherPjctIndustry.hidden = !(
        pjctIndustryOther && pjctIndustryOther.label === '其他'
      )
      this.$set(this.fromModel, 'otherPjctIndustry', row.otherPjctIndustry)
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const { id } = this.fromModel
        const params = {
          ...this.fromModel,
          attaches: this.fromModel.attaches.map(item => item.id)
        }
        if (id) {
          meRecordUpdate(params).then(() => {
            this.operateTips('编辑')
            if (this.ProjectPool.detailVisible)
              this.ProjectPool.$refs.detailDrawer?.getMeRecordDetail(id)
          })
        } else {
          meRecordCreate(params).then(() => {
            this.operateTips('新增')
          })
        }
      })
    },
    operateTips(text) {
      this.$toast.success(`${text}成功`)
      this.drawerVisible = false
      this.ProjectPool.getData()
      this.$emit('getNewTable')
    },
    // 需求类型change
    changeDemandType(e) {
      const options = this.formConfigure1.descriptors.demandType.options || []
      const other = options.find(item => item.label === '其他')
      this.formConfigure1.descriptors.otherDemand.hidden = !e.includes(
        other.value
      )
    },
    // 推荐至change
    changePjctIndustry(e) {
      const options = this.formConfigure1.descriptors.pjctIndustry.options || []
      const other = options.find(item => item.value === e)
      this.formConfigure1.descriptors.otherPjctIndustry.hidden = !(
        other && other.label === '其他'
      )
    },
    // 项目来源change
    changePjctSource(e) {
      const options = this.formConfigure1.descriptors.pjctSource.options || []
      const other = options.find(item => item.value === e)
      this.formConfigure1.descriptors.otherPjctSource.hidden = !(
        other && other.label === '其他'
      )
    },
    // 招商途径change
    changePjctRoad(e) {
      const options = this.formConfigure1.descriptors.pjctRoad.options || []
      const other = options.find(item => item.value === e)
      this.formConfigure1.descriptors.otherPjctRoad.hidden = !(
        other && other.label === '其他'
      )
      this.$set(this.fromModel, 'pjctSource', '')
      this.$set(this.fromModel, 'otherPjctSource', '')
      this.$set(this.fromModel, 'pjctIndustry', '')
      this.$set(this.fromModel, 'otherPjctIndustry', '')
      this.formConfigure1.descriptors.pjctSource.hidden = (
          other && other.label === '其他'
      )
      this.formConfigure1.descriptors.otherPjctSource.hidden = true
      this.formConfigure1.descriptors.pjctIndustry.hidden = (
          other && other.label === '其他'
      ) || e === 1
      this.formConfigure1.descriptors.otherPjctIndustry.hidden = true
    },
    // 细分领域change
    changeIndustrySub(e) {
      const options = this.formConfigure1.descriptors.industrySub.options || []
      const other = options.find(item => item.value === e)
      this.formConfigure1.descriptors.otherIndustrySub.hidden = !(
        other && other.label === '其他'
      )
    },
    // 行业类型change事件
    async changeIndustry(e) {
      const options = this.formConfigure1.descriptors.industry.options || []
      const other = options.find(item => item.value === e)
      this.formConfigure1.descriptors.otherIndustry.hidden = !(
        other && other.label === '其他'
      )
      const res = await getIndustrySub(e)
      this.formConfigure1.descriptors.industrySub.options = res.map(item => {
        return { label: item.label, value: item.value }
      })
      this.$set(this.fromModel, 'industrySub', null)
      this.formConfigure1.descriptors.industrySub.disabled = false
      await this.$nextTick()
      this.$refs['driven-form'].clearValidate()
    },
    // 省份change事件
    async provinceChange(e) {
      const res = await getCityList(e)
      this.formConfigure1.descriptors.cityId.options = res.map(item => {
        return { label: item.name, value: item.cityId }
      })
      this.$set(this.fromModel, 'cityId', '')
      this.$set(this.fromModel, 'countryId', '')
      await this.$nextTick()
      this.$refs['driven-form'].clearValidate()
    },
    // 市级change事件
    async cityChange(e) {
      const res = await getCountryList(e)
      this.formConfigure1.descriptors.countryId.options = res.map(item => {
        return { label: item.name, value: item.countryId }
      })
      this.$set(this.fromModel, 'countryId', '')
      await this.$nextTick()
      this.$refs['driven-form'].clearValidate()
    },
    // 获取外国国家列表
    async getDemandEnumList() {
      const res = await getDemandEnumList()
      this.formConfigure1.descriptors.demandType.options = res.map(item => {
        return { label: item.value, value: item.key }
      })
    },
    // 获取外国国家列表
    async getNationList() {
      const res = await getNationList()
      this.formConfigure1.descriptors.nationId.options = res.map(item => {
        return { label: item.name, value: item.nationId }
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.industryKey) {
  .el-form-item__label {
    &::before {
      display: v-bind(isDisplay);
    }
  }
}
:deep(.cityIdKey) {
  padding-top: 42px;
}
:deep(.countryIdKey) {
  padding-top: 42px;
}
:deep(.otherIndustryKey) {
  padding-top: 42px;
}
:deep(.otherDemandKey) {
  padding-top: 42px;
}
:deep(.otherIndustrySubKey) {
  padding-top: 42px;
}
:deep(.addressKey) {
  padding-top: 42px;
  position: relative;
  .custom-right {
    position: absolute;
    right: 10px;
    top: 12px;
  }
}
</style>
