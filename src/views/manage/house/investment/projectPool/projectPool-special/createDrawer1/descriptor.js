// import {
// validateContact,
// validateDecimal
// } from '@/utils/validate'

import { validateContact } from '@/utils/validate'
import { wayMenus } from '../status'

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '企业名称',
            span: 16,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          industry: {
            form: 'select',
            customLabel: () => {
              return <div></div>
            },
            options: [],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择行业类型'
              }
            ],
            attrs: {
              filterable: true
            },
            span: 8
          },
          headPerson: {
            form: 'select',
            label: '项目经理',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目经理'
              }
            ]
          },
          joinPerson: {
            form: 'select',
            label: '参与人',
            span: 12,
            options: [],
            attrs: {
              multiple: true,
              clearable: true
            },
            rule: [
              {
                type: 'array',
                message: '请选择参与人'
              }
            ]
          },
          background: {
            form: 'input',
            label: '背景描述',
            rule: [
              {
                type: 'string',
                message: '请输入背景描述'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 200,
              showWordLimit: true
            }
          },
          attaches: {
            form: 'component',
            label: '附件',
            rule: [
              {
                type: 'array',
                message: '请上传附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'policyInfoAttach'
              },
              mulity: true,
              maxSize: 10,
              limit: 3
            }
          },
          region: {
            form: 'input',
            label: '区域位置',
            rule: [
              {
                type: 'number',
                message: '请输入区域位置'
              }
            ],
            attrs: {
              maxLength: 50,
              clearable: true
            }
          },
          pjctContact: {
            form: 'input',
            label: '项目对接人',
            rule: [
              {
                type: 'number',
                message: '请输入项目对接人'
              }
            ],
            attrs: {
              maxLength: 50,
              clearable: true
            },
            span: 12
          },
          contactPhone: {
            form: 'input',
            label: '联系方式',
            rule: [
              {
                type: 'number',
                message: '请输入联系方式'
              }
            ],
            attrs: {
              maxLength: 50,
              clearable: true
            },
            span: 12
          },
          pjctType: {
            form: 'select',
            label: '项目类型',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目类型'
              }
            ]
          },
          colleges: {
            form: 'input',
            label: '高校院所',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入高校院所'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          pjctSource: {
            form: 'select',
            label: '项目来源',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目来源'
              }
            ]
          },
          pjctGroup: {
            form: 'select',
            label: '项目分组',
            span: 12,
            options: [],
            rule: [
              {
                type: 'number',
                message: '请选择项目分组'
              }
            ]
          },
          pjctDiff: {
            form: 'select',
            label: '项目难度',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目难度'
              }
            ]
          },
          pjctProcess: {
            form: 'select',
            label: '项目进展',
            span: 12,
            options: [],
            rule: [
              {
                type: 'number',
                message: '请选择项目进展'
              }
            ]
          }
        }
      },
      formConfigure1: {
        descriptors: {
          enterpriseName: {
            form: 'input',
            // label: '项目名称',
            label: '企业名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          provinceId: {
            form: 'select',
            label: '项目所在地',
            span: 5,
            options: [],
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择省份'
              }
            ],
            attrs: {
              filterable: true
            },
            events: {
              change: e => {
                this.provinceChange(e)
              }
            }
          },
          cityId: {
            form: 'select',
            label: '',
            span: 5,
            options: [],
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择市区'
              }
            ],
            attrs: {
              filterable: true
            },
            events: {
              change: e => {
                this.cityChange(e)
              }
            }
          },
          countryId: {
            form: 'select',
            label: '',
            span: 5,
            options: [],
            hidden: false,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请选择区域'
              }
            ],
            attrs: {
              clearable: true,
              filterable: true
            }
          },
          nationId: {
            form: 'select',
            label: '',
            span: 6,
            options: [],
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择国家'
              }
            ],
            attrs: {
              filterable: true
            }
          },
          address: {
            form: 'input',
            label: '',
            span: 9,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入项目所在地'
              }
            ],
            attrs: {
              maxLength: 50
            },
            customRight: () => {
              return (
                <el-checkbox
                  v-model={this.fromModel.abroad}
                  onChange={e => {
                    this.abroadChange(e)
                  }}
                >
                  国外
                </el-checkbox>
              )
            }
          },
          // projectOwnership: {
          //   form: 'input',
          //   label: '项目所属',
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请输入项目所属'
          //     }
          //   ],
          //   attrs: {
          //     maxLength: 50
          //   },
          //   customLabel: () => {
          //     return (
          //       <div class={'pos-relative'}>
          //         <span>项目所属</span>
          //         <div style={'position: absolute; left: 690px; top: 0;'}>
          //           <el-checkbox
          //             v-model={this.fromModel.university}
          //             true-label={2}
          //             false-label={1}
          //           >
          //             院校
          //           </el-checkbox>
          //         </div>
          //       </div>
          //     )
          //   }
          // },
          industry: {
            form: 'select',
            label: '所属行业',
            options: [],
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请选择行业'
              }
            ],
            events: {
              change: e => {
                this.changeIndustry(e)
              }
            },
            attrs: {
              filterable: true
            }
          },
          otherIndustry: {
            form: 'input',
            label: '',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入其他行业类型'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          industrySub: {
            form: 'select',
            label: '细分领域',
            options: [],
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请选择细分领域'
              }
            ],
            disabled: true,
            events: {
              change: e => {
                this.changeIndustrySub(e)
              }
            },
            attrs: {
              filterable: true
            }
          },
          otherIndustrySub: {
            form: 'input',
            label: '',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入其他细分类型'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          pjctContact: {
            form: 'input',
            label: '企业负责人',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业负责人'
              }
            ],
            attrs: {
              maxLength: 50,
              clearable: true
            },
            span: 12
          },
          contactPhone: {
            form: 'input',
            label: '联系方式',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                validator: (rule, value, callback) => {
                  validateContact(rule, value, callback, false)
                }
              }
            ],
            attrs: {
              maxlength: 30
            }
          },
          contactPosition: {
            form: 'input',
            label: '联系人职位',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入联系人职位'
              }
            ],
            attrs: {
              maxLength: 20,
              clearable: true
            },
            span: 12
          },
          // occupationType: {
          //   form: 'select',
          //   label: '入驻类型',
          //   options: [
          //     {
          //       label: '拟租赁',
          //       value: 1
          //     },
          //     {
          //       label: '拟购买',
          //       value: 2
          //     },
          //     {
          //       label: '云孵化',
          //       value: 3
          //     }
          //   ],
          //   span: 12,
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请选择入驻类型'
          //     }
          //   ]
          // },
          intendedArea: {
            form: 'input',
            label: '意向面积',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入意向面积'
              }
              // {
              //   required: false,
              //   type: 'string',
              //   message: '请输入最多两位小数的意向面积',
              //   pattern: /^([1-9][\d]{0,7}|0)(\.[\d]{1,2})?$/
              // }
            ],
            attrs: {
              maxLength: 50
            }
            // customRight: () => {
            //   return (
            //     <div
            //       class="line-height-32 font-size-14"
            //       style={'margin-top: 42px;'}
            //     >
            //       m²
            //     </div>
            //   )
            // }
          },
          visitDate: {
            form: 'date',
            label: '走访/接待时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择走访/接待时间'
              }
            ]
          },
          parkId: {
            form: 'select',
            label: '落地园区',
            span: 12,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择落地园区'
              }
            ],
            options: [],
            attrs: {
              clearable: true
            }
          },
          background: {
            form: 'input',
            label: '项目简介',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入项目简介'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 5,
              maxlength: 1000,
              showWordLimit: true
            }
          },
          pjctDemand: {
            form: 'input',
            label: '落地需求',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入落地需求'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 5,
              maxlength: 300,
              showWordLimit: true
            }
          },
          demandType: {
            form: 'select',
            label: '需求类型',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择需求类型'
              }
            ],
            events: {
              change: e => {
                this.changeDemandType(e)
              }
            },
            attrs: {
              filterable: true,
              multiple: true
            }
          },
          otherDemand: {
            form: 'input',
            label: '',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入其他需求类型'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          attaches: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'policyInfoAttach'
              },
              mulity: true,
              maxSize: 20,
              limit: 3
            }
          },
          headPerson: {
            form: 'select',
            label: '项目经理',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目经理'
              }
            ],
            attrs: {
              filterable: true
            }
          },
          joinPerson: {
            form: 'select',
            label: '参与人',
            span: 12,
            options: [],
            attrs: {
              multiple: true,
              clearable: true,
              filterable: true
            },
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择参与人'
              }
            ]
          },
          pjctRoad: {
            form: 'select',
            label: '招商途径',
            span: 12,
            options: wayMenus,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择招商途径'
              }
            ],
            events: {
              change: e => {
                this.changePjctRoad(e)
              }
            },
            attrs: {
              filterable: true
            }
          },
          otherPjctRoad: {
            form: 'input',
            label: '其他招商途径类型',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入其他招商途径类型'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          pjctSource: {
            form: 'select',
            label: '项目来源',
            span: 12,
            options: [],
            hidden: true,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择项目来源'
              }
            ],
            events: {
              change: e => {
                this.changePjctSource(e)
              }
            },
            attrs: {
              filterable: true,
              clearable: true
            }
          },
          otherPjctSource: {
            form: 'input',
            label: '其他项目来源类型',
            span: 12,
            hidden: true,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入其他项目来源类型'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          pjctIndustry: {
            form: 'select',
            label: '推荐至',
            span: 12,
            options: [],
            hidden: true,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择推荐至'
              }
            ],
            events: {
              change: e => {
                this.changePjctIndustry(e)
              }
            },
            attrs: {
              filterable: true,
              clearable: true
            }
          },
          otherPjctIndustry: {
            form: 'input',
            label: '其他推荐至类型',
            span: 12,
            hidden: true,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入其他推荐至类型'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          pjctType: {
            form: 'select',
            label: '项目类型',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目类型'
              }
            ],
            attrs: {
              filterable: true
            }
          },
          // colleges: {
          //   form: 'input',
          //   label: '高校院所',
          //   span: 12,
          //   rule: [
          //     {
          //       required: false,
          //       type: 'string',
          //       message: '请输入高校院所'
          //     }
          //   ],
          //   attrs: {
          //     maxLength: 30
          //   }
          // },
          pjctProcess: {
            form: 'select',
            label: '项目进展',
            span: 12,
            options: [],
            rule: [
              {
                type: 'number',
                message: '请选择项目进展'
              }
            ],
            attrs: {
              filterable: true
            }
          }
          // pjctGroup: {
          //   form: 'select',
          //   label: '项目分组',
          //   span: 12,
          //   options: [],
          //   rule: [
          //     {
          //       type: 'number',
          //       message: '请选择项目分组'
          //     }
          //   ]
          // },
          // pjctDiff: {
          //   form: 'select',
          //   label: '项目难度',
          //   span: 12,
          //   options: [],
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请选择项目难度'
          //     }
          //   ]
          // },
          // pjctIndustry: {
          //   form: 'select',
          //   label: '所处行业',
          //   span: 12,
          //   options: [],
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请选择所处行业'
          //     }
          //   ]
          // }
        }
      }
    }
  }
}
