<template>
  <div>
    <basic-drawer
      title="项目设置"
      :visible.sync="drawerVisible"
      :haveFooter="false"
      size="840px"
    >
      <basic-tab
        :tabsData="settingTabs"
        :current="current"
        @tabsChange="tabsChange"
      />
      <div class="p-l-16 p-r-16">
        <div class="flex justify-content-end m-b-10">
          <el-button
            type="info"
            size="mini"
            @click="sortableHandle"
            :disabled="!tableData.length"
          >
            <svg-icon :icon-class="isSortable ? 'save' : 'order-ascending'" />
            <span>{{ isSortable ? '保存排序' : '拖动排序' }}</span>
          </el-button>
          <el-button
            type="primary"
            size="mini"
            @click="addHandle"
            :disabled="isSortable"
          >
            <svg-icon icon-class="add" />
            <span>添加</span>
          </el-button>
        </div>
        <el-table
          id="proTable"
          :data="tableData"
          stripe
          :cell-class-name="cellClassName"
        >
          <el-table-column width="20px" class-name="tips-cell">
            <template slot-scope="scope">
              <svg-icon
                v-if="!scope.row.builtIn && isSortable"
                icon-class="sortable-tips"
                class="color-primary"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="typeName"
            label="类型名称"
            align="center"
          ></el-table-column>
          <el-table-column
            v-if="current === 3 || current === 5"
            prop="typeName"
            label="类型颜色"
            align="center"
          >
            <template slot-scope="scope">
              <div class="color-level-container">
                <span
                  class="color-level"
                  :style="{ background: scope.row.colorLevel || '#ed7b2f' }"
                ></span>
                <span>{{ scope.row.colorLevel || '#ed7b2f' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createUser"
            label="创建人"
            align="center"
          ></el-table-column>
          <el-table-column prop="" label="操作" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.builtIn">-</span>
              <template v-else>
                <el-button
                  type="text"
                  :disabled="isSortable"
                  @click="editHandle(scope.row)"
                  >编辑</el-button
                >
                <span class="p-l-8 p-r-8">|</span>
                <el-button
                  type="text"
                  :disabled="isSortable"
                  @click="deleteHandle(scope.row)"
                  >删除</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </basic-drawer>
    <handle-setting
      ref="handleSetting"
      :visible.sync="dialogVisible"
      :dialogTitle="dialogTitle"
      :current="current"
    />
  </div>
</template>

<script>
import DescriptorMixins from './descriptor'
import BasicTab from '@/components/BasicTab'
import HandleSetting from './HandleSetting'
import Sortable from 'sortablejs'
import { merchantConfigDelete, merchantConfigSort } from '../api'
import { deepClone } from '@/utils/tools'

export default {
  name: 'SettingDrawer',
  components: { HandleSetting, BasicTab },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    settingTabs: {
      type: Array,
      default: () => []
    }
  },
  mixins: [DescriptorMixins],
  data() {
    return {
      drawerVisible: false,
      current: 1,
      isSortable: false,
      dragTable: [],
      sort_table: null,
      dialogVisible: false,
      dialogTitle: '添加'
    }
  },
  inject: ['ProjectPool'],
  computed: {
    tableData() {
      return (
        this.settingTabs.find(item => item.value === this.current)?.list || []
      )
    }
  },
  watch: {
    visible(val) {
      this.drawerVisible = val
    },
    drawerVisible(val) {
      if (val) {
        this.ProjectPool.getEnumList(this.current)
      } else {
        this.isSortable = false
        this.current = 1
        this.$emit('update:visible', val)
      }
    },
    isSortable(val) {
      val ? this.initDrop() : this.destroyDrop()
    }
  },
  beforeDestroy() {
    this.destroyDrop()
  },
  methods: {
    sortableHandle() {
      this.isSortable = !this.isSortable
      if (!this.isSortable) this.sortSave()
    },
    // 保存排序
    sortSave() {
      const ids = this.tableData.map(item => item.id)
      const initIds = this.dragTable.map(item => item.id)
      if (JSON.stringify(ids) !== JSON.stringify(initIds) && ids.length) {
        const params = {
          ids: JSON.stringify(ids)
        }
        merchantConfigSort(params).then(() => {
          this.ProjectPool.initEnums()
          this.destroyDrop()
        })
      } else {
        this.destroyDrop()
      }
    },
    // 销毁拖拽排序
    destroyDrop() {
      this.sort_table && this.sort_table.destroy()
      this.sort_table = null
    },
    // 拖拽排序
    initDrop() {
      this.destroyDrop()
      this.dragTable = deepClone(this.tableData)
      const tbody = document.querySelector(
        '#proTable .el-table__body-wrapper tbody'
      )
      const _this = this
      this.sort_table = Sortable.create(tbody, {
        handle: '.sort-row-item',
        animation: 180,
        delay: 0,
        chosenClass: 'sortable-chosen', // 选中classname
        onEnd: evt => {
          const index = _this.ProjectPool.settingTabs.findIndex(
            item => item.value === this.current
          )
          const targetRow = this.ProjectPool.settingTabs[index].list.splice(
            evt.oldIndex,
            1
          )[0]
          _this.ProjectPool.settingTabs[index].list.splice(
            evt.newIndex === 0 ? evt.oldIndex : evt.newIndex,
            0,
            targetRow
          )
          const newList = this.ProjectPool.settingTabs[index].list.slice(0)
          _this.ProjectPool.settingTabs[index].list = []
          this.$nextTick(() => {
            this.ProjectPool.settingTabs[index].list = newList
          })
        }
      })
    },
    cellClassName({ rowIndex }) {
      if (rowIndex > 0) return 'sort-row-item'
    },
    tabsChange(e) {
      this.isSortable = false
      this.current = e
      this.ProjectPool.getEnumList(e)
    },
    // 删除
    deleteHandle(row) {
      this.$confirm('确定删除？', '提示').then(() => {
        merchantConfigDelete(row.id).then(() => {
          this.operateTips('删除')
        })
      })
    },
    // 编辑弹窗
    async editHandle(row) {
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      await this.$refs.handleSetting.editHandle(row)
    },
    // 添加弹窗
    addHandle() {
      this.fromModel = this.$options.data().fromModel
      this.dialogTitle = '添加'
      this.dialogVisible = true
    },
    operateTips(text) {
      if (text) this.$toast.success(`${text}成功`)
      this.ProjectPool.initEnums()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss">
.color-level-container {
  display: flex;
  align-items: center;
  justify-content: center;
  .color-level {
    width: 6px;
    height: 6px;
    margin-right: 6px;
  }
}
:deep(.el-drawer__container) {
  .drawer-content {
    padding: 0;
    .tabs-wrapper {
      border: none;
      margin-bottom: 10px;
      .tabs-item.active:before {
        width: calc(100% - 32px);
        bottom: 2px;
        left: 16px;
      }
    }
    .el-table {
      border: 1px solid #e7e7e7;
      border-bottom: none;
      .tips-cell {
        .cell {
          padding: 0 !important;
          margin-left: 8px;
          cursor: pointer;
          display: none;
        }
      }
      .el-table__row:hover {
        .tips-cell .cell {
          display: block;
        }
      }
      .sortable-chosen {
        .el-table__cell {
          border-top: 2px solid #ed7b2f !important;
          background: #e9f0ff;
        }
      }
    }
  }
}
</style>
