<template>
  <driven-form
    ref="driven-form"
    v-model="formModel"
    :formConfigure="contactPersonConfigure"
    label-position="top"
  />
</template>

<script>
import DescriptorMixin from './descriptor'
import { getMerchantConfig, getMerchantMeContactPhone } from '../../../api'

export default {
  name: 'ContactPerson',
  mixins: [DescriptorMixin],
  data() {
    return {
      formModel: {},
      hasContactPhone: false,
      contactInfo: {}
    }
  },
  watch: {
    'formModel.label'(val) {
      const options =
        this.contactPersonConfigure.descriptors.label.options || []
      const ids = options.map(item => item.value)
      const labelId = []
      const labelName = []
      val.forEach(row => {
        if (ids.includes(row)) {
          labelId.push(row)
        } else {
          labelName.push(row)
        }
      })
      this.$set(this.formModel, 'labelId', labelId)
      this.$set(this.formModel, 'labelName', labelName)
    },
    'formModel.contactName'(val) {
      if (!val) return false
      const row = JSON.parse(val)
      this.$set(this.formModel, 'id', row.id)
      this.$set(this.formModel, 'contact', row.contact)
      this.$set(this.formModel, 'contactPhone', row.contactPhone)
    }
  },
  mounted() {
    this.getLabelList()
  },
  methods: {
    selectContactHandle() {
      this.$set(this.formModel, 'contactName', JSON.stringify(this.contactInfo))
      this.hasContactPhone = false
    },
    contactPhoneInput(e) {
      this.$set(this.formModel, 'contactPhone', e)
      this.$set(this.formModel, 'id', '')
      this.hasContactPhone = false
    },
    async contactPhoneBlur() {
      if (!this.formModel.contactPhone || this.formModel.id) return false
      this.contactInfo = await getMerchantMeContactPhone({
        phone: this.formModel.contactPhone
      })
      if (this.contactInfo && this.contactInfo.id) {
        this.hasContactPhone = true
      }
    },
    getLabelList() {
      getMerchantConfig({ type: 7 }).then(res => {
        const list = res || []
        this.contactPersonConfigure.descriptors.label.options = list.map(
          item => {
            return {
              label: item.typeName,
              value: item.id
            }
          }
        )
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.custom-tips) {
  margin-left: 0 !important;
}
</style>
