<template>
  <div class="drawer-body-right min-h100">
    <div class="flex justify-content-between">
      <div class="right-title">项目基础信息</div>
      <div class="m-t-4" v-if="!detailInfo.lockSts">
        <el-link
          type="primary"
          class="m-r-5"
          @click="editProject"
          v-if="detailInfo.isUpdateOrDelete"
          v-permission="routeButtonsPermission.EDIT"
          :underline="false"
          >{{ routeButtonsTitle.EDIT }}</el-link
        >
        <template v-if="detailInfo.enterSts !== 1 && detailInfo.enterSts !== 2">
          <el-link
            type="danger"
            @click="delProject"
            v-if="detailInfo.isUpdateOrDelete"
            v-permission="routeButtonsPermission.DELETE"
            :underline="false"
            >{{ routeButtonsTitle.DELETE }}</el-link
          >
        </template>
      </div>
    </div>
    <div class="project-detailInfo-container">
      <div class="detail-row">
        <span class="label">项目经理</span>
        <span class="value">{{ detailInfo.headPersonName }}</span>
      </div>
      <div class="detail-row">
        <span class="label">参与人</span>
        <span class="value">
          <template v-if="joinPersonNames.length > 0">
            <el-tag
              type="primary"
              size="mini"
              v-for="(name, index) in joinPersonNames"
              :key="index"
              class="m-r-5 m-b-5"
            >
              {{ name }}
            </el-tag>
          </template>
          <template v-else>0人 </template>
        </span>
      </div>
      <div class="detail-row">
        <span class="label">添加人</span>
        <span class="value">{{ detailInfo.creatorName }}</span>
      </div>
      <div class="detail-row">
        <span class="label">添加时间</span>
        <span class="value">{{ detailInfo.createTime }}</span>
      </div>
      <div class="detail-row">
        <span class="label">更新时间</span>
        <span class="value">{{ detailInfo.updateTime }}</span>
      </div>
      <div class="detail-row">
        <span class="label">项目类型</span>
        <span class="value">{{ detailInfo.pjctTypeStr | noData }}</span>
      </div>
      <div class="detail-row">
        <span class="label">招商途径</span>
        <span class="value">{{ detailInfo.pjctRoadStr | noData }}</span>
      </div>
      <div class="detail-row">
        <span class="label">推荐至</span>
        <span class="value">{{ detailInfo.pjctIndustryName | noData }}</span>
      </div>
      <div class="detail-row">
        <span class="label">项目进展</span>
        <span class="value">
          <el-tag type="primary" size="mini" effect="dark" class="m-r-5 m-b-5">
            {{ detailInfo.pjctProcessStr | noData }}
          </el-tag>
        </span>
      </div>
      <div class="detail-row">
        <span class="label">落地园区</span>
        <span class="value">{{ detailInfo.parkName | noData }}</span>
      </div>
    </div>
    <div
      v-if="detailInfo.type === 2"
      class="color-warning font-size-12 line-height-20 m-b-10"
    >
      <span>温馨提示：</span>
      <span>仅项目经理和参与人可以编辑维护此项目所有信息</span>
    </div>
    <div v-if="detailInfo.type === 2" class="right-statistics">
      <div class="statistics-item">
        <span class="item-label">项目创建人</span>
        <span class="item-content color-primary">{{
          detailInfo.creatorName || '-'
        }}</span>
      </div>
      <div class="statistics-item">
        <span class="item-label">添加时间</span>
        <span class="item-content color-primary">{{
          detailInfo.createTime || '-'
        }}</span>
      </div>
      <div class="statistics-item">
        <span class="item-label">最近更新时间</span>
        <span class="item-content color-primary">{{
          detailInfo.updateTime || '-'
        }}</span>
      </div>
    </div>
    <div v-else class="right-statistics">
      <div class="statistics-item">
        <span class="item-label">联系人</span>
        <span class="item-content color-primary"
          >{{ detailInfo.contactCount || 0 }}个</span
        >
      </div>
      <div class="statistics-item">
        <span class="item-label">跟进记录</span>
        <span class="item-content color-primary"
          >{{ detailInfo.followCount || 0 }}条</span
        >
      </div>
      <div class="statistics-item">
        <span class="item-label">项目匹配</span>
        <span class="item-content color-primary"
          >{{ detailInfo.mateCount || 0 }}项</span
        >
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DetailDrawerRight',
  data() {
    return {
      detailInfo: {}
    }
  },
  inject: ['ProjectPool', 'DetailDrawer'],
  computed: {
    joinPersonNames() {
      const { joinPersonName = '' } = this.detailInfo;

      if (!joinPersonName) return [];

      const rawNames = joinPersonName.split('、');

      return rawNames.map(name => name.split('/').pop().trim());

    }

  },
  methods: {
    editProject() {
      const { id } = this.detailInfo
      this.ProjectPool.editHandle({ id })
    },
    delProject() {
      const { id } = this.detailInfo
      this.ProjectPool.deleteHandle({ id },() => {
        this.ProjectPool.isDetailVisible(false)
      })
    },
    initData(row) {
      this.detailInfo = row || {}
    }
  }
}
</script>

<style scoped lang="scss">
.drawer-body-right {
  width: 30%;
  padding: 16px 0 0 24px;
  .right-title {
    margin-top: 4px;
    margin-bottom: 16px;
    font-size: 16px;
    color: #3a3c41;
    font-weight: bold;
    line-height: 22px;
  }
  .right-statistics {
    border-top: 1px solid;
    @include border_color(--border-color-base);
    padding-top: 10px;
    margin-top: 10px;
    .statistics-item {
      display: flex;
      color: #7d8089;
      line-height: 40px;
      height: 40px;
      font-size: 14px;
      margin-bottom: 8px;
      .item-label {
        width: 90px;
      }
      .item-content {
        width: calc(100% - 90px);
        padding: 0 15px;
      }
    }
  }
}

.project-details-container {
  background-color: #ffffff;
  padding: 0 16px;
  max-width: 600px; /* 限制最大宽度以便于查看 */
  margin: 20px auto; /* 居中显示 */
  border-radius: 8px; /* 轻微圆角 */
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05); /* 添加轻微阴影 */
}

.detail-row {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  font-size: 14px;
  line-height: 16px;
}

/* 移除最后一行的边框 */
.detail-row.no-border,
.detail-row:last-child {
  border-bottom: none;
}

.label {
  color: #646566; /* 标签的灰色 */
  white-space: nowrap;
  margin-right: 12px;
  width: 90px;
}

.value {
  color: #323233; /* 内容的深黑色 */
  width: calc(100% - 90px);
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f2f3f5;
  border-radius: 4px;
  color: #646566;
}

.close-icon {
  margin-left: 4px;
  color: #969799;
  font-weight: bold;
  cursor: pointer;
}

.placeholder {
  color: #c8c9cc; /* 占位符的浅灰色 */
}

.orange-text {
  color: #ee7c24; /* 橙色数字 */
  font-weight: 500;
  font-size: 16px;
  margin-right: 2px;
}
</style>
