<template>
<!--  v-if="detailInfo.isUpdateOrDelete"-->
  <div class="left-footer-container">
<!--    :class="{ lock: !detailInfo.isUpdateOrDelete }"-->
    <div
      class="w100 line-height-40 flex align-items-center justify-content-between"
      v-if="!showInput"
      @click="showClick"
    >
      <span>点击添加该项目的{{ tipsText }}</span>
      <svg-icon icon-class="unfold-more" />
    </div>
    <div class="w100 m-t-6" v-else>
      <component
        :is="componentName"
        :ref="componentName"
        :detailInfo="detailInfo"
      />
      <div class="m-t-12 flex justify-content-end">
        <el-button @click="showInput = false">取消</el-button>
        <el-button type="primary" @click="submitIngHandle">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import FollowUp from './FollowUp'
import Matching from './Matching'
import ContactPerson from './ContactPerson'
import Appeal from './appeal'
import ProjectLanding from './projectLanding'
import ProposedLease from './ProposedLease'
import {
  // getGroundCreate,
  // getGroundUpdate,
  // getMerchantAppealUpdate,
  // merchantLogCreateAppeal,
  merchantLogCreateFollow,
  merchantLogCreateMate, merchantLogUpdateFollow,
  merchantMeContactCreate
} from '../../../api'
export default {
  name: 'LeftFooter',
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    ContactPerson,
    Matching,
    FollowUp,
    Appeal,
    ProjectLanding,
    ProposedLease
  },
  data() {
    return {
      showInput: false,
      componentName: 'FollowUp',
      currentTab: 1,
      tipsText: '跟进内容'
    }
  },
  inject: ['ProjectPool', 'DetailDrawerLeft', 'DetailDrawer'],
  methods: {
    // 编辑
    async editHandle(row) {
      // if (!this.detailInfo.isUpdateOrDelete) return false
      this.showInput = true
      await this.$nextTick()
      this.$refs[this.componentName].editHandle(row)
    },
    async showClick() {
      // if (!this.detailInfo.isUpdateOrDelete) return false
      this.showInput = true
      await this.$nextTick()
      if (this.currentTab === 6) {
        this.$refs[this.componentName].getParkSelect()
        if (this.DetailDrawerLeft.$refs.leftBody.leaseInfo) {
          this.$refs[this.componentName].getProposedLease(this.detailInfo.id)
        }
      }
    },
    // e: 1项目跟进 2项目匹配 3联系方式 4项目诉求 5项目落地
    initData(e) {
      this.currentTab = e
      const arr = [
        'FollowUp',
        'Matching',
        'ContactPerson',
        'Appeal',
        'ProjectLanding',
        'ProposedLease'
      ]
      this.componentName = arr[e - 1]
      if (e === 4) {
        this.componentName = arr[0]
      }
      if (e === 5) {
        this.componentName = arr[0]
      }
      if (e === 1) {
        this.tipsText = '跟进内容'
      }
      if (e === 2) {
        this.tipsText = '匹配内容'
      }
      if (e === 3) {
        this.tipsText = '联系方式'
      }
      if (e === 4) {
        this.tipsText = '企业跟进'
      }
      if (e === 5) {
        this.tipsText = '项目批示'
      }
      if (e === 6) {
        this.tipsText = '拟租赁'
      }
    },
    submitIngHandle() {
      if (this.currentTab === 6) {
        this.$refs[this.componentName].submitHandle()
      } else {
        this.$refs[this.componentName].$refs['driven-form'].validate(valid => {
          if (!valid) return
          const params = {
            ...this.$refs[this.componentName].formModel,
            recordId: this.detailInfo.id
          }
          let apiFn = merchantLogCreateFollow
          if (this.currentTab === 1) {
            params.type = 1
            apiFn = merchantLogCreateFollow
            params.attachIds = this.$refs[
              this.componentName
            ].formModel.attachIds.map(item => item.id)
          } else if (this.currentTab === 2) {
            apiFn = merchantLogCreateMate
            params.attachIds = this.$refs[
              this.componentName
            ].formModel.attachIds.map(item => item.id)
          } else if (this.currentTab === 4) {
            params.type = 4
            apiFn = merchantLogCreateFollow
            params.attachIds = this.$refs[
              this.componentName
            ].formModel.attachIds.map(item => item.id)
          } else if (this.currentTab === 5) {
            params.type = 5
            apiFn = merchantLogCreateFollow
            params.attachIds = this.$refs[
              this.componentName
            ].formModel.attachIds.map(item => item.id)
          } else {
            apiFn = merchantMeContactCreate
            const hasContactPhone = this.$refs.ContactPerson.hasContactPhone
            if (hasContactPhone)
              return this.$toast.warning('当前联系方式已存在联系人，请重新输入')
          }
          if (params.id) {
            apiFn = merchantLogUpdateFollow
          }
          apiFn(params).then(() => {
            this.$toast.success(params.id ? '修改成功' : '提交成功')
            this.DetailDrawerLeft.$refs.leftBody.getMerchantLogList()
            this.DetailDrawer.getMeRecordDetail(this.detailInfo.id)
            this.ProjectPool.getData()
            this.ProjectPool.getNewTable()
            this.showInput = false
          })
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.left-footer-container {
  width: 68.4%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 900;
  -webkit-box-shadow: 0 -3px 8px 0 rgba(0, 0, 0, 0.06);
  box-shadow: 0 -3px 8px 0 rgba(0, 0, 0, 0.06);
  background-color: #fff;
  padding: 8px 20px;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  transition: height 0.1s linear;
  cursor: pointer;
  font-size: 14px;
  color: #adadad;
  border-top: 1px solid;
  @include border_color_mix(--color-primary, #fff, 85%);
  .lock {
    cursor: not-allowed;
    opacity: 0.7;
  }
}
</style>
