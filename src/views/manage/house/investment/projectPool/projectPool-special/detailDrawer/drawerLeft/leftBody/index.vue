<template>
  <div class="left-body">
    <el-radio-group v-model="tabModel" @change="radioChange">
      <el-radio-button v-for="item in tabs" :key="item.key" :label="item.key">{{
        item.label
      }}</el-radio-button>
    </el-radio-group>
    <template
      v-if="
        tabModel === 1 || tabModel === 2 || tabModel === 4 || tabModel === 5
      "
    >
      <basic-tab
        :tabsData="followUpTabs"
        :current="current"
        @tabsChange="tabsChange"
      />
      <div class="timeline-container">
        <template v-if="dataList && dataList.length > 0">
          <div
            class="timeline-item"
            v-for="(item, index) in dataList"
            :key="index"
          >
            <div class="timeline-left">
              <div class="item-icon">{{ item.creatorName.charAt(0) }}</div>
              <div class="item-line"></div>
            </div>
            <div class="timeline-right">
              <div class="timeline-header">
                <div class="flex align-items-center">
                  <span class="font-size-14 header-name line-1">{{
                    item.creatorName
                  }}</span>
                  <span class="m-l-12" v-if="item.operationDate">跟进时间：{{ item.operationDate }}</span>
                  <span class="m-l-12">{{ item.typeName }}</span>
                </div>
              </div>
              <div class="timeline-content">
                <div :class="{ 'color-info': item.type === 3 }" class="flex">
                  <div class="content-row">
                    <div class="content-text">
                      {{ item.updateFlag ? item.title : item.content }}
                    </div>
                    <el-link v-if="item.changeList && item.changeList.length" type="primary" class="m-l-4" @click="viewRecordDetail(item)">修改详情</el-link>
                  </div>

                  <el-link
                    v-if="item.updateFlag"
                    type="primary"
                    class="m-l-4"
                    style="width: 83px"
                    @click="viewRecord(item)"
                  >
                    查看原记录
                  </el-link>
                </div>
                <div class="annex-wrapper" v-if="getAttach(item.attachMap).length && !item.updateFlag">
                  <files-list :files="getAttach(item.attachMap)" onlyForView />
                </div>
                <div class="m-t-10 font-size-12 flex align-items-center justify-content-between">
                  <div>
                    <span
                        v-if="
                      (tabModel === 1 || tabModel === 4 || tabModel === 5) &&
                      item.contactName
                    "
                        class="m-r-16"
                    >
                    联系人：{{ item.contactName }}
                  </span>
                    <span>更新时间：{{ item.createTime }}</span>
                  </div>
                  <div
                      v-if="
                      (tabModel === 1 || tabModel === 4 || tabModel === 5) &&
                      item.permissionFlag && (item.type === 1 || item.type === 4 || item.type === 5)
                      "
                  >
                    <el-link v-permission="routeButtonsPermission.FOLLOW_EDIT" type="primary" @click="itemEditHandle(item)">修改</el-link>
                    <el-link v-permission="routeButtonsPermission.FOLLOW_DELETE" class="m-l-8" @click="itemDeleteHandle(item)">删除</el-link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
        <empty-data v-else class="p-t-32 m-t-32" />
      </div>
    </template>
    <div
      v-if="tabModel === 3"
      class="timeline-container contact-timeline-container"
    >
      <template v-if="contactList.length">
        <div
          class="timeline-item"
          v-for="(item, index) in contactList"
          :key="index"
        >
          <div class="timeline-left">
            <div class="item-icon">{{ item.creatorName.charAt(0) }}</div>
            <div class="item-line"></div>
          </div>
          <div class="timeline-right">
            <div class="timeline-header">
              <div class="flex align-items-center">
                <span class="font-size-14 header-name line-1">{{
                  item.contact
                }}</span>
                <span class="m-l-12">{{ item.contactPhone }}</span>
                <span class="m-l-12">{{ item.labelName }}</span>
                <el-link
                  type="primary"
                  :underline="false"
                  class="copy-text font-size-12 m-l-12"
                  @click="copyHandle(item, $event)"
                  >复制</el-link
                >
              </div>
            </div>
            <div class="timeline-content">
              <div>{{ item.specificContent }}</div>
              <div class="m-t-10 font-size-12">
                <span>{{ item.creatorName }}</span>
                <span class="p-l-4 p-r-4">{{ item.createTime }}</span>
                <span>添加</span>
              </div>
            </div>
          </div>
        </div>
      </template>
      <empty-data v-else class="p-t-32 m-t-32" />
    </div>
    <div v-if="tabModel === 6">
      <div v-if="leaseInfo && Object.keys(leaseInfo).length > 0">
        <div class="title m-b-8">入驻园区</div>
        <div class="font-size-16 line-height-22 font-strong m-b-16">
          {{ leaseInfo.park | noData }}
        </div>
        <div class="m-b-24">
          <div
            class="flex justify-content-between align-items-center font-size-14 line-height-20 m-b-16"
          >
            <div>租赁房源</div>
            <div class="color-warning">
              温馨提示：直至确认上会结果，以下房源均锁定为意向
            </div>
          </div>
          <drive-table
            max-height="380px"
            ref="drive-table"
            :columns="tableColumnRoom"
            :table-data="leaseInfo.room || []"
          />
        </div>
        <table style="width: 100%" border="1" Cellspacing="0" Cellpadding="0">
          <tr>
            <th :colspan="6">
              <div class="line-height-22 font-strong">合同情况</div>
            </th>
          </tr>
          <tr>
            <th>租赁面积（m²）</th>
            <td>{{ leaseInfo.rentalArea | noData }}</td>
            <th>合同租赁期限</th>
            <td>{{ leaseInfo.startTime }}~{{ leaseInfo.endTime }}</td>
          </tr>
          <tr>
            <th>优惠期（月）</th>
            <td>{{ leaseInfo.discountPeriod | noData }}</td>
            <th>免租期（月）</th>
            <td>{{ leaseInfo.freePeriod | noData }}</td>
          </tr>
          <tr>
            <th>租金单价（元/m²/月）</th>
            <td>{{ leaseInfo.rentUnitPrice | noData }}</td>
            <th>总租金（元）</th>
            <td>{{ leaseInfo.totalRent | noData }}</td>
          </tr>
          <tr>
            <th>履约保证金（元）</th>
            <td>{{ leaseInfo.performanceBond | noData }}</td>
            <th>付款方式</th>
            <td>{{ leaseInfo.paymentMethodStr | noData }}</td>
          </tr>
        </table>
        <div class="title m-b-8 m-t-24">创建信息</div>
        <div class="font-size-14 line-height-20">
          {{ leaseInfo.creator }} {{ leaseInfo.createTime }} 创建
        </div>
      </div>
      <div v-else style="padding-top: 120px">
        <empty-data description="暂无数据" />
      </div>
    </div>
    <dialog-cmp
      title="查看记录"
      :visible.sync="recordVisible"
      width="620px"
      :have-operation="false"
    >
      <div class="timeline-container">
        <template v-if="recordInfo && recordInfo.length > 0">
          <div
            class="timeline-item"
            v-for="(item, index) in recordInfo"
            :key="index+ 'record'"
          >
            <div class="timeline-left">
              <div class="item-icon">{{ item.creatorName.charAt(0) }}</div>
              <div class="item-line"></div>
            </div>
            <div class="timeline-right">
              <div class="timeline-header">
                <div class="flex align-items-center">
                  <span class="font-size-14 header-name line-1">{{
                      item.creatorName
                    }}</span>
                  <span class="m-l-12" v-if="item.operationDate">跟进时间：{{ item.operationDate }}</span>
                  <span class="m-l-12">{{ item.typeName }}</span>
                </div>
              </div>
              <div class="timeline-content">
                <div :class="{ 'color-info': item.type === 3 }">{{ item.content }}</div>
                <div class="annex-wrapper" v-if="getAttach(item.attachMap).length">
                  <files-list :files="getAttach(item.attachMap)" onlyForView />
                </div>
                <div class="m-t-10 font-size-12 flex align-items-center justify-content-between">
                  <div>
                    <span
                        v-if="
                      (tabModel === 1 || tabModel === 4 || tabModel === 5) &&
                      item.contactName
                    "
                        class="m-r-16"
                    >
                    联系人：{{ item.contactName }}
                  </span>
                    <span>更新时间：{{ item.createTime }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </dialog-cmp>

    <dialog-cmp
      title="修改详情"
      :visible.sync="detailVisible"
      width="620px"
      :have-operation="false"
    >
    <drive-table
      ref="drive-table"
      :columns="tableColumnRecord"
      :table-data="tableDataRecord"
    >
    </drive-table>
    </dialog-cmp>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import {
  getMerchantMeContactList,
  getMerchantLogList,
  getMerchantAppealList,
  getMerchantAppealDel,
  getGroundList,
  getProjectGroundDel,
  getProposedLease, merchantLogDeleteFollow
} from '../../../api'
import FilesList from '@/components/Uploader/files'
import handleClipboard from '@/utils/clipboard'
import ColumnMixins from './column'
export default {
  name: 'LeftBody',
  mixins: [ColumnMixins],
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  components: { FilesList, BasicTab },
  inject: ['DetailDrawerLeft', 'DetailDrawer', 'ProjectPool'],
  data() {
    return {
      detailVisible: false,
      tabModel: 1,
      tableData: [],
      tableDataRecord: [],
      tabs: [
        {
          label: '招商跟进',
          key: 1
        },
        {
          label: '企业跟进',
          key: 4
        },
        {
          label: '项目需求',
          key: 2
        },
        {
          label: '拟租赁',
          key: 6
        },
        {
          label: '项目名片',
          key: 3
        },
        {
          label: '项目批示',
          key: 5
        }
      ],
      current: 0,
      followUpTabs: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '跟进',
          value: 1
        },
        {
          label: '动态',
          value: 3
        }
      ],
      dataList: [],
      contactList: [], // 联系人日志列表
      appealList: [], // 项目诉求列表
      leadingList: [],
      leaseInfo: {},
      recordInfo: [], // 原记录详情
      recordVisible: false
    }
  },
  watch: {
    recordVisible(val) {
      if (!val) {
        this.recordInfo = this.$options.data().recordInfo
      }
    },
    detailInfo: {
      handler() {
        this.getMerchantLogList()
      },
      deep: true
    }
  },
  methods: {
    viewRecordDetail(row) {
      this.detailVisible = true
      this.tableDataRecord = row?.changeList || []
    },
    // 删除跟进
    itemDeleteHandle(row) {
      this.$confirm('确定删除此记录？', '提示').then(() => {
        merchantLogDeleteFollow({ id: row.id }).then(() => {
          this.$toast.success('删除成功')
          this.getMerchantLogList()
          this.DetailDrawer.getMeRecordDetail(this.detailInfo.id)
        })
      })
    },
    // 查看原记录
    viewRecord(row) {
      this.recordInfo = [row]
      this.recordVisible = true
    },
    // 编辑跟进
    itemEditHandle(row) {
      this.DetailDrawerLeft.$refs.leftFooter.initData(this.tabModel)
      this.DetailDrawerLeft.$refs.leftFooter.editHandle(row)
    },
    copyHandle(item, e) {
      const contactText = item.contact ? `联系人姓名：${item.contact}` : ''
      const contactPhoneText = item.contactPhone
        ? `，联系方式：${item.contactPhone}`
        : ''
      const labelNameText = item.labelName
        ? `，相关身份标签：${item.labelName}`
        : ''
      const text = contactText + contactPhoneText + labelNameText
      handleClipboard(text, e)
    },
    unfoldHandle(row, index) {
      this.$set(this.dataList[index], 'unfold', !row.unfold)
    },
    radioChange(e) {
      this.current = 0
      this.dataList = []
      this.DetailDrawerLeft.$refs.leftFooter.initData(e)
      this.getMerchantLogList()
    },
    tabsChange(e) {
      this.current = e
      this.getMerchantLogList()
    },
    getAttach(attachMap = {}) {
      return attachMap.investment || []
    },
    getMerchantMeContactList() {
      getMerchantMeContactList({ recordId: this.detailInfo.id }).then(res => {
        this.contactList = res || []
      })
    },
    // 项目诉求
    getMerchantAppealList() {
      getMerchantAppealList({ recordId: this.detailInfo.id }).then(res => {
        this.appealList = res || []
      })
    },
    // 项目诉求 - 编辑
    editHandler(val) {
      this.$emit('editHandler', val)
    },
    // 删除 - 项目诉求
    delHandler(val) {
      this.$confirm('确定删除？', '提示').then(async () => {
        if (this.tabModel === 4) {
          await getMerchantAppealDel(val.id)
          this.getMerchantAppealList()
        } else {
          await getProjectGroundDel(val.id)
          this.getGroundList()
        }
        this.DetailDrawer.getMeRecordDetail(this.detailInfo.id)
        this.ProjectPool.getData()
        this.$toast.success('删除成功')
      })
    },
    // 项目落地
    getGroundList() {
      getGroundList({ recordId: this.detailInfo.id }).then(res => {
        this.leadingList = res || []
        this.leadingList.forEach(item => {
          item.list = []
          item.list.push({
            entName: item.entName,
            creditCode: item.creditCode,
            establishmentTime: item.establishmentTime,
            address: item.address
          })
        })
      })
    },
    async getProposedLease() {
      const res = await getProposedLease(this.detailInfo.id)
      this.leaseInfo = res
    },
    getMerchantLogList() {
      // let typeId = ''
      if (this.tabModel === 2) {
        // typeId = this.current ? this.current : ''
      } else if (this.tabModel === 3) {
        return this.getMerchantMeContactList()
      } else if (this.tabModel === 6) {
        return this.getProposedLease()
      }
      const params = {
        recordId: this.detailInfo.id,
        types: this.tabModel,
        // typeId,
        queryType: this.current
      }
      getMerchantLogList(params).then(res => {
        this.dataList = res || []
      })
    }
  }
}
</script>

<style scoped lang="scss">
.left-body {
  margin-top: 20px;
  :deep(.el-radio-button) {
    margin-right: 8px;
    &:last-child {
      margin-right: 0;
    }
    .el-radio-button__inner {
      border: 0;
      border-radius: 4px;
      font-size: 14px;
      color: #292929;
    }
    &.is-active {
      .el-radio-button__inner {
        color: #fff;
      }
    }
    &:not(.is-active):hover {
      .el-radio-button__inner {
        background: #f2f5f7;
      }
    }
  }
  :deep(.tabs-wrapper) {
    margin-top: 8px;
    .tabs-item {
      height: 32px;
      line-height: 32px;
      &::after {
        display: block;
        content: '';
        width: 1px;
        height: 16px;
        background: #e9edf0;
        position: absolute;
        right: 0;
        top: 8px;
      }
      &:first-child {
        padding-left: 0;
      }
      &:last-child {
        &::after {
          display: none;
        }
      }
      &.active::before {
        display: none;
      }
    }
  }
}
.timeline-container {
  margin-top: 12px;
  .timeline-item {
    position: relative;
    .timeline-left {
      position: absolute;
      left: 0;
      top: 0;
      height: 100%;
      .item-icon {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        overflow: hidden;
        text-align: center;
        line-height: 30px;
        font-size: 12px;
        @include background_color(--color-primary);
        @include font_color(--color-white);
        position: absolute;
        left: 0;
      }
      .item-line {
        width: 1px;
        height: calc(100% - 38px);
        @include background_color(--border-color-base);
        position: absolute;
        top: 30px;
        left: 15px;
      }
    }
    .timeline-right {
      margin-left: 32px;
      padding-right: 10px;
      border-radius: 4px;
      cursor: pointer;
      overflow: hidden;
      transition: all 0.3s ease-in-out;
      &:hover {
        background: #f7f7f7;
      }
      .timeline-header {
        margin-top: 10px;
        margin-left: 10px;
        font-size: 12px;
        color: #adadad;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .header-name {
          max-width: 200px;
        }
        .copy-text {
          display: none;
        }
        &:hover {
          .copy-text {
            display: inline-block;
          }
        }
      }
      .timeline-content {
        font-size: 14px;
        padding: 12px 10px 24px;
        color: #404040;
        line-height: 20px;
        word-wrap: break-word;
        .annex-wrapper {
          padding-top: 10px;
        }
      }
    }
  }
  &.contact-timeline-container {
    .timeline-header {
      color: #404040 !important;
    }
    .timeline-content {
      color: #adadad !important;
    }
  }
}
.title {
  font-size: 14px;
  line-height: 20px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.6);
}
table {
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  font-size: 14px;
}

th {
  width: 220px;
  @include background_color_mix(--color-primary, #ffffff, 96%);
  padding: 10px;
  font-weight: 400;
  text-align: left;
}

td {
  width: 480px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  word-break: break-all;
  padding: 10px 10px 11px 10px;
  line-height: 1.6em;
  word-wrap: break-word;
}

.content-row {
  display: flex;
  align-items: center;
  width: 100%;
}

.content-text {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.content-action {
  flex-shrink: 0;
}

</style>
