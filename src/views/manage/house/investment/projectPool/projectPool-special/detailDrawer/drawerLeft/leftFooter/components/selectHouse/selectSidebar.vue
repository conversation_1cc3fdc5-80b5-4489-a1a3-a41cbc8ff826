<template>
  <div class="select-sidebar">
    <!--    <el-tooltip class="item" effect="dark" :content="parkName" placement="top">-->
    <!--      <div class="m-b-24 line-1 pointer" style="width: 180px">-->
    <!--        {{ parkName }}-->
    <!--      </div>-->
    <!--    </el-tooltip>-->
    <div v-for="(item, inx) in houseBody" :key="inx">
      <div class="text-s font-size-12 m-b-14 m-l-8">{{ item.title }}</div>
      <template v-if="item.list.length > 0">
        <ul v-for="(val, i) in item.list" :key="i">
          <li
            :class="[
              currentActive === val.time ? 'active' : '',
              !val.show ? 'disabled-show' : ''
            ]"
            @click="changeHouseList(val, item.title)"
          >
            <span>{{ val.name }}</span>
          </li>
        </ul>
      </template>
      <template v-else>
        <div class="text-s font-size-12 m-b-14 text-center">
          {{ item.noData }}
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import {
  getBusinessTypes,
  getRoomSelectionCount,
  getRoomSelectionList,
  getHouseLeaseTypes,
  getHouseUseTypes
} from '@/views/manage/house/investment/projectPool/projectPool-special/api'

export default {
  name: 'SelectSidebar',
  props: {
    parkId: {
      type: Number
    },
    parkName: {
      type: String
    }
  },
  data() {
    return {
      currentActive: 6,
      houseBody: [
        {
          title: '房间类型',
          noData: '暂无数据',
          list: []
        },
        {
          title: '房间用途',
          noData: '根据选择的房间类型展示',
          list: []
        },
        {
          title: '房间业态',
          list: [],
          noData: '根据选择的房间用途展示'
        },
        {
          title: '房源标签',
          noData: '暂无数据',
          list: [
            {
              name: '即将到期',
              key: 10,
              show: true,
              time:
                10 +
                Math.floor(
                  new Date().getTime() /
                    Math.pow(10, String(new Date().getTime()).length - 6)
                )
            },
            {
              name: '意向房源',
              key: 11,
              show: true,
              time:
                11 +
                Math.floor(
                  new Date().getTime() /
                    Math.pow(10, String(new Date().getTime()).length - 6)
                )
            },
            {
              name: '问题房源',
              key: 12,
              show: true,
              time:
                12 +
                Math.floor(
                  new Date().getTime() /
                    Math.pow(10, String(new Date().getTime()).length - 6)
                )
            },
            {
              name: '健康房源',
              key: 13,
              show: true,
              time:
                13 +
                Math.floor(
                  new Date().getTime() /
                    Math.pow(10, String(new Date().getTime()).length - 6)
                )
            }
          ]
        }
      ],
      numType: '',
      numTag: '',
      num: {},
      buildingId: '',
      active: '',
      current: '',
      visible: false,
      parkN: '',
      type: null,
      paramObj: {
        roomTypes: '',
        roomUseTypes: '',
        businessTypes: '',
        roomLabel: ''
      }
    }
  },
  //
  mounted() {
    this.getHouseLeaseTypes()
    // setTimeout(() => {
    //   this.paramObj.roomTypes = this.type
    //   this.getHouseUseTypes(this.type)
    // }, 100)
  },
  methods: {
    getHouseLeaseTypes() {
      getHouseLeaseTypes(0).then(res => {
        let times = new Date().getMonth()
        for (let i = 0; i < this.houseBody.length; i++) {
          if (this.houseBody[i].title === '房间类型') {
            this.houseBody[i].list = [
              ...res.map((item, inx) => {
                return {
                  name: item.label,
                  key: item.key,
                  show: item.show,
                  time:
                    inx +
                    Math.floor(times / Math.pow(10, String(times).length - 6))
                }
              })
            ]
          }
        }
        // this.type = this.houseBody[0].list[0].key
      })
    },
    getHouseUseTypes(type) {
      getHouseUseTypes(type, 0).then(res => {
        let times = new Date().getHours()
        for (let i = 0; i < this.houseBody.length; i++) {
          if (this.houseBody[i].title === '房间用途') {
            this.houseBody[i].list = [
              ...res.map((item, inx) => {
                return {
                  name: item.label,
                  key: item.key,
                  show: item.show,
                  time:
                    inx +
                    Math.floor(times / Math.pow(10, String(times).length - 6))
                }
              })
            ]
          }
        }
      })
    },
    getBusinessTypes(type) {
      getBusinessTypes(type).then(res => {
        let times = new Date().getTime()
        for (let i = 0; i < this.houseBody.length; i++) {
          if (this.houseBody[i].title === '房间业态') {
            this.houseBody[i].list = [
              ...res.map((item, inx) => {
                return {
                  name: item.label,
                  key: item.key,
                  show: item.show,
                  time:
                    inx +
                    Math.floor(times / Math.pow(10, String(times).length - 6))
                }
              })
            ]
          }
        }
        // this.currentActive = this.houseBody[2].list[0].key
      })
    },
    async getNumType(roomIds = []) {
      const res = await getRoomSelectionCount({
        parkId: this.parkId,
        roomIds: roomIds.join(',')
      })
      this.num = res
    },
    getRequestFN(key, title) {
      if (title === '房间类型') {
        this.getHouseUseTypes(key)
        this.houseBody[2].list = []
        this.paramObj.roomTypes = key
        this.paramObj.roomUseTypes = ''
        this.paramObj.businessTypes = ''
      } else if (title === '房间用途') {
        this.getBusinessTypes(key)
        this.paramObj.roomUseTypes = key
        this.paramObj.businessTypes = ''
      } else if (title === '房间业态') {
        this.paramObj.businessTypes = key
      } else if (title === '房源标签') {
        this.paramObj.roomLabel = key
      }
    },
    async changeHouseList(val, type) {
      if (!val.show) return false
      this.currentActive = val.time
      this.getRequestFN(val.key, type)
      // if(this.active !== 0 || this.active !== 1 || this.active !== 2 ||this.active !== 3 ||this.active !== 4 ){
      //   this.active = ''
      //   this.$emit('changeTypeAll')
      //   return
      // }
      let removeHouseId =
        this.$parent.$parent.$parent.$parent.tableDataHouse.map(
          item => item.roomId
        )
      let idArr = removeHouseId ? Array.from(new Set(removeHouseId)) : []
      let data = idArr.join(',')
      this.active = val.key
      if (!this.buildingId) return
      let res = await getRoomSelectionList({
        buildingId: this.buildingId,
        parkId: this.parkId,
        roomIds: this.$route.query.id ? data : '',
        // businessTypes: val.key,
        // roomTypes: val.key,
        // roomLabel: val.key,
        ...this.paramObj
      })
      this.$emit('changeHouseType', res)
    },
    async clickHouseTag(val) {
      // if(this.current === 0 || this.current === 1 || this.current === 2 ||this.current === 3 ){
      //   this.current = ''
      //   this.$emit('changeTypeAll')
      //   return
      // }
      let removeHouseId = this.$parent.$parent.$parent.$parent.removeHouseId
      let idArr = removeHouseId ? Array.from(new Set(removeHouseId)) : []
      let data = idArr.join(',')
      this.current = val
      if (!this.buildingId) return
      let res = await getRoomSelectionList({
        buildingId: this.buildingId,
        parkId: this.parkId,
        roomIds: this.$route.query.id ? data : '',
        roomLabel: val,
        roomTypes: this.active
      })
      this.$emit('changeHouseType', res)
    }
  }
}
</script>

<style lang="scss" scoped>
.text-center {
  text-align: center;
}
.disable {
  cursor: not-allowed;
  opacity: 0.3;
  pointer-events: none;
}
.select-sidebar {
  width: 200px;
  height: 100%;
  border-right: 1px solid #e7e7e7;
  .text-s {
    color: rgba(0, 0, 0, 0.4);
  }
  li {
    width: 180px;
    height: 28px;
    font-size: 14px;
    line-height: 28px;
    text-align: left;
    padding-left: 33px;
    margin-bottom: 6px;
    border-radius: 3px;
    cursor: pointer;
    &:hover {
      background-color: #e9f0ff;
    }
  }
  .active {
    background-color: #e9f0ff;
  }
  .disabled-show {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.4);
    background-color: transparent !important;
  }
}
</style>
