import DiffSelect from './DiffSelect'
import { wayMenus } from '../../status'

export default {
  components: { DiffSelect },
  data() {
    return {
      formConfigure: {
        labelWidth: '90px',
        descriptors: {
          headPerson: {
            form: 'select',
            label: '项目经理',
            dynamicDisabled: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目经理'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          joinPerson: {
            form: 'select',
            label: '参与人',
            dynamicDisabled: true,
            rule: [
              {
                type: 'array',
                message: '请选择参与人'
              }
            ],
            attrs: {
              multiple: true,
              clearable: true
            },
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          creatorName: {
            form: 'input',
            label: '添加人',
            disabled: true
          },
          createTime: {
            form: 'input',
            label: '添加时间',
            disabled: true
          },
          updateTime: {
            form: 'input',
            label: '更新时间',
            disabled: true
          },
          pjctType: {
            form: 'select',
            label: '项目类型',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目类型'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          // colleges: {
          //   form: 'input',
          //   label: '高校院所',
          //   dynamicDisabled: true,
          //   rule: [
          //     {
          //       type: 'string',
          //       message: '暂无数据'
          //     }
          //   ],
          //   attrs: {
          //     maxLength: 30
          //   },
          //   events: {
          //     change: () => {
          //       this.updateHandle()
          //     }
          //   }
          // },
          pjctRoad: {
            form: 'select',
            label: '招商途径',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择招商途径'
              }
            ],
            options: wayMenus,
            events: {
              change: e => {
                // this.updateHandle()
                this.pjctRoadChange(e)
              }
            }
          },
          source: {
            form: 'select',
            label: '项目来源',
            dynamicDisabled: true,
            hidden: true,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择项目来源'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          pjctIndustry: {
            form: 'select',
            label: '推荐至',
            dynamicDisabled: true,
            hidden: true,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择推荐至'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          // group: {
          //   form: 'select',
          //   label: '项目分组',
          //   dynamicDisabled: true,
          //   rule: [
          //     {
          //       type: 'number',
          //       message: '请选择项目分组'
          //     }
          //   ],
          //   options: [],
          //   events: {
          //     change: () => {
          //       this.updateHandle()
          //     }
          //   }
          // },
          // diff: {
          //   form: 'component',
          //   label: '项目难度',
          //   dynamicDisabled: true,
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请选择项目难度'
          //     }
          //   ],
          //   options: [],
          //   render: () => {
          //     return (
          //       <DiffSelect
          //         v-model={this.formModel.diff}
          //         options={this.formConfigure.descriptors.diff.options}
          //         disabled={this.formConfigure.descriptors.diff.disabled}
          //         onChange={() => {
          //           this.updateHandle()
          //         }}
          //       />
          //     )
          //   }
          // },
          process: {
            form: 'select',
            label: '项目进展',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目进展'
              }
            ],
            options: [],
            events: {
              change: e => {
                this.processColorHandle(e)
                this.updateHandle()
              }
            }
          },
          industry: {
            form: 'select',
            label: '行业类型',
            dynamicDisabled: true,
            rule: [
              {
                type: 'string',
                message: '请选择行业类型'
              }
            ],
            attrs: {
              filterable: true
            },
            options: [],
            events: {
              change: e => {
                this.industryChange(e)
              }
            }
          },
          industrySub: {
            form: 'select',
            label: '细分领域',
            dynamicDisabled: true,
            rule: [
              {
                type: 'string',
                message: '请选择细分领域'
              }
            ],
            // attrs: {
            //   filterable: true
            // },
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          parkId: {
            form:'select',
            label: '落地园区',
            dynamicDisabled: true,
            rule: [
              {
                type:'number',
                message: '请选择落地园区'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
        }
      },
      formConfigure1: {
        labelWidth: '110px',
        descriptors: {
          projectOwnership: {
            form: 'input',
            label: '项目所属',
            disabled: true
          },
          industry: {
            form: 'select',
            label: '所属行业',
            dynamicDisabled: true,
            rule: [
              {
                type: 'string',
                message: '请选择所属行业'
              }
            ],
            attrs: {
              filterable: true
            },
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          pjctContact: {
            form: 'input',
            label: '项目方联系人',
            disabled: true
          },
          contactPhone: {
            form: 'input',
            label: '项目方联系方式',
            disabled: true
          },
          pjctType: {
            form: 'select',
            label: '项目类型',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目类型'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          colleges: {
            form: 'input',
            label: '高校院所',
            dynamicDisabled: true,
            rule: [
              {
                type: 'string',
                message: '暂无数据'
              }
            ],
            attrs: {
              maxLength: 30
            },
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          source: {
            form: 'select',
            label: '项目来源',
            dynamicDisabled: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目来源'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          process: {
            form: 'select',
            label: '项目进展',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目进展'
              }
            ],
            options: [],
            events: {
              change: e => {
                this.processColorHandle(e)
                this.updateHandle()
              }
            }
          },
          headPerson: {
            form: 'select',
            label: '项目经理',
            dynamicDisabled: true,
            rule: [
              {
                type: 'number',
                message: '请选择项目经理'
              }
            ],
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          },
          joinPerson: {
            form: 'select',
            label: '参与人',
            dynamicDisabled: true,
            rule: [
              {
                type: 'array',
                message: '请选择参与人'
              }
            ],
            attrs: {
              multiple: true,
              clearable: true
            },
            options: [],
            events: {
              change: () => {
                this.updateHandle()
              }
            }
          }
        }
      }
    }
  }
}
