import ProjectLocation from './ProjectLocation'
import PjctIndustryList from './pjctIndustryList'
import { validateContact } from '@/utils/validate'

export default {
  components: {
    ProjectLocation,
    PjctIndustryList
  },
  data() {
    return {
      formConfigure: {
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '企业名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          provinceId1: {
            form: 'component',
            label: '项目所在地',
            rule: [
              {
                required: true,
                type: 'object',
                message: '请选择项目所在地'
              }
            ],
            render: () => {
              return (
                <ProjectLocation
                  ref="projectLocation"
                  visible={this.drawerVisible}
                />
              )
            }
          },
          entLabel: {
            form: 'select',
            label: '企业标签',
            span: 12,
            options: [],
            rule: [
              {
                required: false,
                type: 'array',
                message: '请选择企业标签'
              }
            ],
            attrs: {
              multiple: true,
              clearable: true
            }
          },
          industryList: {
            form: 'cascader',
            label: '所属行业',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择所属行业'
              }
            ],
            attrs: {
              filterable: true,
              collapseTags: true,
              props: {
                label: 'label',
                value: 'id',
                children: 'children'
              }
            }
          },
          pjctContact: {
            form: 'input',
            label: '企业负责人',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业负责人'
              }
            ],
            attrs: {
              maxLength: 50,
              clearable: true
            },
            span: 12
          },
          contactPhone: {
            form: 'input',
            label: '联系方式',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                validator: (rule, value, callback) => {
                  validateContact(rule, value, callback, false)
                }
              }
            ],
            attrs: {
              maxlength: 30
            }
          },
          contactPosition: {
            form: 'select',
            label: '联系人职位',
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择联系人职位'
              }
            ],
            span: 12,
            attrs: {
              clearable: true
            }
          },
          intendedArea: {
            form: 'select',
            label: '意向面积',
            span: 12,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择意向面积'
              }
            ],
            attrs: {
              clearable: true
            }
          },
          visitDate: {
            form: 'date',
            label: '走访/接待时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择走访/接待时间'
              }
            ]
          },
          parkId: {
            form: 'select',
            label: '落地园区',
            span: 12,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择落地园区'
              }
            ],
            options: [],
            attrs: {
              clearable: true
            }
          },
          projectInfo: {
            form: 'component',
            render: () => {
              return <div class="font-strong m-t-12 font-size-15">项目简介</div>
            }
          },
          background: {
            form: 'input',
            label: '主营业务',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主营业务'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 1000,
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              showWordLimit: true
            }
          },
          marketInfo: {
            form: 'input',
            label: '市场情况',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入市场情况'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          initTeam: {
            form: 'input',
            label: '创始团队',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入创始团队'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          projectLight: {
            form: 'input',
            label: '项目亮点',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入项目亮点'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          historyFinance: {
            form: 'input',
            label: '历史融资情况',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入历史融资情况'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          financeInfo: {
            form: 'input',
            label: '财务情况（近三年营收利润）',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入财务情况（近三年营收利润）'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          landForm: {
            form: 'input',
            label: '落地形式',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入落地形式'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          demand: {
            form: 'component',
            render: () => {
              return <div class="font-strong m-t-12 font-size-15">落地需求</div>
            }
          },
          pjctDemand: {
            form: 'input',
            label: '融资需求',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入融资需求'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          placeDemand: {
            form: 'input',
            label: '场地需求',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入场地需求'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          policyDemand: {
            form: 'input',
            label: '政策需求',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入政策需求'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          otherDemand: {
            form: 'input',
            label: '其他需求',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入其他需求'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize:{ minRows: 3, maxRows: 8},
              resize: 'vertical',
              maxlength: 1000,
              showWordLimit: true
            }
          },
          attaches: {
            form: 'component',
            label: '企业介绍材料',
            rule: [
              {
                type: 'array',
                message: '请上传企业介绍材料'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'policyInfoAttach'
              },
              mulity: true,
              maxSize: 20,
              limit: 3
            }
          },
          headerPeronList: {
            form: 'cascader',
            label: '项目经理',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择项目经理'
              }
            ],
            attrs: {
              filterable: true,
              collapseTags: true,
              props: {
                label: 'label',
                value: 'key',
                children: 'children'
              }
            }
          },
          joinPeronList: {
            form: 'cascader',
            label: '参与人',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择参与人'
              }
            ],
            attrs: {
              filterable: true,
              collapseTags: true,
              props: {
                multiple: true,
                label: 'label',
                value: 'key',
                children: 'children'
              }
            }
          },
          pjctSourceList: {
            form: 'component',
            customLabel: () => {
              return (
                <div>
                  <span class="is-required-label">
                    <span class="red-star">*</span>
                    <span>招商途径</span>
                  </span>
                </div>
              )
            },
            rule: [
              {
                required: false,
                type: 'array',
                message: '请选择招商途径'
              }
            ],
            render: () => {
              return (
                <PjctIndustryList
                  ref="pjctSourceList"
                  v-model={this.fromModel.pjctSourceList}
                  is-required={true}
                  options={this.pjctSourceListOptions}
                />
              )
            },
            customTip: () => {
              return <div class="is-required-label">*</div>
            }
          },
          pjctIndustryList: {
            form: 'component',
            label: '推荐至',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请选择项推荐信息'
              }
            ],
            render: () => {
              return (
                <PjctIndustryList
                  ref="pjctIndustryList"
                  v-model={this.fromModel.pjctIndustryList}
                  is-required={false}
                  options={this.pjctIndustryListOptions}
                />
              )
            }
          },
          pjctType: {
            form: 'select',
            label: '项目类型',
            options: [],
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目类型'
              }
            ],
            attrs: {
              filterable: true
            }
          },
          pjctProcess: {
            form: 'select',
            label: '项目进展',
            span: 12,
            options: [],
            rule: [
              {
                type: 'number',
                required: true,
                message: '请选择项目进展'
              }
            ],
            attrs: {
              filterable: true
            }
          }
        }
      }
    }
  }
}
