<template>
  <div class="p-24 bg-white">
    <drive-table
      ref="drive-table"
      :api-fn="meRecordPage"
      :columns="tableColumn"
      :extral-querys="extralQuerys"
      :search-querys-hook="searchQueryHook"
      @setLoading="setLoading"
    >
      <template v-slot:operate-left>
        <basic-tab
          :tabs-data="tabsData"
          :current="extralQuerys.examineStatus"
          :disabled="reqLoading"
          @tabsChange="tabsChange"
        />
      </template>
      <template v-slot:operate-right>
        <div class="flex align-items-center">
          <el-switch
            v-model="extralQuerys.careFlag"
            active-text="只看我关注的"
            :disabled="reqLoading"
            @change="careChange"
          />
          <el-button type="text" class="m-l-8" @click="searchDialogVisible = true">
            <svg-icon icon-class="search"/>
            全文查找
          </el-button>
        </div>
      </template>
    </drive-table>
    <search-dialog :visible.sync="searchDialogVisible" />
  </div>
</template>

<script>
import ColumnMixins from './column'
import {
  getMeRecordStatus, meRecordCancelExamine,
  meRecordCareProject,
  meRecordPage
} from '@/views/manage/house/investment/projectPool/projectPool-special/api'
import BasicTab from '@/components/BasicTab/index.vue'
import SearchDialog from './searchDialog.vue'
import { deptList } from '@/api/common'

export default {
  name: 'ProjectList',
  components: { SearchDialog, BasicTab },
  mixins: [ColumnMixins],
  props: {
    settingTabs: {
      type: Array,
      default: () => []
    },
    personList: {
      type: Array,
      default: () => []
    },
    industry: {
      type: Array,
      default: () => []
    },
    landPark: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      meRecordPage,
      extralQuerys: {
        // type: 2,
        scope: 0,
        occupationType: '',
        examineStatus: 2,
        careFlag: false
      },
      tabsData: [],
      reqLoading: true,
      searchDialogVisible: false
    }
  },
  watch: {
    landPark: {
      handler(val) {
        const parkIndex = this.tableColumn.findIndex(
          item => item.prop === 'parkId'
        )
        this.tableColumn[parkIndex].search.options = val || []
      },
      deep: true,
      immediate: true
    },
    settingTabs: {
      handler(val) {
        const row = val.find(item => item.value === 5)
        const pjctProcessIndex = this.tableColumn.findIndex(
          item => item.prop === 'pjctProcess'
        )
        this.tableColumn[pjctProcessIndex].search.options = row.list.map(
          item => {
            return {
              label: item.typeName,
              value: item.id
            }
          }
        )
        const typeRow = val.find(item => item.value === 11)
        const pjctTypeIndex = this.tableColumn.findIndex(
          item => item.prop === 'projectType'
        )
        this.tableColumn[pjctTypeIndex].search.options = typeRow.list.map(
          item => {
            return {
              label: item.typeName,
              value: item.id
            }
          }
        )
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getMeRecordStatus()
    this.deptList()
  },
  methods: {
    cancelExamineHandler(row) {
      this.$confirm('是否撤回该项目信息?').then(() => {
        meRecordCancelExamine({ id: row.id }).then(() => {
          this.$toast.success('撤回成功')
          this.$refs['drive-table'].triggerSearch()
        })
      })
    },
    deptList() {
      deptList().then(res => {
        const deptIndex = this.tableColumn.findIndex(
          item => item.prop === 'deptId'
        )
        this.tableColumn[deptIndex].search.options = res.map(item => {
          return {
            label: item.name,
            value: item.id
          }
        })
      })
    },
    getMeRecordStatus() {
      getMeRecordStatus().then(res => {
       const list = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        }).filter(item => {
          return item.value > 0
        })
        const index = list.findIndex(item => item.value === 2)
        if (index > -1) {
          // 从原位置移除该元素
          const [item] = list.splice(index, 1)
          // 添加到数组开头
          list.unshift(item)
        }
        this.tabsData = list
        const status = this.$route.query.status
        if (status) {
          this.extralQuerys.examineStatus = Number(status)
        } else {
          this.extralQuerys.examineStatus = this.tabsData[0].value
        }
      })
    },
    setLoading(val) {
      this.reqLoading = val
    },
    async followHandle(row) {
      const { id, careFlag } = row
      await meRecordCareProject({ id })
      row.careFlag = !careFlag
      this.$refs['drive-table'].resetTableKeyHandle()
      if (row.careFlag) {
        this.$toast.success('已关注此项目')
      } else {
        this.$toast.success('已取消关注此项目')
      }
    },
    careChange() {
      this.$refs['drive-table'].triggerSearch()
    },
    tabsChange(e) {
      this.extralQuerys.examineStatus = e
      this.$refs['drive-table'].triggerSearch()
    },
    // 重置搜索参数
    searchQueryHook(e) {
      const temp = e
      const [visitDateStartDate = '', visitDateEndDate = ''] = e.visitDate || []
      if (e.visitDate && e.visitDate.length > 0) {
        temp.visitDateStartDate = visitDateStartDate
        temp.visitDateEndDate = visitDateEndDate
      }
      delete e.visitDate
      return temp
    },
    getNewTable(e) {
      this.extralQuerys.scope = e
      this.$refs['drive-table'].refreshTable()
    },
    detailHandler(scope) {
      let row = {
        ...scope.row,
        typeIndex: this.$refs['drive-table'].data,
        selfIndex: scope.$index
      }
      this.$emit('detailHandle', row)
    },
    editHandler({row}) {
      this.$emit('editHandle', row)
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.driven-table-search) {
  .el-form-item__label {
    flex: 0 0 102px !important;
  }
}
:deep(.process-wrapper) {
  color: var(--fill-process-color);
  &:before {
    display: inline-block;
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--fill-process-color);
    margin-right: 8px;
  }
}

:deep(.operate) {
  margin-bottom: 10px;
  div {
    * {
      margin-bottom: 0;
    }
  }
}
</style>
