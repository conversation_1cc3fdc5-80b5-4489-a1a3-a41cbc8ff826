<template>
  <el-dropdown trigger="click" @command="handleCommand">
    <div
      class="el-dropdown-link"
      @click.stop
      v-permission="[
        // ...routeButtonsPermission.EDIT,
        // ...routeButtonsPermission.DELETE,
        ...routeButtonsPermission.TRANSACT_PARK
      ]"
    >
<!--      <template v-if="showMore">-->
<!--        <span v-if="isTable" class="color-primary pointer m-l-8">更多</span>-->
<!--        <template v-else >-->
<!--          <template v-if="!item.lockSts && item.enterSts !== 1 && item.enterSts !== 2">-->
<!--            <svg-icon v-permission="routeButtonsPermission.TRANSACT_PARK" icon-class="ellipsis" class="pointer m-l-8" />-->
<!--          </template>-->
<!--        </template>-->
<!--      </template>-->
    </div>
    <el-dropdown-menu slot="dropdown">
      <!--      <el-dropdown-item-->
      <!--        v-permission="routeButtonsPermission.EDIT"-->
      <!--        command="lock"-->
      <!--        class="investment-more-item-btn"-->
      <!--      >-->
      <!--        <el-link type="warning" :underline="false">{{-->
      <!--          item.lockSts ? '解锁' : '锁定'-->
      <!--        }}</el-link>-->
      <!--      </el-dropdown-item>-->
      <template v-if="!item.lockSts">
<!--        <el-dropdown-item-->
<!--          v-if="item.isUpdateOrDelete"-->
<!--          v-permission="routeButtonsPermission.EDIT"-->
<!--          command="edit"-->
<!--          class="investment-more-item-btn"-->
<!--        >-->
<!--          <el-link class="color-black" :underline="false">{{-->
<!--            routeButtonsTitle.EDIT-->
<!--          }}</el-link>-->
<!--        </el-dropdown-item>-->
        <template v-if="item.enterSts !== 1 && item.enterSts !== 2">
<!--          <el-dropdown-item-->
<!--            v-if="item.isUpdateOrDelete"-->
<!--            v-permission="routeButtonsPermission.DELETE"-->
<!--            command="delete"-->
<!--            class="investment-more-item-btn"-->
<!--          >-->
<!--            <el-link type="danger" :underline="false">{{-->
<!--              routeButtonsTitle.DELETE-->
<!--            }}</el-link>-->
<!--          </el-dropdown-item>-->
          <template v-if="hasParkIn">
            <el-dropdown-item
              v-if="(item.enterStatus === 0 || item.enterStatus === 4) && item.examineStatus === 2"
              v-permission="routeButtonsPermission.TRANSACT_PARK"
              command="transact"
              class="investment-more-item-btn"
            >
              <el-link type="primary" :underline="false">{{
                routeButtonsTitle.TRANSACT_PARK
              }}</el-link>
            </el-dropdown-item>
          </template>
        </template>
      </template>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'MoreOperate',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    isTable: {
      type: Boolean,
      default: false
    },
    hasParkIn: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    showMore() {
      const item = this.item
      const conditions1 = !item.lockSts // 解锁状态
      const conditions2 = item.isUpdateOrDelete // 能编辑状态
      const conditions3 = item.enterSts !== 1 && item.enterSts !== 2
      const conditions4 = this.hasParkIn // 有入园操作
      const conditions5 = (item.enterStatus === 0 || item.enterStatus === 4) && item.examineStatus === 2
      return (
        conditions1 &&
        (conditions2 || (conditions3 && conditions4 && conditions5))
      )
    }
  },
  inject: ['ProjectPool'],
  methods: {
    handleCommand(command) {
      if (command === 'lock') {
        const lockSts = this.item.lockSts ? '解锁' : '锁定'
        this.$confirm(`确定${lockSts}吗？`, '提示').then(() => {
          this.ProjectPool.lockHandle(this.item, () => {
            this.$emit('lockHandle')
          })
        })
      } else if (command === 'edit') {
        this.ProjectPool.editHandle(this.item)
      } else if (command === 'delete') {
        this.$confirm('确定删除？', '提示').then(() => {
          this.ProjectPool.deleteHandle(this.item, () => {
            this.$emit('deleteHandle')
          })
        })
      } else if (command === 'transact') {
        this.ProjectPool.parkInHandle(this.item)
      }
    }
  }
}
</script>

<style lang="scss">
.investment-more-item-btn {
  line-height: 32px !important;
  width: 100px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
