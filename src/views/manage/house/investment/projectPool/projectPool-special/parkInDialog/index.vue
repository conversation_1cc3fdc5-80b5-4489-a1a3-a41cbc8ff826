<template>
  <div>
    <dialog-cmp
      title="请选择入园方式"
      :visible.sync="dialogVisible"
      width="666px"
      :haveOperation="false"
    >
      <div class="park-in-container">
        <div class="park-options">
          <div>
            <div class="park-options-title">标准入园</div>
            <div class="park-options-tips">
              需填写完整的入园信息，从入园项目库开始
            </div>
          </div>
          <el-button type="primary" @click="parkInTypeHandle(2)">
            <span class="p-r-8">确定</span>
            <svg-icon icon-class="swap-right" />
          </el-button>
        </div>
        <div class="park-options">
          <div>
            <div class="park-options-title">快速入园</div>
            <div class="park-options-tips">
              合法性信息检查后直接签订合同入园
            </div>
          </div>
          <el-button type="primary" @click="parkInTypeHandle(3)">
            <span class="p-r-8">确定</span>
            <svg-icon icon-class="swap-right" />
          </el-button>
        </div>
      </div>
    </dialog-cmp>
    <dialog-cmp
      :title="adminEntry === 2 ? '标准入园' : '快速入园'"
      :visible.sync="standardVisible"
      width="560px"
      :haveOperation="false"
    >
      <driven-form
        v-if="standardVisible"
        ref="driven-form"
        v-model="formModel"
        :formConfigure="formConfigure"
      />
      <el-checkbox v-if="sendMsgVisible" class="send-msg" v-model="sendMsg"
        >向该手机发送账号密码信息</el-checkbox
      >
      <div slot="footer">
        <el-button @click="standardVisible = false">返回</el-button>
        <el-button type="primary" @click="nextHandle">{{
          adminEntry === 2 ? '下一步' : '确定'
        }}</el-button>
      </div>
    </dialog-cmp>
    <dialog-cmp
      title="输入手机号"
      :visible.sync="phoneVisible"
      width="400px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="phoneVisible"
        ref="phone-driven-form"
        v-model="phoneFormModel"
        :formConfigure="phoneFormConfigure"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import DescriptorMixin from './descriptor'
import { meRecordCheckPhone, meRecordEnterPark } from '../api'

export default {
  name: 'ParkInDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      detailInfo: {},
      dialogVisible: false,
      adminEntry: 2, // 2标准入园；3快速入园
      standardVisible: false,
      formModel: {},
      sendMsg: false,
      phoneVisible: false,
      phoneFormModel: {},
      creditCodeTips: '',
      sendMsgVisible: true
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    standardVisible(val) {
      if (!val) {
        this.formModel = this.$options.data().formModel
        this.sendMsg = this.$options.data().sendMsg
        this.adminEntry = this.$options.data().adminEntry
        this.creditCodeTips = ''
      }
    },
    phoneVisible(val) {
      if (!val) {
        this.phoneFormModel = this.$options.data().phoneFormModel
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 入园
    meRecordEnterPark() {
      const params = {
        ...this.formModel,
        sendMsg: this.sendMsg,
        adminEntry: this.adminEntry,
        recordId: this.detailInfo.id
      }
      meRecordEnterPark(params).then(() => {
        if (this.adminEntry === 2) {
          this.$router.push({
            path: '/enterPark/settledCreate',
            query: {
              meProjectId: this.detailInfo.id,
              enterpriseName: this.detailInfo.enterpriseName
            }
          })
        } else {
          this.$toast.success('提交入园成功')
          this.$router.push({
            path: '/enterPark/simulateEnterPark',
            query: {
              type: 'fast'
            }
          })
        }
      })
    },
    nextHandle() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return
        this.meRecordEnterPark()
      })
    },
    parkInTypeHandle(type) {
      this.adminEntry = type
      this.phoneVisible = true
    },
    confirmDialog() {
      this.$refs['phone-driven-form'].validate(valid => {
        if (!valid) return false
        meRecordCheckPhone(this.phoneFormModel).then(res => {
          this.$set(this.formModel, 'mobile', res.mobile)
          this.$set(this.formModel, 'checkStatus', res.status)
          this.$set(
            this.formModel,
            'enterpriseName',
            this.detailInfo.enterpriseName
          )
          this.$set(this.formModel, 'creditCode', this.detailInfo.creditCode)
          this.formConfigure.descriptors.username.attrs.readonly = false
          if (res.status === 1) {
            this.formConfigure.descriptors.password.hidden = false
            this.formConfigure.descriptors.secondPassword.hidden = false
            this.sendMsgVisible = true
            this.standardVisible = true
            return
          }
          if (res.status === 3)
            return this.$toast.warning('该手机号已存在办理记录，请更换手机号')
          this.$set(this.formModel, 'username', res.username)
          this.formConfigure.descriptors.username.attrs.readonly = true
          if (res.status === 2) {
            this.$confirm('该手机号已经注册账号, 是否重置密码?', '提示', {
              confirmButtonText: '重置',
              cancelButtonText: '不重置',
              type: 'warning'
            })
              .then(() => {
                this.formConfigure.descriptors.password.hidden = false
                this.formConfigure.descriptors.secondPassword.hidden = false
                this.$set(this.formModel, 'resetPasswd', true)
                this.sendMsgVisible = true
                this.standardVisible = true
              })
              .catch(() => {
                this.formConfigure.descriptors.password.hidden = true
                this.formConfigure.descriptors.secondPassword.hidden = true
                this.$set(this.formModel, 'resetPasswd', false)
                this.sendMsgVisible = false
                this.standardVisible = true
              })
          }
        })
      })
    },
    initData(row) {
      this.detailInfo = row || {}
    }
  }
}
</script>

<style scoped lang="scss">
.park-in-container {
  display: flex;
  justify-content: space-between;
  .park-options {
    width: 300px;
    height: 185px;
    padding: 24px 16px;
    border: 1px solid;
    @include border_color(--border-color-base);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: inherit;
    border-radius: 6px;
    .park-options-title {
      font-size: 14px;
      font-weight: 350;
      line-height: 22px;
      @include font_color_mix(--color-black, #ffffff, 10%);
    }
    .park-options-tips {
      margin-top: 13px;
      font-size: 14px;
      font-weight: 350;
      @include font_color_mix(--color-black, #ffffff, 40%);
      line-height: 22px;
    }
  }
}
.send-msg {
  margin-left: 80px;
}
</style>
