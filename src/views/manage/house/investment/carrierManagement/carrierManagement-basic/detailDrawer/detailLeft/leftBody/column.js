export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '企业名称'
        },
        {
          prop: 'creditCode',
          label: '统一社会信用代码'
        },
        {
          prop: 'entTime',
          label: '入驻时间'
        },
        {
          prop: 'area',
          label: '入驻面积(m²)'
        },
        {
          prop: 'sourceStr',
          label: '企业来源'
        },
        {
          prop: 'creatorName',
          label: '添加人'
        },
        {
          prop: 'createTime',
          label: '添加时间'
        },
        {
          prop: 'operate',
          label: '操作',
          width: 90,
          render: (h, { row }) => {
            return (
              <div>
                <div v-show={this.tabModel === 1}>
                  <el-button
                    type={'text'}
                    class={'m-r-8'}
                    onClick={() => {
                      this.refundHandle(row)
                    }}
                  >
                    腾退
                  </el-button>
                  <el-dropdown trigger="click">
                    <span class="color-primary pointer">更多</span>
                    <el-dropdown-menu
                      slot="dropdown"
                      style="width:116px;text-align: center;"
                    >
                      <el-dropdown-item>
                        <div
                          class={'color-warning'}
                          onClick={() => {
                            this.editHandle(row)
                          }}
                        >
                          编辑
                        </div>
                      </el-dropdown-item>
                      <el-dropdown-item>
                        <div
                          class={'color-danger'}
                          onClick={() => {
                            this.delHandler(row)
                          }}
                        >
                          删除
                        </div>
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <div v-show={this.tabModel === 2}>
                  <el-button
                    type={'text'}
                    onClick={() => {
                      this.editHandle(row)
                    }}
                  >
                    编辑
                  </el-button>
                  <el-button
                    type={'text'}
                    class={'color-danger'}
                    onClick={() => {
                      this.delHandler(row)
                    }}
                  >
                    删除
                  </el-button>
                </div>
              </div>
            )
          }
        }
      ]
    }
  }
}
