import { getCheckCode } from '@/views/manage/house/investment/carrierManagement/carrierManagement-basic/api'

const SocialcreditVerify = /[1-9A-GY]{1}[1239]{1}[1-5]{1}[0-9]{5}[0-9A-Z]{10}/
const validateCreditCode = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入统一社会信用代码'))
  } else {
    if (SocialcreditVerify.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的统一社会信用代码'))
    }
  }
}

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          entName: {
            form: 'input',
            label: '企业名称',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          creditCode: {
            form: 'input',
            label: '统一社会信用代码',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入统一社会信用代码'
              },
              {
                validator: validateCreditCode
              },
              {
                validator: (rule, value, callback) => {
                  getCheckCode({
                    creditCode: value,
                    id: this.formModel.id
                  }).then(res => {
                    if (res) {
                      callback(
                        new Error('该社会信用代码已在项目库中存在，不可新增')
                      )
                    } else {
                      callback()
                    }
                  })
                }
              }
            ],
            attrs: {
              maxLength: 20
            }
          },
          entTime: {
            form: 'date',
            label: '入驻时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择入驻时间'
              }
            ]
          },
          area: {
            form: 'input',
            label: '入驻面积',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入驻面积'
              }
            ],
            attrs: {
              maxLength: 12
            },
            customRight: () => {
              return (
                <div class="font-size-14" style={'padding-top: 52px;'}>
                  m²
                </div>
              )
            }
          }
        }
      }
    }
  }
}
