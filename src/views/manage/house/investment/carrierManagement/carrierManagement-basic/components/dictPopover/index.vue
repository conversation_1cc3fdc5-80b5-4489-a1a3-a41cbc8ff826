<template>
  <div>
    <el-popover
      placement="top-start"
      :title="title"
      width="352"
      trigger="manual"
      v-model="visible"
      ref="popoverRef"
    >
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
      <div class="flex align-items-center justify-content-end">
        <el-button type="info" size="mini" @click="visible = false"
          >取消</el-button
        >
        <el-button type="primary" @click="confirmPopover" size="mini"
          >确定</el-button
        >
      </div>
      <el-button slot="reference" @click="addDict" type="primary"
        >新增</el-button
      >
    </el-popover>
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import { merchantConfigCreate } from '@/views/manage/house/investment/projectPool/projectPool-special/api'

export default {
  name: 'DictPopover',
  mixins: [descriptorMixins],
  props: {
    type: {
      type: String
    },
    title: {
      type: String,
      default: '新增项目进展'
    }
  },
  data() {
    return {
      visible: false,
      fromModel: {
        colorLevel: '#ed7b2f'
      }
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.fromModel = {
          colorLevel: '#ed7b2f'
        }
      }
    }
  },
  methods: {
    addDict() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
    },
    // 新增
    confirmPopover() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let data = {
            type: 5,
            ...this.fromModel
          }
          await merchantConfigCreate(data)
          this.$toast.success(`添加成功`)
          this.$emit('refreshDict', this.fromModel.name)
          this.visible = false
        }
      })
    }
  }
}
</script>
