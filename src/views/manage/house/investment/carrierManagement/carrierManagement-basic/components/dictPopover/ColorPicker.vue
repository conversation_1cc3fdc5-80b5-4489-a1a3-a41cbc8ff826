<template>
  <el-color-picker
    v-model="_value"
    :predefine="predefineColors"
    size="small"
    color-format="hex"
  />
</template>

<script>
export default {
  name: 'ColorPicker',
  props: {
    value: {
      required: true
    }
  },
  data() {
    return {
      predefineColors: [
        '#054CE8',
        '#2D6CF5',
        '#4F87FF',
        '#739EFC',
        '#C9353F',
        '#E34D59',
        '#F36D78',
        '#F78D94',
        '#078D5C',
        '#00A870',
        '#48C79C',
        '#85DBBE',
        '#D35A21',
        '#ED7B2F',
        '#F2995F',
        '#F7C797'
      ]
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped></style>
