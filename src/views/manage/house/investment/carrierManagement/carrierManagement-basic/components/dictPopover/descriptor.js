import ColorPicker from './ColorPicker'

export default {
  components: { ColorPicker },
  data() {
    return {
      formConfigure: {
        descriptors: {
          typeName: {
            form: 'input',
            label: '类型名称',
            attrs: {
              maxLength: 10
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入类型名称'
              }
            ]
          },
          colorLevel: {
            form: 'input',
            label: '类型颜色',
            attrs: {
              type: 'color'
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择类型颜色'
              }
            ],
            render: () => {
              return <color-picker v-model={this.fromModel.colorLevel} />
            }
          }
        }
      }
    }
  }
}
