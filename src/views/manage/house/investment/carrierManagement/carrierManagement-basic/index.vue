<template>
  <div class="min-h100 bg-white">
    <basic-card>
      <basic-tab
        ref="basicTab"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
      />
      <drive-table
        ref="drive-table"
        :api-fn="getCarrierPage"
        :columns="columns"
        :extral-querys="extralQuerys"
        :searchQuerysHook="searchQuerysHook"
      >
        <template v-slot:operate-right>
          <el-button
            type="primary"
            @click="openDrawer"
            v-permission="routeButtonsPermission.ADD"
            >{{ routeButtonsTitle.ADD }}</el-button
          >
        </template>
      </drive-table>
    </basic-card>
    <basic-drawer
      :title="title"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        v-if="drawerVisible"
        ref="driven-form"
        v-model="fromModel"
        label-position="top"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
    <!--载体详情-->
    <detail-drawer
      ref="detailDrawer"
      :visible.sync="visible"
      :detail-info="detailInfo"
    />
  </div>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixins from './descriptor'
import BasicTab from '@/components/BasicTab'
import DetailDrawer from './detailDrawer'
import {
  getCarrierCreate,
  getCarrierDelete,
  getCarrierInfo,
  getCarrierPage,
  getCarrierUpdate,
  getMerchantConfig
} from '@/views/manage/house/investment/carrierManagement/carrierManagement-basic/api'

export default {
  name: 'CarrierManagement',
  components: { DetailDrawer, BasicTab },
  mixins: [ColumnMixin, DescriptorMixins],
  data() {
    return {
      getCarrierPage,
      visible: false,
      drawerVisible: false,
      title: '新增载体',
      extralQuerys: {
        type: 1
      },
      fromModel: {
        type: 1
      },
      list: [
        {
          label: '拟建载体',
          value: 1
        },
        {
          label: '在建载体',
          value: 2
        },
        {
          label: '已建载体',
          value: 3
        }
      ],
      current: 1,
      detailInfo: {}
    }
  },
  provide() {
    return {
      Carrier: this
    }
  },
  computed: {
    columns() {
      return this.current === 3 ? this.tableColumn1 : this.tableColumn
    }
  },
  watch: {
    'fromModel.type': {
      handler(val) {
        if (val) {
          if (val === 3) {
            for (const key in this.formConfigure.descriptors) {
              if (this.formConfigure.descriptors[key].dynamicDisabled) {
                this.$set(this.formConfigure.descriptors[key], 'hidden', true)
              }
              if (this.formConfigure.descriptors[key].dynamicHidden) {
                this.$set(this.formConfigure.descriptors[key], 'hidden', false)
              }
            }
          } else {
            for (const key in this.formConfigure.descriptors) {
              if (this.formConfigure.descriptors[key].dynamicDisabled) {
                this.$set(this.formConfigure.descriptors[key], 'hidden', false)
              }
              if (this.formConfigure.descriptors[key].dynamicHidden) {
                this.$set(this.formConfigure.descriptors[key], 'hidden', true)
              }
            }
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    this.getMerchantConfig()
  },
  methods: {
    // 详情抽屉
    async openDetail(row) {
      const res = await getCarrierInfo(row.id)
      this.detailInfo = res
      this.visible = true
    },
    // tab切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.type = e
      this.$refs['drive-table'].refreshTable()
    },
    openDrawer() {
      this.title = '新增载体'
      this.formConfigure.descriptors.type.disabled = false
      this.fromModel = {
        type: this.current
      }
      this.drawerVisible = true
    },
    // 编辑回显
    async editHandle(row) {
      this.title = '编辑载体'
      const res = await getCarrierInfo(row.id)
      if (row.type === 1 || row.type === 2) {
        this.formConfigure.descriptors.type.disabled = false
        let {
          name,
          address,
          startingTime,
          startingTimeEnd,
          proposedOperatingTime,
          floorSpace,
          pjctProcess,
          introduction
        } = res
        this.fromModel = {
          name,
          address,
          startEndTime: [startingTime, startingTimeEnd],
          proposedOperatingTime,
          floorSpace,
          pjctProcess,
          introduction
        }
      } else {
        this.formConfigure.descriptors.type.disabled = true
        let {
          carrierName,
          carrierAddress,
          operationTime,
          operate,
          carrierFloorSpace,
          carrierIntroduction
        } = res
        this.fromModel = {
          carrierName,
          carrierAddress,
          operationTime,
          operate,
          carrierFloorSpace,
          carrierIntroduction
        }
      }
      this.$set(this.fromModel, 'type', res.type)
      this.$set(this.fromModel, 'id', res.id)
      this.$set(this.fromModel, 'attach', res.attach?.attachIds || [])
      this.drawerVisible = true
    },
    // 删除
    delHandle(row) {
      this.$confirm('确定删除该载体信息？').then(async () => {
        await getCarrierDelete(row.id)
        this.$toast.success('删除成功')
        this.$refs['drive-table'].refreshTable()
      })
    },
    // 表单提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let attach = this.fromModel?.attach?.map(item => {
            return item.id
          })
          let startingTime = this.fromModel.startEndTime[0]
          let startingTimeEnd = this.fromModel.startEndTime[1]
          let data = {
            ...this.fromModel,
            startingTime,
            startingTimeEnd,
            attach
          }
          if (this.fromModel.id) {
            await getCarrierUpdate(data)
            this.$toast.success('编辑成功')
          } else {
            await getCarrierCreate(data)
            this.$toast.success('创建成功')
          }
          this.$refs['drive-table'].refreshTable()
          this.drawerVisible = false
        }
      })
    },
    // 项目起始工期筛选
    searchQuerysHook(e) {
      const { startEndTime } = e
      if (startEndTime && startEndTime.length) {
        const [startingTime, startingTimeEnd] = e.startEndTime
        delete e.startEndTime
        return {
          ...e,
          startingTime,
          startingTimeEnd
        }
      } else {
        return e
      }
    },
    // 项目管理字典
    async getMerchantConfig() {
      const res = await getMerchantConfig({ type: 5 })
      this.formConfigure.descriptors.pjctProcess.options = res.map(item => {
        return { label: item.typeName, value: item.id }
      })
      this.tableColumn[5].search.options = res.map(item => {
        return { label: item.typeName, value: item.id }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
