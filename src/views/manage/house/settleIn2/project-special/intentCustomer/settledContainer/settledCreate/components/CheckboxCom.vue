<template>
  <el-checkbox-group v-model="_value" v-bind="$attrs" v-on="$listeners">
    <el-checkbox v-for="item in options" :key="item.key" :label="item.key">{{
      item.value
    }}</el-checkbox>
  </el-checkbox-group>
</template>

<script>
export default {
  name: 'CheckboxCom',
  props: {
    value: {
      required: true,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-checkbox-group) {
  margin-left: 27px;
  display: flex;
  flex-wrap: wrap;
}
::v-deep {
  .el-checkbox {
    margin-right: 40px;
  }
}
</style>
