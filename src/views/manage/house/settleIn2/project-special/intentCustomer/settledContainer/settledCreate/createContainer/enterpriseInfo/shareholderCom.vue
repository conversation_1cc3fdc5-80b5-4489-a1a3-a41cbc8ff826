<template>
  <el-form-item
    required
    class="item-form-container"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-button class="add-btn" type="primary" @click="addHandle">
      <i class="el-icon-plus"></i>
      <span>添加</span>
    </el-button>
    <el-table
      class="table-container"
      :data="tableData"
      stripe
      :row-style="{
        height: '50px',
        color: 'rgba(0,0,0,0.9)'
      }"
    >
      <el-table-column
        v-for="(item, index) in tableColumn"
        :key="index"
        :prop="item.prop"
        :label="item.label"
      >
        <template slot-scope="scope">
          <span :class="{ 'warning-item': item.warning }">{{
            scope.row[item.prop]
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="100px">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="editHandle(scope.row)"
            >编辑</el-link
          >
          <el-link
            class="danger-item"
            type="danger"
            :underline="false"
            @click="deleteHandle(scope.row)"
            >删除</el-link
          >
        </template>
      </el-table-column>
      <empty-container slot="empty" :width="120" :height="120" padding="33px" />
    </el-table>
    <el-dialog
      custom-class="custom-item-form"
      :title="dialogTitle"
      width="540px"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :append-to-body="false"
      :close-on-click-modal="false"
      :before-close="beforeCloseHandle"
    >
      <el-form
        ref="itemFormRef"
        :model="itemForm"
        label-position="right"
        label-width="80px"
        :rules="itemFormRules"
      >
        <el-form-item label="股东名称" prop="stockholder">
          <input-com
            v-model="itemForm.stockholder"
            label="股东名称"
            type="Input"
          ></input-com>
        </el-form-item>
        <el-form-item
          label="占股比例"
          prop="stockRatio"
          :rules="twoDecimalRules('占股比例', '', 'stockRatio')"
        >
          <input-com
            v-model="itemForm.stockRatio"
            label="占股比例"
            type="Input"
            sortText="%"
            inputType="number"
            :integer="true"
          ></input-com>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="beforeCloseHandle">取 消</el-button>
        <el-button type="primary" @click="validateHandle">确 定</el-button>
      </div>
    </el-dialog>
  </el-form-item>
</template>

<script>
import EmptyContainer from '../../../../components/EmptyContainer'
import InputCom from '../../components/InputCom'
import { generateID } from '../../../../utils/tools'
import RulesMixins from '../rules'
export default {
  name: 'shareholderCom',
  components: { InputCom, EmptyContainer },
  props: {
    value: {
      required: true
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [RulesMixins],
  data() {
    return {
      dialogTitle: '添加',
      itemForm: {
        stockholder: '',
        stockRatio: ''
      },
      itemFormRules: {
        stockholder: [
          {
            required: true,
            message: '请输入股东名称',
            target: ['change', 'blur']
          }
        ]
      },
      dialogVisible: false,
      tableColumn: [
        {
          label: '股东名称',
          prop: 'stockholder'
        },
        {
          label: '占股比例（%）',
          prop: 'stockRatio'
        }
      ],
      tableData: []
    }
  },
  inject: ['SettledCreate'],
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.SettledCreate.$refs.directory.validatorHandle(
          'shareholder',
          !!val.length
        )
        this.$emit('input', val)
      }
    }
  },
  methods: {
    // 数据初始化
    initData(formData) {
      this.tableData = formData.shareholder || []
    },
    // 已经存在股份
    existingShare() {
      let num = 0
      this.tableData.forEach(item => {
        if (this.itemForm.id !== item.id) {
          num += (item.stockRatio * 1000) / 1000
        }
      })
      return num
    },
    // 删除某一列
    deleteHandle(row) {
      this.$confirm('是否删除此股东信息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData.forEach((item, index) => {
          if (item.id === row.id) {
            this.tableData.splice(index, 1)
          }
        })
        this.$message({ type: 'success', message: '删除成功!' })
        this._value = this.tableData
      })
    },
    // 表单验证
    validateHandle() {
      this.$refs.itemFormRef.validate(isValid => {
        if (!isValid) return false
        const existingShare = this.existingShare()
        if ((this.itemForm.stockRatio * 1000) / 1000 + existingShare > 100) {
          return this.$message.warning('股份总份额超出100%')
        }
        if (this.itemForm.id) {
          this.tableData.forEach((item, index) => {
            if (item.id === this.itemForm.id) {
              this.tableData[index] = { ...this.itemForm }
            }
          })
          this.promptHandle('编辑')
        } else {
          this.tableData.push({
            id: generateID(),
            ...this.itemForm
          })
          this.promptHandle('新增')
        }
        this.beforeCloseHandle()
      })
    },
    // 提示
    promptHandle(tips) {
      this.$message({ type: 'success', message: `${tips}成功!` })
      this._value = this.tableData
    },
    // 编辑
    editHandle(row) {
      this.itemForm = { ...row }
      this.dialogTitle = '编辑'
      this.dialogVisible = true
    },
    // 添加弹窗
    addHandle() {
      this.dialogTitle = '添加'
      this.dialogVisible = true
    },
    // 弹窗关闭
    beforeCloseHandle() {
      this.itemForm = {}
      this.$refs.itemFormRef && this.$refs.itemFormRef.resetFields()
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped></style>
