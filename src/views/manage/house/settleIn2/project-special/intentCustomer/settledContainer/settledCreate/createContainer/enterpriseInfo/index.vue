<template>
  <div class="enterprise-container">
    <h1 label="公司/项目简介" class="info-title DirectoryLabel">
      公司/项目简介
    </h1>
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel m-r-8"
        id="existingEmployees"
        v-model="formData.existingEmployees"
        label="公司现有人数"
        prop="existingEmployees"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('公司现有人数', 'existingEmployees')"
        rulesProp="existingEmployees"
        ref="existingEmployees"
        :maxlength="5"
      ></form-item>
      <form-item
        class="flex-1 DirectoryLabel"
        id="doctorNum"
        v-model="formData.doctorNum"
        label="博士人数"
        prop="doctorNum"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('博士人数', 'doctorNum')"
        rulesProp="doctorNum"
        ref="doctorNum"
      ></form-item>
    </div>
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel m-r-8"
        id="masterNum"
        v-model="formData.masterNum"
        label="硕士人数"
        prop="masterNum"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('硕士人数', 'masterNum')"
        rulesProp="masterNum"
        ref="masterNum"
      ></form-item>
      <form-item
        class="flex-1 DirectoryLabel"
        id="partyMembersNum"
        v-model="formData.partyMembersNum"
        label="党员人数"
        prop="partyMembersNum"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('党员人数', 'partyMembersNum')"
        rulesProp="partyMembersNum"
        ref="partyMembersNum"
      ></form-item>
    </div>
<!--    <form-item-->
<!--      class="flex-1 DirectoryLabel"-->
<!--      id="seniorNum"-->
<!--      v-model="formData.seniorNum"-->
<!--      label="高级职称人数"-->
<!--      prop="seniorNum"-->
<!--      type="Input"-->
<!--      inputType="number"-->
<!--      :integer="true"-->
<!--      :rules="numberRules('高级职称人数', 'seniorNum')"-->
<!--      rulesProp="seniorNum"-->
<!--      ref="seniorNum"-->
<!--    ></form-item>-->
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel m-r-8"
        id="alumniType"
        v-model="formData.alumniType"
        label="中国科大校友企业"
        prop=""
        type="radio"
        :options="options"
        has-child="true"
        @change="servicesChange"
      ></form-item>
      <form-item
        v-if="formData.alumniType"
        class="flex-1 DirectoryLabel"
        id="alumni"
        v-model="formData.alumni"
        label="中国科大校友人数"
        prop="alumni"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('中国科大校友人数', 'alumni')"
        rulesProp="alumni"
        ref="alumni"
      ></form-item>
    </div>
    <div class="remarks">
      企业法人代表、董事长、股东或CEO、CTO、CFO等核心高管为中国科大校友，满足其中一项即可认定为中国科大校友企业
    </div>
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel"
        id="overseasType"
        v-model="formData.overseasType"
        label="留学人员企业"
        prop=""
        type="radio"
        :options="options"
        has-child="true"
        @change="personalChange"
      ></form-item>
      <form-item
        v-if="formData.overseasType"
        class="flex-1 DirectoryLabel"
        id="overseasNum"
        v-model="formData.overseasNum"
        label="留学归国（含股东）人数"
        prop="overseasNum"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('留学归国（含股东）人数', 'overseasNum')"
        rulesProp="overseasNum"
        ref="overseasNum"
      ></form-item>
    </div>
    <div class="remarks">
      企业法定代表人由留学人员担任，或留学人员自有资金（含技术入股）及海内外跟进的风险投资占企业总投资或总注册资本的25%以上，满足其中一项即可认定为留学人员企业
    </div>
    <form-item
      class="DirectoryLabel"
      id="industry"
      v-model="formData.industry"
      label="所属产业"
      prop="industry"
      type="Select"
      :rules="industryRules"
      rulesProp="industry"
      :options="industryOptions"
    ></form-item>
    <shareholder-com
      class="DirectoryLabel"
      id="shareholder"
      v-model="formData.shareholder"
      label="股东信息"
      prop="shareholder"
      :formData="formData"
      ref="shareholder"
    />
    <form-item
      class="DirectoryLabel max-width-100"
      v-model="formData.projectDescription"
      id="projectDescription"
      label="主营项目简介"
      prop="projectDescription"
      type="Input"
      inputType="textarea"
      maxlength="1000"
      :rows="10"
      show-word-limit
      :isTips="false"
    ></form-item>
    <intellectual-property
      class="DirectoryLabel"
      id="propertyRight"
      prop="propertyRight"
      v-model="formData.propertyRight"
      label="知识产权情况"
      :formData="formData"
      ref="propertyRight"
    />
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel m-r-8"
        id="patentAcceptance"
        v-model="formData.patentAcceptance"
        label="发明专利受理（项）"
        prop="patentAcceptance"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('发明专利受理', 'patentAcceptance')"
        rulesProp="patentAcceptance"
        ref="patentAcceptance"
      ></form-item>
      <form-item
        class="flex-1 DirectoryLabel"
        id="patentAuthorization"
        v-model="formData.patentAuthorization"
        label="发明专利授权（项）"
        prop="patentAuthorization"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('发明专利授权', 'patentAuthorization')"
        rulesProp="patentAuthorization"
        ref="patentAuthorization"
      ></form-item>
    </div>
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel m-r-8"
        id="newAcceptance"
        v-model="formData.newAcceptance"
        label="实用新型受理（项）"
        prop="newAcceptance"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('实用新型受理', 'newAcceptance')"
        rulesProp="newAcceptance"
        ref="newAcceptance"
      ></form-item>
      <form-item
        class="flex-1 DirectoryLabel"
        id="newAuthorization"
        v-model="formData.newAuthorization"
        label="实用新型授权（项）"
        prop="newAuthorization"
        type="Input"
        inputType="number"
        :integer="true"
        :rules="numberRules('实用新型授权', 'newAuthorization')"
        rulesProp="newAuthorization"
        ref="newAuthorization"
      ></form-item>
    </div>
    <form-item
      class="flex-1 DirectoryLabel"
      id="intellectual"
      v-model="formData.intellectual"
      label="有无企业相关专利"
      prop="intellectual"
      type="radio"
      :options="options"
      :is-tips="false"
      @change="intellectualChange"
    ></form-item>
    <form-item
      style="width: 50%"
      v-if="formData.intellectual"
      class="flex-1 DirectoryLabel"
      id="intellectualPropertyAttachIds"
      v-model="formData.intellectualPropertyAttachIds"
      label="企业相关专利附件"
      prop="intellectualPropertyAttachIds"
      type="upload"
      :options="options"
      :is-tips="true"
      tipsText="文件大小不能超出50M，最多上传三个文件"
      :mulity="true"
      :maxLength="3"
      :limit="3"
      :maxSize="50"
    ></form-item>
    <div class="remarks">具体证明文件另附</div>
    <div class="flex">
      <form-item
        class="flex-1 DirectoryLabel"
        id="highTechEnterprises"
        v-model="formData.highTechEnterprises"
        label="国家高新技术企业"
        prop="highTechEnterprises"
        type="radio"
        :options="options"
        :is-tips="false"
      ></form-item>
      <form-item
        v-if="formData.highTechEnterprises"
        class="flex-1 DirectoryLabel"
        id="qualifications"
        v-model="formData.qualifications"
        label="高新技术企业资质"
        prop="qualifications"
        type="radio"
        :options="qualificationsOptions"
        :is-tips="false"
      ></form-item>
      <form-item
        class="flex-1 DirectoryLabel"
        id="onScale"
        v-model="formData.onScale"
        label="规上企业"
        prop="onScale"
        type="radio"
        :options="options"
        :is-tips="false"
      ></form-item>
    </div>
    <business-status
      ref="businessStatus"
      class="DirectoryLabel"
      id="operationList"
      v-model="formData.operationList"
      label="经营情况(单位：万元)"
      prop=""
      :formData="formData"
      :rules="businessStatusRules"
    />
  </div>
</template>

<script>
import FormItem from '../../components/FormItemCom'
import BusinessStatus from './businessStatus'
import RulesMixins from '../rules'
import {
  getAllEnumList,
  // getAllEnumList,
  getIndustry
} from '../../../../api'
import ShareholderCom from './shareholderCom'
import IntellectualProperty from '@/views/manage/house/settleIn/project-special/intentCustomer/settledContainer/settledCreate/createContainer/enterpriseInfo/intellectualProperty'

export default {
  name: 'EnterpriseInfo',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      registeredMoneyTips: true,
      industryOptions: [],
      options: [
        { label: '是', value: true },
        { label: '否', value: false }
      ],
      qualificationsOptions: []
    }
  },
  inject: ['SettledCreate', 'CreateForm'],
  mixins: [RulesMixins],
  components: {
    IntellectualProperty,
    ShareholderCom,
    BusinessStatus,
    FormItem
  },
  watch: {
    'formData.highTechEnterprises'() {
      this.SettledCreate.$refs.directory.initData(false)
    },
    'formData.intellectualPropertyAttachIds'(val) {
      this.CreateForm.$refs.ruleForm &&
        this.CreateForm.$refs.ruleForm.validateField(
          'intellectualPropertyAttachIds'
        )
      this.SettledCreate.$refs.directory &&
        this.SettledCreate.$refs.directory.validatorHandle(
          'intellectualPropertyAttachIds',
          !!(val && val.length)
        )
    }
  },
  mounted() {
    this.getEnumList()
  },
  methods: {
    intellectualChange(e) {
      const dom = document.querySelector('#intellectual')
      dom.setAttribute('has-child', !!e)
      if (!e) {
        this.$set(
          this.SettledCreate.$refs.createContainer.formData,
          'intellectualPropertyAttachIds',
          []
        )
      }
      this.SettledCreate.$refs.directory.initData()
    },
    initData(formData) {
      this.$refs.propertyRight.initData(formData)
      this.$refs.businessStatus.initData(formData)
      this.$refs.shareholder.initData(formData)
    },
    // 获取枚举
    async getEnumList() {
      const industry = await getIndustry()
      this.industryOptions = industry.map(item => {
        return { value: item.name, key: item.type }
      })
      // 融资方式
      const qualifications = await getAllEnumList({ type: 'qualifications' })
      this.qualificationsOptions = qualifications.map(item => {
        return {
          label: item.value,
          value: item.key
        }
      })
    },
    // 法定代表人及联系方式
    legalPersonChange() {
      const isPass = this.formData.legalPerson
      this.SettledCreate.$refs.directory.validatorHandle(
        'legalPersonAndPhone',
        !!isPass
      )
    },
    // 留学人员企业
    personalChange(e) {
      const dom = document.querySelector('#overseasType')
      dom.setAttribute('has-child', !!e)
      if (!e) {
        this.$set(
          this.SettledCreate.$refs.createContainer.formData,
          'overseasNum',
          ''
        )
      }
      this.SettledCreate.$refs.directory.initData()
    },
    // 是否中国科大校友企业
    servicesChange(e) {
      const dom = document.querySelector('#alumniType')
      dom.setAttribute('has-child', !!e)
      if (!e) {
        this.$set(
          this.SettledCreate.$refs.createContainer.formData,
          'alumni',
          ''
        )
      }
      this.SettledCreate.$refs.directory.initData()
    },
    // 总经理或项目负责人及联系方式
    managerPhoneChange() {
      const isPass = this.formData.manager
      this.SettledCreate.$refs.directory.validatorHandle('manager', !!isPass)
    },
    // 常用联系人及联系方式
    contactsPhoneChange() {
      const isPass = this.formData.contacts
      this.SettledCreate.$refs.directory.validatorHandle('contacts', !!isPass)
    }
  }
}
</script>

<style scoped lang="scss">
.enterprise-container {
  margin-top: 40px;
}
.remarks {
  padding: 8px 16px;
  @include background_color_mix(--color-primary, #ffffff, 96%);
  border-radius: 3px;
  font-size: 14px;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.6);
  margin-bottom: 16px;
}
</style>
