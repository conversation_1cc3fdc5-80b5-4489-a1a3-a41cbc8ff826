<template>
  <div class="header-container">
    <div class="header-back" @click="$router.go(-1)">
      <i class="el-icon-arrow-left"></i>
      <span>返回</span>
    </div>
    <div class="line"></div>
    <div>{{ title }}</div>
  </div>
</template>

<script>
export default {
  name: 'SettledHeader',
  props: {
    title: {
      type: String,
      default: '您正在提交入园相关信息'
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  width: 100%;
  height: 60px;
  background-image: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.6),
    rgba(255, 255, 255, 0.4)
  );
  border-bottom: 1px solid #d5e2ff;
  box-shadow: 0 4px 10px rgba(5, 76, 232, 0.2);
  display: flex;
  align-items: center;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 22px;
  position: relative;
  z-index: 1;
  .header-back {
    margin-left: 16px;
    display: flex;
    align-items: center;
    cursor: pointer;
    span {
      padding-left: 4px;
    }
  }
  .line {
    width: 1px;
    height: 20px;
    background: rgba(5, 76, 232, 0.2);
    border-radius: 50px;
    margin: 0 16px;
  }
}
</style>
