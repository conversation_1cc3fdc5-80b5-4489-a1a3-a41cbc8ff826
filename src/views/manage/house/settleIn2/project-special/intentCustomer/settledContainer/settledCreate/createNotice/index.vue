<template>
  <div class="notice-container">
    <div class="notice-wrapper" v-if="show" @click.stop>
      <div class="notice-title">入园须知</div>
      <div v-if="content" class="notice-content" v-html="content"></div>
      <div v-else class="notice-content">暂无须知</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CreateNotice',
  data() {
    return {
      show: false,
      content: ''
    }
  },
  mounted() {
    document.addEventListener('click', e => {
      this.clickListener(e)
    })
  },
  beforeDestroy() {
    document.removeEventListener('click', e => {
      this.clickListener(e)
    })
  },
  methods: {
    initData(data = {}) {
      this.content = data.enterInfo
      this.show = !this.show
    },
    async clickListener(e) {
      await this.$nextTick()
      const dom = document.querySelector('.park-notice-text')
      const noticeText = dom && dom.contains(e.target)
      if (noticeText) return false
      if (this.show) this.show = false
    }
  }
}
</script>

<style scoped lang="scss">
.notice-container {
  //width: 320px;
  flex-shrink: 0;
  .notice-wrapper {
    width: 100%;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 6px;
    border: 1px solid #e9f0ff;
    flex-shrink: 0;
    padding: 24px;
    .notice-title {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
    }
    .notice-content {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.4);
      line-height: 22px;
      margin-top: 16px;
    }
  }
}
::v-deep {
  .notice-content {
    * {
      width: 100% !important;
    }
  }
}
</style>
