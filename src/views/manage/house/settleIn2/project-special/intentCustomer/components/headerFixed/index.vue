<template>
  <div class="header-container pos-relative">
    <div class="flex align-items-center">
      <div class="header-back" @click="$router.go(-1)">
        <i class="el-icon-arrow-left"></i>
        <span>返回</span>
      </div>
      <div class="line"></div>
      <div>{{ title }}</div>
      <div class="flex align-items-center">
        <div
          v-for="item in list"
          :key="item.value"
          class="tab-main pointer"
          :class="current === item.value ? 'active' : ''"
          @click="tabsChange(item.value)"
        >
          {{ item.label }}
          <div
            class="line-tab"
            :class="current === item.value ? 'active' : ''"
          ></div>
        </div>
      </div>
    </div>
    <div class="flex align-items-center">
      <div class="m-l-8" v-if="$route.query.meProjectId">
        <el-link type="primary" @click="toProjectDetail"
          >查看项目招商过程</el-link
        >
      </div>
    </div>
    <div class="tab-line-info"></div>
  </div>
</template>

<script>
export default {
  name: 'HeaderFixed',
  props: {
    title: {
      type: String,
      default: '您正在提交入园相关信息'
    },
    list: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  methods: {
    tabsChange(e) {
      this.$emit('tabsChange', e)
    },
    toProjectDetail() {
      this.$emit('toProjectDetail')
    }
  }
}
</script>

<style scoped lang="scss">
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 20px;
  font-weight: bold;
  .header-back {
    display: flex;
    align-items: center;
    cursor: pointer;
    span {
      padding-left: 4px;
    }
  }
  .line {
    margin: 0 8px;
  }
}

.tab-main {
  position: relative;
  font-size: 14px;
  margin-left: 24px;
  color: rgba(0, 0, 0, 0.6);
  .line-tab {
    position: absolute;
    left: 50%;
    bottom: -8px;
    transform: translateX(-50%);
    height: 2px;
    width: 40px;
    line-height: 40px;
    &.active {
      @include background_color(--color-warning);
    }
  }
  &.active {
    @include font_color(--color-warning);
  }
}

.tab-line-info {
  position: absolute;
  left: 0;
  top: 28px;
  height: 1px;
  width: 100%;
  background-color: #e7e7e7;
}
</style>
