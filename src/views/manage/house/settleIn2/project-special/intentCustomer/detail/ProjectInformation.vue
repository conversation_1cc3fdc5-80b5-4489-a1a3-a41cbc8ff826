<template>
  <div v-if="detailInfo && Object.keys(detailInfo).length > 0">
    <div v-if="detailInfo.whetherSubmit">
      <div class="title">企业信息</div>
      <table
        style="width: 100%; margin-bottom: 24px"
        border="1"
        Cellspacing="0"
        Cellpadding="0"
      >
        <tr>
          <th>企业名称</th>
          <td :colspan="3">{{ detailInfo.enterpriseName || '--' }}</td>
        </tr>
        <tr>
          <th>统一社会信用代码</th>
          <td :colspan="3">{{ detailInfo.creditCode || '--' }}</td>
        </tr>
        <tr>
          <th>注册资本（万元）</th>
          <td>{{ detailInfo.registeredMoney || '0' }}</td>
          <th>注册地址</th>
          <td>{{ detailInfo.registeredAddress || '--' }}</td>
        </tr>
        <tr>
          <th>注册情况</th>
          <td>{{ registrationStatus(detailInfo.registrationStatus) }}</td>
          <th>入驻类型</th>
          <td>{{ detailInfo.occupationTypeStr || '--' }}</td>
        </tr>
        <tr>
          <th>法定代表人</th>
          <td>{{ detailInfo.legalPerson || '--' }}</td>
          <th>联系电话和E-mail</th>
          <td>
            {{ detailInfo.legalPersonPhone }}
            {{
              detailInfo.legalPersonPhone && detailInfo.legalPersonMailbox
                ? '/'
                : ''
            }}
            {{ detailInfo.legalPersonMailbox }}
          </td>
        </tr>
        <tr>
          <th>总经理或项目负责人</th>
          <td>{{ detailInfo.manager || '--' }}</td>
          <th>联系电话和E-mail</th>
          <td>
            {{ detailInfo.managerPhone }}
            {{
              detailInfo.managerPhone && detailInfo.managerMailbox ? '/' : ''
            }}
            {{ detailInfo.managerMailbox }}
          </td>
        </tr>
        <tr>
          <th>常用联系人</th>
          <td>{{ detailInfo.contacts || '暂无数据' }}</td>
          <th>联系电话和E-mail</th>
          <td>
            {{ detailInfo.contactsPhone }}
            {{
              detailInfo.contactsPhone && detailInfo.contactsMailbox ? '/' : ''
            }}
            {{ detailInfo.contactsMailbox }}
          </td>
        </tr>
      </table>
      <div class="title">公司入驻意向</div>
      <table
        style="width: 100%; margin-bottom: 24px"
        border="1"
        Cellspacing="0"
        Cellpadding="0"
      >
        <tr>
          <th>意向园区</th>
          <td>{{ detailInfo.parkName || '暂无数据' }}</td>
          <th>需求面积（m²）</th>
          <td>{{ detailInfo.expectArea || '0' }}</td>
        </tr>
        <tr>
          <th>申请租房用途</th>
          <td>{{ detailInfo.useTypeStr || '暂无数据' }}</td>
          <th>目前办公面积（m²）</th>
          <td>{{ detailInfo.currentOfficeArea || '0' }}</td>
        </tr>
        <tr>
          <th>期望入驻时间</th>
          <td>{{ detailInfo.expectedTime || '暂无数据' }}</td>
          <th>是否有环境影响</th>
          <td>{{ detailInfo.environmentalStr || '暂无数据' }}</td>
        </tr>
        <tr>
          <th>用电负荷（W/m²）</th>
          <td>{{ detailInfo.powerLoad || '0' }}</td>
          <th>楼板承重需要（kg/m²）</th>
          <td>{{ detailInfo.floorBearing || '0' }}</td>
        </tr>
        <tr>
          <th>是否愿意承担增容费用</th>
          <td :colspan="3">
            {{ detailInfo.increaseCapacity ? '是' : '否' }}
          </td>
        </tr>
        <tr>
          <th>是否有需要的企业服务</th>
          <td :colspan="3">
            {{ detailInfo.projectServicesStr | noData }}
          </td>
        </tr>
        <tr>
          <th>是否有融资需求</th>
          <td :colspan="3">
            {{ detailInfo.financingMethodStr | noData }}
          </td>
        </tr>
        <tr>
          <th>备注内容</th>
          <td :colspan="3">{{ detailInfo.other | noData }}</td>
        </tr>
      </table>
      <div class="title">公司项目简介</div>
      <table
        style="width: 100%; margin-bottom: 24px"
        border="1"
        Cellspacing="0"
        Cellpadding="0"
      >
        <tr>
          <th>公司现有人数</th>
          <td>{{ detailInfo.existingEmployees || '0' }}</td>
          <th>博士人数</th>
          <td>{{ detailInfo.doctorNum || '0' }}</td>
        </tr>
        <tr>
          <th>硕士人数</th>
          <td>{{ detailInfo.masterNum || '0' }}</td>
          <th>党员人数</th>
          <td>{{ detailInfo.partyMembersNum || '0' }}</td>
        </tr>
        <tr>
          <th>中国科大校友企业</th>
          <td>{{ detailInfo.alumniType ? '是' : '否' }}</td>
          <th>中国科大校友人数</th>
          <td>{{ detailInfo.alumni || '0' }}</td>
        </tr>
        <tr>
          <th>留学人员企业</th>
          <td>{{ detailInfo.overseasType ? '是' : '否' }}</td>
          <th>留学归国人员（含股东）</th>
          <td>{{ detailInfo.overseasNum || '0' }}</td>
        </tr>
        <tr>
<!--          <th>高级职称人数</th>-->
<!--          <td>{{ detailInfo.seniorNum || '0' }}</td>-->
          <th>国家高新技术企业</th>
          <td :colspan="3">{{ detailInfo.highTechEnterprises ? '是' : '否' }}</td>
        </tr>
        <tr>
          <th>高新技术企业资质</th>
          <td>{{ detailInfo.qualificationsStr | noData }}</td>
          <th>规上企业</th>
          <td>{{ detailInfo.onScale ? '是' : '否' }}</td>
        </tr>
        <tr>
          <th>发明专利受理（项）</th>
          <td>{{ detailInfo.patentAcceptance || '0' }}</td>
          <th>发明专利授权（项）</th>
          <td>{{ detailInfo.patentAuthorization || '0' }}</td>
        </tr>
        <tr>
          <th>实用新型受理（项）</th>
          <td>{{ detailInfo.newAcceptance || '0' }}</td>
          <th>实用新型授权（项）</th>
          <td>{{ detailInfo.newAuthorization || '0' }}</td>
        </tr>
        <tr>
          <th>有无企业相关专利</th>
          <td>{{ detailInfo.intellectual ? '是' : '否' }}</td>
          <th>企业相关专利附件</th>
          <td>
            <files-list :files="intellectualFileList" :onlyForView="true" />
          </td>
        </tr>
        <tr>
          <th>所属产业</th>
          <td :colspan="3">{{ detailInfo.industryStr | noData }}</td>
        </tr>
        <tr>
          <th>主营项目简介</th>
          <td :colspan="3">
            {{ detailInfo.projectDescription | noData }}
          </td>
        </tr>
      </table>
      <div class="title">股东情况</div>
      <drive-table
        ref="drive-table"
        class="m-b-24"
        :table-data="detailInfo.shareholder"
        :columns="tableColumnShareholder"
      />
      <div class="title">知识产权情况</div>
      <drive-table
        ref="drive-table"
        class="m-b-24"
        :table-data="detailInfo.propertyRight"
        :columns="tableColumn"
      />
      <div class="title">经营情况（单位：万元）</div>
      <drive-table
        ref="drive-table"
        class="m-b-24"
        :table-data="detailInfo.operationList"
        :columns="tableColumnOperate"
      />
      <div class="title">相关材料</div>
      <div class="basic-info">
        <div class="table-item">
          <div class="table-label span-12">相关材料</div>
          <div class="table-label span-12">已上传文件</div>
        </div>
        <div class="table-item">
          <div class="table-content span-12 DirectoryLabel">
            《中安创谷科技园意向入驻企业申请表》
          </div>
          <div class="table-content span-12">
            <el-link
              v-for="(item, index) in detailInfo.applicationAttachIds
                .settleProject"
              :key="index"
              type="primary"
              :underline="false"
              @click="newOpenHandle(item.path)"
              >{{ item.name }}</el-link
            >
          </div>
        </div>
        <div class="table-item">
          <div class="table-content span-12 DirectoryLabel">公司简介</div>
          <div class="table-content span-12">
            <el-link
              v-for="(item, index) in detailInfo.identificationAttachIds
                .settleProject"
              :key="index"
              type="primary"
              :underline="false"
              @click="newOpenHandle(item.path)"
              >{{ item.name }}</el-link
            >
          </div>
        </div>
        <div class="table-item">
          <div class="table-content span-12 DirectoryLabel">
            公司营业执照副本复印件
          </div>
          <div class="table-content span-12">
            <el-link
              v-for="(item, index) in detailInfo.businessLicenseAttachIds
                .settleProject"
              :key="index"
              type="primary"
              :underline="false"
              @click="newOpenHandle(item.path)"
              >{{ item.name }}</el-link
            >
          </div>
        </div>
        <div class="table-item">
          <div class="table-content span-12 DirectoryLabel">
            国家高企证书电子版
          </div>
          <div class="table-content span-12">
            <el-link
              v-for="(item, index) in detailInfo.paymentCertificateAttachIds
                ?.settleProject"
              :key="index"
              type="primary"
              :underline="false"
              @click="newOpenHandle(item.path)"
              >{{ item.name }}</el-link
            >
          </div>
        </div>
        <div class="table-item">
          <div class="table-content span-12 DirectoryLabel">其他佐证材料</div>
          <div class="table-content span-12">
            <el-link
              v-for="(item, index) in detailInfo.otherCertificatesAttachIds
                ?.settleProject"
              :key="index"
              type="primary"
              :underline="false"
              @click="newOpenHandle(item.path)"
              >{{ item.name }}</el-link
            >
          </div>
        </div>
      </div>
    </div>
    <div class="project-tab-left" v-else>
      <div class="flex justify-content-center m-b-24">
        <img
          style="width: 120px; height: 120px"
          src="../projectCreate/image/bg-empty.png"
          alt=""
        />
      </div>
      <div class="font-size-14 line-height-22" style="text-align: center">
        暂未获取到项目信息，请及时关注和提醒企业填写
      </div>
      <div class="m-t-16 flex justify-content-center">
        <el-input
          style="width: 496px"
          placeholder="请输入内容"
          v-model="webLink"
          readonly
        >
          <template slot="append">
            <div
              class="color-black pointer"
              @click="copyHandle(webLink, $event)"
            >
              复制链接
            </div>
          </template>
        </el-input>
        <div class="m-l-8">
          <el-button
            type="primary"
            @click="sendHandle"
            :disabled="!detailInfo.sendSms"
          >
            短信提醒
            <el-tooltip
              effect="dark"
              content="此项目链接还没有企业登录填写"
              placement="top"
            >
              <i
                v-if="!detailInfo.sendSms"
                class="el-icon-question el-icon--right"
              ></i>
            </el-tooltip>
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import columnMixins from './column'
import handleClipboard from '@/utils/clipboard'
import { getNoticeEnterprise } from '@/views/manage/house/settleIn/project-special/intentCustomer/api'
import { mapGetters } from 'vuex'
import FilesList from '@/components/Uploader/files'

export default {
  name: 'ProjectInformation',
  components: { FilesList },
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [columnMixins],
  data() {
    return {
      disabled: false,
      webLink: '',
      formData: {
        projectAttachIds: [],
        businessLicenseAttachIds: [],
        identificationAttachIds: [],
        paymentCertificateAttachIds: [],
        applicationAttachIds: [],
        intellectualPropertyAttachIds: [],
        otherCertificatesAttachIds: []
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    intellectualFileList() {
      const attach = this.detailInfo.intellectualPropertyAttachIds || {}
      return attach.settleProject || []
    }
  },
  mounted() {
    this.getDomainAndPort()
  },
  methods: {
    // 发送短信
    async sendHandle() {
      let id = this.$route.query.id
      await getNoticeEnterprise(id)
      this.$toast.success('短信已发送')
    },
    copyHandle(item, e) {
      handleClipboard(item, e)
    },
    getDomainAndPort() {
      let id = this.$route.query.id
      let pathStr = `${this.userInfo.website}/screen/settledCreate?id=${id}&isBind=true`
      this.webLink = pathStr
    },
    registrationStatus(val) {
      switch (val) {
        case 1:
          return '已入区'
        case 2:
          return '变更注册地址'
        case 3:
          return '注册新公司'
        default:
          return '暂无数据'
      }
    },
    newOpenHandle(url) {
      window.open(url)
    }
  }
}
</script>

<style lang="scss" scoped>
.project-tab-left {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  .text-info {
    margin-top: 8px;
    font-size: 14px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.6);
    text-align: center;
  }
}
.span-12 {
  width: 50%;
}
.basic-info {
  border-radius: 3px;
  border: 1px solid #e7e7e7;
  overflow: hidden;
  .table-item {
    border-bottom: 1px solid #e7e7e7;
    flex-shrink: 1;
    &:last-child {
      border-bottom: none;
    }
    .table-label,
    .table-content {
      border-right: 1px solid #e7e7e7;
      &:last-child {
        border-right: none;
      }
    }
  }
}
.table-item {
  display: flex;
  //align-items: center;
  flex-shrink: 1;
}
.table-label {
  @include background_color_mix(--color-primary, #ffffff, 96%);
  padding: 0 24px;
  text-align: left;
  min-height: 50px;
  font-size: 14px;
  font-weight: 350;
  color: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  flex-shrink: 1;
}
.table-content {
  padding: 0 24px;
  font-size: 14px;
  font-weight: 350;
  color: rgba(0, 0, 0, 0.9);
  background: #fff;
  display: flex;
  align-items: center;
  word-break: break-all;
  min-height: 50px;
  flex-shrink: 1;
}
.title {
  font-weight: 500;
  font-size: 18px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 28px;
  margin-bottom: 16px;
}
table {
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  font-size: 14px;
}

th {
  width: 220px;
  @include background_color_mix(--color-primary, #ffffff, 96%);
  padding: 10px;
  font-weight: 400;
  text-align: left;
}

td {
  width: 480px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  word-break: break-all;
  padding: 10px 10px 11px 10px;
  line-height: 1.6em;
  word-wrap: break-word;
}

:deep(.el-link) {
  width: 100%;
}
:deep(.el-link--inner) {
  width: 100%;
  display: flex;
  align-items: center;
  word-break: break-all;
}
</style>
