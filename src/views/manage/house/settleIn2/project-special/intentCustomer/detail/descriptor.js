export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          result: {
            form: 'radio',
            label: '评估结果',
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择评估结果'
              }
            ],
            options: [
              { label: '通过', value: true },
              { label: '拒绝', value: false }
            ],
            events: {
              change: e => {
                if (!e) this.fromModel.content = ''
              }
            }
          },
          content: {
            form: 'input',
            label: '评估备注',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入评估备注'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 50,
              showWordLimit: true
            }
          }
        }
      }
    }
  }
}
