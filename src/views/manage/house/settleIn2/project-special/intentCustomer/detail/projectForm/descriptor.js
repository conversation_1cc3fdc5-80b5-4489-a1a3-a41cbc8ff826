export default {
  data() {
    return {
      formConfigureAssess: {
        descriptors: {
          result: {
            form: 'radio',
            label: '是否提交该企业的入园信息到审批流程中',
            rule: {
              required: true,
              message: '请选择审批结果',
              type: 'boolean'
            },
            options: [
              {
                label: '拒绝',
                value: false
              },
              {
                label: '提交审核',
                value: true
              }
            ]
          }
          // content: {
          //   form: 'input',
          //   rule: [
          //     {
          //       type: 'string',
          //       message: '请输入内容'
          //     }
          //   ],
          //   attrs: {
          //     type: 'textarea',
          //     maxlength: 200,
          //     rows: 4,
          //     showWordLimit: true
          //   }
          // }
        }
      },
      formConfigure: {
        // labelWidth: '95px',
        descriptors: {
          type: {
            form: 'radio',
            label: '请选择通知类型',
            rule: {
              // required: true,
              message: '请选择通知类型',
              type: 'number'
            },
            options: [
              {
                label: '路演通知',
                value: 2
              },
              {
                label: '看房通知',
                value: 3
              },
              {
                label: '其他通知',
                value: 4
              }
            ],
            events: {
              change: this.changeResults
            }
          },
          noticeTime: {
            form: 'date',
            label: '路演时间',
            hidden: false,
            rule: {
              required: true,
              message: '请选择时间',
              type: 'string'
            },
            props: {
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm:ss',
              'value-format': 'yyyy-MM-dd HH:mm:ss'
            }
          },
          noticeAddress: {
            form: 'input',
            label: '路演地点',
            hidden: false,
            rule: {
              required: true,
              message: '请输入地点',
              type: 'string'
            },
            attrs: {
              maxLength: 100
            }
          },
          noticeAttachIds: {
            form: 'component',
            label: '附件',
            rule: {
              message: '请上传附件',
              type: 'array'
            },
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'roadshow'
              },
              showDelete: this.disabled,
              uploaderText: '上传文件',
              mulity: true,
              maxLength: 5,
              limit: 5
            },
            customRight: () => {
              return (
                <div style="position: absolute;padding-top: 42px; margin-left: calc(-100% + 110px);color: rgba(0,0,0,0.4);font-size: 12px;">
                  支持批量上传，上传格式为pdf，doc，docx，xls，xlsx，ppt，pptx，image，txt文件
                </div>
              )
            }
          },
          content: {
            form: 'input',
            label: '路演内容',
            rule: [
              {
                type: 'string',
                message: '请输入内容'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          }
        }
      },
      formConfigureRecord: {
        descriptors: {
          content: {
            form: 'input',
            label: '路演评审记录',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入内容'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          },
          score: {
            form: 'input',
            label: '请输入分数',
            rule: {
              message: '请输入分数',
              type: 'number'
            },
            directives: [
              {
                name: 'decimal',
                value: '2'
              }
            ]
          },
          noticeAddress: {
            form: 'input',
            label: '带看房源',
            hidden: true,
            rule: {
              message: '请输入房源地址',
              type: 'string'
            },
            attrs: {
              maxLength: 200
            }
          },
          noticeRecordAttachIds: {
            form: 'component',
            label: '附件',
            rule: {
              type: 'array'
            },
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'roadshow'
              },
              uploaderText: '上传文件',
              mulity: true,
              maxLength: 5,
              limit: 5
            },
            customRight: () => {
              return (
                <div style="position: absolute;padding-top: 42px; margin-left: calc(-100% + 110px);color: rgba(0,0,0,0.4);font-size: 12px;">
                  支持批量上传，上传格式为pdf，doc，docx，xls，xlsx，ppt，pptx，image，txt文件
                </div>
              )
            }
          }
        }
      }
    }
  }
}
