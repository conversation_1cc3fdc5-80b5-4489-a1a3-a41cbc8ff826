export default {
  data() {
    return {
      tableColumnRoom: [
        {
          prop: 'building',
          label: '楼栋'
        },
        {
          prop: 'room',
          label: '房号'
        },
        {
          prop: 'area',
          label: '房源面积(m²)'
        }
      ],
      tableColumnShareholder: [
        {
          label: '股东名称',
          prop: 'stockholder'
        },
        {
          label: '占股比例（%）',
          prop: 'stockRatio'
        }
      ],
      tableColumn: [
        {
          label: '发明',
          prop: 'invention'
        },
        {
          label: '实用新型',
          prop: 'utilityModel'
        },
        {
          label: '外观',
          prop: 'exterior'
        },
        {
          label: '计算机著作权',
          prop: 'computerWorks'
        },
        {
          label: '集成电路设计',
          prop: 'integratedCircuit'
        },
        {
          label: '商标',
          prop: 'trademark'
        }
      ],
      tableColumnOperate: [
        {
          label: '年度',
          prop: 'yearStr'
        },
        {
          label: '产值（万元）',
          prop: 'outputValue',
          warning: true
        },
        {
          label: '营业收入（万元）',
          prop: 'businessIncome',
          warning: true
        },
        {
          label: '上缴税收（万元）',
          prop: 'taxPaid',
          warning: true
        },
        {
          label: '净利润（万元）',
          prop: 'netProfit',
          warning: true
        }
      ]
    }
  }
}
