import TimeCycle from './TimeCycle'

export default {
  components: { TimeCycle },
  data() {
    return {
      formConfigure: {
        descriptors: {
          entName: {
            form: 'input',
            label: '企业名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            disabled: true,
            attrs: {
              maxLength: 32
            },
            events: {
              change: this.checkCreditCode
            }
          },
          creditCode: {
            form: 'input',
            label: '统一社会信用代码',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入统一社会信用代码'
              }
            ],
            disabled: true,
            attrs: {
              maxLength: 50
            },
            customRight: () => {
              const className = this.check.isAccord
                ? 'color-primary m-l-4'
                : 'm-l-4'
              return (
                <div class={'flex align-items-center'}>
                  {this.check.isAccord ? (
                    <svg-icon
                      class="font-size-14 color-primary"
                      icon-class="check-rectangle"
                    />
                  ) : (
                    <span class={'credit-code-block'}></span>
                  )}
                  <span class={className}>信息一致</span>
                </div>
              )
            },
            events: {
              change: this.checkCreditCode
            }
          },
          businessLicenseAttachIds: {
            form: 'component',
            label: '营业执照副本',
            hidden: false,
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传营业执照副本'
              }
            ],
            componentName: 'uploader',
            disabled: true,
            props: {
              uploadData: {
                type: 'settleProject'
              },
              showDelete: true,
              onlyForView: false,
              mulity: true,
              maxSize: 10,
              maxLength: 3,
              limit: 3
            }
          }
        }
      },
      deliveryFormConfigure: {
        descriptors: {
          timeCycle: {
            form: 'component',
            label: '通知时间',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择通知时间'
              }
            ],
            render: () => {
              return (
                <div>
                  <time-cycle
                    ref="timeCycle"
                    v-model={this.deliveryInfo.fromModel.timeCycle}
                  />
                </div>
              )
            }
          },
          mobile: {
            form: 'input',
            label: '手机号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入手机号'
              },
              {
                pattern: /^1[3-9]\d{9}$/,
                message: '手机号格式不正确',
                trigger: 'blur'
              }
            ]
          }
        }
      },
      roomConfirmFormConfigure: {
        descriptors: {
          confirmDate: {
            form: 'date',
            label: '交房时间',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择交房时间'
              }
            ]
          },
          attachIds: {
            form: 'component',
            label: '交房确认单',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传交房确认单'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'intentCustomer'
              },
              maxSize: 20,
              axLength: 1,
              limit: 1
            }
          }
        }
      },
      billRefundFormConfigure: {
        descriptors: {
          confirmDate: {
            form: 'date',
            label: '退款时间',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择退款时间'
              }
            ]
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'intentCustomer'
              },
              maxSize: 20,
              axLength: 1,
              limit: 1
            }
          }
        }
      }
    }
  }
}
