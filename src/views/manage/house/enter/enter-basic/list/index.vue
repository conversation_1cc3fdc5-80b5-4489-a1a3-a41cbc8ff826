<template>
  <div>
    <basic-card>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getApplyPage"
        :search-querys-hook="searchQueryHook"
      >
      </drive-table>
    </basic-card>
  </div>
</template>

<script>
import ColumnMixins from './column'
import { getApplyPage, getPark } from './api'

export default {
  name: 'EnterParkList',
  mixins: [ColumnMixins],
  data() {
    return {
      getApplyPage
    }
  },
  created() {
    this.getPark()
  },
  methods: {
    // 重置搜索参数
    searchQueryHook(e) {
      const [expectAreaStart = '', expectAreaEnd = ''] = e.expectArea || []
      const [beginApplyDate = '', endApplyDate = ''] = e.applyDate || []
      delete e.expectArea
      delete e.applyDate
      return {
        ...e,
        expectAreaStart,
        expectAreaEnd,
        beginApplyDate,
        endApplyDate
      }
    },

    // 获取所有园区
    getPark() {
      getPark().then(res => {
        const parkList = res.map(item => {
          return { label: item.park, value: item.id }
        })
        this.tableColumn[1].search.options = parkList
      })
    },
    // 去房源详情
    skipGardenDetail(row) {
      const { id, orderId } = row
      this.$router.push({
        path: '/attractInvestment/enterParkDetail',
        query: {
          id,
          orderId
        }
      })
    }
  }
}
</script>
