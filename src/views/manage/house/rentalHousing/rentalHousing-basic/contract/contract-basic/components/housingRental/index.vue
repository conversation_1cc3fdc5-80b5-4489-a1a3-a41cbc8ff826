<template>
  <div>
    <div class="m-t-35 m-b-25">房源租金</div>
    <drive-table
      max-height="380px"
      ref="drive-table"
      :columns="tableColumn"
      border
      :span-method="objectSpanMethod"
      :table-data="pricesData"
    />

    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <div v-if="visible">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          label-position="top"
          :formConfigure="formConfigure"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
export default {
  name: 'HouseRental',
  props: {
    pricesData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '修正',
      formConfigure: {
        descriptors: {
          amount: {
            form: 'input',
            label: '执行单价',
            disabled: true,
            rule: [
              {
                type: 'number'
              }
            ]
          },
          fixAmount: {
            form: 'input',
            label: '计划单价',
            rule: [
              {
                type: 'string',
                required: true,
                message: '请输入计划单价',
                validator: (rule, value, callback) => {
                  if (!value) return callback(new Error(`请输入计划单价`))
                  callback()
                }
              },
              {
                type: 'string',
                validator: 'validateDecimal'
              }
            ],
            customTips: () => {
              return <div></div>
            }
          },
          reason: {
            form: 'input',
            label: '修正说明',
            rule: [
              {
                message: '请输入',
                type: 'string'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          }
        }
      },
      fromModel: {},
      visible: false,
      tableColumn: [
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '费用类型',
          prop: 'feeName'
        },
        {
          label: '时间',
          prop: 'time'
        },
        {
          label: '租金',
          prop: 'fixAmount',
          render: (h, { row }) => {
            return (
              <div>{row.fixAmount ? `${row.fixAmount} ${row.unit}` : '-'}</div>
            )
          }
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                {this.$route.query.type ? (
                  <span style={'color:#bdbdbd;cursor:no-drop'}>修正</span>
                ) : (
                  <el-button
                    type="text"
                    size="mini"
                    onClick={() => {
                      this.correct(row)
                    }}
                  >
                    修正
                  </el-button>
                )}
              </div>
            )
          }
        }
      ]
    }
  },
  methods: {
    objectSpanMethod({ row, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (
          rowIndex > 0 &&
          this.pricesData[rowIndex - 1].roomId === row.roomId
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.pricesData.length; i++) {
            if (this.pricesData[i].roomId === row.roomId) {
              rowspan++
            } else {
              break
            }
          }

          return {
            rowspan,
            colspan: 1
          }
        }
      } else if (columnIndex === 1) {
        if (
          rowIndex > 0 &&
          this.pricesData[rowIndex - 1].feeCode === row.feeCode
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.pricesData.length; i++) {
            if (this.pricesData[i].feeCode === row.feeCode) {
              rowspan++
            } else {
              break
            }
          }
          return {
            rowspan,
            colspan: 1
          }
        }
      }
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const data = this.pricesData.map(item => {
            if (item.unique === this.fromModel.unique) {
              item = {
                ...item,
                fixAmount: this.fromModel.fixAmount,
                reason: this.fromModel.reason
              }
              return item
            } else {
              return item
            }
          })
          this.$emit('updateData', data)
          this.visible = false
        }
      })
    },
    correct(row) {
      this.visible = true
      this.title = row.feeName + '修正'
      const { amount, fixAmount, reason, unique } = row
      this.fromModel = {
        amount,
        fixAmount,
        reason,
        unique
      }
    }
  }
}
</script>

<style scoped lang="scss"></style>
