import request from '@/utils/request'
export function getContractDetail(id) {
  return request({
    url: `/contract/record/detail/${id}`,
    method: 'get'
  })
}

// 获得合同信息分页
export function getContractList(params) {
  return request({
    url: `/contract/record/page`,
    method: 'get',
    isTable: true,
    params
  })
}

// 删除合同
export function deleteContract(data) {
  return request.post(`/contract/record/delete`, data, {
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 导出合同信息 Excel
export function exportContract(params) {
  return request({
    url: `/contract/record/export-excel`,
    method: 'get',
    params
  })
}

// 获取园区数据
export function getPark() {
  return request({
    url: `/housing/park/listAll`,
    method: 'get',
    hideLoading: true
  })
}

// 下载模板
export function downloadTem() {
  return `${process.env.VUE_APP_URL_PREFIX}/contract/template/download`
}

// 上传合同模板
export function uploadTem(id) {
  return request({
    url: `/contract/template/upload?attachId=${id}`,
    method: 'get'
  })
}
// 下载模板
export function getUploadTem(id) {
  return `${process.env.VUE_APP_URL_PREFIX}/contract/template/upload?attachId=${id}`
}
