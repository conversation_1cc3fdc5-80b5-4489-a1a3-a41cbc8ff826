<template>
  <div class="detail-right h100">
    <!--    合同应收金额-->
    <div class="text-n bg-white font-size-14">
      <div class="m-b-16">合同应收金额</div>
      <div class="color-warning m-b-24">¥:{{ NumFormat(objData.amount) }}</div>
      <div class="m-b-16">合同已收进度</div>
      <div class="m-b-8">
        <el-progress
          :text-inside="true"
          :stroke-width="16"
          :percentage="objData.process"
        ></el-progress>
      </div>
      <div class="color-warning m-b-24">
        ¥:{{ NumFormat(objData.paidAmount) }}
      </div>
      <div
        class="m-b-16"
        v-if="objData.billList && objData.billList.length > 0"
      >
        出账未支付
      </div>
      <div v-if="objData.billList && objData.billList.length > 0">
        <div
          class="flex align-items-center justify-content-between p-r-20"
          v-for="item in objData.billList"
          :key="item.id"
        >
          <div>
            <span class="m-r-8">{{ NumFormat(item.month) }}</span>
            <span class="color-warning">¥:{{ NumFormat(item.amount) }}</span>
          </div>
          <el-button type="text" @click="toAmountHandler(item.id)"
            >查看</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getContractBill } from '../api/create'
import { NumFormat } from '@/utils/tools'
export default {
  name: 'DetailReturned',
  props: {
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      NumFormat,
      objData: {}
    }
  },
  created() {
    this.getContractBill()
  },
  methods: {
    toAmountHandler(id) {
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id
        }
      })
    },
    async getContractBill() {
      let contractId = this.$route.query.id
      const res = await getContractBill({ contractId })
      this.objData = res
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-right {
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
}
</style>
