<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      :modal-append-to-body="false"
      width="30%"
    >
      <template v-slot:title>
        <div class="flex align-items-center p-t-10">
          <svg-icon
            v-if="showIcon"
            class="font-size-24 color-primary"
            icon-class="info-circle-filled"
          />
          <svg-icon
            v-else
            class="font-size-24 color-danger"
            icon-class="info-circle-filled"
          />
          <div class="font-size-16 m-l-8" style="font-weight: 400">
            {{ title }}？
          </div>
        </div>
      </template>
      <div class="m-l-32 m-t-16 p-l-32 line-height-22">
        <div>{{ title }}{{ entName }}合同？</div>
        <div>合同编号为:{{ contractNo }}</div>
      </div>
      <template v-slot:footer>
        <el-button type="info" @click="visible = false">取消</el-button>
        <el-button
          type="primary"
          :disabled="flag"
          @click="delHandler(title, 'renew')"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getRecordDelete,
  // getRecordSign,
  getRecordSubmit,
  getRecordWithdraw
} from '@/views/manage/house/contract/contract-special/api/create'
export default {
  name: 'ContractDialog',
  props: {
    title: {
      type: String,
      default: ''
    },
    showIcon: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      visible: false,
      entName: '某某某有限公司',
      contractNo: '32323123123123121313',
      id: '',
      flag: false
    }
  },
  methods: {
    async delHandler(e, type) {
      if (e === '确认删除') {
        await getRecordDelete(this.id)
        this.visible = false
        this.$toast.success('删除成功')
        if (this.$route.query.id) {
          this.$router.go(-1)
        } else {
          this.$emit('getNewList')
        }
      } else if (e === '确认发起') {
        await getRecordSubmit(this.id)
        this.visible = false
        this.$toast.success('发起成功')
        this.$emit('getNewList')
        await this.$router.push({
          path: '/rentalHousing/contractSpecial'
        })
      } else if (e === '确认撤回审核') {
        this.flag = true
        await getRecordWithdraw(this.id)
        this.visible = false
        this.$toast.success('撤回审核成功')
        this.flag = false
        this.$emit('getNewList')
      } else if (e === '确认签订') {
        this.visible = false
        this.$emit('showContractFill')
      } else if (e === '确认续签') {
        this.visible = false
        let id = this.id
        await this.$router.push({
          path: '/rentalHousing/contractSpecial/contractCreate',
          query: {
            id: id ? id : this.$route.query.id,
            type
          }
        })
      } else if (e === '确认下载') {
        this.visible = false
      }
    }
  }
}
</script>

<style scoped></style>
