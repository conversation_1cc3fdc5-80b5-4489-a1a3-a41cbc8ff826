import { filterDate } from '@/filter'
import { parseTime } from '@/utils/tools'
import CompanyInfo from '@/views/manage/house/contract/contract-special/companyInfo.vue'
export default {
  components: {
    CompanyInfo
  },
  data() {
    return {
      tableColumnContract: [
        {
          label: '楼栋',
          prop: 'building',
          render: (h, scope) => {
            return <div>{scope.row.building + scope.row.floor + '层'}</div>
          }
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '房型',
          prop: 'businessTypeStr'
        },
        {
          label: '房源面积(m²)',
          prop: 'area',
          headerAlign: 'center',
          align: 'center'
        },
        {
          label: '是否已测绘',
          prop: 'hasSurveying',
          align: 'center',
          render: (h, scope) => {
            return <div>{scope.row.hasSurveying ? '是' : '否'}</div>
          }
        },
        {
          label: '执行面积(m²)',
          prop: 'executeArea',
          align: 'center'
        },
        {
          label: '提前退租时间',
          prop: 'earlyTime',
          align: 'center',
          render: (h, { row }) => {
            return row.early ? (
              <div>已退租/{row.earlyTime}</div>
            ) : (
              <span>-</span>
            )
          }
        }
      ],
      tableColumnPlan: [
        {
          label: '期数',
          prop: 'periodNumber'
        },
        {
          label: '费用类型',
          prop: 'planTypeStr'
        },
        {
          label: '账单应缴时间',
          prop: 'billPayTime'
        },
        {
          label: '账单周期',
          prop: 'periodStartTime',
          render: (h, scope) => {
            return (
              <div>
                {filterDate(scope.row.periodStartTime) +
                  (filterDate(scope.row.periodEndTime) !== null &&
                  filterDate(scope.row.periodEndTime) !== ''
                    ? '~'
                    : '') +
                  filterDate(scope.row.periodEndTime)}
              </div>
            )
          }
        },
        {
          label: '账单金额',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div style={'color: #ED7B2F;'}>{scope.row.amount.toFixed(2)}</div>
            )
          }
        }
      ],
      tableColumnTotalPlan: [
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '开始时间',
          prop: 'startTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.startTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '结束时间',
          prop: 'endTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.endTime, '{y}-{m}-{d}')}</div>
          }
        }
      ]
    }
  }
}
