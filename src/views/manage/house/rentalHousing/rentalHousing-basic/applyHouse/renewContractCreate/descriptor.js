import {
  contractTypeOptions,
  payCycleOptions
} from '@/views/manage/house/rentalHousing/rentalHousing-basic/contract/contract-basic/utils/status'
import HouseInfo from './components/houseInfo'
import HousingRental from './components/housingRental'

const validatePhone = (rule, value, callback, label) => {
  if (value === '') {
    callback(new Error(`请输入${label || '手机号'}`))
  } else {
    if (/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value)) {
      callback()
    } else {
      callback(new Error(`请输入正确的${label || '手机号'}或座机号`))
    }
  }
}

let basicSpan = 8
export default {
  components: { HouseInfo, HousingRental },
  data() {
    return {
      formConfigure: {
        labelWidth: '100px',
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '签约主体',
            span: basicSpan,
            options: [],
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择签约主体', //请输入企业名称
                trigger: 'input'
              }
            ]
          },
          contacts: {
            form: 'input',
            label: '联系人',
            span: basicSpan,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入法人代表或授权代表姓名'
              }
            ],
            attrs: {
              maxlength: 20
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            span: basicSpan,
            maxlength: 11,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                message: '联系方式格式不正确',
                validator: validatePhone
              }
            ]
          }
        }
      },
      formConfigureContract: {
        labelWidth: '110px',
        descriptors: {
          park: {
            form: 'input',
            label: '入住园区',
            span: basicSpan,
            disabled: true,
            options: [],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择企业入住的园区'
              }
            ]
          },
          contractType: {
            form: 'input',
            label: '合同类型',
            span: basicSpan,
            options: contractTypeOptions,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择合同类型'
              }
            ]
          },
          contractTemplate: {
            form: 'input',
            label: '合同模版',
            required: true,
            span: basicSpan,
            disabled: true,
            options: [],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择合同模版'
              }
            ]
          },
          houseInfo: {
            form: 'component',
            render: () => {
              return <house-info rooms={this.rooms} />
            }
          },
          housingRental: {
            form: 'component',
            label: '',
            render: () => {
              return this.rooms.length > 0 ? (
                <housing-rental pricesData={this.prices} />
              ) : (
                ''
              )
            }
          },
          contractNo: {
            form: 'input',
            label: '合同编号',
            span: basicSpan,
            disabled: true,
            rule: [
              {
                type: 'string',
                message: '请输入合同编号'
              }
            ]
          },
          startEndTime: {
            form: 'dateRange',
            label: '合同起止时间',
            span: basicSpan,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择合同起止时间'
              }
            ],
            props: {
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              rangeSeparator: '-'
            }
          },
          paymentMethod: {
            form: 'input',
            label: '付款方式',
            span: basicSpan,
            options: payCycleOptions,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择付款方式'
              }
            ]
          },
          whetherFree: {
            label: '免租期',
            form: 'radio',
            span: basicSpan,
            disabled: true,
            rule: [
              {
                message: '请选择是否免租期',
                type: 'boolean'
              }
            ],
            options: [
              {
                label: '是',
                value: true
              },
              {
                label: '否',
                value: false
              }
            ]
          },
          freePeriod: {
            label: '免租时长',
            form: 'input',
            span: basicSpan,
            hidden: true,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入免租时长'
              }
            ],
            customRight: () => {
              return <div class="font-size-14 line-height-32">天</div>
            }
          },
          freeEffectTime: {
            form: 'date',
            label: '免租生效日期',
            span: basicSpan,
            hidden: true,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择免租生效日期'
              }
            ]
          }
        }
      },
      formConfigureAttach: {
        labelWidth: '110px',
        descriptors: {
          additional: {
            form: 'input',
            label: '附加条款',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入附加条款'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 5,
              maxlength: 200,
              showWordLimit: true
            }
          },
          attachMap: {
            form: 'component',
            label: '合同附件上传',
            rule: [
              {
                required: false,
                message: '请上传附件',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'containerSign'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      },
      formConfigureUnitPrice: {
        labelWidth: '130px',
        descriptors: {}
      },
      formConfigureFeeAll: {
        labelWidth: '130px',
        descriptors: {}
      }
    }
  }
}
