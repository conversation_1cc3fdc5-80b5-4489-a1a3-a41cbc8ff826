<template>
  <basic-card class="pos-relative">
    <flow-form
      class="flow-form-wrapper"
      ref="flowForm"
      title="退房"
      v-permission="routeButtonsPermission.AUDIT"
    />
    <el-row class="renew-table-wrapper">
      <el-row>
        <el-col :span="4">续房房间</el-col>
        <el-col :span="8">{{ detailInfo.room }}</el-col>
        <el-col :span="4">续约时间</el-col>
        <el-col :span="8"
          >{{ detailInfo.renewalStartTime }} -
          {{ detailInfo.renewalEndTime }}</el-col
        >
        <el-col :span="4">入住人</el-col>
        <el-col :span="20">{{ detailInfo.occupant }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="4">申请人</el-col>
        <el-col :span="8">{{ detailInfo.applyer }}</el-col>
        <el-col :span="4">申请时间</el-col>
        <el-col :span="8">{{ detailInfo.applyTime }}</el-col>
        <el-col :span="4">申请主体</el-col>
        <el-col :span="20">{{ detailInfo.applyTypeStr }}</el-col>
      </el-row>
      <el-row>
        <el-col :span="4">续约说明</el-col>
        <el-col :span="20">{{ detailInfo.illustrate || '无' }}</el-col>
      </el-row>
    </el-row>
  </basic-card>
</template>

<script>
import { getRenewalDetail } from '../api'
import FlowForm from '@/components/FlowForm/index.vue'
export default {
  name: 'RenewDetail',
  components: { FlowForm },
  data() {
    return {
      detailInfo: {}
    }
  },
  created() {
    this.getRenewalDetail()
  },
  methods: {
    getRenewalDetail() {
      const id = this.$route.query.id
      if (!id) return
      getRenewalDetail({ id }).then(res => {
        this.detailInfo = res || {}
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flow-form-wrapper {
  position: absolute;
  right: 24px;
  top: 18px;
}
.renew-table-wrapper {
  border-left: 1px solid;
  border-top: 1px solid;
  border-color: #e7e7e7;
}
:deep(.el-row) {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
:deep(.el-col) {
  padding: 14px 25px;
  line-height: 22px;
  font-size: 14px;
  color: #666;
  background: #fff;
  border-bottom: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
}
:deep(.el-col-4) {
  color: #999;
  background: rgba(231, 231, 231, 0.5);
}
</style>
