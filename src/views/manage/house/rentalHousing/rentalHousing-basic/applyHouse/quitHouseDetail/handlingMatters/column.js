import { NumFormat } from '@/utils/tools'

function checkChinese(str) {
  let reg = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
  return reg.test(str)
}
export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'billCode',
          label: '账单编号'
        },
        {
          prop: 'typeStr',
          label: '账单类型'
        },
        {
          prop: 'period',
          label: '账单期数'
        },
        {
          prop: 'rcvAmtSdt',
          label: '账单周期',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt}~{scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          prop: 'rcvAmtEdt',
          label: '结束时间'
        },
        // {
        //   prop: 'actualEndTime',
        //   label: '实际结束时间'
        // },
        {
          prop: 'billingAmount',
          label: '账单金额(元)',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.billingAmount)}
              </div>
            )
          }
        },
        {
          prop: 'dueAmount',
          label: '应缴金额(元)',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.dueAmount)}</div>
            )
          }
        },
        {
          prop: 'paidAmount',
          label: '实缴金额(元)',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.paidAmount)}</div>
            )
          }
        },
        {
          prop: 'status ',
          label: '需结清(元)',
          hidden: false,
          render: (h, scope) => {
            return (
              <div
                class={checkChinese(scope.row.status) ? '' : 'color-warning'}
              >
                {NumFormat(scope.row.status)}
              </div>
            )
          }
        },
        {
          prop: 'status',
          label: '应退还(元)',
          hidden: true,
          render: (h, scope) => {
            return (
              <div
                class={checkChinese(scope.row.status) ? '' : 'color-warning'}
              >
                {NumFormat(scope.row.status)}
              </div>
            )
          }
        },
        //操作
        {
          prop: 'operate',
          label: '操作',
          width: '100px',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  v-permission={this.routeButtonsPermission.VIEW}
                  onClick={() => {
                    this.meterReading(scope.row)
                  }}
                  class="m-r-10"
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
                {this.cost === 1 ? (
                  <span>
                    {scope.row.readingPark ||
                    scope.row.verification ||
                    scope.row.remind ? (
                      <el-dropdown>
                        <el-link type="primary">更多</el-link>
                        <el-dropdown-menu slot="dropdown">
                          {scope.row.readingPark ? (
                            <el-dropdown-item>
                              <el-link
                                type="danger"
                                v-permission={
                                  this.routeButtonsPermission.METER_READING_PARK
                                }
                                onClick={() => {
                                  this.meterReading(scope.row)
                                }}
                              >
                                {this.routeButtonsTitle.METER_READING_PARK}
                              </el-link>
                            </el-dropdown-item>
                          ) : (
                            ''
                          )}
                          {scope.row.verification ? (
                            <el-dropdown-item>
                              <el-link
                                type="success"
                                v-permission={
                                  this.routeButtonsPermission.VERIFICATION
                                }
                                onClick={() => {
                                  this.goVerification(scope.row)
                                }}
                              >
                                去{this.routeButtonsTitle.VERIFICATION}
                              </el-link>
                            </el-dropdown-item>
                          ) : (
                            ''
                          )}
                          {scope.row.remind ? (
                            <el-dropdown-item>
                              <el-link
                                type="warning"
                                v-permission={
                                  this.routeButtonsPermission.REMIND_BUSINESSES
                                }
                                onClick={() => {
                                  this.remindEntFn(scope.row)
                                }}
                              >
                                {this.routeButtonsTitle.REMIND_BUSINESSES}
                              </el-link>
                            </el-dropdown-item>
                          ) : (
                            ''
                          )}
                        </el-dropdown-menu>
                      </el-dropdown>
                    ) : (
                      ''
                    )}
                  </span>
                ) : (
                  ''
                )}

                {/*<el-link*/}
                {/*    class="m-l-8"*/}
                {/*    type="success"*/}
                {/*    v-permission={this.routeButtonsPermission.SEND_BACK}*/}
                {/*    onClick={() => {*/}
                {/*      this.sendBackFn(scope.row)*/}
                {/*    }}*/}
                {/*  >*/}
                {/*    {this.routeButtonsTitle.SEND_BACK}*/}
                {/*  </el-link>*/}
                {/*)}*/}
              </div>
            )
          }
        }
      ],
      thingTableColumn: [
        {
          prop: 'eventType',
          label: '事项类型'
        },
        {
          prop: 'matter',
          label: '事项内容'
        },
        {
          prop: 'doneStr',
          label: '处理状态'
        },
        {
          prop: 'result',
          label: '处理结果'
        },
        {
          prop: 'handlers',
          label: '处理人'
        },
        {
          prop: 'handleTime',
          label: '处理时间'
        },
        {
          //操作
          prop: 'operate',
          label: '操作',
          width: '100px',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.done ? (
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.previewEvent(scope.row)
                    }}
                    className="m-r-10"
                  >
                    查看
                  </el-link>
                ) : scope.row.code === 'quit_termination_contract' ? (
                  <el-link
                    type="danger"
                    onClick={() => {
                      this.terminationContractFn(scope.row)
                    }}
                  >
                    终止合同
                  </el-link>
                ) : scope.row.code === 'quit_edit_contract' ? (
                  <el-link
                    type="warning"
                    onClick={() => {
                      this.goDispose(scope.row)
                    }}
                  >
                    去处理
                  </el-link>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ],
      tableColumnMoney: [
        {
          prop: 'feeName',
          label: '费用名称'
        },
        {
          prop: 'recipient',
          label: '收款方'
        },
        {
          prop: 'rcvAmtSdt',
          label: '账期',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt}~{scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          prop: 'amount',
          label: '应缴金额(元)',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          prop: 'remark',
          label: '备注',
          showOverflowTooltip: true
        },
        {
          prop: 'registration',
          label: '登记人'
        },
        {
          prop: 'registrationTime',
          label: '登记时间'
        },
        {
          prop: 'status',
          label: '状态',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.status === 1 ? (
                  <el-tag type="warning">未处理</el-tag>
                ) : (
                  <el-tag type="success">已处理</el-tag>
                )}
              </div>
            )
          }
        },
        {
          //操作
          prop: 'operate',
          label: '操作',
          width: '100px',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.EDIT}
                  type="primary"
                  onClick={() => {
                    this.moneyEdit(scope.row)
                  }}
                  class="m-r-10"
                >
                  {this.routeButtonsTitle.EDIT}
                </el-link>
                {scope.row.status === 1 ? (
                  <el-link
                    v-permission={this.routeButtonsPermission.DELETE}
                    type="danger"
                    onClick={() => {
                      this.moneyDel(scope.row)
                    }}
                  >
                    {this.routeButtonsTitle.DELETE}
                  </el-link>
                ) : (
                  ''
                )}
              </div>
            )
          }
        }
      ]
    }
  }
}
