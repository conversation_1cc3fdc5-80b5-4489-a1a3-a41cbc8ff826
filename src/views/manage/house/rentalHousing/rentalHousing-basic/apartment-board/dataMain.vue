<template>
  <div class="hello">
    <div v-for="(item, index) in tolistone" :key="index">{{ item }}</div>
    <div @click="showto" class="show-more">{{ textwprd }}</div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tolistone: [],
      toList: [
        'html',
        'css',
        'javascript',
        'vue',
        'react',
        'angule' //进行显示的数据
      ],
      textwprd: '查看更多'
    }
  },
  mounted() {
    this.weet()
  },
  methods: {
    // 查看更多和收起
    showto() {
      if (this.textwprd === '查看更多') {
        this.tolistone = this.toList
        this.textwprd = '收起'
      } else {
        this.tolistone = this.toList.slice(0, 4)
        this.textwprd = '查看更多'
      }
    },
    weet() {
      if (this.toList.length > 4) {
        // 截取数组的前四位
        this.tolistone = this.toList.slice(0, 4)
      } else {
        this.tolistone = this.toList
      }
    }
  }
}
</script>
