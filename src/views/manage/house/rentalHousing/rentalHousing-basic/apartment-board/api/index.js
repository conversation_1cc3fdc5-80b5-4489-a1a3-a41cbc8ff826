import request from '@/utils/request'

// 获取园区列表
export function getBoardParks(data) {
  return request({
    url: `/housing/apartment/board/parks`,
    method: 'post',
    data
  })
}
// 获取分组列表
export function getBoardGroups(data) {
  return request({
    url: `/housing/apartment/board/groups`,
    method: 'post',
    data
  })
}
// 获取项目分组列表
export function getBoardMultiGroups(data) {
  return request({
    url: `/housing/board/multi_groups`,
    method: 'post',
    data
  })
}
// 通过分组获取园区
export function getBoardParksByGroups(params) {
  return request({
    url: `/housing/board/parks_by_group`,
    method: 'get',
    params
  })
}
// 获取楼栋
export function getBoardBuildings(parkId) {
  return request({
    url: `/housing/apartment/board/buildings/${parkId}`,
    method: 'get'
  })
}
// 获取楼栋
export function getBoardFloors(parkId) {
  return request({
    url: `/housing/apartment/board/floors/${parkId}`,
    method: 'get'
  })
}
// 获取房源看板
export function getBoard(data) {
  return request({
    url: `/housing/apartment/board/board`,
    method: 'post',
    data
  })
}
// 获取房源信息
export function getBoardDetail(params) {
  return request({
    url: `/housing/board/detail`,
    method: 'get',
    params
  })
}
// 房源统计获取年选择
export function getBoardStatisticsYear(roomId) {
  return request({
    url: `/housing/board/statistics_year/${roomId}`,
    method: 'get'
  })
}
// 房源统计获取年度
export function getBoardStatisticsAnnual(year) {
  return request({
    url: `/housing/board/statistics_annual/${year}`,
    method: 'get'
  })
}
// 房源统计饼图
export function getBoardStatisticsPie({ roomId, year, annual }) {
  return request({
    url: `/housing/board/statistics/${roomId}/${year}/${annual}`,
    method: 'get'
  })
}
// 房源统状态tab
export function getBoardDayStatus() {
  return request({
    url: `/housing/board/day_status`,
    method: 'get'
  })
}
// 房源统计空置房间列表
export function getBoardDay(data) {
  return request({
    url: `/housing/board/day`,
    method: 'post',
    data
  })
}
// 房态历史年份筛选
export function getBoardHistoryYear(roomId) {
  return request({
    url: `/housing/board/history_year/${roomId}`,
    method: 'get'
  })
}
// 房态历史状态筛选
export function getBoardHistoryStatus({ roomId, year }) {
  return request({
    url: `/housing/board/history_status/${roomId}/${year}`,
    method: 'get'
  })
}
// 房态历史列表
export function getBoardHistoryList(data) {
  return request({
    url: `/housing/board/history_list`,
    method: 'post',
    data
  })
}
// 操作记录年份筛选
export function getBoardOperateYear(roomId) {
  return request({
    url: `/housing/board/operate_year/${roomId}`,
    method: 'get'
  })
}
// 操作记录列表
export function getBoardOperateList(data) {
  return request({
    url: `/housing/board/operate_list`,
    method: 'post',
    data
  })
}
// 意向标记
export function getBoardIntent(data) {
  return request({
    url: `/housing/board/intent`,
    method: 'post',
    data
  })
}
// 健康管理
export function getBoardHealthy(data) {
  return request({
    url: `/housing/board/healthy`,
    method: 'post',
    data
  })
}
// 创建房源看板标记
export function getBoardHMarkCreate(data) {
  return request({
    url: `/housing/board-mark/create`,
    method: 'post',
    data
  })
}
// 房源看板标记列表
export function getBoardHMarkList(params) {
  return request({
    url: `/housing/board-mark/list`,
    method: 'get',
    params
  })
}
// 删除房源看板标记
export function getBoardHMarkDelete(params) {
  return request({
    url: `/housing/board-mark/delete`,
    method: 'delete',
    params
  })
}
// 头部统计
export function getHouingHeader(params) {
  return request({
    url: `/statistics/header/houing_header`,
    method: 'get',
    params
  })
}

// 筛选项-房间业态
export function getApartmentTypes(params) {
  return request({
    url: `/housing/apartment/board/types`,
    method: 'get',
    params
  })
}
