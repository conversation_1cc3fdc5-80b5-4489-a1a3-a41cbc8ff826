<template>
  <div class="empty-container wh100">
    <img src="../images/empty.png" alt="" />
    <p class="m-t-4 m-b-16 font-size-12 empty-tips">
      {{
        !isSearch
          ? '还没有任何房源，去添加房间？'
          : '选中的筛选条件暂无数据，可以试试其他条件'
      }}
    </p>
    <el-button
      v-if="!isSearch"
      class="empty-btn font-size-14"
      type="primary"
      @click="toMaintain"
      >去房源库</el-button
    >
  </div>
</template>

<script>
export default {
  name: 'TableEmpty',
  props: {
    isSearch: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toMaintain() {
      this.$router.push('/houseManage/index')
    }
  }
}
</script>

<style scoped lang="scss">
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .empty-tips {
    color: rgba(0, 0, 0, 0.26);
  }
  .empty-btn {
    color: rgba(255, 255, 255, 0.9);
  }
}
</style>
