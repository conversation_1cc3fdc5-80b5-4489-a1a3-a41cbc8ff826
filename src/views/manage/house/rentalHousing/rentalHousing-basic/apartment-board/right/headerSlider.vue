<template>
  <div class="header-slider-container flex">
    <div
      v-if="disabled"
      class="arrowhead arrowhead-left"
      @click="arrowheadHandle('pre')"
    >
      <svg-icon icon-class="chevron-left" />
    </div>
    <div class="slider-container" :class="{ w100: !disabled }">
      <div class="scroll-wrapper" ref="scrollWrapper">
        <div class="scroll-content" ref="scrollContent">
          <div
            class="scroll-item"
            v-for="item in list"
            :key="item.key"
            :class="{ active: selectList.includes(item.key) }"
            @click="clickHandle(item.key)"
          >
            {{ item.label }}
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="disabled"
      class="arrowhead arrowhead-right"
      @click="arrowheadHandle('next')"
    >
      <svg-icon icon-class="chevron-right" />
    </div>
  </div>
</template>

<script>
import BScroll from 'better-scroll'
export default {
  name: 'HeaderSlider',
  props: {
    // 是否全选
    isAll: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      bs: null,
      currentPageX: 0, // 当前x轴位置
      selectList: [], // 选中的元素
      wrapperWidth: 100,
      contentWidth: 100,
      list: [] // 楼栋列表
    }
  },
  computed: {
    disabled() {
      return this.wrapperWidth <= this.contentWidth
    }
  },
  watch: {
    isAll(val) {
      if (val) {
        this.selectList = []
      }
    },
    disabled: {
      handler() {
        this.$nextTick(() => {
          this.init()
        })
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    window.addEventListener('resize', this.getWidth)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getWidth)
    this.bs.destroy()
    this.bs = null
  },
  methods: {
    async getData(list = []) {
      this.list = list
      await this.$nextTick()
      this.getWidth()
    },
    // 获取宽度计算左右箭头是否显示
    getWidth() {
      this.$nextTick(() => {
        this.wrapperWidth = this.$refs.scrollWrapper.offsetWidth
        this.contentWidth = this.$refs.scrollContent.offsetWidth
        this.init()
      })
    },
    // 选中取消
    clickHandle(row) {
      if (this.selectList.includes(row)) {
        this.selectList = this.selectList.filter(item => item !== row)
      } else {
        this.selectList.push(row)
      }
      this.$emit('selectHandle', this.selectList)
    },
    // 左右滚动
    arrowheadHandle(type) {
      // 容器宽度
      const width = this.$refs.scrollWrapper.clientWidth
      const moveSize = width / 2
      this.currentPageX =
        type === 'pre'
          ? this.currentPageX + moveSize
          : this.currentPageX - moveSize
      this.bs.scrollTo(this.currentPageX, 0, 300)
    },
    init() {
      this.bs = new BScroll(this.$refs.scrollWrapper, {
        scrollX: true,
        bounce: false,
        probeType: 3 // listening scroll event
      })
      // 监听滚动结束
      this.bs.on('scrollEnd', page => {
        this.currentPageX = page.x
      })
    }
  }
}
</script>

<style scoped lang="scss">
.header-slider-container {
  width: calc(100% - 40px);
  .slider-container {
    width: calc(100% - 64px);
    .scroll-wrapper {
      width: 100%;
      white-space: nowrap;
      position: relative;
      overflow: hidden;
      .scroll-content {
        display: inline-block;
        .scroll-item {
          display: inline-block;
          line-height: 40px;
          padding: 0 16px 0 17px;
          cursor: pointer;
          border-right-width: 1px;
          border-style: solid;
          @include border_color(--border-color-light);
          &.active {
            background: #e9f0ff;
          }
        }
      }
    }
  }
  .arrowhead {
    width: 32px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    position: relative;
    &.arrowhead-left {
      border-right-width: 1px;
      border-style: solid;
      @include border_color(--border-color-light);
      &::after {
        display: block;
        content: '';
        width: 8px;
        height: 40px;
        background: linear-gradient(
          -90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(231, 231, 231, 0.5996) 100%
        );
        position: absolute;
        top: 0;
        right: -8px;
      }
    }
    &.arrowhead-right {
      border-left-width: 1px;
      border-style: solid;
      @include border_color(--border-color-light);
      &::before {
        display: block;
        content: '';
        width: 8px;
        height: 40px;
        background: linear-gradient(
          90deg,
          rgba(255, 255, 255, 0) 0%,
          rgba(231, 231, 231, 0.5996) 100%
        );
        position: absolute;
        top: 0;
        left: -8px;
      }
    }
  }
}
</style>
