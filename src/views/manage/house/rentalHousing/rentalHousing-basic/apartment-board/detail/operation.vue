<template>
  <div
    class="detail-history"
    v-loading="loading"
    element-loading-text="数据加载中"
    element-loading-spinner="el-icon-loading"
  >
    <div>
      <el-form ref="form" :model="formData" class="flex">
        <el-form-item label="时间选择" label-width="70px">
          <el-select
            style="width: 140px"
            v-model="formData.year"
            placeholder="请选择时间"
            @change="yearHandle"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in yearList"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <el-scrollbar v-if="list && list.length" style="height: calc(100% - 51px)">
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in list"
          :key="index"
          :timestamp="`${item.title} | ${item.dept ? item.dept + '-' : ''}${
            item.operator
          }`"
          type="primary"
          :color="getOperateColor(item.statusId)"
          size="normal"
          placement="top"
        >
          <div class="text-color font-size-12 line-height-20">
            <p class="m-b-8">
              {{ parseTime(item.time, '{y}/{m}/{d} {h}:{i}:{s}') }}
            </p>
            <p class="font-size-14">{{ item.content }}</p>
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-scrollbar>
    <div v-else-if="!loading" class="wh100" style="height: calc(100% - 51px)">
      <empty-data />
    </div>
  </div>
</template>

<script>
import {
  getBoardOperateList,
  getBoardOperateYear
} from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/api'
import { parseTime } from '@/utils/tools'
import { getOperateColor } from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/utils/houseColor'
export default {
  name: 'BoardSpecialRecord',
  data() {
    return {
      getOperateColor,
      parseTime,
      formData: {},
      list: [],
      yearList: [],
      info: {},
      loading: true
    }
  },
  methods: {
    init(info) {
      this.info = info
      this.getBoardOperateYear()
    },
    updateHandle() {
      this.getBoardOperateList()
    },
    // 获取列表
    getBoardOperateList() {
      try {
        this.loading = true
        const params = {
          roomId: this.info.roomId,
          ...this.formData
        }
        getBoardOperateList(params).then(res => {
          this.list = res || []
          this.loading = false
        })
      } catch (e) {
        this.loading = false
        console.error(e)
      }
    },
    // 选择年
    yearHandle() {
      this.getBoardOperateList()
    },
    // 获取年份
    getBoardOperateYear() {
      getBoardOperateYear(this.info.roomId).then(res => {
        this.yearList = res || []
        if (res && res.length) {
          this.$set(this.formData, 'year', res[0].key)
          this.getBoardOperateList()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-history {
  position: absolute;
  width: calc(100% - 24px);
  height: calc(100% - 24px);
  .text-color {
    color: rgba(0, 0, 0, 0.6);
  }
}
::v-deep {
  .el-timeline {
    padding-top: 10px;
  }
  .el-timeline-item__node--normal {
    left: 1px;
    width: 8px;
    height: 8px;
  }
  .el-timeline-item__timestamp {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
  }
  .el-timeline-item__timestamp.is-top {
    padding-top: 0;
  }
  .el-scrollbar__bar.is-vertical > div {
    width: 4px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
    opacity: 1;
  }
}
</style>
