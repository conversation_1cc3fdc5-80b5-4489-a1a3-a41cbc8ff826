<template>
  <!--    意向标记-->
  <dialog-cmp
    :title="`意向标记`"
    :visible.sync="visible"
    width="35%"
    @confirmDialog="confirmDialog"
  >
    <el-form ref="form" :model="formData">
      <el-form-item label="房源名称">
        <el-input
          v-model="formData.listingName"
          :disabled="disabled"
          placeholder="请选择活动区域"
        ></el-input>
      </el-form-item>
      <el-form-item label="意向描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :maxlength="200"
          :rows="2"
          show-word-limit
          placeholder="请简要说明当前房源意向"
        ></el-input>
      </el-form-item>
    </el-form>
  </dialog-cmp>
</template>

<script>
import { getBoardIntent } from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/api'

export default {
  name: 'board-dialog',
  data() {
    return {
      visible: false,
      disabled: true,
      formData: {
        listingName: '',
        description: ''
      },
      info: {}
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.formData = this.$options.data().formData
      }
    }
  },
  methods: {
    // 初始化
    init(info) {
      this.info = info
      this.formData.listingName = info.park + info.building + info.floor
      this.visible = true
    },
    // 确定
    confirmDialog() {
      try {
        this.$confirm('是否标记此房源为意向房源？').then(() => {
          const params = {
            id: this.info.roomId,
            description: this.formData.description
          }
          getBoardIntent(params).then(() => {
            this.$toast.success('房源标记成功')
            this.formData = this.$options.data().formData
            this.visible = false
            this.$emit('update')
          })
        })
      } catch (e) {
        console.error(e)
        this.$toast.error('房源标记错误, 请重新操作')
      }
    }
  }
}
</script>

<style scoped></style>
