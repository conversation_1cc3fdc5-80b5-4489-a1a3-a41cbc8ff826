<template>
  <!--  房源统计-->
  <div class="detail-statistics">
    <div class="m-b-22">
      <el-form ref="form" :model="formData" class="flex">
        <el-form-item label="时间选择" label-width="70px">
          <el-select
            style="width: 140px"
            v-model="formData.year"
            placeholder="请选择时间"
            @change="yearHandle"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in yearList"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="m-l-8">
          <el-select
            style="width: 140px"
            v-model="formData.annual"
            placeholder="请选择季度"
            @change="annualHandle"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in annualList"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="flex align-items-center m-l-32">
      <!--      饼状图-->
      <pie-data
        ref="pie"
        :res-data="pieFormatter(resData)"
        :colors="pieColorsFormatter(resData)"
        :content-data="contentData"
        :visible="visible"
      />
      <div class="m-l-16 font-size-12 line-height-20">
        <div
          class="flex align-items-center m-b-8"
          v-for="(item, index) in resData"
          :key="index"
        >
          <div
            class="item-dot"
            :style="{ background: getStatisticsPieColor(item.stateId) }"
          ></div>
          <span class="text-color">{{ item.name }}</span>
          <span class="m-l-8">{{ item.value }}</span>
          <span class="text-color m-l-4">天</span>
          <span class="text-color m-l-8">占比</span>
          <span class="m-l-4">{{ item.rate }}%</span>
        </div>
      </div>
    </div>
    <template v-if="tableShow && pageInit">
      <basic-tab
        style="margin-top: 40px"
        :tabs-data="tabsData"
        :current="extralQuerys.status"
        @tabsChange="tabsChange"
        :disabled="tabsDisabled"
      >
        <template v-slot:right>
          <el-date-picker
            v-model="datePickerTime"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </template>
      </basic-tab>
      <drive-table
        ref="drive-table"
        :columns="statisticsColumn"
        :scrollTop="false"
        :isNeedPagination="true"
        :extral-querys="extralQuerys"
        :api-fn="getBoardDay"
      >
      </drive-table>
    </template>
  </div>
</template>

<script>
import PieData from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/left/pie'
import {
  getBoardDay,
  getBoardDayStatus,
  getBoardStatisticsAnnual,
  getBoardStatisticsPie,
  getBoardStatisticsYear
} from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/api'
import { getStatisticsPieColor } from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/utils/houseColor'
import ColumnMixins from './column'
import BasicTab from '@/components/BasicTab'

export default {
  name: 'BoardSpecialStatistics',
  props: {
    tableShow: {
      type: Boolean,
      default: false
    }
  },
  mixins: [ColumnMixins],
  components: {
    BasicTab,
    PieData
  },
  data() {
    return {
      getBoardDay,
      extralQuerys: {
        status: -1
      },
      getStatisticsPieColor,
      resData: [],
      contentData: 0,
      visible: false,
      formData: {},
      yearList: [],
      annualList: [],
      info: {},
      pageInit: false,
      tabsData: [],
      tabsDisabled: false,
      datePickerTime: []
    }
  },
  watch: {
    datePickerTime: {
      handler(val) {
        if (val && val.length) {
          const [sTime = '', eTime = ''] = val
          this.$set(this.extralQuerys, 'sTime', sTime)
          this.$set(this.extralQuerys, 'eTime', eTime)
        } else {
          this.$set(this.extralQuerys, 'sTime', '')
          this.$set(this.extralQuerys, 'eTime', '')
        }
        this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    tabsChange(e) {
      if (this.tabsDisabled) return false
      this.extralQuerys.status = e
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    },
    getBoardDayStatus() {
      getBoardDayStatus().then(res => {
        const list = res || []
        if (list && list.length) {
          this.extralQuerys.status = list[0].key
        }
        this.tabsData = list.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    init(info) {
      this.info = info
      this.extralQuerys.roomId = info.roomId
      this.getBoardStatisticsYear()
      this.getBoardDayStatus()
    },
    updateHandle() {
      this.getBoardStatisticsPie()
    },
    // 饼图对应颜色
    pieColorsFormatter(list) {
      let pieColors = []
      list.forEach(item => {
        pieColors.push(getStatisticsPieColor(item.stateId))
      })
      return pieColors
    },
    // 格式化饼图所需数据
    pieFormatter(list) {
      return list.map(item => {
        return {
          value: item.value,
          name: `${item.name} ${item.value}天`
        }
      })
    },
    // 选择季度
    annualHandle() {
      this.$set(this.extralQuerys, 'annual', this.formData.annual)
      this.$emit('tableUpdate', this.extralQuerys)
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
      this.getBoardStatisticsPie()
    },
    // 选择年
    yearHandle() {
      this.$set(this.extralQuerys, 'year', this.formData.year)
      this.$emit('tableUpdate', this.extralQuerys)
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
      this.getBoardStatisticsAnnual()
    },
    // 获取饼图
    getBoardStatisticsPie() {
      const params = {
        roomId: this.info.roomId,
        ...this.formData
      }
      getBoardStatisticsPie(params).then(res => {
        this.contentData = res.total || 0
        this.resData = res.resultList || []
      })
    },
    // 获取季度
    getBoardStatisticsAnnual() {
      getBoardStatisticsAnnual(this.formData.year).then(res => {
        this.annualList = res || []
        if (res && res.length) {
          this.$set(this.formData, 'annual', res[0].key)
          this.$set(this.extralQuerys, 'annual', this.formData.annual)
          this.$emit('tableUpdate', this.extralQuerys)
          this.pageInit = true
          this.getBoardStatisticsPie()
        }
      })
    },
    // 获取年
    getBoardStatisticsYear() {
      getBoardStatisticsYear(this.info.roomId).then(res => {
        this.yearList = res || []
        if (res && res.length) {
          this.$set(this.formData, 'year', res[res.length - 1].key)
          this.$set(this.extralQuerys, 'year', this.formData.year)
          this.getBoardStatisticsAnnual()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-statistics {
  .text-color {
    color: rgba(0, 0, 0, 0.4);
  }
}
</style>
