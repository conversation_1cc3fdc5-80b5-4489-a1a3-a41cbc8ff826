<template>
  <div
    v-show="drawerVisible"
    ref="eagleMapContainer"
    class="detail-container"
    @click.stop
  >
    <div class="drag-eagle" @mousedown="dragEagle"></div>
    <el-drawer
      :visible.sync="drawerVisible"
      direction="btt"
      :modal-append-to-body="false"
      :wrapperClosable="false"
      :modal="false"
      ref="detailDrawer"
      @close="drawerClose"
    >
      <div class="flex justify-content-between" slot="title">
        <el-tabs v-model="componentName" @tab-click="handleClick">
          <el-tab-pane label="房源信息" name="BoardSpecialInfo"></el-tab-pane>
          <el-tab-pane
            label="房态统计"
            name="BoardSpecialStatistics"
          ></el-tab-pane>
          <el-tab-pane
            label="房态历史"
            name="BoardSpecialHistory"
          ></el-tab-pane>
          <el-tab-pane label="操作记录" name="BoardSpecialRecord"></el-tab-pane>
        </el-tabs>
        <div class="right-icon flex align-items-center">
          <el-tooltip class="item" effect="dark" content="固定" placement="top">
            <svg-icon
              v-if="detailInfo.isMark"
              class-name="header-icon"
              icon-class="icon-color-fixed"
              @click="toFixedHandle"
            />
            <svg-icon
              v-else
              class-name="header-icon"
              icon-class="fixed"
              @click="toFixedHandle"
            />
          </el-tooltip>
          <el-tooltip
            class="item"
            effect="dark"
            :content="zoomVisible ? '还原' : '展开'"
            placement="top"
          >
            <svg-icon
              v-if="!zoomVisible"
              icon-class="lifier"
              class-name="m-l-16 pointer"
              @click="unfoldHandle(true)"
            />
            <svg-icon
              v-else
              icon-class="lessen"
              class-name="m-l-16 pointer"
              @click="unfoldHandle(false)"
            />
          </el-tooltip>
          <el-tooltip class="item" effect="dark" content="关闭" placement="top">
            <svg-icon
              class-name="m-l-16 header-icon"
              icon-class="close"
              @click="drawerVisible = false"
            />
          </el-tooltip>
        </div>
      </div>
      <!--      <div class="detail-opacity wh100"></div>-->
      <div class="detail-wrapper w100">
        <el-scrollbar ref="scrollBarRef" class="h100">
          <div class="wh100 p-24">
            <component
              :ref="componentName"
              :is="componentName"
              :info="detailInfo"
              :tableShow="true"
            />
          </div>
        </el-scrollbar>
        <div class="detail-footer">
          <div class="footer-left">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item
                v-for="(item, index) in breadcrumbList"
                :key="index"
                >{{ item }}</el-breadcrumb-item
              >
            </el-breadcrumb>
          </div>
          <div class="footer-right">
            <el-button
              v-permission="routeButtonsPermission.HEALTH"
              type="info"
              @click="boardHealthyHandle"
              >{{ routeButtonsTitle.HEALTH }}</el-button
            >
            <el-button
              v-if="detailInfo.showIntention"
              type="primary"
              @click="intentHandle"
              v-permission="routeButtonsPermission.MARK"
              >意向{{ routeButtonsTitle.MARK }}</el-button
            >
          </div>
        </div>
      </div>
    </el-drawer>
    <board-dialog ref="boardDialog" @update="updateHandle" />
    <board-healthy ref="boardHealthy" @update="updateHealthy" />
  </div>
</template>

<script>
import BoardSpecialInfo from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/detail/info'
import BoardSpecialStatistics from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/detail/statistics'
import BoardSpecialHistory from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/detail/housingHistory'
import BoardSpecialRecord from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/detail/operation'
import BoardDialog from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/detail/components/board-dialog'
import BoardHealthy from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/detail/components/board-healthy'
import {
  getBoardDetail,
  getBoardHMarkCreate,
  getBoardHMarkDelete
} from '@/views/manage/house/rentalHousing/rentalHousing-basic/apartment-board/api'
import { mapState } from 'vuex'
export default {
  name: 'boardSpecialDetail',
  components: {
    BoardHealthy,
    BoardDialog,
    BoardSpecialRecord,
    BoardSpecialHistory,
    BoardSpecialStatistics,
    BoardSpecialInfo
  },
  data() {
    return {
      zoomVisible: false,
      drawerVisible: false, // 抽屉开关
      breadcrumbList: [],
      componentName: 'BoardSpecialInfo',
      roomId: 0,
      detailInfo: {} // 详情
    }
  },
  inject: ['BoardSpecialRight', 'BoardSpecial'],
  computed: {
    ...mapState({
      userId: state => state.user.id
    })
  },
  watch: {
    drawerVisible(val) {
      if (!val) {
        this.unfoldHandle(false)
      }
    }
  },
  mounted() {
    document.addEventListener('click', e => {
      this.clickListener(e)
    })
  },
  beforeDestroy() {
    document.removeEventListener('click', this.clickListener)
  },
  methods: {
    unfoldHandle(val) {
      this.zoomVisible = val
      const rightTable = document.getElementById('tableContainer')
      const targetDiv = this.$refs.eagleMapContainer
      targetDiv.style.height = val ? rightTable.style.height : '400px'
    },
    clickListener(e) {
      const dialogList = document.querySelectorAll('.el-dialog__wrapper')
      const messageList = document.querySelectorAll('.el-message-box__wrapper')
      const viewerList = document.querySelectorAll('.viewer-container')
      const modalList = document.querySelectorAll('.v-modal')
      const hasDialog = Array.from(dialogList).some(item => {
        return item && item.contains(e.target)
      })
      const hasMessage = Array.from(messageList).some(item => {
        return item && item.contains(e.target)
      })
      const hasViewer = Array.from(viewerList).some(item => {
        return item && item.contains(e.target)
      })
      const hasModal = Array.from(modalList).some(item => {
        return item && item.contains(e.target)
      })
      if (hasDialog || hasMessage || hasViewer || hasModal) return
      if (this.drawerVisible) this.drawerClose()
    },
    // 健康管理更新列表
    updateHealthy() {
      this.BoardSpecial.$refs.specialLeft.updateHandle()
      this.updateHandle()
      this.BoardSpecialRight.$refs.specialTable.$refs.specialTableRight.getBoard()
    },
    // 更新通知
    updateHandle() {
      this.getBoardDetail()
      if (this.componentName === 'BoardSpecialInfo') return
      this.$refs[this.componentName].updateHandle()
    },
    // 意向标记
    intentHandle() {
      this.$refs.boardDialog.init(this.detailInfo)
    },
    // 健康管理
    boardHealthyHandle() {
      this.$refs.boardHealthy.init(this.detailInfo)
    },
    // 弹窗关闭
    drawerClose() {
      this.componentName = 'BoardSpecialInfo'
      this.detailInfo = {}
      if (
        this.BoardSpecialRight &&
        this.BoardSpecialRight.$refs.specialTable &&
        this.BoardSpecialRight.$refs.specialTable.$refs.specialTableRight
      ) {
        this.BoardSpecialRight.$refs.specialTable.$refs.specialTableRight.selectHandle(
          0
        )
      }
      this.drawerVisible = false
    },
    // 获取详情
    getBoardDetail() {
      getBoardDetail({ roomId: this.roomId }).then(res => {
        this.detailInfo = res || {}
        this.$refs.BoardSpecialInfo &&
          this.$refs.BoardSpecialInfo.init(this.detailInfo)
        this.breadcrumbList = [
          res.entName,
          res.park,
          res.building,
          res.floor,
          res.room
        ].filter(item => {
          return item
        })
      })
    },
    // 初始化
    init(row) {
      this.roomId = row.roomId
      setTimeout(() => {
        this.getBoardDetail()
        this.drawerVisible = true
      }, 0)
    },
    // 切换
    handleClick() {
      this.$refs.scrollBarRef.wrap.scrollTop = 0
      this.$nextTick(() => {
        this.$refs[this.componentName].init(this.detailInfo)
      })
    },
    // 拖拽窗口大小
    dragEagle(e) {
      const targetDiv = this.$refs.eagleMapContainer
      const targetDivHeight = targetDiv.offsetHeight
      // clientY是该表高度，也可以取clientX改变宽度
      const startY = e.clientY
      document.onmousemove = function (e) {
        e.preventDefault()
        const distY = Math.abs(e.clientY - startY)
        if (e.clientY < startY) {
          targetDiv.style.height = targetDivHeight + distY + 'px'
        }
        if (e.clientY > startY) {
          targetDiv.style.height = targetDivHeight - distY + 'px'
        }
      }
      document.onmouseup = function () {
        document.onmousemove = null
      }
    },
    // 固定
    async toFixedHandle() {
      const isMark = this.detailInfo.isMark
      if (!isMark) {
        await getBoardHMarkCreate({
          roomId: this.roomId,
          apply: 2
        })
      } else {
        await getBoardHMarkDelete({
          id: this.roomId
        })
      }
      this.getBoardDetail()
      this.BoardSpecialRight.$refs.specialTable.$refs.specialTableRight.$refs.specialTableTab.getTab()
    }
  }
}
</script>

<style scoped lang="scss">
.detail-container {
  width: 100%;
  min-height: 300px;
  max-height: 700px;
  height: 400px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: auto;
  transition: all 300ms ease-in-out;
  .drag-eagle {
    width: 100%;
    height: 2px;
    cursor: n-resize;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2001;
  }
  .right-icon {
    position: relative;
    z-index: 1;
    .header-icon {
      cursor: pointer;
    }
  }
  .detail-opacity {
    position: fixed;
    left: 0;
    top: 56px;
    bottom: 0;
    right: 0;
  }
  .detail-wrapper {
    position: absolute;
    left: 0;
    top: 40px;
    bottom: 0;
    right: 0;
  }
  .detail-footer {
    display: flex;
    height: 56px;
    border-top: 1px solid;
    @include border_color(--border-color-lighter);
    justify-content: space-between;
    padding: 0 24px;
    .footer-left,
    .footer-right {
      display: flex;
      align-items: center;
    }
  }
}
::v-deep {
  .el-drawer__wrapper {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    top: auto;
    .el-drawer {
      height: 100% !important;
    }
    .el-drawer__header {
      height: 40px;
      padding: 0 8px 0 24px;
      border: 1px solid;
      background: rgba(255, 255, 255, 0.9);
      color: rgba(0, 0, 0, 0.6);

      .el-tabs__header {
        padding: 0;
        margin: 0;

        .el-tabs__nav-wrap::after {
          display: none;
        }

        .el-tabs__active-bar {
          display: none;
        }

        .el-tabs__item {
          padding: 0 16px;
          color: rgba(0, 0, 0, 0.6);

          &.is-active {
            color: #ed7b2f;
            position: relative;

            &::after {
              display: block;
              content: '';
              width: 100%;
              height: 2px;
              background: #ed7b2f;
              position: absolute;
              left: 0;
              bottom: 1px;
            }
          }
        }
      }

      .el-drawer__close-btn {
        display: none;
      }
    }
    .el-drawer__body {
      border: 1px solid #e7e7e7;
      border-top: 0;
      border-radius: 0 0 3px 3px;
    }
  }
  .el-scrollbar {
    height: calc(100% - 56px);
    .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    .is-horizontal {
      display: none;
    }
  }
  .el-breadcrumb {
    .el-breadcrumb__item {
      .el-breadcrumb__separator {
        width: 16px;
        display: inline-block;
        color: rgba(0, 0, 0, 0.4);
        text-align: center;
      }
      .el-breadcrumb__inner {
        color: rgba(0, 0, 0, 0.4);
      }
      &:last-child .el-breadcrumb__inner,
      &:last-child .el-breadcrumb__inner a,
      &:last-child .el-breadcrumb__inner a:hover,
      &:last-child .el-breadcrumb__inner:hover {
        color: rgba(0, 0, 0, 0.4);
      }
      // prettier-ignore
      &:last-child .el-breadcrumb__separator,
      .el-menu--collapse > .el-menu-item .el-submenu__icon-arrow,
      .el-menu--collapse
      > .el-submenu
      > .el-submenu__title
      .el-submenu__icon-arrow {
        display: none;
      }
    }
  }
  .el-select {
    .el-input__inner {
      width: 100%;
      height: 32px;
      line-height: 32px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 14px;
      letter-spacing: 1px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .is-focus .el-input__inner {
      box-shadow: 0 0 2px #ed7b2f;
    }
    .el-input__suffix {
      .el-select__caret {
        color: #dcdcdc;
        width: 16px;
        line-height: 32px;
        &.is-reverse {
          color: #ed7b2f;
        }
      }
    }
    .el-select-dropdown {
      padding: 8px 4px 8px 8px;
      left: 0 !important;
      box-shadow: 0 2px 4px -1px rgb(0 0 0 / 0.12),
        0 4px 5px 0 rgb(0 0 0 / 0.08), 0 1px 10px 0 rgb(0 0 0 / 0.05);
      margin-top: 8px;
      .popper__arrow {
        display: none;
      }
      .el-select-dropdown__wrap {
        max-height: 225px;
        margin-bottom: 0 !important;
        .el-select-dropdown__list {
          padding: 0 4px 0 0;
          .el-select-dropdown__item {
            height: 40px;
            line-height: 40px;
            margin-bottom: 4px;
            color: rgba(0, 0, 0, 0.9);
            padding: 0 0 0 8px;
            border-radius: 3px;
            letter-spacing: 1px;
            &.hover,
            &:hover {
              background: #e9f0ff;
            }
            &.selected {
              background: #e9f0ff;
              color: #0052d9;
              font-weight: normal;
            }
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
}
</style>
