// 条件筛选：房源状态 0-空置房源 1-锁定房源 2-已用房源（已租已售）
const fillBackground = ['#D5E2FF', '#F9E0C7', '#E8F8F2']
const fillColor = ['#ed7b2f', '#ED7B2F', '#00A870']
// 条件筛选：房源健康
const healthBackground = ['#00A870', '#E34D59', '#ed7b2f']
// 房源信息房源状态
const houseBackground = ['#E9F0FF', '#FEF3E6', '#E8F8F2']
const houseStatusColor = ['#ed7b2f', '#ED7B2F', '#00A870']
// 房源信息房源健康
const problemBackground = ['#E8F8F2', '#E9F0FF', '#F8B9BE']
const problemColor = ['#00A870', '#ed7b2f', '#C9353F']
// 房源历史时间线 0-房源空置 1-房源锁定 2-房屋已租 3-房源已售 100-健康房源 101-修缮中 102-诉讼中 200-未测绘 201-已测绘
const historyColors = [
  {
    status: [1, 200],
    color: '#ED7B2F'
  },
  {
    status: [2, 3, 100, 201],
    color: '#00A870'
  },
  {
    status: [102],
    color: '#E34D59'
  },
  {
    status: [0, 101],
    color: '#054CE8'
  }
]
// 操作记录时间线 0-房源锁定 1-房源预定 2-标记为意向房源（人工） 3-意向房源解除（自动） 4-意向房源解除（合同解除）
// 5-提前终止 6-合同到期 7-合同作废 8-合同续签 100-标记为健康房源 101-标记为修缮中 102-标记为诉讼中 103-标记为诉讼结束 200-房屋测绘 201-面积修改
const operateColors = [
  {
    status: [2, 3, 101, 201],
    color: '#054CE8'
  },
  {
    status: [0, 1, 103],
    color: '#ED7B2F'
  },
  {
    status: [8, 100, 200],
    color: '#00A870'
  },
  {
    status: [4, 5, 6, 7, 102],
    color: '#E34D59'
  }
]
// 房源状态柱状图配色 0-锁定房源 1-已用房源 2-空置房源
const houseStatusPie = [
  {
    status: [0],
    color: '#F9E0C7'
  },
  {
    status: [1],
    color: '#BCEBDC'
  },
  {
    status: [2],
    color: '#BED2FE'
  }
]
// 房源统计柱状图配色 0-已用天数 1-空置天数 2-锁定天数
const statisticsPie = [
  {
    status: [0],
    color: '#BCEBDC'
  },
  {
    status: [1],
    color: '#BED2FE'
  },
  {
    status: [2],
    color: '#F9E0C7'
  }
]
// 房源统计柱状图
export const getHouseStatusPieColor = status => {
  return houseStatusPie.find(item => item.status.includes(status)).color
}
// 房源状态柱状图
export const getStatisticsPieColor = status => {
  return statisticsPie.find(item => item.status.includes(status)).color
}
// 历史操作
export const getHistoryColor = status => {
  return historyColors.find(item => item.status.includes(status)).color
}
// 操作记录
export const getOperateColor = status => {
  return operateColors.find(item => item.status.includes(status)).color
}
// 条件筛选获取房源健康颜色
export const getHealthBackground = type => {
  return healthBackground[type]
}
// 房源状态背景
export const getStatusBackground = type => {
  return fillBackground[type]
}
// 条件筛选获取房源选中颜色
export const getStatusColor = type => {
  return fillColor[type]
}
// 房源信息获取房源状态背景
export const getHouseBackground = type => {
  return houseBackground[type]
}
// 房源信息获取房源状态选中颜色
export const getHouseStatusColor = type => {
  return houseStatusColor[type]
}
// 房源信息获取房源健康背景
export const getProblemBackground = type => {
  return problemBackground[type]
}
// 房源信息获取房源健康颜色
export const getProblemColor = type => {
  return problemColor[type]
}
