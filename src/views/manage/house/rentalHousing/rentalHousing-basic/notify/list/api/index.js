import request from '@/utils/request'

// 获取消息列表
export function getNoticeList(params) {
  return request({
    url: `/housing/apartment/notice/info/page`,
    method: 'get',
    params
  })
}

// 获取消息通知排序接口
export function getNoticeAll(params) {
  return request({
    url: `/housing/apartment/notice/info/list`,
    method: 'get',
    params
  })
}

// 获取消息通知排序接口
export function getNoticeSort(data) {
  return request({
    url: `/housing/apartment/notice/info/sort`,
    method: 'post',
    data,
    isFormData: true
  })
}

// 新增消息
export function createNotice(data) {
  return request({
    url: `/housing/apartment/notice/info/create`,
    method: 'post',
    data
  })
}

// 获取消息详情
export function getInNoticeDetails(params) {
  return request({
    url: `/housing/apartment/notice/info/get`,
    method: 'get',
    params
  })
}

// 更新消息
export function updateNotice(data) {
  return request({
    url: `/housing/apartment/notice/info/update`,
    method: 'put',
    data
  })
}

//删除消息
export function deleteNotice(id) {
  return request({
    url: `/housing/apartment/notice/info/delete?id=${id}`,
    method: 'delete'
  })
}

// 修改发布状态
export function changePublish(id) {
  return request({
    url: `/housing/apartment/notice/info/publish?id=${id}`,
    method: 'get'
  })
}
// 消息类型新增
export function createNoticeType(data) {
  return request({
    url: `/housing/apartment/notice/type/create`,
    method: 'post',
    data
  })
}
// 发布-撤回
export function changeStatus(id) {
  return request({
    url: `/hatch/information/pac/${id}`,
    method: 'get'
  })
}
// 消息列表不分页
export function getNoticeListAll() {
  return request({
    url: `/housing/apartment/notice/type/list_all`,
    method: 'get'
  })
}
