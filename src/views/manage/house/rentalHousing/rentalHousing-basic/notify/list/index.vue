<template>
  <div class="min-h100 flex">
    <basic-card>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :isSortable="isSortable"
        :api-fn="getNoticeList"
        :sort-fn="getNoticeSort"
        :search-querys-hook="searchQueryHook"
      >
        <template v-slot:operate-right>
          <el-button
            v-permission="routeButtonsPermission.MESSAGE_TYPE"
            type="info"
            @click="toTypeHandler"
            >{{ routeButtonsTitle.MESSAGE_TYPE }}</el-button
          >
          <el-button
            type="info"
            v-permission="routeButtonsPermission.SORT"
            @click="sortableHandle(!isSortable)"
            :disabled="dragTable.length === 0"
            size="small"
          >
            <span>
              {{ isSortable ? `退出排序` : `排序管理` }}
            </span>
          </el-button>
          <el-button
            type="primary"
            size="small"
            v-permission="routeButtonsPermission.ADD"
            @click="addHander"
          >
            <span>{{ routeButtonsTitle.ADD }}</span>
          </el-button>
        </template>
      </drive-table>
    </basic-card>

    <!-- 新增编辑弹框 -->
    <basic-drawer
      :title="drawerTitle"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </basic-drawer>

    <!-- 资讯详情 -->
    <dialog-cmp
      title="消息通知详情"
      :visible.sync="visible"
      width="920px"
      :haveOperation="false"
    >
      <div>
        <preview :informationData="informationData" />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
import descriptorMixins from './descriptor'
import preview from './preview'
import {
  getNoticeList,
  createNotice,
  getInNoticeDetails,
  updateNotice,
  deleteNotice,
  changePublish,
  changeStatus,
  getNoticeListAll,
  getNoticeSort,
  getNoticeAll
} from './api'

export default {
  name: 'Notify',
  mixins: [ColumnMixins, descriptorMixins],
  components: {
    preview
  },
  data() {
    return {
      getNoticeList,
      getNoticeSort,
      listLength: 0, // 列表长度
      isSortable: false, // 是否开启拖拽排序
      drawerVisible: false, // 新增编辑弹框
      drawerTitle: '新增消息通知', // 新增编辑弹框名称
      fromModel: {
        status: 1
      }, // 新增编辑表单数据
      visible: false, // 预览资讯详情
      informationData: {}, // 资讯详情数据
      dragTable: [] // 拖拽排序数据
    }
  },
  methods: {
    toTypeHandler() {
      this.$router.push({
        path: 'notify/notifyType'
      })
    },
    getNoticeAll() {
      getNoticeAll().then(res => {
        this.dragTable = res
      })
    },
    getInformationType() {
      this.getNoticeListAll()
    },
    // 拖拽排序开启关闭
    sortableHandle(val) {
      this.isSortable = val
      this.listLength = this.dragTable.length
      if (val)
        this.$refs['drive-table'] &&
          this.$refs['drive-table'].initDrag(this.dragTable)
    },
    // 下移
    moveDown(index, row) {
      this.$refs['drive-table'] &&
        this.$refs['drive-table'].moveDown(index, row)
    },
    // 上移
    moveUp(index, row) {
      this.$refs['drive-table'] && this.$refs['drive-table'].moveUp(index, row)
    },
    // 置顶
    moveTop(index, row) {
      this.$refs['drive-table'] && this.$refs['drive-table'].moveTop(index, row)
    },
    // 置底
    moveBottom(index, row) {
      this.$refs['drive-table'] &&
        this.$refs['drive-table'].moveBottom(index, row)
    },
    addHander() {
      this.drawerVisible = true
      this.drawerTitle = '新增消息通知'
      this.fromModel = this.$options.data().fromModel
    },
    // 重置搜索参数
    searchQueryHook(e) {
      let [beginCreateTime = '', endCreateTime = ''] = e.createTime || []
      if (beginCreateTime && endCreateTime) {
        beginCreateTime = beginCreateTime + ' ' + '00:00:00'
        endCreateTime = endCreateTime + ' ' + '23:59:59'
      }
      delete e.createTime
      return {
        ...e,
        beginCreateTime,
        endCreateTime
      }
    },

    // 新增编辑资讯提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { id, attachIds, status } = this.fromModel
          const params = {
            ...this.fromModel,
            status: status === 1
          }
          if (attachIds && attachIds.length > 0) {
            params.attachIds = attachIds.map(item => item.id)
          }

          if (!id) {
            // 新增资讯
            createNotice(params).then(() => {
              this.operationSuccess('新增')
            })
          } else {
            updateNotice(params).then(() => {
              this.operationSuccess('编辑')
            })
          }
        }
      })
    },

    // 编辑政策详情
    editInformation(row) {
      getInNoticeDetails({ id: row.id, type: 2 }).then(res => {
        this.drawerTitle = '编辑消息通知'
        const { attachMap = {} } = res
        this.fromModel = {
          ...res,
          attachIds: attachMap.informationAttach,
          status: res.status ? 1 : 2
        }
        this.drawerVisible = true
      })
    },

    // 置顶切换
    changePublish(row) {
      const { id, status } = row
      const tips = status ? '取消发布' : '发布'
      this.$confirm(`确定${tips}该消息通知？`).then(() => {
        changePublish(id).then(() => {
          this.$toast.success(`${tips}消息通知成功`)
          this.$refs['drive-table'].refreshTable()
        })
      })
    },

    // 下架上架状态
    changeStatus(row) {
      const { id, status } = row
      const tips = status === 1 ? '上架' : '下架'
      this.$confirm(`确定${tips}该消息通知？`).then(() => {
        changeStatus(id).then(() => {
          this.$toast.success(`${tips}消息通知成功`)
          this.$refs['drive-table'].refreshTable()
        })
      })
    },

    // 新增提示信息
    operationSuccess(tips) {
      this.$toast.success(tips + '成功')
      this.getNoticeAll()
      this.$refs['drive-table'].refreshTable()
      this.drawerVisible = false
    },

    // 删除公告
    deleteNotice(row) {
      this.$confirm('确定删除该消息通知？').then(() => {
        const { id } = row
        deleteNotice(id).then(() => {
          this.$toast.success('删除成功')
          this.getNoticeAll()
          this.$refs['drive-table'].refreshTable()
        })
      })
    },

    // 查看
    previewEvent(row) {
      getInNoticeDetails({ id: row.id, type: 1 }).then(res => {
        this.informationData = res
        this.visible = true
      })
    },

    // 获取资讯类型
    getNoticeListAll() {
      getNoticeListAll().then(res => {
        const levelList = res.map(item => {
          return { label: item.value, value: item.key }
        })
        this.tableColumn[1].search.options = levelList
        this.formConfigure.descriptors.titleTypeId.options = levelList
      })
    }
  },
  activated() {
    this.getNoticeListAll()
    this.getNoticeAll()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  }
}
</script>

<style lang="scss" scoped>
.qr-code {
  width: 200px;
  height: 200px;
  margin: 0 auto;
}
.code-content {
  margin-top: 8px;
  margin-bottom: 36px;
  text-align: center;
}
.copy-link {
  height: 32px;
  margin: 0 auto;
  padding: 0 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 1px solid #dcdcdc;
}
::v-deep {
  .el-dialog__title {
    font-size: 16px;
    font-weight: 400;
  }
  .el-dialog .el-dialog__header {
    border-bottom-width: 0;
    margin-bottom: 24px;
  }
}
</style>
