<template>
  <div class="min-h100 flex">
    <basic-card>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getOccupantPage"
      >
        <template v-slot:operate-right>
          <el-button
            type="primary"
            size="small"
            v-permission="routeButtonsPermission.LEADING_OUT"
            @click="exportClick"
          >
            <span>{{ routeButtonsTitle.LEADING_OUT }}</span>
          </el-button>
        </template>
      </drive-table>
    </basic-card>
  </div>
</template>

<script>
import ColumnMixins from './column'
import {
  getOccupantEnterprise,
  getOccupantPage,
  getOccupantParks,
  getOccupantRooms,
  getOccupantRoomType,
  occupantExport
} from './api'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'
import dayjs from 'dayjs'

export default {
  name: 'Cohabitant',
  mixins: [ColumnMixins],
  data() {
    return {
      getOccupantPage,
      data() {
        return {
          enterpriseList: []
        }
      }
    }
  },
  methods: {
    exportClick() {
      let url = occupantExport() + '?'
      url += formatGetParams(this.$refs['drive-table'].querys)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '入住人库.xls')
    },
    getOccupantRoomType() {
      getOccupantRoomType().then(res => {
        this.tableColumn[2].search.options = res || []
      })
    },
    getOccupantRooms() {
      getOccupantRooms().then(res => {
        this.tableColumn[1].search.options = res || []
      })
    },
    getOccupantParks() {
      getOccupantParks().then(res => {
        this.tableColumn[0].search.options = res || []
      })
    },
    getOccupantEnterprise() {
      getOccupantEnterprise().then(res => {
        const list = res || []
        this.enterpriseList = list.map(item => {
          return {
            value: item.label,
            id: item.value
          }
        })
      })
    },
    querySearch(queryString, cb) {
      const restaurants = this.enterpriseList
      const results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        )
      }
    },
    goEnterpriseHandle(row) {
      let { entId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId
        }
      })
    },
    handleView(row) {
      console.log('handleView-----',row)
      this.$router.push({
        path: '/rentalHousing/cohabitantDetail',
        query: {
          id: row.id
        }
      })
    }
  },
  activated() {
    this.getOccupantEnterprise()
    this.getOccupantParks()
    this.getOccupantRooms()
    this.getOccupantRoomType()
    this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
  }
}
</script>

<style lang="scss" scoped></style>
