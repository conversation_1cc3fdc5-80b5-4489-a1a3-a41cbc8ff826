<template>
  <div class="min-h100 flex">
    <basic-card>
      <drive-table
        ref="drive-table"
        :isSortable="isSortable"
        :columns="tableColumn"
        :api-fn="getINoticeType"
        :sort-fn="getINoticeTypeSort"
        :search-querys-hook="searchQueryHook"
      >
        <template v-slot:operate-right>
          <el-button
            type="info"
            size="small"
            @click="sortableHandle(!isSortable)"
          >
            <span>{{ isSortable ? `退出排序` : `排序管理` }}</span>
          </el-button>
          <el-button type="primary" size="small" @click="addHander">
            <span>新增类型</span>
          </el-button>
        </template>
      </drive-table>
    </basic-card>

    <!-- 新增编辑弹框 -->
    <dialog-cmp
      :title="drawerTitle"
      width="30%"
      :visible.sync="drawerVisible"
      @confirmDialog="confirmDrawer"
    >
      <div v-if="drawerVisible">
        <el-form
          ref="driven-form"
          :model="fromModel"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="消息类型" prop="nameList">
            <el-input
              v-model="fromModel.nameList"
              class="my-input"
              placeholder="请输入消息类型"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
import descriptorMixins from './descriptor'
import {
  getINoticeType,
  createNoticeType,
  updateNoticeType,
  deleteNoticeType,
  getINoticeTypelist,
  getINoticeTypeSort
} from './api'

export default {
  name: 'NotifyType',
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      getINoticeTypeSort,
      listLength: 0,
      tableList: [],
      isSortable: false,
      getINoticeType,
      drawerVisible: false, // 新增编辑弹框
      drawerTitle: '新增消息类型', // 新增编辑弹框名称
      fromModel: {}, // 新增编辑表单数据
      visible: false, // 预览资讯详情
      informationData: {}, // 资讯详情数据
      rules: {
        nameList: [
          { required: true, message: '请输入消息类型', trigger: 'blur' },
          {
            max: 10,
            message: '最多输入10个字符'
          }
        ]
      }
    }
  },
  mounted() {
    this.getINoticeTypelist()
  },
  methods: {
    getINoticeTypelist() {
      getINoticeTypelist().then(res => {
        this.tableList = res
      })
    },
    // 上移
    moveUp(index, row) {
      this.$refs['drive-table'] && this.$refs['drive-table'].moveUp(index, row)
    },
    // 置顶
    moveTop(index, row) {
      this.$refs['drive-table'] && this.$refs['drive-table'].moveTop(index, row)
    },
    // 置底
    moveBottom(index, row) {
      this.$refs['drive-table'] &&
        this.$refs['drive-table'].moveBottom(index, row)
    },
    // 下移
    moveDown(index, row) {
      this.$refs['drive-table'] &&
        this.$refs['drive-table'].moveDown(index, row)
    },
    // 拖拽排序开启关闭
    sortableHandle(val) {
      this.isSortable = val
      this.listLength = this.tableList.length
      if (val)
        this.$refs['drive-table'] &&
          this.$refs['drive-table'].initDrag(this.tableList)
    },
    addHander() {
      this.fromModel = {}
      this.drawerVisible = true
      this.drawerTitle = '新增消息类型'
    },
    // 重置搜索参数
    searchQueryHook(e) {
      let [beginCreateTime = '', endCreateTime = ''] = e.createTime || []
      if (beginCreateTime && endCreateTime) {
        beginCreateTime = beginCreateTime + ' ' + '00:00:00'
        endCreateTime = endCreateTime + ' ' + '23:59:59'
      }
      delete e.createTime
      return {
        ...e,
        beginCreateTime,
        endCreateTime
      }
    },

    // 新增编辑资讯提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          console.log(this.fromModel)
          const { id } = this.fromModel
          const nameList = this.fromModel.nameList.split('\n')
          if (!id) {
            // 新增资讯
            createNoticeType({ nameList }).then(() => {
              this.operationSuccess('新增')
            })
          } else {
            updateNoticeType({ nameList, id }).then(() => {
              this.operationSuccess('编辑')
            })
          }
        }
      })
    },

    // 新增提示信息
    operationSuccess(tips) {
      this.$toast.success(tips + '消息类型成功')
      this.getINoticeTypelist()
      this.drawerVisible = false
      this.$refs['drive-table'].refreshTable()
    },
    editInformation(row) {
      this.drawerVisible = true
      this.drawerTitle = '编辑消息类型'
      this.fromModel = {
        id: row.id,
        nameList: row.name
      }
    },

    // 删除公告
    deleteInformation(row) {
      this.$confirm('确定删除该消息类型？').then(() => {
        const { id } = row
        deleteNoticeType(id).then(() => {
          this.$toast.success('删除消息类型成功')
          this.getINoticeTypelist()
          this.$refs['drive-table'].refreshTable()
        })
      })
    }
  },
  activated() {
    this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
  }
}
</script>

<style lang="scss" scoped></style>
