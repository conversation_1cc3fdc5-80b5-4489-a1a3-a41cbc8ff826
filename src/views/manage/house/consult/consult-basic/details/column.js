export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'stockholder',
          label: '股东名称'
        },
        {
          prop: 'stockRatio',
          label: '持股比例'
        }
      ],
      energyTableColumn: [
        {
          prop: 'quarter',
          label: '季度'
        },
        {
          prop: 'turnover',
          label: '营业额(万元)'
        },
        {
          prop: 'taxYield',
          label: '税收总额(万元)'
        },
        {
          prop: 'netProfit',
          label: '净利润(万元)'
        },
        {
          prop: 'researchInput',
          label: '研发投入(万元)'
        }
      ],
      moreTableColumn: [
        {
          prop: 'materialsType',
          label: '材料类型'
        },
        {
          prop: 'accessoryNums',
          label: '附件数量'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 60,
          render: () => {
            return <div>查看附件</div>
          }
        }
      ]
    }
  }
}
