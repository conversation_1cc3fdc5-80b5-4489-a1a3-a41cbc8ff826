import { filterDate } from '@/filter'
import { parseTime } from '@/utils/tools'
import CompanyInfo from '../companyInfo.vue'
import { getPlanType } from '@/views/manage/house/contract/contract-special/utils/status'
export default {
  components: {
    CompanyInfo
  },
  data() {
    return {
      standardTableColumn: [
        {
          prop: 'enterpriseName',
          label: '企业名称',
          minWidth: 200
        },
        {
          prop: 'nolyRegisteredStr',
          label: '入驻类型'
        },
        {
          prop: 'parkName',
          label: '入驻园区',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'expectArea',
          label: '需求面积',
          render: (h, scope) => {
            return <div>{(scope.row.expectArea || 0) + 'm²左右'}</div>
          }
        },
        {
          prop: 'showRecords',
          label: '路演记录',
          render: (h, scope) => {
            return (
              <div>
                <svg-icon
                  icon-class="check-circle-filled"
                  className="icon-size"
                  class={
                    scope.row.showRecords
                      ? 'color-success'
                      : 'color-text-secondary'
                  }
                />
              </div>
            )
          }
        },
        {
          prop: 'viewingRecords',
          label: '看房记录',
          render: (h, scope) => {
            return (
              <div>
                <svg-icon
                  icon-class="check-circle-filled"
                  className="icon-size color-success"
                  class={
                    scope.row.showRecords
                      ? 'color-success'
                      : 'color-text-secondary'
                  }
                />
              </div>
            )
          }
        },
        {
          prop: 'progress',
          label: '办理进度',
          minWidth: 150,
          render: (h, scope) => {
            return (
              <div>
                <el-progress
                  text-inside={true}
                  stroke-width={16}
                  text-color={'#000000'}
                  percentage={Number(scope.row.progress)}
                  status="success"
                ></el-progress>
              </div>
            )
          }
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                <el-button
                  type="text"
                  size="mini"
                  onClick={() => {
                    this.chooseBody(row)
                  }}
                >
                  选择
                </el-button>
              </div>
            )
          }
        }
      ],
      fastTableColumn: [
        {
          prop: 'enterpriseName',
          label: '企业名称',
          showOverflowTooltip: true
        },
        {
          prop: 'creditCode',
          label: '统一社会信用代码',
          showOverflowTooltip: true
        },
        {
          prop: 'industryStr',
          label: '所属行业',
          showOverflowTooltip: true
        },
        {
          prop: 'createTime',
          label: '添加时间'
        },
        {
          prop: 'createUser',
          label: '添加人'
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                <el-tooltip
                  disabled={!row.enterFlag || !row.noticeMsg}
                  content={row.noticeMsg}
                  placement="top"
                >
                  <el-button
                    type="text"
                    disabled={row.enterFlag}
                    size="mini"
                    onClick={() => {
                      this.chooseBody(row)
                    }}
                  >
                    选择
                  </el-button>
                </el-tooltip>
              </div>
            )
          }
        }
      ],
      tableColumnRentalHous: [
        {
          label: '申请人',
          prop: 'applyName'
        },
        {
          label: '联系方式',
          prop: 'applyPhone'
        },
        {
          label: '申请主体',
          prop: 'applyTypeStr'
        },
        {
          label: '所在企业',
          prop: 'entName'
        },
        {
          label: '申请时间',
          prop: 'applyTime'
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                <el-tooltip
                  disabled={!row.enterFlag || !row.noticeMsg}
                  content={row.noticeMsg}
                  placement="top"
                >
                  <el-button
                    type="text"
                    disabled={row.enterFlag}
                    size="mini"
                    onClick={() => {
                      this.chooseBody(row)
                    }}
                  >
                    选择
                  </el-button>
                </el-tooltip>
              </div>
            )
          }
        }
      ],
      tableColumnInd: [
        {
          label: '姓名',
          prop: 'contact'
        },
        {
          label: '联系方式',
          prop: 'phone'
        },
        {
          label: '账号类型',
          prop: 'userTypeStr'
        },
        {
          label: '所在企业',
          prop: 'source'
        },
        {
          label: '历史合同',
          prop: 'ctCount'
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                <el-tooltip
                  disabled={!row.enterFlag || !row.noticeMsg}
                  content={row.noticeMsg}
                  placement="top"
                >
                  <el-button
                    type="text"
                    disabled={row.enterFlag}
                    size="mini"
                    onClick={() => {
                      this.chooseBody(row)
                    }}
                  >
                    选择
                  </el-button>
                </el-tooltip>
              </div>
            )
          }
        }
      ],
      tableColumnEnt: [
        // {
        //   label: '母子公司',
        //   width: 100,
        //   prop: 'enterpriseName',
        //   type: 'expand',
        //   render: (h, scope) => {
        //     const parentList = scope.row.parentList || []
        //     const subsidiaries = scope.row.subsidiaries || []
        //     return (
        //       <div>
        //         <CompanyInfo
        //           ref="companyInfo"
        //           parent={parentList}
        //           subsidiaries={subsidiaries}
        //           onChoose={this.chooseBody}
        //         />
        //       </div>
        //     )
        //   }
        // },
        {
          label: '企业名称',
          prop: 'enterpriseName',
          showOverflowTooltip: true
        },
        {
          label: '统一社会信用代码',
          prop: 'creditCode',
          width: 200,
          showOverflowTooltip: true
        },
        {
          label: '企业联系人',
          prop: 'contact'
        },
        {
          label: '联系方式',
          prop: 'phone',
          width: 120
        },
        {
          label: '企业来源',
          prop: 'source'
        },
        {
          label: '历史合同',
          prop: 'ctCount'
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                <el-tooltip
                  disabled={!row.enterFlag || !row.noticeMsg}
                  content={row.noticeMsg}
                  placement="top"
                >
                  <el-button
                    type="text"
                    disabled={row.enterFlag}
                    size="mini"
                    onClick={() => {
                      this.chooseBody(row)
                    }}
                  >
                    选择
                  </el-button>
                </el-tooltip>
              </div>
            )
          }
        }
      ],
      tableColumnHouse: [
        {
          label: '楼栋',
          prop: 'building',
          render: (h, scope) => {
            const building = scope.row.building
            const unit = building[building.length - 1] === '栋' ? '' : '栋'
            return (
              <div>{scope.row.building + unit + scope.row.floor + '层'}</div>
            )
          }
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          renderHeader: () => {
            return (
              <div class="flex align-items-center justify-content-center">
                <span class="m-r-4">房源面积(m²)</span>
                <el-tooltip
                  class="item"
                  placement="top"
                  effect="dark"
                  content="房源创建时录入的面积，合同登记时不可修改"
                >
                  <i class="el-icon-info"></i>
                </el-tooltip>
              </div>
            )
          },
          prop: 'area',
          headerAlign: 'center',
          align: 'center'
        },
        {
          label: '是否已测绘',
          prop: 'hasSurveying',
          headerAlign: 'center',
          align: 'center',
          render: (h, scope) => {
            return <div>{scope.row.hasSurveying ? '是' : '否'}</div>
          }
        },
        {
          renderHeader: () => {
            return (
              <div class="flex align-items-center justify-content-center">
                <span class="m-r-4">合同面积(m²)</span>
                {!this.transferRoom && (
                  <el-tooltip
                    placement="top"
                    effect="dark"
                    content="默认等同于房源面积，可根据实际情况修正仅对此份合同生效"
                  >
                    <i class="el-icon-info"></i>
                  </el-tooltip>
                )}
              </div>
            )
          },
          prop: 'executeArea',
          headerAlign: 'center',
          render: (h, scope) => {
            return (
              <div class="h100 w100 flex align-items-center justify-content-center">
                <div class="flex align-items-center">
                  <div class="font-size-14 line-height-22 m-r-4">
                    {scope.row.executeArea}
                  </div>
                  {this.transferRoom ? (
                    <span style={'color:#bdbdbd;cursor:no-drop'}>修正</span>
                  ) : (
                    <el-button
                      className="m-l-10"
                      type="text"
                      onclick={() => {
                        this.editHouseArea(scope.row, scope.$index)
                      }}
                    >
                      修正
                    </el-button>
                  )}
                </div>
              </div>
            )
          }
        },
        // {
        //   prop:'price',
        //   label: '租赁单价(元/m²/月)',
        //   render:(h,scope) => {
        //     return <div class="color-warning">{scope.row.price}</div>
        //   }
        // },
        {
          prop: 'operation',
          label: '操作',
          width: 148,
          headerAlign: 'center',
          align: 'center',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div>
                {this.$route.query.type ? (
                  this.$route.query.applyId ? (
                    <el-checkbox
                      disabled
                      v-model={scope.row.transferRoom}
                      onChange={() => {
                        this.transferRoomChange()
                      }}
                    >
                      退租
                    </el-checkbox>
                  ) : (
                    <span style={'color:#bdbdbd;cursor:no-drop'}>移出</span>
                  )
                ) : (
                  <el-link
                    type="primary"
                    onclick={() => {
                      this.deleteHouse(scope.row, scope.$index)
                    }}
                  >
                    移出
                  </el-link>
                )}
              </div>
            )
          }
        }
      ],
      tableColumnPlan: [
        {
          label: '期数',
          prop: 'periodNumber'
        },
        {
          label: '费用类型',
          prop: 'planTypeStr'
        },
        {
          label: '账单应缴时间',
          prop: 'billPayTime'
        },
        {
          label: '账单周期',
          prop: 'periodStartTime',
          render: (h, scope) => {
            return (
              <div>
                {filterDate(scope.row.periodStartTime) +
                  (filterDate(scope.row.periodEndTime) !== null &&
                  filterDate(scope.row.periodEndTime) !== ''
                    ? '~'
                    : '') +
                  filterDate(scope.row.periodEndTime)}
              </div>
            )
          }
        },
        {
          label: '账单金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div style={'color: #ED7B2F;'}>
                {this.transferRoom
                  ? (scope.row.quitAmount || 0).toFixed(2)
                  : scope.row.amount.toFixed(2)}
              </div>
            )
          }
        },
        {
          label: '出账状态',
          prop: 'status',
          render: (h, scope) => {
            return (
              <div class="color-primary">{getPlanType(scope.row.status)}</div>
            )
          }
        }
      ],
      tableColumnTotalPlan: [
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '开始时间',
          prop: 'startTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.startTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '结束时间',
          prop: 'endTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.endTime, '{y}-{m}-{d}')}</div>
          }
        }
      ]
    }
  }
}
