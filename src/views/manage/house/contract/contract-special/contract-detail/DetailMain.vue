<template>
  <div class="detail-main bg-white font-size-14 p-24 m-t-8">
    <div class="text-n m-b-18">主合同</div>
    <drive-table
      ref="drive-table"
      :table-data="tableData"
      :columns="mainTableColumn"
    />
  </div>
</template>

<script>
import CostColumn from './column/column'

export default {
  name: 'DetailMain',
  mixins: [CostColumn],
  props: {
    detailCost: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    tableData() {
      return this.detailCost.main ? [this.detailCost.main] : []
    }
  },
  methods: {
    //  合同详情
    detailHandle(row) {
      this.$router.push({
        path: '/contract/index/contractDetails',
        query: {
          id: row.id,
          orderId: row.orderId
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.detail-main {
  border-radius: 3px;
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
}
::v-deep {
  .empty-content {
    padding: 20px 0;
  }
}
</style>
