import { parseTime } from '@/utils/tools'

export default {
  data() {
    return {
      roomColumns: [
        {
          label: '楼栋',
          prop: 'building'
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '合同面积(m²)',
          prop: 'executeArea'
        },
        {
          label: '起租日',
          prop: 'addTime'
        },
        {
          label: '优惠开始日',
          prop: 'freeStartTime'
        },
        {
          label: '优惠结束日',
          prop: 'freeEndTime'
        },
        {
          label: '退租日',
          prop: 'earlyTim'
        }
      ],
      unitPriceColumns: [
        {
          label: '楼栋',
          prop: 'building'
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '费用类型',
          prop: 'feeName'
        },
        {
          label: '时间',
          prop: 'time'
        },
        {
          renderHeader: () => {
            return (
              <div class="flex align-items-center">
                <span class="m-r-4">租金标准</span>
              </div>
            )
          },
          prop: 'fixAmount',
          render: (h, { row }) => {
            return (
              <div>{row.fixAmount ? `${row.fixAmount} ${row.unit}` : '-'}</div>
            )
          }
        }
      ],
      tableColumn: [
        {
          label: '房间',
          prop: 'period'
        },
        {
          label: '开始时间',
          prop: 'startTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.startTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '结束时间',
          prop: 'endTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.endTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 150,
          render: (h, scope) => {
            return (
              <el-button
                type="text"
                onClick={() => this.handleDetail(scope.row)}
              >
                详情
              </el-button>
            )
          }
        }
      ]
    }
  }
}
