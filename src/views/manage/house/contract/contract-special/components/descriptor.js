export default {
  data() {
    return {
      formConfigureStop: {
        labelWidth: '140px',
        descriptors: {
          entName: {
            form: 'input',
            label: '企业名称',
            disabled: true,
            rule: [
              {
                type: 'string'
              }
            ]
          },
          contractNo: {
            form: 'input',
            label: '合同编号',
            disabled: true,
            rule: [
              {
                type: 'string'
              }
            ]
          },
          signDate: {
            form: 'date',
            type: 'string',
            label: '签订日期',
            required: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择签订日期'
              }
            ]
          },
          attachIds: {
            form: 'component',
            label: '已签订合同扫描件',
            rule: [
              {
                required: true,
                message: '请上传已签订合同扫描件',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <div style="position: absolute;padding-top: 42px; margin-left: calc(-100% + 110px);color: rgba(0,0,0,0.4);font-size: 12px;line-height:20px;">
                  <div>请上传文件类型为jpeg/png/pdf格式的合同扫描件</div>
                  <div>大小不得超过50M</div>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'containerSign'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 50,
              limit: 3
            }
          }
        }
      }
    }
  }
}
