import request from '@/utils/request'

// 获取房间合同
export function getRoomContractList(data) {
	return request({
		url: `/fee/type/contract/get_rooms`,
		method: 'post',
		data
	})
}
// 合同起止时间
export function getStartTime(data) {
	return request({
		url: `/contract/record/room_time`,
		method: 'post',
		data
	})
}
// 获取园区列表
export function getParkSelect(params) {
	return request({
		url: `/housing/park/select`,
		method: 'get',
		params
	})
}
export function getAdditional() {
	return request({
		url: `/contract/record/get_additional`,
		method: 'get'
	})
}
export function recordRoom(data) {
	return request({
		url: `/contract/record/get_room`,
		method: 'post',
		data
	})
}
// 合同选房-获取园区下所有房间
export function getRecordParkRoom(params) {
	return request({
		url: `/contract/room_selection/park_room`,
		method: 'get',
		params
	})
}
// 获得表头（type 1-房间单价 2-合同单价 3-房源历史 4-缴费周期）
export function priceTitles(type) {
	return request({
		url: `/bs/contract-fee-price/titles/${type}`,
		method: 'get'
	})
}
// 获取签约主体详情
export function getRecordBodyDetail(params) {
	return request({
		url: `/contract/record/select_body_by_id`,
		method: 'get',
		params
	})
}
// 获取合同模板
export function getContractTemplate(params) {
	return request({
		url: `/contract/record/get_template`,
		method: 'get',
		params
	})
}
// 生成合同编号
export function getContactNumber(params) {
	return request({
		url: `/contract/record/gen_contract_number`,
		method: 'get',
		params
	})
}
// 获取历史保证金
export function getHistoryDeposit(params) {
	return request({
		url: `/contract/refactor/record/get_history_deposit`,
		method: 'get',
		params
	})
}
// 获取优惠/免租期数
export function getFreePeriod(data) {
	return request({
		url: `/contract/refactor/record/gen_free_period`,
		method: 'post',
		data
	})
}
// 预览缴费计划
export function getFeePlanView(data) {
	return request({
		url: `/contract/refactor/record/gen_fee_plan_view`,
		method: 'post',
		data
	})
}
// 合同保存
export function contractCreate(data) {
	return request({
		url: `/contract/refactor/record/create`,
		method: 'post',
		data
	})
}
// 合同更新
export function contractUpdate(data) {
	return request({
		url: `/contract/refactor/record/update`,
		method: 'post',
		data
	})
}
// 合同提交审批
export function contractSubmit(params) {
	return request({
		url: `/contract/record/submit`,
		method: 'get',
		params
	})
}

// 获取合同详情
export function getContractDetail(params) {
	return request({
		url: `/contract/record/get_detail`,
		method: 'get',
		params
	})
}
// 获取楼栋
export function getBuild(parkId) {
	return request({
		url: `/housing/building/select/${parkId}`,
		method: 'get',
		hideLoading: true
	})
}
// 获取楼层
export function getFloorList(buildingId) {
	return request({
		url: `/housing/floor/select/${buildingId}`,
		method: 'get',
		hideLoading: true
	})
}
// 合同选房-房间筛选数据
export function getRoomSelectionList(params) {
	return request({
		url: `/contract/room_selection/list`,
		method: 'get',
		params
	})
}
// 获得业态类型
export function getBusinessTypes(type) {
	return request({
		url: `/housing/room/business_type/${type}`,
		method: 'get'
	})
}

// 合同选房-房间筛选数据
export function getRoomSelectionCount(params) {
	return request({
		url: `/contract/room_selection/count`,
		method: 'get',
		params
	})
}
// 获得房间类型
export function getHouseLeaseTypes(bsType) {
	return request({
		url: `/housing/room/lease_types?bsType=${bsType}`,
		method: 'get'
	})
}
// 获得房间用途
export function getHouseUseTypes(type, bsType) {
	return request({
		url: `/housing/room/use_types/${type}/${bsType}`,
		method: 'get'
	})
}
// 获取合同缴费计划
export function getPlan(params) {
	return request({
		url: `/contract/refactor/record/get_plan`,
		method: 'get',
		params
	})
}
// 获取合同缴费变更计划
export function getHistoryPlan(params) {
	return request({
		url: `/contract/refactor/record/get_history_plan`,
		method: 'get',
		params
	})
}
// 合同详情
export function getRecordDetail(params) {
	return request({
		url: `/contract/record/get_detail`,
		method: 'get',
		params
	})
}
// 合同签订
export function uploadContract(data) {
	return request({
		url: `/contract/record/sign`,
		method: 'post',
		data
	})
}

// 生成合同编号
export function getShowDefault(params) {
  return request({
    url: `/housing/room-manage/select?showDefault=true`,
    method: 'get',
    params
  })
}


export function getContractNoHistory(params) {
  return request({
    url: `/contract/refactor/record/contractNo_history?id=${params}`,
    method: 'get',
  })
}
