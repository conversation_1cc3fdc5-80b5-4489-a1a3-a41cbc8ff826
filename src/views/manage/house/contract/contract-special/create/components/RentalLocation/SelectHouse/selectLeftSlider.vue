<template>
  <div class="left-slider-container">
    <div class="scroll-wrapper" ref="scrollWrapper">
      <div class="scroll-content">
        <div
          class="scroll-item"
          v-for="item in list"
          :key="item.key"
          :class="{ active: selectList.includes(item.key) }"
          @click="clickHandle(item.key)"
        >
          <div class="item-text" v-tooltip="item.label">{{ item.label }}</div>
        </div>
      </div>
      <!--自定义滚动条-->
      <div class="custom-vertical-scrollbar" ref="vertical">
        <div class="custom-vertical-indicator"></div>
      </div>
    </div>
  </div>
</template>

<script>
import BScroll from 'better-scroll'
export default {
  name: 'LeftSlider',
  props: {
    // 是否全选
    isAll: {
      type: Boolean,
      default: true
    },
    // 楼层列表
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      bs: null,
      selectList: []
    }
  },
  watch: {
    isAll(val) {
      if (val) {
        this.selectList = []
      }
    },
    list: {
      handler() {
        this.$nextTick(() => {
          this.init()
        })
      },
      immediate: true,
      deep: true
    }
  },
  beforeDestroy() {
    this.bs.destroy()
    this.bs = null
  },
  methods: {
    // 选中取消
    clickHandle(row) {
      if (this.selectList.includes(row)) {
        this.selectList = this.selectList.filter(item => item !== row)
      } else {
        this.selectList.push(row)
      }
      this.$emit('selectHandle', this.selectList)
    },
    init() {
      this.bs = new BScroll(this.$refs.scrollWrapper, {
        probeType: 3,
        mouseWheel: true, // 滚轮滚动
        scrollbar: {
          customElements: [this.$refs.vertical], // 创建自定义滚动条
          fade: false // 关闭滚动条
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.left-slider-container {
  height: calc(100% - 40px);
  .scroll-wrapper {
    height: 100%;
    overflow: hidden;
    position: relative;
    .scroll-content {
      .scroll-item {
        height: 39px;
        line-height: 39px;
        text-align: center;
        cursor: pointer;
        border-bottom-width: 1px;
        border-style: solid;
        @include border_color(--border-color-light);
        &.active {
          background: #e9f0ff;
        }
        .item-text {
          padding: 0 2px;
        }
      }
    }
    .custom-vertical-scrollbar {
      width: 4px;
      height: 100%;
      border-radius: 2px;
      background: transparent;
      position: absolute;
      top: 50%;
      right: 0;
      transform: translateY(-50%) translateZ(0);
      visibility: hidden;
      .custom-vertical-indicator {
        width: 100%;
        height: 120px;
        background: rgba(0, 0, 0, 0.26);
        border-radius: 2px;
      }
    }
  }
  &:hover {
    .scroll-wrapper .custom-vertical-scrollbar {
      visibility: visible;
    }
  }
}
</style>
