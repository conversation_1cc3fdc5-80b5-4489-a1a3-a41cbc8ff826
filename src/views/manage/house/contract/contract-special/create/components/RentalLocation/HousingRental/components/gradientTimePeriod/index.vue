<template>
  <div class="pos-relative">
    <div class="add" @click="add">
      <el-link type="primary">新增</el-link>
    </div>
    <el-form ref="driven-form" :model="fromModel">
      <template v-for="(row, inx) in fromModel.gradientTimePeriod">
        <el-form-item
          :prop="'gradientTimePeriod.' + inx + '.value'"
          :key="inx"
          v-if="inx < 10"
          :rules="[
            {
              required: true,
              message: '请输入时间段/收费标准',
              trigger: ['blur', 'change']
            },
            { validator: validatorFn, trigger: ['blur', 'change'] }
          ]"
        >
          <div class="flex align-items-center">
            <el-date-picker
              v-model="row.time"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
            >
            </el-date-picker>
            <el-input class="m-l-8" placeholder="请输入" v-model="row.value">
              <template slot="append">
                <span class="color-info">{{ unit }}</span>
              </template>
            </el-input>
            <div
              class="m-l-16 pointer"
              v-if="inx > 0"
              @click="deleteHandle(inx)"
            >
              <svg-icon icon-class="delete" />
            </div>
          </div>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'GradientTimePeriod',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    unit: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fromModel: {
        gradientTimePeriod: [
          {
            value: '',
            time: []
          }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val.length > 0) {
          this.fromModel.gradientTimePeriod = val
        } else {
          this.fromModel.gradientTimePeriod = [
            {
              value: '',
              time: []
            }
          ]
        }
      },
      immediate: true,
      deep: true
    },
    fromModel: {
      handler(val) {
        this.$emit('input', val.gradientTimePeriod)
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    validatorFn(rule, value, callback) {
      const arr = rule.field.split('.')
      const idx = Number(arr[1])
      const time = this.fromModel.gradientTimePeriod[idx].time
      if (!time || !time.length) return callback(new Error('请选择时间段'))
      const reg = /^([1-9]\d{0,7}|0)(\.\d{1,2})?$/
      if (reg.test(value)) {
        callback()
      } else {
        callback(new Error('请输入正确的收费标准'))
      }
    },
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    add() {
      this.fromModel.gradientTimePeriod.push({
        value: '',
        time: []
      })
    },
    deleteHandle(index) {
      this.fromModel.gradientTimePeriod.splice(index, 1)
    }
  }
}
</script>

<style scoped lang="scss">
.add {
  position: absolute;
  right: 0;
  top: -40px;
}
</style>
