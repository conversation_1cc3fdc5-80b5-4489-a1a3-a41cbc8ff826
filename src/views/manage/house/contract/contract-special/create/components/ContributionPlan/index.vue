<template>
  <basic-card title="缴费计划" class="m-b-8">
    <div
      class="m-b-8 flex align-items-center justify-content-between font-size-14 line-height-22"
    >
      <div>
        <div class="flex">
          <div>
            共 <span class="color-warning">{{feePlans.length}}</span> 期
          </div>
          <div class="m-l-8 m-r-6">总计</div>
          <div class="color-warning">
            ¥: {{ NumFormat(totalAmount) }}
          </div>
          <div class="m-l-8 m-r-8">|</div>
          <div class="m-r-6">租金</div>
          <div class="color-warning">
            ¥: {{ NumFormat(rentAmount) }}
          </div>
          <div class="m-l-16 m-r-6">保证金</div>
          <div class="color-warning">
            ¥: {{ NumFormat(depositAmount) }}
          </div>
        </div>
      </div>
      <el-button
        class="price-btn"
        type="primary"
        @click="totalCalculation"
      >智能生成
      </el-button>
    </div>
    <drive-table
      :columns="tableColumnPlan"
      :table-data="feePlans"
      row-key="uid"
      :tree-props="{
        children: 'children',
        hasChildren: 'hasChildren'
      }"
    />
  </basic-card>
</template>

<script>
import Column from './column'
import { NumFormat } from '@/utils/tools'
import { getFeePlanView } from '../../../api'

export default {
  name: 'ContributionPlan',
  mixins: [Column],
  data() {
    return {
      NumFormat,
      feePlans: [],
      totalAmount: 0,
      depositAmount: 0,
      rentAmount: 0
    }
  },
  methods: {
    // 智能生成计算
    initData(row, cb) {
      this.getFeePlanView(row).then(() => {
        cb && cb()
      })
    },
    // 获取缴费计划
    getFeePlanView(row) {
      return new Promise((resolve, reject) => {
        getFeePlanView(row).then(res => {
          this.feePlans = res.feePlans || []
          this.totalAmount = res.totalAmount || 0
          this.depositAmount = res.depositAmount || 0
          this.rentAmount = res.rentAmount || 0
          resolve(res)
        }).catch(err => {
          reject(err)
        })
      })
    },
    totalCalculation() {
      this.$emit('totalCalculation')
    }
  }
}
</script>

<style scoped lang="scss">
</style>
