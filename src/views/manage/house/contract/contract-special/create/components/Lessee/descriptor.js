import { validateContact } from '@/utils/validate'
import Autocomplete from '@/components/Autocomplete'

export default {
  components: {
    Autocomplete
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '100px',
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '承租方',
            span: 8,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入承租方'
              }
            ]
          },
          contacts: {
            form: 'component',
            label: '联系人',
            span: 8,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人姓名'
              }
            ],
            render: () => {
              return <Autocomplete v-model={this.formModel.contacts} />
            },
            attrs: {
              maxLength: 20
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            span: 8,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              { validator: validateContact }
            ],
          }
        }
      }
    }
  }
}
