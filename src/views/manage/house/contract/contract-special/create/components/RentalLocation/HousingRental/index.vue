<template>
  <div>
    <div class="m-t-35 m-b-25">收费标准</div>
    <drive-table
      max-height="380px"
      ref="drive-table"
      :columns="tableColumn"
      border
      :span-method="objectSpanMethod"
      :table-data="pricesData"
    />

    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="620px"
      :haveOperation="false"
    >
      <div class="dialog-edit">
        <driven-form
          class="m-b-20"
          v-if="visible"
          ref="driven-form"
          v-model="fromModel"
          label-position="top"
          :formConfigure="formConfigure"
        />
        <div class="footer flex justify-content-between align-items-center">
          <el-checkbox v-model="checked">
            修正后的价格适用于此合同当前已选择的房间
          </el-checkbox>
          <div>
            <el-button @click="visible = false">取 消</el-button>
            <el-button type="primary" @click="confirmDialog">
              <span>确 定</span>
            </el-button>
          </div>
        </div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import { getAdditional, recordRoom } from '../../../../api'
import formConfigure from './descriptor'
export default {
  name: 'HouseRental',
  mixins: [formConfigure],
  props: {
    pricesData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '修正',
      checked: true,
      fromModel: {
        additional: 1
      },
      visible: false,
      tableColumn: [
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '费用类型',
          prop: 'feeName'
        },
        {
          label: '时间',
          prop: 'time'
        },
        {
          renderHeader: () => {
            return (
              <div class="flex align-items-center">
                <span class="m-r-4">租金标准</span>
                <el-tooltip
                  placement="top"
                  effect="dark"
                  content="根据房间的收费标准获取，可修改仅对此份合同生效"
                >
                  <i className="el-icon-info"></i>
                </el-tooltip>
              </div>
            )
          },
          prop: 'fixAmount',
          render: (h, { row }) => {
            return (
              <div>{row.fixAmount ? `${row.fixAmount} ${row.unit}` : '-'}</div>
            )
          }
        },
        {
          label: '操作',
          prop: 'operate',
          width: '80px',
          render: (h, { row }) => {
            return (
              <div class="operate">
                <el-button
                  type="text"
                  size="mini"
                  onClick={() => {
                    this.correct(row)
                  }}
                >
                  修正
                </el-button>
              </div>
            )
          }
        }
      ]
    }
  },
  mounted() {
    this.getAdditional()
  },
  methods: {
    getAdditional() {
      getAdditional().then(res => {
        this.formConfigure.descriptors.additional.options = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    objectSpanMethod({ row, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (
          rowIndex > 0 &&
          this.pricesData[rowIndex - 1].roomId === row.roomId
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.pricesData.length; i++) {
            if (this.pricesData[i].roomId === row.roomId) {
              rowspan++
            } else {
              break
            }
          }

          return {
            rowspan,
            colspan: 1
          }
        }
      } else if (columnIndex === 1) {
        if (
          rowIndex > 0 &&
          this.pricesData[rowIndex - 1].feeCode === row.feeCode
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.pricesData.length; i++) {
            if (this.pricesData[i].feeCode === row.feeCode) {
              rowspan++
            } else {
              break
            }
          }
          return {
            rowspan,
            colspan: 1
          }
        }
      }
    },
    confirmDialog() {
      this.$refs.feePriceCom?.validateHandle().then(() => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            const feePriceData = this.$refs.feePriceCom.fromModel
            // feePriceData.gradientTime = this.$refs.feePriceCom.gradientTime
            const data = this.pricesData
              .map(item => {
                if (this.checked) {
                  item = {
                    ...item,
                    additional: this.fromModel.additional,
                    fixAmount: this.fromModel.fixAmount,
                    reason: this.fromModel.reason,
                    json: JSON.stringify(feePriceData)
                  }
                  return item
                } else {
                  if (item.roomId === this.fromModel.roomId) {
                    item = {
                      ...item,
                      additional: this.fromModel.additional,
                      fixAmount: this.fromModel.fixAmount,
                      reason: this.fromModel.reason,
                      json: JSON.stringify(feePriceData)
                    }
                    return item
                  } else {
                    return item
                  }
                }
              })
              .filter(Boolean)
            const newData = data.filter((item, index) => {
              return (
                index ===
                data.findIndex(
                  item2 =>
                    item2.roomId === item.roomId &&
                    item2.feeCode === item.feeCode
                )
              )
            })

            recordRoom(newData).then(res => {
              this.$emit('updateData', res.prices)
              this.visible = false
            })
          }
        })
      })
    },
    correct(row) {
      const json = JSON.parse(row.json)
      this.visible = true
      if (row.feeName === '房租费'){
        this.title = '房租费（梯度单价）修正'
      }else {
        this.title = row.feeName + '修正'
      }
      const { amount, fixAmount, reason, unique, additional, roomId } = row
      this.fromModel = {
        amount,
        fixAmount,
        reason,
        additional: additional ? additional : 1,
        unique,
        json,
        roomId
      }
    }
  }
}
</script>

<style scoped lang="scss">
.footer {
  width: 100%;
  padding: 15px 20px;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  right: 0;
}
:deep(.additionalKey) {
  margin-top: -42px;
}
.dialog-edit{
  padding-bottom: 20px;
  :deep(.el-form) {
    .form-item-container{

      .custom-tips {
        display: none !important;
      }
    }

  }
}

</style>
