<template>
  <div class="rent-free-rules w-100 pos-relative m-t-15">
    <div class="head">
      <el-tooltip
        effect="dark"
        content="自定义设置在季度付款的凑整模式下，优惠月数的设定会导致部分时间无法享受优惠政策，如5月15至9月30的账期，设置为3个月优惠，则9月15至9月30的账期会按照原价计算"
        placement="top"
      >
        <div class="flex align-items-center pointer m-b-4">
          <div class="m-r-6">优惠规则</div>
          <svg-icon icon-class="help-circle" />
        </div>
      </el-tooltip>
      <div class="edit-btn">
        <el-button type="primary" size="mini" @click="visible = true">
          <svg-icon icon-class="add" />
          <span>修改</span>
        </el-button>
      </div>
    </div>

    <drive-table
      max-height="300px"
      ref="drive-table"
      :columns="columnsCom"
      :table-data="rentFreeData"
    >
      <template v-slot:empty>
        <empty-data class="m-t-30 m-b-30" description="请先录入合同期限及付款方式，再明确免租和优惠规则" />
      </template>
    </drive-table>

    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <el-form
        :model="fromModel"
        v-if="visible"
        ref="fromModel"
        label-width="60px"
        label-position="top"
      >

        <el-form-item
          v-for="(domain, index) in fromModel.domains"
          :label="index < 1? '约定' : ''"
          :key="index + 'domains'"
          :prop="'domains.' + index + '.months'"
          :rules="[
            {required: true,message: '约定不能为空', trigger: 'blur'},
          ]"
        >
          <div class="flex">
            <div class="flex">
              <el-input v-model="domain.period" @input="filterValue($event,domain,'period')" class="m-r-8">
                <template slot="prepend">
                  <span class="color-info">第</span>
                </template>
                <template slot="append">
                  <span class="color-info">期</span>
                </template>
              </el-input>
            </div>
            <div class="flex">
              <el-input v-model="domain.months" @input="filterValue($event,domain,'months')" class="m-r-8">
                <template slot="prepend">
                  <span class="color-info">{{rentFree === 1 ? '免租' : '优惠'}}</span>
                </template>
                <template slot="append">
                  <span class="color-info">个月</span>
                </template>
              </el-input>
            </div>
            <el-button v-if="index > 0" type="text"  @click.prevent="removeDomain(domain)">删除</el-button>
            <el-button v-else type="text" @click="addDomain">新增</el-button>
          </div>
        </el-form-item>
      </el-form>
    </dialog-cmp>
  </div>
</template>

<script>
import { deepClone } from '@/utils/tools'

export default {
  name: 'RentFreeRules',
  props: {
    value: {
      type: Array,
      default: () => ([])
    },
    rentFree: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      fromModel: {
        domains: [
          {
            period: '',
            months: ''
          }
        ],
      },
      columns: [
        {
          label: '期数',
          prop: 'period',
          width: 120,
          render: (h, {row}) => {
            return (
              <div>第{row.period}期</div>
            )
          }
        },
        {
          label: '免租月数',
          prop: 'months',
          render: (h, {row}) => {
            return (
              <div>{row.months}个月</div>
            )
          }
        }
      ],
      visible: false,
      rentFreeData: []
    }
  },
  computed: {
    title() {
      return this.rentFree === 1 ? '免租约定' : '优惠约定'
    },
    columnsCom() {
      return this.columns.map((col, inx) => {
        if (inx === 1) {
          col.label = this.rentFree === 1 ? '免租月数' : '优惠月数'
        }
        return col
      })
    }
  },
  watch: {
    value: {
      handler(val) {
        if(val && val.length) {
          this.fromModel.domains = deepClone(val)
          this.rentFreeData = deepClone(val)
        } else {
          this.fromModel = this.$options.data().fromModel
          this.rentFreeData = []
        }
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['ContractTerms'],
  methods: {
    filterValue(val,domain,type) {
      const reg = /[^\d]/g
      let newVal = val.replace(reg, '')

      if (newVal.length > 8) {
        newVal = newVal.slice(0, 8)
      }
      if (newVal === 0 || newVal === '0') {
        newVal = ''
      }

      this.$set(domain, type, newVal)
    },
    removeDomain(item) {
      const index = this.fromModel.domains.indexOf(item)
      if (index !== -1) {
        this.fromModel.domains.splice(index, 1)
      }
    },
    addDomain() {
      if (this.fromModel.domains.length >20) return this.$message.warning('最多添加20条约定')
      this.fromModel.domains.push({
        months: '',
        period: ''
      })
    },
    confirmDialog() {
      this.$refs.fromModel.validate(valid => {
        if (valid) {
          const data = this.fromModel.domains.map(item => Number(item.period))
          if (data.length !== new Set(data).size) return this.$message.warning('期数不能重复')
          const contractPeriod = this.ContractTerms.fromModel.contractPeriod
          const payCycle = this.ContractTerms.fromModel.payCycle
          if (!contractPeriod.length) return this.$toast.warning('请选择合同期限')
          if (!payCycle) return this.$toast.warning('请选择付款方式')
          const freeList = deepClone(this.fromModel.domains)
          this.$emit('input', freeList)
          this.visible = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.head{
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  left: 0;
  top: -38px;
  .edit-btn {
    width: 66px;
    margin-left: 20px;
  }
}

</style>
