<template>
  <basic-card title="承租方" class="m-b-8" :is-padding="false">
    <driven-form
      ref="drive-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
      :disabled="disabled"
    />
  </basic-card>
</template>

<script>
import descriptor from './descriptor'
export default {
  name: '<PERSON>ee',
  mixins: [descriptor],
  data() {
    return {
      fromModel: {},
      visible: false,
      renewType: 0, // 0非续签；1新增续签合同；2编辑续签合同
    }
  },
  computed: {
    disabled() {
      return this.renewType !== 0
    }
  },
  watch: {
    'fromModel.contacts'(val) {
      if (!val) return false
      try {
        const row = JSON.parse(val)
        console.log('row----',row)
        this.$set(this.formModel, 'contact', row.contact)
        this.$set(this.formModel, 'contactPhone', row.contactPhone)
      } catch (error) {
        console.log('error----',error)
      }
    }
  },
  inject: ['ContractCreate'],
  methods: {
    // 数据初始化
    async initData(row) {
      return new Promise(resolve => {
        this.renewType = this.ContractCreate.renewType
        this.fromModel = {
          entId: row.entId,
          enterpriseName: row.enterpriseName,
          contacts: row.contacts,
          phone: row.phone
        }
        this.$nextTick(() => {
          this.$refs['drive-form'].clearValidate()
        })
        resolve()
      })
    },
    // 校验
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['drive-form'].validate(valid => {
          if (valid) {
            resolve(this.fromModel)
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
