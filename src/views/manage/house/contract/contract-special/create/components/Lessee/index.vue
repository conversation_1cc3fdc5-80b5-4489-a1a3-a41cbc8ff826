<template>
  <basic-card title="承租方" class="m-b-8" :is-padding="false">
    <driven-form
      ref="drive-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
      :disabled="disabled"
    />
  </basic-card>
</template>

<script>
import descriptor from './descriptor'
import { getContacts } from '@/api/common'
export default {
  name: 'Lessee',
  mixins: [descriptor],
  data() {
    return {
      fromModel: {},
      visible: false,
      renewType: 0, // 0非续签；1新增续签合同；2编辑续签合同
      restaurants: []
    }
  },
  computed: {
    disabled() {
      return this.renewType !== 0
    }
  },
  inject: ['ContractCreate'],
  methods: {
    selectContact(val) {
      this.$set(this.fromModel, 'contacts', val.value )
      this.$set(this.fromModel, 'phone', val.phone )
    },
    getContacts(row) {
      getContacts(row).then(res => {
        this.restaurants = res.map(item => {
          return {
            address: item.contact,
            value: item.contact,
            phone: item.phone
          }
        })
      })
    },
    fetchContacts(queryString, cb) {
      let restaurants = this.restaurants;
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        const contact = restaurant.value || '';
        return contact.toLowerCase().indexOf(queryString.toLowerCase()) > -1;
      };
    },
    // 数据初始化
    async initData(row) {
      await this.getContacts(row.entId)
      return new Promise(resolve => {
        this.renewType = this.ContractCreate.renewType
        this.fromModel = {
          entId: row.entId,
          enterpriseName: row.enterpriseName,
          contacts: row.contacts,
          phone: row.phone
        }
        this.$nextTick(() => {
          this.$refs['drive-form'].clearValidate()
        })
        resolve()
      })
    },
    // 校验
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['drive-form'].validate(valid => {
          if (valid) {
            resolve(this.fromModel)
          } else {
            reject()
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-autocomplete) {
  width: 100%;
}
</style>
