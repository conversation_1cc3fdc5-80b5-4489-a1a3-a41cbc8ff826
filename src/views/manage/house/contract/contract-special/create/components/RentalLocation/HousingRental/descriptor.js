import feePriceCom from './feePriceCom'
export default {
  components: { feePriceCom },
  data() {
    return {
      gradientTime: this.getTime(), //获取当前年月日
      formConfigure: {
        descriptors: {
          list: {
            form: 'component',
            label: '',
            rule: [
              {
                required: true,
                type: 'array',
                validator: (rule, value, callback) => {
                  callback()
                }
              }
            ],
            render: () => {
              return (
                <div>
                  <fee-price-com
                    ref="feePriceCom"
                    v-model={this.fromModel.json}
                  />
                </div>
              )
            }
          },
          additional: {
            label: '附加项',
            form: 'radio',
            hidden: true,
            rule: [
              {
                message: '请选择附加项',
                type: 'number'
              }
            ],
            options: []
          },
          reason: {
            form: 'input',
            label: '修正说明',
            rule: [
              {
                message: '请输入',
                type: 'string'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          }
        }
      }
    }
  },
  methods: {
    getTime() {
      return '2025-01-01'
      // const currentDate = new Date()
      // const year = currentDate.getFullYear()
      // const month = currentDate.getMonth() + 1 // 月份从0开始，需要加1
      // const day = currentDate.getDate()
      // return `${year}-${month}-${day}`
    }
  }
}
