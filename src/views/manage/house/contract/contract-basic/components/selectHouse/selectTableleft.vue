<template>
  <div class="table-left-container h100">
    <div class="item-all" :class="{ active: isAll }" @click="allHandle">
      全部
    </div>
    <left-slider :is-all="isAll" @selectHandle="selectHandle" :list="floors" />
  </div>
</template>

<script>
import LeftSlider from '@/views/manage/house/contract/contract-basic/components/selectHouse/selectLeftSlider'
import { getFloorList } from '@/views/manage/house/contract/contract-basic/api/create'
// import { getBoardFloors } from '@/views/manage/house/board/board-special/api'
export default {
  name: 'SpecialTableLeft',
  components: { LeftSlider },
  data() {
    return {
      isAll: true,
      floors: [],
      floorId: []
    }
  },
  // inject: ["BoardSpecialTable"],
  methods: {
    // 获取楼层
    async getFloorList(e) {
      const res = await getFloorList(e)
      this.floors = res
    },
    // 重置
    resetHandle() {
      this.isAll = true
      this.floorId = []
    },
    // 全部
    allHandle() {
      this.$emit('getRoomListAll')
      this.resetHandle()
      // this.BoardSpecialTable.$refs.specialTableRight.getFloorId(this.floorId)
    },
    // 获取楼层
    // getBoardFloors(parkId) {
    //   this.resetHandle()
    //   getBoardFloors(parkId).then(res => {
    //     this.floors = res || []
    //   })
    // },
    // 子组件选中的楼栋
    selectHandle(list = []) {
      this.floorId = list
      this.isAll = !list.length
      this.$emit('changeLeftFloor', this.floorId)
      // this.BoardSpecialTable.$refs.specialTableRight.getFloorId(this.floorId)
    }
  }
}
</script>

<style scoped lang="scss">
.table-left-container {
  width: 60px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  flex: none;
  border-right-width: 1px;
  border-style: solid;
  @include border_color(--border-color-light);
  .item-all {
    text-align: center;
    line-height: 39px;
    cursor: pointer;
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-light);
    &.active {
      background: #e9f0ff;
    }
  }
}
</style>
