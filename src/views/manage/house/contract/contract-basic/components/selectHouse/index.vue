<template>
  <div class="h100 flex justify-content-between">
    <div class="m-r-24">
      <select-sidebar
        ref="selectSidebar"
        :park-name="parkName"
        :park-id="parkId"
        @changeHouseType="changeHouseType"
        @changeTypeAll="getBuild"
      />
    </div>
    <div style="flex: 1">
      <div class="flex justify-content-between">
        <div class="selectBox flex">
          <div class="m-r-16 font-size-14 line-height-22">楼栋切换</div>
          <el-select
            v-model="buildingId"
            @change="getRoomSelectionList"
            placeholder="请选择"
          >
            <el-option
              v-for="item in list"
              :key="item.key"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <div class="line"></div>
        </div>
        <div class="flex align-items-center">
          <div class="font-size-14 m-r-16 line-height-22">
            <span class="m-r-4 text-color">已选中</span>
            <span class="color-primary">{{ count }}</span>
            <span class="m-l-4 text-color">个</span>
          </div>
          <!--          <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>-->
        </div>
      </div>
      <div class="select-table m-t-8">
        <select-table-top
          ref="select-table-top"
          @hideRoom="hideRoom"
          :park-name="parkName"
        />
        <div class="select-table-left flex">
          <select-table-left
            ref="selectTableLeft"
            @changeLeftFloor="changeLeftFloor"
            @getRoomListAll="getRoomListAll"
          />
          <select-table-right
            ref="selectTableRight"
            :acArea="acArea"
            :props-checked="propsChecked"
            @getChekedCount="getChekedCount"
            @checkBoxFn="checkBoxFn"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SelectTableTop from '@/views/manage/house/contract/contract-basic/components/selectHouse/selectTableTop'
import SelectTableLeft from '@/views/manage/house/contract/contract-basic/components/selectHouse/selectTableleft'
import SelectTableRight from '@/views/manage/house/contract/contract-basic/components/selectHouse/selectTableRight'
import SelectSidebar from '@/views/manage/house/contract/contract-basic/components/selectHouse/selectSidebar'
import {
  getBuild,
  getRoomSelectionList
} from '@/views/manage/house/contract/contract-basic/api/create'
export default {
  name: 'selectHouse',
  components: {
    SelectSidebar,
    SelectTableTop,
    SelectTableLeft,
    SelectTableRight
  },
  props: {
    parkId: {
      type: Number
    },
    parkName: {
      type: String
    },
    contractList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      buildingId: '',
      building: '',
      list: [],
      checkAll: '',
      isIndeterminate: false,
      count: 0,
      area: [],
      areaTotal: 0,
      floorList: [],
      houseList: [],
      propsChecked: [],
      acArea: [],
      roomId: []
    }
  },
  created() {
    this.getBuild()
    this.$nextTick(() => {
      let roomIds = this.$parent.$parent.$parent.tableDataHouse.map(
        item => item.roomId
      )
      this.$refs.selectSidebar.getNumType(roomIds)
    })
  },
  methods: {
    getChekedCount(val) {
      this.count = val
      this.$emit('getChekedCount', this.count)
    },
    // 选择房源后点击回显
    chooseHouseEnd(val, area) {
      this.houseList = area
      this.roomId = area.map(item => {
        return item.roomId
      })
      this.propsChecked = this.roomId
      this.acArea = area
      let checkedCount = this.propsChecked.length
      this.checkAll = checkedCount === val.length
      //   this.isIndeterminate = checkedCount > 0 && checkedCount < val.length;
      this.count = val.length
      this.$refs.selectTableRight.checkedBoxes = this.roomId
    },
    async changeLeftFloor(val) {
      let roomIds = this.$parent.$parent.$parent.tableDataHouse.map(
        item => item.roomId
      )
      let idArr = roomIds ? Array.from(new Set(roomIds)) : []
      let data = idArr.join(',')
      let roomTypes = this.$refs.selectSidebar.active
      let roomLabel = this.$refs.selectSidebar.current
      // let roomLabel = this.$refs.selectSidebar.current
      let str = val.join(',')
      const res = await getRoomSelectionList({
        parkId: this.parkId,
        buildingId: this.buildingId,
        floorIds: str,
        roomTypes,
        roomLabel,
        roomIds: this.$route.query.id ? data : ''
      })

      this.$refs.selectTableRight.getFloorList(res)
    },
    // 房源类型筛选
    changeHouseType(val) {
      this.$refs.selectTableRight.getFloorList(val)
    },
    // 隐藏未选中房间
    hideRoom() {
      this.$nextTick(() => {
        this.$refs.selectTableRight.hideRoom()
      })
    },
    // 获取楼栋
    async getBuild() {
      const res = await getBuild(this.parkId)
      this.list = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.buildingId = this.list[0].value
      this.list.forEach(item => {
        if (this.buildingId === item.value) {
          this.building = item.label
        }
      })
      this.$refs['select-table-top'].building = this.building
      this.$nextTick(() => {
        this.$refs.selectTableLeft.getFloorList(this.buildingId)
        this.$refs.selectSidebar.buildingId = this.buildingId
      })
      this.getRoomSelectionList()
    },
    // 全部数据
    async getRoomListAll() {
      let roomTypes = this.$refs.selectSidebar.active
      let roomLabel = this.$refs.selectSidebar.current
      let roomIds = this.$parent.$parent.$parent.tableDataHouse.map(
        item => item.roomId
      )
      let idArr = roomIds ? Array.from(new Set(roomIds)) : []
      let data = idArr.join(',')
      const res = await getRoomSelectionList({
        parkId: this.parkId,
        buildingId: this.buildingId,
        roomIds: this.$route.query.id ? data : '',
        roomTypes,
        roomLabel
      })
      this.$refs.selectTableRight.getFloorList(res)
    },
    // 房源选择数据
    async getRoomSelectionList() {
      let roomTypes = this.$refs.selectSidebar.active
      let roomLabel = this.$refs.selectSidebar.current
      this.$refs.selectSidebar.buildingId = this.buildingId
      this.$nextTick(() => {
        this.$refs.selectTableLeft.getFloorList(this.buildingId)
      })
      let roomIds = this.$parent.$parent.$parent.tableDataHouse.map(
        item => item.roomId
      )
      let idArr = roomIds ? Array.from(new Set(roomIds)) : []
      let data = idArr.join(',')
      this.list.forEach(item => {
        if (this.buildingId === item.value) {
          this.building = item.label
        }
      })
      this.$refs['select-table-top'].building = this.building
      const res = await getRoomSelectionList({
        parkId: this.parkId,
        buildingId: this.buildingId,
        roomIds: this.$route.query.id ? data : '',
        roomTypes,
        roomLabel
      })
      this.$refs.selectTableRight.getFloorList(res)
    },
    searchQueryHook(e) {
      console.log(e)
    },
    // handleCheckAllChange(val){
    //  this.count = val ? this.$refs.selectTableRight.list.length : 0;
    //  this.propsChecked = val ? this.$refs.selectTableRight.list : [];
    //  //  this.$refs.selectTableRight.checkedBoxes = val ? this.$refs.selectTableRight.list : [];
    //  this.areaTotal = 0
    //  if(val){
    //    this.$refs.selectTableRight.areaList.forEach(item => {
    //      this.areaTotal += item.area
    //    })
    //  }
    //  this.$emit('checkRoom',this.count,this.areaTotal)
    //  this.$emit('chooseHouseAll',this.$refs.selectTableRight.floorList,this.$refs.selectTableRight.list)
    //  this.isIndeterminate = false;
    // },
    checkBoxFn(val) {
      this.listAll = val
      let checkedCount = val.length
      this.checkAll = checkedCount === this.$refs.selectTableRight.list.length
      this.isIndeterminate =
        checkedCount > 0 &&
        checkedCount < this.$refs.selectTableRight.list.length
      //   this.area = this.$refs.selectTableRight.areaList.filter((item) => {
      //     if(val.indexOf(item.id) > -1){
      //       return item.area
      //     }
      //   })
      // this.areaTotal = 0
      // this.area.forEach(item => {
      //   this.areaTotal += item.area
      // })
      // this.$emit('checkRoom',this.areaTotal)
      this.$emit('chooseHouse', val, this.$refs.selectTableRight.list)
    }
  }
}
</script>

<style lang="scss" scoped>
.text-color {
  color: rgba(0, 0, 0, 0.4);
}
.select-table {
  height: calc(100% - 22px);
  border: 1px solid #dcdcdc;
  border-radius: 3px;
}
.select-table-left {
  height: calc(100% - 40px);
}
::v-deep {
  .el-table--border {
    display: none;
  }
  .selectBox > .el-select {
    .el-input__inner {
      max-width: 92px;
      height: 22px;
      line-height: 22px;
      border: none;
      padding: 0;
      color: rgba(0, 0, 0, 0.9);
      font-size: 14px;
      letter-spacing: 1px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .el-input__suffix {
      .el-select__caret {
        color: #dcdcdc;
        width: 16px;
        line-height: 22px;
      }
    }
    .el-select-dropdown {
      width: 320px;
      padding: 8px 4px 8px 8px;
      box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.12),
        0 4px 5px 0 rgba(0, 0, 0, 0.08), 0 1px 10px 0 rgba(0, 0, 0, 0.05);
      left: 0 !important;
      margin-top: 4px;
      .popper__arrow {
        display: none;
      }
      .el-select-dropdown__wrap {
        max-height: 225px;
        .el-select-dropdown__list {
          padding: 0 4px 0 0;
          .el-select-dropdown__item {
            height: 40px;
            line-height: 40px;
            margin-bottom: 4px;
            color: rgba(0, 0, 0, 0.9);
            padding: 0 0 0 8px;
            border-radius: 3px;
            letter-spacing: 1px;
            &.hover,
            &:hover {
              background: #e9f0ff;
            }
            &.selected {
              background: #e9f0ff;
              color: #0052d9;
              font-weight: normal;
            }
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
  }
  .el-col-lg-8 {
    width: 50% !important;
  }
}
</style>
