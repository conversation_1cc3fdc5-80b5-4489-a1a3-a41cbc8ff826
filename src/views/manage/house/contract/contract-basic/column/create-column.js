import { filterDate } from '@/filter'
import { parseTime } from '@/utils/tools'
export default {
  data() {
    return {
      tableColumnHouse: [
        {
          label: '楼栋',
          prop: 'building',
          render: (h, scope) => {
            return <div>{scope.row.building + scope.row.floor + '层'}</div>
          }
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '房源面积(m²)',
          prop: 'area',
          headerAlign: 'center',
          align: 'center'
        },
        {
          label: '是否已测绘',
          prop: 'hasSurveying',
          headerAlign: 'center',
          align: 'center',
          render: (h, scope) => {
            return <div>{scope.row.hasSurveying ? '是' : '否'}</div>
          }
        },
        {
          label: '执行面积(m²)',
          prop: 'executeArea',
          headerAlign: 'center',
          render: (h, scope) => {
            return (
              <div class="h100 w100 flex align-items-center justify-content-center">
                <div class="flex align-items-center">
                  <div class="font-size-14 line-height-22 m-r-4">
                    {scope.row.executeArea}
                  </div>
                  {/*判断路由是否有renew参数，有则禁用*/}
                  {this.$route.query.type ? (
                    <span style={'color:#bdbdbd;cursor:no-drop'}>修正</span>
                  ) : (
                    <el-button
                      className="m-l-10"
                      type="text"
                      onclick={() => {
                        this.editHouseArea(scope.row, scope.$index)
                      }}
                    >
                      修正
                    </el-button>
                  )}
                </div>
              </div>
            )
          }
        },
        // {
        //   prop:'price',
        //   label: '租赁单价(元/m²/月)',
        //   render:(h,scope) => {
        //     return <div class="color-warning">{scope.row.price}</div>
        //   }
        // },
        {
          prop: 'operation',
          label: '操作',
          width: 148,
          headerAlign: 'center',
          align: 'center',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div>
                {this.$route.query.type ? (
                  <span style={'color:#bdbdbd;cursor:no-drop'}>移出</span>
                ) : (
                  <el-link
                    type="primary"
                    onclick={() => {
                      this.deleteHouse(scope.row, scope.$index)
                    }}
                  >
                    移出
                  </el-link>
                )}
              </div>
            )
          }
        }
      ],
      tableColumnPlan: [
        {
          label: '期数',
          prop: 'periodNumber'
        },
        {
          label: '费用类型',
          prop: 'planTypeStr'
        },
        {
          label: '账单应缴时间',
          prop: 'billPayTime'
        },
        {
          label: '账单周期',
          prop: 'periodStartTime',
          render: (h, scope) => {
            return (
              <div>
                {filterDate(scope.row.periodStartTime) +
                  (filterDate(scope.row.periodEndTime) !== null &&
                  filterDate(scope.row.periodEndTime) !== ''
                    ? '~'
                    : '') +
                  filterDate(scope.row.periodEndTime)}
              </div>
            )
          }
        },
        {
          label: '账单金额',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div style={'color: #ED7B2F;'}>{scope.row.amount.toFixed(2)}</div>
            )
          }
        }
      ],
      tableColumnTotalPlan: [
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '开始时间',
          prop: 'startTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.startTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '结束时间',
          prop: 'endTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.endTime, '{y}-{m}-{d}')}</div>
          }
        }
      ]
    }
  }
}
