import { contractTypeOptions, payCycleOptions } from '../utils/status.js'
import dayjs from 'dayjs'

const validatePhone = (rule, value, callback, label) => {
  if (value === '') {
    callback(new Error(`请输入${label || '手机号'}`))
  } else {
    if (/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value)) {
      callback()
    } else {
      callback(new Error(`请输入正确的${label || '手机号'}或座机号`))
    }
  }
}

const validatorStartEnd = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请选择合同起止时间'))
  } else {
    let startChange = new Date(value[0]).getTime()
    let endChange = new Date(value[1]).getTime()
    if (startChange === endChange) {
      callback(new Error('合同起止时间不能在同一天'))
    } else {
      callback()
    }
  }
}

let basicSpan = 7
export default {
  data() {
    return {
      formConfigureEnt: {
        labelWidth: '100px',
        descriptors: {
          entId: {
            form: 'select',
            label: '企业名称',
            span: basicSpan,
            options: [],
            disabled: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择企业' //请输入企业名称
              }
            ],
            events: {
              change: this.entChange
            },
            customRight: () => {
              return (
                <div style="margin-top:1px;">
                  <el-button
                    disabled={
                      !this.fromModelEnt.entId ||
                      !!this.$route.query.type ||
                      this.isParkInfo
                    }
                    v-permission={this.routeButtonsPermission.DETAIL}
                    type="primary"
                    onclick={() => this.entDetail(this.fromModelEnt.entId)}
                  >
                    企业{this.routeButtonsTitle.DETAIL}
                  </el-button>
                </div>
              )
            },
            props: {
              filterable: true
            }
          },
          contact: {
            form: 'input',
            label: '企业联系人',
            span: basicSpan,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入法人代表或授权代表姓名'
              }
            ],
            events: {
              change: this.contactChange
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            span: basicSpan,
            maxlength: 11,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                message: '联系方式格式不正确',
                validator: validatePhone
              }
            ],
            events: {
              change: this.phoneChange
            }
          }
        }
      },
      // 选择园区
      formConfigureHouse: {
        labelWidth: '100px',
        descriptors: {
          parkId: {
            form: 'select',
            label: '入驻园区',
            span: basicSpan,
            disabled: false,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择企业入驻的园区'
              }
            ],
            events: {
              change: this.showSelectHouseChange
            },
            customTips: () => {
              return (
                <div
                  class={'h100 flex align-items-center'}
                  v-show={this.intentionHouseInfo.num}
                >
                  企业意向园区：xxx园区
                </div>
              )
            },
            props: {
              filterable: true
            }
          },
          type: {
            form: 'select',
            label: '合同类型',
            span: basicSpan,
            options: contractTypeOptions,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择合同类型'
              }
            ],
            events: {
              change: this.getContactType
            },
            customTips: () => {
              return (
                <div
                  class={'h100 flex align-items-center'}
                  v-show={this.intentionHouseInfo.num}
                >
                  企业意向入驻方式：租房入住
                </div>
              )
            }
          },
          tplName: {
            form: 'select',
            label: '合同模版',
            required: true,
            span: basicSpan,
            disabled: false,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择合同模版'
              }
            ],
            attrs: {
              noDataText: '暂未设置任何合同模板,请联系园区管理员添加'
            },
            events: {
              change: this.getContactNumber
            }
          }
        }
      },
      formConfigureContract: {
        labelWidth: '130px',
        descriptors: {
          contractNo: {
            form: 'input',
            label: '合同编号',
            // required: true,
            span: basicSpan,
            rule: [
              {
                type: 'string',
                message: '请输入合同编号'
              }
            ],
            render: () => {
              return (
                <div style={'display: flex;flex-flow: wrap;'}>
                  {this.contractArr.map((item, index) => {
                    return (
                      <div>
                        <div
                          v-show={item.type === 'string'}
                          style="display:inline-block"
                        >
                          {item['value' + index]}
                        </div>
                        <el-input
                          v-show={item.type === 'input'}
                          v-model={item.value}
                          style="width:55px;display:inline-block;"
                        >
                          {item.value}
                        </el-input>
                      </div>
                    )
                  })}
                </div>
              )
            }
          },
          startEndTime: {
            form: 'dateRange',
            label: '合同起止时间',
            span: basicSpan,
            // required: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择合同起止时间'
              },
              {
                validator: validatorStartEnd
              },
              {
                validator: (rule, value, callback) => {
                  if (value) {
                    if (value && value.length > 1) {
                      const date1 = dayjs(value[1])
                      let data2 = date1.diff(value[0], 'day')
                      let num = this.fromModelContract.freePeriod
                      if (num !== undefined && num !== '') {
                        if (num > data2) {
                          callback(
                            new Error('合同起止时间不能小于优惠期限天数')
                          )
                        } else {
                          callback()
                        }
                      } else {
                        callback()
                      }
                    } else {
                      callback()
                    }
                  } else {
                    callback()
                  }
                },
                trigger: 'change'
              }
            ],
            props: {
              // type: 'datetimerange',
              // format: 'yyyy-MM-dd HH:mm',
              // 'value-format': 'yyyy-MM-dd HH:mm',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
              rangeSeparator: '-'
              // pickerOptions: {
              //   disabledDate(date) {
              //     if (date.getDate() === 0 || date.getDate() === 6) {
              //       return true
              //     }
              //     return false
              //   }
              // }
            },
            events: {
              input: event => {
                // 借用input的@input事件，此配置一旦传入会覆盖原有的监听事件，为了能正常赋值，这里需要写上赋值的代码
                // select则不需要这样写
                this.$set(this.fromModelContract, 'startEndTime', event)
              }
            }
          },
          // freePeriod: {
          //   form: 'input',
          //   inputType: 'number',
          //   label: '优惠期限',
          //   span: basicSpan,
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请输入优惠天数'
          //     },
          //     {
          //       pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
          //       message: '请输入有效的优惠天数',
          //       trigger: 'change'
          //     },
          //     {
          //       validator: (rule, value, callback) => {
          //         if (value !== undefined && value !== '') {
          //           let list = this.fromModelContract.startEndTime
          //           if (list && list.length > 1) {
          //             const date1 = dayjs(list[1])
          //             let data2 = date1.diff(list[0], 'day')
          //             if (value > data2) {
          //               callback(new Error('优惠期限天数不能大于合同起止时间'))
          //             } else {
          //               callback()
          //             }
          //           } else {
          //             callback()
          //           }
          //         } else {
          //           callback()
          //         }
          //       },
          //       trigger: 'change'
          //     }
          //   ],
          //   events: {
          //     input: event => {
          //       this.$set(
          //         this.fromModelContract,
          //         'freePeriod',
          //         event.replace(/[^\d]/g, '').replace(/^0{1,}/g, '')
          //       )
          //       this.clearPayPlan()
          //     }
          //   },
          //   customRight: () => {
          //     return <div class="font-size-14 line-height-32">天</div>
          //   }
          // },
          payCycle: {
            form: 'select',
            label: '付款方式',
            span: basicSpan,
            options: payCycleOptions,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择付款方式'
              }
            ]
            // events: {
            //   change: this.clearPayPlan
            // }
          },
          rentFreePeriod: {
            label: '免租期',
            form: 'radio',
            span: basicSpan,
            rule: [
              {
                message: '请选择是否免租期',
                type: 'number'
              }
            ],
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          rentFreeTime: {
            label: '免租时长',
            form: 'input',
            span: basicSpan,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入免租时长'
              }
            ],
            customRight: () => {
              return <div class="font-size-14 line-height-32">天</div>
            },
            events: {
              change: () => {
                this.$set(this.fromModelContract, 'rentFreeEffective', '')
              }
            }
          },
          rentFreeEffective: {
            form: 'date',
            label: '免租生效日期',
            span: basicSpan,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择免租生效日期'
              }
            ],
            props: {
              pickerOptions: {
                disabledDate: date => {
                  const { startEndTime, rentFreeTime = 0 } =
                    this.fromModelContract
                  const time = rentFreeTime * 24 * 60 * 60
                  if (!startEndTime || !startEndTime.length) return true
                  const [startTime, endTime] = startEndTime
                  return (
                    dayjs(date).unix() < dayjs(startTime).unix() ||
                    dayjs(date).unix() > dayjs(endTime).unix() - time
                  )
                }
              }
            }
          }
          // lateFeeRate: {
          //   form: 'input',
          //   label: '滞纳金利率',
          //   required: true,
          //   span: basicSpan,
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请输入滞纳金利率'
          //     },
          //     {
          //       pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
          //       message: '请输入有效的滞纳金利率',
          //       trigger: 'change'
          //     }
          //   ],
          //   customRight: () => {
          //     return <div class="font-size-14 line-height-32">%</div>
          //   },
          //   events: {
          //     input: event => {
          //       this.$set(
          //         this.fromModelContract,
          //         'lateFeeRate',
          //         event.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
          //       )
          //       this.clearPayPlan()
          //     }
          //   }
          // },
          // unitPrice: {
          //   form: 'input',
          //   type: 'number',
          //   label: '房屋租赁单价',
          //   required: true,
          //   span: basicSpan,
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请输入房屋租赁单价'
          //     },
          //     {
          //       pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
          //       message: '请输入有效的房屋租赁单价',
          //       trigger: 'change'
          //     }
          //   ],
          //   events: {
          //     input: event => {
          //       this.$set(
          //         this.fromModelContract,
          //         'unitPrice',
          //         event.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
          //       )
          //       this.clearPayPlan()
          //     }
          //   },
          //   customRight: () => {
          //     return <div class="font-size-14 line-height-32">元/月·m²</div>
          //   }
          // },
        }
      },
      formConfigureTotal: {
        labelWidth: '130px',
        descriptors: {}
      },
      formConfigureTotalDisabled: {
        labelWidth: '130px',
        descriptors: {}
      },
      formConfigureAgreement: {
        descriptors: {
          agreement: {
            form: 'input',
            label: '附加条款',
            rule: [
              {
                type: 'string',
                message: '请输入附加条款内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 8,
              maxlength: 200,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            }
          }
        }
      },
      formConfigureHouseEdit: {
        labelWidth: '120px',
        descriptors: {
          building: {
            form: 'input',
            label: '房源面积(m²)',
            disabled: true,
            rule: [
              {
                type: 'number'
              }
            ],
            customRight: () => {
              return this.isHasSurveying ? (
                <div
                  class={'color-success line-height-32'}
                  style="height:32px;width:50px;margin-top: 44px;"
                >
                  已测绘
                </div>
              ) : (
                <div
                  class={'color-warning line-height-32'}
                  style="height:32px;width:50px;margin-top: 44px;"
                >
                  未测绘
                </div>
              )
            },
            customTips: () => {
              return (
                <div>
                  {this.fromModelHouseEdit.park &&
                    this.fromModelHouseEdit.park + '#'}
                  {this.fromModelHouseEdit.building &&
                    this.fromModelHouseEdit.building + '栋#'}
                  {this.fromModelHouseEdit.floor &&
                    this.fromModelHouseEdit.floor + '层#'}
                  {this.fromModelHouseEdit.room &&
                    this.fromModelHouseEdit.room + '号'}
                </div>
              )
            }
          },
          area: {
            form: 'input',
            label: '修正合同面积',
            // disabled: true,
            rule: [
              {
                type: 'string',
                required: true,
                message: '请输入修正合同面积',
                validator: (rule, value, callback) => {
                  if (!value) return callback(new Error(`请输入修正合同面积`))
                  callback()
                }
              },
              {
                type: 'string',
                validator: 'validateDecimal'
              }
            ],
            customTips: () => {
              return <div>根据修正后的合同面积计算价格</div>
            },
            events: {
              change: area => {
                if (!area) {
                  this.formConfigureHouseEdit.descriptors.area.customTips =
                    () => {
                      return <div>根据修正后的合同面积计算价格</div>
                    }
                } else {
                  const { building } = this.fromModelHouseEdit
                  const isDeviation =
                    area - building > building * 0.15 ||
                    area - building < building * -0.15
                  if (isDeviation) {
                    this.formConfigureHouseEdit.descriptors.area.customTips =
                      () => {
                        return (
                          <div style={'color:#ed7b2f'}>
                            修正后的合同面积与房源面积偏差超过15%，但不影响您继续操作
                          </div>
                        )
                      }
                  } else {
                    this.formConfigureHouseEdit.descriptors.area.customTips =
                      () => {
                        return <div>根据修正后的合同面积计算价格</div>
                      }
                  }
                }
              }
            }
          },
          correct: {
            form: 'input',
            label: '修正说明',
            rule: [
              {
                message: '请输入',
                type: 'string'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          }
          // correctArea: {
          //   form: 'input',
          //   label: '合同面积(m²)',
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请输入合同面积'
          //     },
          //     {
          //       pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
          //       message: '请输入有效的合同面积',
          //       trigger: 'change'
          //     }
          //   ],
          //   events: {
          //     input: event => {
          //       this.$set(
          //         this.fromModelHouseEdit,
          //         'correctArea',
          //         event.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
          //       )
          //     }
          //   }
          // }
        }
      },
      priceFormConfigure: {
        labelWidth: '120px',
        descriptors: {
          executePrice: {
            form: 'input',
            label: '执行单价',
            disabled: true,
            rule: [
              {
                type: 'number'
              }
            ]
          },
          planPrice: {
            form: 'input',
            label: '计划单价',
            rule: [
              {
                type: 'string',
                required: true,
                message: '请输入计划单价',
                validator: (rule, value, callback) => {
                  if (!value) return callback(new Error(`请输入计划单价`))
                  callback()
                }
              },
              {
                type: 'string',
                validator: 'validateDecimal'
              }
            ],
            customTips: () => {
              return <div></div>
            },
            events: {
              change: e => {
                const { executePrice } = this.priceDialogInfo.fromModel
                this.planPriceChange(e, executePrice)
              }
            }
          },
          remark: {
            form: 'input',
            label: '修正说明',
            rule: [
              {
                message: '请输入',
                type: 'string'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          }
          // correctArea: {
          //   form: 'input',
          //   label: '合同面积(m²)',
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请输入合同面积'
          //     },
          //     {
          //       pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
          //       message: '请输入有效的合同面积',
          //       trigger: 'change'
          //     }
          //   ],
          //   events: {
          //     input: event => {
          //       this.$set(
          //         this.fromModelHouseEdit,
          //         'correctArea',
          //         event.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
          //       )
          //     }
          //   }
          // }
        }
      },
      formConfigurePriceEdit: {
        labelWidth: '120px',
        descriptors: {
          statistics: {
            form: 'input',
            label: '总计',
            disabled: true,
            rule: [
              {
                type: 'string'
              }
            ],
            customRight: () => {
              return (
                <el-button type="primary" style="margin-top: 44px;">
                  租赁费
                </el-button>
              )
            }
          },
          totalCorrection: {
            form: 'input',
            label: '修正总计',
            // disabled: true,
            rule: [
              {
                type: 'number'
              }
            ],
            customTips: () => {
              return (
                <div class="flex justify-content-start">
                  棂据修正后的费用项总金额计算每期应收
                </div>
              )
            }
          },
          correct: {
            form: 'input',
            label: '修正说明',
            rule: [
              {
                message: '请输入',
                type: 'string'
              }
            ],
            attrs: {
              type: 'textarea',
              maxlength: 200,
              rows: 4,
              showWordLimit: true
            }
          }
          // correctArea: {
          //   form: 'input',
          //   label: '合同面积(m²)',
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请输入合同面积'
          //     },
          //     {
          //       pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
          //       message: '请输入有效的合同面积',
          //       trigger: 'change'
          //     }
          //   ],
          //   events: {
          //     input: event => {
          //       this.$set(
          //         this.fromModelHouseEdit,
          //         'correctArea',
          //         event.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
          //       )
          //     }
          //   }
          // }
        }
      }
    }
  }
}
