export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'park',
          label: '园区名称'
        },
        {
          prop: 'building',
          label: '楼栋'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-button
                onClick={() => this.delParkHandler(scope)}
                type={'text'}
                class={'color-danger'}
              >
                移出
              </el-button>
            )
          }
        }
      ]
    }
  }
}
