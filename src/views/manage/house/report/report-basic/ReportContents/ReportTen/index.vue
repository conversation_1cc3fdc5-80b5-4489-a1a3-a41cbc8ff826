<template>
  <div class="report-ten-container">
    <div :id="titleId" class="m-b-8 line-height-22">{{ title }}</div>
    <div class="report-wrapper">
      <div class="color-f font-size-14 line-height-22 m-b-8">
        指报告期末实际存续租约按照租赁面积加权计算的签约每月租金金额。
      </div>
      <!--      表格区域-->
      <div class="line-height-22 font-size-14 m-b-8">
        报告期末每平米租金计算
      </div>
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList1"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <!--      表格区域-->
      <div class="line-height-22 font-size-14 m-b-8">报告期内每月应收租金</div>
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList2"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <!--      表格区域-->
      <div class="line-height-22 font-size-14 m-b-8">报告期内每月应收租金</div>
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList3"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReportTen',
  props: {
    title: {
      type: String,
      default: '十、报告期末每平米租金'
    },
    titleId: {
      type: String,
      default: 'ReportTen'
    },
    tenData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableList1: [
        {
          label: '报告期末',
          list: []
        },
        {
          label: '期末存续合同租金（元）',
          list: []
        },
        {
          label: '期末租赁面积（m²）',
          list: []
        },
        {
          label: '期末每平米租金（元/m²）',
          list: []
        }
      ],
      tableList2: [
        {
          label: '月份',
          list: []
        },
        {
          label: '应收租金（元）',
          list: []
        }
      ],
      tableList3: [
        {
          label: '月份',
          list: []
        },
        {
          label: '租赁面积（m²）',
          list: []
        },
        {
          label: '实际租赁天数',
          list: []
        },
        {
          label: '本月总天数',
          list: []
        }
      ]
    }
  },
  created() {
    this.getDetailSquareMetre()
  },
  methods: {
    getDetailSquareMetre() {
      // 报告期末每平米租金
      const rentPerSquare = this.tenData?.rentPerSquare || {}
      this.tableList1[0].list = [rentPerSquare.month]
      this.tableList1[1].list = [rentPerSquare.rent]
      this.tableList1[2].list = [rentPerSquare.rentArea]
      this.tableList1[3].list = [rentPerSquare.rentPerSquare]
      // 报告期内每月应收租金
      const rentPerSquareRent = this.tenData?.rentPerSquareRent || []
      this.tableList2[0].list = rentPerSquareRent.map(item => item.month) || []
      this.tableList2[1].list = rentPerSquareRent.map(item => item.rent) || []
      // 报告期内每月应收租金
      const rentPerSquareRentDetail =
        this.tenData?.rentPerSquareRentDetail || []
      this.tableList3[0].list =
        rentPerSquareRentDetail.map(item => item.month) || []
      this.tableList3[1].list =
        rentPerSquareRentDetail.map(item => item.rentArea) || []
      this.tableList3[2].list =
        rentPerSquareRentDetail.map(item => item.rentDays) || []
      this.tableList3[3].list =
        rentPerSquareRentDetail.map(item => item.totalDays) || []
    }
  }
}
</script>

<style lang="scss" scoped>
.report-ten-container {
  margin-bottom: 24px;
  .color-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .report-wrapper {
    padding-left: 24px;
    .table-list {
      border-right: 1px solid #e7e7e7;
      border-top: 1px solid #e7e7e7;
      border-left: 1px solid #e7e7e7;
      border-radius: 3px;
      .table-main {
        .table-body {
          width: calc(100% - 196px);
          .table-item {
            flex: 1;
            padding: 16px 0;
            font-size: 14px;
            text-align: center;
            //border-right: 1px solid #ebeef5;
            border-bottom: 1px solid #e7e7e7;
            //&:last-child {
            //  border-right: 1px solid transparent;
            //}
          }
        }
        .table-header {
          width: 196px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          //border-right: 1px solid #ebeef5;
          border-bottom: 1px solid #e7e7e7;
          text-align: left;
          padding: 16px 0 16px 24px;
          //@include background_color_mix(--color-primary, #ffffff, 96%);
        }
      }
      .table-main:nth-child(2n + 1) {
        background-color: #f0f2f5;
      }
      .table-main:first-child {
        color: rgba(0, 0, 0, 0.4);
        background-color: transparent;
      }
    }
  }
}
</style>
