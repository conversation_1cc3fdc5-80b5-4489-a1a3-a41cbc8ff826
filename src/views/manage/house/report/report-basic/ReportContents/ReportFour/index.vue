<template>
  <div class="report-four-container">
    <div :id="titleId" class="m-b-8 line-height-22">{{ title }}</div>
    <div class="report-wrapper">
      <div class="color-f font-size-14 line-height-22 m-b-8">
        报告期内新签租约的租户数量、租赁面积、签约每月租金情况。
      </div>
      <div class="line-height-22 font-size-14 m-b-8">
        报告期内新签约每月租金情况
      </div>
      <!--      表格区域-->
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <div class="flex font-size-14 line-height-22 m-b-8">
        <div class="m-r-8">数据结论</div>
        <div class="color-f" style="flex: 1">
          报告期内新签租约的租户数{{ newSignTotal }}家。新签约租赁面积{{
            newSignAreaTotal
          }}m²
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReportFour',
  props: {
    title: {
      type: String,
      default: '四、报告期内新签租约情况'
    },
    titleId: {
      type: String,
      default: 'ReportFour'
    },
    fourData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableList: [
        {
          label: '月份',
          list: []
        },
        {
          label: '新签约租户数',
          list: []
        },
        {
          label: '新签约租赁面积（m²）',
          list: []
        },
        {
          label: '新签约租金（元）',
          list: []
        }
      ],
      newSignAreaTotal: 0,
      newSignTotal: 0
    }
  },
  created() {
    this.getDetailNewSign()
  },
  methods: {
    getDetailNewSign() {
      this.newSignTotal = this.fourData.newSignTotal
      this.newSignAreaTotal = this.fourData.newSignAreaTotal
      // 报告期各月实际出租面积和可供出租面积以及日均出租率
      const newSign = this.fourData.newSign || []
      this.tableList[0].list = newSign.map(item => item.month) || []
      this.tableList[1].list = newSign.map(item => item.newSignTenantNum) || []
      this.tableList[2].list = newSign.map(item => item.newSignRentArea) || []
      this.tableList[3].list = newSign.map(item => item.newSignRent) || []
    }
  }
}
</script>

<style lang="scss" scoped>
.report-four-container {
  margin-bottom: 24px;
  .color-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .report-wrapper {
    padding-left: 24px;
    .table-list {
      border-right: 1px solid #e7e7e7;
      border-top: 1px solid #e7e7e7;
      border-left: 1px solid #e7e7e7;
      border-radius: 3px;
      .table-main {
        .table-body {
          width: calc(100% - 196px);
          .table-item {
            flex: 1;
            padding: 16px 0;
            font-size: 14px;
            text-align: center;
            //border-right: 1px solid #ebeef5;
            border-bottom: 1px solid #e7e7e7;
            //&:last-child {
            //  border-right: 1px solid transparent;
            //}
          }
        }
        .table-header {
          width: 196px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          //border-right: 1px solid #ebeef5;
          border-bottom: 1px solid #e7e7e7;
          text-align: left;
          padding: 16px 0 16px 24px;
          //@include background_color_mix(--color-primary, #ffffff, 96%);
        }
      }
      .table-main:nth-child(2n + 1) {
        background-color: #f0f2f5;
      }
      .table-main:first-child {
        color: rgba(0, 0, 0, 0.4);
        background-color: transparent;
      }
    }
  }
}
</style>
