<template>
  <div class="report-seven-container">
    <div :id="titleId" class="m-b-8 line-height-22">{{ title }}</div>
    <div class="report-wrapper">
      <div class="color-f font-size-14 line-height-22 m-b-8">
        报告期内到期租约的租户数量、租赁面积、签约每月租金的情况，以及其中续签的租户数量、租赁面积、签约每月租金占到期租约的相应比例。
      </div>
      <!--      表格区域-->
      <div class="line-height-22 font-size-14 m-b-8">报告期内租约到期汇总</div>
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList1"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <!--      表格区域-->
      <div class="line-height-22 font-size-14 m-b-8">报告期内续租汇总</div>
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList2"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <!--      表格区域-->
      <div class="line-height-22 font-size-14 m-b-8">
        报告期内续租的租户数、租赁面积、每月租金占比租约到期的比例
      </div>
      <div class="table-list wh100 m-b-8">
        <div
          class="table-main flex"
          v-for="(val, index) in tableList3"
          :key="index"
        >
          <span class="table-header inline-block">{{ val.label }}</span>
          <div class="table-body flex">
            <span
              class="table-item inline-block"
              v-for="(item, index) in val.list"
              :key="index"
              >{{ item }}</span
            >
          </div>
        </div>
      </div>
      <div class="flex font-size-14 line-height-22 m-b-8">
        <div class="m-r-8">数据结论</div>
        <div class="color-f" style="flex: 1">
          报告期内租约到期数{{ tenants }}家、租赁面积{{
            area
          }}（m²），续租租户数{{ renewalTenants }}家（占比到期{{
            proportionTenants
          }}%）。租赁面积{{ renewalArea }}（m²）（占比到期{{
            proportionArea
          }}%）
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ReportSeven',
  props: {
    title: {
      type: String,
      default: '七、报告期内租约到期及其续租情况'
    },
    titleId: {
      type: String,
      default: 'ReportSeven'
    },
    sevenData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableList1: [
        {
          label: '月份',
          list: []
        },
        {
          label: '租约到期租户数',
          list: []
        },
        {
          label: '租赁面积（m²）',
          list: []
        },
        {
          label: '每月合计租金（元）',
          list: []
        }
      ],
      tableList2: [
        {
          label: '月份',
          list: []
        },
        {
          label: '续租租户数',
          list: []
        },
        {
          label: '租赁面积（m²）',
          list: []
        },
        {
          label: '每月合计租金（元）',
          list: []
        }
      ],
      tableList3: [
        {
          label: '月份',
          list: []
        },
        {
          label: '续租租户数',
          list: []
        },
        {
          label: '租赁面积',
          list: []
        },
        {
          label: '每月合计租金',
          list: []
        }
      ],
      area: 0,
      proportionArea: 0,
      proportionTenants: 0,
      renewalArea: 0,
      renewalTenants: 0,
      tenants: 0
    }
  },
  created() {
    this.getDetailLeaseExpires()
  },
  methods: {
    getDetailLeaseExpires() {
      this.area = this.sevenData.area
      this.proportionArea = this.sevenData.proportionArea
      this.proportionTenants = this.sevenData.proportionTenants
      this.renewalArea = this.sevenData.renewalArea
      this.renewalTenants = this.sevenData.renewalTenants
      this.tenants = this.sevenData.tenants
      //  报告期内租约到期汇总
      const leaseExpiration = this.sevenData.leaseExpiration || []
      this.tableList1[0].list = leaseExpiration.map(item => item.month) || []
      this.tableList1[1].list =
        leaseExpiration.map(item => item.leaseExpirationTenantNum) || []
      this.tableList1[2].list =
        leaseExpiration.map(item => item.leaseExpirationRentArea) || []
      this.tableList1[3].list =
        leaseExpiration.map(item => item.leaseExpirationRent) || []
      // 报告期内续租汇总
      const renewal = this.sevenData.renewal || []
      this.tableList2[0].list = renewal.map(item => item.month) || []
      this.tableList2[1].list =
        renewal.map(item => item.leaseExpirationTenantNum) || []
      this.tableList2[2].list =
        renewal.map(item => item.leaseExpirationRentArea) || []
      this.tableList2[3].list =
        renewal.map(item => item.leaseExpirationRent) || []
      // 报告期内续租的租户数、租赁面积、每月租金占比租约到期的比例
      const renewalDetail = this.sevenData.renewalDetail || []
      this.tableList3[0].list = renewalDetail.map(item => item.month) || []
      this.tableList3[1].list = renewalDetail.map(item => item.numRate) || []
      this.tableList3[2].list = renewalDetail.map(item => item.areaRate) || []
      this.tableList3[3].list = renewalDetail.map(item => item.rentRate) || []
    }
  }
}
</script>

<style lang="scss" scoped>
.report-seven-container {
  margin-bottom: 24px;
  .color-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .report-wrapper {
    padding-left: 24px;
    .table-list {
      border-right: 1px solid #e7e7e7;
      border-top: 1px solid #e7e7e7;
      border-left: 1px solid #e7e7e7;
      border-radius: 3px;
      .table-main {
        .table-body {
          width: calc(100% - 196px);
          .table-item {
            flex: 1;
            padding: 16px 0;
            font-size: 14px;
            text-align: center;
            //border-right: 1px solid #ebeef5;
            border-bottom: 1px solid #e7e7e7;
            //&:last-child {
            //  border-right: 1px solid transparent;
            //}
          }
        }
        .table-header {
          width: 196px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          //border-right: 1px solid #ebeef5;
          border-bottom: 1px solid #e7e7e7;
          text-align: left;
          padding: 16px 0 16px 24px;
          //@include background_color_mix(--color-primary, #ffffff, 96%);
        }
      }
      .table-main:nth-child(2n + 1) {
        background-color: #f0f2f5;
      }
      .table-main:first-child {
        color: rgba(0, 0, 0, 0.4);
        background-color: transparent;
      }
    }
  }
}
</style>
