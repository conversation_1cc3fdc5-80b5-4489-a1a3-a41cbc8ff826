<template>
  <transition name="steps-tips-fade">
    <div class="steps-tips" v-if="isShow">
      <div class="flex align-center">
        <div class="tips-icon">
          <svg-icon icon-class="help-circle-filled" />
        </div>
        <div class="tips-content">点击步骤条可回溯查看详情和修改操作</div>
      </div>
      <div class="tips-close" @click="isShow = false">
        <svg-icon icon-class="close" />
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'StepsTips',
  data() {
    return {
      isShow: false
    }
  },
  methods: {
    show() {
      this.isShow = true
    }
  }
}
</script>

<style scoped lang="scss">
.steps-tips {
  width: 346px;
  padding: 13px 16px;
  background: #fff;
  box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.08),
    0 16px 24px 2px rgba(0, 0, 0, 0.04), 0 6px 30px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid;
  @include border_color(--border-color-base);
  border-radius: 3px;
  position: fixed;
  top: 64px;
  left: calc((100% - 346px) / 2);
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.4s;
  .tips-icon {
    @include font_color(--color-primary);
  }
  .tips-content {
    padding-left: 9px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 20px;
  }
  .tips-close {
    color: rgba(0, 0, 0, 0.6);
    cursor: pointer;
  }
}
// 过渡效果样式
.steps-tips-fade-enter,
.steps-tips-fade-leave-active {
  opacity: 0;
  transform: translateY(-100%);
}
</style>
