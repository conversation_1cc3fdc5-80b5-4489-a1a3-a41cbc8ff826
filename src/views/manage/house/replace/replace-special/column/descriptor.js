export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          applyName: {
            form: 'input',
            label: '申请名称',
            disabled: true,
            rule: [
              {
                // required:true,
                type: 'string',
                message: '请输入申请名称'
              }
            ]
          },
          content: {
            form: 'input',
            label: '完结说明',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请简要说明当前完结理由'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 2,
              maxlength: 200,
              showWordLimit: true
            }
          }
        }
      },
      formConfigureRelated: {
        descriptors: {
          contractNo: {
            form: 'select',
            label: '选择合同',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择选择合同' //请选择选择合同
              }
            ]
          }
        }
      },
      formConfigureDate: {
        descriptors: {
          checkOut: {
            form: 'date',
            label: '退租日期',
            disabled: false,
            customRight: () => {
              return (
                <div
                  class="line-height-33 font-size-12"
                  style={'color:rgba(0,0,0,0.40);'}
                >
                  日期选择会影响后续合同及缴费管理等业务办理，须谨慎选择
                </div>
              )
            },
            events: {
              change: this.changeDate
            }
          }
        }
      },
      formConfigureMatter: {
        descriptors: {
          title: {
            form: 'input',
            label: '事项标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入事项标题'
              }
            ],
            attrs: {
              maxlength: 50
            }
          },
          content: {
            form: 'input',
            label: '事项描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请简要说明当前事项'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 3,
              maxlength: 50,
              showWordLimit: true
            }
          }
        }
      },
      formConfigureData: {
        descriptors: {
          personIds: {
            form: 'cascader',
            label: '办理人员',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择需要办理的人员'
              }
            ],
            options: [],
            props: {
              showAllLevels: true,
              props: {
                multiple: true,
                value: 'value',
                label: 'label',
                children: 'children'
              }
            },
            slotRender: (h, options, scope) => {
              const { data } = scope
              return <span v-tooltip={data.label + '@@@&&&166'}></span>
            }
            // events: {
            //   change: (e) => {this.personChange(e)},
            // },
          }
        }
      }
    }
  }
}
