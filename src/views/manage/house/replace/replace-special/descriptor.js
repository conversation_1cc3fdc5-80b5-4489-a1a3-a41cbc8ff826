let validateOccupancyDate = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请选择期望入驻日期'))
  } else {
    let changeDate = new Date(value).getTime()
    let nowDate = new Date().getTime() - 86400000
    if (nowDate > changeDate) {
      callback(new Error('期望入驻日期应在当前日期及当前日期之后'))
    } else {
      callback()
    }
  }
}

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '140px',
        descriptors: {
          type: {
            form: 'select',
            label: '调房类型',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择调房类型'
              }
            ],
            events: {
              change: this.changeType
            }
          },
          // ====================================================================
          contractId: {
            form: 'select',
            label: '合同选择',
            span: 12,
            hidden: true,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择合同选择'
              }
            ],
            events: {
              change: e => {
                this.changeTable(e)
              }
            }
          },
          quitDate: {
            form: 'date',
            label: '退租日期',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择日期'
              }
            ],
            props: {
              format: ''
            }
          },
          contractDate: {
            form: 'dateRange',
            label: '合同有效期',
            span: 12,
            hidden: true,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择日期'
              }
            ]
          },
          reason: {
            form: 'input',
            label: '退租原因',
            span: 24,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 3,
              maxlength: 200,
              showWordLimit: true
            }
          },
          // ====================================================================
          parkId: {
            form: 'select',
            label: '意向园区',
            span: 12,
            hidden: false,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择意向园区'
              }
            ]
          },
          area: {
            form: 'input',
            label: '需求面积',
            span: 12,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入房屋需求面积'
              }
            ],
            customRight: () => {
              return <div class={'line-height-32 font-size-14'}>m²</div>
            }
          },
          occupancyTime: {
            form: 'date',
            label: '期望入驻日期',
            span: 12,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择入驻日期'
              },
              {
                validator: validateOccupancyDate
              }
            ]
          },
          cause: {
            form: 'input',
            label: '增房原因',
            span: 24,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 3,
              maxlength: 200,
              showWordLimit: true
            }
          }
        }
      },
      formConfigureNeed: {
        labelWidth: '140px',
        descriptors: {
          housingUse: {
            form: 'select',
            label: '房屋用途',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择房屋用途'
              }
            ]
          },
          bearing: {
            form: 'input',
            label: '楼板承重',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入承重需求'
              }
            ],
            customRight: () => {
              return <div class={'line-height-32 font-size-14'}>kg/m²</div>
            }
          },
          electricityLoad: {
            form: 'input',
            label: '用电负荷',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入每月用电负荷'
              }
            ],
            customRight: () => {
              return <div class={'line-height-32 font-size-14'}>kw</div>
            }
          },
          waterDemand: {
            form: 'select',
            label: '用水需求',
            span: 12,
            options: [
              {
                label: '有',
                value: true
              },
              {
                label: '否',
                value: false
              }
            ],
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择'
              }
            ]
          },
          drainageNeeds: {
            form: 'select',
            label: '排水需求',
            span: 12,
            options: [
              {
                label: '有',
                value: true
              },
              {
                label: '否',
                value: false
              }
            ],
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择'
              }
            ]
          },
          environment: {
            form: 'select',
            label: '环境影响',
            span: 12,
            options: [
              {
                label: '有',
                value: true
              },
              {
                label: '否',
                value: false
              }
            ],
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择'
              }
            ]
          },
          financing: {
            form: 'select',
            label: '融资需求',
            span: 12,
            options: [
              {
                label: '有',
                value: true
              },
              {
                label: '否',
                value: false
              }
            ],
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择'
              }
            ]
          },
          service: {
            form: 'select',
            label: '项目服务需求',
            span: 12,
            options: [
              {
                label: '有',
                value: true
              },
              {
                label: '否',
                value: false
              }
            ],
            rule: [
              {
                required: true,
                type: 'boolean',
                message: '请选择'
              }
            ]
          },
          environmentFactor: {
            form: 'checkbox',
            label: '影响因素',
            span: 24,
            hidden: true,
            required: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择影响类型'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <el-input
                  v-show={this.ipt}
                  v-model={this.environmentFactorOther}
                  placeholder="请输入对环境产生的影响"
                  style="position: absolute;width:49%;margin-left: calc(-50% + 12px);"
                ></el-input>
              )
            }
          },
          financingMethods: {
            form: 'checkbox',
            label: '可接受的融资方式',
            span: 24,
            hidden: true,
            required: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择可接受的融资方式'
              }
            ],
            options: []
          },
          projectServices: {
            form: 'checkbox',
            label: '项目服务',
            span: 24,
            hidden: true,
            required: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择项目服务'
              }
            ],
            options: []
          }
        }
      },
      formConfigureRenting: {
        labelWidth: '100px',
        descriptors: {
          housingType: {
            form: 'select',
            label: '调房类型',
            span: 12,
            options: [],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择调房类型'
              }
            ]
          },
          contractId: {
            form: 'select',
            label: '合同选择',
            span: 12,
            hidden: true,
            options: [],
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择合同选择'
              }
            ]
          },
          quitDate: {
            form: 'date',
            label: '退租日期',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择日期'
              }
            ]
          },
          contractDate: {
            form: 'date',
            label: '合同有效期',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择日期'
              }
            ]
          },
          reasons: {
            form: 'input',
            label: '退租原因',
            span: 24,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 3
              // maxlength: 200,
              // showWordLimit: true
            }
          }
        }
      }
    }
  }
}
