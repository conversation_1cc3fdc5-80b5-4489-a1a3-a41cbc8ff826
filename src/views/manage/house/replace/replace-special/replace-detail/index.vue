<template>
  <div class="replace-detail min-h100">
    <!--    头部步骤条-->
    <replace-header
      ref="replaceHeader"
      :operation-list="operationList"
      @stepClickHandle="stepClickHandle"
      :obj-data="replaceDetail"
      @getNewPage="getNewPage"
      :replace-header="replaceMatter"
    />
    <!--    v-if="butStatus.isProgress && butStatus.isStart"-->
    <div
      v-if="replaceDetail.current === 2"
      class="flex justify-content-between m-t-8"
      style="height: calc(100vh - 250px)"
    >
      <div style="flex: 1; display: flex; flex-direction: column">
        <replace-consult ref="consult" :replace-consult="replaceDetail" />
        <replace-demand ref="demand" :replace-demand="replaceDetail" />
      </div>
      <replace-auxiliary
        ref="auxiliary"
        v-if="replaceDetail.showMaking"
        :obj-detail="replaceDetail"
      />
      <replace-record-back v-else :operation-list="operationList" />
    </div>
    <div
      v-if="
        (replaceDetail.current === 3 || replaceDetail.current === 4) &&
        replaceDetail.type === 0
      "
      class="flex justify-content-between m-t-8"
      style="height: calc(100vh - 320px)"
    >
      <replace-matter
        ref="matter"
        :replace-matter="replaceMatter"
        :obj-data="replaceDetail"
        @getNewPage="getNewPage"
      />
      <replace-record v-if="finshData" :operation-list="operationList" />
    </div>
    <div
      v-if="replaceDetail.type === 2 && replaceDetail.current === 3 && process"
      class="flex justify-content-between m-t-8"
      style="height: calc(100vh - 320px)"
    >
      <replace-matter
        ref="matter"
        :replace-matter="replaceMatter"
        :obj-data="replaceDetail"
        @getNewPage="getNewPage"
      />
      <replace-record v-if="finshData" :operation-list="operationList" />
    </div>
    <replace-form
      :obj-data="replaceDetail"
      :obj-value="replaceMatter"
      v-if="
        replaceDetail.type === 2 &&
        (replaceDetail.current === 3 || replaceDetail.current === 4) &&
        !process
      "
      @changeDate="changeDate"
    />
    <div
      v-if="
        replaceDetail.type === 2 &&
        (replaceDetail.current === 3 || replaceDetail.current === 4) &&
        !process
      "
      class="replace flex bg-white justify-content-between m-t-8 p-24"
      style="height: calc(100vh - 320px)"
    >
      <replace-renting :obj-data="replaceMatter" :data-detail="replaceDetail" />
      <div class="line"></div>
      <replace-check
        :obj-data="replaceMatter"
        :data-detail="replaceDetail"
        @getNewPage="getNewPage"
      />
    </div>
  </div>
</template>

<script>
import ReplaceHeader from '@/views/manage/house/replace/replace-special/components/replace-header'
import ReplaceRecordBack from '@/views/manage/house/replace/replace-special/components/replaceRecordBack'
import ReplaceConsult from '@/views/manage/house/replace/replace-special/replace-detail/replaceConsult'
import ReplaceDemand from '@/views/manage/house/replace/replace-special/replace-detail/replaceDemand'
import ReplaceAuxiliary from '@/views/manage/house/replace/replace-special/replace-detail/replaceAuxiliary'
import ReplaceMatter from '@/views/manage/house/replace/replace-special/replace-detail/replaceMatter'
import ReplaceRecord from '@/views/manage/house/replace/replace-special/replace-detail/replaceRecord'
import ReplaceRenting from '@/views/manage/house/replace/replace-special/replace-detail/replaceRenting'
import ReplaceCheck from '@/views/manage/house/replace/replace-special/replace-detail/replaceCheck'
import ReplaceForm from '@/views/manage/house/replace/replace-special/components/replaceForm'
import {
  getApplyParkDetail,
  getApplyParkTransfer,
  getReviseApplyOperations
} from '@/views/manage/house/replace/replace-special/api'
export default {
  name: 'ReplaceDetail',
  components: {
    ReplaceCheck,
    ReplaceRenting,
    ReplaceRecord,
    ReplaceMatter,
    ReplaceAuxiliary,
    ReplaceDemand,
    ReplaceConsult,
    ReplaceHeader,
    ReplaceForm,
    ReplaceRecordBack
  },
  provide() {
    return {
      SettleInProjectDetail: this
    }
  },
  data() {
    return {
      replaceDetail: {},
      replaceMatter: {},
      finshData: true,
      operationList: [],
      process: false
    }
  },
  created() {
    this.getApplyParkDetail() // 详情 - 获得详情 - 园区审核
    this.getApplyParkTransfer() // 详情 - 获得详情 - 调房办理
    this.getReviseApplyOperations() // 详情 - 操作记录
  },
  methods: {
    // 退租日期
    changeDate(e) {
      this.$refs.replaceHeader.outDate = e
      this.replaceDetail.withdrawalTime = e
    },
    // 详情 - 获得详情 - 园区审核
    async getApplyParkDetail() {
      let id = this.$route.query.id
      const res = await getApplyParkDetail(id)
      this.replaceDetail = res
    },
    // 详情 - 获得详情 - 调房办理
    async getApplyParkTransfer() {
      let id = this.$route.query.id
      const res = await getApplyParkTransfer(id)
      this.replaceMatter = res
    },
    // 点击回溯
    stepClickHandle(row, index) {
      this.replaceDetail.current = index + 1
      this.finshData = true
      if (index === 2 && row.isSuccess) {
        this.process = true
      } else if (index === 3) {
        this.process = false
      }
      // this.getApplyParkDetail()
      if (this.replaceDetail.current === 4 && this.replaceDetail.type === 0) {
        this.finshData = false
      }
    },
    // 刷新页面
    getNewPage() {
      this.$nextTick(() => {
        this.$refs.replaceHeader.init() // 详情流程
      })
      this.getReviseApplyOperations() // 详情 - 操作记录
      if (this.process && this.replaceDetail.type === 2)
        return (this.process = true)
      this.getApplyParkDetail() // 详情 - 获得详情 - 园区审核
      this.getApplyParkTransfer() // 详情 - 获得详情 - 调房办理
    },
    // 详情 - 操作记录
    async getReviseApplyOperations() {
      let id = this.$route.query.id
      const res = await getReviseApplyOperations(id, 0)
      this.operationList = res
    }
  }
}
</script>

<style lang="scss" scoped>
.line {
  width: 1px;
  padding-top: 24px;
  background-color: #dcdcdc;
}
.replace {
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 1px solid #dcdcdc;
}
:deep(.basic-card[data-v-6ab51d61]) {
  min-height: 0;
}
</style>
