<template>
  <div class="m-t-20">
    <div class="pos-relative w100">
      <basic-tab
        :current="current"
        :tabsData="tabsData"
        @tabsChange="tabsChange"
      />
      <div class="edit-info" v-if="detail.showMaking">
        <span>以下数据全部基于：</span>
        <span>{{ detail.actualQuitDate }}</span>
        <span>退租计算所得</span>
        <span class="color-success m-l-6 m-r-6 pointer" @click="edit"
          >修改</span
        >
        <span class="color-primary pointer" @click="flushed">刷新</span>
      </div>
      <div class="font-size-14" v-if="detail.type === 0 && current === 1">
        企业申请增加房间无需对任何费用进行应收和应退办理
      </div>

      <div>
        <div v-if="current === 1 && detail.type === 2">
          <el-skeleton v-if="!loading" animated :loading="!loading" />
          <div v-else>
            <div class="font-size-14 m-b-20">
              <span>企业应全部结清：</span>
              <span class="color-warning">¥{{ shouldSettle }}元 </span>,
              <span>园区应退还企业：</span>
              <span class="color-warning">¥{{ shouldReturn }}元 </span>,
              <span>以下是详细清单</span>
            </div>
            <div class="flex justify-content-between">
              <el-radio-group v-model="cost" class="m-b-16">
                <el-radio-button :label="1">应收</el-radio-button>
                <el-radio-button :label="2">应退</el-radio-button>
                <el-radio-button :label="3">自定义款项</el-radio-button>
              </el-radio-group>
              <div v-if="cost === 3">
                <span class="font-size-12 color-info"
                  >以下收款事项仅作数据记录，请线下及时联系企业处理</span
                >
                <el-button
                  v-permission="routeButtonsPermission.ADD_ITEM"
                  type="primary"
                  class="m-l-20"
                  @click="visibleMoney = true"
                  >{{ routeButtonsTitle.ADD_ITEM }}</el-button
                >
              </div>
            </div>
            <drive-table
              ref="drive-table"
              :height="height"
              :columns="tableColumnFn"
              :table-data="tableDataFn"
            />
          </div>
          <!--      <div v-else>企业申请增加房间无需对任何费用进行应收和应退办理</div>-->
        </div>
        <div v-if="current === 2">
          <el-skeleton v-if="!loading" animated :loading="!loading" />
          <drive-table
            v-else
            ref="drive-table"
            :height="height2"
            :columns="thingTableColumn"
            :table-data="thingTableData"
          />
        </div>
      </div>
      <div v-if="!detail.showMaking && detail.type === 2">
        <el-button type="primary" class="m-t-20" @click="visible = true"
          >企业扫描</el-button
        >
        <div class="color-info font-size-14 m-t-12">
          点击开始搜索该企业全部数据并生成所有相关需办理的事项，开始前必须输入一个实际退租时间，所有数据会基于此时间来进行计算
        </div>
      </div>
    </div>
    <!--    //选择时间弹框-->
    <dialog-cmp
      title="选择时间"
      width="360px"
      :visible.sync="visible"
      @confirmDialog="confirmDialog"
    >
      <div class="w100">
        <el-date-picker
          class="w100"
          style="width: 100%"
          v-model="time"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
        />
      </div>
    </dialog-cmp>

    <!--    自定义款项弹框-->
    <basic-drawer
      title="自定义款项"
      width="360px"
      :visible.sync="visibleMoney"
      @confirmDrawer="moneyConfirmDialog"
    >
      <driven-form
        v-if="visibleMoney"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
  </div>
</template>

<script>
import {
  getBillDetail,
  entScanning,
  eventDone,
  remindEnt,
  sendBack,
  terminationContract,
  accountList,
  accountCreate,
  accountUpdate,
  accountDel,
  accountDetail
} from '../../api'
import BasicTab from './BasicTab'
import ColumnMixins from './column'
import ColumnDescriptor from './descriptor'
import { NumFormat } from '@/utils/tools'
export default {
  name: 'HandlingMatters',
  components: {
    BasicTab
  },
  props: {
    detail: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [ColumnMixins, ColumnDescriptor],
  data() {
    return {
      moneyData: [],
      visibleMoney: false,
      height: 'calc(100vh - 660px)',
      height2: 'calc(100vh - 580px)',
      shouldSettle: 0,
      shouldReturn: 0,
      thingTableData: [],
      cost: 1,
      tableData: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      time: '',
      visible: false,
      current: 1,
      loading: null,
      tabsData: [
        {
          label: '钱结清',
          value: 1,
          num: 0
        },
        {
          label: '事办完',
          value: 2,
          num: 0
        }
      ],
      billsPayableData: [],
      receivablesData: [],
      fromModel: {
        status: 1,
        type: 0,
        rcvAmtSdt: []
      }
    }
  },
  created() {
    this.accountListFn(this.$route.query.id)
  },
  methods: {
    accountListFn(id) {
      const data = {
        applyId: id,
        type: 0
      }
      accountList(data).then(res => {
        this.moneyData = res || []
      })
    },
    moneyEdit(row) {
      accountDetail(row.id).then(res => {
        const { attachMap } = res
        this.fromModel = {
          ...res,
          type: 0,
          amount: String(res.amount),
          attachIds: attachMap?.informationAttach || []
        }
        if (res.rcvAmtSdt) {
          this.fromModel.rcvAmtSdt = [res.rcvAmtSdt, res.rcvAmtEdt]
        } else {
          this.fromModel.rcvAmtSdt = []
        }
        this.visibleMoney = true
      })
    },
    moneyDel(row) {
      this.$confirm('确定要删除该条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        accountDel(row.id).then(() => {
          this.$message.success('删除成功')
          this.accountListFn(this.$route.query.id)
        })
      })
    },
    moneyConfirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { id, attachIds = [], rcvAmtSdt } = this.fromModel
          const attachId = attachIds.map(item => item.id)
          const data = {
            ...this.fromModel,
            attachIds: attachId
          }
          if (rcvAmtSdt && rcvAmtSdt.length > 0) {
            data.rcvAmtSdt = rcvAmtSdt[0]
            data.rcvAmtEdt = rcvAmtSdt[1]
          } else {
            delete data.rcvAmtSdt
            delete data.rcvAmtEdt
          }
          if (id) {
            accountUpdate(data).then(() => {
              this.$message.success('编辑成功')
              this.visibleMoney = false
              this.accountListFn(this.$route.query.id)
            })
          } else {
            data.applyId = this.$route.query.id
            accountCreate(data).then(() => {
              this.$message.success('添加成功')
              this.visibleMoney = false
              this.accountListFn(this.$route.query.id)
            })
          }
        }
      })
    },
    terminationContractFn() {
      this.$confirm('确定要终止合同吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        terminationContract(this.$route.query.id).then(() => {
          this.$message.success('终止成功')
          this.$emit('updateDetail')
          this.eventDone()
        })
      })
    },
    goDispose(row, type) {
      console.log(row, type)
      if (type) {
        this.$router.push({
          path: '/contract/contractCreate',
          query: {
            id: this.detail.contractId,
            applyId: this.$route.query.id,
            applyType: true,
            type: 'renew'
          }
        })
      } else {
        this.$router.push({
          path: '/contract/contractCreate',
          query: {
            applyId: this.$route.query.id,
            applyType: true
          }
        })
      }
    },
    previewEvent(row) {
      this.$router.push({
        path: '/contract/index/contractDetails',
        query: {
          id: row.contractId,
          orderId: row.orderId
        }
      })
    },
    sendBackFn(row) {
      sendBack({ applyId: this.$route.query.id, id: row.id, type: 0 }).then(
        () => {
          this.$message.success('退回成功')
          this.$emit('updateDetail')
        }
      )
    },
    remindEntFn(row) {
      remindEnt({ applyId: this.$route.query.id, id: row.id, type: 0 }).then(
        () => {
          this.$message.success('提醒成功')
        }
      )
    },
    goVerification(row) {
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id: row.totalId,
          type: row.urlType
        }
      })
    },
    meterReading(row) {
      if (row.type === 10) {
        this.$router.push({
          path: '/hydropower/waterMeter'
        })
      } else if (row.type === 11) {
        this.$router.push({
          path: '/hydropower/ammeter'
        })
      } else {
        this.$router.push({
          path: '/payment/accountsReceivable/accountsReceivableDetails',
          query: {
            id: row.totalId,
            type: row.urlType
          }
        })
      }
    },
    edit() {
      this.visible = true
      this.time = this.detail.actualQuitDate
    },
    flushed() {
      this.current === 1 ? this.getBillDetail() : this.eventDone()
      this.$message.success('刷新成功')
    },
    eventDone() {
      const { id } = this.$route.query
      eventDone(id).then(res => {
        this.thingTableData = res
      })
    },
    getBillDetail() {
      const { id } = this.$route.query
      getBillDetail(id).then(res => {
        if (res) {
          this.billsPayableData = res.billsPayable || []
          this.receivablesData = res.receivables || []
          this.tableData =
            this.cost === 1 ? res.receivables || [] : res.billsPayable || []
          this.shouldSettle = NumFormat(res.shouldSettle)
          this.shouldReturn = NumFormat(res.shouldReturn)
        }
      })
    },
    confirmDialog() {
      this.visible = false
      if (this.time) {
        const data = {
          actualTime: this.time,
          id: this.$route.query.id,
          type: 1
        }
        entScanning(data).then(res => {
          console.log(res)
          this.visible = false
          this.$emit('updateDetail')
          this.$message.success('修改成功')
        })
      } else {
        this.$message.error('请选择时间')
      }
    },
    tabsChange(e) {
      this.current = e
      this.$refs['drive-table'].doLayout()
    }
  },
  computed: {
    tableColumnFn() {
      if (this.cost === 3) {
        return this.tableColumnMoney
      } else {
        return this.tableColumn
      }
    },
    tableDataFn() {
      if (this.cost === 3) {
        return this.moneyData
      } else {
        return this.tableData
      }
    }
  },
  watch: {
    visibleMoney(val) {
      if (!val) {
        this.fromModel = {
          status: 1,
          type: 0
        }
      }
    },
    detail: {
      handler(val) {
        if (val.type === 2 && val.showMaking) {
          this.loading = val.showMaking
          this.getBillDetail()
        }
      },
      deep: true,
      immediate: true
    },
    current: {
      handler(val) {
        if (this.detail.type === 2 && this.detail.showMaking) {
          this.loading = this.detail.showMaking
          val === 1 ? this.getBillDetail() : this.eventDone()
        } else if (this.detail.type === 0 && val === 2) {
          this.loading = true
          this.eventDone()
        }
      },
      immediate: true
    },
    cost: {
      handler(val) {
        if (val === 1) {
          this.tableColumn[this.tableColumn.length - 2].hidden = true
          this.tableColumn[this.tableColumn.length - 3].hidden = false
          this.tableData = this.receivablesData
        } else {
          this.tableColumn[this.tableColumn.length - 2].hidden = false
          this.tableColumn[this.tableColumn.length - 3].hidden = true
          this.tableData = this.billsPayableData
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-radio-button .el-radio-button__inner) {
  padding: 8px 15px;
  font-size: 14px;
  width: 121px;
}
.edit-info {
  position: absolute;
  right: 0;
  top: 0;
  padding: 10px 20px;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #909399;
}
</style>
