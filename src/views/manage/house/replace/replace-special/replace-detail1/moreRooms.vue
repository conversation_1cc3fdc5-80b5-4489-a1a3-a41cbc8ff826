<template>
  <div>
    <div class="m-b-20 color-basic">基本信息</div>
    <el-descriptions class="margin-top" :column="2" size="medium" border>
      <el-descriptions-item>
        <template slot="label"> 调房类型 </template>
        <div>{{ replaceDetailInfo.detailsRoomTransfer.typeStr | noData }}</div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 意向园区 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.intendedPark | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 需求面积 </template>
        <div>
          {{ (replaceDetailInfo.detailsRoomTransfer.area + 'm²') | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 期望入驻日期 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.occupancyTime | noData }}
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <div class="m-b-20 m-t-20 color-basic">更多需求</div>
    <el-descriptions class="margin-top" :column="2" size="medium" border>
      <el-descriptions-item>
        <template slot="label"> 住房用途 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.housingUseStr | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 楼板承重 </template>
        <div>
          {{
            (replaceDetailInfo.detailsRoomTransfer.bearing + 'kg/m2') | noData
          }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 用电负荷 </template>
        <div>
          {{
            (replaceDetailInfo.detailsRoomTransfer.electricityLoad + '千瓦')
              | noData
          }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 排水需求 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.drainageNeeds | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 用水需求 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.waterDemand | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 环境影响 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.environment | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 影响因素 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.environmentFactor | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 融资需求 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.financing | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 可接受的融资方式 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.financingMethods | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 项目服务需求 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.serviceStr | noData }}
        </div>
      </el-descriptions-item>
      <el-descriptions-item>
        <template slot="label"> 服务需求 </template>
        <div>
          {{ replaceDetailInfo.detailsRoomTransfer.projectServices | noData }}
        </div>
      </el-descriptions-item>
    </el-descriptions>
    <div class="m-t-20 footer">
      <div class="m-b-12 color-basic">增房原因</div>
      <div class="line-3 line-height-22 color-basic">
        {{ replaceDetailInfo.detailsRoomTransfer.cause | noData }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MoreRooms',
  inject: ['replaceDetailInfo']
}
</script>

<style scoped lang="scss">
:deep(.is-bordered-label) {
  width: 230px;
  text-align: right;
  color: #999;
}
.color-basic {
  font-size: 14px;
  font-weight: 350;
  color: #434343;
}
.footer {
  max-height: 130px;
}
</style>
