<template>
  <basic-card class="min-h100">
    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
    />
    <drive-table
      ref="drive-table"
      :api-fn="getApplyParkPage"
      :columns="tableColumn"
      :extral-querys="extralQuerys"
      :searchQuerysHook="searchQuerysHook"
      @resetSearch="resetSearch"
      :isNeedPagination="true"
    >
      <template v-slot:operate-right>
        <el-button
          v-permission="routeButtonsPermission.REGISTER_TRANSFERS"
          @click="visible = true"
          type="primary"
          size="small"
          ><svg-icon icon-class="cloud-upload" /><span>
            {{ routeButtonsTitle.REGISTER_TRANSFERS }}
          </span></el-button
        >
        <el-button
          v-permission="routeButtonsPermission.LEADING_OUT"
          type="info"
          @click="getApplyParkExport"
          ><svg-icon icon-class="download" /><span>{{
            routeButtonsTitle.LEADING_OUT
          }}</span></el-button
        >
      </template>
    </drive-table>
    <transfer-drawer @updateTable="updateTable" :visible.sync="visible" />
  </basic-card>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import ColumnMixins from '@/views/manage/house/replace/replace-special/column/column'
import {
  getApplyParkExport,
  getApplyParkPage,
  getApplyParkStatistics,
  getApplyRoomTypes,
  getHousingMultiGroupTree,
  getPark
} from '@/views/manage/house/replace/replace-special/api'
import downloads from '@/utils/download'
import TransferDrawer from './components/TransferDrawer'
import dayjs from 'dayjs'
export default {
  name: 'ReplaceSpecial',
  components: {
    TransferDrawer,
    BasicTab
  },
  mixins: [ColumnMixins],
  data() {
    return {
      getApplyParkPage,
      current: 0,
      extralQuerys: {},
      list: [],
      visible: false
    }
  },
  created() {
    this.getPark()
    this.initTab()
    this.getApplyRoomTypes()
    this.getHousingMultiGroupTree()
  },
  methods: {
    // 项目分组数据处理
    groupChange(e) {
      if (!e || !e.length) return (this.extralQuerys.groupIds = [])
      const list = e.filter(item => item.length && item.length === 1)
      this.extralQuerys.groupIds = list.map(item => item[0])
    },
    updateTable() {
      this.initTab()
      this.$refs['drive-table'].refreshTable()
    },
    goEntDetail(row) {
      let { entId } = row
      if (!entId) return
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId
        }
      })
    },
    async initTab() {
      // 列表tab接口
      const res = await getApplyParkStatistics() // 列表tab
      this.list = res
    },
    async getPark() {
      // 获取园区
      const resPark = await getPark() // 列表所属园区-全部园区
      this.tableColumn[1].search.options = resPark.map(item => {
        return { value: item.id, label: item.park }
      })
    },
    // 项目分组下拉数据
    async getHousingMultiGroupTree() {
      const res = await getHousingMultiGroupTree()
      this.tableColumn[11].search.options = res || []
    },
    async getApplyRoomTypes() {
      // 列表调房类型表单
      const res = await getApplyRoomTypes()
      this.tableColumn[2].search.options = res
    },
    tabsChange(e) {
      // tab切换
      this.$refs['drive-table'].PAGE_NUMBER = 1
      this.current = e
      this.extralQuerys.status = e
      this.$refs['drive-table'].refreshTable()
    },
    resetSearch() {
      // 列表搜索重置
      this.extralQuerys.status = this.current
    },
    searchQuerysHook(e) {
      // 列表搜索
      const { applyDate } = e
      if (applyDate && applyDate.length) {
        const [applyDateStart, applyDateEnd] = e.applyDate
        delete e.applyDate
        return {
          ...e,
          applyDateEnd,
          applyDateStart
        }
      } else {
        return e
      }
    },
    detailHandler(row) {
      // 查看跳转详情
      this.$router.push({
        path: 'index/replaceDetails',
        query: {
          id: row.id,
          orderId: row.orderId
        }
      })
    },
    getApplyParkExport() {
      // 导出
      const { groupIds = [] } = this.$refs['drive-table'].querys
      const params = {
        ...this.$refs['drive-table'].querys,
        status: this.current
      }
      if (!groupIds || !groupIds.length) {
        params.floorId = []
      } else {
        const list = groupIds.filter(item => item.length && item.length === 1)
        params.groupIds = list.map(item => item[0])
      }
      downloads.requestDownloadPost(
        getApplyParkExport(),
        params,
        'excel',
          dayjs().format('YYYY-MM-DD') + '调房管理.xls'
      )
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-date-editor .el-range__icon {
    position: absolute;
    right: 0;
    top: 1px;
  }
}
</style>
