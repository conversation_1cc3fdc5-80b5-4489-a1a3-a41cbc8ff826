<template>
  <div class="min-h100">
    <detail-left
      v-if="isReady"
      ref="detailLeft"
      :detail-data="detailData"
      @getNewList="init"
      @changeDetail="changeDetail"
    />
  </div>
</template>

<script>
import DetailLeft from './detail-left'
import { getRecordDetail } from '../api'
export default {
  name: 'ContractDetail',
  components: { DetailLeft },
  data() {
    return {
      id: this.$route.query.id,
      detailData: {},
      isReady: false
    }
  },
  created() {
    if (this.id) {
      this.init()
    }
  },
  provide() {
    return {
      contractDetail: this
    }
  },
  methods: {
    async changeDetail(id, orderId) {
      this.$router.push({
        path: 'contractDetails',
        query: {
          id,
          orderId
        }
      })
      const res = await getRecordDetail({ id })
      this.detailData = res
      if (this.$refs.detailLeft) this.$refs.detailLeft.detailData = res
    },
    async init() {
      const res = await getRecordDetail({ id: this.id })
      this.detailData = res
      this.isReady = true
    }
  }
}
</script>

<style scoped></style>
