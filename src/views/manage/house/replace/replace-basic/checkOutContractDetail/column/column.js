import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      subsTableColumn: [
        {
          prop: 'contractNo',
          label: '协议编号',
          minWidth: '150px',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <el-link
                style="display:inline"
                type="primary"
                onClick={() => {
                  this.detailHandle(scope.row)
                }}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          prop: 'statusStr',
          label: '状态',
          width: '120px'
        },
        {
          prop: 'park',
          label: '园区',
          minWidth: '150px'
        },
        {
          prop: 'area',
          label: '协议面积(㎡)',
          width: '120px'
        },
        {
          prop: 'typeStr',
          label: '协议类型',
          width: '120px'
        },
        {
          prop: 'signDate',
          label: '签订日期',
          width: '120px'
        },
        {
          prop: 'startTime',
          label: '起始时间',
          width: '120px'
        },
        {
          prop: 'endTime',
          label: '截止时间',
          width: '120px'
        }
      ],
      mainTableColumn: [
        {
          prop: 'contractNo',
          label: '合同编号',
          minWidth: '150px',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <el-link
                style="display:inline"
                type="primary"
                onClick={() => {
                  this.detailHandle(scope.row)
                }}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          prop: 'statusStr',
          label: '状态',
          width: '120px'
        },
        {
          prop: 'park',
          label: '园区',
          minWidth: '150px'
        },
        {
          prop: 'area',
          label: '合同面积(㎡)',
          width: '120px'
        },
        {
          prop: 'typeStr',
          label: '合同类型',
          width: '120px'
        },
        {
          prop: 'signDate',
          label: '签订日期',
          width: '120px'
        },
        {
          prop: 'startTime',
          label: '起始时间',
          width: '120px'
        },
        {
          prop: 'endTime',
          label: '截止时间',
          width: '120px'
        }
      ],
      tableColumn: [
        {
          label: '楼栋',
          prop: 'building'
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '费用类型',
          prop: 'feeName'
        },
        {
          label: '时间',
          prop: 'time'
        },
        {
          label: '租金标准',
          prop: 'fixAmount',
          render: (h, { row }) => {
            return (
              <div>{row.fixAmount ? `${row.fixAmount} ${row.unit}` : '-'}</div>
            )
          }
        }
      ],
      tableColumn1: [
        {
          label: '楼栋',
          prop: 'building'
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '合同面积(m²)',
          prop: 'executeArea'
        },
        {
          label: '起租日',
          prop: 'addTime'
        },
        {
          label: '优惠期间',
          prop: 'freeStartTime',
          width: 220,
          render: (h, scope) => {
            const freeList = scope.row.freeList || []
            return (<div>
              {freeList.map(item => {
                return (
                  <div>{item.freeStartDate} ~ {item.freeEndDate}</div>
                )
              })}
            </div>)
          }
        },
        {
          label: '退租日',
          prop: 'earlyTime'
        },
        {
          label: '是否提前退租',
          prop: 'early',
          render: (h, scope) => {
            return <span>{scope.row.early ? '是' : '否'}</span>
          }
        }
      ],
      // 缴费计划
      tableColumnPlan: [
        {
          label: '期数',
          prop: 'period',
          render: (h, scope) => {
            return <div class={'inline-block'}>
              <div class={'flex align-items-center'}>
                <div>{scope.row.period}</div>
                {/*{*/}
                {/*  scope.row.changeFlag && (*/}
                {/*    <el-tag*/}
                {/*      class={ 'm-l-4 pointer flex align-items-center' }*/}
                {/*      type={ 'warning' }*/}
                {/*      onClick={ () => {*/}
                {/*        this.changeRecordHandle(scope.row)*/}
                {/*      }}*/}
                {/*    >*/}
                {/*      <span>变更 ></span>*/}
                {/*      <el-tooltip*/}
                {/*        effect="dark"*/}
                {/*        content="系统基于合同期限的年份向下取整，如2年6个月，则为保证金需2个月"*/}
                {/*        placement="top"*/}
                {/*      >*/}
                {/*        <div class="m-l-4">*/}
                {/*          <svg-icon icon-class="help-circle"/>*/}
                {/*        </div>*/}
                {/*      </el-tooltip>*/}
                {/*    </el-tag>*/}
                {/*  )*/}
                {/*}*/}
              </div>
            </div>
          }
        },
        {
          label: '租金(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
                <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '开始时间',
          prop: 'startDate'
        },
        {
          label: '结束时间',
          prop: 'endDate'
        }
      ],
      // 变更记录
      tableColumnPlanChangeRecord: [
        {
          label: '类型',
          prop: 'changeType'
        },
        {
          label: '期数',
          prop: 'period',
        },
        {
          label: '租金(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '开始时间',
          prop: 'startDate'
        },
        {
          label: '结束时间',
          prop: 'endDate'
        },
        {
          prop: 'planStatusStr',
          label: '出账状态',
          render: (h, scope) => {
            const obj = {
              0: 'warning',
              1: 'success',
              2: 'danger',
              3: 'primary'
            }
            return (
              <basic-tag label={scope.row.planStatusStr} type={obj[scope.row.planStatus]}/>
            )
          }
        },
      ],
      tableColumnCost: [
        {
          label: '费用名称',
          prop: 'name'
        },
        {
          label: '计划单价',
          prop: 'price',
          width: 180,
          render: (h, scope) => {
            return <div>{scope.row.price + scope.row.unit}</div>
          }
        },
        {
          label: '修正后执行单价(元)',
          prop: 'fixPrice',
          width: 180,
          render: (h, scope) => {
            return <div>{scope.row.fixPrice + scope.row.unit}</div>
          }
        },
        {
          label: '同比',
          prop: 'ratio',
          width: 180,
          render: (h, scope) => {
            return (
              <div
                class={
                  scope.row.ratioValue < 0 ? 'color-danger' : 'color-success'
                }
              >
                {scope.row.ratio}
              </div>
            )
          }
        },
        {
          label: '修正原因',
          prop: 'reason'
        }
      ],
      tableColumnHouse: [
        {
          label: '位置',
          prop: 'buildRoom',
          width: 180
        },
        {
          label: '系统面积(m²)',
          prop: 'area',
          width: 180
        },
        {
          label: '修正后面积(m²)',
          prop: 'acArea',
          width: 180
        },
        {
          label: '同比',
          prop: 'percentage',
          width: 180,
          render: (h, scope) => {
            return (
              <div class={'color-success'}>{scope.row.percentage * 100}%</div>
            )
          }
        },
        {
          label: '修正时间',
          prop: 'createTime',
          width: 180
        },
        {
          label: '操作人',
          prop: 'opUserName',
          width: 180
        },
        {
          label: '修正原因',
          prop: 'reason',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <el-popover placement="bottom-end" width="300" trigger="click">
                <div class={'p-8 w100'} style={'border: 1px solid #DCDCDC;'}>
                  {scope.row.reason === '' ? '暂无修正原因~' : scope.row.reason}
                </div>
                {/*<div class={'p-8 w100'} v-show={scope.row.reason === null || scope.row.reason === ''}*/}
                {/*     style={'border: 1px solid #DCDCDC;'}>暂无修正原因~</div>*/}
                <el-button slot="reference" type="text">
                  查看
                </el-button>
              </el-popover>
            )
          }
        }
      ],
      roomColumns: [
        {
          label: '楼栋',
          prop: 'periodNumber'
        },
        {
          label: '房号',
          prop: 'planTypeStr'
        },
        {
          label: '合同面积(m²)',
          prop: 'periodNumber'
        },
        {
          label: '起租日',
          prop: 'addTime'
        },
        {
          label: '优惠开始日',
          prop: 'freeStartTime'
        },
        {
          label: '优惠结束日',
          prop: 'freeEndTime'
        },
        {
          label: '退租日',
          prop: 'earlyTim'
        }
      ],
      unitPriceColumns: [
        {
          label: '楼栋',
          prop: 'periodNumber'
        },
        {
          label: '房号',
          prop: 'planTypeStr'
        },
        {
          label: '费用类型',
          prop: 'periodNumber'
        },
        {
          label: '时间',
          prop: 'planTypeStr'
        },
        {
          label: '租金标准',
          prop: 'periodNumber'
        }
      ],
      expensesColumns: [
        {
          label: '费用详情',
          prop: 'feeName'
        },
        {
          label: '收费标准',
          prop: 'sysFee'
        },
        {
          label: '修正后的费用',
          prop: 'newFee'
        }
      ]
    }
  }
}
