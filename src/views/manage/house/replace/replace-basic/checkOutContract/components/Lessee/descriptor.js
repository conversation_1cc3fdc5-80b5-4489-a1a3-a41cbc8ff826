import { validateContact } from '@/utils/validate'

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '100px',
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '承租方',
            span: 8,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入承租方'
              }
            ]
          },
          contacts: {
            form: 'select',
            label: '联系人',
            span: 8,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人'
              }
            ],
            attrs: {
              filterable: true,
              allowCreate: true,
              defaultFirstOption: true,
            },
            options: [],
            events: {
              change: this.contactsChange
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            span: 8,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              { validator: validateContact }
            ]
          }
        }
      }
    }
  }
}
