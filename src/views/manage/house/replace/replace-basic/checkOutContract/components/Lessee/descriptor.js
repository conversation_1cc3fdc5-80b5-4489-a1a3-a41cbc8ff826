import { validateContact } from '@/utils/validate'

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '100px',
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '承租方',
            span: 8,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入承租方'
              }
            ]
          },
          contacts: {
            form: 'suggestion',
            label: '联系人',
            span: 8,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系人姓名'
              }
            ],
            attrs: {
              maxLength: 20,
              fetchSuggestions: this.fetchContacts
            },
            events: {
              select: this.selectContact
            },
            scopedSlots: {
              default: props => {
                const h = this.$createElement
                return h('div', [
                  h('div', `${props.item.value}/${props.item.phone}`),
                ])
              }
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            span: 8,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              { validator: validateContact }
            ]
          }
        }
      }
    }
  }
}
