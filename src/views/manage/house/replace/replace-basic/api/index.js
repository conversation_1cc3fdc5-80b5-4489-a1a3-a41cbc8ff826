import request from '@/utils/request'

// 动态表头获取
export function getFeePriceTitles(type) {
  return request({
    url: `/bs/contract-fee-price/titles/${type}`,
    method: 'get'
  })
}
// 企业列表
export function reviseApplyEntList(params) {
  return request({
    url: `/revise/apply/park/ent_list`,
    method: 'get',
    params
  })
}
// 调房登记
export function reviseApplySubmit(data) {
  return request({
    url: `/revise/apply/park/submit`,
    method: 'post',
    data
  })
}
export function getAdditional() {
  return request({
    url: `/contract/record/get_additional`,
    method: 'get'
  })
}
export function recordRoom(data) {
  return request({
    url: `/contract/record/get_room`,
    method: 'post',
    data
  })
}
// 获取列表
export function reviseApplyPage(data) {
  return request({
    url: `/revise/apply/park/page`,
    method: 'post',
    data
  })
}
// 获取状态
export function reviseApplyStatus(params) {
  return request({
    url: `/revise/apply/park/get_status`,
    method: 'get',
    params
  })
}
// 调房统计
export function reviseApplyStatistics(data) {
  return request({
    url: `/revise/apply/park/statistics`,
    method: 'post',
    data
  })
}
// 获取合同
export function reviseApplyContracts(params) {
  return request({
    url: `/revise/apply/park/contracts`,
    method: 'get',
    params
  })
}
// 监测企业是否有进行中的调房或离园业务
export function reviseApplyCheckRevise(params) {
  return request({
    url: `/revise/apply/admin/event/check_revise`,
    method: 'get',
    params
  })
}
// 获取退租房间
export function reviseApplyRooms(contractId) {
  return request({
    url: `/revise/apply/park/rooms/${contractId}`,
    method: 'get'
  })
}
// 获取详情
export function reviseApplyDetail(id) {
  return request({
    url: `/revise/apply/park/detail/${id}`,
    method: 'get'
  })
}
// 获取添加的办理事项列表
export function getProjectMatter(params) {
  return request({
    url: '/pjct/matter/getByProjectId',
    method: 'get',
    params
  })
}
// 创建添加的办理事项
export function createProjectMatter(data) {
  return request({
    url: '/pjct/matter/create',
    method: 'post',
    data
  })
}

// 添加的办理记录
export function createProjectMatterHandle(data) {
  return request({
    url: '/pjct/matter/handle',
    method: 'put',
    data
  })
}

// 获取事项办理
export function reviseApplyTransferMatters(id) {
  return request({
    url: `/revise/apply/admin/event/transfer_matters/${id}`,
    method: 'get'
  })
}

// 获取下发事项
export function getCommonMatter(params) {
  return request({
    url: `/hatch/matter/record/get_common_matter`,
    method: 'get',
    params
  })
}
// 创建下发事项
export function getRecordMatter(data) {
  return request({
    url: `/hatch/matter/record/create`,
    method: 'post',
    data
  })
}
// 撤回接口
export function revocation(data) {
  return request({
    url: `/hatch/matter/record/cancel_matter?recordId=${data}`,
    method: 'get'
  })
}
// 通知企业
export function noticeEnterprise(params) {
  return request({
    url: `/revise/apply/admin/event/notice_enterprise`,
    method: 'get',
    params
  })
}
// 上传确认单
export function uploadConfirm(data) {
  return request({
    url: `/revise/apply/admin/event/upload_confirm`,
    method: 'post',
    data
  })
}
// 上传确认单
export function reviseUploadConfirm(data) {
  return request({
    url: `/zacg/enter/contract/revise_upload_confirm`,
    method: 'post',
    data
  })
}

// 合同选择
export function reviseApplyGetEntSelect(id) {
  return request({
    url: `revise/apply/admin/event/get_ent_select/${id}`,
    method: 'get'
  })
}
// 通知企业联系人
export function reviseApplyNoticePay(params) {
  return request({
    url: `revise/apply/admin/event/notice_pay`,
    method: 'get',
    params
  })
}
// 申请信息履约总计
export function reviseApplyParkTotalFeeInfo(params) {
  return request({
    url: `revise/apply/park/total_fee_info`,
    method: 'get',
    params
  })
}
// 申请信息履约明细
export function reviseApplyParkTotalFeeDetail(params) {
  return request({
    url: `revise/apply/park/total_fee_detail`,
    method: 'get',
    params
  })
}
// 账单详情
export function getReviseApplyBillDetail(applyId) {
  return request({
    url: `/revise/apply/admin/event/bill_detail?applyId=${applyId}`,
    method: 'get'
  })
}

// 导出结算单
export function reviseApplyBillDetailExport() {
  return `${process.env.VUE_APP_URL_PREFIX}/revise/apply/admin/event/bill_detail_export`
}

// 获取修改账单的房间列表信息
export function reviseApplyGetRoomStatus(params) {
  return request({
    url: `/revise/apply/admin/event/get_room_status`,
    method: 'get',
    params
  })
}
// 企业扫描接口
export function entScanning(data) {
  return request({
    url: `/revise/apply/park/enterprise_scanning`,
    method: 'post',
    data
  })
}

// 修改账单金额
export function reviseApplyModifyRoomStatus(data) {
  return request({
    url: `/revise/apply/admin/event/modify_room_status`,
    method: 'post',
    data
  })
}
// 修改保证金
export function reviseApplyModifyDeposit(data) {
  return request({
    url: `/revise/apply/admin/event/modify_deposit`,
    method: 'post',
    data
  })
}

// 合同保存
export function contractCreate(data) {
  return request({
    url: `/contract/refactor/record/create_cooperation`,
    method: 'post',
    data
  })
}

// 合同提交审批
export function contractSubmit(params) {
  return request({
    url: `/contract/record/submit`,
    method: 'get',
    params
  })
}

// 合同更新
export function contractUpdate(data) {
  return request({
    url: `/contract/refactor/record/update`,
    method: 'post',
    data
  })
}

// 获取合同详情
export function getContractDetail(params) {
  return request({
    url: `/contract/record/get_detail`,
    method: 'get',
    params
  })
}

// 获取签约主体详情
export function getRecordBodyDetail(params) {
  return request({
    url: `/contract/record/select_body_by_id`,
    method: 'get',
    params
  })
}

// 获取房间合同
export function getRoomContractList(data) {
  return request({
    url: `/fee/type/contract/get_rooms`,
    method: 'post',
    data
  })
}

// 合同起止时间
export function getStartTime(data) {
  return request({
    url: `/contract/record/room_time`,
    method: 'post',
    data
  })
}

// 获取园区列表
export function getParkSelect(params) {
  return request({
    url: `/housing/park/select`,
    method: 'get',
    params
  })
}

// 合同选房-获取园区下所有房间
export function getRecordParkRoom(params) {
  return request({
    url: `/contract/room_selection/park_room`,
    method: 'get',
    params
  })
}

// 生成合同编号
export function getContactNumber(params) {
  return request({
    url: `/contract/record/gen_contract_number`,
    method: 'get',
    params
  })
}

// 获取合同模板
export function getContractTemplate(params) {
  return request({
    url: `/contract/record/get_template`,
    method: 'get',
    params
  })
}

// 获取优惠/免租期数
export function getFreePeriod(data) {
  return request({
    url: `/contract/refactor/record/gen_free_period`,
    method: 'post',
    data
  })
}

// 获取历史保证金
export function getHistoryDeposit(params) {
  return request({
    url: `/contract/refactor/record/get_history_deposit`,
    method: 'get',
    params
  })
}

// 预览缴费计划
export function getFeePlanView(data) {
  return request({
    url: `/contract/refactor/record/gen_fee_plan_view`,
    method: 'post',
    data
  })
}
// 合同详情
export function getRecordDetail(params) {
  return request({
    url: `/contract/record/get_detail`,
    method: 'get',
    params
  })
}
// 获取合同缴费计划
export function getPlan(params) {
  return request({
    url: `/contract/refactor/record/get_plan`,
    method: 'get',
    params
  })
}

// 获取合同缴费协议
export function getPlanAgreement(data) {
  return request({
    url: `/contract/refactor/record/gen_fee_plan_view`,
    method: 'post',
    data
  })
}

// 获取合同缴费变更计划
export function getHistoryPlan(params) {
  return request({
    url: `/contract/refactor/record/get_history_plan`,
    method: 'get',
    params
  })
}


export function getContractNoHistory(params) {
  return request({
    url: `/contract/refactor/record/contractNo_history?id=${params}`,
    method: 'get',
  })
}
