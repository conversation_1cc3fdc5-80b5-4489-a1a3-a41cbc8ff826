<template>
  <div class="half-container wh100">
    <div class="half-header flex justify-content-between align-items-center">
      <div class="half-title">
        {{ findInfo.showActual ? '结算单' : '结算单预览' }}
      </div>
      <div class="flex align-items-center">
        <div
          v-if="!findInfo.showActual"
          class="font-size-14 line-height-23 color-info m-r-8"
        >
          以下数据跟据：{{ detailInfo.actualQuitDate | noData }}实际减房计算所得
        </div>
        <div class="font-size-14 line-height-23 color-info m-r-8" v-else>
          以下数据基于假设性：{{
            detailInfo.actualQuitDate | noData
          }}时间减房计算所得
        </div>
        <el-link
          v-if="findInfo.showActual"
          type="primary"
          @click="updateTimeHandle"
        >
          更改时间
        </el-link>
      </div>
    </div>
    <div class="m-t-16 flex align-items-center justify-content-between">
      <el-radio-group v-model="tabValue">
        <el-radio-button
          v-for="item in tabData"
          :key="item.value"
          :label="item.value"
        >
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>
      <div class="flex align-items-center font-size-12 color-info">
        <svg-icon icon-class="info-circle-filled" />
        <span class="p-l-4">部分存量合同的账单应收金额可修改</span>
        <el-link type="primary" @click="downloadHandle">下载结算单</el-link>
      </div>
    </div>
    <div class="m-t-16" v-if="tabValue === 1">
      <drive-table
        :columns="totalTableColumn"
        :table-data="totalTableData"
        :span-method="totalSpanMethod"
      />
      <div class="tips-wrapper m-t-8">
        <div class="font-size-14 color-black">温馨提示</div>
        <div>各费项的详细内容可在明细中查看</div>
        <div>
          {{ amountTotal > 0 ? '企业总共需缴纳' : '共需向企业退款' }}
          <span class="color-warning"
            >{{ NumFormat(Math.abs(amountTotal)) }}元 </span
          >即可完成在该时间点租赁调整的所有费用结算
        </div>
      </div>
    </div>
    <template v-else>
      <el-collapse v-model="collapseVal" class="m-t-16">
        <el-collapse-item name="1">
          <template slot="title">
            <div class="w100 flex justify-content-between align-items-center">
              <div
                  class="flex align-items-center font-size-14 line-height-20 color-info"
              >
                <span class="m-r-8">账单应收款</span>
                <el-tooltip
                    class="sort-item"
                    effect="dark"
                    content="只统计已出账账单"
                    placement="top"
                >
                  <svg-icon class="pointer" icon-class="help-circle-filled" />
                </el-tooltip>
              </div>
              <div class="flex align-items-center m-r-20">
                <span class="font-size-14">合计：</span>
                <span class="font-size-18 color-warning"
                >￥{{ NumFormat(findInfo.shouldSettle) }}</span
                >
              </div>
            </div>
          </template>
          <drive-table
              ref="drive-table"
              class="m-t-16"
              :columns="tableColumn"
              :table-data="findInfo.receivables || []"
          />
        </el-collapse-item>
        <el-collapse-item name="2">
          <template slot="title">
            <div class="w100 flex justify-content-between align-items-center">
              <div
                  class="flex align-items-center font-size-14 line-height-20 color-info"
              >
                <span class="m-r-8">账单应退款</span>
                <el-tooltip
                    class="sort-item"
                    effect="dark"
                    content="只统计已出账账单"
                    placement="top"
                >
                  <svg-icon class="pointer" icon-class="help-circle-filled" />
                </el-tooltip>
              </div>
              <div class="flex align-items-center m-r-20">
                <span class="font-size-14">合计：</span>
                <span class="font-size-18 color-warning"
                >￥{{ NumFormat(findInfo.shouldReturn) }}</span
                >
              </div>
            </div>
          </template>
          <drive-table
              ref="table-refund"
              class="m-t-16"
              :columns="tableColumnRefund"
              :table-data="findInfo.billsPayable || []"
          />
        </el-collapse-item>
        <el-collapse-item name="3">
          <template slot="title">
            <div class="w100 flex justify-content-between align-items-center">
              <div
                  class="flex align-items-center font-size-14 line-height-20 color-info"
              >
                <span class="m-r-8">其他款项</span>
              </div>
              <div class="flex align-items-center m-r-20">
                <span class="font-size-14">合计：</span>
                <span class="font-size-18 color-warning"
                >￥{{ NumFormat(findInfo.otherSum) }}</span
                >
              </div>
            </div>
          </template>
          <drive-table
              ref="table-other-funds"
              class="m-t-16"
              :columns="tableColumnOtherFunds"
              :table-data="findInfo.otherBills || []"
          />
        </el-collapse-item>
      </el-collapse>
      <div class="flex justify-content-between align-items-center m-t-16">
        <div class="font-size-14 line-height-20 color-info">
          <span class="m-r-8">保证金结算</span>
          <el-tooltip
            class="sort-item"
            effect="dark"
            content="保证金结算需走企业账户抵扣流程及付款申请进行结算"
            placement="top"
          >
            <svg-icon class="pointer" icon-class="help-circle-filled" />
          </el-tooltip>
        </div>
        <div class="flex align-items-center">
          <span class="font-size-14">合计：</span>
          <span class="font-size-18 color-warning"
            >￥{{ NumFormat(depositRefund) }}</span
          >
        </div>
      </div>
      <div class="check-out-contract m-t-16 flex align-items-center justify-content-between font-size-14">
        <div class="flex align-items-center">
          <div>
            <span>当前退房合同保证金：</span>
            <span class="color-warning">
            ￥{{ NumFormat(currentDeposit.depositBase || 0) }}
          </span>
          </div>
          <div class="m-l-16">
            <span>退房后保证金：</span>
            <span class="color-warning">
            ￥{{ NumFormat(currentDeposit.afterDepositBase || 0) }}
          </span>
          </div>
        </div>
        <el-link v-if="findInfo.showActual" :disabled="detailInfo.quitType === 1" type="primary" class="m-l-8" @click="depositEditHandle">修改</el-link>
      </div>
      <drive-table
        ref="table-refund"
        class="m-t-8"
        :columns="tableColumnMarginRefund"
        :table-data="deposit"
      />
    </template>
    <!--选择时间弹框-->
    <dialog-cmp
      title="选择时间"
      width="360px"
      :visible.sync="visible"
      @confirmDialog="confirmDialog"
    >
      <div class="w100">
        <el-date-picker
          class="w100"
          style="width: 100%"
          v-model="time"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          :clearable="false"
        />
      </div>
    </dialog-cmp>
    <dialog-cmp
      title="修改账单应收金额"
      width="1230px"
      :visible.sync="receivableVisible"
      @confirmDialog="receivableConfirmDialog"
    >
      <el-alert
        class="m-b-16"
        type="warning"
        :closable="false"
        title="系统提示："
        description="下方为当前账单下各房间的费用情况，如需调整，请请仔细核对修改的账单信息，此数据的修改将影响园区后续整体运营数据的统计"
      />
      <drive-table
        :columns="receivableTableColumn"
        :table-data="receivableTableData"
      />
    </dialog-cmp>
    <dialog-cmp
        title="修改保证金金额"
        width="420px"
        :visible.sync="depositVisible"
        :haveOperation="false"
    >
      <div class="flex align-items-center font-size-14">
        <span style="width: 140px">当前退房合同保证金：</span>
        <span class="color-warning">
            ￥{{ NumFormat(currentDeposit.depositBase || 0) }}
          </span>
      </div>
      <div class="m-t-16 flex align-items-center font-size-14">
        <span style="width: 140px;flex-shrink: 0">退房后保证金：</span>
        <el-input
          v-model="depositModel.afterDepositBase"
          placeholder="请输入退房后保证金"
          @input="(val) => positiveIntegerInput(val, depositModel, 'afterDepositBase')"
          @blur="(event) => positiveIntegerBlur(event, depositModel, 'afterDepositBase')"
          :maxlength="15"
        >
          <span slot="append">元</span>
        </el-input>
      </div>
      <template v-slot:footer>
        <el-button @click="depositVisible = false">取消</el-button>
        <el-button type="success" @click="depositRestoreHandle">还原保证金</el-button>
        <el-button type="primary" @click="depositConfirmDialog(true)">确定</el-button>
      </template>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
import DescriptorMixin from './descriptor'
import {
  getReviseApplyBillDetail,
  entScanning,
  reviseApplyBillDetailExport,
  reviseApplyGetRoomStatus,
  reviseApplyModifyRoomStatus, reviseApplyModifyDeposit
} from '../../../api'
import { formatGetParams, NumFormat } from '@/utils/tools'
import downloads from '@/utils/download'

export default {
  name: 'Addition',
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [ColumnMixins, DescriptorMixin],
  data() {
    return {
      tabValue: 1,
      tabData: [
        { label: '总计', value: 1 },
        { label: '明细', value: 2 }
      ],
      NumFormat,
      visible: false,
      time: '',
      pickerOptions: {
        // disabledDate(time) {
        //   return time.getTime() < Date.now() - 8.64e7
        // }
      },
      findInfo: {
        showActual: false
      },
      totalTableData: [],
      receivableVisible: false,
      receivableTableData: [],
      amountTotal: 0,
      mergeObj: {}, // 用来记录需要合并行的下标
      mergeArr: ['feeTypeName'], // 表格中的列名
      collapseVal: ['1', '2', '3'], // collapse默认展开项
      depositVisible: false,
      depositModel: {
        afterDepositBase: '' // 退房后保证金
      }
    }
  },
  computed: {
    currentDeposit() {
      return this.findInfo.currentDeposit || {}
    },
    deposit() {
      const deposit = this.findInfo.deposit || {}
      return JSON.stringify(deposit) === '{}' ? [] : [deposit]
    },
    depositRefund() {
      return this.deposit.length ? this.deposit[0].refund : 0
    }
  },
  inject: ['ReplaceDetail'],
  created() {
    this.getReviseApplyBillDetail()
  },
  activated() {
    this.getReviseApplyBillDetail()
  },
  methods: {
    // 还原保证金
    depositRestoreHandle() {
      this.$confirm('是否还原保证金？').then(() => {
        this.depositConfirmDialog(false)
      })
    },
    // 确定修改保证金
    depositConfirmDialog(flag) {
      if (!this.depositModel.afterDepositBase) return this.$toast.warning('请输入退房后保证金')
      const params = {
        applyId: this.$route.query.id,
        flag
      }
      if (flag) {
        params.amount = this.depositModel.afterDepositBase
      }
      reviseApplyModifyDeposit(params).then(() => {
        this.$toast.success(flag ? '修改成功' : '还原成功')
        this.getReviseApplyBillDetail()
        this.depositModel = this.$options.data().depositModel
        this.depositVisible = false
      })
    },
    // 修改保证金弹窗
    depositEditHandle() {
      this.depositModel.afterDepositBase = this.currentDeposit.afterDepositBase || ''
      this.depositVisible = true
    },
    positiveIntegerInput(value, row, key) {
      // 去除非数字和小数点字符
      value = value.replace(/[^\d.]/g, '')

      // 只允许一个小数点
      const parts = value.split('.')
      if (parts.length > 2) {
        value = parts[0] + '.' + parts[1]
      }

      // 限制最多两位小数
      if (parts[1]) {
        parts[1] = parts[1].substring(0, 2)
        value = parts[0] + '.' + parts[1]
      }

      // 正则匹配合法格式（非负 + 最多两位小数）
      const match = value.match(/^(\d*)(\.?\d{0,2})?$/)
      if (match) {
        let intPart = match[1]
        let decimalPart = match[2] || ''

        // 限制整数部分前导0：只能是单个0，或非0开头
        if (intPart.length > 1 && intPart.startsWith('0')) {
          intPart = intPart.replace(/^0+/, '') || '0'
        }

        value = intPart + decimalPart
      } else {
        value = ''
      }

      row[key] = value
    },

    positiveIntegerBlur(event, row, key) {
      let value = event.target.value
      const invalidStr = ['', '.', '-.']

      // 小于0或非法则清空
      if (invalidStr.includes(value) || value.charAt(0) === '-' || parseFloat(value) < 0) {
        value = ''
      }

      row[key] = value
    },
    downloadHandle() {
      const params = {
        applyId: this.$route.query.id
      }
      let url = reviseApplyBillDetailExport() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(
        url,
        'word',
        this.detailInfo.entName + '租赁调整结算单.docx'
      )
    },
    // 账单修改确定
    receivableConfirmDialog() {
      // const row = this.receivableTableData.find(item => !item.modifyAmount)
      // if (row) return this.$toast.warning('请填写实际应收账单金额')
      const list = this.receivableTableData.map(item => {
        return {
          id: item.id,
          modifyAmount: item.modifyAmount
        }
      })
      reviseApplyModifyRoomStatus(list).then(() => {
        this.$toast.success('修改成功')
        this.getReviseApplyBillDetail()
        this.receivableVisible = false
      })
    },
    async editHandle(row) {
      const params = {
        applyId: this.$route.query.id,
        planId: row.planId
      }
      const res = await reviseApplyGetRoomStatus(params)
      this.receivableTableData = res || []
      this.receivableVisible = true
    },
    getSpanArr(data) {
      this.mergeArr.forEach(key => {
        let count = 0 // 用来记录需要合并行的起始位置
        this.mergeObj[key] = [] // 记录每一列的合并信息
        data.forEach((item, index) => {
          // index == 0表示数据为第一行，直接 push 一个 1
          if (index === 0) {
            this.mergeObj[key].push(1)
          } else {
            // 判断当前行是否与上一行其值相等 如果相等 在 count 记录的位置其值 +1 表示当前行需要合并 并push 一个 0 作为占位
            if (item[key] && item[key] === data[index - 1][key]) {
              this.mergeObj[key][count] += 1
              this.mergeObj[key].push(0)
            } else {
              // 如果当前行和上一行其值不相等
              count = index // 记录当前位置
              this.mergeObj[key].push(1) // 重新push 一个 1
            }
          }
        })
      })
    },
    totalSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (row.type === 'total') {
        if (columnIndex === 0) {
          return [1, 2]
        } else if (columnIndex === 1) {
          return [0, 0]
        }
      }
      // 判断列的属性
      if (this.mergeArr.indexOf(column.property) !== -1) {
        // 判断其值是不是为0
        if (this.mergeObj[column.property][rowIndex]) {
          return [this.mergeObj[column.property][rowIndex], 1]
        } else {
          // 如果为0则为需要合并的行
          return [0, 0]
        }
      }
    },
    marginRefundDetail(row) {
      this.$router.push({
        path: '/account/depositCollection',
        query: {
          entId: row.entId,
          depositId: row.depositId
        }
      })
    },
    meterReading(row) {
      if (!row.id) return this.$toast.warning('当前账单暂未出账')
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id: row.id,
          type: 1
        }
      })
    },
    // 获取应收款/应退款
    async getReviseApplyBillDetail() {
      let applyId = this.$route.query.id
      const res = await getReviseApplyBillDetail(applyId)
      this.findInfo = res || {}
      const list = res.list || []
      const amountTotal = list.reduce((total, item) => {
        return total + Number(item.amount || 0) * 100
      }, 0)
      this.amountTotal = amountTotal / 100
      const obj = {
        feeTypeName: '合计',
        type: 'total',
        amount: this.amountTotal
      }
      this.totalTableData = [...list, obj]
      this.getSpanArr(this.totalTableData)
    },
    updateTimeHandle() {
      this.time = this.detailInfo.actualQuitDate
      this.visible = true
    },
    // 更新时间 - 提交
    confirmDialog() {
      this.visible = false
      if (this.time) {
        const data = {
          actualTime: this.time,
          id: this.$route.query.id,
          type: 1
        }
        entScanning(data).then(() => {
          this.visible = false
          this.getReviseApplyBillDetail()
          this.ReplaceDetail.reviseApplyDetail()
          this.$toast.success('修改成功')
        })
      } else {
        this.$toast.warning('请选择时间')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.half-container {
  .half-header {
    padding-bottom: 16px;
    border-bottom: 1px solid #dcdcdc;
    .half-title {
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 23px;
    }
  }
  .tips-wrapper {
    background: #fef3e6;
    border-radius: 3px;
    padding: 8px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
    line-height: 22px;
  }

  .check-out-contract{
    padding: 12px 12px;
    background-color: rgba(222, 129, 66, 0.05);
    border-radius: 3px;
    border: 1px solid rgba(222, 129, 66, 0.8);
  }
}
:deep(.el-collapse) {
  .el-collapse-item__arrow {
    top: 18px;
  }
}
:deep(.empty-content) {
  padding: 30px 0 !important;
}
</style>
