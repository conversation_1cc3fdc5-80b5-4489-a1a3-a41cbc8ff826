<template>
  <div class="sign-contract-container">
    <div
      v-if="!info.signContract || JSON.stringify(info.signContract) === '{}'"
    >
      <div>
        <div class="color-info">处理方式</div>
        <el-radio-group v-model="tabValue" class="m-t-4">
          <el-radio-button
            v-for="item in tabData"
            :key="item.value"
            :label="item.value"
          >
            {{ item.label }}
          </el-radio-button>
        </el-radio-group>
      </div>
      <div class="agreement-declare">
        {{
          tabValue === 2
            ? '新签合同：签订一份新合同'
            : '补充协议：基于原合同新增补充协议，按新房间生成首期账单，其余账期合并应收'
        }}
      </div>
      <el-link
        type="primary"
        :disabled="info.status === 1"
        class="m-t-8"
        @click="contractHandle('create')"
      >
        {{
          tabValue === 2
            ? '去新增合同'
            : info.flow === 'quit_contract_return'
            ? '去签订退房协议'
            : '去签订增房协议'
        }}
      </el-link>
    </div>
    <div v-else>
      <div class="flex align-items-center">
        <div class="color-info">处理方式</div>
        <div class="m-l-8 flex align-items-center">
          <span>{{ tabValue === 2 ? '新签合同' : '补充协议' }}</span>
          <el-tooltip
            effect="dark"
            :content="
              tabValue === 2
                ? '新签合同：签订一份新合同'
                : '补充协议：基于原合同新增补充协议，按新房间生成首期账单，其余账期合并应收'
            "
            placement="top"
          >
            <svg-icon icon-class="help-circle-filled" class="m-l-4" />
          </el-tooltip>
        </div>
      </div>
      <div
        class="color-info m-t-8 flex align-items-center justify-content-between"
      >
        <span>{{ tabValue === 2 ? '新签合同' : '补充协议' }}</span>
        <el-button
          type="primary"
          v-if="info.signContract && info.signContract.showEdit"
          @click="editContractHandle('edit')"
          >{{ tabValue === 2 ? '编辑合同' : '编辑协议' }}</el-button
        >
      </div>
      <drive-table
        class="m-t-4"
        ref="drive-table"
        :columns="signContractTableColumn"
        :table-data="tableData"
      />
      <div
        v-if="info.signContract && info.signContract.transactor"
        class="color-info m-t-8"
      >
        <div>办理人</div>
        <div class="font-size-12 color-text-secondary">
          {{ info.signContract.transactor }}
        </div>
      </div>
    </div>
    <dialog-cmp
      title="请选择主合同"
      :visible.sync="dialogVisible"
      width="420px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        ref="driven-form"
        v-if="dialogVisible"
        v-model="fromModel"
        :formConfigure="signContractFormConfigure"
      />
      <template v-if="contractInfo.id">
        <div class="flex align-items-center">
          <div>合同期限</div>
          <div class="m-l-16">{{ contractInfo.contractTime | noData }}</div>
        </div>
        <div class="flex align-items-center m-t-8">
          <div>合同面积</div>
          <div class="m-l-16">{{ contractInfo.area | noData }}㎡</div>
        </div>
        <div class="flex align-items-center m-t-8">
          <div>租赁房源</div>
          <div class="m-l-16">{{ contractInfo.rentalHousing | noData }}</div>
        </div>
      </template>
    </dialog-cmp>
  </div>
</template>

<script>
import DescriptorMixin from '../descriptor'
import ColumnMixin from '../column'
import { reviseApplyGetEntSelect } from '../../../../../api'

export default {
  name: 'SignContract',
  props: {
    info: {
      type: Object
    },
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [DescriptorMixin, ColumnMixin],
  data() {
    return {
      dialogVisible: false,
      fromModel: {},
      contractInfo: {},
      tabValue: 2,
      tabData: [
        // { label: '补充协议', value: 1 },
        { label: '新签合同', value: 2 }
      ]
    }
  },
  computed: {
    tableData() {
      return this.info.signContract ? [this.info.signContract] : []
    }
  },
  watch: {
    info: {
      handler() {
        // const signContract = val.signContract || {}
        this.tabValue = 2
        this.signContractTableColumn[0].label =
          this.tabValue === 1 ? '协议编号' : '合同编号'
        this.signContractTableColumn[3].label =
          this.tabValue === 1 ? '协议面积(㎡)' : '合同面积(㎡)'
        this.signContractTableColumn[4].label =
          this.tabValue === 1 ? '协议状态' : '合同状态'
        if (this.detailInfo.type === 1) {
          this.tabData = this.$options.data().tabData
          this.tabValue = 1
        }
        if (this.detailInfo.type === 2 && this.detailInfo.changeRoom) {
          this.tabData = [{ label: '新签合同', value: 2 }]
          this.tabValue = 2
        }
        if (this.detailInfo.type === 2 && !this.detailInfo.changeRoom) {
          this.tabData = [{ label: '补充协议', value: 1 }]
          this.tabValue = 1
        }
      },
      deep: true,
      immediate: true
    },
    dialogVisible(val) {
      if (!val) {
        this.fromModel = this.$options.data().fromModel
        this.contractInfo = this.$options.data().contractInfo
      }
    }
  },
  inject: ['ReplaceDetail'],
  methods: {
    contractChange(e) {
      const options = this.signContractFormConfigure.descriptors.id.options
      const row = options.find(item => item.value === e)
      this.contractInfo = row || {}
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        this.goContractCreate()
        this.dialogVisible = false
      })
    },
    contractDetail(row) {
      const path = this.tabValue === 2 ? '/contract/index/contractDetails' : '/rentOut/contractDetails'
      this.$router.push({
        path,
        query: {
          id: row.contractId,
          orderId: row.orderId
        }
      })
    },
    editContractHandle(type) {
      this.fromModel.id = this.info.signContract.editId
      this.goContractCreate(type)
    },
    goContractCreate(type) {
      let query = {
        id: this.fromModel.id,
        applyType: true,
        applyId: this.$route.query.id,
        reviseFlag: !!this.detailInfo.changeRoom
      }

      if (this.info.flow === 'quit_contract_return') {
        query.type = 'transferRoom'
      }
      if (this.tabValue === 2) {
        query = {
          entId: this.detailInfo.entId,
          applyId: this.$route.query.id,
          handOverStatus: 1
        }
        if (this.info.signContract && this.info.signContract.showEdit) {
          query = {
            id: this.info.signContract.contractId
          }
        }
      }
      if (this.info.flow === 'quit_contract_return' || this.tabValue !== 2) {
        if (type && type === 'edit'){
          query.id = this.tableData[0]?.contractId
        }
        query.type = type
        this.$router.push({
          path: '/rentOut/contractCreate',
          query
        })
      }else {
        query.type = 'create'
        this.$router.push({
          path: '/contract/contractCreate',
          query
        })
      }

    },
    contractHandle(type) {
      if (this.tabValue === 2) return this.goContractCreate()
      if (this.info.flow === 'quit_contract_return') {
        this.fromModel.id = this.ReplaceDetail.detailInfo.contractId
        this.goContractCreate(type)
        return
      }
      reviseApplyGetEntSelect(this.$route.query.id).then(res => {
        if (!res || !res.length) return this.$toast.success('暂无可签订合同')
        if (res && res.length > 1) {
          this.signContractFormConfigure.descriptors.id.options = res.map(
            item => {
              return {
                ...item,
                value: item.id,
                label: item.contractNo
              }
            }
          )
          this.dialogVisible = true
        } else {
          this.fromModel = res[0]
          this.goContractCreate()
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.sign-contract-container {
  .agreement-declare {
    margin-top: 8px;
    padding: 4px 8px;
    border-radius: 4px;
    @include background_color_mix(--color-primary, #fff, 90%);
    font-size: 12px;
    @include font_color(--color-info);
  }
}
</style>
