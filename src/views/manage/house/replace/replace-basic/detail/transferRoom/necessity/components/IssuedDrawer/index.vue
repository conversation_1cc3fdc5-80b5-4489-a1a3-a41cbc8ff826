<template>
  <basic-drawer
    :title="title"
    size="45%"
    :visible.sync="comVisible"
    @close="close"
    :haveFooter="false"
    @confirmDrawer="confirmDrawer"
  >
    <template v-if="comVisible">
      <div class="immediately">
        <el-button type="info" @click="immediately" :disabled="isDisabled">
          <svg-icon icon-class="arrow-down" />
          <span>立即下发</span>
        </el-button>
      </div>
      <el-collapse
        v-if="questionMatter && questionMatter.length"
        v-model="activeNames"
        accordion
      >
        <el-collapse-item
          v-for="(item, inx) in questionMatter"
          :key="inx"
          :name="item.id"
        >
          <template slot="title">
            <div
              class="w100 flex justify-content-between align-items-center m-r-8"
            >
              <div>
                <span>{{ item.deptName }}</span>
                <el-tag class="m-l-20">
                  {{ item.questionMatterDistContentVO.questionType }}
                </el-tag>
              </div>
              <el-button
                v-if="item.dealStatus === 1"
                size="small"
                type="info"
                @click.stop="revocation(item)"
                :disabled="isDisabled"
              >
                撤回
              </el-button>
            </div>
          </template>
          <div class="flex">
            <div :style="{ width: item.dealStatus === 1 ? '100%' : '60%' }">
              <div class="title">下发事项</div>
              <div class="main">
                <span class="left">{{ item.nickname }}</span>
                <span class="right">{{ item.distTime }}</span>
              </div>
              <div class="info">
                {{ item.questionMatterDistContentVO.content }}
              </div>
              <el-image
                class="info_img bg-base m-t-8 m-r-8"
                fit="contain"
                :src="item2.path"
                alt=""
                v-for="(item2, index) in item.distAttachMap.park"
                :key="index"
                :preview-src-list="
                  item.distAttachMap.park.map(item => item.path)
                "
              />
            </div>
            <div
              class="transact m-l-12"
              v-if="item.dealStatus === 2"
              :style="{ width: item.dealStatus === 2 ? '40%' : '' }"
            >
              <div class="m-b-8">
                <span class="title">办理情况</span>
              </div>
              <record :historyList="historyListCom(item)"></record>
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
      <empty-data v-else />
    </template>
  </basic-drawer>
</template>

<script>
import record from './record.vue'
import { revocation } from '../../../../../api'

export default {
  name: 'IssuedDrawer',
  components: { record },
  inject: ['main'],
  props: {
    questionDetail: {
      type: Object,
      default: () => ({})
    },
    isDisabled: {
      type: Boolean,
      default: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    questionMatter: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      comVisible: false,
      activeNames: [],
      questionHandleRespVOS: [],
      questionTrackRespVOS: []
    }
  },
  methods: {
    initHandle(row) {
      if (row && row.type === 3) {
        this.activeNames = [row.matterId]
      } else {
        if (this.questionMatter && this.questionMatter.length) {
          this.activeNames = this.questionMatter[0].id
        }
      }
    },
    revocation(item) {
      this.$confirm('确定撤回该条数据吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        revocation(item.id).then(() => {
          this.main.getCommonMatter()
          this.main.tipsHandle('撤回')
        })
      })
    },
    historyListCom(item) {
      const { dealAttachMap = {}, dealContent = '', dealTime = '' } = item
      return [
        {
          attachMap: dealAttachMap,
          title: dealContent,
          type: 1,
          trackTime: dealTime
        }
      ]
    },
    close() {
      this.$emit('update:visible', false)
    },
    confirmDrawer() {
      this.$emit('confirmDrawer')
    },
    immediately() {
      this.$emit('immediately')
    }
  },
  watch: {
    comVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    visible(val) {
      this.comVisible = val
    }
  }
}
</script>

<style lang="scss" scoped>
.immediately {
  width: 100%;
  height: 70px;
  background: #ffffff;
  border-radius: 3px 3px 3px 3px;
  border: 1px solid #f9e0c7;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
}
.info_img {
  width: 144px;
  height: 80px;
  border-radius: 3px;
}
.title {
  font-weight: bold;
  font-size: 15px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 22px;
}
.float-right {
  float: right;
}
.color-text {
  font-weight: 400;
  font-size: 14px;
  color: #ed7b2f;
  line-height: 22px;
  cursor: pointer;
  margin-left: 12px;
}
.main {
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 22px;
  .left {
    color: rgba(0, 0, 0, 0.9);
  }
  .right {
    color: rgba(0, 0, 0, 0.4);
    margin-left: 16px;
  }
}
.info {
  font-weight: 350;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  line-height: 22px;
  word-wrap: break-word;
  word-break: break-all;
}
.transact {
  width: 100%;
  background: #f7f9fb;
  border-radius: 3px;
  margin-top: 8px;
  padding: 8px;
  .title {
    font-weight: bold;
    font-size: 15px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    margin-bottom: 16px;
  }
}
</style>
