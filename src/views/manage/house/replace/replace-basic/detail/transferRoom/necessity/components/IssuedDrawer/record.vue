<template>
  <div class="record-container">
    <div v-if="historyList.length">
      <div
        v-for="(item, index) in historyList"
        :key="index"
        class="record-wrapper flex"
      >
        <div class="right">
          <div
            class="right-header font-size-14 color-text-primary font-strong line-height-22 m-b-6"
          >
            <template v-if="item.title">
              <span class="line">{{ item.title }}</span>
            </template>
          </div>

          <div
            class="line font-size-14 color-text-regular line-height-22 m-b-6"
          >
            {{ item.content }}
          </div>

          <div v-if="item.attachMap && Object.keys(item.attachMap).length">
            <el-image
              class="info_img bg-base m-r-8"
              fit="contain"
              :src="item2.path"
              alt=""
              v-for="(item2, index) in item.attachMap.park"
              :key="index"
              :preview-src-list="item.attachMap.park.map(item => item.path)"
            >
            </el-image>
          </div>
          <div class="font-size-12 line-height-20 color-text-regular">
            {{ item.trackTime }}
          </div>
        </div>
      </div>
    </div>
    <empty-data v-else description="暂无审批记录" />
  </div>
</template>

<script>
export default {
  name: 'FlowRecord',
  props: {
    historyList: {
      type: Array,
      default: () => []
    }
  },
  inject: ['main'],
  data() {
    return {
      historyListCopy: []
    }
  },
  watch: {
    historyList: {
      handler(val) {
        if (val.length) {
          this.historyListCopy = JSON.parse(JSON.stringify(val))
          this.historyListCopy.shift()
        }
      },
      immediate: true
    }
  },
  methods: {
    getCircleBg(e) {
      const bgs = new Map()
      bgs.set(1, 'bg-primary')
      bgs.set(2, 'bg-warning')
      bgs.set(3, 'bg-danger')
      return bgs.get(e)
    },

    getVale(e) {
      const bgs = new Map()
      bgs.set(1, '通过')
      bgs.set(2, '退回')
      bgs.set(3, '拒绝')
      return bgs.get(e.result)
    }
  }
}
</script>

<style lang="scss" scoped>
.line {
  word-wrap: break-word;
  word-break: break-all;
}
.record-container {
  height: 100%;
  overflow: hidden;
  //overflow-x: hidden;
  //overflow-y: scroll;
  //padding-right: 12px;
  .record-wrapper {
    .left {
      flex: 0 0 32px;
      position: relative;

      .record-line {
        position: absolute;
        width: 2px;
        height: 100%;
        top: 10px;
        left: 11px;
        @include background_color(--border-color-base);
      }

      .record-circle {
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        top: 8px;
        left: 8px;
      }
    }
    .right {
      width: fit-content;
      margin-bottom: 12px;
    }
  }
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }
}

.info_img {
  width: 42px;
  height: 42px;
  border-radius: 3px;
}
</style>
