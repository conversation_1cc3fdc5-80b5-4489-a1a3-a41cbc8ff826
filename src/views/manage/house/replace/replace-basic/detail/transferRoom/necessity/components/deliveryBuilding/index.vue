<template>
  <div>
    <div class="font-size-14 m-b-4">通知企业{{ titleName }}</div>
    <template
      v-if="info.confirm.opeartionList && info.confirm.opeartionList.length"
    >
      <div
        v-for="(row, rowIndex) in info.confirm.opeartionList"
        :key="rowIndex"
        class="color-text-secondary"
      >
        <div class="flex align-items-center">
          <span>{{ row.opTime }} | {{ row.opDept }} | {{ row.content }}</span>
          <el-link
            v-if="row.type === 5"
            type="primary"
            class="m-l-4"
            @click="deliveryHandle(info, row)"
            >查看事项内容
          </el-link>
        </div>
      </div>
    </template>
    <template v-if="!info.confirm.confirmDate">
      <el-button
        :disabled="info.status === 0 || info.status === 1"
        type="primary"
        @click="deliveryInfo.notifyVisible = true"
        >通知企业
      </el-button>
      <el-button
        :disabled="info.status === 0 || info.status === 1"
        @click="deliveryHandle"
        >事项下发
      </el-button>
    </template>
    <div class="font-size-14 m-t-8">{{ titleName }}确认单</div>
    <div
      v-if="confirmTipShow(info.confirm)"
      class="font-size-12 color-text-secondary"
    >
      请先通知企业何时前往园区完成{{ titleName }}
    </div>
    <template v-else>
      <div
        v-if="
          !info.confirm.attachMap ||
          JSON.stringify(info.confirm.attachMap) === '{}'
        "
        class="font-size-12 color-text-secondary"
      >
        等待{{ titleName }}确认单上传并确定最终{{ titleName }}时间
      </div>
      <files-list
        v-else
        :files="info.confirm.attachMap.intentCustomer"
        onlyForView
      />
    </template>
    <el-button
      :disabled="info.status === 0 || info.status === 1"
      v-if="!info.confirm.confirmDate"
      class="m-t-8"
      type="primary"
      @click="checkHandle"
      >上传{{ titleName }}确认单
    </el-button>
    <div class="m-t-8" v-if="info.confirm.confirmDate">
      <div>{{ titleName }}时间</div>
      <div class="font-size-12 color-text-secondary">
        正式{{ titleName }}时间 {{ info.confirm.confirmDate }}
      </div>
    </div>
    <div class="m-t-8" v-if="info.confirm.confirmOpTime">
      <div>办理人</div>
      <div class="font-size-12 color-text-secondary">
        {{ info.confirm.confirmOpTime }} | {{ info.confirm.confirmUserDept }} -
        {{ info.confirm.confirmUser }}上传{{ titleName }}确认单并确认{{ titleName }}时间
      </div>
    </div>
    <issued-drawer
      ref="issuedDrawer"
      :questionDetail="deliveryInfo.questionDetail"
      :questionMatter="deliveryInfo.questionMatter"
      title="下发事项"
      @immediately="immediately"
      :visible.sync="deliveryInfo.visibleMatters"
      :is-disabled="deliveryInfo.issueDisabled"
    />
    <!-- 立即下发-->
    <issued-cmp
      ref="issuedCmp"
      title="下发事项"
      :visible.sync="deliveryInfo.visibleIssued"
    />
    <dialog-cmp
      title="通知企业房屋交付"
      :visible.sync="deliveryInfo.notifyVisible"
      @confirmDialog="deliveryNotifyConfirm"
      width="620px"
    >
      <driven-form
        v-if="deliveryInfo.notifyVisible"
        ref="delivery-driven-form"
        v-model="deliveryInfo.fromModel"
        :formConfigure="deliveryFormConfigure"
        label-position="top"
      />
    </dialog-cmp>
    <dialog-cmp
      :title="'上传' + titleName + '确认单'"
      :visible.sync="deliveryInfo.roomConfirmVisible"
      @confirmDialog="roomConfirmDialog"
      width="420px"
    >
      <driven-form
        v-if="deliveryInfo.roomConfirmVisible"
        ref="room-confirm-driven-form"
        v-model="deliveryInfo.roomConfirmFromModel"
        :formConfigure="roomConfirmFormConfigure"
        label-position="top"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import FilesList from '@/components/Uploader/files'
import IssuedDrawer from '../IssuedDrawer'
import IssuedCmp from '../IssuedCmp'
import DescriptorMixin from '../descriptor'
import {
  getCommonMatter,
  noticeEnterprise,
  reviseUploadConfirm,
  uploadConfirm
} from '../../../../../api'

export default {
  name: 'DeliveryBuilding',
  components: { IssuedCmp, IssuedDrawer, FilesList },
  props: {
    info: {
      type: Object
    },
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      deliveryInfo: {
        notifyVisible: false,
        fromModel: {},
        roomConfirmVisible: false,
        roomConfirmFromModel: {},
        questionDetail: {},
        questionMatter: [],
        visibleMatters: false,
        visibleIssued: false,
        issueDisabled: true
      }
    }
  },
  computed: {
    titleName() {
      const flow = this.info.flow
      if (flow === 'quit_house_return_other') {
        return '退房'
      } else {
        return '交房'
      }
    }
  },
  provide() {
    return {
      main: this
    }
  },
  inject: ['Necessity', 'ReplaceDetail'],
  watch: {
    'deliveryInfo.roomConfirmVisible'(val) {
      if (!val) {
        this.deliveryInfo.roomConfirmFromModel = {}
      }
    },
    'deliveryInfo.notifyVisible'(val) {
      if (!val) {
        this.deliveryInfo.fromModel = {}
      }
    }
  },
  methods: {
    checkHandle() {
      this.$set( this.deliveryInfo.roomConfirmFromModel, 'confirmDate', this.detailInfo?.actualQuitDate || '' )
      this.roomConfirmFormConfigure.descriptors.confirmDate.label = this.titleName + '时间'
      this.roomConfirmFormConfigure.descriptors.attachIds.label = this.titleName + '确认单'

      this.roomConfirmFormConfigure.descriptors.confirmDate.disabled = this.titleName === '退房';

      this.roomConfirmFormConfigure.descriptors.attachIds.rule[0].message = `请上传${this.titleName}确认单`
      this.deliveryInfo.roomConfirmVisible = true
    },
    deliveryNotifyConfirm() {
      this.$refs['delivery-driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.deliveryInfo.fromModel,
          id: this.$route.query.id
        }
        noticeEnterprise(params).then(() => {
          this.tipsHandle('通知', () => {
            this.deliveryInfo.notifyVisible = false
          })
        })
      })
    },
    tipsHandle(text, cb) {
      this.Necessity.reviseApplyTransferMatters()
      this.$toast.success(text + '成功')
      cb && cb()
    },
    roomConfirmDialog() {
      this.$refs['room-confirm-driven-form'].validate(valid => {
        if (!valid) return false
        const attachIds = this.deliveryInfo.roomConfirmFromModel.attachIds || []
        const params = {
          ...this.deliveryInfo.roomConfirmFromModel,
          attachIds: attachIds.length ? attachIds.map(item => item.id) : [],
          applyId: this.$route.query.id,
          id: this.$route.query.id
        }
        let url = uploadConfirm
        if (this.info.flow === 'change_house_delivery') {
          url = reviseUploadConfirm
        }
        url(params).then(() => {
          this.tipsHandle('上传', () => {
            this.deliveryInfo.roomConfirmVisible = false
            this.ReplaceDetail.reviseApplyDetail()
          })
        })
      })
    },
    immediately() {
      this.deliveryInfo.visibleIssued = true
    },
    confirmTipShow(row) {
      if (row.confirmDate) return false
      const list = row.opeartionList || []
      const item = list.find(item => item.type === 7)
      return !item
    },
    getCommonMatter() {
      return new Promise((resolve, reject) => {
        const params = {
          bsId: this.$route.query.id,
          bsType: 5
        }
        getCommonMatter(params)
          .then(res => {
            this.deliveryInfo.questionMatter = res || []
            resolve(res)
          })
          .catch(err => {
            reject(err)
          })
      })
    },
    async deliveryHandle(row, val) {
      if (row.status === 0 || !!this.info.confirm.confirmDate) {
        this.deliveryInfo.issueDisabled = true
      } else {
        this.deliveryInfo.issueDisabled = false
      }
      await this.getCommonMatter()
      this.deliveryInfo.visibleMatters = true
      this.$nextTick(() => {
        this.$refs.issuedDrawer.initHandle(val)
      })
    }
  }
}
</script>

<style scoped></style>
