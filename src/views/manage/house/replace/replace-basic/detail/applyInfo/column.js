import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: ' 全选',
          type: 'selection',
          width: 40,
          selectable: this.selectableHandle
        },
        {
          prop: 'contractNo',
          label: '合同编号',
          minWidth: '150px',
          showOverflowTooltip: true,
          render: (h, scope) => {
            let style = 'display:flow;'
            if (!scope.row.choose) {
              style += 'cursor:not-allowed'
            }
            return (
              <el-link
                style={style}
                class={'line-1'}
                type={'primary'}
                onClick={() => this.contractDetail(scope.row)}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          prop: 'park',
          label: '园区',
          minWidth: '150px',
          showOverflowTooltip: true
        },
        {
          prop: 'building',
          label: '楼栋'
        },
        {
          prop: 'room',
          label: '房间'
        },
        {
          prop: 'area',
          label: '面积(㎡)'
        }
      ],
      // 总计费用
      totalTableColumn: [
        {
          prop: 'feeTypeStr',
          label: '费用类型'
        },
        {
          prop: 'payAmount',
          label: '应收金额(元)',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        {
          prop: 'acAmount',
          label: '实收金额(元)',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.acAmount)}</div>
            )
          }
        },
        {
          prop: 'statusStr',
          label: '核销状态'
        }
      ],
      receivableTableColumn: [
        {
          prop: 'feeTypeStr',
          label: '费用名称',
          minWidth: 120
        },
        {
          prop: 'period',
          label: '期数',
          minWidth: 120
        },
        {
          prop: 'rcvAmtSdt',
          label: '开始日期',
          minWidth: 120
        },
        {
          prop: 'rcvAmtEdt',
          label: '结束日期',
          minWidth: 120
        },
        // {
        //   prop: 'quarterPeriod',
        //   label: '账期',
        //   minWidth: 120
        // },
        {
          prop: 'payAmount',
          label: '应收金额(元)',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        {
          prop: 'acAmount',
          label: '实收金额(元)',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.acAmount)}</div>
            )
          }
        },
        {
          prop: 'statusStr',
          label: '核销状态'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 60,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.detailHandle(scope.row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ],
      // 保证金缴存
      depositTableColumn: [
        {
          prop: 'depositBase',
          label: '保证金基数(元)',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.depositBase)}
              </div>
            )
          }
        },
        {
          prop: 'depositBalance',
          label: '保证金余额(元)',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.depositBalance)}
              </div>
            )
          }
        },
        {
          prop: 'depositStatus',
          label: '缴存状态'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.depositDetailHandle(scope.row)
                }}
              >
                查看
              </el-link>
            )
          }
        }
      ]
    }
  }
}
