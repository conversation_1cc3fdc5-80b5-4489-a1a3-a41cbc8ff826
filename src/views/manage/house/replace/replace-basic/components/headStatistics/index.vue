<template>
  <div class="head-statistics-container m-b-32">
    <div class="flex align-items-center justify-content-between">
      <div class="left flex align-items-center justify-content-between">
        <div class="left-content">
          <div class="title flex align-items-center">
            <span class="m-r-8">流程中合同总数</span>
            <el-tooltip
              effect="dark"
              content="包含起草中、审批中的所有合同"
              placement="top"
            >
              <svg-icon icon-class="help-circle" />
            </el-tooltip>
          </div>
          <div class="flex align-items-end m-t-16">
            <div class="flex align-items-end">
              <span class="font-size-28 font-strong line-height-28">{{
                objData.total
              }}</span>
              <span class="font-size-18 font-strong m-l-4 line-height-20"
                >份</span
              >
            </div>
            <el-tooltip
              effect="dark"
              content="我发起的流程中合同数"
              placement="top"
            >
              <div class="flex align-items-center m-l-16 pointer">
                <svg-icon icon-class="contract-flow" />
                <span class="color-primary m-l-4">{{ objData.myTotal }}份</span>
              </div>
            </el-tooltip>
          </div>
          <!--          <div class="color-e font-size-12 line-height-22 m-t-8">包含起草中、审批中的所有合同</div>-->
        </div>
        <div class="left-content">
          <div class="title flex align-items-center">
            <span class="m-r-8">执行中的合同总数</span>
            <el-tooltip
              effect="dark"
              content="当前待生效和执行中的所有合同"
              placement="top"
            >
              <svg-icon icon-class="help-circle" />
            </el-tooltip>
          </div>
          <div class="flex align-items-end m-t-16">
            <div class="flex align-items-end">
              <span class="font-size-28 font-strong line-height-28">{{
                objData.executingTotal
              }}</span>
              <span class="font-size-18 font-strong m-l-4 line-height-20"
                >份</span
              >
            </div>
            <el-tooltip
              effect="dark"
              content="我发起的执行中合同数"
              placement="top"
            >
              <div class="flex align-items-center m-l-16 pointer">
                <svg-icon icon-class="contract-success" />
                <span class="color-success m-l-4"
                  >{{ objData.myExecutingTotal }}份</span
                >
              </div>
            </el-tooltip>
          </div>
        </div>
        <div class="left-content">
          <div class="title flex align-items-center">
            <span class="m-r-8">即将到期的合同总数</span>
            <el-tooltip
              effect="dark"
              content="距离合同截止时间在30天以内"
              placement="top"
            >
              <svg-icon icon-class="help-circle" />
            </el-tooltip>
          </div>
          <div class="flex align-items-end m-t-16">
            <div class="flex align-items-end">
              <span class="font-size-28 font-strong line-height-28">{{
                objData.expiring
              }}</span>
              <span class="font-size-18 font-strong m-l-4 line-height-20"
                >份</span
              >
            </div>
            <div
              class="flex align-items-center m-l-16"
              v-if="objData.expiring > 0"
            >
              <svg-icon class="color-danger" icon-class="error-circle-filled" />
              <span
                class="font-size-12 color-danger m-l-4 line-height-22 line-1"
                >请及时关注合同续签情况</span
              >
            </div>
          </div>
          <!--          <div class="color-e font-size-12 line-height-22 m-t-8">距离合同截止时间在30天以内</div>-->
        </div>
        <div class="left-content">
          <div class="title flex align-items-center">
            <span class="m-r-8">已结束的合同总数</span>
            <el-tooltip
              effect="dark"
              content="已终止和自然到期的合同"
              placement="top"
            >
              <svg-icon icon-class="help-circle" />
            </el-tooltip>
          </div>
          <div class="flex align-items-end m-t-16">
            <div class="flex align-items-end">
              <span class="font-size-28 font-strong line-height-28">{{
                objData.ended
              }}</span>
              <span class="font-size-18 font-strong m-l-4 line-height-20"
                >份</span
              >
            </div>
            <div class="flex align-items-center m-l-16">
              <el-tooltip
                effect="dark"
                content="提前终止的合同数"
                placement="top"
              >
                <div class="flex align-items-center pointer">
                  <svg-icon icon-class="contract-danger" />
                  <span class="color-danger m-l-4 m-r-20"
                    >{{ objData.termination }}份</span
                  >
                </div>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                content="自然到期的合同数"
                placement="top"
              >
                <div class="flex align-items-center pointer">
                  <svg-icon icon-class="contract-warning" />
                  <span class="color-warning m-l-4"
                    >{{ objData.naturally }}份</span
                  >
                </div>
              </el-tooltip>
            </div>
          </div>
          <!--          <div class="color-e font-size-12 line-height-22 m-t-8">所有已终止和自然到期的所有合同</div>-->
        </div>
      </div>
      <div class="right" v-if="chartData.length > 0">
        <!--        <div class="title">新签和续签的分布对比</div>-->
        <div id="chart"></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'HeadStatistics',
  props: {
    objData: {
      type: Object,
      default: () => ({})
    },
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      option: {}
    }
  },
  methods: {
    init() {
      let chartDom = document.getElementById('chart')
      let myChart = echarts.init(chartDom)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      let that = this
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          },
          borderWidth: 0,
          formatter(params) {
            let html = ''
            for (let i = 0; i < params.length; i++) {
              let param = params[i]
              html += param.marker + param.seriesName + '<br/>'
            }
            return html
          },
          backgroundColor: '#000000',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          bottom: 6,
          left: 15,
          itemWidth: 10,
          itemHeight: 10,
          icon: 'circle',
          textStyle: {
            color: '#cacbcd'
          }
        },
        grid: {
          left: '20px',
          top: '0px',
          right: '10px',
          bottom: '0px',
          containLabel: true,
          splitLine: {
            // 设置竖线
            show: false
          },
          borderWidth: 0
        },
        xAxis: {
          type: 'value',
          boundaryGap: false,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: this.chartData?.map((item, index) => {
          return {
            name: item.name + item.value + item.unit,
            type: 'bar',
            stack: 'total',
            //改变每一根柱子的颜色
            itemStyle: {
              //改变主子的高度
              color:
                index === 0 ? '#f7c797' : index === 1 ? '#bcebdc' : '#99b9ff'
            },
            barWidth: 12,
            label: {
              normal: {
                show: true,
                position: 'top',
                aline: 'center',
                formatter(item) {
                  //求出每一项在总数中的占比
                  let total = 0
                  that.chartData?.forEach(t => {
                    total += t.value
                  })
                  let percent = ((item.value / total) * 100).toFixed(2)
                  if (Number(percent) > 0) {
                    return `${percent}%`
                  } else {
                    return ''
                  }
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: [item.value],
            showBackground: !(item.value > 0)
          }
        })
      }
      this.option && myChart.setOption(this.option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  },
  updated() {
    if (this.chartData.length > 0) {
      this.init()
    }
  },

  beforeDestroy() {
    this.option = null
  }
}
</script>

<style lang="scss" scoped>
.head-statistics-container {
  .color-e {
    color: rgba(0, 0, 0, 0.8);
  }
  .left {
    flex: 0.75;
    .left-content {
      flex: 1;
      padding: 24px 16px;
    }
  }
  .right {
    flex: 0.25;
    width: 100%;
    height: 100%;
    margin-left: 16px;
    #chart {
      width: 100%;
      height: 77px;
    }
  }
  .title {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;
  }
}
</style>
