<template>
  <el-dialog
    :visible.sync="visible"
    :modal-append-to-body="false"
    width="30%"
    @close="fromModel = {}"
  >
    <template v-slot:title>
      <div class="p-t-10">
        <div class="font-size-16 m-l-8" style="font-weight: 400">
          合同{{ title }}
        </div>
      </div>
    </template>
    <div class="w100 m-l-32 m-r-32 m-t-16 line-height-22">
      <div>确认{{ title }}{{ entName }}合同？</div>
      <div class="m-b-16">合同编号为:{{ contractNo }}</div>
      <driven-form
        v-if="visible"
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formContractFrom"
      />
    </div>
    <template v-slot:footer>
      <el-button type="info" @click="visible = false">取消</el-button>
      <el-button type="primary" @click="endContract(title)">确定</el-button>
    </template>
  </el-dialog>
</template>

<script>
import descriptorMixins from './descriptor-detail'
import { getRecordDestroy } from '@/views/manage/house/contract/contract-special/api/create'
export default {
  name: 'ContractEnd',
  mixins: [descriptorMixins],
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      fromModel: {},
      entName: '',
      contractNo: '',
      id: ''
    }
  },
  methods: {
    endContract() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          await getRecordDestroy({
            contractId: this.id,
            reason: this.fromModel.reason
          })
          this.visible = false
          this.$toast.success('合同作废成功')
          this.$emit('getNewList')
          this.fromModel = {}
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
