<template>
  <div>
    <div
      class="bg-info"
      :class="{ border: inx > 0 }"
      v-for="(row, inx) in subs"
      :key="row.id"
    >
      <div></div>
      <div>
        <div class="label">房间</div>
        <div class="value color-primary">
          <el-link
            style="display: inline"
            type="primary"
            @click="handleDetail(row)"
            >{{ row.period }}></el-link
          >
        </div>
      </div>
      <div v-for="item in dynamicHeader" :key="item.label">
        <div class="label">{{ item.label }}</div>
        <div class="value color-primary">
          {{ NumFormat(row[item.prop]) }}
        </div>
      </div>
      <div>
        <div class="label">开始时间</div>
        <div class="value">
          {{ parseTime(row.startTime, '{y}-{m}-{d}') }}
        </div>
      </div>
      <div>
        <div class="label">结束时间</div>
        <div class="value">
          {{ parseTime(row.endTime, '{y}-{m}-{d}') }}
        </div>
      </div>
    </div>

    <dialog-cmp
      title="房间价格详情"
      :visible.sync="visible"
      width="1000px"
      :have-operation="false"
    >
      <div class="dialog-content flex">
        <div class="left m-r-16">
          <el-menu :default-active="defaultActive" class="wh100">
            <el-menu-item
              @click="buildingHandle(item.value)"
              :index="item.value"
              v-for="(item, index) in buildings"
              :key="index"
            >
              <div v-tooltip="item.label">{{ item.label }}</div>
            </el-menu-item>
          </el-menu>
        </div>
        <div class="right">
          <div class="m-b-16">
            <div class="m-b-8">房间信息</div>
            <drive-table
              height="280px"
              ref="room-table"
              :columns="roomColumns"
              :table-data="roomsCom"
            >
            </drive-table>
          </div>
          <div>
            <div class="m-b-8">单价信息</div>
            <drive-table
              class="no-hover"
              height="280px"
              ref="price-table"
              :columns="unitPriceColumns"
              :table-data="pricesCom"
              :span-method="objectSpanMethod"
            >
            </drive-table>
          </div>
        </div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import CostColumn from './column'
import { get_rooms } from '../../api/create'
import { NumFormat, parseTime } from '@/utils/tools'
export default {
  name: 'SubTableInfo',
  mixins: [CostColumn],
  props: {
    subs: {
      type: Array,
      default: () => []
    },
    dynamicHeader: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      NumFormat,
      defaultActive: '216',
      tableData: [],
      visible: false,
      fixDetails: {}
    }
  },
  methods: {
    parseTime,
    objectSpanMethod({ row, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (
          rowIndex > 0 &&
          this.pricesCom[rowIndex - 1].building === row.building
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.pricesCom.length; i++) {
            if (this.pricesCom[i].building === row.building) {
              rowspan++
            } else {
              break
            }
          }
          return {
            rowspan,
            colspan: 1
          }
        }
      }

      if (columnIndex === 1) {
        if (
          rowIndex > 0 &&
          this.pricesCom[rowIndex - 1].roomId === row.roomId
        ) {
          return {
            rowspan: 0,
            colspan: 0
          }
        } else {
          let rowspan = 1
          for (let i = rowIndex + 1; i < this.pricesCom.length; i++) {
            if (this.pricesCom[i].roomId === row.roomId) {
              rowspan++
            } else {
              break
            }
          }
          return {
            rowspan,
            colspan: 1
          }
        }
      }

      // 默认返回值，处理其他情况
      return {
        rowspan: 1,
        colspan: 1
      }
    },
    buildingHandle(value) {
      this.fixDetails =
        this.tableData.find(item => item.roomId === Number(value)) || {}
    },
    handleDetail(row) {
      const id = this.$route.query.id
      if (id) {
        get_rooms(id).then(res => {
          if (res && res.length) {
            this.visible = true
            this.tableData = res
            this.defaultActive = String(row.id)
            this.fixDetails = res.find(item => item.roomId === row.id) || {}
          }
        })
      }
    }
  },
  mounted() {
    console.log('dynamicHeader----', this.dynamicHeader, this.subs)
    this.tableColumn.splice(1, 0, ...this.dynamicHeader)
  },
  computed: {
    roomsCom() {
      const { rooms = [] } = this.fixDetails.roomPrice || {}
      if (rooms.length) {
        return rooms
      } else {
        return []
      }
    },
    pricesCom() {
      const { prices = [] } = this.fixDetails.roomPrice || {}
      if (prices.length) {
        return prices
      } else {
        return []
      }
    },
    buildings() {
      if (this.tableData.length) {
        return this.tableData.map(item => ({
          label: item.room,
          value: String(item.roomId)
        }))
      } else {
        return []
      }
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-menu) {
  .el-menu-item.is-active {
    color: #ed7b2f;
    background-color: #fdf2ea;
  }
  .el-menu-item {
    height: 36px;
    line-height: 36px;
    padding: 0 20px;
    font-size: 14px;
    color: #303133;
    cursor: pointer;
    transition: all 0.3s;
    margin-bottom: 6px;
    &:hover {
      background-color: #fdf2ea;
    }
  }
}

.bg-info {
  background-color: #eeeeee !important;
  padding: 10px 0 10px 0;
  display: grid;
  //第一列宽48px,后几列宽自适应
  grid-template-columns: 48px 1fr 1fr 1fr 1fr;
  //grid-column-gap: 20px;
}

.border {
  border-top: 1px solid #e7e7e7;
}
.label {
  font-weight: 350;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 30px;
  padding-right: 10px;
  padding-left: 10px;
}

.w300 {
  width: 300px;
  display: inline-block;
}

.value {
  font-weight: 350;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  line-height: 30px;
  padding-right: 10px;
  padding-left: 10px;
}

.dialog-content {
  .left {
    width: 150px;
    height: initial;
  }
  .right {
    flex: 1;
  }
}
</style>
