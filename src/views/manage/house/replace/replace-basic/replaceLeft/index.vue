<template>
  <div class="replace-left">
    <div class="m-b-16">
      <ul class="font-size-14 m-t-8">
        <li class="item-title">处理方式</li>
        <li
          class="pointer flex align-items-center justify-content-between"
          :class="[
            item.class + '-li',
            searchValue === item.value ? item.class : ''
          ]"
          v-for="(item, index) in dueTypeList"
          :key="index"
          @click="handleData(item.value)"
        >
          <div class="flex align-items-center">
            <el-tooltip effect="dark" :content="item.tips" placement="bottom">
              <span v-if="item.tips" class="m-r-4">
                <svg-icon
                  icon-class="help-circle-filled"
                  class="text-s font-size-12"
                />
              </span>
            </el-tooltip>
            <span class="text-s">{{ item.label }}</span>
            <span class="text-s" v-if="item.value > -1"
              >（{{ item.count }}）</span
            >
          </div>
          <svg-icon class="svg-right" icon-class="chevron-right" />
        </li>
      </ul>
      <ul class="font-size-14 m-t-8">
        <li class="item-title">申请类型</li>
        <li
          class="pointer flex align-items-center justify-content-between"
          :class="[
            item.class + '-li',
            applySearchIndex === index ? item.class : ''
          ]"
          v-for="(item, index) in applyTypeList"
          :key="index"
          @click="applyHandleData(item, index)"
        >
          <div class="flex align-items-center">
            <div class="font-size-12 tip-item" v-if="item.changeRoom === false">
              退
            </div>
            <div class="font-size-12 tip-item" v-if="item.changeRoom === true">
              换
            </div>
            <span class="text-s">{{ item.label }}</span>
            <span class="text-s" v-if="item.value > -1"
              >（{{ item.count }}）</span
            >
          </div>
          <svg-icon class="svg-right" icon-class="chevron-right" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { reviseApplyStatistics } from '../api'

export default {
  name: 'ReplaceLeft',
  data() {
    return {
      searchValue: -1,
      applySearchIndex: 0,
      dueTypeList: [],
      applyTypeList: [],
      reqLoading: false
    }
  },
  inject: ['ReplaceApply'],
  activated() {
    this.reviseApplyStatistics()
  },
  methods: {
    reviseApplyStatistics() {
      reviseApplyStatistics().then(res => {
        const dueTypeList = res.dueTypeList || []
        const all = {
          label: '全部',
          value: -1
        }
        this.dueTypeList = [all, ...dueTypeList]
        this.dueTypeList[0].key = 'all'
        this.dueTypeList[0].class = 'all'
        this.dueTypeList[1].key = 'pending'
        this.dueTypeList[1].class = 'primary'
        this.dueTypeList[1].tips =
          '基于原合同添加补充内容，账期不变重新计算应收'
        this.dueTypeList[2].key = 'processed'
        this.dueTypeList[2].class = 'success'
        this.dueTypeList[2].tips = '完全新增合同，不限制任何合同内容'
        const applyTypeList = res.applyTypeList || []
        const applyAll = {
          label: '全部',
          value: -1
        }
        this.applyTypeList = [applyAll, ...applyTypeList]
        this.applyTypeList[0].key = 'all'
        this.applyTypeList[0].class = 'all'
        this.applyTypeList[1].key = 'pending'
        this.applyTypeList[1].class = 'primary'
        this.applyTypeList[2].key = 'exited'
        this.applyTypeList[2].class = 'danger'
        this.applyTypeList[3].key = 'processed'
        this.applyTypeList[3].class = 'success'
      })
    },
    async applyHandleData(val, index) {
      if (this.reqLoading) return false
      this.reqLoading = true
      this.applySearchIndex = index
      this.ReplaceApply.$refs.ReplaceRight.searchApplyTableHandle(val)
    },
    async handleData(val) {
      if (this.reqLoading) return false
      this.reqLoading = true
      this.searchValue = val
      this.ReplaceApply.$refs.ReplaceRight.searchTableHandle(this.searchValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.replace-left {
  width: 240px;
  border-right: 1px solid #e7e7e7;
  .text-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
  .text-s {
    color: rgba(0, 0, 0, 0.6);
  }
  li {
    width: 216px;
    height: 36px;
    line-height: 36px;
    padding-left: 16px;
    padding-right: 10px;
    margin-bottom: 4px;
    &.item-title {
      padding-left: 0;
    }
    .tip-item {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      @include font_color(--color-black);
      @include background_color(--border-color-base);
      margin-right: 4px;
    }
  }
  .all {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #054ce8;
    }
    .svg-right {
      display: block !important;
    }
    color: #054ce8;
  }
  .primary {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #ed7b2f;
    }
    .svg-right {
      display: block !important;
    }
    color: #ed7b2f;
  }
  .warning {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #ed7b2f;
    }
    .svg-right {
      display: block !important;
    }
    color: #ed7b2f;
  }
  .success {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #00a870;
    }
    .svg-right {
      display: block !important;
    }
    color: #00a870;
  }
  .danger {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #e34d59;
    }
    .svg-right {
      display: block !important;
    }
    color: #e34d59;
  }
  .all-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #054ce8;
      }
      .svg-right {
        display: block;
      }
      color: #054ce8;
    }
  }
  .warning-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #ed7b2f;
      }
      .svg-right {
        display: block;
      }
      color: #ed7b2f;
    }
  }
  .primary-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #ed7b2f;
      }
      .svg-right {
        display: block;
      }
      color: #ed7b2f;
    }
  }
  .success-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #00a870;
      }
      .svg-right {
        display: block;
      }
      color: #00a870;
    }
  }
  .danger-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #e34d59;
      }
      .svg-right {
        display: block;
      }
      color: #e34d59;
    }
  }
}
</style>
