import LeaseRooms from './LeaseRooms'
import { reviseApplyCheckRevise } from '../api'
import { validateContact } from '@/utils/validate'

export default {
  components: { LeaseRooms },
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          entId: {
            form: 'select',
            label: '调房企业',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择调房企业'
              },
              {
                validator: async (rule, value, callback) => {
                  const res = await reviseApplyCheckRevise({ entId: value })
                  if (res)
                    return callback(
                      new Error(
                        '当前企业有正在办理中的调房或离园业务，请选择其他企业'
                      )
                    )
                  callback()
                },
                trigger: ['blur']
              }
            ],
            options: [],
            attrs: {
              filterable: true
            },
            events: {
              change: this.entChange
            }
          },
          type: {
            form: 'radio',
            label: '调房类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择调房类型'
              }
            ],
            options: [
              {
                label: '增加房间',
                value: 0
              },
              {
                label: '退换房间',
                value: 2
              }
            ],
            props: {
              button: 'radio-button'
            }
          },
          parkId: {
            form: 'select',
            label: '意向园区',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择意向园区'
              }
            ],
            options: [],
            attrs: {
              filterable: true
            }
          },
          area: {
            form: 'input',
            label: '需求面积',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入需求面积'
              },
              {
                validator: (rule, value, callback) => {
                  if (!value) callback(new Error(`请输入需求面积`))
                  if (
                    /(^[0-9]([0-9]+)?(\.[0-9]{1,3})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
                      +value
                    )
                  ) {
                    const maxNum = 10000000000
                    if (value >= maxNum) {
                      return callback(new Error(`面积最大支持10位数`))
                    }
                    callback()
                  } else {
                    callback(new Error('最多保留三位小数'))
                  }
                }
              }
            ],
            customRight: () => {
              return <div style={'margin-top: 50px;'}>m²</div>
            }
          },
          occupancyTime: {
            form: 'date',
            label: '期望日期',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择期望入驻日期'
              }
            ]
          },
          cause: {
            form: 'input',
            label: '增房原因',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入增房原因'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true
            }
          },
          contractId: {
            form: 'select',
            label: '合同选择',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择合同'
              }
            ],
            options: [],
            events: {
              change: this.contractChange
            }
          },
          contractRoomId: {
            form: 'component',
            label: '选择退租的房间',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择退租的房间'
              }
            ],
            render: () => {
              return (
                <div>
                  <lease-rooms
                    ref="leaseRooms"
                    v-model={this.fromModel.contractRoomId}
                  />
                </div>
              )
            }
          },
          quitDate: {
            form: 'date',
            label: '期望日期',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择期望退租日期'
              }
            ]
          },
          changeRoom: {
            form: 'switch',
            label: '是否更换其他房间',
            hidden: true,
            rule: [{ type: 'boolean' }]
          },
          needArea: {
            form: 'input',
            label: '需求面积',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入需求面积'
              },
              {
                validator: (rule, value, callback) => {
                  if (!value) callback(new Error(`请输入需求面积`))
                  if (
                    /(^[0-9]([0-9]+)?(\.[0-9]{1,3})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
                      +value
                    )
                  ) {
                    const maxNum = 10000000000
                    if (value >= maxNum) {
                      return callback(new Error(`面积最大支持10位数`))
                    }
                    callback()
                  } else {
                    callback(new Error('最多保留三位小数'))
                  }
                }
              }
            ],
            customRight: () => {
              return <div style={'margin-top: 50px;'}>m²</div>
            }
          },
          reason: {
            form: 'input',
            label: '调整原因',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入调房原因'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true
            }
          },
          entBasic: {
            form: 'input',
            label: '企业基本情况',
            rule: [
              {
                required: false,
                type: 'string',
                message:
                  '包括但不限于：从业人数、营业收入、利润、税收、发展前景等内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true
            }
          },
          contact: {
            form: 'suggestion',
            label: '主要联系人',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主要联系人'
              }
            ],
            attrs: {
              maxLength: 20,
              fetchSuggestions: this.fetchContacts
            },
            events: {
              select: this.selectContact
            },
            scopedSlots: {
              default: props => {
                const h = this.$createElement
                return h('div', [
                  h('div', `${props.item.value}/${props.item.phone}`),
                ])
              }
            },
            customTips: () => {
              return (
                <div>默认引用了该企业常用联系人信息，具体以输入信息为准</div>
              )
            }
          },
          phone: {
            form: 'input',
            label: '联系方式',
            span: 8,
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              { validator: validateContact }
            ]
          },
          audited: {
            form: 'switch',
            label: '需要审核',
            rule: [
              {
                required: false,
                type: 'boolean',
                message: '请选择需要审核'
              }
            ],
            customRight: () => {
              return <div class={'audit-tips'}>是</div>
            },
            customTips: () => {
              return (
                <div>
                  根据企业此次实际调房情况来确定是否需要审核。如果选择否则直接进入办理阶段
                </div>
              )
            }
          }
        }
      }
    }
  }
}
