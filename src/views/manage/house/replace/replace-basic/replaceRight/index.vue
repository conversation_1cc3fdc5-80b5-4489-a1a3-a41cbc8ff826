<template>
  <div class="replace-right">
    <div class="flex justify-content-between align-items-center">
      <basic-tab
        :tabs-data="tabsData"
        :current="extralQuerys.status"
        :disabled="reqLoading"
        @tabsChange="tabsChange"
      >
        <template v-slot:right>
          <div class="flex align-items-center">
            <div class="flex align-items-center">
              <el-switch
                v-model="extralQuerys.lookMe"
                active-color="#ed7b2f"
                inactive-color="#dcdfe6"
                @change="lookMeChange"
                :disabled="reqLoading"
              >
              </el-switch>
              <span class="m-l-15 m-r-25 font-size-15">只看我录入的</span>
            </div>
            <el-button
              type="primary"
              size="small"
              v-permission="routeButtonsPermission.REGISTER_TRANSFERS"
              @click="drawerVisible = true"
              >{{ routeButtonsTitle.REGISTER_TRANSFERS }}</el-button
            >
          </div>
        </template>
      </basic-tab>
    </div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="reviseApplyPage"
      :extral-querys="extralQuerys"
      @getData="getTableData"
      :search-querys-hook="searchQueryHook"
    />
    <basic-drawer
      title="调房申请"
      :visible.sync="drawerVisible"
      size="820px"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        ref="driven-form"
        v-if="drawerVisible"
        v-model="fromModel"
        :formConfigure="formConfigure"
        label-position="top"
      />
    </basic-drawer>
  </div>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
import {
  reviseApplyContracts,
  reviseApplyEntList,
  reviseApplyPage,
  reviseApplyRooms,
  reviseApplyStatus,
  reviseApplySubmit
} from '../api'
import { getParkAll,getContacts } from '@/api/common'
import BasicTab from '@/components/BasicTab'

export default {
  name: 'ReplaceRight',
  components: { BasicTab },
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      reviseApplyPage,
      tabsData: [],
      extralQuerys: {
        lookMe: false,
        status: 0,
        type: -1,
        dueType: -1
      },
      drawerVisible: false,
      fromModel: {
        type: 0,
        audited: false,
        changeRoom: false
      },
      reqLoading: true,
      roomList: []
    }
  },
  watch: {
    'fromModel.changeRoom'() {
      this.needAreaHandle()
    },
    'fromModel.contractRoomId'() {
      this.changeRoomHandle()
    },
    'fromModel.type'(val) {
      this.formConfigure.descriptors.parkId.hidden = val !== 0
      this.formConfigure.descriptors.area.hidden = val !== 0
      this.formConfigure.descriptors.occupancyTime.hidden = val !== 0
      this.formConfigure.descriptors.cause.hidden = val !== 0
      this.formConfigure.descriptors.contractId.hidden = val === 0
      this.formConfigure.descriptors.quitDate.hidden = val === 0
      this.formConfigure.descriptors.contractRoomId.hidden = val === 0
      this.formConfigure.descriptors.reason.hidden = val === 0
      this.$set(this.fromModel, 'changeRoom', false)
      this.changeRoomHandle()
      this.needAreaHandle()
    },
    drawerVisible(val) {
      if (!val) {
        this.fromModel = this.$options.data().fromModel
        this.formConfigure.descriptors.contractId.options = []
      } else {
        this.reviseApplyEntList()
        this.getParkAll()
      }
    }
  },
  inject: ['ReplaceApply'],
  activated() {
    this.reviseApplyStatus()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    getContacts(row) {
      getContacts(row).then(res => {
        this.formConfigure.descriptors.phone.options = res.map(item => {
          return {
            label: `${item.contact}-(${item.phone})`,
            value: item.phone
          }
        })
      })
    },
    searchQueryHook(e) {
      let [applyDateStart = '', applyDateEnd = ''] = e.applyDate || []
      delete e.applyDate
      return {
        ...e,
        applyDateStart,
        applyDateEnd
      }
    },
    needAreaHandle() {
      if (!this.fromModel.changeRoom) {
        this.formConfigure.descriptors.needArea.hidden = true
      } else {
        if (this.fromModel.type === 0) {
          this.formConfigure.descriptors.needArea.hidden = false
        } else {
          this.formConfigure.descriptors.needArea.hidden =
            !this.fromModel.changeRoom
        }
      }
    },
    changeRoomHandle() {
      if (this.fromModel.contractRoomId && this.fromModel.type === 2) {
        const contractRoomId = JSON.parse(this.fromModel.contractRoomId)
        this.formConfigure.descriptors.changeRoom.hidden = !(
          contractRoomId.length &&
          contractRoomId.length === this.roomList.length
        )
      } else {
        this.formConfigure.descriptors.changeRoom.hidden = true
      }
    },
    reviseApplyStatus() {
      reviseApplyStatus().then(res => {
        this.tabsData = res || []
      })
    },
    tabsChange(e) {
      if (this.extralQuerys.status === e) return false
      this.extralQuerys.status = e
      this.reqLoading = true
      this.$refs['drive-table'].triggerSearch()
    },
    contractChange(val) {
      if (!val) return false
      reviseApplyRooms(val).then(res => {
        this.roomList = res || []
        this.$refs.leaseRooms && this.$refs.leaseRooms.initData(res)
      })
    },
    async entChange(val) {
      if (!val) return false
      await this.getContacts(val)
      const row = this.formConfigure.descriptors.entId.options.find(
        item => item.value === val
      )
      this.$set(this.fromModel, 'contact', row.contact)
      this.$set(this.fromModel, 'phone', row.phone)
      this.$set(this.fromModel, 'contractId', undefined)
      this.formConfigure.descriptors.contractId.options = []
      this.$set(this.fromModel, 'contractRoomId', '')
      this.$refs.leaseRooms && this.$refs.leaseRooms.initData([])
      reviseApplyContracts({ entId: val }).then(res => {
        this.formConfigure.descriptors.contractId.options = res.map(item => {
          return {
            label: item.label,
            value: item.value
          }
        })
      })
    },
    detailHandle(row) {
      this.$router.push({
        path: '/rentOut/replace/replaceDetail',
        query: {
          id: row.id,
          orderId: row.orderId
        }
      })
    },
    getTableData() {
      this.reqLoading = false
      this.ReplaceApply.$refs.ReplaceLeft.reqLoading = false
    },
    lookMeChange() {
      this.reqLoading = true
      this.ReplaceApply.$refs.ReplaceLeft.reqLoading = true
      this.$refs['drive-table'] && this.$refs['drive-table'].triggerSearch()
    },
    searchTableHandle(val) {
      this.extralQuerys.dueType = val
      this.$refs['drive-table'] && this.$refs['drive-table'].triggerSearch()
    },
    searchApplyTableHandle(row) {
      this.extralQuerys.type = row.value
      this.extralQuerys.changeRoom = row.changeRoom
      this.$refs['drive-table'] && this.$refs['drive-table'].triggerSearch()
    },
    getParkAll() {
      getParkAll().then(res => {
        this.formConfigure.descriptors.parkId.options = res.map(item => {
          return {
            value: item.id,
            label: item.park
          }
        })
      })
    },
    reviseApplyEntList() {
      reviseApplyEntList().then(res => {
        this.formConfigure.descriptors.entId.options = res.map(item => {
          return {
            ...item,
            value: item.id,
            label: item.name
          }
        })
      })
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel
        }
        if (this.fromModel.type === 0) {
          delete params.changeRoom
        }
        if (this.fromModel.contractRoomId && this.fromModel.type === 2) {
          const contractRoomId = JSON.parse(this.fromModel.contractRoomId)
          if (
            contractRoomId.length &&
            contractRoomId.length === this.roomList.length
          ) {
            params.quitType = 1
          } else {
            params.quitType = 0
          }
        }
        reviseApplySubmit(params).then(() => {
          this.$toast.success('登记成功')
          this.drawerVisible = false
          this.extralQuerys.status = 0
          this.lookMeChange()
          this.reviseApplyStatus()
          this.ReplaceApply.$refs.ReplaceLeft.reviseApplyStatistics()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.replace-right {
  width: calc(100% - 240px);
  padding-left: 24px;
  :deep(.tip-item) {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    @include font_color(--color-primary);
    @include background_color_mix(--color-primary, #ffffff, 80%);
    margin-left: 4px;
    &.refund {
      @include font_color(--color-danger);
      @include background_color_mix(--color-danger, #ffffff, 80%);
    }
  }
}
:deep(.auditedKey) {
  position: relative;
  .custom-right {
    position: absolute;
    left: 56px;
    top: 50px;
  }
}
:deep(.contractRoomIdKey) {
  .empty-content {
    padding: 20px 0;
  }
}
</style>
