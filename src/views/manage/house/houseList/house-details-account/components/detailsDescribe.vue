<template>
  <div class="details-decrease flex justify-content-between">
    <div class="decrease-left p-r-16" style="flex: 1">
      <div class="flex justify-content-between">
        <div class="text-color font-size-14">房源信息</div>
        <div>
          <el-button
            v-permission="routeButtonsPermission.HEALTH"
            type="info"
            @click="HouseDetailsAccount.boardHealthyHandle"
            >{{ routeButtonsTitle.HEALTH }}</el-button
          >
          <el-button
            v-if="info.showIntention"
            type="primary"
            @click="HouseDetailsAccount.intentHandle"
            v-permission="routeButtonsPermission.MARK"
            >意向{{ routeButtonsTitle.MARK }}</el-button
          >
        </div>
      </div>
      <div class="flex align-items-center">
        <div class="m-r-16 font-strong">{{ info.floor + info.room }}</div>
        <div class="label">
          <!--房源状态-->
          <div
            class="label-item"
            :style="{
              background: getProblemBackground(info.status),
              color: getProblemColor(info.status)
            }"
          >
            {{ getStatus(info.status) }}
          </div>
          <!--房源健康-->
          <div
            class="label-item"
            :style="{
              background: getProblemBackground(info.problemStatus),
              color: getProblemColor(info.problemStatus)
            }"
          >
            {{ getProblemStatus(info.problemStatus) }}
          </div>
          <!--意向房源-->
          <div
            v-if="info.intention"
            class="label-item"
            :style="{
              background: getProblemBackground(1),
              color: getProblemColor(1)
            }"
          >
            意向房源
          </div>
          <!--预约房源-->
          <div
            v-if="info.booking"
            class="label-item"
            :style="{
              background: getProblemBackground(1),
              color: getProblemColor(1)
            }"
          >
            预约房源
          </div>
        </div>
      </div>
      <div class="info-wrapper">
        <div class="info-item">
          <div class="item-label">所属园区</div>
          <div class="item-content">
            {{ info.park }}/{{ info.building }}/{{ info.floor }}
          </div>
        </div>
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <div class="item-label">房源面积</div>
              <div class="item-content">
                <span>{{ info.area }}</span>
                <span class="area-unit">㎡</span>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <div class="item-label">房源用途</div>
              <div class="item-content">{{ info.useTypeStr }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <div class="item-label">入驻企业</div>
              <div class="item-content" v-if="info.entName">
                <el-link type="primary" @click="goEntHandle(info.entId)">{{
                  info.entName
                }}</el-link>
              </div>
              <div class="item-content empty" v-else>--</div>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <div class="info-item">
              <div class="item-label">房源类型</div>
              <div class="item-content">{{ info.roomTypesStr }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <div class="item-label">测绘状态</div>
              <div class="item-content">
                {{ info.hasSurveying ? '已测绘' : '未测绘' }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <div class="item-label">统一社会信用代码</div>
              <div class="item-content" v-if="info.creditCode">
                {{ info.creditCode }}
              </div>
              <div class="item-content empty" v-else>--</div>
            </div>
          </el-col>
        </el-row>
        <template v-if="info.problemStatus === 1">
          <div class="info-item">
            <div class="item-label">维修附件</div>
            <div
              class="item-content"
              v-if="
                info.repairAccessories && info.repairAccessories.houseHealthy
              "
            >
              <Uploader
                :width="60"
                :height="60"
                v-model="info.repairAccessories.houseHealthy"
                type="avatar"
                mulity
                onlyForView
              />
            </div>
          </div>
          <div class="info-item">
            <div class="item-label">维修时间</div>
            <div class="item-content">
              {{ parseTime(info.repairTime, '{y}-{m}-{d}') }}
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getProblemBackground,
  getProblemColor
} from '@/views/manage/house/board/board-special/utils/houseColor'
import { parseTime } from '@/utils/tools'
const houseStatus = ['空置房源', '锁定房源', '已用房源', '已用房源']
const problemStatus = ['健康房源', '修缮中', '诉讼中']
export default {
  name: 'DetailsDescribe',
  inject: ['HouseDetailsAccount'],
  data() {
    return {
      getProblemBackground,
      getProblemColor,
      parseTime,
      info: {},
      tagsList: [
        {
          type: 'warning',
          label: '已租房源'
        },
        {
          type: 'success',
          label: '健康房源'
        },
        {
          type: 'primary',
          label: '修缮中'
        },
        {
          type: 'danger',
          label: '诉讼中'
        },
        {
          type: 'primary',
          label: '锁定房源'
        },
        {
          type: 'success',
          label: '空置房源'
        },
        {
          type: 'primary',
          label: '意向房源'
        },
        {
          type: 'primary',
          label: '预约房源'
        }
      ]
    }
  },
  methods: {
    goEntHandle(id) {
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id
        }
      })
    },
    init(info) {
      this.info = info
    },
    // 房源健康
    getProblemStatus(type) {
      return problemStatus[type]
    },
    // 房间状态
    getStatus(type) {
      return houseStatus[type]
    }
  }
}
</script>

<style lang="scss" scoped>
.details-decrease {
  width: calc(100% - 392px);
  .text-color {
    color: rgba(0, 0, 0, 0.9);
  }
  .label {
    margin-left: 16px;
    display: flex;
    .label-item {
      padding: 2px 8px;
      border-radius: 829px;
      font-size: 12px;
      text-align: center;
      line-height: 20px;
      background: #fef3e6;
      margin-right: 8px;
      font-weight: 400;
    }
  }
  .info-wrapper {
    .info-item {
      font-size: 14px;
      margin-top: 16px;
      display: flex;
      line-height: 22px;
      .item-label {
        color: rgba(0, 0, 0, 0.4);
        flex-shrink: 0;
      }
      .item-content {
        padding-left: 16px;
        color: rgba(0, 0, 0, 0.9);
        .area-unit {
          color: rgba(0, 0, 0, 0.4);
          padding-left: 2px;
        }
        &.empty {
          color: rgba(0, 0, 0, 0.26);
        }
      }
    }
  }
}
</style>
