import {
  Password_Regexp_Validate,
  Phone_Regexp_Validate,
  Username_Regexp_Validate
} from '@/utils/validate'
import { meRecordCheckCode } from '@/views/manage/house/investment/projectPool/projectPool-basic/api'

export default {
  data() {
    return {
      entFormConfigure: {
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '企业名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxLength: 30
            }
          }
        }
      },
      projectFormConfigure: {
        descriptors: {
          meProjectId: {
            form: 'select',
            label: '从招商项目库中选择',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择从招商项目库中选择'
              }
            ],
            events: {
              change: this.meProjectChange
            }
          },
          meProjectSource: {
            form: 'radio',
            label: '意向客户信息填写',
            options: [
              {
                label: '复制链接给企业',
                value: 6
              },
              {
                label: '后台添加',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择'
              }
            ],
            props: {
              button: 'radio-button'
            }
          }
        }
      },
      phoneFormConfigure: {
        descriptors: {
          phone: {
            form: 'input',
            label: '手机号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入手机号'
              },
              {
                require: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  Phone_Regexp_Validate(false)(rule, value, callback)
                }
              }
            ]
          }
        }
      },
      formConfigure: {
        labelWidth: '140px',
        descriptors: {
          enterpriseName: {
            form: 'input',
            label: '企业名称',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '企业名称'
              },
              {
                max: 20,
                message: '最多输入20个字符'
              }
            ]
          },
          creditCode: {
            form: 'input',
            hidden: false,
            label: '统一社会信用代码',
            placeholder: '请输入统一社会信用代码',
            rule: [
              {
                required: true,
                type: 'string',
                validator: async (rule, value, callback) => {
                  if (!value) {
                    this.creditCodeTips = ''
                    return callback(new Error('请输入统一社会信用代码'))
                  }
                  const res = await meRecordCheckCode({
                    creditCode: value,
                    entName: this.formModel.enterpriseName
                  })
                  const tips = [
                    '统一社会信用代码有误',
                    '统一社会信用代码与公司名称一致',
                    '统一社会信用代码与公司名称不一致'
                  ]
                  this.creditCodeTips = tips[res] || ''
                  callback()
                },
                trigger: 'blur'
              }
            ],
            customTips: () => {
              return <div class="color-primary">{this.creditCodeTips}</div>
            },
            attrs: {
              maxLength: 30
            }
          },
          contact: {
            form: 'input',
            label: '企业联系人',
            hidden: false,
            rule: [
              {
                type: 'string',
                required: true,
                message: '请输入企业联系人'
              },
              {
                max: 20,
                message: '最多输入20个字符'
              }
            ]
          },
          username: {
            form: 'input',
            label: '管理账号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入管理账号'
              },
              {
                require: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  Username_Regexp_Validate(false)(rule, value, callback)
                },
                trigger: ['blur']
              }
            ],
            attrs: {
              maxLength: 30,
              readonly: true
            }
          },
          mobile: {
            form: 'input',
            label: '手机号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入手机号'
              },
              {
                require: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  Phone_Regexp_Validate(false)(rule, value, callback)
                },
                trigger: ['blur']
              }
            ],
            attrs: {
              readonly: true
            }
          },
          password: {
            form: 'input',
            label: '登录密码',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入登录密码'
              },
              {
                require: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  Password_Regexp_Validate()(rule, value, callback)
                },
                trigger: ['blur']
              }
            ],
            attrs: {
              type: 'password'
            }
          },
          secondPassword: {
            form: 'input',
            label: '确认密码',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入确认密码'
              },
              {
                require: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  Password_Regexp_Validate()(rule, value, callback)
                },
                trigger: ['blur']
              },
              {
                validator: (rule, value, callback) => {
                  if (value !== this.formModel.password) {
                    callback(new Error('两次所输入的密码不一样，请重新输入'))
                  } else {
                    callback()
                  }
                },
                trigger: ['blur', 'change']
              }
            ],
            attrs: {
              type: 'password'
            }
          }
        }
      }
    }
  }
}
