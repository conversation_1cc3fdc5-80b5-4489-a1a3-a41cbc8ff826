<template>
  <div class="card-list-container">
    <div class="card-list-main pointer" @click.stop="detailHandle">
      <div class="title pos-relative">
        <el-tooltip
          effect="dark"
          :content="item.enterpriseName"
          placement="top"
        >
          <div class="flex align-items-center p-r-24">
            <div class="line-1 m-r-4">
              {{ item.enterpriseName || '--' }}
            </div>
            <div style="width: 20px">
              <svg-icon icon-class="swap-right" />
            </div>
          </div>
        </el-tooltip>
        <div
          v-if="item.status === 1"
          class="pos-absolute"
          style="right: 0; top: 0"
          @click.stop="delHandle(item.id)"
        >
          <svg-icon class="color-danger m-l-4" icon-class="delete" />
        </div>
      </div>
      <div v-if="item.whetherSubmit" class="content-container m-t-8">
        <div class="flex justify-content-between">
          <div
            class="flex"
            :class="showContent ? 'flex-wrap' : 'line-1'"
            style="flex: 1"
          >
            <!--            <basic-tag-->
            <!--              v-if="item.registeredMoney"-->
            <!--              class="m-r-8 m-t-8"-->
            <!--              type="warning"-->
            <!--              :label="`注册资本${String(item.registeredMoney)}万`"-->
            <!--            />-->
            <!--            <basic-tag-->
            <!--              v-if="item.existingEmployees"-->
            <!--              class="m-r-8 m-t-8"-->
            <!--              type="warning"-->
            <!--              :label="`团队规模${String(item.existingEmployees)}人`"-->
            <!--            />-->
            <!--            <basic-tag-->
            <!--              v-if="item.industryStr"-->
            <!--              type="warning"-->
            <!--              class="m-t-8"-->
            <!--              :label="item.industryStr && item.industryStr?.split(',')[0]"-->
            <!--            />-->
          </div>
          <!--          <div-->
          <!--            class="pointer m-t-8 p-t-4"-->
          <!--            @click.stop="showContent = !showContent"-->
          <!--          >-->
          <!--            <svg-icon v-if="showContent" icon-class="chevron-up" />-->
          <!--            <svg-icon v-else icon-class="chevron-down" />-->
          <!--          </div>-->
        </div>
        <div class="car-col" :class="showContent ? '' : 'active'">
          <div class="flex align-items-center">
            <div class="label-main m-r-8">意向园区</div>
            <div class="label-text line-1" style="flex: 1">
              {{ item.parkName || '--' }}
            </div>
          </div>
          <!--          <div class="flex align-items-center m-t-16">-->
          <!--            <div class="label-main m-r-8">入驻类型</div>-->
          <!--            <div class="label-text line-1" style="flex: 1">-->
          <!--              {{ item.occupationTypeStr || '&#45;&#45;' }}-->
          <!--            </div>-->
          <!--          </div>-->
          <!--          <div class="flex align-items-center m-t-16">-->
          <!--            <div class="label-main m-r-8">房屋用途</div>-->
          <!--            <div class="label-text line-1" style="flex: 1">-->
          <!--              {{ item.useTypeStr || '&#45;&#45;' }}-->
          <!--            </div>-->
          <!--          </div>-->
          <div class="flex align-items-center m-t-16">
            <div class="label-main m-r-8">需求面积</div>
            <div class="label-text line-1" style="flex: 1">
              {{ item.expectArea ? item.expectArea + 'm²左右' : '--' }}
            </div>
          </div>
          <!--          <div class="flex align-items-center m-t-16" style="flex: 1">-->
          <!--            <div class="label-main m-r-8">信息来源</div>-->
          <!--            <div class="label-text line-1" style="flex: 1">-->
          <!--              {{ item.projectSourceStr || '&#45;&#45;' }}-->
          <!--            </div>-->
          <!--          </div>-->
        </div>
      </div>
      <div v-else class="no-info flex align-items-center m-t-16">
        <svg-icon icon-class="error-circle" />
        <div class="m-l-8">暂未获取到项目信息，请及时关注和提醒</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardList',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showContent: true
    }
  },
  methods: {
    delHandle(id) {
      this.$emit('delHandle', id)
    },
    detailHandle() {
      this.$router.push({
        path: '/enterPark/intentCustomerDetail',
        query: {
          id: this.item.id,
          status: this.item.status
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card-list-container {
  .car-col {
    //height: 174px;
    margin-top: 16px;
    overflow: hidden;
    transition: all 0.3s ease;
    &.active {
      height: 0;
      margin-top: 0;
    }
  }
  .content-container {
    max-height: 222px;
    overflow-y: scroll;
    &::-webkit-scrollbar-thumb {
      background-color: transparent !important;
    }
    &::-webkit-scrollbar-track-piece {
      background: transparent;
    }
    &:hover {
      &::-webkit-scrollbar-thumb {
        background-color: #ddd !important;
      }
    }
  }
  .card-list-main {
    height: 100%;
    padding: 16px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e7e7e7;
    &:hover {
      background-color: rgba(237, 123, 47, 0.05);
    }
  }
  .title {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    font-weight: 600;
    padding-bottom: 16px;
    border-bottom: 1px solid #e7e7e7;
  }
  .label-main {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.4);
    line-height: 22px;
  }
  .label-text {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
  }
  .no-info {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.4);
    line-height: 22px;
  }
}

//::v-deep {
//  ::-webkit-scrollbar {
//    width: 0 !important;
//    height: 0 !important;
//    &:hover {
//      width: 4px !important;
//    }
//  }
//}
</style>
