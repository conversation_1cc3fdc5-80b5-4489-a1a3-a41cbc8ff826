<template>
  <div class="project-create-container w100 min-h100 bg-white">
    <basic-card>
      <div class="header-container-main w100 m-b-32">
        <header-fixed
          :list="list"
          :current="current"
          @tabsChange="tabsChange"
          @toProjectDetail="toProjectDetail"
        />
      </div>
      <div class="project-tab-left" v-if="current === 1">
        <div class="flex justify-content-center m-b-24">
          <img
            style="width: 120px; height: 120px"
            src="./image/bg-empty.png"
            alt=""
          />
        </div>
        <div class="font-size-14 line-height-22" style="text-align: center">
          暂未获取到项目信息，请及时关注和提醒企业填写
        </div>
        <div class="m-t-16 flex justify-content-center">
          <el-input
            style="width: 496px"
            placeholder="请输入内容"
            v-model="webLink"
            readonly
          >
            <template slot="append">
              <div
                class="color-black pointer"
                @click="copyHandle(webLink, $event)"
              >
                复制链接
              </div>
            </template>
          </el-input>
          <div class="m-l-8">
            <el-button type="primary" @click="sendHandle" :disabled="disabled">
              短信提醒
              <el-tooltip
                effect="dark"
                content="此项目链接还没有企业登录填写"
                placement="top"
              >
                <i v-if="disabled" class="el-icon-question el-icon--right"></i>
              </el-tooltip>
            </el-button>
          </div>
        </div>
      </div>
      <div class="project-tab-right" v-else>
        <proposed-lease ref="proposedLease" />
      </div>
    </basic-card>
    <div class="footer" v-if="current === 2">
      <div v-if="$route.query.edit">
        <el-button type="info" @click="$router.go(-1)">取消</el-button>
        <el-button type="success" @click="saveHandle">确定</el-button>
      </div>
      <el-button v-else type="success" @click="saveHandle">保存</el-button>
    </div>
  </div>
</template>

<script>
import HeaderFixed from '@/views/manage/house/settleIn/project-special/intentCustomer/components/headerFixed'
import handleClipboard from '@/utils/clipboard'
import ProposedLease from '@/views/manage/house/settleIn/project-special/intentCustomer/projectCreate/proposedLease'
import { getNoticeEnterprise } from '@/views/manage/house/settleIn/project-special/intentCustomer/api'
import { mapGetters } from 'vuex'

export default {
  name: 'index',
  components: { ProposedLease, HeaderFixed },
  data() {
    return {
      disabled: true,
      webLink: '',
      list: [
        {
          label: '项目信息',
          value: 1
        },
        {
          label: '拟租赁',
          value: 2
        }
      ],
      current: 1
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    let { current } = this.$route.query
    if (current) {
      this.current = Number(current)
    }
  },
  mounted() {
    this.getDomainAndPort()
  },
  methods: {
    // 发送短信
    async sendHandle() {
      let id = this.$route.query.projectId
      await getNoticeEnterprise(id)
      this.$toast.success('短信已发送')
    },
    toProjectDetail() {
      this.$router.push({
        path: '/investment/index',
        query: {
          detail: 1,
          id: this.$route.query.meProjectId
        }
      })
    },
    tabsChange(e) {
      this.current = e
    },
    copyHandle(item, e) {
      handleClipboard(item, e)
    },
    getDomainAndPort() {
      let id = this.$route.query.projectId
      let pathStr = `${this.userInfo.website}/screen/settledCreate?id=${id}&isBind=true`
      this.webLink = pathStr
    },
    // 保存
    saveHandle() {
      this.$refs.proposedLease.submitHandle()
    }
  }
}
</script>

<style lang="scss" scoped>
.project-create-container {
  margin-bottom: 65px;
  .project-tab-left {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    .text-info {
      margin-top: 8px;
      font-size: 14px;
      line-height: 20px;
      color: rgba(0, 0, 0, 0.6);
      text-align: center;
    }
  }
  .project-tab-right {
    width: 1200px;
    margin: 0 auto;
  }
  .footer {
    position: fixed;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: end;
    padding: 16px 48px 16px 16px;
    background-color: #fff;
    width: 100%;
    height: 65px;
    border-top: 1px solid #dcdcdc;
    z-index: 999;
  }
}
</style>
