<template>
  <div class="table-header-container flex">
    <div class="header-left">
      <div class="building-text">楼栋</div>
      <div class="floor-text">楼层</div>
    </div>
    <div class="header-right w100 line-height-40">
      <span class="m-r-16">{{ parkName }}{{ building }}</span>
      <span v-if="showFlag" class="color-primary pointer" @click="hideRoom"
        >隐藏未选中的房间</span
      >
      <span v-else class="color-primary pointer" @click="hideRoom"
        >显示全部房间</span
      >
    </div>
  </div>
</template>

<script>
export default {
  name: 'SpecialTableTop',
  props: {
    parkName: {
      type: String
    }
  },
  data() {
    return {
      isAll: true,
      buildings: [],
      buildingId: [],
      building: '',
      showFlag: true
    }
  },
  methods: {
    hideRoom() {
      this.showFlag = !this.showFlag
      this.$emit('hideRoom', this.showFlag)
    }
  }
  // inject: ["BoardSpecialTable"],
  // methods: {
  //   // 重置
  //   resetHandle() {
  //     this.isAll = true
  //     this.buildingId = []
  //   },
  //   // 不限
  //   allHandle() {
  //     this.resetHandle()
  //     this.BoardSpecialTable.$refs.specialTableRight.getBuildingId(this.buildingId)
  //   },
  //   // 获取楼栋
  //   getBoardBuildings(parkId) {
  //     this.resetHandle()
  //     getBoardBuildings(parkId).then(res => {
  //       this.buildings = res || []
  //     })
  //   },
  //   // 子组件选中的楼栋
  //   selectHandle(list = []) {
  //     this.buildingId = list
  //     this.isAll = !list.length
  //     this.BoardSpecialTable.$refs.specialTableRight.getBuildingId(this.buildingId)
  //   }
  // }
}
</script>

<style scoped lang="scss">
.table-header-container {
  height: 40px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.6);
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-light);
  .header-left {
    width: 60px;
    height: 100%;
    flex: none;
    @include background_color(--background-color-regular);
    border-radius: 3px 0 0 0;
    font-size: 12px;
    position: relative;
    .building-text {
      position: absolute;
      top: 3px;
      right: 4px;
    }
    .floor-text {
      position: absolute;
      left: 4px;
      bottom: 3px;
    }
    &::after {
      display: block;
      content: '';
      width: 50px;
      height: 1px;
      background: rgba(0, 0, 0, 0.6);
      transform: rotate(30deg);
      position: absolute;
      top: 20px;
      left: 6px;
    }
  }
  .header-right {
    width: calc(100% - 60px);
    background-color: #f3f3f3;
    text-align: center;
    .item-all {
      width: 40px;
      text-align: center;
      line-height: 40px;
      cursor: pointer;
      flex: none;
      border-right-width: 1px;
      border-style: solid;
      @include border_color(--border-color-light);
      &.active {
        background: #e9f0ff;
      }
    }
  }
}
</style>
