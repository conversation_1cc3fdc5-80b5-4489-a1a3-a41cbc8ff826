<template>
  <el-form
    class="form-wrapper"
    :model="formData"
    ref="ruleForm"
    @submit.native.prevent
    :rules="rules"
  >
    <!-- 申请信息 -->
    <apply-info :formData="formData" />
    <!-- 公司/项目简介 -->
    <enterprise-info ref="enterpriseInfo" :formData="formData" />
    <!-- 需求说明 -->
    <demand-illustrate :formData="formData" />
    <!-- 入园材料 -->
    <park-material :formData="formData" />
  </el-form>
</template>

<script>
import ApplyInfo from './applyInfo'
import EnterpriseInfo from './enterpriseInfo'
import DemandIllustrate from './demandIllustrate'
import ParkMaterial from './parkMaterial'
import { uploaderType } from '../../configure'
import {
  getMeRecordDetail,
  getCXYDetail,
  getProjectInfoDetail
} from '../../../api'

export default {
  name: 'SettledCreate',
  components: {
    ParkMaterial,
    DemandIllustrate,
    EnterpriseInfo,
    ApplyInfo
  },
  provide() {
    return {
      CreateForm: this
    }
  },
  data() {
    return {
      formData: {
        alumniType: true, // 中国科大校友企业
        overseasType: true, // 留学人员企业
        recommendAttachIds: [], // 推荐文件
        servicesNeeds: true, // 是否有服务需求
        financingNeeds: true, // 是否有服务需求
        projectServices: [], // 需要培训服务的内容
        financingMethod: [] // 需要培训服务的内容
      },
      rules: {
        propertyRight: [
          {
            required: true,
            message: '请输入知识产权情况',
            trigger: ['change', 'blur']
          }
        ],
        shareholder: [
          {
            required: true,
            message: '请输入股东信息',
            trigger: ['change', 'blur']
          }
        ],
      }
    }
  },
  inject: ['SettledCreate'],
  async mounted() {
    // const { id, type } = this.$route.query
    const { edit } = this.$route.query
    this.$refs.ruleForm.clearValidate()
    // if (type === '3') return
    // if (type === '1') {
    //   this.getCXYDetail(id)
    // } else {
    //   this.getMeRecordDetail(id)
    // }
    if (edit) await this.initData()
  },
  methods: {
    getMeRecordDetail(id) {
      getMeRecordDetail({ id }).then(res => {
        if (!res) return false
        this.$set(this.formData, 'enterpriseName', res.enterpriseName)
        this.$set(this.formData, 'creditCode', res.creditCode)
      })
    },
    getCXYDetail(id) {
      getCXYDetail({ id }).then(res => {
        if (!res) return false
        this.$set(this.formData, 'enterpriseName', res.entName)
        this.$set(this.formData, 'creditCode', res.creditCode)
      })
    },
    async initData() {
      let id = this.$route.query.id
      const res = await getProjectInfoDetail(id)
      // 附件相关需要的格式
      const attachKeys = [
        'recommendAttachIds',
        // 'projectAttachIds',
        'businessLicenseAttachIds',
        'identificationAttachIds',
        'paymentCertificateAttachIds',
        'applicationAttachIds',
        'intellectualPropertyAttachIds',
        'otherCertificatesAttachIds',
        'zjtxxjrAttach',
        'gsAttach',
        'szjtxAttach',
        'gczAttach',
        'highTecAttach',
      ]
      attachKeys.forEach(item => {
        res[item] =
          JSON.stringify(res[item]) === '{}' ? [] : res[item][uploaderType]
      })
      // 数组多选字符串转数组
      const multipleKeys = [
        // 'industry',
        'financingMethod',
        'projectServices',
        'useType',
        'environmental'
      ]
      multipleKeys.forEach(item => {
        if (!res[item]) {
          res[item] = []
        } else if (res[item].length === 1) {
          res[item] = [parseFloat(res[item])]
        } else {
          const arr = []
          const itemList = res[item].split(',')
          itemList.forEach(val => {
            arr.push(parseFloat(val))
          })
          res[item] = arr
        }
      })
      this.formData = { ...res }
      console.log('formData------',this.formData)
      this.$refs.enterpriseInfo.initData(this.formData)
      await this.$nextTick()
      this.$refs.ruleForm.clearValidate()
      this.SettledCreate.$refs.directory.initData(true)
    }
  }
}
</script>

<style scoped lang="scss">
.form-wrapper {
  //width: calc(100% - 320px);
  padding: 0 24px;
}
:deep(.flex) {
  display: flex;
  .flex-1 {
    flex: 1;
  }
}
:deep(.info-title) {
  height: 28px;
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.9);
  line-height: 28px;
  margin-bottom: 24px;
}
:deep(.item-form-container) {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px !important;
  position: relative;
  .el-form-item__label {
    display: flex;
  }
  .add-btn {
    padding: 5px 16px;
    font-size: 14px;
    line-height: 20px;
    position: absolute;
    top: -32px;
    right: 0;
    .el-icon-plus {
      margin-right: 3px;
    }
  }
  .table-container {
    margin-top: 16px;
    border: 1px solid #e7e7e7;
    border-bottom: none;
    font-size: 14px;
    .warning-item {
      color: #ed7b2f;
    }
    .danger-item {
      margin-left: 20px;
    }
  }
}
:deep(.custom-item-form) {
  .el-form-item__label {
    display: block;
  }
}
:deep(.max-width-100 .input-wrapper) {
  max-width: 100% !important;
}
::v-deep {
  .el-table {
    thead {
      th.el-table__cell {
        padding: 10px 0;
        .cell {
          font-weight: 400;
          color: rgba(0, 0, 0, 0.4);
          line-height: 22px;
        }
      }
    }
  }
}
</style>
