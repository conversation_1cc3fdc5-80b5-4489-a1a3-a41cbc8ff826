<template>
  <div class="directory-container">
    <el-scrollbar style="width: 100%; height: 100%">
      <div class="directory-wrapper">
        <div
          class="directory-item"
          v-for="(item, index) in list"
          :key="index"
          :class="{
            active: activeId === item.id && activeId,
            'item-title': item.nodeName === 'H1'
          }"
          @click="itemHandle(item)"
        >
          <div
            class="item-name"
            :class="{
              required: item.prop,
              'has-child': item.hasChild,
              'is-child': item.isChild,
              'is-weight': item.nodeName === 'H2'
            }"
          >
            {{ item.name }}
          </div>
          <svg-icon
            v-if="item.requiredPass"
            class="required-success"
            icon-class="check-circle-filled"
          />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import { Debounce } from '../../utils/tools'

export default {
  name: 'SettledDirectory',
  props: {
    containerRef: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      list: [],
      scrollData: [],
      activeId: ''
    }
  },
  inject: ['SettledCreate'],
  mounted() {
    this.enableListener()
  },
  beforeDestroy() {
    this.destroyListener()
  },
  methods: {
    // 下发进度改变
    progressHandle() {
      this.SettledCreate.$refs.createFooter &&
        this.SettledCreate.$refs.createFooter.initData(this.scrollData)
    },
    // 表单验证
    validatorHandle(prop, isPass) {
      if (!prop) return
      // 初始化进度
      this.list.forEach((item, index) => {
        if (item.id === prop) {
          this.$set(this.list[index], 'requiredPass', isPass)
          this.progressHandle()
        }
      })
    },
    // 监听启用
    enableListener() {
      let element = document.querySelector('#el-scroll')
      element && element.addEventListener('scroll', this.handleScroll, true)
    },
    // 监听销毁
    destroyListener() {
      let element = document.querySelector('#el-scroll')
      element && element.removeEventListener('scroll', this.handleScroll, true)
    },
    // 页面滚动处理
    handleScroll() {
      let scrollTop = this.SettledCreate.$refs.elScrollbar.wrap.scrollTop
      this.scrollData.forEach(item => {
        if (document.querySelector('#' + item.id).offsetTop < scrollTop + 20) {
          this.activeId = item.id
        }
      })
    },
    // 初始化目录
    async initData(first = false) {
      await this.$nextTick()
      const formData = this.SettledCreate.$refs[this.containerRef].formData
      const list = document.querySelectorAll('.DirectoryLabel')
      this.list = []
      list.forEach(item => {
        let requiredPass = false
        requiredPass = Array.isArray(formData[item.id])
          ? !!formData[item.id].length
          : formData[item.id] !== '' &&
            formData[item.id] !== null &&
            formData[item.id] !== undefined
        // 法定代表人联系方式单独处理
        if (item.id === 'legalPersonAndPhone') {
          if (formData.legalPerson || formData.legalPersonPhone) {
            requiredPass = true
          }
        }
        this.list.push({
          id: item.id,
          name: item.getAttribute('label'),
          nodeName: item.nodeName,
          hasChild: item.getAttribute('has-child') === 'true',
          isChild: item.getAttribute('is-child') === 'true',
          prop: !!item.getAttribute('prop'),
          requiredPass
        })
      })
      this.scrollData = this.list.filter(item => item.id)
      this.progressHandle()
      if (!first) return false
      const findItem = this.list.find(item => item.nodeName !== 'H1')
      if (findItem && findItem.id) this.activeId = findItem.id
    },
    // 点击目录
    itemHandle(row) {
      if (!row || !row.id) return
      if (row.nodeName === 'H1') return
      this.destroyListener()
      this.activeId = row.id
      let navPage = document.querySelector(`#${row.id}`)
      this.SettledCreate.navPageHandle(navPage)
      Debounce(this.enableListener, 1000)
    }
  }
}
</script>

<style scoped lang="scss">
.directory-container {
  //width: 360px;
  height: 100%;
  flex-shrink: 0;
}
.directory-wrapper {
  width: 100%;
  height: 100%;
  border-right: 1px solid #e9f0ff;
  padding: 33px 16px 33px 40px;
  .directory-item {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    padding: 6px 16px;
    position: relative;
    border-radius: 3px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    &.active {
      background: #e9f0ff;
      color: #ed7b2f;
      &:after {
        display: block;
        content: '';
        width: 2px;
        height: 34px;
        background: #ed7b2f;
        position: absolute;
        top: 0;
        right: -16px;
      }
    }
    .required:before {
      display: block;
      content: '*';
      color: #e34d59;
      position: absolute;
      left: 8px;
      top: 8px;
    }
    .item-name {
      width: calc(100% - 14px);
      height: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    &.item-title {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
      cursor: default;
      padding-left: 0;
    }
    .is-weight {
      font-weight: 700;
    }
    .has-child {
      padding-left: 8px;
      &:after {
        width: 0;
        height: 0;
        display: block;
        content: '';
        border-top: 6px solid rgba(0, 0, 0, 0.9);
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        font-size: 0;
        line-height: 0;
        position: absolute;
        left: 8px;
        top: 14px;
      }
    }
    .is-child {
      padding-left: 8px;
      &.required:before {
        left: 16px;
      }
    }
    .required-success {
      flex-shrink: 0;
      @include font_color(--color-success);
    }
  }
}
::v-deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
    .el-scrollbar__view {
      padding: 0 !important;
      display: block;
    }
  }
  .is-horizontal {
    display: none;
  }
}
</style>
