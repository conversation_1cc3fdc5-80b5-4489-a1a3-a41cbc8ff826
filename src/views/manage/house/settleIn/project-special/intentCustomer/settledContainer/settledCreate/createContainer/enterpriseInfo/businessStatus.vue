<template>
  <el-form-item
    required
    class="item-form-container"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <!--    <el-button class="add-btn" type="primary" @click="addHandle">-->
    <!--      <i class="el-icon-plus"></i>-->
    <!--      <span>添加</span>-->
    <!--    </el-button>-->
    <el-table
      class="table-container"
      :data="tableData"
      stripe
      :row-style="{
        height: '50px',
        color: 'rgba(0,0,0,0.9)'
      }"
    >
      <el-table-column
        v-for="(item, index) in tableColumn"
        :key="index"
        :prop="item.prop"
        :label="item.label"
      >
        <template slot-scope="scope">
          <span :class="{ 'warning-item': item.warning }">{{
            scope.row[item.prop] | noData
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100px">
        <template slot-scope="scope">
          <el-link
            type="primary"
            :underline="false"
            @click="editHandle(scope.row)"
            >编辑</el-link
          >
          <!--          <el-link-->
          <!--            class="danger-item"-->
          <!--            type="danger"-->
          <!--            :underline="false"-->
          <!--            @click="deleteHandle(scope.row)"-->
          <!--            >删除</el-link-->
          <!--          >-->
        </template>
      </el-table-column>
      <empty-container slot="empty" :width="120" :height="120" padding="33px" />
    </el-table>
    <el-dialog
      custom-class="custom-item-form"
      :title="dialogTitle"
      width="540px"
      :visible.sync="dialogVisible"
      :modal-append-to-body="false"
      :append-to-body="false"
      :close-on-click-modal="false"
      :before-close="beforeCloseHandle"
    >
      <el-form
        v-if="dialogVisible"
        ref="itemFormRef"
        :model="itemForm"
        label-position="right"
        label-width="80px"
        :rules="itemFormRules"
      >
        <el-form-item label="时间范围" prop="year">
          <!--          <el-date-picker-->
          <!--            style="width: 100%"-->
          <!--            v-model="itemForm.year"-->
          <!--            type="year"-->
          <!--            placeholder="选择年"-->
          <!--            value-format="yyyy"-->
          <!--            :picker-options="pickerOptions"-->
          <!--          >-->
          <!--          </el-date-picker>-->
          <el-select
            v-model="itemForm.year"
            placeholder="请选择时间范围"
            disabled
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产值" prop="outputValue">
          <input-com
            v-model="itemForm.outputValue"
            label="产值"
            type="Input"
            sortText="万元"
            inputType="number"
            :integer="true"
          ></input-com>
        </el-form-item>
        <el-form-item label="营业收入" prop="businessIncome">
          <input-com
            v-model="itemForm.businessIncome"
            label="营业收入"
            type="Input"
            sortText="万元"
            inputType="number"
            :integer="true"
          ></input-com>
        </el-form-item>
        <el-form-item label="上缴税收" prop="taxPaid">
          <input-com
            v-model="itemForm.taxPaid"
            label="上缴税收"
            type="Input"
            sortText="万元"
            inputType="number"
            :integer="true"
          ></input-com>
        </el-form-item>
        <el-form-item label="净利润" prop="netProfit">
          <input-com
            v-model="itemForm.netProfit"
            label="净利润"
            type="Input"
            sortText="万元"
            inputType="number"
            :integer="true"
          ></input-com>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="beforeCloseHandle">取 消</el-button>
        <el-button type="primary" @click="validateHandle">确 定</el-button>
      </div>
    </el-dialog>
  </el-form-item>
</template>

<script>
import InputCom from '../../components/InputCom'
import { generateID } from '../../../../utils/tools'
import RulesMixins from '../rules'
import dayjs from 'dayjs'
import EmptyContainer from '../../../../components/EmptyContainer'
import { getAllEnumList } from '@/views/manage/house/settleIn/project-special/intentCustomer/api'

export default {
  name: 'BusinessStatus',
  components: { EmptyContainer, InputCom },
  props: {
    value: {
      required: true
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [RulesMixins],
  data() {
    const businessValidator = (rule, value, callback) => {
      if (/^-?\d{1,5}(\.\d{1,2})?$/.test(+value)) return callback()
      callback(new Error('最多可输入5位数，且最多保留2位小数'))
    }
    return {
      dialogTitle: '添加',
      itemForm: {
        year: null,
        outputValue: '',
        businessIncome: '',
        taxPaid: '',
        netProfit: ''
      },
      itemFormRules: {
        year: [
          {
            required: true,
            message: '请选择时间范围',
            target: ['change', 'blur']
          }
        ],
        outputValue: [
          {
            required: true,
            message: '请输入产值',
            target: ['change', 'blur']
          },
          {
            required: true,
            validator: businessValidator,
            target: ['change', 'blur']
          }
        ],
        businessIncome: [
          {
            required: true,
            message: '请输入营业收入',
            target: ['change', 'blur']
          },
          {
            required: true,
            validator: businessValidator,
            target: ['change', 'blur']
          }
        ],
        taxPaid: [
          {
            required: true,
            message: '请输入上缴税收',
            target: ['change', 'blur']
          },
          {
            required: true,
            validator: businessValidator,
            target: ['change', 'blur']
          }
        ],
        netProfit: [
          {
            required: true,
            message: '请输入净利润',
            target: ['change', 'blur']
          },
          {
            required: true,
            validator: businessValidator,
            target: ['change', 'blur']
          }
        ]
      },
      dialogVisible: false,
      tableColumn: [
        {
          label: '年度',
          prop: 'year'
        },
        {
          label: '产值（万元）',
          prop: 'outputValue',
          warning: true
        },
        {
          label: '营业收入（万元）',
          prop: 'businessIncome',
          warning: true
        },
        {
          label: '上缴税收（万元）',
          prop: 'taxPaid',
          warning: true
        },
        {
          label: '净利润（万元）',
          prop: 'netProfit',
          warning: true
        }
      ],
      tableData: [],
      pickerOptions: {
        disabledDate: time => {
          return (
            time.getTime() >= dayjs().add(1, 'year').startOf('year').valueOf()
          )
        }
      },
      options: []
    }
  },
  inject: ['SettledCreate', 'CreateForm'],
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
        if (!val || !val.length) {
          this.SettledCreate.$refs.directory.validatorHandle(
            'operationList',
            false
          )
        } else {
          this.SettledCreate.$refs.directory.validatorHandle(
            'operationList',
            true
          )
        }
        this.CreateForm.$refs &&
          this.CreateForm.$refs.ruleForm.validateField('operationList')
      }
    }
  },
  mounted() {
    this.getEnumList()
    this._value = this.tableData
  },
  methods: {
    // 获取枚举
    async getEnumList() {
      // 年度
      const res = await getAllEnumList({ type: 'operationYear' })
      this.options = res.map(item => {
        return { label: item.value, value: item.key }
      })
      this.tableData = this.options.map((item, index) => {
        return {
          id: index + 1,
          year: item.value,
          outputValue: '',
          businessIncome: '',
          taxPaid: '',
          netProfit: ''
        }
      })
    },
    // 数据初始化
    initData(formData) {
      const operationList = formData.operationList
      if (operationList && operationList.length) {
        this.tableData = formData.operationList
      } else {
        this.tableData = this.options.map((item, index) => {
          return {
            id: index + 1,
            year: item.value,
            outputValue: '',
            businessIncome: '',
            taxPaid: '',
            netProfit: ''
          }
        })
      }
      this._value = this.tableData
    },
    // 删除某一列
    deleteHandle(row) {
      this.$confirm('是否删除此经营情况?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.tableData.forEach((item, index) => {
          if (item.id === row.id) {
            this.tableData.splice(index, 1)
          }
        })
        this.$message({ type: 'success', message: '删除成功!' })
        this._value = this.tableData
      })
    },
    // 表单验证
    validateHandle() {
      this.$refs.itemFormRef.validate(isValid => {
        if (!isValid) return false
        if (this.itemForm.id) {
          this.tableData.forEach((item, index) => {
            if (item.id === this.itemForm.id) {
              this.tableData[index] = { ...this.itemForm }
            }
          })
          this.promptHandle('编辑')
        } else {
          const exists = this.tableData.some(item => {
            return item.year === this.itemForm.year
          })
          if (exists)
            return this.$message.warning(
              `${this.itemForm.year}年财报已经存在，请勿重复添加`
            )
          this.tableData.push({
            id: generateID(),
            ...this.itemForm
          })
          this.promptHandle('新增')
        }
        this.beforeCloseHandle()
      })
    },
    // 提示
    promptHandle(tips) {
      this.$message({ type: 'success', message: `${tips}成功!` })
      this._value = this.tableData
    },
    // 编辑
    editHandle(row) {
      this.itemForm = { ...row }
      this.dialogTitle = '编辑'
      this.dialogVisible = true
      this.CreateForm.$refs &&
        this.CreateForm.$refs.ruleForm.clearValidate(this.$attrs.prop)
      this.$nextTick(() => {
        this.$refs.itemFormRef.resetFields()
      })
    },
    // 添加弹窗
    addHandle() {
      this.dialogTitle = '添加'
      this.dialogVisible = true
      this.CreateForm.$refs &&
        this.CreateForm.$refs.ruleForm.clearValidate(this.$attrs.prop)
    },
    // 弹窗关闭
    beforeCloseHandle() {
      this.itemForm = this.$options.data().itemForm
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped lang="scss"></style>
