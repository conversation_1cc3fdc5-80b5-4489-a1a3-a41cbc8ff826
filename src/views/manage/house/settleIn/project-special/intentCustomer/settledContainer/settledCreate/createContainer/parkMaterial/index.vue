<template>
  <div class="park-material-container">
    <h1 label="入园材料" class="info-title DirectoryLabel">入园材料</h1>
    <div class="material-wrapper">
      <div class="material-header">
        <div class="serial-num">序号</div>
        <div class="material">相关材料</div>
        <div class="serial-num primary-num">附件个数</div>
        <div class="operate">操作</div>
      </div>
      <div class="material-body">
        <div
          class="material-item"
          v-for="(item, index) in tableData"
          :key="item.value"
        >
          <div class="serial-num primary-num">{{ index + 1 }}</div>
          <div
            class="material DirectoryLabel"
            :id="item.value"
            :prop="item.prop"
            :label="item.label"
          >
            <el-form-item
              :prop="item.prop"
              :label="item.label"
              :rules="fileRules(item.prop)"
            >
              <div slot="label" class="flex align-items-center">
                <span>{{ item.label }}</span>
<!--                <el-button class="m-l-8" v-if="index === 0" type="primary" size="mini" @click="exportApplicationForm">-->
<!--                  导出申请表-->
<!--                </el-button>-->
              </div>
            </el-form-item>
          </div>
          <div class="serial-num" style="padding-left: 20px">
            <el-link
              v-if="formData[item.value] && formData[item.value].length"
              type="primary"
              :underline="false"
              @click="fileClick(item.value)"
            >
              {{ formData[item.value].length }}
            </el-link>
            <el-link
              v-else
              type="primary"
              :underline="false"
              @click="fileClick(item.value)"
              >0
            </el-link>
          </div>
          <div class="operate">
            <el-link
              type="primary"
              :underline="false"
              @click="fileClick(item.value)"
              >上传附件</el-link
            >
          </div>
        </div>
      </div>
    </div>
    <basic-dialog
      custom-class="park-material-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      width="560px"
      @confirmDialog="confirmDialog"
    >
      <div class="uploader-wrapper">
        <uploader
          v-model="formData[currentValue]"
          uploaderText="上传文件"
          :uploadData="uploadData"
          :mulity="true"
          :maxLength="3"
          :limit="3"
          :key="uploaderKey"
        />
        <div class="uploader-tips">最多支持上传3个文件</div>
      </div>
      <empty-container
        v-if="!formData[currentValue] || !formData[currentValue].length"
        :width="120"
        :height="120"
        tipsText="暂无附件，请上传文件"
      />
    </basic-dialog>
  </div>
</template>

<script>
import BasicDialog from '@/components/BasicDialog'
import Uploader from '@/components/Uploader'
import EmptyContainer from '../../../../components/EmptyContainer'
import { uploaderType } from '../../../configure'
import dayJs from 'dayjs'
import { projectExport } from '../../../../api'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'

export default {
  name: 'parkMaterial',
  components: { EmptyContainer, BasicDialog, Uploader },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      uploadData: {
        type: uploaderType
      },
      tableData: [
        {
          label:
            '《中安创谷科技园意向入驻企业申请表》请下载当前内容为pdf并签字盖章',
          prop: 'applicationAttachIds',
          value: 'applicationAttachIds'
        },
        // {
        //   label:
        //     '《中安创谷科技园意向入驻企业申请表》请下载当前内容为pdf并签字盖章',
        //   prop: 'projectAttachIds',
        //   value: 'projectAttachIds'
        // },
        {
          label: '公司简介，对企业或项目的更多内容介绍',
          prop: 'identificationAttachIds',
          value: 'identificationAttachIds'
        },
        {
          label: '公司营业执照副本复印件，要求扫描清晰可辨',
          prop: 'businessLicenseAttachIds',
          value: 'businessLicenseAttachIds'
        },

        // {
        //   label: '国家高企证书电子版，仅高企需提供，没有可不传',
        //   prop: '',
        //   value: 'paymentCertificateAttachIds'
        // },
        // {
        //   label: '国家高企证书电子版，仅高企需提供，没有可不传',
        //   prop: '',
        //   value: 'intellectualPropertyAttachIds'
        // },
        {
          label: '其他佐证材料',
          prop: '',
          value: 'otherCertificatesAttachIds'
        }
      ],
      dialogVisible: false,
      title: '附件详情',
      currentValue: '',
      uploaderKey: Math.random()
    }
  },
  inject: ['SettledCreate', 'CreateForm'],
  watch: {
    formData: {
      handler(value) {
        const itemFile = value[this.currentValue]
        if (!itemFile) return
        if (itemFile && itemFile.length) {
          this.CreateForm.$refs.ruleForm &&
            this.CreateForm.$refs.ruleForm.validateField(this.currentValue)
          this.SettledCreate.$refs.directory &&
            this.SettledCreate.$refs.directory.validatorHandle(
              this.currentValue,
              true
            )
        } else {
          this.CreateForm.$refs.ruleForm &&
            this.CreateForm.$refs.ruleForm.validateField(this.currentValue)
          this.SettledCreate.$refs.directory &&
            this.SettledCreate.$refs.directory.validatorHandle(
              this.currentValue,
              false
            )
        }
      },
      deep: true
    }
  },
  methods: {
    // 导出项目申请表
    exportProjectHandle(id) {
      const name = `${dayJs().format('YYYY-MM-DD')}项目申请表.pdf`
      const params = {
        id
      }
      let url = projectExport() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(url, 'pdf', name)
    },
    // 导出事件
    exportApplicationForm() {
      this.SettledCreate.$refs.createContainer.$refs.ruleForm.validate((valid, invalidFields) => {
        if (!valid) {
          const firstField = Object.keys(invalidFields)[0];
          let firstError = invalidFields[firstField][0];

          if (typeof firstError === 'object' && firstError.message) {
            firstError = firstError.message;
          }
          if (typeof firstError === 'string') {
            firstError = firstError.replace(/^Error:\\s*/, '');
          }

          this.$message.warning(firstError);
          return false;

        }
        this.SettledCreate.$refs.createFooter.autoSave(id => {
          this.exportProjectHandle(id)
        })

      });
    },
    // 确定
    confirmDialog() {
      this.dialogVisible = false
    },
    fileClick(val) {
      this.currentValue = val
      this.uploaderKey = Math.random()
      this.dialogVisible = true
    },
    fileRules(prop) {
      return [
        {
          required: !!prop,
          validator: (rule, value, callback) => {
            if (!value || !value.length) {
              this.SettledCreate.$refs.directory.validatorHandle(
                rule.field,
                false
              )
              callback(new Error(`请上传相关材料`))
            } else {
              this.SettledCreate.$refs.directory.validatorHandle(
                rule.field,
                true
              )
              callback()
            }
          },
          target: ['blur', 'change']
        }
      ]
    }
  }
}
</script>

<style scoped lang="scss">
.park-material-container {
  margin-top: 40px;
  .material-wrapper {
    margin-top: 16px;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    font-size: 14px;
    .material-header {
      border-bottom: 1px solid #e7e7e7;
      color: rgba(0, 0, 0, 0.4);
      display: flex;
      height: 42px;
      padding: 10px 24px;
    }
    .serial-num {
      width: 100px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }
    .material {
      width: 100%;
      display: flex;
      align-items: center;
    }
    .operate {
      width: 120px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }
    .material-body {
      .material-item {
        border-bottom: 1px solid #e7e7e7;
        display: flex;
        min-height: 50px;
        padding: 20px 24px;
        &:nth-child(2n) {
          background: #fafafa;
        }
        &:hover {
          background-color: #eee;
        }
        &:last-child {
          border-bottom: none;
        }
        .primary-num {
          color: #ed7b2f;
        }
      }
    }
  }
}
.uploader-wrapper {
  display: flex;
  position: relative;
  .uploader-tips {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4);
    line-height: 20px;
    position: absolute;
    left: 100px;
    top: 8px;
  }
}
::v-deep {
  .el-form-item__label {
    text-align: left;
    display: flex;
    .el-button {
      position: relative;
      z-index: 1;
    }
  }
  .el-form-item {
    margin-bottom: 0 !important;
  }
}
</style>
<style lang="scss">
.park-material-dialog {
  .close-btn {
    display: none;
  }
}
</style>
