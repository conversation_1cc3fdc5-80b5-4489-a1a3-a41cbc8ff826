<template>
  <dialog-cmp
    :title="title"
    :visible.sync="comVisible"
    width="620px"
    @close="close"
    @confirmDialog="confirmDialog"
  >
    <driven-form
      v-if="comVisible"
      :key="key"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
  </dialog-cmp>
</template>

<script>
import { deptList, getByTenantDictType, getUserListByDept } from '@/api/common'
import { getRecordMatter } from '../../../../api'
export default {
  name: 'IssuedCmp',
  inject: ['main'],
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      key: 0,
      comVisible: false,
      questionTypeLabel: '',
      fromModel: {
        smsNotice: false
      },
      formConfigure: {
        descriptors: {
          deptId: {
            form: 'select',
            label: '部门',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择部门'
              }
            ],
            attrs: {
              filterable: true
            },
            events: {
              change: this.deptChange
            }
          },
          receiverId: {
            form: 'select',
            label: '下发人员',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择下发人员'
              }
            ],
            attrs: {
              filterable: true
            }
          },
          questionType: {
            form: 'select',
            label: '事项类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择事项类型'
              }
            ],
            events: {
              change: this.questionTypeChange
            }
          },
          matterDescription: {
            form: 'input',
            label: '事项描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入事项描述'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 2,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            }
          },
          attachIds: {
            form: 'component',
            label: '相关图片',
            rule: [
              {
                type: 'array',
                message: '请上传图片信息'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'park'
              },
              accept: 'image/*',
              mulity: true,
              limit: 3
            },
            customTips: () => {
              return (
                <div class="font-size-12 line-height-20">
                  请上传格式为png/jpg不大于10MB的图片，图片不得超过三个
                </div>
              )
            }
          },
          smsNotice: {
            form: 'switch',
            label: '发送短信',
            rule: [{ type: 'boolean' }]
          }
        }
      }
    }
  },
  mounted() {
    this.transactionType()
    this.deptList()
  },
  methods: {
    fileList(attachMap = {}) {
      if (attachMap.park && attachMap.park.length > 0) {
        return attachMap.park
      } else if (attachMap.attachList && attachMap.attachList.length > 0) {
        return attachMap.attachList
      } else {
        return []
      }
    },
    replyFn(item) {
      this.main.mattersFinish(item)
      this.key = Math.random()
    },
    questionTypeChange(e) {
      this.questionTypeLabel =
        this.formConfigure.descriptors.questionType.options.find(
          item => item.value === e
        ).label
    },
    async deptChange() {
      //清空下发人员
      this.$set(this.fromModel, 'receiverId', undefined)
      const res = await getUserListByDept({ deptId: this.fromModel.deptId })
      this.formConfigure.descriptors.receiverId.options = res.map(item => {
        return { label: item.nickname, value: item.id }
      })
    },
    transactionType() {
      getByTenantDictType('question_type').then(res => {
        this.formConfigure.descriptors.questionType.options = res.map(item => {
          return {
            label: item.label,
            value: Number(item.value)
          }
        })
      })
    },
    close() {
      this.fromModel = {
        smsNotice: false
      }
      this.$emit('update:visible', false)
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { attachIds } = this.fromModel
          const data = {
            ...this.fromModel,
            questionId: this.$route.query.id,
            smsNotice: this.fromModel.smsNotice ? 1 : 0,
            type: 2
          }
          data.questionType =
            this.formConfigure.descriptors.questionType.options.find(
              item => item.value === Number(this.fromModel.questionType)
            ).label
          if (attachIds.length > 0) {
            data.attachIds = attachIds.map(item => item.id)
          }
          getRecordMatter(data).then(() => {
            this.main.tipsHandle('下发')
            this.main.getCommonMatter()
            this.$emit('update:visible', false)
          })
        }
      })
    },
    deptList() {
      deptList().then(res => {
        this.formConfigure.descriptors.deptId.options = res.map(item => {
          return { label: item.name, value: item.id }
        })
      })
    }
  },
  watch: {
    visible(val) {
      this.comVisible = val
    }
  }
}
</script>

<style scoped lang="scss">
.popover-content {
  max-height: 320px;
  overflow-y: auto;
  .popover-item {
    padding: 8px;
    &:hover {
      background: #f7f9fb;
      cursor: pointer;
      .line-2 {
        color: #ed7b2f;
      }
    }
    .image {
      width: 62px;
      height: 62px;
      border-radius: 3px;
    }
  }
}
</style>
