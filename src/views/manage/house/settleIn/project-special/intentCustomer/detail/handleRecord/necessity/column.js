import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumnDeposit: [
        {
          prop: 'name',
          label: '企业名称',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          label: '保证金额基数(元)',
          prop: 'depositBase',
          minWidth: 140,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.depositBase)}
              </div>
            )
          }
        },
        {
          label: '已缴存金额(元)',
          prop: 'depositBalance',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.depositBalance)}
              </div>
            )
          }
        },
        {
          label: '待缴存金额(元)',
          prop: 'payDeposit',
          minWidth: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payDeposit)}
              </div>
            )
          }
        },
        {
          label: '缴存状态',
          prop: 'depositStatus'
        }
      ],
      tableColumn: [
        {
          prop: 'enterpriseName',
          label: '企业名称',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'cycle',
          label: '账期',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          },
          width: 180
        },
        {
          label: '应收金额(元)',
          prop: 'amount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'paidAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'collectStatusStr',
          width: 120
        },
        {
          label: '操作',
          prop: 'operation',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.billDetailHandle(scope.row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ],
      billRefundTableColumn: [
        {
          prop: 'enterpriseName',
          label: '企业名称',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'cycle',
          label: '账期',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          },
          width: 180
        },
        {
          label: '应收金额(元)',
          prop: 'amount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'paidAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '应退(元)',
          prop: 'backAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.backAmount)}
              </div>
            )
          }
        },
        {
          label: '操作',
          prop: 'operation',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.billDetailHandle(scope.row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ],
      contractTableColumn: [
        {
          label: '合同编号',
          prop: 'numberNo',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goContractDetails(scope.row)
                }}
              >
                {scope.row.numberNo}
              </el-link>
            )
          }
        },
        {
          prop: 'parkName',
          label: '园区'
        },
        {
          label: '入驻房源',
          prop: 'room'
        },
        {
          label: '合同面积(㎡)',
          prop: 'area',
          width: 120
        },
        {
          label: '合同状态',
          prop: 'statusStr',
          width: 120
        }
      ]
    }
  }
}
