<template>
  <div class="project-detail-contain min-h100 bg-white">
    <basic-card>
      <basic-tab
        ref="basicTab"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
        :disabled="detailInfo.adminEntry === 3"
      >
        <div slot="right">
          <el-button
            type="primary"
            v-if="detailInfo.whetherSubmit"
            @click="exportProjectHandle"
            >导出项目表</el-button
          >
          <el-button
            type="primary"
            v-if="detailInfo.status < 9 && current === 3"
            @click="getProposed"
          >
            {{
              leaseInfo && Object.keys(leaseInfo).length > 0 ? '修改' : '添加'
            }}
          </el-button>
          <el-button
            type="primary"
            v-if="
              detailInfo.status === 1 &&
              current === 2 &&
              detailInfo.whetherSubmit
            "
            @click="updateHandle"
            >修改</el-button
          >
          <el-button
            type="primary"
            v-if="
              detailInfo.status === 1 &&
              current === 2 &&
              detailInfo.whetherSubmit
            "
            @click="submitHandle"
          >
            意向入园
          </el-button>
          <el-button
            type="primary"
            v-if="detailInfo.status === 6"
            @click="dialogVisible = true"
            >入园办理</el-button
          >
          <el-button
            type="primary"
            v-if="detailInfo.status === 9 || detailInfo.status === 11"
            @click="terminateHandle"
            >终止办理</el-button
          >
          <el-button
            v-if="
              detailInfo.adminEntry !== 3 &&
              detailInfo.status !== 5 &&
              detailInfo.status !== 10
            "
            type="info"
            @click="assessHandle(2)"
          >
            下发通知
          </el-button>
          <el-button
            v-if="detailInfo.status === 5 && current === 2"
            type="primary"
            @click="againHandle"
            >重新起草</el-button
          >
        </div>
      </basic-tab>
      <div class="project-detail-content">
        <handle-record
          ref="handleRecord"
          v-if="current === 1"
          :detail-info="detailInfo"
        />
        <project-information v-if="current === 2" :detail-info="detailInfo" />
        <proposed-lease-detail v-if="current === 3" :detail-info="leaseInfo" />
        <notification-record
          ref="notification"
          v-if="current === 4"
          :detail-info="detailInfo"
        />
      </div>
    </basic-card>
    <project-form ref="projectForm" @getNewPage="getNewPage" />
    <!--评估-->
    <dialog-cmp
      title="入园办理评估"
      width="35%"
      :visible.sync="dialogVisible"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import ProposedLeaseDetail from './proposedLeaseDetail'
import ProjectInformation from './ProjectInformation'
import NotificationRecord from './notificationRecord'
import HandleRecord from './handleRecord'
import {
  getApplySubmit,
  getProjectInfoDetail,
  getProposedLease,
  getRedraftProject,
  getWillMeetProject,
  projectExport,
  projectReturn
} from '@/views/manage/house/settleIn/project-special/intentCustomer/api'
import ProjectForm from './projectForm'
import descriptorMixins from './descriptor'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'
import dayJs from 'dayjs'

export default {
  name: 'IntentCustomerDetail',
  mixins: [descriptorMixins],
  components: {
    ProjectForm,
    HandleRecord,
    NotificationRecord,
    ProposedLeaseDetail,
    ProjectInformation,
    BasicTab
  },
  data() {
    return {
      dialogVisible: false,
      fromModel: {
        result: true,
        content: '评估通过'
      },
      list: [
        {
          label: '办理记录',
          value: 1
        },
        {
          label: '项目信息',
          value: 2
        },
        {
          label: '拟租赁',
          value: 3
        },
        {
          label: '通知记录',
          value: 4
        }
      ],
      current: 1,
      detailInfo: {},
      leaseInfo: {}
    }
  },
  provide() {
    return {
      IntentCustomerDetail: this
    }
  },
  created() {
    const status = Number(this.$route.query.status)
    if (status && status < 9) {
      this.list = [
        {
          label: '项目信息',
          value: 2
        },
        {
          label: '拟租赁',
          value: 3
        },
        {
          label: '通知记录',
          value: 4
        }
      ]
      this.current = 2
    }
  },
  mounted() {
    this.initHandle()
    this.getProposedLease()
  },
  methods: {
    exportProjectHandle() {
      const name = `${dayJs().format('YYYY-MM-DD')}项目申请表.pdf`
      const params = {
        id: this.detailInfo.id
      }
      let url = projectExport() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(url, 'pdf', name)
    },
    getNewPage() {
      this.initHandle()
      this.getProposedLease()
      if (this.current === 4) {
        this.$refs.notification.getProjectNoticeList()
      }
    },
    // 意向入园
    async submitHandle() {
      await getWillMeetProject(this.detailInfo.id)
      this.$toast.success('意向入园成功')
      this.initHandle()
      this.getProposedLease()
    },
    // 重新起草
    async againHandle() {
      await getRedraftProject(this.detailInfo.id)
      this.$toast.success('重新起草成功')
      this.initHandle()
      this.getProposedLease()
    },
    // 终止办理
    terminateHandle() {
      this.$confirm('确定终止办理？').then(() => {
        projectReturn({ id: this.detailInfo.id }).then(() => {
          this.$toast.success('终止成功')
          this.initHandle()
          this.$refs.handleRecord &&
            this.$refs.handleRecord.$refs.necessity.getRequiredMatters()
        })
      })
    },
    // 项目信息详情
    async initHandle() {
      let id = this.$route.query.id
      const res = await getProjectInfoDetail(id)
      this.detailInfo = res
      if (res.status >= 9) {
        this.list = this.$options.data().list
        this.current = 1
      }
    },
    // 拟租赁详情
    async getProposedLease() {
      let projectId = this.$route.query.id
      const res = await getProposedLease(projectId)
      this.leaseInfo = res
    },
    // 下发通知
    assessHandle(type) {
      let id = this.$route.query.id
      this.$refs.projectForm.init(type, id)
    },
    // 入园办理评估提交
    confirmDialog() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let data = {
            ...this.fromModel,
            projectId: this.$route.query.id
          }
          await getApplySubmit(data)
          this.$toast.success('入园办理评估成功')
          this.dialogVisible = false
          this.initHandle()
        }
      })
    },
    tabsChange(e) {
      this.current = e
    },
    updateHandle() {
      this.$router.push({
        path: '/enterPark/settledCreate',
        query: {
          // examine: this.examine ? 1 : 0,
          // type: '2',
          id: this.detailInfo.id,
          edit: 1
        }
      })
    },
    getProposed() {
      this.$router.push({
        path: '/enterPark/projectCreate',
        query: {
          projectId: this.detailInfo.id,
          current: 2,
          edit:
            this.leaseInfo && Object.keys(this.leaseInfo).length > 0 ? 1 : ''
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.project-detail-contain {
  padding-bottom: 65px;
}
.footer {
  position: fixed;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: end;
  padding: 16px 24px 16px 16px;
  background-color: #fff;
  width: 100%;
  height: 65px;
  border-top: 1px solid #dcdcdc;
  z-index: 999;
}
</style>
