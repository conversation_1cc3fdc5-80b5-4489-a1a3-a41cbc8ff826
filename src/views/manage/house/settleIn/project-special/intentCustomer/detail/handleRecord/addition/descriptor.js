export default {
  data() {
    return {
      additionFormConfigure: {
        descriptors: {
          name: {
            form: 'input',
            label: '事项标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入事项标题'
              }
            ],
            attrs: {
              maxLength: 20
            }
          },
          records: {
            form: 'input',
            label: '事项内容',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入事项内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 200,
              showWordLimit: true
            }
          },
          attach: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'intentCustomer'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      },
      addRecordFormConfigure: {
        descriptors: {
          handle: {
            form: 'radio',
            label: '办理状态',
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择办理状态'
              }
            ],
            options: [
              { label: '未办理', value: false },
              { label: '已办理', value: true }
            ],
            props: {
              button: 'radio-button'
            }
          },
          records: {
            form: 'input',
            label: '记录内容',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入记录内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 200,
              showWordLimit: true
            }
          },
          matterRecordAttachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'intentCustomer'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      }
    }
  }
}
