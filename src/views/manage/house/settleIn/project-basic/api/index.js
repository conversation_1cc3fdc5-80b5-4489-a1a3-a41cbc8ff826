import request from '@/utils/request'

//入园项目库tab数据
export function getProjectTab(params) {
  return request({
    url: `/pjct/admin/project_info/project_status_count`,
    method: 'get',
    params
  })
}

// 获得项目信息页面状态数量
export function getProjectStatusCount(params) {
  return request({
    url: `/pjct/admin/project_info/status_count`,
    method: 'get',
    params
  })
}

// 获得项目信息分页
export function getProjectListForPage(params) {
  return request({
    url: `/pjct/admin/project_info/page`,
    method: 'get',
    params
  })
}

// 获得项目信息
export function getProjectInfo(params) {
  return request({
    url: `/pjct/project_info/get`,
    method: 'get',
    params
  })
}

// 导出项目信息 Excel
// export function exportProjectListForExcel(params) {
//   return request({
//     url: `/pjct/admin/project_info/project_export`,
//     method: 'get',
//     params
//   })
// }
export function exportProjectListForExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/pjct/admin/project_info/project_export`
}

export function exportExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/pjct/admin/project_info/enter_export`
}

// 通知记录列表
export function getProjectNoticeList(params) {
  return request({
    url: `/pjct/project_notice/notice_list`,
    method: 'get',
    params
  })
}

// 下发通知
export function createProjectNotice(data) {
  return request({
    url: `/pjct/project_notice/create`,
    method: 'post',
    data
  })
}

// 通知记录-评审记录创建修改
export function createProjectNoticeRecord(data) {
  return request({
    url: `/pjct/project_notice/create_record`,
    method: 'post',
    data
  })
}

// 项目提交评估
export function getProjectApplySubmit(data) {
  return request({
    url: `/pjct/admin/project_info/apply_submit`,
    method: 'post',
    data
  })
}

// 获取合同状态
export function getContractStats(params) {
  return request({
    url: `/pjct/admin/project_info/get_contract`,
    method: 'get',
    params
  })
}

// 办理进度
export function getProjectProgress(params) {
  return request({
    url: `/pjct/admin/project_info/progress`,
    method: 'get',
    params
  })
}

// 修改企业名称
export function udpateProjectEntpName(params) {
  return request({
    url: `/pjct/admin/project_info/udpate_name`,
    method: 'get',
    params
  })
}

// 修改企业信用代码
export function updateProjectUpdateCode(params) {
  return request({
    url: `/pjct/admin/project_info/udpate_code`,
    method: 'get',
    params
  })
}

// 修改营业执照附件副本
export function updateProjectUpdateBusiness(data) {
  return request({
    url: `/pjct/project_info/upload_business`,
    method: 'post',
    data
  })
}
// 办理合同
export function handleContractForProject(data) {
  return request({
    url: `/pjct/admin/project_info/handle_contract`,
    method: 'post',
    data
  })
}

// 获得办理事项记录列表 projectId
export function getProjectMatterListById(params) {
  return request({
    url: `/pjct/matter/getByProjectId`,
    method: 'get',
    params
  })
}

// 创建办理事项记录
export function createProjectMatter(data) {
  return request({
    url: `/pjct/matter/create`,
    method: 'post',
    data
  })
}

// 删除办理事项记录
export function deleteMatter(params) {
  return request({
    url: `/pjct/matter/delete`,
    method: 'delete',
    params
  })
}

// 修改办理事项记录
export function updateMatter(data) {
  return request({
    url: `/pjct/matter/update`,
    method: 'put',
    data
  })
}

// 获取该项目合规性信息列表
export function getComplianceList(params) {
  return request({
    url: `/wk/compliance_record/list`,
    method: 'get',
    params
  })
}

// 更新合规性检验记录
export function updateComplianceRecord(data) {
  return request({
    url: `/wk/compliance_record/update`,
    method: 'post',
    data
  })
}

// 获取该项目合规性信息是否通过  0-未检查，1-已通过，2未通过
export function getComplianceRecordStatus(params) {
  return request({
    url: `/wk/compliance_record/status`,
    method: 'get',
    params
  })
}

// 获得办理事项记录
export function getMatterInfo(params) {
  return request({
    url: `/pjct/matter/get`,
    method: 'get',
    params
  })
}

// 项目库头部统计
export function getProjectHeader(params) {
  return request({
    url: `/statistics/header/project_header`,
    method: 'get',
    params
  })
}

// 拟入园库头部统计
export function getProjectEntHeader(params) {
  return request({
    url: `/statistics/header/project_ent_header`,
    method: 'get',
    params
  })
}
// 意向入驻园区
export function getParkSelect(params) {
  return request({
    url: `/housing/park/select`,
    method: 'get',
    params
  })
}
// 创建相关数据字典
export function getAllEnumList(params) {
  return request({
    url: `/pjct/project_info/get_all_enum_list`,
    method: 'get',
    params
  })
}
// 提交审批
export function projectInfoSubmit(data) {
  return request({
    url: `/pjct/project_info/submit`,
    method: 'post',
    data
  })
}
// 招商项目详情
export function getMeRecordDetail(params) {
  return request({
    url: `/me/record/detail`,
    method: 'get',
    params
  })
}

// 产学研项目详情
export function getCXYDetail(params) {
  return request({
    url: `/tsta/achievement/record/detail`,
    method: 'get',
    params
  })
}

// 通过手机号检查
export function meRecordCheckPhone(params) {
  return request({
    url: `/contract/record/check_phone`,
    method: 'get',
    params
  })
}

// 入园
export function meRecordEnterPark(data) {
  return request({
    url: `/contract/record/enter_park`,
    method: 'post',
    data
  })
}

// 录入新项目
export function registerUser(data) {
  return request({
    url: `/pjct/admin/project_info/register_user`,
    method: 'post',
    data
  })
}

//退出入园
export function quitPark(params) {
  return request({
    url: `/pjct/admin/project_info/project_return?id=${params}`,
    method: 'get'
  })
}

//获取指定人员办理下拉数据
export function getPerson() {
  return request({
    url: `/revise/apply/park/getPerson`,
    method: 'get'
  })
}
// 社会信用代码检查
export function meRecordCheckCode(params) {
  return request({
    url: `/wk/compliance_record/check_code`,
    method: 'get',
    params
  })
}
