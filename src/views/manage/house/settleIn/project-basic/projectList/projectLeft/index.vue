<template>
  <div class="project-left">
    <div class="m-b-16">
      <ul class="font-size-14 m-t-8">
        <li
          class="pointer flex align-items-center justify-content-between"
          :class="[
            item.class + '-li',
            searchValue === item.type ? item.class : ''
          ]"
          v-for="(item, index) in typeList"
          :key="index"
          @click="handleData(item.type)"
        >
          <div>
            <svg-icon class="text-n" :icon-class="item.icon" />
            <span class="text-s m-l-4">{{
              item.name + '（' + (staticsData[item.key] || 0) + '）'
            }}</span>
          </div>
          <svg-icon class="svg-right" icon-class="chevron-right" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getProjectStatusCount } from '@/views/manage/house/settleIn/project-basic/api'

export default {
  name: 'ProjectLeft',
  inject: ['ProjectBasic'],
  data() {
    return {
      primary: false,
      warning: false,
      success: false,
      danger: false,
      primary1: false,
      warning1: false,
      danger1: false,
      searchValue: 4,
      typeList: [
        {
          key: 'pending',
          name: '待办理的入园',
          type: 4,
          class: 'primary',
          icon: 'login'
        },
        {
          key: 'processed',
          name: '已办理的入园',
          type: 5,
          class: 'success',
          icon: 'thumb-up'
        },
        {
          key: 'exited',
          name: '已退出的入园',
          type: 6,
          class: 'danger',
          icon: 'user-clear'
        }
      ],
      staticsData: {},
      loading: false
    }
  },
  mounted() {
    this.getProjectStatusCount()
  },
  methods: {
    async handleData(val) {
      this.$emit('setLoading', true)
      this.searchValue = val
      await this.$nextTick()
      this.ProjectBasic.$refs.right.tabsChange(val)
    },
    getProjectStatusCount() {
      getProjectStatusCount().then(res => {
        console.log('res---------', res)
        this.staticsData = res
      })
    },
    setLoading(val) {
      this.loading = val
    }
  }
}
</script>

<style lang="scss" scoped>
.project-left {
  width: 240px;
  border-right: 1px solid #e7e7e7;
  .text-f {
    color: rgba(0, 0, 0, 0.4);
  }
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
  .text-s {
    color: rgba(0, 0, 0, 0.6);
  }
  li {
    width: 216px;
    height: 36px;
    line-height: 36px;
    padding-left: 16px;
    padding-right: 10px;
    margin-bottom: 4px;
  }
  .primary {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #ed7b2f;
    }
    .svg-right {
      display: block !important;
    }
    color: #ed7b2f;
  }
  .warning {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #ed7b2f;
    }
    .svg-right {
      display: block !important;
    }
    color: #ed7b2f;
  }
  .success {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #00a870;
    }
    .svg-right {
      display: block !important;
    }
    color: #00a870;
  }
  .danger {
    background: rgba(243, 243, 243, 0.898);
    border-radius: 3px;
    .text-n,
    .text-s {
      color: #e34d59;
    }
    .svg-right {
      display: block !important;
    }
    color: #e34d59;
  }

  .warning-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #ed7b2f;
      }
      .svg-right {
        display: block;
      }
      color: #ed7b2f;
    }
  }
  .primary-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #ed7b2f;
      }
      .svg-right {
        display: block;
      }
      color: #ed7b2f;
    }
  }
  .success-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #00a870;
      }
      .svg-right {
        display: block;
      }
      color: #00a870;
    }
  }
  .danger-li {
    .svg-right {
      display: none;
    }
    &:hover {
      .text-n,
      .text-s {
        color: #e34d59;
      }
      .svg-right {
        display: block;
      }
      color: #e34d59;
    }
  }
}
</style>
