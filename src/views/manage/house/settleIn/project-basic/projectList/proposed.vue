<template>
  <div class="bg-white min-h100">
    <basic-card>
      <div class="project-content flex" v-loading="loading">
        <project-left ref="left" @setLoading="setLoading" />
        <project-right ref="right" @setLoading="setLoading" />
      </div>
    </basic-card>
  </div>
</template>

<script>
import ProjectLeft from '@/views/manage/house/settleIn/project-basic/projectList/projectLeft'
import ProjectRight from '@/views/manage/house/settleIn/project-basic/projectList/projectRight'
import {
  getProjectEntHeader,
  getProjectHeader
} from '@/views/manage/house/settleIn/project-basic/api'

export default {
  name: 'SettleInProposedBasic',
  components: {
    ProjectLeft,
    ProjectRight
  },
  data() {
    return {
      isProject: true,
      loading: true,
      backgroundColor: true,
      data: [],
      title: '招商概况',
      chartData: []
    }
  },
  provide() {
    return {
      ProjectBasic: this
    }
  },
  props: {
    view: {
      type: Number,
      default: 2
    }
  },
  created() {
    this.getProjectHeader()
  },
  methods: {
    setLoading(val) {
      this.loading = val
    },
    async getProjectHeader() {
      let routeTab = this.$route.fullPath // /enterpark/index // /enterpark/proposed
      if (routeTab === '/enterpark/index') {
        const res = await getProjectHeader()
        this.data = res.headerList
        this.data[2].prompt = '评估通过且审核通过的项目数占总招商项目数的比率'
        this.chartData = res.jsonList.map(item => {
          return { name: item.name, value: item.value, unit: '项' }
        })
        this.isProject = true
      } else {
        const data = await getProjectEntHeader()
        this.data = data.headerList
        this.data[2].prompt = '48小时内及时处理入园事项占比'
        this.isProject = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.project-content {
  //height: 100%;
  padding: 0 24px 24px;
}

:deep(.content) {
  height: calc(100% - 61px);
  padding: 0 !important;
}
</style>
