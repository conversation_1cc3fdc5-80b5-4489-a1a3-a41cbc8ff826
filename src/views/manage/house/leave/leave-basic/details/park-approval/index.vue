<template>
  <div>
    <basic-card title="离园信息">
      <div class="leave-wrapper">
        <div>
          <drive-table
            ref="drive-table"
            :columns="tableColumn"
            :table-data="list"
          >
          </drive-table>
        </div>
      </div>
    </basic-card>
    <!-- <div class="m-t-8">
      <basic-card title="账单信息">
        <div class="leave-wrapper">
          <el-row :gutter="20">
            <el-col :span="9">
              <div class="flex line-height-22 m-b-16">
                <div class="font-size-14 color-text-secondary label">
                  银行账号
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="12">
              <div class="flex line-height-22 m-b-16">
                <div class="font-size-14 color-text-secondary label label-b">
                  开户行
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="3">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  合同金额总计
                </div>
                <div class="font-size-14 text-black value">sadasda</div>
              </div>
            </el-col>
          </el-row>
          <div>
            <drive-table
              ref="drive-table"
              :columns="billInfoColumn"
              :table-data="approvalList"
            >
            </drive-table>
          </div>
        </div>
      </basic-card>
    </div> -->
    <!-- <div class="m-t-8">
      <basic-card title="企业信息">
        <div class="leave-wrapper leave-wrapper-custom">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  司法涉诉
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  行政处罚
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  抵押登记
                </div>
                <div class="font-size-14 text-black value">sadasda</div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  执行中的合同
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  使用中房源面积
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  缴费预期次数
                </div>
                <div class="font-size-14 text-black value">sadasda</div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="flex line-height-22">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  2021年度主营业务收入
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  2021年度企业税收
                </div>
                <div class="font-size-14 text-black value">asdadasd</div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  2021年度净利润
                </div>
                <div class="font-size-14 text-black value">sadasda</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </basic-card>
    </div> -->
  </div>
</template>

<script>
import ColumnMixins from './column'
// import { getContract } from '../api'
export default {
  name: 'ParkApproval',
  mixins: [ColumnMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // tableData: []
    }
  },
  created() {
    // this.getContract()
  },
  computed: {},
  methods: {
    // getContract() {
    //   getContract().then(res => {
    //     this.tableData = res
    //   })
    // }
  }
}
</script>

<style lang="scss" scoped>
.leave-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);

  .label {
    width: 76.01px;
    text-align-last: right;
    margin-right: 16px;
  }

  .label-custom {
    width: 143px !important;
  }

  .label-custom {
    width: 86.01px;
  }

  .value {
    flex: 1;
  }

  &.no-border {
    border: none;
  }

  &.healthy-wrapper {
    padding-top: 24px;
    padding-bottom: 0;
  }

  .member-table {
    padding: 0 6px;
  }
  .enterprise-report {
    width: 176px;
    text-align: right;
  }
}
.leave-wrapper-custom {
  border-bottom: none;
}
</style>
