<template>
  <div class="steps-container flex align-items-center justify-content-between">
    <div
      class="flex steps-wrapper"
      v-for="(item, index) in stepsData"
      :key="index"
    >
      <div class="steps-item flex">
        <div class="left flex align-items-center">
          <div
            v-if="active === index"
            class="circle active-circle font-size-16 flex align-items-center justify-content-center font-strong"
            :class="getStyle(index).iconStyle"
          >
            <svg-icon
              v-if="getStyle(index).iconStyle === 'icon-danger'"
              icon-class="close"
            />
            <span v-else class="color-white">{{ index + 1 }}</span>
          </div>
          <div
            v-else
            class="circle font-size-16 flex align-items-center justify-content-center font-strong"
            :class="getStyle(index).iconStyle"
          >
            <svg-icon v-if="active > index" icon-class="check" />
            <span v-else>{{ index + 1 }}</span>
          </div>
        </div>
        <div class="right">
          <div
            class="font-size-16 color-text-placeholder title font-strong"
            :class="getStyle(index).labelStyle"
          >
            {{ item.label }}
          </div>
          <div class="font-size-14 status" :class="getStyle(index).valueStyle">
            {{ getVale(index, item) || '-' }}
          </div>
        </div>
      </div>

      <div v-if="index < stepsData.length - 1" class="steps-line"></div>
    </div>
  </div>
</template>

<script>
import { getComplete } from './api'

export default {
  name: 'ContractBasicSteps',
  props: {
    leaveData: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.fromModel = {}
      }
    }
  },
  data() {
    return {
      stepsData: [
        {
          label: '申请表提交',
          prop: 'applyDate',
          statusText: '待提交'
        },
        {
          label: '园区审批',
          prop: 'wfCompleteTime',
          statusText: '审核中'
        },
        {
          label: '离园办理',
          prop: 'leaveHandle',
          statusText: '待执行'
        }
      ],
      dangerStatue: [5, 6],
      visible: false,
      fromModel: {}
    }
  },
  computed: {
    active() {
      const status = new Map()
      status.set(1, 0)
      status.set(2, 1)
      status.set(3, 2)
      status.set(4, 2)
      status.set(5, 2)
      status.set(6, 2)
      return status.get(this.leaveData.status)
    },
    stepsContainerWidth() {
      return this.stepsData.length * 150 + 100 + 'px'
    }
  },
  methods: {
    getStyle(index) {
      const { active, dangerStatue, leaveData } = this
      const status = leaveData.status || 0
      if (active > index) {
        return {
          iconStyle: 'icon-primary',
          labelStyle: 'label-text-primary',
          valueStyle: 'color-text-regular'
        }
      } else if (active === index) {
        if (dangerStatue.includes(status)) {
          return {
            iconStyle: 'icon-danger',
            labelStyle: 'text-danger',
            valueStyle: 'color-text-regular'
          }
        } else {
          return {
            iconStyle: 'icon-primary active-circle',
            labelStyle: 'label-text-primary',
            valueStyle: 'color-text-regular'
          }
        }
      } else if (active < index) {
        return {
          iconStyle: '',
          labelStyle: '',
          valueStyle: 'color-text-placeholder'
        }
      } else {
        return {}
      }
    },

    _genResultText() {
      const status = new Map()
      status.set(1, '待审核')
      status.set(2, '审核中')
      status.set(3, '办理中')
      status.set(4, '已完结')
      status.set(5, '已退回')
      status.set(6, '已拒绝')
      return status.get(this.leaveData.status)
    },

    getVale(index, { prop, statusText }) {
      const { active, leaveData, dangerStatue } = this
      if (active > index) {
        return leaveData[prop]
      } else if (active === index) {
        if (dangerStatue.includes(leaveData.status)) {
          return leaveData[prop]
        } else {
          return this._genResultText()
        }
      } else {
        return statusText
      }
    },

    // 打开合同签订弹框
    openDialog() {
      this.fromModel = {
        entName: this.leaveData.entName,
        contractNo: this.leaveData.contractNo
      }
      this.visible = true
    },

    // 合同签订
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { attachList, signDate } = this.fromModel
          const { contractId } = this.$route.query
          const params = {
            signDate,
            contractId
          }

          if (attachList && attachList.length > 0) {
            params.attachIds = attachList.map(item => item.id)
          }

          getComplete(params).then(() => {
            this.$toast.success('签订成功')
            this.visible = false
            this.$emit('refreshContract', contractId)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.steps-container {
  width: 100%;

  .steps-wrapper {
    flex: 1;

    .steps-item {
      .left {
        margin-right: 16px;
        height: 30px;
        line-height: 30px;

        .circle {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid;
          @include font_color(--color-text-placeholder);
          @include border_color(--color-text-placeholder);
        }

        .active-circle {
          @include font_color(--color-white);
          @include background_color(--color-primary);
          @include border_color(--color-primary);
        }

        .icon-primary {
          @include font_color(--color-primary);
          @include border_color(--color-primary);
        }

        .icon-danger {
          @include font_color(--color-danger);
          @include border_color(--color-danger);
        }
      }
      .right {
        .title {
          height: 30px;
          line-height: 30px;
          margin-bottom: 6px;
        }

        .label-text-primary {
          @include font_color(--color-text-primary);
        }

        .label-primary {
          @include font_color(--color-primary);
        }

        .text-danger {
          @include font_color(--color-danger);
        }

        .status {
          line-height: 22px;
        }
      }
    }

    .steps-line {
      flex: 1;
      height: 2px;
      @include background_color(--color-text-placeholder);
      margin: 13px 16px 0;
    }

    .line-primary {
      @include background_color(--color-primary);
    }
  }
}
</style>
