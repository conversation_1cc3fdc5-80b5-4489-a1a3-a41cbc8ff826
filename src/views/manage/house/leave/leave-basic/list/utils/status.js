// 入园状态列表
export const gardenStatus = [
  {
    label: '待审核',
    value: 1
  },
  {
    label: '审核中',
    value: 2
  },
  {
    label: '办理中',
    value: 3
  },
  {
    label: '完结',
    value: 4
  },
  {
    label: '退回',
    value: 5
  },
  {
    label: '拒绝',
    value: 6
  }
]

// 获取离园状态
export function getGardenStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag isDot type="info" label="待审核" />
    case 2:
      return <basic-tag isDot type="warning" label="审核中" />
    case 3:
      return <basic-tag isDot type="warning" label="办理中" />
    case 4:
      return <basic-tag isDot type="success" label="完结" />
    case 5:
      return <basic-tag isDot type="danger" label="退回" />
    case 6:
      return <basic-tag isDot type="danger" label="拒绝" />

    default:
      return '-'
  }
}
