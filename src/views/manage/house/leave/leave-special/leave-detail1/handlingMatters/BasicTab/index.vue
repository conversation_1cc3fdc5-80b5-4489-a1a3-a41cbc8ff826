<template>
  <div
    class="w100 tabs-wrapper flex flex-wrap align-items-center justify-content-between"
  >
    <div class="flex align-items-center tabs-content">
      <div
        v-for="(item, index) in tabsData"
        :key="index"
        class="font-size-14 color-text-regular p-l-16 p-r-16 tabs-item"
        :class="item.value === current ? 'active' : ''"
        @click="tabsChange(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
    <slot name="right"></slot>
  </div>
</template>

<script>
export default {
  name: 'BasicTab',
  props: {
    tabsData: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    tabsChange(e) {
      if (this.disabled) return
      this.$emit('tabsChange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  margin-bottom: 24px;
  .tabs-content {
    transition: all 0.3s;
    overflow-x: hidden;
    overflow-y: hidden;
    white-space: nowrap;
    &:hover {
      transition: all 0.3s;
      overflow-x: auto;
    }
  }
  .tabs-item {
    height: 40px;
    line-height: 40px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;

    &.active {
      @include font_color(--color-primary);
      &::before {
        content: '';
        width: 100%;
        height: 2px;
        @include background-color(--color-primary);
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}
</style>
