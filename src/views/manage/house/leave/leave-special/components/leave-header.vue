<template>
  <basic-card class="replace-header w100">
    <div
      class="flex align-items-center justify-content-between"
      style="height: 34px"
    >
      <div class="flex align-items-center">
        <div class="font-strong m-r-16">{{ entName }}</div>
      </div>
      <flow-form v-if="objDetail.showAudit" class="m-l-8" ref="flowForm" />
      <div v-else>
        <el-button
          type="info"
          v-if="objDetail.showRecord"
          @click="showRecord = true"
          ><svg-icon icon-class="file-copy" /><span>操作记录</span></el-button
        >
        <el-button
          type="primary"
          v-if="objDetail.showSubmit"
          @click="submitPerson"
          ><svg-icon icon-class="attach" /><span>提交办理</span></el-button
        >
      </div>
    </div>
    <project-details-steps ref="steps" @stepClickHandle="stepClickHandle" />
    <!--    操作记录弹窗-->
    <dialog-cmp
      title="操作记录"
      :visible.sync="showRecord"
      width="38%"
      :have-operation="false"
    >
      <div style="height: 480px">
        <el-scrollbar
          v-if="operationList && operationList.length"
          style="height: 100%"
        >
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in operationList"
              :key="index"
              :timestamp="`${item.user}|${item.title}`"
              :color="getHistoryColor(item.titleId)"
              size="normal"
              placement="top"
            >
              <div class="font-s font-size-12 line-height-20">
                <div class="m-b-8">
                  {{ parseTime(item.time, '{y}/{m}/{d} {h}:{i}:{s}') }}
                </div>
                <p class="font-size-14 m-b-8" style="width: 520px">
                  {{ item.content }}
                </p>
                <div v-if="false">
                  <!--              <img class="m-r-8" src="../images/empty.png">-->
                  <!--              <img class="m-r-8" src="../images/empty.png">-->
                  <!--              <img class="m-r-8" src="../images/empty.png">-->
                  <div class="demo-image__preview">
                    <el-image
                      style="width: 42px; height: 42px"
                      :src="item.url"
                      :preview-src-list="item.srcList"
                    >
                    </el-image>
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-scrollbar>
        <div v-else class="wh100" style="height: calc(100% - 51px)">
          <empty-data />
        </div>
      </div>
    </dialog-cmp>
    <!--    提交办理弹窗-->
    <dialog-cmp
      title="提交办理"
      :visible.sync="showForm"
      width="38%"
      @confirmDialog="getReviseApplySubmit"
    >
      <driven-form
        ref="driven-form"
        label-position="top"
        v-model="fromPerson"
        :formConfigure="formConfigureData"
      />
    </dialog-cmp>
  </basic-card>
</template>

<script>
import FlowForm from '@/components/FlowForm/index.vue'
import ProjectDetailsSteps from './steps'
import { parseTime } from '@/utils/tools'
import { getHistoryColor } from '@/views/manage/house/replace/replace-special/utils/status'
import descriptorMixins from '@/views/manage/house/replace/replace-special/column/descriptor'
import {
  getApplyParkTitle,
  getReviseApplyPerson
} from '@/views/manage/house/replace/replace-special/api'
import {
  getLeaveParkDone,
  getLeaveParkProcess
} from '@/views/manage/house/leave/leave-special/api'
export default {
  name: 'LeaveHeader',
  props: {
    objDetail: {
      type: Object,
      default: () => ({})
    },
    operationList: {
      type: Array,
      default: () => []
    }
  },
  mixins: [descriptorMixins],
  components: {
    FlowForm,
    ProjectDetailsSteps
  },
  data() {
    return {
      showRecord: false,
      parseTime,
      getHistoryColor,
      list: [],
      showForm: false,
      fromPerson: {},
      entName: '',
      outDate: '',
      personIds: []
    }
  },
  created() {
    this.getReviseApplyPerson()
    if (this.$route.query.id) {
      this.getLeaveParkProcess()
      this.getApplyParkTitle()
    }
  },
  methods: {
    // 点击流程状态切换
    stepClickHandle(row, index) {
      this.$emit('stepClickHandle', row, index)
    },
    // 提交办理
    submitPerson() {
      this.$confirm(
        `确认以${parseTime(
          this.objDetail.leavingDate,
          '{y}-{m}-{d}'
        )}为退房日期并提交办理事项`
      ).then(() => {
        this.showForm = true
        this.fromPerson = {}
      })
    },
    async getReviseApplySubmit() {
      this.personIds = this.fromPerson.personIds.map(item => item[1])
      this.personIds = JSON.parse(JSON.stringify(this.personIds))
      this.personIds = JSON.stringify(this.personIds)
      this.outDate = parseTime(this.objDetail.leavingDate, '{y}-{m}-{d}')
      await getLeaveParkDone({
        personIds: this.personIds,
        leaveDate: this.outDate,
        applyId: this.$route.query.id
      })
      this.$emit('getNewPage')
      this.showForm = false
      this.$toast.success('提交办理成功')
    },
    // 详情 - 获得部门人员
    async getReviseApplyPerson() {
      const res = await getReviseApplyPerson()
      this.formConfigureData.descriptors.personIds.options = res
    },
    // 详情 - 获得标题
    async getApplyParkTitle() {
      let id = this.$route.query.id
      const res = await getApplyParkTitle(id, 1)
      this.entName = res.title
    },
    // 流程
    async getLeaveParkProcess() {
      let id = this.$route.query.id
      const res = await getLeaveParkProcess(id)
      this.$nextTick(() => {
        this.$refs.steps.stepList = res
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.replace-header {
  border-radius: 3px;
  opacity: 1;
  border: 1px solid #dcdcdc;
}

::v-deep {
  .el-cascader__tags .el-tag > span {
    color: #000;
  }
  .el-cascader__tags .el-tag {
    background-color: transparent;
  }
  .el-timeline {
    padding-top: 10px;
    padding-bottom: 20px;
  }
  .el-timeline-item__node--normal {
    left: 1px;
    width: 8px;
    height: 8px;
  }
  .el-timeline-item__timestamp {
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
  }
  .el-timeline-item__timestamp.is-top {
    padding-top: 0;
  }
  .el-scrollbar__bar.is-vertical > div {
    width: 4px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
    opacity: 1;
  }
  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }
  .el-scrollbar__bar.is-vertical > div {
    width: 4px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
    opacity: 1;
  }
}
</style>
