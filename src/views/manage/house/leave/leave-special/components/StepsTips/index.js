import Vue from 'vue'
import Main from './src'
const StepsTipsConstructor = Vue.extend(Main)
let instance
const StepsTips = () => {
  instance = new StepsTipsConstructor()
  instance.$mount()
  document.body.appendChild(instance.$el)
  instance.isShow = false
  instance.dom = instance.$el
  return instance
}
Vue.use(StepsTips)
StepsTips.isShow = isShow => {
  instance.isShow = isShow
}
export default StepsTips
