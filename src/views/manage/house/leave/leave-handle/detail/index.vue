<template>
  <div class="leave-park-container min-h100 bg-white">
    <basic-card v-loading="loading">
      <div class="flex align-items-center line-height-32 m-b-16">
        <div class="font-size-18 font-strong m-r-8">
          {{ detailInfo.entName | noData }}
        </div>
        <basic-tag type="warning" :label="detailInfo.statusStr" />
      </div>
      <basic-tab
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
        :disabled="disabled"
      >
        <template slot="right">
          <flow-form />
        </template>
      </basic-tab>
      <div class="leave-park-main">
        <!--申请信息-->
        <apply-information
          ref="applyInformation"
          v-if="current === 0"
          :detail-info="detailInfo"
        />
        <!--离园办理-->
        <leave-record
          ref="leaveRecord"
          v-if="current === 1"
          :detail-info="detailInfo"
        />
      </div>
    </basic-card>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import FlowForm from '@/components/FlowForm/index.vue'
import ApplyInformation from './applyInformation'
import LeaveRecord from './leaveRecord'
import { getLeaveDetail } from '@/views/manage/house/leave/leave-handle/api'

export default {
  name: 'index',
  components: { LeaveRecord, ApplyInformation, FlowForm, BasicTab },
  data() {
    return {
      loading: false,
      list: [
        {
          label: '申请信息',
          value: 0
        },
        {
          label: '离园办理',
          value: 1
        }
      ],
      current: 0,
      detailInfo: {}
    }
  },
  provide() {
    return {
      leaveMain: this
    }
  },
  computed: {
    disabled() {
      let flag = true
      if (this.detailInfo.status === 3 || this.detailInfo.status === 6) {
        flag = false
      }
      return flag
    }
  },
  mounted() {
    this.getLeaveDetail()
  },
  methods: {
    tabsChange(e) {
      this.current = e
    },
    async getLeaveDetail() {
      this.loading = true
      let id = this.$route.query.id
      const res = await getLeaveDetail(id)
      this.detailInfo = res
      this.loading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.leave-park-container {
  .leave-park-main {
    margin-top: 16px;
  }
}
</style>
