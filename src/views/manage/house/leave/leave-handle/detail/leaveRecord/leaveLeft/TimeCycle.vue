<template>
  <div class="flex">
    <el-date-picker
      v-model="date"
      type="date"
      placeholder="选择日期"
      value-format="yyyy-MM-dd"
      format="yyyy-MM-dd"
      @change="dateChange"
      :clearable="false"
    >
    </el-date-picker>
    <el-time-picker
      class="m-l-4"
      is-range
      v-model="time"
      :default-value="['09:00', '12:00']"
      range-separator="至"
      start-placeholder="开始时间"
      end-placeholder="结束时间"
      placeholder="选择时间范围"
      format="HH:mm"
      value-format="HH:mm"
      @change="dateChange"
      popper-class="hours-wrapper"
      :clearable="false"
    >
    </el-time-picker>
  </div>
</template>

<script>
export default {
  name: 'TimeCycle',
  data() {
    return {
      date: '',
      time: ['09:00', '12:00']
    }
  },
  methods: {
    dateChange() {
      if (!this.date) return this.$emit('input', '')
      const str = `${this.date} ${this.time[0]} ~ ${this.time[1]}`
      this.$emit('input', str)
    }
  }
}
</script>

<style lang="scss">
.hours-wrapper {
  .el-time-spinner__wrapper {
    width: 100% !important;
  }
}
</style>
