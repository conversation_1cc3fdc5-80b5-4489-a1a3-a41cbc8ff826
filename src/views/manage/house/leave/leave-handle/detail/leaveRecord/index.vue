<template>
  <div class="leave-record-container flex justify-content-between">
    <div class="item-wrapper" style="width: calc(50% - 12px)">
      <leave-left :detail-info="detailInfo" />
    </div>
    <div class="item-wrapper" style="width: calc(50% - 12px)">
      <leave-right ref="leaveRight" :detail-info="detailInfo" />
    </div>
  </div>
</template>

<script>
import LeaveLeft from './leaveLeft/index'
import LeaveRight from './leaveRight/index'

export default {
  name: 'LeaveRecord',
  props: {
    detailInfo: {
      type: Object,
      default: () => ({})
    }
  },
  components: { LeaveRight, LeaveLeft },
  provide() {
    return {
      leaveRecord: this
    }
  }
}
</script>

<style lang="scss" scoped>
.leave-record-container {
  min-height: calc(100vh - 310px);
  .item-wrapper {
    border-radius: 6px;
    border: 1px solid #e7e7e7;
    padding: 24px;
  }
  :deep(.el-collapse) {
    .title-active {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .el-collapse-item__header {
      height: auto;
      line-height: initial;
      padding: 12px 0;
      position: relative;
    }
    .el-collapse-item__content {
      padding-bottom: 16px;
    }
    .el-collapse-item__arrow {
      position: absolute;
      right: 0;
      top: 16px;
      margin: 0;
    }
    .is-active {
      .title-active {
        white-space: normal;
        height: auto;
      }
    }
  }
}
</style>
