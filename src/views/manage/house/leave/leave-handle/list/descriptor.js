import { validateContact } from '@/utils/validate'
import { reviseApplyCheckRevise } from '../api'

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          entId: {
            form: 'select',
            label: '离园企业',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入或下拉选择离园的企业'
              },
              {
                validator: async (rule, value, callback) => {
                  const res = await reviseApplyCheckRevise({ entId: value })
                  if (res)
                    return callback(
                      new Error(
                        '当前企业有正在办理中的调房或离园业务，请选择其他企业'
                      )
                    )
                  callback()
                },
                trigger: ['blur']
              }
            ],
            props: {
              filterable: true
            },
            events: {
              change: e => this.leaveParkChange(e)
            }
          },
          leavingDate: {
            form: 'date',
            label: '预计离园时间',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择预计离园时间'
              }
            ]
            // props: {
            //   pickerOptions: {
            //     disabledDate: time => {
            //       return time.getTime() < Date.now() - 8.64e7
            //     }
            //   }
            // }
          },
          leavingReasonType: {
            form: 'select',
            label: '离园原因和意向去向',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择离园原因和意向去向'
              }
            ],
            options: [],
            attrs: {
              filterable: true
            }
          },
          leavingReason: {
            form: 'input',
            label: '',
            rule: [
              {
                type: 'string',
                message: '请输入离园原因和意向去向'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 4,
              maxlength: 300,
              showWordLimit: true
            }
          },
          entBasic: {
            form: 'input',
            label: '企业基本情况',
            rule: [
              {
                required: false,
                type: 'string',
                message:
                  '包括但不限于：从业人数、营业收入、利润、税收、发展前景等内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 4,
              maxlength: 300,
              showWordLimit: true
            }
          },
          advice: {
            form: 'input',
            label: '意见和建议',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入意见和建议'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 4,
              maxlength: 300,
              showWordLimit: true
            }
          },
          contact: {
            form: 'input',
            label: '主要联系人',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主要联系人'
              }
            ],
            attrs: {
              maxlength: 15
            },
            customTips: () => {
              return (
                <div>默认引用了该企业常用联系人信息，具体以输入信息为准</div>
              )
            }
          },
          phone: {
            form: 'input',
            label: ' ',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                validator: validateContact
              }
            ]
          },
          audited: {
            form: 'switch',
            label: '需要审核',
            rule: [
              {
                required: false,
                type: 'boolean',
                message: '请选择需要审核'
              }
            ],
            customRight: () => {
              return <div class={'audit-tips'}>是</div>
            },
            customTips: () => {
              return (
                <div>
                  根据企业此次实际离园情况来确定是否需要审核。如果选择否则直接进入办理阶段
                </div>
              )
            }
          }
        }
      }
    }
  }
}
