<template>
  <div class="min-h100 bg-white">
    <basic-card class="min-h100">
      <div class="flex justify-content-between">
        <left-tabs
          :tabs-data="list"
          :current="current"
          @tabsChange="tabsChange"
          :disabled="reqLoading"
        />
        <div class="right-table flex-1">
          <div
            class="header-main flex align-items-center justify-content-between"
          >
            <div class="font-strong">离园记录</div>
            <div class="flex justify-content-end align-items-center">
              <div>
                <el-switch
                  v-model="extralQuerys.lookMe"
                  @change="changeHandle"
                  active-color="#ed7b2f"
                  inactive-color="#dcdfe6"
                  :disabled="reqLoading"
                >
                </el-switch>
                <span class="m-l-15 m-r-25 font-size-15">只看我录入的</span>
              </div>
              <el-button type="primary" @click="openHandle">登记离园</el-button>
            </div>
          </div>
          <drive-table
            ref="drive-table"
            :api-fn="getLeavePage"
            :columns="tableColumn"
            :extral-querys="extralQuerys"
            @getTotal="reqLoading = false"
            :search-querys-hook="searchQueryHook"
          >
          </drive-table>
        </div>
      </div>
    </basic-card>
    <!--登记离园-->
    <basic-drawer
      title="登记离园"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        v-if="drawerVisible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
        label-position="top"
      />
    </basic-drawer>
  </div>
</template>

<script>
import LeftTabs from '@/views/manage/house/leave/leave-handle/list/components/leftTabs'
import ColumnMixins from './column'
import descriptorMixins from './descriptor'
import {
  getLeavePage,
  getLeaveSubmit,
  getParkEntList,
  getStatusList
} from '@/views/manage/house/leave/leave-handle/api'
import { getByTenantDictType, getContacts } from '@/api/common'
export default {
  name: 'LeaveHandleList',
  mixins: [ColumnMixins, descriptorMixins],
  components: { LeftTabs },
  data() {
    return {
      getLeavePage,
      drawerVisible: false,
      list: [],
      current: 0,
      extralQuerys: {
        status: 0,
        lookMe: false
      },
      fromModel: {
        audited: false
      },
      parkList: [],
      reqLoading: true,
      restaurants: []
    }
  },
  deactivated() {
    this.reqLoading = true
  },
  activated() {
    this.getStatusList()
    this.getParkEntList()
    this.getByTenantDictType()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    searchQueryHook(e) {
      let [createTimeStart = '', createTimEnd = ''] = e.createTime || []
      delete e.createTime
      return {
        ...e,
        createTimeStart,
        createTimEnd
      }
    },
    getByTenantDictType() {
      getByTenantDictType('leaving_reason_type').then(res => {
        this.formConfigure.descriptors.leavingReasonType.options = res || []
      })
    },
    selectContact(val) {
      this.$set(this.fromModel, 'contact', val.value )
      this.$set(this.fromModel, 'phone', val.phone )
    },
    getContacts(row) {
      getContacts(row).then(res => {
        this.restaurants = res.map(item => {
          return {
            address: item.contact,
            value: item.contact,
            phone: item.phone
          }
        })
      })
    },
    fetchContacts(queryString, cb) {
      let restaurants = this.restaurants;
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        const contact = restaurant.value || '';
        return contact.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
      };
    },
    // 离园企业change事件
    async leaveParkChange(e) {
      if (!e) return false
      this.getContacts(e)
      this.parkList.forEach(item => {
        if (item.value === e) {
          this.$set(this.fromModel, 'contact', item.contact)
          this.$set(this.fromModel, 'phone', item.phone)
        }
      })
    },
    // tab切换
    tabsChange(e) {
      if (this.current === e) return false
      this.reqLoading = true
      this.current = e
      this.extralQuerys.status = e
      this.$refs['drive-table'].refreshTable()
    },
    // 只看我录入的
    changeHandle() {
      this.reqLoading = true
      this.$refs['drive-table'].refreshTable()
    },
    // 登记离园 - 打开表单抽屉
    openHandle() {
      this.fromModel = this.$options.data().fromModel
      this.drawerVisible = true
    },
    // 登记离园 - 提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let data = {
            ...this.fromModel
          }
          await getLeaveSubmit(data)
          this.$toast.success('登记成功')
          this.$refs['drive-table'].refreshTable()
          this.getStatusList()
          this.drawerVisible = false
        }
      })
    },
    // 获取离园企业
    async getParkEntList() {
      const res = await getParkEntList()
      this.parkList = this.formConfigure.descriptors.entId.options = res.map(
        item => {
          return {
            label: item.name,
            value: item.id,
            contact: item.contact || '',
            phone: item.phone || ''
          }
        }
      )
    },
    // 筛选状态
    async getStatusList() {
      const res = await getStatusList()
      this.list = res.map(item => {
        return {
          label: `${item.statusValue}（${item.count}）`,
          value: item.status,
          class: this.getClassStatus(item.status)
        }
      })
      this.extralQuerys.status = this.current = this.list[0].value
    },
    getClassStatus(val) {
      switch (val) {
        case 0:
          return 'all'
        case 2:
          return 'primary'
        case 3:
          return 'success'
        case 4:
          return 'danger'
        default:
          return 'warning'
      }
    },
    // 离园详情
    detailHandle(row) {
      this.$router.push({
        path: '/rentOut/leavePark/leaveParkDetail',
        query: {
          id: row.id,
          orderId: row.processId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.right-table {
  width: calc(100% - 240px);
  padding-left: 24px;
  .header-main {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #dcdcdc;
  }
}
.flex-1 {
  flex: 1;
}

:deep(.phoneKey) {
  .el-form-item__label {
    visibility: hidden;
  }
}

:deep(.auditedKey) {
  position: relative;
  .custom-right {
    position: absolute;
    left: 56px;
    top: 50px;
  }
}
</style>
