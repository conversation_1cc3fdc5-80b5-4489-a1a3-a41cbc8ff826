export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '企业名称',
          minWidth: 180,
          showOverflowTooltip: true,
          search: {
            type: 'input'
          }
        },
        {
          prop: 'parkName',
          label: '所属园区',
          minWidth: 140,
          showOverflowTooltip: true
        },
        {
          prop: 'contact',
          label: '主要联系人',
          minWidth: 100,
          showOverflowTooltip: true
        },
        {
          prop: 'phone',
          label: '联系方式',
          minWidth: 120
        },
        {
          prop: 'leavingDate',
          label: '预计离园日期',
          minWidth: 120
        },
        {
          prop: 'actualLeavingDateStr',
          label: '实际离园日期',
          minWidth: 120
        },
        {
          prop: 'createTime',
          label: '申请时间',
          minWidth: 120,
          search: {
            type: 'daterange'
          }
        },
        {
          prop: 'statusStr',
          label: '状态',
          minWidth: 120,
          render: (h, scope) => {
            const obj = {
              2: 'info',
              3: 'primary',
              6: 'success'
            }
            return (
              <basic-tag
                type={obj[scope.row.status]}
                label={scope.row.statusStr}
              />
            )
          }
        },
        {
          prop: 'creator',
          label: '经办人',
          minWidth: 150,
          showOverflowTooltip: true
        },
        {
          prop: 'operation',
          label: '操作',
          fixed: 'right',
          width: 80,
          render: (h, { row }) => {
            return (
              <el-button
                type={'text'}
                onClick={() => {
                  this.detailHandle(row)
                }}
              >
                查看
              </el-button>
            )
          }
        }
      ]
    }
  }
}
