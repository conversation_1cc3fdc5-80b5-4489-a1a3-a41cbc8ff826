import request from '@/utils/request'

// 园区端 状态列表
export function getStatusList(params) {
  return request({
    url: `/leaving/park/status_list`,
    method: 'get',
    params
  })
}

// 获得离园申请分页
export function getLeavePage(data) {
  return request({
    url: `/leaving/park/page`,
    method: 'POST',
    data
  })
}

// 获取离园企业
export function getParkEntList(params) {
  return request({
    url: `/revise/apply/park/ent_list`,
    method: 'get',
    params
  })
}

// 提交离园申请
export function getLeaveSubmit(data) {
  return request({
    url: `/leaving/park/submit`,
    method: 'POST',
    data
  })
}

// 监测企业是否有进行中的调房或离园业务
export function reviseApplyCheckRevise(params) {
  return request({
    url: `/revise/apply/admin/event/check_revise`,
    method: 'get',
    params
  })
}

// 获得离园申请详情
export function getLeaveDetail(id) {
  return request({
    url: `/leaving/park/detail?id=${id}`,
    method: 'get'
  })
}

// 获得事项办理
export function getTransferMatters(id) {
  return request({
    url: `/leaving/park/transfer_matters/${id}`,
    method: 'get'
  })
}

// 账单详情
export function getLeaveBillDetail(applyId) {
  return request({
    url: `/leaving/park/bill_detail?applyId=${applyId}`,
    method: 'get'
  })
}

// 企业扫描接口
export function entScanning(data) {
  return request({
    url: `/revise/apply/park/enterprise_scanning`,
    method: 'post',
    data
  })
}

// 自定义款项列表
export function accountList(params) {
  return request({
    url: `/revise/account/list`,
    method: 'get',
    params
  })
}

// 自定义款项更新
export function accountUpdate(data) {
  return request({
    url: `/revise/account/update`,
    method: 'post',
    data
  })
}

// 自定义款项添加
export function accountCreate(data) {
  return request({
    url: `/revise/account/create`,
    method: 'post',
    data
  })
}

// 自定义款项列表
export function accountDetail(params) {
  return request({
    url: `/revise/account/get?id=${params}`,
    method: 'get'
  })
}

// 自定义款项删除
export function accountDel(id) {
  return request({
    url: `/revise/account/delete?id=${id}`,
    method: 'delete'
  })
}

// 撤回接口
export function revocation(data) {
  return request({
    url: `/hatch/matter/record/cancel_matter?recordId=${data}`,
    method: 'get'
  })
}

// 创建下发事项
export function getRecordMatter(data) {
  return request({
    url: `/hatch/matter/record/create`,
    method: 'post',
    data
  })
}

// 获取下发事项
export function getCommonMatter(params) {
  return request({
    url: `/hatch/matter/record/get_common_matter`,
    method: 'get',
    params
  })
}

// 添加的办理记录
export function createProjectMatterHandle(data) {
  return request({
    url: '/pjct/matter/handle',
    method: 'put',
    data
  })
}

// 创建添加的办理事项
export function createProjectMatter(data) {
  return request({
    url: '/pjct/matter/create',
    method: 'post',
    data
  })
}

// 获取添加的办理事项列表
export function getProjectMatter(params) {
  return request({
    url: '/pjct/matter/getByProjectId',
    method: 'get',
    params
  })
}

// 通知企业
export function noticeEnterprise(params) {
  return request({
    url: `/leaving/park/notice_enterprise`,
    method: 'get',
    params
  })
}

// 上传退房确认单
export function uploadConfirm(data) {
  return request({
    url: `/leaving/park/upload_confirm`,
    method: 'post',
    data
  })
}

// 关系解除
export function getDissolution(applyId, titleId) {
  return request({
    url: `/leaving/park/dissolution?applyId=${applyId}&titleId=${titleId}`,
    method: 'get'
  })
}
// 获取申请信息履约总计
export function getTotalFeeInfo(params) {
  return request({
    url: `/leaving/park/total_fee_info`,
    method: 'get',
    params
  })
}
// 获取申请信息履约详情
export function getTotalFeeDetail(params) {
  return request({
    url: `/leaving/park/total_fee_detail`,
    method: 'get',
    params
  })
}
// 修改账单获取子账单列表
export function getTotalEditBillDetail(params) {
  return request({
    url: `/leaving/park/edit_bill_detail`,
    method: 'get',
    params
  })
}
// 修改账单
export function leavingParkEditBill(data) {
  return request({
    url: `/leaving/park/edit_bill`,
    method: 'post',
    data
  })
}
// 导出结算单
export function reviseApplyBillDetailExport() {
  return `${process.env.VUE_APP_URL_PREFIX}/leaving/park/bill_detail_export`
}
// 清空修改账单
export function leavingParkDeleteEditBill(data) {
  return request({
    url: `/leaving/park/delete_edit_bill`,
    method: 'post',
    data
  })
}
export function reviseApplyGetRoomStatus(params) {
  return request({
    url: `/revise/apply/admin/event/get_room_status`,
    method: 'get',
    params
  })
}
export function reviseApplyModifyRoomStatus(data) {
  return request({
    url: `/revise/apply/admin/event/modify_room_status`,
    method: 'post',
    data
  })
}
