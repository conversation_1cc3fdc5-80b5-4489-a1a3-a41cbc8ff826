<template>
  <div>
    <basic-drawer
      :title="title"
      :visible.sync="visible"
      @confirmDrawer="confirmDrawer"
      :haveFooter="!disabled"
    >
      <div class="font-size-14 m-b-10">房间类型</div>
      <basic-tab
        ref="basicTab"
        :disabled="disabled"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
      />
      <driven-form
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :disabled="disabled"
        :formConfigure="formConfigureRoom"
      />
      <div
        v-if="showDesc"
        class="font-size-14 p-b-16 m-b-12"
        style="border-bottom: 1px solid #e7e7e7"
      >
        房间描述
      </div>
      <driven-form
        ref="driven-form-desc"
        v-if="showDesc"
        label-position="top"
        v-model="fromData"
        :disabled="disabled"
        :formConfigure="formConfigureDesc"
      />
      <div
        class="font-size-14 p-b-16 m-b-12 flex align-items-center justify-content-between"
        style="border-bottom: 1px solid #e7e7e7"
      >
        <span>产证信息</span>
        <span
          class="pointer"
          @click="propertyShow = !propertyShow"
          :class="{ 'is-up': propertyShow }"
        >
          <svg-icon icon-class="chevron-down" />
        </span>
      </div>
      <driven-form
        v-show="propertyShow"
        ref="driven-form-property"
        label-position="top"
        v-model="fromProperty"
        :disabled="disabled"
        :formConfigure="formConfigureProperty"
      />
    </basic-drawer>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import descriptorMixins from './descriptor'
import {
  createRoom,
  getCategorySelect,
  getFitmentSelect,
  getFloorbBuilding,
  getFloorbSelect,
  getNewApartmentTypes,
  getNewRoomTypes,
  getNewRoomUseTypes,
  getNewVenueTypes,
  getOwnershipSelect,
  getParkSelect,
  getPriceSelect,
  getRoomDetails,
  getRoomManageSelect,
  updateRoom
} from '../../api'

export default {
  name: 'SubmitDrawer',
  mixins: [descriptorMixins],
  components: {
    BasicTab
  },
  data() {
    return {
      list: [
        {
          label: '产业配套',
          value: 0
        },
        {
          label: '商业配套',
          value: 1
        },
        {
          label: '产业住宅',
          value: 2
        },
        {
          label: '公共服务',
          value: 3
        },
        {
          label: '管理自用',
          value: 4
        },
        {
          label: '虚拟空间',
          value: 5
        }
      ],
      current: 1,
      title: '新增房间',
      visible: false,
      disabled: false,
      fromModel: {},
      fromData: {
        hasSurveying: [0],
        parkId: '',
        buildingId: '',
        floorId: '',
        priceId: -1,
        userIds: [-1]
      },
      fromProperty: {},
      belongsModel: {}, // 房间号校验
      showDesc: true,
      extralQuerys: {},
      id: '',
      propertyShow: false
    }
  },
  mounted() {
    // this.getParkSelect()
    this.getNewRoomTypes()
    this.getNewRoomUseTypes(1)
    this.getPriceSelect()
    this.getRoomManageSelect()
    this.getCategorySelect()
    this.getFitmentSelect()
    this.getOwnershipSelect()
  },
  watch: {
    disabled(val) {
      this.formConfigureDesc.descriptors.realAttachIds.props.onlyForView = val
      this.formConfigureDesc.descriptors.planAttachIds.props.onlyForView = val
      this.formConfigureDesc.descriptors.typeAttachIds.props.onlyForView = val
    },
    visible(val) {
      if (val) {
        this.$set(this.fromData, 'parkId', this.extralQuerys.parkId)
        // this.changePark(this.extralQuerys.parkId)
        // this.changeBuilding(this.extralQuerys.buildingId)
        this.$set(this.fromData, 'buildingId', this.extralQuerys.buildingId)
        this.$set(this.fromData, 'floorId', this.extralQuerys.floorId)
        this.$set(this.belongsModel, 'parkId', this.extralQuerys.parkId)
        this.$set(this.belongsModel, 'buildingId', this.extralQuerys.buildingId)
        this.$set(this.belongsModel, 'floorId', this.extralQuerys.floorId)
      } else {
        this.fromProperty = this.$options.data().fromProperty
        this.propertyShow = false
        this.formConfigureDesc.descriptors.realAttachIds.customTips =
          this.formConfigureDesc.descriptors.planAttachIds.customTips =
          this.formConfigureDesc.descriptors.planAttachIds.typeAttachIds =
            () => {
              return (
                <div class="font-size-12 line-height-20">
                  请上传格式为png/jpg/jpeg的图片
                </div>
              )
            }
      }
    }
    //   'fromModel.useType': {
    //     deep: true,
    //     handler(val) {
    //       if (val || val === 0) {
    //         this.showDesc = true
    //       } else {
    //         this.showDesc = false
    //       }
    //       if (val === 3 || val === 7) {
    //         this.formConfigureDesc.descriptors.businessType.hidden = false
    //       } else {
    //         this.formConfigureDesc.descriptors.businessType.hidden = true
    //       }
    //     }
    //   }
  },
  methods: {
    getOwnershipSelect() {
      getOwnershipSelect().then(res => {
        this.formConfigureProperty.descriptors.ownershipId.options = res || []
      })
    },
    getFitmentSelect() {
      getFitmentSelect().then(res => {
        this.formConfigureDesc.descriptors.fitment.options = res || []
      })
    },
    getCategorySelect() {
      getCategorySelect().then(res => {
        this.formConfigureDesc.descriptors.category.options = res || []
      })
    },
    getRoomManageSelect() {
      getRoomManageSelect({ showDefault: true }).then(res => {
        this.formConfigureDesc.descriptors.userIds.options = res || []
      })
    },
    // 执行单价枚举
    async getPriceSelect() {
      const res = await getPriceSelect({ showDefault: true })
      this.formConfigureDesc.descriptors.priceId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 获取房间类型选择
    async getNewRoomTypes() {
      const res = await getNewRoomTypes()
      this.list = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 获取园区下拉数据
    async getParkSelect() {
      const res = await getParkSelect()
      this.formConfigureDesc.descriptors.parkId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 园区下拉框change事件
    async changePark(e) {
      this.fromData.buildingId = ''
      this.fromData.floorId = ''
      // 楼栋下拉数据
      const res = await getFloorbBuilding(e)
      this.formConfigureDesc.descriptors.buildingId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 楼栋下拉框change事件
    async changeBuilding(e) {
      this.fromData.floorId = ''
      // 楼层下拉数据
      const res = await getFloorbSelect(e)
      this.formConfigureDesc.descriptors.floorId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.validFloor(this.fromData)
    },
    changeFloor() {
      this.validFloor(this.fromData)
    },
    // 房间用途change事件
    async changeRoomUse(e) {
      this.$set(this.fromData, 'businessType', '')
      this.formConfigureDesc.descriptors.businessType.options = []
      if (e === 3) {
        const res = await getNewApartmentTypes()
        this.formConfigureDesc.descriptors.businessType.options = res.map(
          item => {
            return {
              label: item.label,
              value: item.key
            }
          }
        )
      }
      if (e === 7) {
        const res = await getNewVenueTypes()
        this.formConfigureDesc.descriptors.businessType.options = res.map(
          item => {
            return {
              label: item.label,
              value: item.key
            }
          }
        )
      }
    },
    // 验证房间
    validFloor(e) {
      this.$set(this.belongsModel, 'buildingId', e.buildingId)
      this.$set(this.belongsModel, 'floorId', e.floorId)
      this.$set(this.fromModel, 'buildingId', e.buildingId)
      this.$set(this.fromModel, 'floorId', e.floorId)
      this.$refs['driven-form'].validateField('room')
    },
    // tab切换
    async tabsChange(e) {
      this.getNewRoomUseTypes(e)
      this.current = e
      this.fromModel = {}
      this.$set(this.fromData, 'businessType', '')
    },
    // 获取房间用途
    async getNewRoomUseTypes(e) {
      const res = await getNewRoomUseTypes(e)
      this.formConfigureRoom.descriptors.useType.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 表单提交
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          if (this.showDesc) {
            this.$refs['driven-form-desc'].validate(async v => {
              if (v) {
                let roomTypes = this.current
                let hasSurveying = this.fromData.hasSurveying.length > 0 ? 1 : 0
                const { userIds = [] } = this.fromData
                let ids = []
                userIds.forEach(item => {
                  const len = item.length
                  if (len) {
                    ids.push(item[len - 1])
                  }
                })
                let params = {
                  roomTypes,
                  ...this.fromModel,
                  ...this.fromData,
                  ...this.fromProperty,
                  hasSurveying,
                  id: this.id,
                  realAttachIds: this.fromData.realAttachIds
                    ? this.fromData.realAttachIds.map(item => item.id)
                    : [],
                  planAttachIds: this.fromData.planAttachIds
                    ? this.fromData.planAttachIds.map(item => item.id)
                    : [],
                  typeAttachIds: this.fromData.typeAttachIds
                    ? this.fromData.typeAttachIds.map(item => item.id)
                    : [],
                  userIds: ids
                }
                if (!this.id) {
                  await createRoom(params)
                  this.$emit('operationSuccess', '新增')
                } else {
                  await updateRoom(params)
                  this.$emit('operationSuccess', '编辑')
                }
                this.visible = false
              }
            })
          }
        }
      })
    },
    // 编辑回显
    async editRoom(row) {
      this.title = '编辑房间'
      const { id } = row
      const res = await getRoomDetails(id)
      let {
        roomTypes,
        useType,
        room,
        parkId,
        buildingId,
        floorId,
        area,
        priceId,
        userIds,
        businessType,
        hasSurveying,
        category,
        fitment,
        buildingArea,
        pooledArea,
        ownershipId,
        certificate,
        processingDate,
        realAttachMap = {},
        planAttachMap = {},
        typeAttachMap = {}
      } = res
      const realAttachIds = realAttachMap.roomAttach || []
      const planAttachIds = planAttachMap.roomAttach || []
      const typeAttachIds = typeAttachMap.roomAttach || []
      this.changeRoomUse(useType)
      this.getNewRoomUseTypes(roomTypes)
      // this.changePark(parkId)
      // this.changeBuilding(buildingId)
      this.current = roomTypes
      this.id = res.id
      this.fromModel = { useType }
      this.fromData = {
        room,
        parkId,
        buildingId,
        floorId,
        area,
        priceId,
        userIds,
        businessType,
        hasSurveying: hasSurveying === 0 ? [] : [0],
        category,
        fitment,
        realAttachIds,
        planAttachIds,
        typeAttachIds
      }
      this.fromProperty = {
        buildingArea,
        pooledArea,
        ownershipId,
        certificate,
        processingDate
      }
      this.$set(this.belongsModel, 'parkId', res.parkId)
      this.$set(this.belongsModel, 'buildingId', res.buildingId)
      this.$set(this.belongsModel, 'floorId', res.floorId)
      this.disabled = false
      this.visible = true
    },
    // 查看
    async previewEvent(row) {
      this.title = '查看房间'
      const { id } = row
      const res = await getRoomDetails(id)
      let {
        roomTypes,
        useType,
        room,
        parkId,
        buildingId,
        floorId,
        area,
        priceId,
        userIds,
        businessType,
        hasSurveying,
        category,
        fitment,
        buildingArea,
        pooledArea,
        ownershipId,
        certificate,
        processingDate,
        realAttachMap = {},
        planAttachMap = {},
        typeAttachMap = {}
      } = res
      const realAttachIds = realAttachMap.roomAttach || []
      const planAttachIds = planAttachMap.roomAttach || []
      const typeAttachIds = typeAttachMap.roomAttach || []
      this.changeRoomUse(useType)
      this.getNewRoomUseTypes(roomTypes)
      // this.changePark(parkId)
      // this.changeBuilding(buildingId)
      this.current = roomTypes
      this.fromModel = { useType, id: res.id }
      this.fromData = {
        room,
        parkId,
        buildingId,
        floorId,
        area,
        priceId,
        userIds,
        businessType,
        hasSurveying: hasSurveying === 0 ? [] : [0],
        category,
        fitment,
        realAttachIds,
        planAttachIds,
        typeAttachIds
      }
      this.fromProperty = {
        buildingArea,
        pooledArea,
        ownershipId,
        certificate,
        processingDate
      }
      this.$set(this.belongsModel, 'parkId', res.parkId)
      this.$set(this.belongsModel, 'buildingId', res.buildingId)
      this.$set(this.belongsModel, 'floorId', res.floorId)

      if (realAttachIds && realAttachIds.length) {
        this.formConfigureDesc.descriptors.realAttachIds.customTips = null
      } else {
        this.formConfigureDesc.descriptors.realAttachIds.customTips = () => {
          return (
            <div class="font-size-12 line-height-20 empty-img">暂无图片</div>
          )
        }
      }
      if (planAttachIds && planAttachIds.length) {
        this.formConfigureDesc.descriptors.planAttachIds.customTips = null
      } else {
        this.formConfigureDesc.descriptors.planAttachIds.customTips = () => {
          return (
            <div class="font-size-12 line-height-20 empty-img">暂无图片</div>
          )
        }
      }
      if (typeAttachIds && typeAttachIds.length) {
        this.formConfigureDesc.descriptors.typeAttachIds.customTips = null
      } else {
        this.formConfigureDesc.descriptors.typeAttachIds.customTips = () => {
          return (
            <div class="font-size-12 line-height-20 empty-img">暂无图片</div>
          )
        }
      }
      this.disabled = true
      this.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.empty-img) {
  margin-top: -20px;
}
:deep(.buildingIdKey) {
  padding-top: 42px;
}

:deep(.floorIdKey) {
  padding-top: 42px;
}

:deep(.hasSurveyingKey) {
  padding-top: 28px;
}
.pointer {
  transition: all 300ms ease-in-out;
}
.is-up {
  transform: rotate(180deg);
}
</style>
