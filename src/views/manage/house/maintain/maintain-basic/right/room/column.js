import OperationContainer from '../components/operationContainer'

export default {
  components: {
    OperationContainer
  },
  data() {
    return {
      tableColumn: [
        {
          prop: 'room',
          label: '房间号'
        },
        {
          prop: 'area',
          label: '可用面积(m²)'
        },
        {
          prop: 'useTypeStr',
          label: '房间用途'
        },
        {
          prop: 'statusStr',
          label: '状态'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <OperationContainer
                item={scope}
                listLength={this.listLength}
                isSortable={this.isSortable}
                groupRadio={this.groupRadio}
                groupRoom={this.groupRadio !== 1}
                onPreviewEvent={() => {
                  this.previewEvent(scope.row)
                }}
                onEditHandle={() => {
                  this.editRoom(scope.row)
                }}
                onDeleteHandle={() => {
                  this.deleteRoom(scope.row)
                }}
                onMoveDown={() => {
                  this.moveDown(scope.$index, scope.row)
                }}
                onMoveUp={() => {
                  this.moveUp(scope.$index, scope.row)
                }}
                onMoveTop={() => {
                  this.moveTop(scope.$index, scope.row)
                }}
                onMoveBottom={() => {
                  this.moveBottom(scope.$index, scope.row)
                }}
              />
            )
          }
        }
      ]
    }
  }
}
