<template>
  <div>
    <el-form ref="formRef" :model="fromModel" class="flex" :rules="formRules">
      <el-form-item prop="buildingId">
        <el-select
          :disabled="disabled"
          v-model="fromModel.buildingId"
          placeholder="请选择所属楼栋"
          @change="changeHandle"
        >
          <el-option
            v-for="item in buildingList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'formRight',
  data() {
    return {
      fromModel: {},
      formRules: {
        buildingId: [
          {
            required: true,
            message: '请选择所属楼栋',
            trigger: ['blur', 'change']
          }
        ]
      },
      disabled: false,
      buildingList: []
    }
  },
  methods: {
    changeHandle(e) {
      this.$emit('change', e)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__content {
    margin-left: 0 !important;
  }
}
</style>
