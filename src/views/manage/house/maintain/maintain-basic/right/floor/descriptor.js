import Vue from 'vue' // vue框架

import FormRight from './formRight'
import { getFloorRepeat } from '@/views/manage/house/maintain/maintain-basic/api'
Vue.component('FormRight', FormRight)
export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          floor: {
            form: 'input',
            label: '楼层顺序',
            attrs: {
              type: 'number'
            },
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入楼层顺序'
              },
              {
                validator: async (rule, value, callback) => {
                  const val = this.fromModel.floor
                  if (val === 0) return callback(new Error('楼层顺序不能为0'))
                  if (!val) return callback(new Error('请输入楼层顺序'))
                  const str = val.toString()
                  if (str.includes('.'))
                    return callback(new Error('请输入整数'))
                  if (
                    !this.belongsModel.parkId ||
                    !this.belongsModel.buildingId
                  )
                    return callback()
                  const repeat = await getFloorRepeat({
                    name: val,
                    parkId: this.belongsModel.parkId,
                    buildingId: this.belongsModel.buildingId,
                    id: this.fromModel.id
                  })
                  if (repeat) return callback(new Error('楼层顺序已存在'))
                  return callback()
                }
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14 p-t-42">层</div>
            }
          },
          // parkId: {
          //   form: 'select',
          //   label: '所属楼栋',
          //   rule: [
          //     {
          //       required: true,
          //       type: 'number',
          //       message: '请选择所属园区'
          //     }
          //   ],
          //   options: [],
          //   events: {
          //     change: this.parkHandle
          //   },
          //   customRight: () => {
          //     return (
          //       <div>
          //         <FormRight
          //           ref="rightForm"
          //           onChange={e => {
          //             this.validFloor(e)
          //           }}
          //         />
          //       </div>
          //     )
          //   }
          // },
          remark: {
            form: 'input',
            label: '备注',
            hidden: true,
            rule: [
              {
                type: 'string',
                message: '请输入备注'
              }
            ]
          },
          pics: {
            form: 'component',
            label: '平面图',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传平面图'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'floor'
              },
              accept: 'image/*',
              onlyForView: this.disabled
            }
          },
          priceId: {
            form: 'select',
            label: '计费规则',
            options: [],
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择计费规则'
              }
            ],
            attrs: {
              filterable: true
            }
          },
          userIds: {
            form: 'cascader',
            label: '负责人',
            options: [],
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择负责人'
              }
            ],
            attrs: {
              filterable: true,
              props: {
                multiple: true,
                label: 'label',
                value: 'key',
                children: 'children'
              }
            }
          }
        }
      },
      formConfigureProperty: {
        descriptors: {
          buildingArea: {
            form: 'input',
            label: '套内建筑面积',
            span: 12,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请输入套内建筑面积'
              },
              {
                validator: (rule, value, callback) => {
                  if (isNaN(value)) return callback(new Error(`请输入有效数字`))
                  if (!value) return callback()
                  if (
                    /(^[0-9]([0-9]+)?(\.[0-9]{1,3})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
                      +value
                    )
                  ) {
                    const maxNum = 10000000
                    if (value >= maxNum) {
                      return callback(new Error(`面积最大支持7位数`))
                    }
                    callback()
                  } else {
                    callback(new Error('最多保留三位小数'))
                  }
                }
              }
            ],
            customRight: () => {
              return <div style={'margin-top: 50px;'}>m²</div>
            }
          },
          pooledArea: {
            form: 'input',
            label: '分摊建筑面积',
            span: 12,
            rule: [
              {
                required: false,
                type: 'number',
                message: '请输入分摊建筑面积'
              },
              {
                validator: (rule, value, callback) => {
                  if (isNaN(value)) return callback(new Error(`请输入有效数字`))
                  if (!value) return callback()
                  if (
                    /(^[0-9]([0-9]+)?(\.[0-9]{1,3})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
                      +value
                    )
                  ) {
                    const maxNum = 10000000
                    if (value >= maxNum) {
                      return callback(new Error(`面积最大支持7位数`))
                    }
                    callback()
                  } else {
                    callback(new Error('最多保留三位小数'))
                  }
                }
              }
            ],
            customRight: () => {
              return <div style={'margin-top: 50px;'}>m²</div>
            }
          },
          ownershipId: {
            form: 'select',
            label: '所有权属',
            options: [],
            rule: [
              {
                required: false,
                type: 'number',
                message: '请选择所有权属'
              }
            ]
          },
          certificate: {
            form: 'input',
            label: '产证号',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入产证号'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          processingDate: {
            form: 'date',
            label: '产证办理日期',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请选择产证办理日期'
              }
            ]
          }
        }
      }
    }
  }
}
