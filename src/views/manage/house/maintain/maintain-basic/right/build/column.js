import OperationContainer from '../components/operationContainer'

export default {
  components: {
    OperationContainer
  },
  data() {
    return {
      tableColumn: [
        {
          prop: 'building',
          label: '楼栋',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.building.charAt(scope.row.building.length - 1) !==
                '栋'
                  ? `${scope.row.building}栋`
                  : scope.row.building}
              </div>
            )
          }
        },
        {
          prop: 'availableArea',
          label: '可用面积(m²)'
        },
        {
          prop: 'usedArea',
          label: '已用面积(m²)'
        },
        {
          prop: 'unusedArea',
          label: '未用面积(m²)'
        },
        {
          prop: 'floorCount',
          label: '楼层数(层)'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <OperationContainer
                item={scope}
                listLength={this.listLength}
                isSortable={this.isSortable}
                groupRadio={this.groupRadio}
                onPreviewEvent={() => {
                  this.previewEvent(scope.row)
                }}
                onEditHandle={() => {
                  this.editBuild(scope.row)
                }}
                onDeleteHandle={() => {
                  this.deleteBuild(scope.row)
                }}
                onMoveDown={() => {
                  this.moveDown(scope.$index, scope.row)
                }}
                onMoveUp={() => {
                  this.moveUp(scope.$index, scope.row)
                }}
                onMoveTop={() => {
                  this.moveTop(scope.$index, scope.row)
                }}
                onMoveBottom={() => {
                  this.moveBottom(scope.$index, scope.row)
                }}
                onMoveOutHandle={() => {
                  this.moveOutHandle(scope.row)
                }}
              />
            )
          }
        }
      ]
    }
  }
}
