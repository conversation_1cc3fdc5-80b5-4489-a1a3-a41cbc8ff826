<template>
  <div>
    <div v-if="!isSortable">
      <el-link
        v-permission="routeButtonsPermission.EDIT"
        type="primary"
        @click="editHandle"
        class="font-size-14px"
        >{{ routeButtonsTitle.EDIT }}</el-link
      >
    </div>
    <div v-else>
      <el-tooltip
        v-if="item.$index !== 0"
        class="sort-item"
        effect="dark"
        content="上移"
        placement="top"
      >
        <svg-icon icon-class="arrow-up" @click="moveUp" />
      </el-tooltip>
      <el-tooltip
        v-if="item.$index !== listLength - 1"
        class="sort-item"
        effect="dark"
        content="下移"
        placement="top"
      >
        <svg-icon icon-class="arrow-down" @click="moveDown" />
      </el-tooltip>
      <el-tooltip
        v-if="item.$index === listLength - 1"
        class="sort-item"
        effect="dark"
        content="置顶"
        placement="top"
      >
        <svg-icon icon-class="backtop" @click="moveTop" />
      </el-tooltip>
      <el-tooltip
        v-if="item.$index === 0"
        class="sort-item"
        effect="dark"
        content="置底"
        placement="top"
      >
        <svg-icon
          class-name="rotate-180"
          icon-class="backtop"
          @click="moveBottom"
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OperationContainer',
  props: {
    item: {
      type: Object,
      default: null
    },
    isSortable: {
      type: Boolean,
      default: false
    },
    listLength: {
      type: Number,
      default: 0
    }
  },
  methods: {
    editHandle() {
      this.$emit('editHandle')
    },
    moveDown() {
      this.$emit('moveDown')
    },
    moveUp() {
      this.$emit('moveUp')
    },
    moveTop() {
      this.$emit('moveTop')
    },
    moveBottom() {
      this.$emit('moveBottom')
    }
  }
}
</script>

<style scoped lang="scss">
.sort-item {
  color: #ed7b2f;
  cursor: pointer;
  margin-right: 15px !important;
}
.rotate-180 {
  transform: rotate(180deg);
}
</style>
