<template>
  <div class="tree-wrapper">
    <el-tree
      ref="tree"
      :data="treeData"
      :props="defaultProps"
      node-key="unique"
      @node-click="handleNodeClick"
      :default-expanded-keys="expandedList"
      highlight-current
      accordion
      :expand-on-click-node="false"
    ></el-tree>
  </div>
</template>

<script>
import { getMultiGroupTree, getParkTree, getTenantPark } from '../api'
export default {
  name: 'HouseMaintainLeft',
  props: {
    groupDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      expandedList: ['TenantPark'],
      tenantData: {},
      groupRadio: 1,
      currentHigh: 'TenantPark',
      selectData: {}
    }
  },
  inject: ['dataSource'],
  async created() {
    // 获取租户
    this.tenantData = await getTenantPark()
    await this.getData()
  },
  methods: {
    // 点击节点触发
    handleNodeClick(e) {
      this.$bus.$emit('handleNodeClickUp', e)
      this.$bus.$emit('handleNodeClick', e)
      this.selectData = e
      this.currentHigh = e.unique
      this.expandedList = ['TenantPark', e.unique]
      this.$nextTick(() => {
        setTimeout(() => {
          if (
            this.dataSource.$refs?.right.$refs.componentName?.projectGroupClick
          ) {
            this.dataSource.$refs?.right.$refs.componentName?.projectGroupClick(
              e
            )
          }
        }, 500)
      })
    },
    // 获取园区数据
    async getData() {
      // 获取园区
      const treeData = await getParkTree()
      this.treeData = [
        {
          name: this.tenantData.name,
          lv: 0,
          unique: 'TenantPark',
          children: treeData
        }
      ]
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(this.currentHigh)
        this.dataSource.groupDisabled = false
      })
    },
    async getHouseData() {
      if (this.groupRadio === 1) {
        await this.getData()
      } else {
        await this.getMultiGroup()
      }
    },
    // 获取项目分组
    async getMultiGroup() {
      const treeData = await getMultiGroupTree()
      this.treeData = [
        {
          name: this.tenantData.name,
          lv: 0,
          unique: 'TenantPark',
          children: treeData
        }
      ]
      this.$nextTick(() => {
        this.$refs.tree.setCurrentKey(this.currentHigh)
        this.dataSource.groupDisabled = false
      })
    },
    // 标签切换数据初始化
    async initData(groupRadio) {
      this.handleNodeClick(this.treeData[0])
      this.groupRadio = groupRadio
      if (groupRadio === 1) {
        await this.getData()
      } else {
        await this.getMultiGroup()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-wrapper {
  padding: 8px 14px 8px;
}
::v-deep {
  .el-tree-node__content:hover,
  .el-upload-list__item:hover {
    background: #fdf4f0;
  }
  // prettier-ignore
  .el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
    color: #ed7b2f;
    background: transparent;
    &:hover {
      background: #fdf4f0;
    }
  }
  .el-tree-node__content {
    padding-left: 0 !important;
  }
  .el-tree-node::after {
    width: 10px !important;
  }
}
</style>
