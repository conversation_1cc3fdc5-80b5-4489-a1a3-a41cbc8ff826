<template>
  <div class="basic-info">
<!--    <Head />-->

    <drive-table
      ref="drive-table"
      :api-fn="getQualification"
      height="calc(100vh - 369px)"
      :columns="tableColumn"
      @getData="(row) => row?.authStatus === false && verifyFn()"
    >
      <template v-slot:operate-right>
        <div class="flex align-items-center">
          <ExportRecord ref="exportRecord" :type="current" v-permission="routeButtonsPermission.Q_I_LEADING_OUT" />
          <el-button
            v-permission="routeButtonsPermission.Q_I_LEADING_OUT"
            type="primary"
            size="mini"
            @click="exportExcel"
          >
            <svg-icon icon-class="download" />
            <span>导出</span></el-button
          >
        </div>
      </template>
    </drive-table>
  </div>
</template>

<script>
// import Head from '../../components/Head'
import ExportRecord from '../../components/ExportRecord'
import MinixColumn from './column'
import { getQualification, saveQualificationExcel } from '../../api'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'
export default {
  name: 'QualificationCertificate',
  mixins: [MinixColumn],
  props:{
    value:{
      type:Boolean,
      default:true
    },
    current:{
      type:[String,Number],
      default:''
    },
  },
  components: {
      // Head,
      ExportRecord
  },
  inject:['dataLedge'],
  data() {
    return {
      getQualification,
    }
  },
  computed: {
    _value:{
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    verifyFn() {
      this.dataLedge.verifyPermissions({authStatus:false})
    },
    exportExcel() {
      const params = {
        ...this.$refs['drive-table'].querys,
        ...this.extralQuerys
      }
      let url = saveQualificationExcel() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(
        url,
        'excel','资质认证信息表.xls',(res) => {
          const data = res?.request?.response
          if (data.type === "application/json"){
            this.dataLedge.verifyPermissions({authStatus:false})
          }
          this.$refs.exportRecord?.getBrainLog(this.current)
        }
      )
    },

  }
}
</script>

<style scoped lang="scss">
.basic-info{
  padding: 24px;
  background-color: #fff;
}
</style>
