<template>
  <div class="basic-info">
<!--    <Head />-->

    <drive-table
      ref="drive-table"
      :api-fn="getSoftware"
      height="calc(100vh - 369px)"
      :columns="tableColumn"
      @getData="(row) => row?.authStatus === false && verifyFn()"
    >
      <template v-slot:operate-right>
        <div class="flex align-items-center">
          <ExportRecord ref="exportRecord" :type="current" v-permission="routeButtonsPermission.S_C_LEADING_OUT" />
          <el-button
            v-permission="routeButtonsPermission.S_C_LEADING_OUT"
            type="primary"
            size="mini"
            @click="exportExcel"
          >
            <svg-icon icon-class="download" />
            <span>导出</span></el-button
          >
        </div>
      </template>
    </drive-table>
  </div>
</template>

<script>
// import Head from '../../components/Head'
import ExportRecord from '../../components/ExportRecord'
import MinixColumn from './column'
import { getSoftware, saveSoftwareExcel } from '../../api'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'
export default {
  name: 'SoftwareCopyright',
  mixins: [MinixColumn],
  props:{
    value:{
      type:Boolean,
      default:true
    },
    current:{
      type:[String,Number],
      default:''
    },
  },
  components: {
      // Head,
      ExportRecord
  },
  inject:['dataLedge'],
  data() {
    return {
      getSoftware,
    }
  },
  computed: {
    _value:{
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    verifyFn() {
      this.dataLedge.verifyPermissions({authStatus:false})
    },
    exportExcel() {
      const params = {
        ...this.$refs['drive-table'].querys,
        ...this.extralQuerys
      }
      let url = saveSoftwareExcel() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(
        url,
        'excel','软件著作权信息表.xls',
        (res) => {
          const data = res?.request?.response
          if (data.type === "application/json"){
            this.dataLedge.verifyPermissions({authStatus:false})
          }
          this.$refs.exportRecord?.getBrainLog(this.current)
        }
      )
    },
  }
}
</script>

<style scoped lang="scss">
.basic-info{
  padding: 24px;
  background-color: #fff;
}
</style>
