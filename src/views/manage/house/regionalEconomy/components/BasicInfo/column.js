import { NumFormat, parseTime } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'comName',
          label: '企业名称',
          minWidth: 180,
          fixed: 'left',
          showOverflowTooltip: true,
          search: {
            type: 'input'
          }
        },
        {
          prop: 'creditCode',
          label: '统一社会信用代码',
          align: 'center',
          showOverflowTooltip: true,
          minWidth: 140
        },
        {
          prop: 'legalRepresentative',
          label: '法人姓名',
          align: 'center',
          minWidth: 140
        },
        {
          prop: 'foundingTime',
          minWidth: 140,
          align: 'center',
          label: '成立日期'
        },
        {
          prop: 'businessStatus',
          label: '经营状态',
          minWidth: 140,
          align: 'center',
          search: {
            type: 'input'
          }
        },
        {
          prop: 'registeredCapital',
          label: '注册资本',
          hidden: true,
          search: {
            type: 'input'
          }
        },
        {
          prop: 'registeredCapital',
          label: '注册资本(万元)',
          align: 'center',
          minWidth: 140,
          render: (h, { row }) => {
            return (
              <div class="color-primary">
                {NumFormat(row.registeredCapital)}
              </div>
            )
          }
        },
        {
          prop: 'comType',
          label: '企业类型',
          minWidth: 140,
          align: 'center',
          showOverflowTooltip: true,
          search: {
            type: 'input'
          }
        },
        {
          prop: 'beginFoundingTime',
          label: '成立日期',
          hidden: true,
          align: 'center',
          search: {
            type: 'daterange',
            startPlaceholder: '开始时间',
            endPlaceholder: '结束时间',
            rangeSeparator: '-'
          }
        },
        {
          prop: 'industry',
          label: '所属行业',
          align: 'center',
          minWidth: 140
        },
        {
          prop: 'businessScope',
          label: '经营范围',
          align: 'center',
          minWidth: 140,
          showOverflowTooltip: true
        },
        {
          prop: 'dataUpdateTime',
          label: '更新时间',
          align: 'center',
          minWidth: 160,
          render(h, { row }) {
            return (
              <div>
                {parseTime(row.dataUpdateTime, '{y}-{m}-{d} {h}:{i}:{s}')}
              </div>
            )
          }
        }
      ]
    }
  }
}
