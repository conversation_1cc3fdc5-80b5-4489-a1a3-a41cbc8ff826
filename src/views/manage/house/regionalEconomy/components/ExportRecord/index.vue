<template>
  <div class="m-r-8">
    <el-popover popper-class="export-record" placement="bottom-start" width="220" trigger="click">
      <div class="record-card" v-if="record.length">
        <el-scrollbar style="max-height: 280px;min-height: 66px;overflow-y: auto;">
          <div v-for="row in record" :key="row.id">
          <div class="record flex align-items-center">
<!--            <el-avatar class="avatar" :size="38" :src="row.avatar"></el-avatar>-->
            <div class="info">
              <div class="font-size-12 font-strong line-1">{{row.deptName}}-{{ row.opUserName }}</div>
              <div class="font-size-12 color-info">
                {{parseTime(row.opTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
              </div>
            </div>
          </div>
        </div>
        </el-scrollbar>
      </div>
      <div v-else class="font-size-14 color-info text-align-center p-12">暂无记录</div>
      <el-button
        size="mini"
        type="success"
        slot="reference"
      >
        <span>导出记录</span>
        <svg-icon icon-class="chevron-down" />
      </el-button>
    </el-popover>
  </div>
</template>

<script>
import { getBrainLog } from '../../api'
import { parseTime } from '@/utils/tools'

export default {
  name: 'ExportRecord',
  data() {
    return {
      parseTime,
      record: []
    }
  },
  props:{
    type:{
      type:[String,Number],
      default:''
    },
  },
  watch:{
    type:{
      handler(val){
        this.getBrainLog(val)
      },
      immediate:true
    }
  },
  methods:{
    getBrainLog(val){
      getBrainLog(val).then(res => {
        if (res && res.list){
          this.record = res.list
        }
      })
    },
  }
}
</script>

<style scoped lang="scss">
.text-align-center{
  text-align: center;
}
.record{
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
}
.record-card {
  display: flex;
  flex-direction: column;

  .avatar {
    flex: 0 0 38px;
    margin-right: 8px;
  }

  .info {
    width: 100%;
  }
}
</style>
<style>
.export-record{
  padding: 0;
}
</style>
