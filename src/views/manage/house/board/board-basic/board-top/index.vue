<template>
  <div class="h100">
    <el-row :gutter="20" class="h100" type="flex">
      <el-col :span="14">
        <board-search />
      </el-col>
      <el-col :span="10">
        <board-analysis />
      </el-col>
    </el-row>
  </div>
</template>

<script>
import boardAnalysis from './board-analysis.vue'
import BoardSearch from './board-search.vue'

export default {
  name: 'AssetsBoardTopBasic',
  components: {
    boardAnalysis,
    BoardSearch
  },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped></style>
