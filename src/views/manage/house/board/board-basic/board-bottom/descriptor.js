export default {
  data() {
    return {
      formConfigure: {
        labelPosition: 'top',
        descriptors: {
          house: {
            form: 'input',
            label: '问题房源',
            disabled: true,
            rule: [
              {
                type: 'string',
                message: '请输入'
              }
            ]
          },
          reason: {
            form: 'input',
            label: '问题描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请简要说明当前房源问题'
              }
            ],
            props: {
              type: 'textarea'
            }
          },
          attachList: {
            form: 'component',
            label: '附件',
            rule: [
              {
                type: 'array',
                message: '请上传附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'houseBoard'
              }
            }
          }
        }
      }
    }
  }
}
