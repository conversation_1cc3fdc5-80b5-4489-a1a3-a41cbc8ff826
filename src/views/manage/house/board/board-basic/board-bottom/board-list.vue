<template>
  <div class="board-list-wrapper">
    <div v-for="(item, index) in dataSource.boardData" :key="index">
      <div
        v-for="(build, buildIndex) in item.buildingList"
        :key="buildIndex"
        class="board-list-item m-b-16"
      >
        <div
          class="board-list-title bg-base font-size-14 color-text-regular m-b-8"
        >
          {{ item.parkName }} {{ build.building }}
        </div>

        <div
          class="board-container flex m-b-16"
          v-for="(floor, floorIndex) in build.housingBordFloorList"
          :key="floorIndex"
        >
          <div
            class="board-list-floor bg-base font-size-14 color-text-regular m-r-16"
          >
            <div class="h100 flex align-items-center">
              <span class="text-center">{{ floor.floor }}层</span>
            </div>
          </div>

          <div class="board-list-room">
            <el-row :gutter="16">
              <el-col
                v-for="(room, roomIndex) in floor.housingBordRoomList"
                :key="roomIndex"
                :span="30"
                class="m-b-16"
              >
                <div class="room-item border-color-base">
                  <div
                    class="room-title pos-relative font-strong flex align-items-center justify-content-between"
                    :class="_genRoomClass(room.status)"
                    :style="_genRoomStyle(room.status)"
                  >
                    <div class="room-wrapper p-r-10 line-1">
                      {{ room.room }}
                    </div>
                    <div
                      v-if="IsProblem(room.problemStatus)"
                      class="problem-house p-l-8 color-white"
                      :style="{ background: problem }"
                    >
                      <svg-icon icon-class="info-circle" />
                    </div>
                    <div class="p-t-4 pos-absolute more-icon">
                      <el-dropdown>
                        <span
                          :class="
                            IsProblem(room.problemStatus)
                              ? 'color-white'
                              : _genRoomClass(room.status, room.problemStatus)
                          "
                          class="font-size-20"
                        >
                          <svg-icon icon-class="more" />
                        </span>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item>
                            <div
                              @click="
                                getHouseHistory({
                                  ...room,
                                  parkName: item.parkName,
                                  building: build.building,
                                  floor: floor.floor
                                })
                              "
                            >
                              <el-link type="primary"> 房态历史 </el-link>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="room.problemStatus === 1">
                            <div
                              @click="
                                operationEvent(1, {
                                  ...room,
                                  parkName: item.parkName,
                                  building: build.building,
                                  floor: floor.floor
                                })
                              "
                            >
                              <el-link type="danger"> 标记为问题房源 </el-link>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="room.problemStatus === 2">
                            <div
                              @click="
                                operationEvent(2, {
                                  ...room,
                                  parkName: item.parkName,
                                  building: build.building,
                                  floor: floor.floor
                                })
                              "
                            >
                              <el-link type="warning"> 解除问题状态 </el-link>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item v-if="room.status === 2">
                            <div
                              @click="
                                operationEvent(3, {
                                  ...room,
                                  parkName: item.parkName,
                                  building: build.building,
                                  floor: floor.floor
                                })
                              "
                            >
                              <el-link type="danger"> 解除锁定状态 </el-link>
                            </div>
                          </el-dropdown-item>
                          <el-dropdown-item
                            v-if="room.status === 3 || room.status === 4"
                          >
                            <div
                              @click="
                                operationEvent(4, {
                                  ...room,
                                  parkName: item.parkName,
                                  building: build.building,
                                  floor: floor.floor
                                })
                              "
                            >
                              <el-link type="warning"> 房源锁定 </el-link>
                            </div>
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </div>
                  </div>
                  <div class="room-info">
                    <div
                      class="flex align-items-center font-size-14 line-height-22 m-b-8"
                    >
                      <div class="m-r-6 label color-text-secondary">
                        房间面积
                      </div>
                      <div class="value color-text-primary line-1">
                        {{ room.area }}m²
                      </div>
                    </div>

                    <div
                      v-if="room.status === 2"
                      class="flex align-items-center font-size-14 line-height-22"
                    >
                      <div class="m-r-6 label color-text-secondary">
                        锁定原因
                      </div>
                      <div class="value color-black line-1">
                        {{ room.lockReason }}
                      </div>
                    </div>

                    <div
                      v-if="room.problemStatus === 2"
                      class="flex align-items-center font-size-14 line-height-22"
                    >
                      <div class="m-r-6 label color-text-secondary">
                        问题描述
                      </div>
                      <div class="value color-black line-1">
                        {{ room.problemDesc }}
                      </div>
                    </div>

                    <div
                      v-if="room.status === 4 && room.entName"
                      class="flex align-items-center font-size-14 line-height-22"
                    >
                      <div class="m-r-6 label color-text-secondary">
                        入住公司
                      </div>
                      <div class="value color-primary line-1">
                        <el-tooltip
                          class="item"
                          effect="dark"
                          :content="room.entName"
                          placement="top"
                        >
                          <el-link class="color-primary">{{
                            room.entName
                          }}</el-link>
                        </el-tooltip>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 房源历史 -->
    <house-history ref="house-history" />

    <!-- 操作 -->
    <house-operation ref="house-operation" />
  </div>
</template>

<script>
import colorMixin from './_genColor'
import HouseHistory from './houseHistory'
import HouseOperation from './houseOperation'

export default {
  name: 'BoardList',
  mixins: [colorMixin],
  inject: ['dataSource'],
  components: {
    HouseHistory,
    HouseOperation
  },
  data() {
    return {}
  },
  methods: {
    // 查看房源历史
    getHouseHistory(room) {
      this.$refs['house-history'].getRoomLog(room)
    },

    operationEvent(flag, room) {
      this.$refs['house-operation'].openDialog(flag, room)
    }
  }
}
</script>

<style lang="scss" scoped>
.board-list-wrapper {
  .board-list-item {
    .board-list-title {
      width: 100%;
      height: 40px;
      background: #f3f3f3;
      border-radius: 3px;
      line-height: 40px;
      text-align: center;
    }

    .board-container {
      width: 100%;
      .board-list-floor {
        flex: 0 0 30px;
        border-radius: 3px;
        padding: 0 7px;

        .text-center {
          text-align: center;
        }
      }

      .board-list-room {
        flex: 1;
        .room-item {
          height: 152px;
          border-radius: 3px;
          padding: 8px;
          border: 1px solid;
          .room-title {
            width: 100%;
            height: 30px;
            border-radius: 3px;
            line-height: 30px;
            padding-left: 8px;

            .room-wrapper {
              flex: 1;
            }

            .problem-house {
              flex: 0 0 60px;
              width: 60px;
              height: 30px;
              border-radius: 3px;
              backdrop-filter: blur(4px);
            }

            .more-icon {
              right: 0;
            }
          }
        }

        .room-info {
          padding: 16px 8px 0;

          .label {
            flex: 0 0 56px;
          }

          .value {
            flex: 1;
          }
        }
        .el-col-30 {
          width: 20%;
          height: 152px;
          padding: 8px;
        }
      }
    }
  }
}
</style>
