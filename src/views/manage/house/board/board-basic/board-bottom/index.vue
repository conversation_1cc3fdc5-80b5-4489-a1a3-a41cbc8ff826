<template>
  <div>
    <div class="top-wrapper flex align-items-center justify-content-between">
      <div class="tips-wrapper flex align-items-center font-size-14 text-black">
        <div
          v-for="(item, index) in tipsList"
          :key="index"
          class="tips-item flex align-items-center m-r-24"
        >
          <div
            v-if="item.label === '问题房源'"
            class="problem-circle color-white flex align-items-center justify-content-center m-r-4"
            :style="{ background: item.color }"
          >
            <svg-icon icon-class="info-circle" class />
          </div>
          <span
            v-else
            class="tip-circle"
            :style="{ background: item.color }"
          ></span>
          <span>{{ item.label }}</span>
        </div>
      </div>

      <tab @triggerEvent="triggerEvent" :current="current" />
    </div>

    <div class="bottom-wrapper p-t-16">
      <div v-if="dataSource.boardData && dataSource.boardData.length">
        <component :is="board" />
      </div>
      <div v-else>
        <empty-data />
      </div>
    </div>
  </div>
</template>

<script>
import color from '../../../colorBasic'
import Tab from './tab.vue'
import BoardList from './board-list'
import boardTable from './boardTable'

export default {
  name: 'AssetsBoardBottomBasic',
  inject: ['dataSource'],
  components: {
    Tab,
    BoardList,
    boardTable
  },
  data() {
    return {
      tipsList: [
        {
          label: '已用房源',
          color: color.basic[2]
        },
        {
          label: '空置房源',
          color: color.basic[1]
        },
        {
          label: '锁定房源',
          color: color.basic[0]
        },
        {
          label: '问题房源',
          color: color.problem
        }
      ],
      current: 0
    }
  },
  computed: {
    board() {
      return this.current === 0 ? boardTable : BoardList
    }
  },
  methods: {
    triggerEvent(e) {
      this.current = e
    }
  }
}
</script>

<style lang="scss" scoped>
.top-wrapper {
  height: 40px;
  line-height: 40px;
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  .tips-wrapper {
    .tips-item {
      .tip-circle {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
        background: #bed2fe;
      }

      .problem-circle {
        width: 20px;
        height: 20px;
        border-radius: 3px;
      }
    }
  }
}
</style>
