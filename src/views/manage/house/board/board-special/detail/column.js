import { parseTime } from '@/utils/tools'

export default {
  data() {
    return {
      statisticsColumn: [
        {
          label: '天数',
          prop: 'day',
          align: 'center',
          render: (h, scope) => {
            return <div>第{scope.row.day}天</div>
          }
        },
        {
          label: '状态',
          prop: 'statusStr',
          align: 'center'
        },
        {
          label: '操作人',
          prop: 'operator',
          align: 'center'
        },
        {
          label: '开始时间',
          prop: 'stime',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>{parseTime(scope.row.stime, '{y}-{m}-{d} {h}:{i}')}</div>
            )
          }
        },
        {
          label: '结束时间',
          prop: 'etime',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>{parseTime(scope.row.etime, '{y}-{m}-{d} {h}:{i}')}</div>
            )
          }
        }
      ]
    }
  }
}
