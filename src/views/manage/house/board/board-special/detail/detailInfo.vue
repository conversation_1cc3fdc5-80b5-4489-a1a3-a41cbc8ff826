<template>
  <div class="info-wrapper">
    <el-row>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">房源面积</div>
          <div class="item-content">
            <span>{{ info.area }}</span>
            <span class="area-unit">㎡</span>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">测绘状态</div>
          <div class="item-content">
            {{ info.hasSurveying ? '已测绘' : '未测绘' }}
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">房源类型</div>
          <div class="item-content" v-if="info.roomTypesStr">
            {{ info.roomTypesStr }}
          </div>
          <div class="item-content empty" v-else>--</div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">房源用途</div>
          <div class="item-content" v-if="info.useTypeStr">
            {{ info.useTypeStr }}
          </div>
          <div class="item-content empty" v-else>--</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">业态类别</div>
          <div class="item-content" v-if="info.businessTypeStr">
            {{ info.businessTypeStr }}
          </div>
          <div class="item-content empty" v-else>--</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">装修情况</div>
          <div class="item-content" v-if="info.fitmentStr">
            {{ info.fitmentStr }}
          </div>
          <div class="item-content empty" v-else>--</div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">租售类别</div>
          <div class="item-content" v-if="info.categoryStr">
            {{ info.categoryStr }}
          </div>
          <div class="item-content empty" v-else>--</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">计费规则</div>
          <div class="item-content" v-if="info.priceStr">
            {{ info.priceStr }}
          </div>
          <div class="item-content empty" v-else>暂无数据</div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">合同数量</div>
          <div class="item-content">
            执行中{{ info.execution }}份/历史{{ info.history }}份
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">入驻企业</div>
          <div class="item-content" v-if="info.entName">
            <el-link
              v-if="info.enterStatus === 1 || info.enterStatus === 2"
              type="primary"
              @click="goEntHandle(info.entId)"
              >{{ info.entName }}</el-link
            >
            <span v-else>{{ info.entName }}</span>
          </div>
          <div class="item-content empty" v-else>暂无数据</div>
        </div>
      </el-col>
      <el-col :span="16">
        <div class="info-item">
          <div class="item-label">当前合同</div>
          <div
            v-if="info.currentContractNo"
            class="item-content flex align-items-center"
          >
            <el-link type="primary" @click="toContractDetail">{{
              info.currentContractNo
            }}</el-link>
            <basic-tag
              v-if="info.expiredDay"
              class="m-l-8"
              :label="info.expiredDay + '日后过期'"
            />
          </div>
          <div class="item-content empty" v-else>暂无数据</div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="8">
        <div class="info-item">
          <div class="item-label">租赁期限</div>
          <div class="item-content" v-if="info.startTime">
            {{ info.startTime }} ~ {{ info.endTime }}
          </div>
          <div class="item-content empty" v-else>暂无数据</div>
        </div>
      </el-col>
    </el-row>
    <slider-img-viewer
      v-if="showSlider"
      ref="sliderImgViewer"
      class="m-t-16"
      :list="sliderList"
      @tabChange="tabChange"
      :current="currentTab"
      @copyHandle="copyHandle"
    />
    <empty-data
      v-else
      style="margin-top: 100px"
      description="此房间下没有任何影像信息"
    />
  </div>
</template>

<script>
import { parseTime } from '@/utils/tools'
import SliderImgViewer from '@/components/SliderImgViewer'
import handleClipboard from '@/utils/clipboard'

export default {
  name: 'DetailInfo',
  components: { SliderImgViewer },
  props: {
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      parseTime,
      currentTab: 1
    }
  },
  computed: {
    showSlider() {
      const row = this.sliderList.find(item => item.pic && item.pic.length)
      return !!row
    },
    sliderList() {
      const {
        realAttachMap = {},
        planAttachMap = {},
        typeAttachMap = {}
      } = this.info
      const realAttachIds = realAttachMap.roomAttach || []
      const planAttachIds = planAttachMap.roomAttach || []
      const typeAttachIds = typeAttachMap.roomAttach || []
      return [
        { label: '实景图', value: 1, pic: realAttachIds },
        { label: '平面图', value: 2, pic: planAttachIds },
        { label: '户型图', value: 3, pic: typeAttachIds }
      ]
    }
  },
  methods: {
    copyHandle($event) {
      const { origin, pathname } = window.location
      const url =
        origin + pathname + '#/housingImgViewer?roomId=' + this.info.roomId
      handleClipboard(url, $event)
    },
    tabChange(e) {
      this.currentTab = e
    },
    // 查看详情跳转合同详情页面
    toContractDetail() {
      const routeData = this.$router.resolve({
        path: '/contract/index/contractDetails',
        query: {
          id: this.info.currentContractId,
          orderId: this.info.currentContractOrderId
        }
      })
      window.open(routeData.href, '_blank')
    },
    goEntHandle(id) {
      const routeData = this.$router.resolve({
        path: '/business/enterpriseDetails',
        query: {
          id
        }
      })
      window.open(routeData.href, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
.info-wrapper {
  margin-top: -16px;
  .info-item {
    font-size: 14px;
    margin-top: 16px;
    display: flex;
    line-height: 22px;
    .item-label {
      color: rgba(0, 0, 0, 0.4);
      flex-shrink: 0;
    }
    .item-content {
      padding-left: 16px;
      color: rgba(0, 0, 0, 0.9);
      .area-unit {
        color: rgba(0, 0, 0, 0.4);
        padding-left: 2px;
      }
      &.empty {
        color: rgba(0, 0, 0, 0.26);
      }
    }
  }
  .slider-empty {
    text-align: center;
  }
}
</style>
