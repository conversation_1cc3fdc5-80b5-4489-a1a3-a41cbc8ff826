<template>
  <div class="park-empty-container">
    <dialog-cmp
      title="房源看板"
      :visible="dialogVisible"
      :have-operation="false"
      :append-to-body="false"
      width="800px"
      @close="dialogVisible = false"
    >
      <div class="guide-empty">
        <div class="p-t-12 flex align-items-center">
          <div class="guide-empty-content p-r-24 font-size-14 line-height-22">
            <div class="color-black">什么是房源看板?</div>
            <div class="content m-t-16">
              根据业务系统中的入园情况、合同情况去综合判断房间的使用状态和房态历史,并以可视化方式直观呈现房源的销控情况。并对关注的整体房源或部分房源进行入驻率相关的统计。
            </div>
            <div class="m-t-16 color-black">为什么这里没有任何数据?</div>
            <div class="content m-t-16 flex flex-wrap">
              目前没有任何房源数据录入到系统中。首先您需要在
              <el-link type="primary" @click="goHouseHandle">房源库</el-link>
              目录中去手动添加或批量导入房源(有相关模版提供下载)。其次对企业的入园协议、租赁协议进行管理，系统会自动呈现完整的看板数据。
            </div>
          </div>
          <div class="guide-empty-img p-l-24">
            <img class="wh100" src="../images/house-board.png" alt="" />
          </div>
        </div>
        <div class="add-btn">
          <el-button type="primary" @click="addHouseHandle">添加房源</el-button>
        </div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
export default {
  name: 'ParkEmpty',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  watch: {
    visible: {
      handler(val) {
        this.dialogVisible = val
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    goHouseHandle() {
      const routeUrl = this.$router.resolve({
        path: '/houseManage/index'
      })
      window.open(routeUrl.href, '_blank')
    },
    addHouseHandle() {
      this.dialogVisible = false
      this.$router.push('/houseManage/index')
    }
  }
}
</script>

<style scoped lang="scss">
.park-empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .guide-empty {
    &.guide-empty-wrapper {
      max-width: 800px;
      padding: 24px;
    }
    .guide-empty-content {
      border-right: 1px solid;
      @include border_color(--border-color-light);
      .content {
        color: rgba(0, 0, 0, 0.6);
      }
    }
    .guide-empty-img {
      width: 350px;
      height: 230px;
      border-radius: 3px;
      overflow: hidden;
      flex-shrink: 0;
      img {
        object-fit: contain;
      }
    }
    .add-btn {
      margin-top: 86px;
      text-align: right;
    }
  }
}
</style>
