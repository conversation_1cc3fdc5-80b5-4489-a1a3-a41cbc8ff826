import request from '@/utils/request'

// 账户树
export function findPayAccTree() {
  return request({
    url: '/payAccInfo/findPayAccTree',
    method: 'post'
  })
}

// 根据账户Id查看账户信息
export function getPayAccInfoById(id) {
  return request({
    url: `/payAccInfo/getPayAccInfoById/${id}`,
    method: 'get'
  })
}

// 根据id查询账户列表
export function findParkAccList(params) {
  return request({
    url: '/payAccInfo/findParkAccList',
    method: 'get',
    params
  })
}

// 根据账号获取历史账务信息
export function getHisAccountingList(params) {
  return request({
    url: '/payAccInfo/getHisAccountingList',
    method: 'get',
    params
  })
}

// 获取当日账务信息
export function getToDayAccountingList(data) {
  return request({
    url: '/payAccInfo/getToDayAccountingList',
    method: 'post',
    data
  })
}

// 根据账户Ids刷新账户信息
export function refreshPayAccInfoById(data) {
  return request({
    url: '/payAccInfo/refreshPayAccInfoById',
    method: 'post',
    data
  })
}

// 企业端-入园申请查询可选的所有入驻园区信息
export function queryParkInfo(parkType) {
  return request({
    url: `dataStation/queryParkInfo/${parkType}`,
    method: 'get'
  })
}
