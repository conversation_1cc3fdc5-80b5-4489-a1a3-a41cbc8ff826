// 合同状态【1-待审核；2-起草中；3-审核中；4-已通过；5-已签订；6-执行中；7-已到期；8-系统终止；9-手动终止；10-已退回；11-已拒绝；】
export function getContractStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag isDot type="warning" label="待审核" />
    case 2:
      return <basic-tag isDot type="primary" label="起草中" />
    case 3:
      return <basic-tag isDot type="success" label="审核中" />
    case 4:
      return <basic-tag isDot type="success" label="已通过" />
    case 5:
      return <basic-tag isDot type="success" label="已签订" />
    case 6:
      return <basic-tag isDot type="success" label="执行中" />
    case 7:
      return <basic-tag isDot type="danger" label="已到期" />
    case 8:
      return <basic-tag isDot type="danger" label="系统终止" />
    case 9:
      return <basic-tag isDot type="danger" label="手动终止" />
    case 10:
      return <basic-tag isDot type="danger" label="已退回" />
    case 11:
      return <basic-tag isDot type="danger" label="已拒绝" />
    default:
      return '-'
  }
}

// 合同状态
export const contractStatusOptions = [
  {
    label: '待审核',
    value: 1
  },
  {
    label: '起草中',
    value: 2
  },
  {
    label: '审核中',
    value: 3
  },
  {
    label: '已通过',
    value: 4
  },
  {
    label: '已签订',
    value: 5
  },
  {
    label: '执行中',
    value: 6
  },
  {
    label: '已到期',
    value: 7
  },
  {
    label: '系统终止',
    value: 8
  },
  {
    label: '手动终止',
    value: 9
  },
  {
    label: '已退回',
    value: 10
  },
  {
    label: '已拒绝',
    value: 11
  }
]

// 获取合同类型
export function getContractType(h, val) {
  switch (val) {
    case 1:
      return '新签租房'
    case 2:
      return '续签租房'
    // case 3:
    //   return '售房合同'
    default:
      return '-'
  }
}

export const contractTypeOptions = [
  {
    label: '新签租房',
    value: 1
  },
  {
    label: '续签租房',
    value: 2
  }
  // {
  //   label: '售房合同',
  //   value: 3
  // }
]

// 付款方式【1-月度付款；2-季度付款；3-半年付款；4-年度付款】
export const payCycleOptions = [
  {
    label: '月度付款',
    value: 1
  },
  {
    label: '季度付款',
    value: 3
  },
  {
    label: '半年付款',
    value: 6
  },
  {
    label: '年度付款',
    value: 12
  }
]

export function getPayCycle(val) {
  switch (val) {
    case 1:
      return '月度付款'
    case 3:
      return '季度付款'
    case 6:
      return '半年付款'
    case 12:
      return '年度付款'
    default:
      return '-'
  }
}

// 费用类型【1-房租费；2-保证金；3-服务费】
export function getPlanTypeType(h, val) {
  switch (val) {
    case 1:
      return '房租费'
    case 2:
      return '保证金'
    case 3:
      return '服务费'
    default:
      return '-'
  }
}

export function getStatus(val) {
  switch (val) {
    case 1:
      return '起草中'
    case 2:
      return '审批中'
    case 3:
      return '待签订'
    case 4:
      return '待生效'
    case 5:
      return '执行中'
    case 6:
      return '已终止'
    case 7:
      return '已作废'
    case 8:
      return '已拒绝'
    case 9:
      return '已到期'
    case 10:
      return '-'
    default:
      return '-'
  }
}

export function getStatusColor(val) {
  switch (val) {
    case 1:
      return 'warning'
    case 2:
      return 'primary'
    case 3:
      return 'primary'
    case 4:
      return 'success'
    case 5:
      return 'warning'
    case 6:
      return 'danger'
    case 7:
      return 'danger'
    case 8:
      return 'danger'
    case 9:
      return 'danger'
    case 10:
      return 'warning'
    default:
      return '-'
  }
}

// 获取合同类型
export function getType(val) {
  switch (val) {
    case 1:
      return '新签租房'
    case 2:
      return '续签租房'
    // case 3:
    //   return '售房合同'
    default:
      return '-'
  }
}

// 支付状态
export function getPayStatus(val) {
  switch (val) {
    case 1:
      return '未收'
    case 2:
      return '部分已收'
    case 3:
      return '已收'
    default:
      return '-'
  }
}

// 状态
export function getPlanType(val) {
  switch (val) {
    case 0:
      return '未出账'
    case 1:
      return '已出账'
    case 2:
      return '作废'
    default:
      return '-'
  }
}

export function tranListToTreeData(list, rootValue) {
  for (let i = 0; i < list.length; i++) {
    if (list[i].list[i].id === rootValue[i]) {
      tranListToTreeData(list[i].list)
    }
  }
  console.log(tranListToTreeData, '\\\\')
}
