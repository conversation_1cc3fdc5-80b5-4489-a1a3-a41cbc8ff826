<template>
  <div>
    <!--<div class="legend flex justify-content-center m-b-20">-->
    <!--<div-->
    <!--v-for="(item, key) in colors"-->
    <!--:key="key"-->
    <!--class="item flex align-items-center"-->
    <!--&gt;-->
    <!--<span>{{ item.label }}</span>-->
    <!--<div class="color" :style="{ backgroundColor: item.value }" />-->
    <!--</div>-->
    <!--</div>-->
    <div id="container" ref="container" />
  </div>
</template>

<script>
import G6 from '@antv/g6'
export default {
  name: 'TreeEcharts',
  props: {
    dataSource: {
      type: Object
    },
    flag: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      colors: [
        {
          label: '一级账户',
          value: '#e5e0eb'
        },
        {
          label: '二级账户',
          value: '#ddd9c5'
        },
        {
          label: '三级账户',
          value: '#c9d9ef'
        },
        {
          label: '四级账户',
          value: '#eedddc'
        }
      ]
    }
  },
  watch: {
    dataSource() {
      this.$nextTick(() => {
        this.initData()
      })
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    /**
     * 隐藏四级账户
     */
    hide4thDepthData(data) {
      if (data.childs && data.childs.length > 0) data.children = data.childs
      data.id += '' // id只能为string
      if (data.children && data.children.length > 0) {
        if (data.children[0].level === 3) {
          data.collapsed = true // 默认不展开企业汇缴账户
          if (data.children.length >= 3) {
            // const extraNum = data.children.length - 3
            data.children.length = 3
            data.children.push({
              id: Math.random(),
              level: 3,
              name: `……`
            })
          }
          data.children.forEach(child => {
            child.id += ''
          })
        } else {
          data.children.forEach(x => {
            this.hide4thDepthData(x)
          })
        }
      }
      return data
    },
    initData() {
      const _this = this
      const container = this.$refs.container
      const width =
        document.getElementById('container').scrollWidth || window.innerWidth

      const height =
        document.getElementById('container').scrollHeight ||
        window.innerHeight - 222

      G6.registerEdge('hvh', {
        draw(cfg, group) {
          const startPoint = cfg.startPoint
          const endPoint = cfg.endPoint
          const shape = group.addShape('path', {
            attrs: {
              stroke: '#A3B1BF',
              path: [
                ['M', startPoint.x, startPoint.y],
                ['L', startPoint.x, endPoint.y / 3 + (2 / 3) * startPoint.y], // 三分之一处
                ['L', endPoint.x, endPoint.y / 3 + (2 / 3) * startPoint.y], // 三分之二处
                ['L', endPoint.x, endPoint.y]
              ]
            },
            // must be assigned in G6 3.3 and later versions. it can be any value you want
            name: 'path-shape'
          })
          return shape
        }
      })
      G6.registerNode(
        'tree-node',
        {
          drawShape: function drawShape(cfg, group) {
            let rect = group.addShape('rect', {
              attrs: {
                fill: _this.colors[cfg.level]?.value || '#fff',
                stroke: '#666',
                radius: 3,
                cursor: 'pointer',
                opacity: 0.5
              }
            })
            let content = cfg.name.replace(/(.{19})/g, '$1\n')
            let text = group.addShape('text', {
              attrs: {
                text: content,
                x: 0,
                y: 0,
                fontSize: 8,
                textAlign: 'center',
                textBaseline: 'middle',
                fontWeight: 'bold',
                fill: '#17386c',
                cursor: 'pointer',
                lineHeight: 18
              }
            })
            let bbox = text.getBBox()
            let haschildren = cfg.children && cfg.children.length > 0
            if (haschildren) {
              group.addShape('text', {
                attrs: {
                  x: bbox.width / 2 + 2,
                  y: bbox.height / 2,
                  fill: '#17386c',
                  cursor: 'pointer',
                  text: cfg.collapsed ? '+' : '-'
                },
                name: 'collapse-text'
              })
            }
            const normalNodeAttr = {
              x: bbox.minX - 8,
              y: bbox.minY - 4,
              width: bbox.width + 16,
              height: bbox.height + 8
            }
            const hasChildNodeAttr = {
              x: bbox.minX - 4,
              y: bbox.minY - 4,
              width: bbox.width + 16,
              height: bbox.height + 8
            }
            rect.attr(haschildren ? hasChildNodeAttr : normalNodeAttr)
            return rect
          }
        },
        'single-shape'
      )

      const graph = new G6.TreeGraph({
        container,
        width,
        height,
        fitView: true,
        fitCenter: true,
        // minZoom: 4,
        maxZoom: 5,
        // linkCenter: true,
        modes: {
          default: [
            'drag-canvas',
            'zoom-canvas',
            // {
            //   type: 'click-select',
            //   multiple: false
            // },
            {
              type: 'collapse-expand',
              trigger: 'click'

              // shouldBegin: (e) => {
              //   if (e.item && e.item.getModel().depth === 2) return true
              //   return false
              // }
            }
          ]
        },

        // 节点不同状态下的样式集合
        nodeStateStyles: {
          selected: {
            lineWidth: 1,
            stroke: '#17386c'
          }
        },
        defaultNode: {
          type: 'tree-node',
          anchorPoints: [
            // [0, 0.5],
            // [1, 0.5]
            [0.5, 0],
            [0.5, 1]
          ]
        },
        defaultEdge: {
          type: 'hvh',
          // type: 'cubic-horizontal',
          style: {
            stroke: '#A3B1BF',
            radius: 2
          }
        },

        layout: {
          type: 'compactBox',
          direction: 'TB',
          getId: function getId(d) {
            return d.id
          },
          getHeight: function getHeight() {
            return 16
          },
          getWidth: function getWidth() {
            return 24
          },
          getVGap: function getVGap() {
            return 20
          },
          getHGap: function getHGap(d) {
            const { level, name } = d
            // 一、二级账户默认返回40
            if (level < 2) {
              return 40
            }
            // 三、四级账户间距特殊处理
            let factor = 1
            if (level === 2) {
              factor = 5
            }
            if (level === 3) {
              factor = 5
            }
            return factor * name.length || 40
          }
        }
      })
      const data = _this.hide4thDepthData(this.dataSource)
      graph.data(data)
      graph.render()
      graph.fitView()

      // graph.on('node:click', (event) => {
      //   if (this.flag === 'accounting' && event.item._cfg.model.depth === 3) {
      //     return false
      //   }
      //   this.$emit(
      //     'open',
      //     event.item._cfg.id,
      //     event.item._cfg.model.depth,
      //     event.item._cfg.model.cusAc
      //   )
      //   graph.changeSize(800, 600)
      //   graph.fitView()
      // })
      graph.on('itemcollapsed', e => {
        graph.fitView()
        const { item, collapsed } = e
        const group = item.getContainer()
        const collapseText = group.find(x => x.get('name') === 'collapse-text')
        if (!collapseText) return
        collapseText.attr({
          text: collapsed ? '+' : '-'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.legend {
  .color {
    height: 20px;
    width: 40px;
    margin-left: 10px;
    margin-right: 40px;
    border: 2px solid #666;
    border-radius: 3px;
    opacity: 0.5;
  }
}
</style>
