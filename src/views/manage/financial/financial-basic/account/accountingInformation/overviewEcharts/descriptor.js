export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '0',
        descriptors: {
          time: {
            form: 'select',
            span: 5,
            label: '',
            options: [
              {
                label: '男',
                value: 1
              },
              {
                label: '女',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择性别'
              }
            ]
          },
          date: {
            form: 'select',
            span: 5,
            label: '',
            options: [
              {
                label: '男',
                value: 1
              },
              {
                label: '女',
                value: 2
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择性别'
              }
            ]
          },
          onDutyTime: {
            form: 'date',
            span: 5,
            label: '',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择日期'
              }
            ]
          }
        }
      }
    }
  }
}
