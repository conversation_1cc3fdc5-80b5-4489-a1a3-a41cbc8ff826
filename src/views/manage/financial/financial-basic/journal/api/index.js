import request from '@/utils/request'

export function getEntAccountsStatistics() {
  return request({
    url: `/bill/bankdzd/record_statistics`,
    method: 'get'
  })
}

// 获得园区
export function getParks() {
  return request({
    url: `/bill/purse/admin/parks`,
    method: 'get'
  })
}

// 获得流水记录分页-按明细
export function getPurseList(params) {
  return request({
    url: `/bill/bankdzd/page`,
    method: 'get',
    params
  })
}

export function getStates() {
  return request({
    url: `/bill/purse/admin/states`,
    method: 'get'
  })
}

// 获得详情
export function getDetails(params) {
  return request({
    url: `/bill/bankdzd/get?id=${params}`,
    method: 'get'
  })
}

// 获得余额类型选择
export function getBalanceType() {
  return request({
    url: `/bill/purse/admin/balance_type`,
    method: 'get'
  })
}

export function getTurnTab() {
  return request({
    url: `/bill/purse/admin/turn`,
    method: 'get'
  })
}

// 获得列表
export function getDetailsList(params) {
  return request({
    url: `/bill/purse/admin/detail_page`,
    method: 'get',
    params
  })
}

//详情 - 获得业务来源
export function getSourceType() {
  return request({
    url: `/bill/purse/admin/source`,
    method: 'get'
  })
}

//详情 - 获得部门
export function getSimple() {
  return request({
    url: `/system/dept/list-all-simple`,
    method: 'get'
  })
}

// 房源导入模板
export function getImportTemplate() {
  return `${process.env.VUE_APP_URL_PREFIX}/upload-file/流水记录导入模板.xlsx`
}

// 导入
export function getParkUpload(data) {
  return request({
    url: `/bill/bankdzd/upload`,
    method: 'post',
    data,
    isFormData: true
  })
}

// 认领状态下拉
export function getClaimStatus() {
  return request({
    url: `/bill/bankdzd/claim_status`,
    method: 'get'
  })
}
//导出流水 Excel
export function exportBankdzdRecordExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/bill/bankdzd/export_record`
}

// 费用类型下拉
export function getFeeType() {
  return request({
    url: `/bill/bankdzd/get_fee_type`,
    method: 'get'
  })
}

// 费用类型下拉
export function getContract(data) {
  return request({
    url: `/bill/bankdzd/get_contract_by_entId?entId=${data}`,
    method: 'get'
  })
}

// 费用类型下拉
export function getEnt(params) {
  return request({
    url: `/bill/bankdzd/get_ent_list?oppacc=${params}`,
    method: 'get'
  })
}
// 创建
export function claimCreate(data) {
  return request({
    url: `/bill/bankdzd/claim_create`,
    method: 'post',
    data
  })
}

// 获取待核销账单
export function getCollectBill(params) {
  return request({
    url: `/pay/bill_total/get_collect_bill`,
    method: 'get',
    params
  })
}
// 获取账户信息
export function getRefundApply(params) {
  return request({
    url: `/bill/withdrawal/admin/get_refund_apply`,
    method: 'get',
    params
  })
}
