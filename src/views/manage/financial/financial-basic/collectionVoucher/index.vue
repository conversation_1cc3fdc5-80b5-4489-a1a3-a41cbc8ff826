<template>
  <div>
    <basic-card>
      <div class="p-t-14 p-b-14">
        <drive-table
          ref="driveTable"
          height="calc(100vh - 335px)"
          :columns="tableColumn"
          :api-fn="getPaymentBankTraceList"
          :search-querys-hook="searchQuerysHook"
        >
          <template v-slot:operate-right>
            <el-button type="info" size="small" @click="exportExcels">
              <i class="el-icon-download m-r-4"></i>
              导出</el-button
            >
          </template>
        </drive-table>
      </div>
    </basic-card>
  </div>
</template>

<script>
import { getPaymentBankTraceList, saveExportExcel } from './api/index'
import CloumnMixins from './column'
export default {
  name: 'collectionVoucher',
  mixins: [CloumnMixins],
  data() {
    return { getPaymentBankTraceList }
  },
  created() {
    // this.queryParkInfo()
  },
  methods: {
    // 选中
    // selectionChange(e) {},

    // 导出凭证报表
    exportExcels() {
      const params = {
        ...this.$refs.driveTable.querys.querys
      }
      saveExportExcel(params).then(uuid1 => {
        window.open(
          `${process.env.VUE_APP_URL_PREFIX}/receipt/exportExcel/${uuid1}`
        )
      })
    },

    // 下载凭证
    // 查看凭证
    goTableDetails(e) {
      // console.log(e, 'e')
      const newWindow = window.open()
      // newWindow.open(
      //   `${process.env.VUE_APP_URL_PREFIX}/receipt/download/${e.id}`
      // )
      newWindow.location.href = `${process.env.VUE_APP_URL_PREFIX}/receipt/download/${e.id}`
    },

    // 查询园区
    // queryParkInfo() {
    //   queryParkInfo(-2).then(res => {
    //     this.tableColumn[0].search.options = res.map(item => {
    //       return {
    //         value: item.parkId,
    //         label: item.parkName
    //       }
    //     })
    //   })
    // },

    // 跳转企业详情
    // goDetails(row) {
    //   const { entId, entName } = row
    //   this.$bus.$emit('openEnterpriseDetails', {
    //     entId: entId,
    //     entName: entName
    //   })
    //   this.$router.push({
    //     path: '/enterpriseLibrary/enterpriseLibraryDetails',
    //     query: {
    //       entId: e.entId
    //     }
    //   })
    // },

    // 查询参数钩子
    searchQuerysHook(e) {
      const [startTxnTime = '', endTxnTime = ''] = e.txnTime || []
      delete e.txnTime
      return {
        ...e,
        startTxnTime,
        endTxnTime
      }
    }

    // 根据id查询单条凭证
    // findAllPayCredentials() {
    //   findAllPayCredentials().then(res => {
    //     // console.log(res)
    //   })
    // },

    // 根据id查看多条凭证
    // getPayCredentialsById() {
    //   getPayCredentialsById().then(res => {})
    // }
  }
}
</script>

<style></style>
