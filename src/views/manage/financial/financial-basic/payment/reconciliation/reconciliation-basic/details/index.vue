<template>
  <div>
    <basic-card>
      <div>
        <el-row>
          <el-col span="6">
            <div class="font-size-18 font-strong">
              {{ detailsInfo.headName }}
            </div>
          </el-col>
          <el-col span="18">
            <div
              class="flex justify-content-end"
              v-if="detailsInfo.ticketStatus === 1"
            >
              <el-button
                v-permission="routeButtonsPermission.WITHDRAW"
                type="info"
                class="m-l-8"
                @click="sendBackVisible = true"
                >{{ routeButtonsTitle.WITHDRAW }}</el-button
              >
              <el-button
                v-permission="routeButtonsPermission.CONFIRM_INVOICING"
                type="primary"
                class="m-l-8"
                @click="drawbillClick"
                >{{ routeButtonsTitle.CONFIRM_INVOICING }}</el-button
              >
            </div>
            <div class="flex justify-content-end m-t-8">
              <span class="font-size-14 info inline-block m-r-8">申请时间</span>
              <span class="font-size-14 info">{{
                detailsInfo.createTime | noData
              }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="font-size-14 m-b-16">申请信息</div>
      <table
        class="w100 table"
        border="1"
        Cellspacing="0"
        Cellpadding="0"
        bordercolor="#dcdcdc"
      >
        <tr align="left">
          <th>购买方名称</th>
          <td>{{ detailsInfo.headName | noData }}</td>
          <th>纳税人识别号</th>
          <td>{{ detailsInfo.headCreditCode | noData }}</td>
        </tr>
        <tr align="left">
          <th>地址电话</th>
          <td>
            {{ detailsInfo.headRegAddress | noData }}
            {{ detailsInfo.headPhone | noData }}
          </td>
          <th>开户行及银行账户</th>
          <td>
            {{ detailsInfo.headBankName | noData }}
            {{ detailsInfo.headBankAccount | noData }}
          </td>
        </tr>
        <tr align="left">
          <th>销售方名称</th>
          <td>
            {{ detailsInfo.subject | noData }}
          </td>
          <th>纳税人识别号</th>
          <td>{{ detailsInfo.subjectCreditCode | noData }}</td>
        </tr>
        <tr align="left">
          <th>地址电话</th>
          <td>{{ detailsInfo.subjectAddressPhone | noData }}</td>
          <th>开户行及银行账户</th>
          <td>{{ detailsInfo.subjectBack | noData }}</td>
        </tr>
      </table>
    </basic-card>
    <basic-card :is-title="false" class="m-b-10 m-t-10 p-t-24">
      <div class="font-size-14 m-b-16">开票信息</div>
      <table
        class="w100 table"
        border="1"
        Cellspacing="0"
        Cellpadding="0"
        bordercolor="#dcdcdc"
      >
        <tr align="left">
          <th>发票类型</th>
          <td>
            {{
              detailsInfo.ticketType === 1
                ? '普通电子发票'
                : '专用发票' | noData
            }}
          </td>
          <th>发票状态</th>
          <td>
            {{
              detailsInfo.ticketStatus == 1
                ? '待开票'
                : detailsInfo.ticketStatus == 2
                ? '已退回'
                : '已开票' | noData
            }}
          </td>
        </tr>
        <tr align="left">
          <th>合计金额</th>
          <td>{{ NumFormat(detailsInfo.ticketFee) | noData }}</td>
          <th>合计税额</th>
          <td>{{ NumFormat(detailsInfo.ticketTax) | noData }}</td>
        </tr>
        <tr align="left">
          <th>价税合计</th>
          <td>
            {{ NumFormat(detailsInfo.ticketPay) | noData }}
          </td>
          <th>发票号</th>
          <td>{{ detailsInfo.ticketCode | noData }}</td>
        </tr>
        <tr align="left">
          <th>发票备注</th>
          <!--          去除td边框线-->

          <td class="border-none">{{ detailsInfo.comment | noData }}</td>
          <!--          <th>开户行及银行账户</th>-->
          <!--          <td>{{ '微商银行合肥高新开发区支行2081012080038328' | noData }}</td>-->
        </tr>
      </table>
    </basic-card>
    <basic-card :is-title="false" class="p-t-24">
      <div class="font-size-14 m-b-16">发票条目</div>
      <drive-table
        ref="drive-table"
        height="204"
        :columns="tableColumn"
        :table-data="tableList"
      >
      </drive-table>
      <div class="m-t-20 m-b-20" v-if="detailsInfo.ticketStatus === 2">
        <span class="info m-r-8 font-size-14">退回原因</span>
        <span class="font-size-14">{{
          detailsInfo.returnReason | noData
        }}</span>
      </div>
      <div v-if="detailsInfo.ticketStatus === 2">
        <span class="info m-r-8 font-size-14">退回时间</span>
        <span class="font-size-14">{{ detailsInfo.checkTime | noData }}</span>
      </div>
    </basic-card>

    <!--退回弹框-->
    <dialog-cmp
      title="退回开票申请"
      :visible.sync="sendBackVisible"
      width="26%"
      @confirmDialog="sendBackDialog"
    >
      <driven-form
        label-position="top"
        ref="drive-form-sendBack"
        v-model="fromModelSendBack"
        :formConfigure="formConfigureSendBack"
      />
    </dialog-cmp>

    <!--确认开票-->
    <dialog-cmp
      title="确认开票"
      :visible.sync="drawbillVisible"
      width="26%"
      @confirmDialog="drawbillDialog"
    >
      <driven-form
        label-position="top"
        ref="drive-form-drawbill"
        v-model="fromModelDrawbill"
        :formConfigure="formConfigureDrawbill"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import formConfigureData from './descriptor'
// import downloads from '@/utils/download'
// import { getContractReceivables } from './api'
import { ticketDetails, returnExamine } from './api'
export default {
  name: 'DrawBillDetails',
  mixins: [ColumnMixins, formConfigureData],
  data() {
    return {
      NumFormat,
      // getContractReceivables,
      drawbillVisible: false,
      sendBackVisible: false,
      fromModelDrawbill: {
        rentFreePeriod: 1,
        ticketType: ''
      },
      fromModelSendBack: {},
      detailsInfo: {},
      tableList: []
    }
  },
  mounted() {
    const { id } = this.$route.query
    this.ticketDetails(id)
  },
  methods: {
    drawbillClick() {
      this.fromModelDrawbill.ticketType = this.detailsInfo.ticketType
      this.drawbillVisible = true
    },
    ticketDetails(e) {
      ticketDetails({ id: e }).then(res => {
        console.log(res)
        this.detailsInfo = res
        let data = {
          feeType:
            res.feeType === 1
              ? '房租费'
              : res.feeType === 2
              ? '保证金'
              : '服务费',
          taxRate: res.taxRate,
          ticketFee: res.ticketFee,
          ticketTax: res.ticketTax
        }
        this.tableList = [data]
      })
    },
    drawbillDialog() {
      this.$refs['drive-form-drawbill'].validate(valid => {
        if (valid) {
          console.log(this.fromModelDrawbill)
          let data = {
            ...this.fromModelDrawbill
          }
          data.attachIds = this.fromModelDrawbill.attachIds.map(item => {
            return item.id || []
          })
          data.ticketStatus = 3
          data.id = this.detailsInfo.id
          returnExamine(data).then(() => {
            this.$toast.success('提交成功')
            setTimeout(() => {
              this.$router.push('/drawBill/drawBillList')
            }, 1500)
          })
        }
      })
    },
    sendBackDialog() {
      this.$refs['drive-form-sendBack'].validate(valid => {
        if (valid) {
          console.log(333)
          let data = {
            ...this.fromModelSendBack
          }
          data.attachIds = this.fromModelSendBack.attachIds.map(item => {
            return item.id || []
          })
          data.ticketStatus = 2
          data.id = this.detailsInfo.id
          console.log(this.fromModelSendBack)
          returnExamine(data).then(() => {
            this.$toast.success('退回成功')
            setTimeout(() => {
              this.$router.push('/drawBill/drawBillList')
            }, 1500)
          })
        }
      })
      console.log('ss')
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item--small.el-form-item) {
  margin-bottom: 0 !important;
}

.info {
  color: #999;
}
//修改table边框颜色

.table {
  border-radius: 3px 3px 3px 3px;
  tr {
    line-height: 50px;
    th {
      width: 12%;
      padding-left: 26px;
      font-size: 14px;
      background-color: #f3f3f3;
      color: #979797;
      letter-spacing: 1px;
    }
    td {
      width: 30%;
      padding-left: 26px;
      font-size: 14px;
      letter-spacing: 1px;
    }
  }
}

.border-none {
  border: none;
}
</style>
