import request from '@/utils/request'

export function getContractReceivables(params) {
  return request({
    url: `/contract/parkFindContractFee/list`,
    method: 'get',
    isTable: true,
    type: 'finance',
    params
  })
}

// 统计信息/admin-api
export function applyCount() {
  return request({
    url: '/ticket/apply/count',
    method: 'get'
  })
}

// 发票申请列表
export function ticketApplyPagebyent(params) {
  return request({
    url: '/ticket/apply/page_by_ent',
    method: 'get',
    params
  })
}

// 发票申请列表
export function ticketApplyPagebyents(params) {
  return request({
    url: '/ticket/apply/page_by_ticket',
    method: 'get',
    params
  })
}

// 获取所有园区
export function getPark() {
  return request({
    url: `/housing/park/listAll`,
    method: 'get'
  })
}
