import { NumFormat } from '@/utils/tools'
import {
  // ticketTypeStatus,
  statusType,
  feeTypeTypeStatus
} from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '企业名称',
          width: 300,
          prop: 'entName',
          render: (h, scope) => {
            return (
              <div>
                <el-tooltip
                  className="item"
                  effect="dark"
                  content={scope.row.entName}
                  placement="top"
                >
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.goDetail(scope.row)
                    }}
                    style="width:auto;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display: inline-block;"
                  >
                    {scope.row.entName}
                  </el-link>
                </el-tooltip>
              </div>
            )
          }
        },
        {
          label: '企业缴款账号',
          prop: 'billCode'
        },
        {
          label: '对方银行',
          prop: 'ticketType',
          render: (h, scope) => {
            return <div>{feeTypeTypeStatus(h, scope.row.ticketType)}</div>
          }
        },
        {
          label: '对方账号 ',
          prop: 'feeType',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.feeType)}</div>
            )
          }
        },
        {
          label: '对方户名',
          prop: 'ticketPay',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.ticketPay)}
              </div>
            )
          }
        },
        {
          label: '交易金额(元)',
          prop: 'ticketTax',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.ticketTax)}
              </div>
            )
          }
        },
        {
          label: '银行流水号',
          prop: 'ticketStatus',
          render: (h, scope) => {
            return <div>{statusType(h, scope.row.ticketStatus)}</div>
          }
        },
        {
          label: '交易时间',
          prop: 'ticketStatus'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.previewEvent(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.LEADING_OUT}
                >
                  {this.routeButtonsTitle.LEADING_OUT}
                </el-link>
              </div>
            )
          }
        }
      ],
      //  按企业
      tableColumnFirm: [
        {
          label: '总笔数',
          prop: 'totalCount'
        },
        {
          label: '总金额(元)',
          prop: 'totalFee',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.totalFee)}</div>
            )
          }
        },
        {
          label: '不平账笔数',
          prop: 'feeType'
        },
        {
          label: '不平账金额(元)',
          prop: 'totalFee',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.totalFee)}</div>
            )
          }
        },
        {
          label: '对账时间',
          prop: 'createTime'
        },
        {
          label: '对账结果',
          prop: 'createTime'
        },
        {
          label: '归集结果',
          prop: 'createTime'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.previewEvent(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DETAIL}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
              </div>
            )
          }
        }
      ],
      //  账户核销缴费明细
      tableColumnAccount: [
        {
          label: '企业名称',
          width: 120,
          prop: 'entName',
          render: (h, scope) => {
            return (
              <div>
                <el-tooltip
                  className="item"
                  effect="dark"
                  content={scope.row.entName}
                  placement="top"
                >
                  <el-link
                    type="primary"
                    onClick={() => {
                      this.goDetail(scope.row)
                    }}
                    style="width:auto;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display: inline-block;"
                  >
                    {scope.row.entName}
                  </el-link>
                </el-tooltip>
              </div>
            )
          }
        },
        {
          label: '交易金额',
          prop: 'totalFee',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.totalFee)}</div>
            )
          }
        },
        {
          label: '交易时间',
          prop: 'createTime'
        },
        {
          label: '账单编号',
          prop: 'totalCount'
        },
        {
          label: '缴费账号',
          prop: 'totalCount'
        },
        {
          label: '核销方式',
          prop: 'totalCount'
        },
        {
          label: '收款识别号',
          prop: 'totalCount'
        },
        {
          label: '交易状态',
          prop: 'totalCount'
        },
        {
          label: '平账状态',
          prop: 'totalCount'
        },
        {
          label: '归集状态',
          prop: 'totalCount'
        }
      ],
      //  资金归集缴费明细
      tableColumnPoolingFundsl: [
        {
          label: '园区',
          prop: 'totalFee'
        },
        {
          label: '账户类型',
          prop: 'totalCount'
        },
        {
          label: '账号',
          prop: 'totalCount'
        },
        {
          label: '费用期间',
          prop: 'totalCount'
        },
        {
          label: '归集时间',
          prop: 'totalCount'
        },
        {
          label: '归集金额',
          prop: 'totalCount'
        },
        {
          label: '操作类型',
          prop: 'totalCount'
        },
        {
          label: '归集状态',
          prop: 'totalCount'
        }
      ]
    }
  }
}
