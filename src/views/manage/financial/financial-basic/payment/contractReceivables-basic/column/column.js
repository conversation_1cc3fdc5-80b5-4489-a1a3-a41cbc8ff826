import { NumFormat } from '@/utils/tools'
// import {
  // contractTypeOptions,
  // contractStatusOptions,
  //  getContractType
  // getContractStatusReceivable
// } from '@/views/manage/house/contract/contract-basic/utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '合同编号',
          minWidth: 230,
          prop: 'contractNo'
        },
        {
          label: '企业名称',
          minWidth: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                <div
                  onClick={() => {
                    this.goDetail(scope.row)
                  }}
                  class={'line-1'}
                  class={`${
                    scope.row?.enterStatus === 1 || scope.row?.enterStatus === 2
                      ? 'color-primary pointer'
                      : ''
                  }`}
                >
                  {scope.row.enterpriseName}
                  {scope.row.enterpriseNameTxt ? (
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                      placement="top"
                    >
                      <svg-icon class="m-l-8" icon-class="link-m" />
                    </el-tooltip>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            )
          }
        },
        {
          label: '起始日期',
          width: 120,
          prop: 'startTime'
        },
        {
          label: '结束日期',
          width: 120,
          prop: 'endTime'
        },
        {
          label: '合同总面积(㎡)',
          width: 120,
          prop: 'roomArea'
        },
        {
          label: '合同总金额(元)',
          prop: 'totalAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.totalAmount)}
              </div>
            )
          }
        },
        {
          label: '应收总金额(元)',
          prop: 'amount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '核销总金额(元)',
          prop: 'written',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.written)}</div>
            )
          }
        },
        {
          label: '退款总金额(元)',
          prop: 'refund',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.refund)}</div>
            )
          }
        },
        {
          label: '实收总金额(元)',
          prop: 'paidAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '未出账期数',
          width: 120,
          prop: 'unBilled'
        },
        {
          label: '已出账期数',
          width: 120,
          prop: 'billed'
        },
        {
          label: '合同状态',
          prop: 'status',
          width: 120,
          render: (h, scope) => {
            const obj = {
              1: 'primary',
              2: 'warning',
              3: 'info',
              4: 'primary',
              5: 'warning',
              6: 'danger',
              7: 'danger',
              8: 'danger',
              9: 'danger',
              10: 'danger'
            }
            return <basic-tag isDot type={obj[scope.row.status]} label={scope.row.statusStr} />
          }
        }
        // {
        //   label: '应收进度',
        //   prop: 'returnMoney',
        //   width: 200,
        //   render: (h, scope) => {
        //     return (
        //       <div>
        //         <el-progress
        //           text-inside={true}
        //           stroke-width={16}
        //           percentage={
        //             Number(
        //               ((scope.row.paidAmount / scope.row.amount) * 100).toFixed(
        //                 2
        //               )
        //             ) || 0
        //           }
        //           status="success"
        //         ></el-progress>
        //       </div>
        //     )
        //   }
        // },
      ],
      //  按企业
      tableColumnFirm: [
        {
          label: '企业名称',
          minWidth: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                <div
                  onClick={() => {
                    this.goDetail(scope.row)
                  }}
                  class={'line-1'}
                  class={`${
                    scope.row?.enterStatus === 1 || scope.row?.enterStatus === 2
                      ? 'color-primary pointer'
                      : ''
                  }`}
                >
                  {scope.row.enterpriseName}
                  {scope.row.enterpriseNameTxt ? (
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                      placement="top"
                    >
                      <svg-icon class="m-l-8" icon-class="link-m" />
                    </el-tooltip>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            )
          }
        },
        {
          label: '执行中合同(份)',
          width: 120,
          prop: 'executingCount'
        },
        {
          label: '已结束合同(份)',
          width: 120,
          prop: 'endedCount'
        },
        {
          label: '合同总数(份)',
          width: 120,
          prop: 'count'
        },
        {
          label: '合同总面积(㎡)',
          width: 120,
          prop: 'roomTotalArea'
        },
        {
          label: '当前租赁面积(㎡)',
          width: 140,
          prop: 'roomArea'
        },
        {
          label: '合同总金额(元)',
          width: 120,
          prop: 'totalAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.totalAmount)}
              </div>
            )
          }
        },
        {
          label: '应收总金额(元)',
          width: 120,
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '核销总金额(元)',
          prop: 'written',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.written)}</div>
            )
          }
        },
        {
          label: '退款总金额(元)',
          prop: 'refund',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.refund)}</div>
            )
          }
        },
        {
          label: '实收总金额(元)',
          width: 120,
          prop: 'paidAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '未出账期数',
          width: 120,
          prop: 'unBilled'
        },
        {
          label: '已出账期数',
          width: 120,
          prop: 'billed'
        }
        // {
        //   label: '应收进度',
        //   prop: 'returnMoney',
        //   width: 200,
        //   render: (h, scope) => {
        //     return (
        //       <div>
        //         <el-progress
        //           text-inside={true}
        //           stroke-width={16}
        //           percentage={
        //             Number(
        //               ((scope.row.paidAmount / scope.row.amount) * 100).toFixed(
        //                 2
        //               )
        //             ) || 0
        //           }
        //           status="success"
        //         ></el-progress>
        //       </div>
        //     )
        //   }
        // }
      ]
    }
  }
}
