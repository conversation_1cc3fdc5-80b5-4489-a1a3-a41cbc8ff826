import { NumFormat } from '@/utils/tools'
import { noData } from '@/filter'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '主体名称',
          prop: 'bodyName',
          minWidth: 180,
          search: {
            type: 'input'
          },
          render: (h, scope) => {
            return (
              <div>
                {scope.row.invHeader === 1 ? (
                  <el-link
                    type={'primary'}
                    onClick={() => {
                      this.goEntDetail(scope.row)
                    }}
                  >
                    【企业】{scope.row.bodyName}
                  </el-link>
                ) : (
                  <span>【个人】{scope.row.bodyName}</span>
                )}
              </div>
            )
          }
        },
        {
          label: '发票类型',
          prop: 'invoiceType',
          width: 140,
          render: (h, scope) => {
            return <span>{scope.row.invoiceTypeStr}</span>
          },
          search: {
            type: 'select',
            options: []
          }
        },
        {
          label: '所属园区',
          minWidth: 150,
          prop: 'parkId',
          search: {
            type: 'select',
            options: []
          },
          render: (h, scope) => {
            return <span>{noData(scope.row.parkName)}</span>
          }
        },
        {
          label: '申请时间',
          prop: 'applyTime',
          width: 140,
          search: {
            type: 'daterange'
          }
        },
        {
          label: '申请人',
          prop: 'creatorStr'
        },
        {
          label: '金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>{NumFormat(scope.row.amount)}</span>
            )
          }
        },
        {
          label: '税额(元)',
          prop: 'taxAmount',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.taxAmount)}
              </span>
            )
          }
        },
        {
          label: '价税合计(元)',
          prop: 'sumAmount',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.sumAmount)}
              </span>
            )
          }
        },
        {
          label: '申请端',
          prop: 'userTypeStr'
        },
        {
          label: '审核状态',
          prop: 'examineStatusStr',
          render: (h, scope) => {
            const obj = {
              1: 'warning',
              2: 'success',
              3: 'danger'
            }
            return (
              <el-tag type={obj[scope.row.examineStatus]}>
                {scope.row.examineStatusStr}
              </el-tag>
            )
          }
        },
        {
          label: '开票状态',
          prop: 'openStatus',
          width: 100,
          search: {
            type: 'select',
            options: []
          },
          render: (h, scope) => {
            const obj = {
              1: 'warning',
              2: 'primary',
              3: 'success',
              4: 'danger',
              5: 'info'
            }
            return (
              <el-tag type={obj[scope.row.openStatus]}>
                {scope.row.openStatusStr}
              </el-tag>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.DETAIL}
                  type="primary"
                  onclick={() => {
                    this.detailHandle(scope.row)
                  }}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
                {scope.row.openStatus === 3 && (
                  <el-link
                    v-permission={this.routeButtonsPermission.DOWNLOAD_INVOICE}
                    class={'m-l-8'}
                    type="primary"
                    onclick={() => {
                      this.downloadHandle(scope.row)
                    }}
                  >
                    {this.routeButtonsTitle.DOWNLOAD_INVOICE}
                  </el-link>
                )}
                {scope.row.examineStatus === 3 && (
                  <el-link
                    v-permission={this.routeButtonsPermission.EDIT}
                    class={'m-l-8'}
                    type="primary"
                    onclick={() => {
                      this.editHandle(scope.row)
                    }}
                  >
                    {this.routeButtonsTitle.EDIT}
                  </el-link>
                )}
              </div>
            )
          }
        }
      ]
    }
  }
}
