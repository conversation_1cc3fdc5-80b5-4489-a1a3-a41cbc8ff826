<template>
  <basic-drawer
    size="760px"
    title="操作历史"
    :visible.sync="drawerVisible"
    @confirmDrawer="confirmDrawer"
  >
    <div class="detail-history bg-white m-t-8">
      <div class="flex" v-for="(item, index) in detailHistory" :key="index">
        <div class="m-t-8">
          <div class="isDone" :class="item.point ? 'active' : ''"></div>
          <div
            class="isLine"
            :class="item.point ? 'activeLine' : ''"
            v-if="!(detailHistory.length === index + 1)"
          ></div>
        </div>
        <div class="m-l-16 text-s font-size-14 line-height-22" style="flex: 1">
          <div class="flex justify-content-between">
            <div
              class="text-n font-size-16 m-b-16 line-height-24"
              :class="item.point ? 'active-text' : ''"
            >
              {{ item.opTypeStr }}
            </div>
          </div>
          <div class="m-b-8 m-r-8" style="word-wrap: normal">
            操作人：{{ item.deptName }}-{{ item.opUser }} /
            {{ item.createTime }}
          </div>
          <div class="m-b-8" style="word-break: break-all">
            备注：{{ item.content | noData }}
          </div>
          <div
            class="m-b-8 flex"
            v-if="getFilesList(item) && getFilesList(item).length > 0"
          >
            <span style="flex-shrink: 0">附件：</span>
            <files-list :files="getFilesList(item)" onlyForView />
          </div>
        </div>
      </div>
    </div>
  </basic-drawer>
</template>

<script>
import FilesList from '@/components/Uploader/files'
export default {
  name: 'HistoryRecord',
  components: { FilesList },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    detailHistory: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerVisible: false
    }
  },
  watch: {
    visible(val) {
      this.drawerVisible = val
    },
    drawerVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    getFilesList(row) {
      const examineAttach = row.examineAttach || {}
      return examineAttach.invoice || []
    },
    confirmDrawer() {
      this.drawerVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-history {
  .text-s {
    color: rgba(0, 0, 0, 0.6);
  }
  .text-n {
    color: rgba(0, 0, 0, 0.9);
  }
  .isDone {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    opacity: 1;
    border: 2px solid #c5c5c5;
  }
  .isLine {
    width: 2px;
    height: 84px;
    margin-left: 3px;
    margin-top: 6px;
    background-color: #c5c5c5;
  }
  .active {
    border: 2px solid;
    @include background_color(--color-primary);
    @include border_color(--color-primary);
  }
  .activeLine {
    @include background_color(--color-primary);
  }
  .active-text {
    @include font_color(--color-primary);
  }
}
</style>
