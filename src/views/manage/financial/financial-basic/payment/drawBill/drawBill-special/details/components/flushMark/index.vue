<template>
  <dialog-cmp
    title="发票冲红标记"
    :visible.sync="dialogVisible"
    @confirmDialog="confirmDialog"
    width="40%"
  >
    <div class="tips-wrapper p-8 color-text-secondary line-height-18">
      <svg-icon icon-class="info-circle-filled" />
      <span
        >该冲红操作只做业务标记，不实际对发票进行冲红，请在开票系统冲红完成后再进行本次标记操作。标记冲红后该发票对应的账单剩余可开票金额将回滚。</span
      >
    </div>
    <driven-form
      v-if="dialogVisible"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
      label-position="top"
    />
  </dialog-cmp>
</template>

<script>
import DescriptorMixin from './descriptor'
import { invApplyRecordDestroy } from '../../../api'

export default {
  name: 'FlushMark',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      dialogVisible: false,
      fromModel: {}
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  methods: {
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel,
          applyId: this.$route.query.id
        }
        if (this.fromModel.attachIds && this.fromModel.attachIds.length) {
          params.attachIds = this.fromModel.attachIds.map(item => item.id)
        }
        invApplyRecordDestroy(params).then(() => {
          this.$emit('updateHandle')
          this.$toast.success('操作成功')
          this.dialogVisible = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.tips-wrapper {
  @include background_color(--background-color-regular);
}
:deep(.custom-tips) {
  margin-left: 0 !important;
}
</style>
