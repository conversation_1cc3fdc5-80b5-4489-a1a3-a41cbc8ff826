<template>
  <div>
    <!-- 头部 -->
    <div class="bg-white p-20">
      <breadcrumb />
      <div class="flex justify-content-between m-t-8">
        <div class="flex align-items-center">
          <span class="font-strong font-size-20">{{
            detailInfo.bodyName
          }}</span>
          <el-tag
            class="m-l-8"
            style="border-radius: 435px"
            :type="examineType"
            >{{ detailInfo.examineStatusStr }}</el-tag
          >
          <el-tag class="m-l-8" style="border-radius: 435px" :type="openType">{{
            detailInfo.openStatusStr
          }}</el-tag>
          <el-tooltip
            effect="dark"
            :content="detailInfo.failReason"
            placement="bottom"
          >
            <svg-icon
              v-if="
                detailInfo.openStatus === 4 && detailInfo.examineStatus === 2
              "
              icon-class="error-circle-filled"
              class-name="color-danger m-l-4"
              style="margin-top: -2px"
            />
          </el-tooltip>
        </div>
        <div>
          <el-button
            v-if="detailInfo.examineList && detailInfo.examineList.length"
            type="info"
            @click="historyVisible = true"
            >操作历史</el-button
          >
          <template v-if="detailInfo.examineStatus === 1">
            <el-button
              v-permission="routeButtonsPermission.WITHDRAW"
              type="info"
              @click="refundVisible = true"
            >
              {{ routeButtonsTitle.WITHDRAW }}
            </el-button>
            <el-button
              v-permission="routeButtonsPermission.CONFIRM_INVOICING"
              type="primary"
              @click="confirmBillVisible = true"
            >
              {{ routeButtonsTitle.CONFIRM_INVOICING }}
            </el-button>
          </template>
          <template v-if="detailInfo.openStatus === 3">
            <template v-if="detailInfo.operateType === 1">
              <el-button
                v-permission="routeButtonsPermission.DOWNLOAD_RESEND"
                type="primary"
                @click="downloadRetryVisible = true"
              >
                {{ routeButtonsTitle.DOWNLOAD_RESEND }}
              </el-button>
            </template>
            <el-button
              v-permission="routeButtonsPermission.FLUSH_MARK"
              type="primary"
              @click="flushMarkVisible = true"
            >
              {{ routeButtonsTitle.FLUSH_MARK }}
            </el-button>
          </template>
          <template
            v-if="
              detailInfo.openStatus === 4 &&
              (detailInfo.reqStatus === '22' || detailInfo.reqStatus === '24')
            "
          >
            <el-button
              v-permission="routeButtonsPermission.ANEW_SUBMIT"
              type="primary"
              @click="anewSubmitHandle"
            >
              {{ routeButtonsTitle.ANEW_SUBMIT }}
            </el-button>
          </template>
        </div>
      </div>
      <div class="m-t-16 font-size-14 flex">
        <div>
          <span class="color-text-secondary">所属园区：</span>
          <span class="color-text-primary">{{
            detailInfo.parkName | noData
          }}</span>
        </div>
        <div class="m-l-32">
          <span class="color-text-secondary">申请人：</span>
          <span class="color-text-primary">{{ detailInfo.applyUser }}</span>
        </div>
        <template v-if="detailInfo.operateType === 1">
          <div class="m-l-32" v-if="detailInfo.openStatus === 2">
            <span class="color-text-secondary">等待航信开票完成</span>
          </div>
        </template>
        <template v-else>
          <div class="m-l-32" v-if="detailInfo.openStatus === 2">
            <span class="color-text-secondary"
              >需手动上传电子票据或通知发票已开线下领取</span
            >
          </div>
        </template>
      </div>
    </div>
    <!-- 发票信息 -->
    <div class="bg-white p-20 m-t-8">
      <div class="flex justify-content-between">
        <div class="font-size-14 line-height-22 color-text-primary">
          发票信息
        </div>
        <template
          v-if="
            detailInfo.examineStatus === 1 || detailInfo.examineStatus === 3
          "
        >
          <el-button v-if="formView" type="primary" @click="formView = false"
            >票样视图</el-button
          >
          <el-button v-else type="primary" @click="formView = true"
            >表单视图</el-button
          >
        </template>
        <template v-if="detailInfo.operateType === 2">
          <div class="m-l-32" v-if="detailInfo.openStatus === 2">
            <el-button
              v-permission="routeButtonsPermission.UPLOAD_TICKET"
              type="primary"
              @click="uploadTicketVisible = true"
            >
              {{ routeButtonsTitle.UPLOAD_TICKET }}
            </el-button>
          </div>
        </template>
      </div>
      <template v-if="formView">
        <div class="m-t-8">
          <el-descriptions class="descriptions-wrapper" :column="1" border>
            <el-descriptions-item label="发票类型">
              <span>{{
                detailInfo.openType === 1 ? '普通发票' : '专用发票'
              }}</span>
              （<span>{{ detailInfo.kind === 1 ? '电子' : '纸质' }}</span
              >）
              <template
                v-if="
                  detailInfo.openStatus === 3 && detailInfo.operateType === 2
                "
              >
                <span v-if="detailInfo.offlineFlag" class="color-primary"
                  >线下领取</span
                >
                <span
                  v-else
                  class="color-primary pointer"
                  @click="downloadHandInvoice"
                  >下载发票</span
                >
              </template>
            </el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="descriptions-title" :column="1" border>
            <el-descriptions-item>抬头信息</el-descriptions-item>
          </el-descriptions>
          <el-descriptions class="descriptions-wrapper" :column="2" border>
            <el-descriptions-item label="购买方名称">{{
              detailInfo.bodyName
            }}</el-descriptions-item>
            <el-descriptions-item label="税号">{{
              detailInfo.buyerCode
            }}</el-descriptions-item>
            <el-descriptions-item label="地址、电话">
              <template
                v-if="detailInfo.buyerAddress || detailInfo.buyerContact"
              >
                {{ detailInfo.buyerAddress }} {{ detailInfo.buyerContact }}
              </template>
              <span v-else>暂无数据</span>
            </el-descriptions-item>
            <el-descriptions-item label="开户行及账号">
              <template
                v-if="detailInfo.buyerBankName || detailInfo.buyerBankNo"
              >
                {{ detailInfo.buyerBankName }} {{ detailInfo.buyerBankNo }}
              </template>
              <span v-else>暂无数据</span>
            </el-descriptions-item>
          </el-descriptions>
        </div>
        <div class="m-t-16">
          <div
            class="flex align-items-center font-size-14 line-height-22 p-l-4"
          >
            <div>
              <span>税额总计</span>
              <span class="m-l-8 color-warning">
                {{ `¥: ${NumFormat(detailInfo.sumTax || 0)}` }} 元
              </span>
            </div>
            <div class="m-l-32">
              <span>金额总计</span>
              <span class="m-l-8 color-warning">
                {{ `¥: ${NumFormat(detailInfo.sumAmount || 0)}` }} 元
              </span>
            </div>
            <div class="m-l-32">
              <span>价税合计(大写)</span>
              <span class="m-l-8 color-warning">
                {{ detailInfo.sumTaxAmountStr }}
              </span>
            </div>
            <div class="m-l-32">
              <span>价税合计(小写)</span>
              <span class="m-l-8 color-warning">
                {{ `￥: ${NumFormat(detailInfo.sumTaxAmount || 0)}` }} 元
              </span>
            </div>
          </div>
          <drive-table
            class="m-t-8"
            :columns="tableColumn"
            :table-data="detailInfo.invoiceList || []"
          />
        </div>
        <!-- 备注信息 -->
        <div class="m-t-24">
          <div class="font-size-14 line-height-22 color-text-primary">
            备注信息
          </div>
          <div class="font-size-14 line-height-22 color-text-secondary m-t-8">
            {{ detailInfo.invRemark | noData }}
          </div>
        </div>
      </template>
      <invoice-preview v-else class="invoice-preview" :info="detailInfo" />
    </div>
    <!-- 非合同主题开票证明材料 -->
    <div class="bg-white p-20 m-t-8">
      <div class="font-size-14 line-height-22 color-text-primary m-b-8">
        非合同主题开票证明材料
      </div>
      <files-list
        v-if="filesList && filesList.length > 0"
        :files="filesList"
        onlyForView
      />
      <div v-else class="color-text-secondary font-size-14 line-height-22">
        暂无数据
      </div>
    </div>
    <!-- 关联账单 -->
    <div class="bg-white p-20 m-t-8">
      <div class="font-size-14 line-height-22 color-text-primary">关联账单</div>
      <drive-table
        class="m-t-8"
        :columns="billTableColumn"
        :table-data="detailInfo.relationBillList || []"
      />
    </div>
    <!-- 退回弹窗 -->
    <refund-dialog :visible.sync="refundVisible" @updateHandle="updateHandle" />
    <!-- 确认开票弹窗 -->
    <confirm-bill
      :visible.sync="confirmBillVisible"
      @updateHandle="updateHandle"
    />
    <!-- 上传票据信息弹窗 -->
    <upload-ticket
      :visible.sync="uploadTicketVisible"
      @updateHandle="updateHandle"
    />
    <!-- 冲红标记 -->
    <flush-mark :visible.sync="flushMarkVisible" @updateHandle="updateHandle" />
    <!-- 操作历史 -->
    <history-record
      :visible.sync="historyVisible"
      :detail-history="detailInfo.examineList || []"
    />
    <!-- 下载和重发 -->
    <download-retry :visible.sync="downloadRetryVisible" :info="detailInfo" />
  </div>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb'
import { numberToChinese, NumFormat } from '@/utils/tools'
import ColumnMixin from './column'
import FilesList from '@/components/Uploader/files'
import RefundDialog from './components/refundDialog'
import ConfirmBill from './components/confirmBill'
import { invApplyRecordGet, invApplyRecordReTry } from '../api'
import InvoicePreview from '../components/InvoicePreview'
import UploadTicket from './components/uploadTicket'
import FlushMark from './components/flushMark'
import HistoryRecord from './components/historyRecord'
import downloads from '@/utils/download'
import DownloadRetry from './components/downloadRetry'

export default {
  name: 'DrawBillDetails',
  components: {
    DownloadRetry,
    HistoryRecord,
    FlushMark,
    UploadTicket,
    InvoicePreview,
    ConfirmBill,
    RefundDialog,
    FilesList,
    Breadcrumb
  },
  mixins: [ColumnMixin],
  data() {
    return {
      formView: true,
      NumFormat,
      numberToChinese,
      filesList: [],
      refundVisible: false,
      confirmBillVisible: false,
      uploadTicketVisible: false,
      historyVisible: false,
      flushMarkVisible: false,
      downloadRetryVisible: false,
      detailInfo: {}
    }
  },
  computed: {
    openType() {
      const obj = {
        1: 'warning',
        2: 'primary',
        3: 'success',
        4: 'danger',
        5: 'info'
      }
      return obj[this.detailInfo.openStatus]
    },
    examineType() {
      const obj = {
        1: 'warning',
        2: 'success',
        3: 'danger'
      }
      return obj[this.detailInfo.examineStatus]
    }
  },
  created() {
    this.invApplyRecordGet()
  },
  methods: {
    // 重新提交
    anewSubmitHandle() {
      invApplyRecordReTry({ applyId: this.detailInfo.id }).then(() => {
        this.$toast.success('提交成功')
        this.updateHandle()
      })
    },
    // 下载手动上传电子发票
    downloadHandInvoice() {
      const invoiceAttach = this.detailInfo.invoiceAttach || {}
      const list = invoiceAttach.invoice || []
      if (list && list.length) {
        downloads.addressDownload(list[0])
      } else {
        this.$toast.warning('暂无发票')
      }
    },
    updateHandle() {
      this.invApplyRecordGet()
    },
    billDetailHandle(row) {
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id: row.totalId,
          type: row.feeType
        }
      })
    },
    invApplyRecordGet() {
      invApplyRecordGet({ id: this.$route.query.id }).then(res => {
        this.detailInfo = res || {}
        this.detailInfo.billList = this.detailInfo.invoiceList || []
        const attachMap = this.detailInfo.attachMap || {}
        this.filesList = attachMap.invoice || []
      })
    }
  }
}
</script>

<style scoped lang="scss">
.invoice-preview {
  width: 953px;
  margin: 16px auto 0;
}
:deep(.descriptions-wrapper) {
  .el-descriptions-item__label {
    width: 120px;
  }
}
:deep(.descriptions-title) {
  margin-top: -1px;
  margin-bottom: -1px;
  .el-descriptions-item__label {
    display: none;
  }
}
:deep(.el-descriptions-item__cell) {
  font-size: 14px;
}
</style>
