<template>
  <div>
    <div class="title">开票信息</div>
    <div class="flex align-items-center font-size-14 line-height-22">
      <div>
        <span>税额总计</span>
        <span class="m-l-8 color-warning">
          {{ `￥: ${NumFormat(this.taxAmountTotal)}` }} 元
        </span>
      </div>
      <div class="m-l-32">
        <span>金额总计</span>
        <span class="m-l-8 color-warning">
          {{ `￥: ${NumFormat(this.paidAmountTotal)}` }} 元
        </span>
      </div>
      <div class="m-l-32">
        <span>价税合计(大写)</span>
        <span class="m-l-8 color-warning">
          {{ numberToChinese(this.openAmountTotal) }}
        </span>
      </div>
      <div class="m-l-32">
        <span>价税合计(小写)</span>
        <span class="m-l-8 color-warning">
          {{ `￥: ${NumFormat(this.openAmountTotal)}` }} 元
        </span>
      </div>
    </div>
    <el-form :model="formModel" :rules="rules" ref="ruleForm">
      <el-table class="m-t-16" border :data="formModel.tableData" stripe>
        <el-table-column prop="feeTypeStr" label="项目" min-width="100">
        </el-table-column>
        <el-table-column
          prop="residueAmount"
          label="剩余可开票金额(元)"
          width="150"
        >
          <template slot-scope="scope">
            <span class="color-warning">{{
              NumFormat(scope.row.residueAmount)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="feeCycle" label="本期开票周期" min-width="240">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.feeCycle'"
              :rules="rules.feeCycle"
            >
              <el-date-picker
                v-model="scope.row.feeCycle"
                type="daterange"
                range-separator="至"
                start-placeholder="起始时间"
                end-placeholder="结束时间"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          prop="openAmount"
          label="本期开票金额(含税)"
          width="150"
        >
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.openAmount'"
              :rules="rules.openAmount"
            >
              <el-input
                v-decimal
                v-model="scope.row.openAmount"
                placeholder="请输入"
                :maxLength="moneyLength"
                @input="getTaxAmount(scope.row)"
              >
                <span slot="append" class="color-text-primary">元</span>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="invModel" label="规格型号" width="180">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.invModel'">
              <el-select
                class="w100"
                v-model="scope.row.invModel"
                placeholder="请选择"
                clearable
                filterable
                allow-create
              >
                <el-option
                  v-for="(item, index) in scope.row.list"
                  :key="index"
                  :label="item.invModel"
                  :value="item.invModel"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="invUnit" label="单位" width="150">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.invUnit'">
              <el-select
                class="w100"
                v-model="scope.row.invUnit"
                placeholder="请选择"
                clearable
                filterable
                allow-create
              >
                <el-option
                  v-for="(item, index) in scope.row.list"
                  :key="index"
                  :label="item.invUnit"
                  :value="item.invUnit"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" width="150">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.count'"
              :rules="rules.count"
            >
              <el-input
                v-model="scope.row.count"
                placeholder="请输入"
                :maxlength="10"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价(元)" width="150">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.price'"
              :rules="rules.price"
            >
              <el-select
                class="w100"
                v-model="scope.row.price"
                placeholder="请选择"
                clearable
                filterable
                allow-create
              >
                <el-option
                  v-for="(item, index) in scope.row.list"
                  :key="index"
                  :label="item.price"
                  :value="item.price"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="taxPercent" label="税率" width="180">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.taxPercent'"
              :rules="rules.taxPercent"
            >
              <el-select
                class="w100"
                v-model="scope.row.taxPercent"
                placeholder="请选择"
                @change="getTaxPercent($event, scope.row, scope.$index)"
              >
                <el-option
                  v-for="(item, index) in scope.row.list"
                  :key="index"
                  :label="item.freeTax ? '免税' : item.taxPercent + '%'"
                  :value="item.freeTax ? item.freeTax : item.taxPercent"
                ></el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="taxAmount" label="税额(元)" width="100">
          <template slot-scope="scope">
            <span class="color-warning">{{
              NumFormat(scope.row.taxAmount || 0)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" label="金额(元)" width="120">
          <template slot-scope="scope">
            <span class="color-warning">{{
              NumFormat(scope.row.paidAmount || 0)
            }}</span>
          </template>
        </el-table-column>
        <empty-data class="p-t-20 p-b-20" slot="empty" />
      </el-table>
    </el-form>
  </div>
</template>

<script>
import { validateDecimal } from '@/utils/validate'
import { numberToChinese, NumFormat } from '@/utils/tools'
import { getBillTaxConfig } from '../../../api'

export default {
  name: 'BillInfo',
  props: {
    billData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      NumFormat,
      numberToChinese,
      formModel: {
        tableData: []
      },
      moneyLength: 15,
      rules: {
        feeCycle: [
          {
            required: true,
            message: '请选择本期开票周期',
            trigger: ['blur', 'change']
          }
        ],
        openAmount: [
          {
            required: true,
            message: '请输入本期开票金额',
            trigger: ['blur', 'change']
          },
          {
            validator: validateDecimal
          }
        ],
        count: [
          {
            validator: validateDecimal
          }
        ],
        price: [
          {
            required: false,
            message: '请选择单价',
            trigger: ['blur', 'change']
          },
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const val = Number(value)
              if (isNaN(val)) return callback(new Error('请输入数字'))
              const regex = /^(\d+\.?\d{0,8})$/
              if (!regex.test(value))
                return callback(new Error('最多保留8位小数'))
              callback()
            }
          }
        ],
        taxPercent: [
          {
            required: true,
            message: '请选择税率',
            trigger: ['blur', 'change']
          }
        ]
      },
      taxAmountTotal: 0,
      paidAmountTotal: 0,
      openAmountTotal: 0
    }
  },
  watch: {
    billData: {
      handler(val) {
        if (val && val.length) {
          this.getBillTaxConfig()
        } else {
          this.formModel.tableData = []
          this.computeHandle()
        }
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['InvoiceCreate'],
  methods: {
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    // 计算税额总计、金额总计、价税总计
    computeHandle(editInit = false) {
      const tableData = this.formModel.tableData
      this.taxAmountTotal = tableData.reduce((total, item) => {
        return total + Number(item.taxAmount || 0)
      }, 0)
      this.paidAmountTotal = tableData.reduce((total, item) => {
        return total + Number(item.paidAmount || 0)
      }, 0)
      this.openAmountTotal = tableData.reduce((total, item) => {
        return total + Number(item.openAmount || 0)
      }, 0)
      this.$emit('getRemark', editInit)
    },
    getTaxPercent(value, row, index) {
      this.formModel.tableData[index].freeTax = value === true ? value : false
      this.getTaxAmount(row)
    },
    // 计算税额和金额
    getTaxAmount(row) {
      // 含税金额*100
      const openAmount = row.openAmount ? row.openAmount * 100 : 0
      // 税率*100
      let taxPercent = 0
      if (!row.freeTax) {
        taxPercent = row.taxPercent ? row.taxPercent : 0
      }
      // （含税金额 / 1+ 税率）* 税率 = 税额
      row.taxAmount = (
        ((openAmount / (100 + taxPercent)) * taxPercent) /
        100
      ).toFixed(2)
      const taxAmount = row.taxAmount ? row.taxAmount * 100 : 0
      row.paidAmount = (openAmount - taxAmount) / 100
      this.computeHandle()
    },
    getBillTaxConfig() {
      getBillTaxConfig({
        billIds: this.billData.map(item => item.billId).toString()
      }).then(res => {
        this.formModel.tableData = res.map(item => {
          return {
            ...item,
            feeCycle: [item.rcvAmtSdt, item.rcvAmtEdt],
            openAmount: item.residueAmount,
            paidAmount: item.residueAmount
          }
        })
        // 编辑数据回显
        if (this.$route.query.id) {
          const invoiceList = this.InvoiceCreate.detailInfo.invoiceList || []
          this.formModel.tableData.forEach((item, index) => {
            invoiceList.forEach(row => {
              if (item.billId === row.billId) {
                this.$set(
                  this.formModel.tableData[index],
                  'openAmount',
                  row.openAmount
                )
                this.$set(
                  this.formModel.tableData[index],
                  'feeCycle',
                  row.openCycle.split(',')
                )
                this.$set(
                  this.formModel.tableData[index],
                  'invModel',
                  row.invModel
                )
                this.$set(
                  this.formModel.tableData[index],
                  'invUnit',
                  row.invUnit
                )
                this.$set(this.formModel.tableData[index], 'count', row.number)
                this.$set(this.formModel.tableData[index], 'price', row.price)
                this.$set(
                  this.formModel.tableData[index],
                  'taxPercent',
                  row.freeTax ? row.freeTax : row.tax
                )
                this.$set(
                  this.formModel.tableData[index],
                  'freeTax',
                  row.freeTax
                )
                this.$set(
                  this.formModel.tableData[index],
                  'taxAmount',
                  row.taxAmount
                )
                this.$set(
                  this.formModel.tableData[index],
                  'paidAmount',
                  row.amount
                )
              }
            })
          })
        }
        // 编辑时通知父级备注取原始值，而不是由开票信息覆盖
        this.computeHandle(!!this.$route.query.id)
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
:deep(.el-input-group__append) {
  padding: 0 10px;
}
</style>
