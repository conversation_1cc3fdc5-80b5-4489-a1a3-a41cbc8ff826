<template>
  <div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="saleConfigPage"
      :extral-querys="extralQuerys"
    >
      <template v-slot:operate-right>
        <el-button type="primary" @click="addHandle">添加</el-button>
      </template>
    </drive-table>
    <dialog-cmp
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="620px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="dialogVisible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
import {
  saleConfigCreate,
  saleConfigDelete,
  saleConfigPage,
  saleConfigUpdate
} from '../../api'

export default {
  name: 'PurchaserInfoMaintain',
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      saleConfigPage,
      dialogTitle: '新增',
      dialogVisible: false,
      fromModel: {},
      extralQuerys: {
        type: 2
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.dialogTitle = this.$options.data().dialogTitle
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  methods: {
    addHandle() {
      this.formConfigure.descriptors.taxpayerNumber.disabled = false
      this.dialogVisible = true
    },
    editHandle(row) {
      this.fromModel = { ...row }
      this.dialogTitle = '编辑'
      this.formConfigure.descriptors.taxpayerNumber.disabled = true
      this.dialogVisible = true
    },
    successHandle(text) {
      this.$toast.success(text + '成功')
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const { id = '' } = this.fromModel
        let url = saleConfigCreate
        if (id) url = saleConfigUpdate
        const params = {
          ...this.fromModel,
          type: 2
        }
        url(params).then(() => {
          this.successHandle(id ? '编辑' : '创建')
          this.dialogVisible = false
        })
      })
    },
    deleteHandle(row) {
      this.$confirm('确定删除此税率信息？').then(() => {
        saleConfigDelete({ id: row.id }).then(() => {
          this.successHandle('删除')
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  flex: 0 0 90px !important;
}
</style>
