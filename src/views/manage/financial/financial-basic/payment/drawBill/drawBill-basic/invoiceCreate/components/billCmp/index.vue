<template>
  <dialog-cmp
    title="选择账单"
    :visible.sync="_visible"
    width="1280px"
    @confirmDialog="confirmDialog"
  >
    <el-tabs v-model="active" @tab-click="handleClick">
      <el-tab-pane :label="item.label" :name="item.value" v-for="item in feeTypeList" :key="item.value" />
    </el-tabs>

    <el-row class="m-t-12 m-b-12" :gutter="20">
      <el-col :span="12">
        <el-col :span="8" v-if="active === '1'">
          <el-select style="width: 100%" @change="conditional($event,'contractId')" v-model="extralQuerys.contractId" clearable placeholder="请选择合同">
            <el-option
              v-for="item in contractList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select style="width: 100%"  @change="conditional($event,'openStatus')" v-model="extralQuerys.collectStatus" clearable placeholder="请选择开票状态">
            <el-option
              v-for="item in collectStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="8">
          <el-select style="width: 100%"   @change="conditional($event,'collectStatus')" v-model="extralQuerys.billStatus" clearable placeholder="请选择核销状态">
            <el-option
              v-for="item in writeOffList"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
      </el-col>
    </el-row>

    <drive-table
      ref="drive-table"
      v-show="_visible"
      :columns="tableColumnCom"
      :table-data="tableData"
      v-loading="loading"
      :extral-querys="extralQuerys"
      @selection-change="handleSelectionChange"
    />

    <div class="m-t-12">
      已选 {{ selectedNum }} 个，合计可开票金额
      <span class="color-primary">￥{{ amount }}</span>
    </div>
  </dialog-cmp>
</template>

<script>
import Column from './column'
import { NumFormat } from '@/utils/tools'
import { get_bill_select,getFeeType,getBillStatus,get_contract_select } from './api'
import { getWriteOffList } from '@/views/manage/financial/financial-jtyh/payment/accountsReceivable-basic/utils/status'
export default {
  name: 'BillCmp',
  mixins: [Column],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    openTypeInvoicing: {
      type: Number,
      default: undefined
    },
    entId: {
      type: Number,
      default: undefined
    },
    tableDataLC: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      loading: false,
      active: undefined,
      tableData: [],
      extralQuerys: {},
      selectedRows: [],
      feeTypeList:[],
      contractList:[],
      collectStatusList:[],
      writeOffList:getWriteOffList()
    }
  },
  watch: {
    visible(val) {
      if (!val){
        this.tableData = []
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    tableColumnCom() {
      return this.active === '1'
        ? this.tableColumnRent
        : this.tableColumnLD
    },
    selectedNum() {
      return this.selectedRows.length
    },
    amount() {
      return NumFormat(
        this.selectedRows.reduce(
          (total, item) => total + item.payAmount,
          0
        )
      )
    }
  },
  mounted() {
    this.getFeeType()
    this.getBillStatus()
  },
  methods: {

    billDetailFn({totalBillId,orderId}) {
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id: totalBillId,
          type: 0,
          orderId,
        }
      })
    },
    conditional(row, key) {
      this.extralQuerys[key] = row
      this.getBillSelect(this.tableDataLC)
    },
    paymentMemoHandle(row) {
      this.$emit('paymentMemoHandle', row)
    },
    getContractSelect() {
      const params = {
        entId: this.entId,
      }
      if (!this.entId) return this.$message.warning('请先选择企业')
      get_contract_select(params).then(res => {
        this.contractList = res.map(item => ({
          label: item.contractNo,
          value: item.contractId
        }))
      })
    },
    contractDetail(row) {
      this.$router.push({
        path: '/contract/index/contractDetails',
        query: {
          id: row.contractId,
          orderId: row.orderId
        }
      })
    },
    getBillStatus() {
      getBillStatus().then(res => {
        this.collectStatusList = res.map(item => ({
          label: item.label,
          value: item.key
        }))
      })
    },
    getFeeType() {
      getFeeType().then(res => {
        this.feeTypeList = res.map(item => ({
          label: item.label,
          value: String(item.key)
        }))
        this.active = this.feeTypeList[0].value
      })
    },
    getBillSelect(tableDataLC) {
      const params = {
        entId: this.entId,
        feeType: this.active,
        openTypeInvoicing: this.openTypeInvoicing,
        ...this.extralQuerys
      }
      this.loading = true
      get_bill_select(params).then(res => {
        this.loading = false
        this.tableData = res
        //匹配数据回显
        if (tableDataLC) {
          const data = tableDataLC.map(item => {
            return this.tableData.find(row => row.billId === item.billId)
          })

          this.$nextTick(() => {
            data.forEach(row => {
              this.$refs['drive-table'].toggleRowSelection(row);
            });
          })
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handleSelectionChange(rows) {
      this.selectedRows = rows
    },
    handleClick(tab) {
      this.active = tab.name
      this.selectedRows = []
      this.extralQuerys = {}
      this.getBillSelect(this.tableDataLC)
    },
    confirmDialog() {
      if (!this.selectedRows.length) {
        this.$message.warning('请选择需要开票的账单')
        return
      }
      this.$emit('addTableData', this.selectedRows)
    },
    reset() {
      this.selectedRows = []
      this.active = this.feeTypeList[0].value
      this._visible = false
    }
  }
}
</script>

<style scoped lang="scss"></style>
