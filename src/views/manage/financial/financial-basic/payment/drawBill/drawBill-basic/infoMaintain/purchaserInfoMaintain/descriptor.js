import {
  validateBankNumber,
  validateContact,
  validateEmail
} from '@/utils/validate'

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '120px',
        descriptors: {
          saleName: {
            form: 'input',
            label: '购买方名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入购买方名称'
              }
            ],
            attrs: {
              maxlength: 100
            }
          },
          taxpayerNumber: {
            form: 'input',
            label: '纳税人识别号',
            disabled: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入纳税人识别号'
              }
            ],
            attrs: {
              maxlength: 50
            }
          },
          saleAddress: {
            form: 'input',
            label: '购买方地址',
            rule: [
              {
                type: 'string',
                message: '请输入购买方地址'
              }
            ],
            attrs: {
              type: 'textarea',
              autosize: { minRows: 1, maxRows: 4 },
              maxlength: 200
            }
          },
          contactNumber: {
            form: 'input',
            label: '电话',
            rule: [
              {
                type: 'string',
                message: '请输入电话'
              },
              {
                validator: (rule, value, callback) => {
                  validateContact(rule, value, callback, false)
                }
              }
            ]
          },
          openType: {
            form: 'radio',
            label: '开票类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入开票类型'
              }
            ],
            options: [
              { label: '普通发票', value: 1 },
              { label: '增值税专用发票', value: 2 }
            ]
          },
          saleBankName: {
            form: 'input',
            label: '购买方开户行',
            rule: [
              {
                type: 'string',
                message: '请输入购买方开户行'
              }
            ],
            attrs: {
              maxlength: 25
            }
          },
          saleBankNo: {
            form: 'input',
            label: '银行账号',
            rule: [
              {
                type: 'string',
                message: '请输入银行账号'
              },
              {
                validator: validateBankNumber
              }
            ]
          },
          receiveEmail: {
            form: 'input',
            label: '发票接收邮箱',
            rule: [
              {
                type: 'string',
                message: '请输入发票接收邮箱'
              },
              {
                validator: validateEmail
              }
            ]
          }
        }
      }
    }
  }
}
