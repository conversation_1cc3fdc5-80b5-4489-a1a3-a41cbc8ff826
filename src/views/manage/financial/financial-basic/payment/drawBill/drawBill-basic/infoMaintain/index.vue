<template>
  <basic-card>
    <div
        class="w100 tabs-wrapper flex flex-wrap align-items-center justify-content-between"
    >
      <div class="flex align-items-center tabs-content">
        <template v-for="(item, index) in tabsData">
          <div
            v-permission="routeButtonsPermission[item.permission]"
            :key="index"
            class="font-size-14 color-text-regular p-l-16 p-r-16 tabs-item"
            :class="{ active: item.value === current }"
            @click="tabsChange(item.value)"
          >
            {{ item.label }}
          </div>
        </template>
      </div>
    </div>
    <receive-info v-if="current === 3" />
    <seller-info-maintain v-if="current === 1" />
    <purchaser-info-maintain v-if="current === 2" />
  </basic-card>
</template>

<script>
import ReceiveInfo from './receiveInfo'
import SellerInfoMaintain from './sellerInfoMaintain'
import PurchaserInfoMaintain from './purchaserInfoMaintain'
import { mapGetters } from 'vuex'
import { ALL_PERMISSION } from '@/settings'

export default {
  name: 'InfoMaintain',
  components: { PurchaserInfoMaintain, SellerInfoMaintain, ReceiveInfo },
  data() {
    return {
      tabsData: [ //RECEIVE_INFO_MAINTAIN
        { label: '开票主体信息维护', value: 3, permission: 'RECEIVE_INFO_MAINTAIN' },
        { label: '销售方信息维护', value: 1, permission: 'SELL_INFO_MAINTAIN' },
        { label: '购买方信息维护', value: 2, permission: 'BUY_INFO_MAINTAIN' }
      ],
      current: 3
    }
  },
  computed: {
    ...mapGetters(['permissions'])
  },
  created() {
    this.setDefaultCurrentByPermissions()

    const type = this.$route.query.type
    if (type) {
      const targetType = Number(type)
      if (this.hasPermissionForType(targetType)) {
        this.current = targetType
        console.log('this.current333',this.current)
      }
    }
  },
  methods: {
    tabsChange(e) {
      this.current = e
    },
    setDefaultCurrentByPermissions() {
      const hasReceivePermission = this.permissions.some(p =>
        p === ALL_PERMISSION || this.routeButtonsPermission.RECEIVE_INFO_MAINTAIN.includes(p)
      )
      const hasSellPermission = this.permissions.some(p =>
        p === ALL_PERMISSION || this.routeButtonsPermission.SELL_INFO_MAINTAIN.includes(p)
      )
      const hasBuyPermission = this.permissions.some(p =>
        p === ALL_PERMISSION || this.routeButtonsPermission.BUY_INFO_MAINTAIN.includes(p)
      )

      if (hasSellPermission && hasBuyPermission && hasReceivePermission) {
        this.current = 3
      } else if (hasSellPermission) {
        this.current = 1
      } else if (hasBuyPermission) {
        this.current = 2
      } else if (hasReceivePermission) {
        this.current = 3
      } else {
        this.current = ''
      }
    },
    hasPermissionForType(type) {
      const hasReceivePermission = this.permissions.some(p =>
        p === ALL_PERMISSION || this.routeButtonsPermission.RECEIVE_INFO_MAINTAIN.includes(p)
      )
      const hasSellPermission = this.permissions.some(p =>
        p === ALL_PERMISSION || this.routeButtonsPermission.SELL_INFO_MAINTAIN.includes(p)
      )
      const hasBuyPermission = this.permissions.some(p =>
        p === ALL_PERMISSION || this.routeButtonsPermission.BUY_INFO_MAINTAIN.includes(p)
      )

      switch (type) {
        case 1: // 销售方信息维护
          return hasSellPermission
        case 2: // 采购方信息维护
          return hasBuyPermission
        case 3:
          return hasReceivePermission
        default:
          return false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.tabs-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  margin-bottom: 24px;
  .tabs-content {
    transition: all 0.3s;
    overflow-x: hidden;
    overflow-y: hidden;
    white-space: nowrap;
    &:hover {
      transition: all 0.3s;
      overflow-x: auto;
    }
  }
  .tabs-item {
    height: 40px;
    line-height: 40px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
    &.disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }
    &.active {
      @include font_color(--color-primary);
      opacity: 1;
      &::before {
        content: '';
        width: 100%;
        height: 2px;
        @include background-color(--color-primary);
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
  }
}
</style>
