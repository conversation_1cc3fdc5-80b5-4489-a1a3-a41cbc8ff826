<template>
  <div>
    <driven-form
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
  </div>
</template>

<script>
import DescriptorMixin from './descriptor'
import { getSaleBody } from '../../../api'

export default {
  name: 'InvoiceInfo',
  props: {
    isTip: {
      type: Boolean,
      default: false
    },
    purchaser: {
      type: Boolean,
      default: true
    },
    openType: {
      type: Number,
      default: 1
    },
    disabled: {
      type: Boolean,
      default: false
    },
    entId: {
      type: Number,
      default: undefined
    },
    isFlag: {
      type: Boolean,
      default: false
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      defaultConfig: false,
      fromModel: {},
      nameList: [], // 原始下拉数据
      options: []
    }
  },
  watch: {
    isFlag:{
      handler(val){
        console.log('isFlag',val)
        if(val){
          this.formConfigure.descriptors.address.label = '购买方地址'
          this.formConfigure.descriptors.bankName.label = '购买方开户行'
        }else{
          this.formConfigure.descriptors.bankName.disabled = '销售方开户行'
        }
       },
      immediate: true
    },
    purchaser: {
      handler() {
        if (this.isFlag){
          this.formConfigure.descriptors.address.label = '购买方地址'
          this.formConfigure.descriptors.address.rule[0].message =
            '请输入购买方地址'
          this.formConfigure.descriptors.bankName.label = '购买方开户行'
          this.formConfigure.descriptors.bankName.rule[0].message =
            '请输入购买方开户行'
        }else {
          this.formConfigure.descriptors.address.label = '销售方地址'
          this.formConfigure.descriptors.address.rule[0].message =
            '请输入销售方地址'
          this.formConfigure.descriptors.bankName.label = '销售方开户行'
          this.formConfigure.descriptors.bankName.rule[0].message =
            '请输入销售方开户行'
        }

        this.formConfigure.descriptors.taxpayerNumber.disabled = true
        this.formConfigure.descriptors.taxpayerNumber.rule[0].required = false
        this.formConfigure.descriptors.address.disabled = true
        this.formConfigure.descriptors.contactNumber.disabled = true
        this.formConfigure.descriptors.bankName.disabled = true
        this.formConfigure.descriptors.bankNo.disabled = true
      },
      deep: true,
      immediate: true
    },
  },
  inject: ['InvoiceCreate'],
  methods: {
    initData(row) {
      this.info = row
      if (this.purchaser) {
        this.purchaserInitData(row)
      } else {
        if (!this.$route.query.id) {
          this.saleInitData(row)
        }
      }
    },
    // 销售方编辑
    saleInitData(data) {
      const row = data.invSaleConfigRespVO
      const id = !isNaN(row.id) ? row.id.toString() : row.id
      this.fromModel = {
        name: id || '',
        taxpayerNumber: row?.taxpayerNumber || '',
        address: row?.saleAddress || '',
        contactNumber: row?.contactNumber || '',
        bankName: row?.saleBankName || '',
        bankNo: row?.saleBankNo || ''
      }
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
      this.nameList = this.options = [
        {
          label: row?.saleName || '',
          value: id,
          labelHtml: row ? row.parkName + '/' + row.saleName : ''
        }
      ]
    },
    // 购买方编辑
    purchaserInitData(row) {
      const id = !isNaN(row.buyerId) ? row.buyerId.toString() : row.buyerId
      const value = this.$route.query.id ? id : row
      this.fromModel = {
        name: id || '',
        taxpayerNumber: row?.buyerCode || '',
        address: row?.buyerAddress || '',
        contactNumber: row?.buyerContact || '',
        bankName: row?.buyerBankName || '',
        bankNo: row?.buyerBankNo || ''
      }
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })

      this.nameList = this.options = [
        {
          label: row?.buyerName,
          value,
          labelHtml: row?.buyerName
        }
      ]
    },
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    nameChange(e) {
      let row = this.nameList.find(item => item.value === e)
      if (!row) {
        row = {
          saleName: e
        }
      }
      this.defaultConfig = !row.entId
      this.setPurchaserHandle(row)
    },
    // 购卖房表单赋值
    setPurchaserHandle(row) {
      this.$set(this.fromModel, 'taxpayerNumber', row.taxpayerNumber)
      this.$set(this.fromModel, 'address', row.saleAddress)
      this.$set(this.fromModel, 'contactNumber', row.contactNumber)
      this.$set(this.fromModel, 'bankName', row.saleBankName)
      this.$set(this.fromModel, 'bankNo', row.saleBankNo)
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
      this.$emit('getInfo', row)
    },
    remoteMethod(e) {
      if (!e) return (this.options = [])
      if (this.purchaser) {
        this.nameRemote(e, 2)
      } else {
        this.nameRemote(e, 3)
      }
    },
    // 购买方下拉
    nameRemote(e, type, cb) {
      let searchName = typeof e === 'object' ? '' : e
      getSaleBody({ searchName, type,entId:this.entId }).then(res => {
        this.nameList = this.options = res.map(item => {
          let name = item.flagName
          if (type === 1) {
            name = item.flagName + '/' + item.parkName
          }
          return {
            ...item,
            label: item.saleName,
            labelHtml: name,
            value: item.id.toString()
          }
        })
        cb && cb()
      })
    }
  }
}
</script>

<style scoped></style>
