<template>
  <div>
    <basic-card>
      <div class="flex justify-content-center step-container">
        <basic-steps :steps="stepsData" :current="current" />
      </div>
    </basic-card>
    <div class="invoice-wrapper m-t-8 bg-white">
      <div v-show="current === 1">
        <!-- 发票信息 -->
        <div class="flex justify-content-between">
          <div class="info-item">
            <div class="title">1购买方信息</div>
            <div class="info-item-wrapper">
              <invoice-info
                ref="purchaserInfo"
                :purchaser="true"
                :isTip="true"
                :entId="purchaserNameInfo.entId"
                :open-type="openType"
                :disabled="isId"
                @getInfo="getPurchaserInfo"
              />
            </div>
          </div>
          <div class="info-item">
            <div class="title">开票主体信息</div>
            <div class="info-item-wrapper">
              <invoice-info
                ref="saleInfo"
                :purchaser="false"
                :entId="purchaserNameInfo.entId"
                :open-type="openType"
                @getInfo="getSaleInfo"
              />
            </div>
          </div>
        </div>

        <!-- 开票方式 -->
        <div class="m-t-24">
          <div class="title">开票方式</div>
          <div class="flex">
            <div
              v-for="item in openTypeInvoicingList"
              :key="item.value"
              class="type-item pointer"
              :class="{ active: item.value === applyType,disabled: item.disabled }"
              @click="!item.disabled && openTypeInvoicingChange(item.value)"
            >
              <div class="flex align-items-center justify-content-between">
                <span class="font-size-14 line-height-22">{{
                    item.label
                  }}</span>
                <el-checkbox
                  :value="item.value === applyType"
                  @change="!item.disabled && openTypeInvoicingChange(item.value)"
                  :disabled="item.disabled"
                ></el-checkbox>
              </div>
              <div class="font-size-12 color-text-secondary m-t-8">
                {{item.desc}}
              </div>
            </div>
          </div>
        </div>

        <!-- 开票类型 -->
        <div class="m-t-24">
          <div class="title">开票类型</div>
          <div class="flex">
            <div
              v-for="item in openTypeList"
              :key="item.value"
              class="type-item pointer"
              :class="{ active: item.value === openType }"
              @click="openTypeChange(item.value)"
            >
              <div class="flex align-items-center justify-content-between">
                <span class="font-size-14 line-height-22">{{
                  item.label
                }}</span>
                <el-checkbox
                  :value="item.value === openType"
                  @change="openTypeChange(item.value)"
                ></el-checkbox>
              </div>
              <div class="font-size-12 color-text-secondary m-t-8">
                申请后生成电子发票，企业自行下载发票
              </div>
            </div>
          </div>
        </div>
        <div class="m-t-24">
          <div class="title flex align-items-center justify-content-between">
            <div>关联收款</div>
            <el-button :disabled="isDisabled" type="primary" class="m-l-12" @click="openBillInfo">选择账单</el-button>
          </div>
          <drive-table ref="drive-table" :columns="tableColumnLC" :table-data="tableDataLC" />
        </div>
        <!-- 开票信息 -->
        <div class="m-t-24">
          <bill-info
            :applyType="applyType"
            ref="billInfo"
            :open-type="openType"
          />
        </div>
        <!-- 备注信息 -->
        <div class="m-t-24">
          <div class="title flex justify-content-between">
            <div class="remark-title">备注信息</div>
            <el-link type="primary" @click="getRemark(false)"
              >生成备注信息</el-link
            >
          </div>
          <el-input
            v-model="invRemark"
            placeholder="请填写备注信息"
            type="textarea"
            maxlength="200"
            show-word-limit
            :rows="6"
          ></el-input>
        </div>

        <!-- 先票后款开票证明材料 -->
        <driven-form
          ref="driven-form"
          class="m-t-24"
          v-model="fromModel"
          v-if="applyType === 2"
          label-position="top"
          :formConfigure="formConfigure"
        />

      </div>
      <invoice-preview
        v-show="current === 2"
        class="invoice-preview"
        :info="invoiceData"
      />
      <div
        v-show="current === 3"
        class="audit-tips-wrapper flex flex-direction-column align-items-center"
      >
        <svg-icon icon-class="check-circle" />
        <div class="font-size-14 line-height-22">您的开票申请正在审核中</div>
        <div
          class="font-size-14 line-height-22 m-t-8 color-text-secondary flex align-items-center"
        >
          可在<el-link type="primary" @click="goInvoiceHandle"
            >开票管理列表</el-link
          >查看本次提交的申请和开票进度
        </div>
        <el-button type="primary" class="m-t-24" @click="goBack"
          >返回</el-button
        >
      </div>
      <div
        v-if="current === 1 || current === 2"
        class="bottom-wrapper flex justify-content-end m-t-24 p-t-24"
      >
        <el-button v-if="current === 1" type="primary" @click="nextHandle"
          >下一步</el-button
        >
        <template v-if="current === 2">
          <el-button type="primary" @click="current = 1">上一步</el-button>
          <el-button type="primary" @click="submitHandle">确认</el-button>
        </template>
      </div>
    </div>
    <pay-notice :visible.sync="payVisible" :id="paymentMemoId" />

    <bill-cmp :tableDataLC="tableDataLC" :entId="purchaserNameInfo.entId" :openTypeInvoicing="applyType" @addTableData="addTableData" @paymentMemoHandle="paymentMemoHandle" ref="billCmp" :visible.sync="billVisible"  />
  </div>
</template>

<script>
import BasicSteps from '@/components/BasicSteps'
import InvoiceInfo from './components/invoiceInfo'
import BillInfo from './components/billInfo'
import InvoicePreview from '../components/InvoicePreview'
import BillCmp from './components/billCmp'
import ColumnMixin from '../details/column'
import {
  invApplyRecordCreate,
  invApplyRecordOpenType,
  getApplyBill,
  invApplyRecordGetFn, get_remark
} from '../api'
import PayNotice from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/components/PayNotice'

export default {
  name: 'InvoiceCreate',
  mixins: [ColumnMixin],
  components: {
    PayNotice,
    InvoicePreview,
    BillInfo,
    InvoiceInfo,
    BasicSteps,
    BillCmp
  },
  data() {
    return {
      fromModel:{},
      preDisabled: false,
      paidDisabled: false,
      billVisible: false,
      current: 1,
      stepsData: [
        {
          title: '填写开票内容',
          desc: '正确填写开票内容',
          value: 1
        },
        {
          title: '确认发票信息',
          desc: '核对发票信息',
          value: 2
        },
        {
          title: '提交开票审核',
          desc: '审核通过后,等待系统开票完成',
          value: 3
        }
      ],
      kind: 1,
      // 开票类型
      openTypeInvoicingList: [
        {
          label: '先款后票',
          value: 1,
          disabled: false,
          desc:'基于已结款项开票'
        },
        {
          label: '先票后款',
          value: 2,
          disabled: false,
          desc:'基于未结款项开票'
        }
      ],
      openTypeList:[],
      openType: 2,
      invRemark: '',
      purchaserNameInfo: {}, // 购买方选择名称下拉信息
      saleNameInfo: {}, // 销售方选择名称下拉信息
      invoiceData: {}, // 整合的数据
      detailInfo: {}, // 编辑详情信息
      taxRateData: [],
      applyType: 1,
      tableDataLC: [],
      paymentMemoId: 0,
      payVisible: false,
      formConfigure: {
        descriptors: {
          preInvoicingIds: {
            form: 'component',
            label: '先票后款开票证明材料',
            rule: [
              {
                required: true,
                message: '请上传先票后款开票证明材料',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'containerSign'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      },
      billList:[]
    }
  },
  provide() {
    return {
      InvoiceCreate: this
    }
  },
  watch: {
    invRemark(val) {
      if (val && val.length > 200) {
        this.invRemark = val.substring(0, 200)
      }
    },
    tableDataLC: {
      handler(val) {
        if (val.length) {
          this.paidDisabled = this.applyType === 2
          this.preDisabled = this.applyType !== 2
        } else {
          this.paidDisabled = false
          this.preDisabled = false
        }
      },
      deep: true
    }
  },
  mounted() {
    this.invApplyRecordOpenType()
    // 编辑
    if (this.$route.query.id) {
      this.invApplyRecordGet()
    }
  },
  computed: {
    isId() {
      return !!this.$route.query.id
    },
    isDisabled() {
      return !!this.$route.query?.type
    }
  },
  methods: {
    contractDetail(row) {
      this.$router.push({
        path: '/contract/index/contractDetails',
        query: {
          id: row.contractId,
          orderId: row.orderId
        }
      })
    },
    openTypeInvoicingChange(val) {
      if (val === this.applyType) return
      this.applyType = val
      this.tableDataLC = []
      this.$refs.billInfo.initData(this.tableDataLC)
      if (this.isDisabled){
        this.tableDataLC = this.billList
          .filter(row =>
            val === 1
              ? row.collectStatus   !== 1
              : row.collectStatus === 1
          )

        this.$refs.billInfo.initData(this.tableDataLC,() => val)
        this.$refs.billInfo.allCountInput()
      }
    },
    openBillInfo() {
      this.$refs.billCmp.getBillSelect(this.tableDataLC)
      this.$refs.billCmp.getContractSelect()
      this.billVisible = true
    },
    addTableData(val) {
      const newItems = val.filter(
        v => !this.tableDataLC.some(item => item.billId === v.billId)
      );

      if (newItems.length > 0) {
        this.tableDataLC = this.tableDataLC.concat(newItems);
        this.$refs.billInfo.initData(this.tableDataLC);
      }

      this.$refs.billCmp.reset();
    },
    paymentMemoHandle(row) {
      this.paymentMemoId = row.totalBillId
      this.payVisible = true
    },
    moveOut(row) {
      const index = this.tableDataLC.findIndex(val => val.billId === row.billId)
      if (index !== -1) {
        this.tableDataLC.splice(index, 1)
      }
      this.$refs.billInfo.initData(this.tableDataLC)

      this.tableDataLC.forEach(item => {
        this.$refs.billInfo.getTaxAmount(item)
      })
    },
    // 关联收款数据填充
    addTableDataLC(val) {
      this.tableDataLC = val || []
      this.tableDataLC.forEach(item => {
        this.$refs.billInfo.getTaxAmount(item)
      })
      this.$refs.billInfo.initData(this.tableDataLC)
    },
    getApplyBill(val, type) {
      this.applyType = val
      const entId = this.purchaserNameInfo.entId
      if (!entId) return this.$message.warning('请先选择购买方信息')
      this.$refs.billInfo.invoicingVisible = true
      const data = {
        applyType: val,
        bodyId: entId
      }
      if (type) {
        data.feeType = type
      }
      getApplyBill(data).then(res => {
        this.$refs.billInfo.$refs.invoicing.tableData = res || []
        const data = res?.filter(item1 =>
          this.tableDataLC.some(item2 => item1.billId === item2.billId)
        )
        this.$refs.billInfo.$refs.invoicing.toggleSelection(data)
      })
    },
    invApplyRecordOpenType() {
      invApplyRecordOpenType().then(res => {
        this.openTypeList = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    invApplyRecordGet() {
      const { id } = this.$route.query

      invApplyRecordGetFn({ totalId: id }).then(res => {
        if (!res) {
          this.detailInfo = {}
          return
        }

        const {
          kind,
          openType,
          invRemark = '',
          openTypeInvoicing,
          bodyId,
          billList,
        } = res

        this.detailInfo = res
        this.kind = kind
        this.openType = openType
        this.invRemark = invRemark
        this.applyType = openTypeInvoicing[0] || 1
        this.purchaserNameInfo.entId = bodyId
        this.billList = billList || []
        this.tableDataLC = billList
          .filter(row =>
            this.applyType === 1
              ? row.collectStatus   !== 1
              : row.collectStatus === 1
          )
        this.purchaserNameInfo.saleName = res?.buyerName || ''

        this.openTypeInvoicingList = this.openTypeInvoicingList.map(item => {
          item.disabled = openTypeInvoicing.indexOf(item.value) === -1
          return item
        })

        this.$nextTick(() => {
          this.$refs.purchaserInfo.initData(res)
          this.$refs.saleInfo.initData(res)
          this.$refs.billInfo.initData(this.tableDataLC).then(() => {
            this.getRemark(false)
          })
          // this.tableColumnLC[this.tableColumnLC.length - 1].hidden = true
        })
      })
    },
    submitHandle() {
      const type = {
        1: { label: '先款后票' },
        2: { label: '先票后款' }
      }
      this.$confirm(
        `您的开票方式是 <span style="color:red;font-weight:bold;">${type[this.applyType].label}</span>，请确认是否继续？`,
        '提示',
        {
          type: 'warning',
          dangerouslyUseHTMLString: true
        }
      )
        .then(() => {
          const params = { ...this.getData() }
          return invApplyRecordCreate(params)
        })
        .then(() => {
          this.current = 3
        })
    },
    validateHandle() {
      const form = this.$refs['driven-form'];
      return form
        ? new Promise((resolve, reject) =>
          form.validate(valid => (valid ? resolve() : reject()))
        )
        : Promise.resolve()
    },
    // 下一步
    nextHandle() {
      Promise.all([
        this.$refs.purchaserInfo.validateHandle(),
        this.$refs.saleInfo.validateHandle(),
        this.$refs.billInfo.validateHandle(),
        this.validateHandle()
      ]).then(() => {
        const invRemark = this.invRemark?.trim()
        if (this.tableDataLC.length < 1)
          return this.$message.warning('请选择关联收款')
        if (!invRemark) return this.$toast.warning('请填写备注信息')
        const billInfo = this.$refs.billInfo
        const tableDta = billInfo.formModel.tableData || []
        const freeTax = tableDta.filter(item => !item.taxPercent)
        if (this.openType === 2 && freeTax.length) {
          return this.$toast.warning('当前账单包含免租税率，不可开具专票')
        }
        this.invoiceData = this.getData()
        this.current = 2
      })
    },
    // 获取请求数据
    getData() {
      const purchaserInfo = this.$refs.purchaserInfo.fromModel
      const billInfo = this.$refs.billInfo
      const tableDta = billInfo.formModel.tableData || []
      let { preInvoicingIds = []} = this.fromModel
      if (preInvoicingIds.length){
        preInvoicingIds = preInvoicingIds.map(item => item.id)
      }
      let bodyId
      let buyerId
      if (this.$route.query.id) {
        bodyId = this.detailInfo.bodyId
        buyerId = this.detailInfo.buyerId
      } else {
        buyerId = this.purchaserNameInfo.id
        bodyId = this.purchaserNameInfo.entId
      }
      const billList = tableDta.map(item => {
        return {
          billId: item.billId,
          invModel: item.invModel,
          invUnit: item.invUnit,
          number: item.count,
          openAmount: item.openAmount,
          openCycle:
            item.feeCycle && item.feeCycle.length
              ? item.feeCycle.toString()
              : '',
          price: item.price,
          taxData: item.taxPercentStr,
          tax: item.taxPercent,
          taxAmount: item.taxAmount,
          feeType: item.feeType,
          feeTypeStr: item.feeTypeStr,
          projectName: item.projectName,
          amount: item.paidAmount,
          freeTax: item.freeTax
        }
      })
      const params = {
        kind: this.kind,
        openType: this.openType,
        invHeader: this.purchaserNameInfo.type,
        buyerName: this.purchaserNameInfo.saleName,
        buyerAddress: purchaserInfo.address,
        buyerBankName: purchaserInfo.bankName,
        buyerBankNo: purchaserInfo.bankNo,
        buyerCode: purchaserInfo.taxpayerNumber,
        buyerContact: purchaserInfo.contactNumber,
        invSaleConfigRespVO: this.saleNameInfo,
        billList,
        invRemark: this.invRemark,
        sumTaxAmount: billInfo.openAmountTotal,
        sumTax: billInfo.taxAmountTotal,
        sumAmount: billInfo.paidAmountTotal,
        projectId: billInfo.projectId,
        bodyId,
        buyerId,
        openTypeInvoicing: this.applyType,
        preInvoicingIds
      }
      console.log('params-----',params)
      return params
    },
    // 生成默认备注信息
    getRemark(editInit = false) {
      // 编辑备注回显特殊处理，直接在返回值赋值会被开票信息覆盖
      if (editInit) return (this.invRemark = this.detailInfo.invRemark)
      const tableDta = this.$refs.billInfo?.formModel?.tableData.map( row => row.billId).filter(Boolean).join(',')
      if (!tableDta || !tableDta.length) return (this.invRemark = '')
      const data = {
        billIds:tableDta
      }
      get_remark(data).then( res => {
        this.invRemark = res
      })
    },
    // 销售方信息
    getSaleInfo(row) {
      this.saleNameInfo = row || {}
    },
    // 购买方信息
    getPurchaserInfo(row) {
      this.openType = row.openType
      this.purchaserNameInfo = row || {}
      this.tableDataLC = []
      this.$refs.billInfo.initData([])
      this.invRemark = ''
    },
    openTypeChange(e) {
      this.openType = e
    },
    goInvoiceHandle() {
      this.$router.replace('/drawBill/drawBillList')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.step-container {
  padding: 42px 0 32px 0;
}
.invoice-wrapper {
  padding: 24px;
  border-radius: 3px;
  .type-item {
    border-radius: 6px;
    border: 1px solid;
    @include border_color(--border-color-light);
    padding: 16px;
    & + .type-item {
      margin-left: 18px;
    }
    &.active {
      @include background_color_mix(--color-primary, #ffffff, 95%);
    }
    &.disabled {
      cursor: not-allowed;
      opacity: 0.7;
      background: #f5f7fa !important;
    }
  }
  .info-item {
    width: calc(50% - 18px);
    .info-item-wrapper {
      border-radius: 6px;
      border: 1px solid;
      @include border_color(--border-color-light);
      padding: 24px;
    }
  }
  .invoice-preview {
    width: 953px;
    margin: 0 auto;
  }
  .audit-tips-wrapper {
    padding: 80px 0;
    .svg-icon {
      font-size: 110px;
      @include font_color(--color-success);
    }
  }
}
.bottom-wrapper {
  border-top: 1px solid;
  @include border_color(--border-color-base);
}
.remark-title {
  padding-left: 12px;
  position: relative;
  &::before {
    display: inline-block;
    content: '*';
    color: #e34d59;
    position: absolute;
    left: 0;
  }
}
:deep(.title) {
  line-height: 22px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 16px;
}
</style>
