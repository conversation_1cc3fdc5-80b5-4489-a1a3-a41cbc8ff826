import { NumFormat } from '@/utils/tools'
import { noData } from '@/filter'
import {
  getOpenStatusType,
  getVerificationType, getWriteOffStatus
} from '@/views/manage/financial/financial-jtyh/payment/accountsReceivable-basic/utils/status'

export default {
  data() {
    const that = this
    return {
      tableColumn: [
        {
          label: '项目',
          prop: 'invProjectName'
        },
        {
          label: '本期开票金额(含税)',
          prop: 'openAmount',
          minWidth: '140px',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.openAmount)}
              </span>
            )
          }
        },
        {
          label: '本期开票周期',
          prop: 'openCycle',
          minWidth: '140px',
          render: (h, scope) => {
            return (
              <span>
                {scope.row.openCycle || '--'}
              </span>
            )
          }
        },
        {
          label: '规格型号',
          prop: 'invModel'
        },
        {
          label: '单位',
          prop: 'invUnit'
        },
        {
          label: '数量',
          prop: 'number'
        },
        {
          label: '单价(元)',
          prop: 'price',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.price ||
                scope.row.price === 0 ||
                scope.row.price === '0' ? (
                  <span class={'color-warning'}>{scope.row.price}</span>
                ) : (
                  <span>{noData(scope.row.price)}</span>
                )}
              </div>
            )
          }
        },
        {
          label: '金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>{NumFormat(scope.row.amount)}</span>
            )
          }
        },
        {
          label: '税率',
          prop: 'tax',
          render: (h, scope) => {
            return (
              <span>
                {!scope.row.taxData ? '免税' : scope.row.tax * 100 + '%'}
              </span>
            )
          }
        },
        {
          label: '税额(元)',
          prop: 'taxAmount',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.taxAmount)}
              </span>
            )
          }
        }
      ],
      billTableColumn: [
        {
          label: '客户名称',
          prop: 'customer',
          showOverflowTooltip: true,
          minWidth: 180
        },
        {
          label: '付款方',
          prop: 'payAcName',
          showOverflowTooltip: true,
          minWidth: 180
        },
        {
          label: '款项名称',
          prop: 'feePriceStr'
        },
        {
          label: '收款金额(含税)',
          prop: 'moneyCr',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.moneyCr)}</div>
            )
          }
        },
        {
          label: '收款部门',
          prop: 'deptName',
          minWidth: 120
        },
        {
          label: '收款单',
          prop: 'statusStr',
          minWidth: 120,
          render: (h, scope) => {
            return <span class={'color-primary'}>{scope.row.statusStr}</span>
          }
        },
        {
          label: '可开票金额(元)',
          prop: 'invoiceMoneyCr',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.invoiceMoneyCr)}
              </div>
            )
          }
        },
        {
          label: '已开票金额(元)',
          prop: 'openMmount ',
          minWidth: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.openMmount)}
              </div>
            )
          }
        }
      ],
      tableColumnLC: [
        {
          label: '合同编号',
          prop: 'contractNo',
          showOverflowTooltip: true,
          minWidth: 180,
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onclick={() => this.contractDetail(scope.row)}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          label: '费目、账期',
          prop: 'billName',
          minWidth: 150,
          render(h, scope) {
            return (
              <div class="flex flex-direction-column align-items-start" onClick={() => that.billDetailFn(scope.row)}>
                <el-link type="primary">{scope.row.billName}</el-link>
                <el-link type="primary">
                  {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
                </el-link>
              </div>
            )
          }
        },
        // {
        //   label: '期数',
        //   prop: 'billingPeriod'
        // },
        {
          label: '应收金额(元)',
          prop: 'payAmount ',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payAmount)}
              </div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'payActualAmount ',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.payActualAmount)}</div>
            )
          }
        },
        {
          renderHeader: () => {
            return (
              <div class="flex align-items-center">
                <div>已开票金额(元)</div>
                {/*<el-tooltip className="item" effect="dark" placement="top">*/}
                {/*  <svg-icon*/}
                {/*    className="font-size-14 m-l-6"*/}
                {/*    icon-class="prompt"*/}
                {/*  />*/}
                {/*  <div slot="content">*/}
                {/*    <h3 class="m-b-8">已开票金额计算标准</h3>*/}
                {/*    <div class="m-b-8">*/}
                {/*      已开票金额(先款后票)=本账单开票金额(先款后票)-本账单红冲金额(先款后票)*/}
                {/*    </div>*/}
                {/*    <div>*/}
                {/*      已开票金额(先票后款)=本账单开票金额(先票后款)-本账单红冲金额(先票后款)*/}
                {/*    </div>*/}
                {/*  </div>*/}
                {/*</el-tooltip>*/}
              </div>
            )
          },
          prop: 'openAmount ',
          minWidth: 160,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.openAmount)}</div>
            )
          }
        },
        {
          label: '开票状态',
          prop: 'openStatus ',
          minWidth: 120,
          render: (h, scope) => {
            return <div>{getOpenStatusType(h, scope.row.openStatus)}</div>
          }
        },
        {
          label: '核销状态',
          prop: 'collectStatus ',
          minWidth: 120,
          render: (h, scope) => {
            return <div>{getWriteOffStatus(h, scope.row.collectStatus)}</div>
          }
        },
        {
          label: '缴费通知单',
          prop: 'billStatus ',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onclick={() => this.paymentMemoHandle(scope.row)}
              >
                缴费通知单
              </el-link>
            )
          }
        },
        //操作
        {
          label: '操作',
          width: 80,
          hidden: false,
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                disabled={this.isDisabled}
                onClick={() => {
                  this.moveOut(scope.row)
                }}
              >
                移出
              </el-link>
            )
          }
        }
      ],
      tableColumnLCD: [
        {
          label: '合同编号',
          prop: 'contractNo',
          showOverflowTooltip: true,
          minWidth: 180,
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onclick={() => this.contractDetail(scope.row)}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          label: '费目、账期',
          prop: 'feeTypeStr',
          minWidth: 130,
          render(h, scope) {
            return (
              <div class="flex flex-direction-column align-items-start"  onClick={() => that.billDetailFn(scope.row)}>
                <el-link type="primary">{scope.row.feeTypeStr}</el-link>
                <el-link type="primary">
                  {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
                </el-link>
              </div>
            )
          }
        },
        {
          label: '应收金额(元)',
          prop: 'payAmount ',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payAmount)}
              </div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'acAmount ',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.acAmount)}</div>
            )
          }
        },
        {
          label: '已开票金额(元)',
          prop: 'openAmount',
          minWidth: 150,
          render: (h, scope) => {
            return (
              <div>
                <div class={'color-warning   m-b-8'}>
                  {NumFormat(scope.row.openAmount)}
                </div>
                <div class={'flex align-items-center'}>
                  <div class={'flex align-items-center'}>
                    <el-tooltip
                      className="item"
                      effect="dark"
                      content="先款后票已开票金额"
                      placement="top"
                    >
                      <div
                        style={
                          'color: #fff; background: #de8142; padding: 0 4px; border-radius: 2px; margin-right: 4px;'
                        }
                      >
                        款
                      </div>
                    </el-tooltip>
                    <div class={'color-warning'}>
                      {' '}
                      {NumFormat(scope.row.openAcAmount)}
                    </div>
                  </div>
                  <div class={'flex align-items-center m-l-12'}>
                    <el-tooltip
                      className="item"
                      effect="dark"
                      content="先票后款已开票金额"
                      placement="top"
                    >
                      <div
                        style={
                          'color: #fff; background: red; padding: 0 4px; border-radius: 2px; margin-right: 4px;'
                        }
                      >
                        票
                      </div>
                    </el-tooltip>
                    <div class={'color-warning'}>
                      {NumFormat(scope.row.openPayAmount)}
                    </div>
                  </div>
                </div>
              </div>
            )
          }
        },
        {
          label: '可开票金额(元)',
          prop: 'remainOpenAmount',
          width: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.remainOpenAmount)}
              </div>
            )
          }
        },
        {
          label: '开票状态',
          prop: 'openStatusStr',
          render: (h, scope) => {
            return (
              <div>
                {getOpenStatusType(
                  h,
                  scope.row.openStatus,
                  scope.row.openStatusStr
                )}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'billStatus ',
          render: (h, scope) => {
            return <div>{getVerificationType(h, scope.row.billStatus)}</div>
          }
        },
        {
          label: '缴费通知单',
          prop: 'billStatus ',
          render: (h, scope) => {
            return (
              <el-link
                type={'primary'}
                onclick={() => this.paymentMemoHandle(scope.row)}
              >
                查看
              </el-link>
            )
          }
        }
        // {
        //   label: '已开票金额(元)',
        //   prop: 'invoicedAmount ',
        //   render: (h, scope) => {
        //     return (
        //         <div class={'color-warning'}>
        //           {NumFormat(scope.row.invoicedAmount)}
        //         </div>
        //     )
        //   }
        // },
        // {
        //   label: '开票状态',
        //   prop: 'openStatusStr',
        // },
      ]
    }
  },
  methods:{
    billDetailFn({totalBillId,orderId}) {
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id: totalBillId,
          type: 0,
          orderId,
        }
      })
    },
  }
}
