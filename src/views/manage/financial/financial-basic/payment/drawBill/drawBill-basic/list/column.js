import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '客户名称',
          prop: 'bodyName',
          showOverflowTooltip: true,
          minWidth: 200
        },
        {
          label: '开票金额(含税)',
          minWidth: 140,
          prop: 'sumAmount',
          render: (h, {row}) => {
            return (
              <div
                class={{
                  'color-warning': !row.destroyFlag,
                  'color-info': row.destroyFlag,
                  'underline': row.destroyFlag
                }}
              >
                {NumFormat(row.sumAmount)}
              </div>
            )
          }
        },
        {
          label: '发票类型',
          prop: 'invoiceTypeStr',
          minWidth: 140,
          render: (h, { row }) => {
            const text = row.destroyFlag ? '红字发票（电子）' : row.invoiceTypeStr;
            return <span>{text}</span>;
          }
        },
        {
          label: '申请时间',
          prop: 'applyTime',
          minWidth: 140
        },
        {
          label: '开票类型',
          prop: 'openTypeInvoicingStr',
          minWidth: 140,
          render: (h, { row }) => {
            const text = row.destroyFlag
              ? `发票红冲 ${row.openTypeInvoicingStr}`
              : row.openTypeInvoicingStr;
            return <span>{text}</span>;
          }
        },
        // {
        //   label: '开票依据',
        //   prop: 'invoiceBasisStr',
        //   minWidth: 140,
        //   render: (h, scope) => {
        //     return (
        //       <div>
        //         {scope.row.invoiceBasis === 2 ?
        //           <el-link
        //             type={'primary'}
        //             onClick={() => {this.goJournalDetailHandle(scope.row)}}
        //           >{noData(scope.row.invoiceBasisStr)}</el-link> :
        //           <span>{noData(scope.row.invoiceBasisStr)}</span> }
        //       </div>
        //     )
        //   }
        // },
        {
          label: '开票状态',
          prop: 'examineStatusStr',
          minWidth: 140,
          render: (h, scope) => {
            const obj = {
              1: 'warning',
              2: 'success',
              3: 'danger'
            }
            return (
              <el-tag type={obj[scope.row.examineStatus]}>
                {scope.row.examineStatusStr}
              </el-tag>
            )
          }
        },
        {
          label: '当前审核节点',
          prop: 'currentStep',
          minWidth: 140,
          showOverflowTooltip: true,
        },
        {
          label: '发票上传',
          prop: 'uploadInv',
          minWidth: 100,
          render: (h, scope) => {
            const type = {
              true: 'success',
              false: 'info'
            }
            return (
              <div>
                <el-tag type={type[scope.row.uploadInv]}>
                  {scope.row.uploadInv ? '已上传' : '未上传'}
                </el-tag>
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.VIEW}
                  type="primary"
                  onclick={() => {
                    this.detailHandle(scope.row)
                  }}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
                {scope.row.examineStatus === 1 && scope.row.withdrawPermission && (
                  <el-link
                    type="danger"
                    class="m-l-8"
                    onclick={() => {
                      this.withdraw(scope.row)
                    }}
                  >
                    撤回申请
                  </el-link>
                )}
                {/*{scope.row.examineStatus === 3 &&(*/}
                {/*  <el-link*/}
                {/*    class="m-l-8"*/}
                {/*    type="primary"*/}
                {/*    onclick={() => {*/}
                {/*      this.editHandle(scope.row)*/}
                {/*    }}*/}
                {/*  >*/}
                {/*    重新发起*/}
                {/*  </el-link>*/}
                {/*)}*/}
                {scope.row.examineStatus === 2 && (
                  <el-link
                    v-permission={this.routeButtonsPermission.UPLOAD_INVOICE}
                    class="m-l-8"
                    type="primary"
                    onclick={() => {
                      this.invoiceUpload(scope.row)
                    }}
                  >
                    {scope.row.uploadInv ? '重新上传' : '发票上传'}
                  </el-link>
                )}

              </div>
            )
          }
        }
      ]
    }
  }
}
