export default {
  data() {
    return {
      tableColumn: [
        {
          label: '序号',
          prop: 'index',
          width: 80,
          render: (h, scope) => {
            return <span>{scope.$index + 1}</span>
          }
        },
        {
          label: '费用类型',
          prop: 'feeTypeName'
        },
        {
          label: '税率',
          prop: 'taxPercentStr',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.freeTax ? (
                  '免税'
                ) : (
                  <span>{scope.row.taxPercentStr}%</span>
                )}
              </div>
            )
          }
        },
        {
          label: '规格型号',
          prop: 'invModel'
        },
        {
          label: '计量单位',
          prop: 'invUnit'
        },
        {
          label: '单价(元)',
          prop: 'price',
          render: (h, scope) => {
            return <div class="color-warning">{scope.row.price}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type={'text'}
                  class={'color-success'}
                  onClick={() => {
                    this.editHandle(scope.row)
                  }}
                >
                  修改
                </el-link>
                <el-link
                  onClick={() => {
                    this.deleteHandle(scope.row)
                  }}
                  type={'text'}
                  class={'color-danger m-l-8'}
                >
                  删除
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
