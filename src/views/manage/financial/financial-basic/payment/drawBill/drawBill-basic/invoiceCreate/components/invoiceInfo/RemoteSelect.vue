<template>
  <div>
    <el-select
      class="w100"
      v-model="_value"
      filterable
      :disabled="disabled"
      remote
      placeholder="请输入名称"
      :remote-method="remoteMethod"
      @change="changeHandle"
      @focus="focusHandle"
    >
      <el-option
        v-for="(item, index) in options"
        :key="index"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      >
        <div v-html="item.labelHtml"></div>
      </el-option>
      <template slot="empty">
        <div class="flex align-items-center font-size-14 p-6">
          <span class="color-info m-r-8">未搜索到相关信息</span>
          <el-link type="primary" @click="goMaintainHandle">去维护</el-link>
        </div>
      </template>
    </el-select>
    <span v-if="isTip && defaultConfig" class="color-warning font-size-12"
      >这是一家非在园的企业，请及时关注信息准确性</span
    >
  </div>
</template>

<script>
export default {
  name: 'RemoteSelect',
  props: {
    isTip: {
      type: Boolean,
      default: false
    },
    defaultConfig: {
      type: Boolean,
      default: false
    },
    value: {
      required: true
    },
    options: {
      type: Array,
      default: () => []
    },
    purchaser: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    goMaintainHandle() {
      this.$router.push({
        path: '/drawBill/drawBillList/infoMaintain',
        query: {
          type: this.purchaser ? 2 : 1
        }
      })
    },
    focusHandle(e) {
      if (this.purchaser) return
      this.$emit('remoteMethod', e)
    },
    changeHandle(e) {
      this.$emit('changeHandle', e)
    },
    remoteMethod(e) {
      this.$emit('remoteMethod', e)
    }
  }
}
</script>

<style scoped></style>
