<template>
  <div>
    <div class="flex align-items-center justify-content-between">
      <div class="title" style="margin-bottom: 0">开票信息</div>
      <div
        class="flex align-items-center"
        @click="rateVisible = true"
        v-if="!$route.query.id"
      >
        <svg-icon class="font-size-14" icon-class="prompt" />
        <el-link :underline="false" class="m-l-4 color-text-secondary"
          >查看费用税率标准</el-link
        >
      </div>
    </div>
    <el-form :model="formModel" :rules="rules" ref="ruleForm">
      <el-table
        class="m-t-16"
        border
        :data="formModel.tableData"
        stripe
        :key="tableKey"
        show-summary
        ref="table"
        :summary-method="getSummaries"
      >
        <el-table-column prop="projectName" label="项目" min-width="180">
        </el-table-column>
        <el-table-column
          prop="openAmount"
          min-width="140"
        >
          <template slot="header">
            <div class="pos-relative-box">
              <el-popover
                placement="top"
                width="800"
                trigger="hover">
                <div>
                  <div class="font-size-14 m-b-12 font-strong">可开票金额计算标准</div>
                  <el-table
                    :data="tableDataAmount"
                    border
                    style="width: 100%">
                    <el-table-column
                      prop="int"
                      label="开票方式"
                      width="80">
                    </el-table-column>
                    <el-table-column
                      prop="date"
                      label="条件"
                      width="300">
                    </el-table-column>
                    <el-table-column
                      prop="address"
                      label="计算公式(负值计为0)">
                    </el-table-column>
                  </el-table>
                </div>
                <div  slot="reference" class="flex align-items-center">
                  <span>可开票金额(元)</span>
                  <svg-icon
                    class="font-size-14 m-l-6"
                    icon-class="prompt"
                  />
                </div>
              </el-popover>
            </div>
          </template>
          <template slot-scope="scope">
            <span class="color-primary">{{
              NumFormat(scope.row.currentOpenAmount)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="openAmount"
          label="本期开票金额(含税)"
          width="180"
        >
          <template slot="header">
            <el-popover
              placement="top"
              trigger="hover">
              <div class="font-size-14">本期开票金额(含税) {{typeStr}} 可开票金额</div>
              <div  slot="reference" style="width: 180px" class="flex align-items-center">
                <div>
                  <span style="color: #e34d59">*</span>
                  <span>本期开票金额(含税)</span>
                </div>
                <svg-icon
                  class="font-size-14 m-l-6"
                  icon-class="prompt"
                />
              </div>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.openAmount'"
              :rules="rules.openAmount"
            >
              <el-input
                :value="scope.row.openAmount"
                @input="val => handleOpenAmount(val, scope.row)"
                placeholder="请输入"
                :disabled="applyType === 2"
                :maxLength="moneyLength"
              >
                <span slot="append" class="color-text-primary">元</span>
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="feeCycle" label="本期开票周期" min-width="240">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.feeCycle'">
              <el-date-picker
                v-model="scope.row.feeCycle"
                type="daterange"
                range-separator="至"
                start-placeholder="起始时间"
                end-placeholder="结束时间"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="invModel" label="规格型号" width="250">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.invModel'">
              <el-input
                v-model="scope.row.invModel"
                placeholder="请输入"
                :maxLength="15"
              >
              </el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="invUnit" label="单位" width="185">
          <template slot-scope="scope">
            <el-form-item :prop="'tableData.' + scope.$index + '.invUnit'">
              <el-input
                v-model="scope.row.invUnit"
                placeholder="请输入"
                :maxlength="10"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="count" label="数量" width="150">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.count'"
              :rules="rules.count"
            >
              <el-input
                v-model="scope.row.count"
                placeholder="请输入"
                :maxlength="15"
                @input="countInput(scope.row)"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="price" label="单价(元)" width="200">
          <template slot-scope="scope">
            <el-form-item
              :prop="'tableData.' + scope.$index + '.price'"
              :rules="rules.price"
            >
              <el-input
                v-model="scope.row.price"
                placeholder="请输入"
                :maxlength="20"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column prop="paidAmount" width="120">
          <template slot="header">
            <el-popover
              placement="top"
              trigger="hover">
              <div class="font-size-14">金额=本期开票金额(含税)/(1+税率)</div>
              <div  slot="reference" style="width: 120px" class="flex align-items-center">
                <div>
                  金额(元)
                </div>
                <svg-icon
                  class="font-size-14 m-l-6"
                  icon-class="prompt"
                />
              </div>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <span class="color-warning">{{
              NumFormat(scope.row.paidAmount || 0)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taxPercent" label="税率" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.taxPercentStr }}</span>
            <!--            <el-form-item-->
            <!--              :prop="'tableData.' + scope.$index + '.taxPercent'"-->
            <!--              :rules="rules.taxPercent"-->
            <!--            >-->
            <!--              <el-select-->
            <!--                class="w100"-->
            <!--                v-model="scope.row.taxPercent"-->
            <!--                placeholder="请选择"-->
            <!--                :disabled="true"-->
            <!--                @change="getTaxPercent($event, scope.row, scope.$index)"-->
            <!--                :clearable="openType !== 2"-->
            <!--              >-->
            <!--                <el-option-->
            <!--                  v-for="(item, index) in taxOptions"-->
            <!--                  :key="index"-->
            <!--                  :label="item.label + '%'"-->
            <!--                  :value="item.value"-->
            <!--                ></el-option>-->
            <!--              </el-select>-->
            <!--            </el-form-item>-->
          </template>
        </el-table-column>
        <el-table-column prop="taxAmount" width="120">
          <template slot="header">
            <el-popover
              placement="top"
              trigger="hover">
              <div class="font-size-14">税额=本期开票金额(含税)-金额</div>
              <div  slot="reference" style="width: 120px" class="flex align-items-center">
                <div>
                  税额(元)
                </div>
                <svg-icon
                  class="font-size-14 m-l-6"
                  icon-class="prompt"
                />
              </div>
            </el-popover>
          </template>
          <template slot-scope="scope">
            <span class="color-warning">{{
                NumFormat(scope.row.taxAmount || 0)
              }}</span>
          </template>
        </el-table-column>

        <empty-data class="p-t-20 p-b-20" slot="empty" />
      </el-table>
    </el-form>
    <div class="m-t-8 flex align-items-center font-size-14 line-height-22">
      <div>
        <span>价税合计(大写)</span>
        <span class="m-l-8 color-warning">
          {{ numberToChinese(this.openAmountTotal) }}
        </span>
      </div>
      <div class="m-l-32">
        <span>价税合计(小写)</span>
        <span class="m-l-8 color-warning">
          {{ `￥: ${NumFormat(this.openAmountTotal)}` }} 元
        </span>
      </div>
    </div>

    <!-- 预开票 -->
    <invoicing ref="invoicing" :visible.sync="invoicingVisible" />

    <!-- 税率标准 -->
    <tax-rate-standard ref="taxRateStandard" :visible.sync="rateVisible" />
  </div>
</template>

<script>
import { validateDecimal, validateThreeDecimal } from '@/utils/validate'
import { numberToChinese, NumFormat } from '@/utils/tools'
import { getTaxTable, taxConfigTaxConfigList } from '../../../api'
import { getDict } from '@/api/common'
import Invoicing from '../invoicing'
import TaxRateStandard from '../taxRateStandard'

export default {
  name: 'BillInfo',
  props: {
    openType: {
      type: Number,
      default: 2
    },
    applyType: {
      type: [String, Number],
      default: undefined
    }
  },
  components: {
    Invoicing,
    TaxRateStandard
  },
  data() {
    return {
      typeStr: '',
      tableDataAmount: [
        {
          date: '部分核销、全额核销账单',
          int: '先款后票',
          address: '可开票金额(先款后票)=实收金额-已开票金额(先款后票)'
        }, {
          date: '未核销账单',
          int: '先票后款',
          address: '可开票金额(先票后款)=应收金额'
        },
      ],
      rateVisible: false,
      invoicingVisible: false,
      NumFormat,
      numberToChinese,
      formModel: {
        tableData: []
      },
      moneyLength: 15,
      rules: {
        feeCycle: [
          {
            required: true,
            message: '请选择本期开票周期',
            trigger: ['blur', 'change']
          }
        ],
        openAmount: [
          {
            required: true,
            message: '请输入本期开票金额',
            trigger: ['blur', 'change']
          },
          {
            validator: validateDecimal
          }
        ],
        count: [
          {
            validator: validateThreeDecimal
          }
        ],
        price: [
          {
            message: '请选择单价',
            trigger: ['blur', 'change']
          },
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const val = Number(value)
              if (isNaN(val)) return callback(new Error('请输入数字'))
              const regex = /^(\d+\.?\d{0,12})$/
              if (!regex.test(value))
                return callback(new Error('最多保留12位小数'))
              callback()
            }
          }
        ],
        taxPercent: [
          {
            required: true,
            message: '请选择税率',
            trigger: ['blur', 'change']
          }
        ]
      },
      taxAmountTotal: 0,
      paidAmountTotal: 0,
      openAmountTotal: 0,
      tableKey: Math.random(),
      taxOptions: [],
      taxRateStandardTableData: []
    }
  },
  watch: {
    applyType: {
      immediate: true,
      handler(val) {
        this.typeStr = val === 1? '<=' :'='
        this.tableKey = this.$options.data().tableKey
      }
    },
    openType(val) {
      this.rules.taxPercent[0].required = val === 2
      this.tableKey = this.$options.data().tableKey
    }
  },
  inject: ['InvoiceCreate'],
  created() {
    this.getDict()
  },
  mounted() {
    this.getTaxTable()
  },
  methods: {
    getTaxTable() {
      getTaxTable().then(res => {
        this.taxRateStandardTableData = res || []
        this.$refs.taxRateStandard.tableData = res || []
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (
          column.property === 'taxAmount' ||
          column.property === 'paidAmount'
        ) {
          const values = data.map(item => Number(item[column.property]))
          const total = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              // 继续累加，确保精度
              return prev + value
            } else {
              return prev
            }
          }, 0)

          sums[index] = (Math.round(total * 100) / 100).toFixed(2)
        }
      })
      return sums
    },

    initData(row,cd) {
      return new Promise((resolve, reject) => {
        try {
          const applyType = cd? cd() : this.applyType
          this.formModel.tableData = row.map(item => {
            let openAmount
            let rcvAmtSdt
            let rcvAmtEdt
            openAmount = applyType === 2 ? item.remainPayOpenAmount : item.remainActualOpenAmount
            rcvAmtSdt = item.rcvAmtSdt || ''
            rcvAmtEdt = item.rcvAmtEdt || ''
            // if (this.$route.query.id) {
            //   openAmount = item.openAmount || 0
            //   rcvAmtSdt = item.openCycle ? item.openCycle.split(',')[0] : ''
            //   rcvAmtEdt = item.openCycle ? item.openCycle.split(',')[1] : ''
            // } else {
            //   openAmount = this.applyType === 2 ? item.remainPayOpenAmount : item.remainActualOpenAmount
            //   rcvAmtSdt = item.rcvAmtSdt || ''
            //   rcvAmtEdt = item.rcvAmtEdt || ''
            // }
            const taxPercent = this.taxRateStandardTableData.find(row => {
              if (row.feeType.includes(item.feeType)) {
                return row
              }
            }).taxPercent
            const taxPercentStr = this.taxRateStandardTableData.find(row => {
              if (row.feeType.includes(item.feeType)) {
                return row
              }
            }).taxPercentStr

            const row = {
              ...item,
              taxPercent,
              taxPercentStr,
              feeCycle: [rcvAmtSdt, rcvAmtEdt],
              count: item.number,
              openAmount,
              currentOpenAmount:openAmount
            }
            this.getTaxAmount(row)
            return row
          })
          console.log('this.formModel.tableData----',this.formModel.tableData)
          this.computeHandle()
          resolve()
        } catch (error) {
          reject(error)
        }
      })
    },
    getDict() {
      getDict('tax_rate').then(res => {
        this.taxOptions = res || []
      })
    },
    projectChange(val) {
      taxConfigTaxConfigList({ id: val }).then(res => {
        const list = res || []
        this.projectInfo = list.find(item => item.typeKey === 1) || {}
        if (!this.projectInfo || JSON.stringify(this.projectInfo) === '{}') {
          this.formModel.tableData.forEach((item, index) => {
            this.$set(this.formModel.tableData[index], 'invModel', '')
            this.$set(this.formModel.tableData[index], 'invUnit', '')
            this.$set(this.formModel.tableData[index], 'count', '')
            this.$set(this.formModel.tableData[index], 'taxPercent', '')
            this.$set(this.formModel.tableData[index], 'taxPercentStr', '')
            this.getTaxAmount(item)
          })
        } else {
          const freeTax = this.projectInfo.freeTax
          this.formModel.tableData.forEach((item, index) => {
            for (const itemKey in this.projectInfo) {
              this.$set(
                this.formModel.tableData[index],
                itemKey,
                this.projectInfo[itemKey]
              )
              if (freeTax) {
                this.$set(this.formModel.tableData[index], 'taxPercent', '')
              }
            }
            this.getTaxAmount(item)
          })
        }
        this.tableKey = this.$options.data().tableKey
      })
    },
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    // 计算税额总计、金额总计、价税总计
    computeHandle() {
      const tableData = this.formModel.tableData
      this.taxAmountTotal = tableData.reduce((total, item) => {
        return total + Number(item.taxAmount || 0)
      }, 0)
      this.paidAmountTotal = tableData.reduce((total, item) => {
        return total + Number(item.paidAmount || 0)
      }, 0)
      this.openAmountTotal = tableData.reduce((total, item) => {
        return total + Number(item.openAmount || 0)
      }, 0)
    },
    getTaxPercent(value, row, index) {
      const item = this.taxOptions.find(item => item.value === value)
      this.formModel.tableData[index].taxPercentStr = item ? item.label : ''
      this.getTaxAmount(row)
    },
    calcPrice(row) {
      if (!row.count || row.count === '0') {
        row.price = ''
        return
      }
      const result = (row.paidAmount * 1000) / (row.count * 1000)
      const str = result.toString()
      const dotIdx = str.indexOf('.')
      const decimals = dotIdx === -1 ? 0 : str.length - dotIdx - 1
      row.price = decimals < 13 ? str : result.toFixed(12)
    },
    countInput(row) {
      this.calcPrice(row)
    },
    allCountInput() {
      this.formModel.tableData.forEach(row => this.calcPrice(row))
    },
    handleOpenAmount(val, row) {
      let s = String(val || '')
      const max = Number(row.currentOpenAmount) || 0

      const formatDecimal = (value) => {
        let filtered = value.replace(/[^0-9.]/g, '')
        const parts = filtered.split('.')
        if (parts.length > 2) {
          filtered = parts[0] + '.' + parts[1]
        }
        if (parts[1] && parts[1].length > 2) {
          filtered = parts[0] + '.' + parts[1].substring(0, 2)
        }
        return filtered
      }
      s = formatDecimal(s)

      console.log('handleOpenAmount', s)

      if (s === '') {
        row.openAmount = ''
      } else {
        const numValue = Number(s)
        if (!isNaN(numValue) && numValue > max) {
          row.openAmount = max.toFixed(2)
        } else {
          row.openAmount = s
        }
      }

      this.getTaxAmount(row)
      this.countInput(row)
    },
    // 计算税额和金额
    getTaxAmount(row) {

      // 含税金额*100
      const openAmount = row.openAmount ? row.openAmount * 100 : 0
      // 税率*100
      const taxPercent = row.taxPercent ? row.taxPercent * 100 : 0
      // （含税金额 / 1 + 税率）* 税率 = 税额
      row.taxAmount = Number(
        (((openAmount / (100 + taxPercent)) * taxPercent) / 100).toFixed(2)
      )
      const taxAmount = row.taxAmount ? row.taxAmount * 100 : 0
      row.paidAmount = (openAmount - taxAmount) / 100
      this.computeHandle()
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  width: 100%;
}
:deep(.el-date-editor) {
  width: 100%;
}
:deep(.el-input-group__append) {
  padding: 0 10px;
}
:deep(.el-table) {
  .el-form-item {
    margin-top: 18px;
  }
}

</style>
