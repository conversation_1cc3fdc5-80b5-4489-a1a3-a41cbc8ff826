<template>
  <div class="seller-info-container">
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="saleConfigPage"
      :extral-querys="extralQuerys"
    >
      <template v-slot:operate-right>
        <el-button type="primary" @click="dialogVisible = true">添加</el-button>
      </template>
    </drive-table>
    <dialog-cmp
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="620px"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="dialogVisible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
import {
  getParkSelect,
  saleConfigCreate,
  saleConfigDelete,
  saleConfigPage,
  saleConfigUpdate
} from '../../api'

export default {
  name: 'SellerInfoMaintain',
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      saleConfigPage,
      dialogTitle: '新增',
      dialogVisible: false,
      fromModel: {
        defaultConfig: true
      },
      extralQuerys: {
        type: 1
      }
    }
  },
  watch: {
    dialogVisible(val) {
      if (!val) {
        this.dialogTitle = this.$options.data().dialogTitle
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  created() {
    this.getPark()
  },
  methods: {
    getPark() {
      getParkSelect().then(res => {
        this.formConfigure.descriptors.parkId.options = res.map(item => {
          return {
            ...item,
            label: item.label,
            value: item.key
          }
        })
      })
    },
    editHandle(row) {
      this.fromModel = { ...row }
      this.dialogTitle = '编辑'
      this.dialogVisible = true
    },
    successHandle(text) {
      this.$toast.success(text + '成功')
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const { id = '' } = this.fromModel
        let url = saleConfigCreate
        if (id) url = saleConfigUpdate
        const params = {
          ...this.fromModel,
          type: 1
        }
        url(params).then(() => {
          this.successHandle(id ? '编辑' : '创建')
          this.dialogVisible = false
        })
      })
    },
    deleteHandle(row) {
      this.$confirm('确定删除此税率信息？').then(() => {
        saleConfigDelete({ id: row.id }).then(() => {
          this.successHandle('删除')
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.seller-info-container {
  :deep(.default-tips) {
    padding: 2px;
    font-size: 12px;
    border-radius: 3px;
    align-items: center;
    justify-content: center;
    @include font_color(--color-primary);
    @include background_color_mix(--color-primary, #ffffff, 80%);
    margin-right: 4px;
  }
}
</style>
