import request from '@/utils/request'

// 获取下拉园区列表
export function get_bill_select(params) {
  return request({
    url: `/inv/zacg/apply_record/get_bill_select`,
    method: 'get',
    params
  })
}

// 获取费用类型
export function getFeeType() {
  return request({
    url: `/inv/zacg/tax_config/get_fee_type`,
    method: 'get'
  })
}

// 获取开票状态
export function getBillStatus() {
  return request({
    url: `/inv/zacg/apply_record/get_bill_status`,
    method: 'get'
  })
}

// 获取合同选择
export function get_contract_select(params) {
  return request({
    url: `/inv/zacg/apply_record/get_contract_select`,
    method: 'get',
    params
  })
}
