import request from '@/utils/request'

// 获取下拉园区列表
export function getParkSelect() {
  return request({
    url: `/housing/park/select`,
    method: 'get'
  })
}
// 税率维护创建项目
export function taxConfigCreateTaxProject(data) {
  return request({
    url: `/inv/zacg/tax_config/create_tax_project`,
    method: 'post',
    data
  })
}
// 获取税率维护项目列表
export function taxConfigGetTaxProject(params) {
  return request({
    url: `/inv/zacg/tax_config/get_tax_project`,
    method: 'get',
    params
  })
}
// 税率维护更新项目
export function taxConfigUpdateTaxProject(data) {
  return request({
    url: `/inv/zacg/tax_config/update_tax_project`,
    method: 'post',
    data
  })
}
// 税率维护删除项目
export function taxConfigDeleteTaxProject(params) {
  return request({
    url: `/inv/zacg/tax_config/delete_project`,
    method: 'get',
    params
  })
}

// 获得项目税率维护列表
export function taxConfigTaxConfigList(params) {
  return request({
    url: `/inv/zacg/tax_config/tax_config_list`,
    method: 'get',
    params
  })
}
// 获取费用类型选择
export function taxConfigTaxGetFeeType() {
  return request({
    url: `/inv/zacg/tax_config/get_fee_type`,
    method: 'get'
  })
}
// 税率配置创建
export function taxConfigCreateTaxConfig(data) {
  return request({
    url: `/inv/zacg/tax_config/create_tax_config`,
    method: 'post',
    data
  })
}
// 税率配置更新
export function taxConfigUpdate(data) {
  return request({
    url: `/inv/zacg/tax_config/update`,
    method: 'post',
    data
  })
}
// 税率配置删除
export function taxConfigDelete(params) {
  return request({
    url: `/inv/zacg/tax_config/delete`,
    method: 'get',
    params
  })
}

// 获得销售方信息维护分页
export function saleConfigPage(params) {
  return request({
    url: `/inv/zacg/sale_config/page`,
    method: 'get',
    params
  })
}
// 创建销售方信息维护
export function saleConfigCreate(data) {
  return request({
    url: `/inv/zacg/sale_config/create`,
    method: 'post',
    data
  })
}
// 更新销售方信息维护
export function saleConfigUpdate(data) {
  return request({
    url: `/inv/zacg/sale_config/update`,
    method: 'post',
    data
  })
}
// 删除销售方信息维护
export function saleConfigDelete(params) {
  return request({
    url: `/inv/zacg/sale_config/delete`,
    method: 'get',
    params
  })
}

// 获取开票主体下拉
export function getInvBodyList(params) {
  return request({
    url: `/inv/zacg/apply_record/get_body_list`,
    method: 'get',
    params
  })
}
// 开票获取销售方信息下拉
export function getSaleBody(params) {
  return request({
    url: `/inv/zacg/sale_config/get_sale_body`,
    method: 'get',
    params
  })
}

// 获取发票种类选择
export function invApplyRecordKind() {
  return request({
    url: `/inv/zacg/apply_record/get_kind_select`,
    method: 'get'
  })
}
// 获取开票类型选择
export function invApplyRecordOpenType() {
  return request({
    url: `/inv/zacg/apply_record/get_open_type_select`,
    method: 'get'
  })
}
// 获取开票状态下拉
export function getBillStatus(params) {
  return request({
    url: `/inv/zacg/apply_record/get_bill_status`,
    method: 'get',
    params
  })
}
// 获取账单费率配置
export function getBillTaxConfig(params) {
  return request({
    url: `/inv/zacg/apply_record/get_bill_tax_config`,
    method: 'get',
    params
  })
}
// 创建发票申请记录
export function invApplyRecordCreate(data) {
  return request({
    url: `/inv/zacg/apply_record/create`,
    method: 'post',
    data
  })
}

// 获得发票申请记录分页
export function invApplyRecordPage(params) {
  return request({
    url: `/inv/zacg/apply_record/page`,
    method: 'get',
    params
  })
}
// 获取审核状态选择
export function invApplyRecordExamineStatus() {
  return request({
    url: `/inv/zacg/apply_record/get_examine_status`,
    method: 'get'
  })
}
// 获取分页查询条件-发票类型
export function invApplyRecordInvType() {
  return request({
    url: `/inv/zacg/apply_record/get_open_type`,
    method: 'get'
  })
}
// 获取分页查询条件-开票状态
export function invApplyRecordOpenStatus() {
  return request({
    url: `/inv/zacg/apply_record/get_open_status_select`,
    method: 'get'
  })
}

// 获得发票申请记录
export function invApplyRecordGet(params) {
  return request({
    url: `/inv/zacg/apply_record/get`,
    method: 'get',
    params
  })
}

// 从开票获得发票申请记录
export function invApplyRecordGetFn(params) {
  return request({
    url: `/inv/zacg/apply_record/get_by_bill_v1`,
    method: 'get',
    params
  })
}

// 发票申请退回
export function invApplyRecordBack(data) {
  return request({
    url: `/inv/zacg/apply_record/back`,
    method: 'post',
    data
  })
}
// 更新发票申请记录
export function invApplyRecordUpdate(data) {
  return request({
    url: `/inv/zacg/apply_record/update`,
    method: 'post',
    data
  })
}
// 确认开票
export function invApplyRecordConfirm(data) {
  return request({
    url: `/inv/zacg/apply_record/confirm`,
    method: 'post',
    data
  })
}
// 上传票据信息
export function invApplyRecordUploadInvoice(data) {
  return request({
    url: `/inv/zacg/apply_record/upload_invoice`,
    method: 'post',
    data
  })
}
// 发票冲红标记
export function invApplyRecordDestroy(data) {
  return request({
    url: `/inv/zacg/apply_record/destroy`,
    method: 'post',
    data
  })
}
// 获取园区、楼栋
export function invTaxConfigGetBuildPark(params) {
  return request({
    url: `/inv/zacg/tax_config/get_build_park`,
    method: 'get',
    params
  })
}
// 获取历史购买方信息
export function invApplyRecordHistory(params) {
  return request({
    url: `/inv/zacg/apply_record/get_history_apply`,
    method: 'get',
    params
  })
}
// 发票重发
export function invApplyRecordResend(params) {
  return request({
    url: `/inv/zacg/apply_record/resend`,
    method: 'get',
    params
  })
}
// 系统开票重试
export function invApplyRecordReTry(params) {
  return request({
    url: `/inv/zacg/apply_record/re_try`,
    method: 'get',
    params
  })
}
//获取全部园区
export function getAllParkApi(params) {
  return request({
    url: `/housing/park/listAll`,
    method: 'get',
    params
  })
}
// 导出
export function getApplyExport() {
  return `${process.env.VUE_APP_URL_PREFIX}/inv/zacg/apply_record/export_open_record`
}

//获取园区
export function getPark() {
  return request({
    url: `/housing/park/select_sub`,
    method: 'get'
  })
}

//获取开票信息
export function getInvoiceInfo(id) {
  return request({
    url: `/inv/zacg/apply_record/get_upload_detail?id=${id}`,
    method: 'get'
  })
}

//发票上传
export function invoicePost(data) {
  return request({
    url: `/inv/zacg/apply_record/upload_inv`,
    method: 'post',
    data
  })
}

//获取税率表
export function getTaxTable() {
  return request({
    url: `/inv/zacg/apply_record/get_tax_table`,
    method: 'get'
  })
}

//获取开票账单列表
export function getApplyBill(params) {
  return request({
    url: `/inv/zacg/apply_record/get_apply_bill`,
    method: 'get',
    params
  })
}

//账单类型
export function getFeeType() {
  return request({
    url: `/inv/zacg/tax_config/get_fee_type`,
    method: 'get'
  })
}

//撤回冲红申请
export function cancel_destroy(params) {
  return request({
    url: `/inv/zacg/apply_record/cancel_destroy`,
    method: 'get',
    params
  })
}

//撤回申请
export function withdraw(params) {
  return request({
    url: `/inv/zacg/apply_record/withdraw`,
    method: 'get',
    params
  })
}

//获取红冲记录
export function get_destroy_record(params) {
  return request({
    url: `/inv/zacg/apply_record/get_destroy_record`,
    method: 'get',
    params
  })
}

export function get_remark(params) {
  return request({
    url: `/inv/zacg/apply_record/gen_remark`,
    method: 'get',
    params
  })
}
