<template>
  <basic-drawer
    size="720px"
    title="发票上传"
    :visible.sync="_visible"
    @confirmDrawer="confirmDrawer"
  >
    <div v-loading="loading">
      <div class="font-size-14 line-height-32 p-b-10">开票信息</div>
      <el-table class="w100 m-b-18" :data="invoiceInfoData" border>
        <el-table-column prop="invAmount" label="开票金额(含税)">
          <template slot-scope="scope">
            <span class="color-primary">{{
              NumFormat(scope.row.invAmount)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceTypeStr" label="发票类型">
        </el-table-column>
        <el-table-column prop="openTypeInvoicingStr" label="开票类型">
        </el-table-column>
        <el-table-column prop="uploadInv" label="票据上传">
          <template slot-scope="scope">
            <span>{{ scope.row.uploadInv ? '已上传' : '未上传' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <driven-form
        v-if="_visible"
        ref="driven-form"
        :key="key"
        v-model="fromModel"
        label-position="top"
        :formConfigure="formConfigure"
      />
    </div>
  </basic-drawer>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import { getInvoiceInfo, invoicePost } from '../api'

export default {
  name: 'InvoiceUpload',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: [String, Number],
      default: ''
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  data() {
    return {
      key: new Date().getTime(),
      loading: false,
      invoiceInfoData: [],
      NumFormat,
      fromModel: {},
      formConfigure: {
        descriptors: {
          entId: {
            form: 'input',
            label: '付款企业',
            disabled: true,
            rule: [
              {
                type: 'string',
                message: '请输入付款企业'
              }
            ]
          },
          attachIds: {
            form: 'component',
            label: '发票上传',
            rule: [
              {
                type: 'array',
                required: true,
                message: '请上传发票'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              accept: 'application/pdf',
              maxLength: 1,
              maxSize: 20,
              limit: 1
            }
          }
        }
      }
    }
  },
  watch: {
    visible() {
      this.getInvoiceInfo()
    }
  },
  methods: {
    getInvoiceInfo() {
      this.loading = true
      getInvoiceInfo(this.id)
        .then(res => {
          const {
            buyerName = '',
            invoiceTypeStr = '',
            openTypeInvoicingStr = '',
            invAmount = 0,
            uploadInv = false,
            attach = {}
          } = res || {}
          this.invoiceInfoData = [
            {
              invoiceTypeStr,
              openTypeInvoicingStr,
              invAmount,
              uploadInv
            }
          ]
          this.$set(this.fromModel, 'entId', buyerName)
          if (attach?.informationAttach && attach?.informationAttach.length) {
            this.$set(this.fromModel, 'attachIds', attach?.informationAttach)
          } else {
            this.$set(this.fromModel, 'attachIds', [])
          }
          this.loading = false
          this.key = new Date().getTime()
        })
        .catch(() => {
          this.loading = false
        })
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { attachIds = [] } = this.fromModel
          if (attachIds.length) {
            this.fromModel.attachIds = attachIds.map(item => item.id)
          }
          const data = {
            ...this.fromModel,
            id: this.id
          }
          invoicePost(data).then(() => {
            this.$message.success('上传成功')
            this.$emit('update:visible', false)
            this.$emit('updateData')
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
