<template>
  <dialog-cmps
    v-if="billAdvice"
    title=""
    :visible.sync="payVisible"
    width="980px"
    :haveOperation="false"
  >
    <template v-slot:title>
      <div>
        <div class="flex justify-content-between">
          <div class="font-size-16">下载缴费通知单</div>
          <div class="font-size-12 p-r-28">
            <span class="info"> {{ billAdvice.parkName | noData }} | </span>
            <span class="info">
              打印时间：{{ billAdvice.printDate | noData }}
            </span>
          </div>
        </div>
        <div class="flex justify-content-between">
          <div>
            <span class="label-top">公司名称</span>
            <span class="value-top m-l-8">{{
              billAdvice.enterpriseName | noData
            }}</span>
          </div>
          <div>
            <span class="label-top">统一社会信用代码</span>
            <span class="value-top m-l-8">{{
              billAdvice.creditCode | noData
            }}</span>
          </div>
          <div>
            <span class="label-top">合同编号</span>
            <span class="value-top m-l-8">{{
              billAdvice.contractNo | noData
            }}</span>
          </div>
        </div>
      </div>
    </template>
    <div class="body">
      <div v-if="billAdvice.billTotal">
        <div class="color-basic font-size-14 m-b-16">账单信息</div>
        <el-row class="m-b-24 flex">
          <el-col :span="8">
            <span class="right font-size-14 info">账单编号</span>
            <span class="inline-block m-l-8 font-size-14">{{
              billAdvice.billTotal.billNo | noData
            }}</span>
          </el-col>
          <el-col :span="8">
            <span class="right font-size-14 info">账单生成日期</span>
            <span class="inline-block m-l-8 font-size-14">{{
              billAdvice.billTotal.createTime | noData
            }}</span>
          </el-col>
          <el-col :span="8">
            <span class="right font-size-14 info">账单周期</span>
            <span class="m-l-8 font-size-14"
              >{{ billAdvice.billTotal.rcvAmtSdt | noData }} -
              {{ billAdvice.billTotal.rcvAmtEdt | noData }}</span
            >
          </el-col>
        </el-row>
        <el-row class="m-b-24 flex">
          <el-col :span="8">
            <span class="right font-size-14 info">应缴时间</span>
            <span class="inline-block m-l-8 font-size-14">{{
              billAdvice.billTotal.payTime | noData
            }}</span>
          </el-col>
          <el-col :span="16">
            <span class="right font-size-14 info">账单总金额</span>
            <span class="color-warning m-l-8 font-size-14"
              >￥{{ NumFormat(billAdvice.billTotal.amount) }}</span
            >
          </el-col>
        </el-row>
        <el-row class="m-b-24 flex">
          <el-col :span="8">
            <span class="font-size-14 info">已缴总金额</span>
            <span class="color-warning m-l-8 font-size-14"
              >￥{{ NumFormat(billAdvice.billTotal.paidAmount) }}</span
            >
          </el-col>
          <el-col :span="16">
            <span class="font-size-14 info">未缴总金额</span>
            <span class="color-warning m-l-8 font-size-14"
              >{{ billAdvice.billTotal.unpaidAmountUp | noData }}（￥{{
                NumFormat(billAdvice.billTotal.unpaidAmount)
              }})</span
            >
          </el-col>
        </el-row>
      </div>
      <div class="color-basic font-size-14 m-b-16">费用明细</div>
      <drive-table
        ref="drive-table"
        :columns="tableColumnPayDetails"
        :table-data="billAdvice.billList"
      >
      </drive-table>
      <div class="info m-b-24 m-t-10 font-size-12 line-height-20">
        {{ billAdvice.prefix | noData }}
      </div>
    </div>
  </dialog-cmps>
</template>

<script>
import DialogCmps from '../DialogCmp'
import { getBillAdvice } from '../../api'
import ColumnMixins from '../../column/column'
import { NumFormat } from '@/utils/tools'

export default {
  name: 'PaymentMemo',
  mixins: [ColumnMixins],
  props: {
    id: {
      type: Number,
      default: 0
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  components: { DialogCmps },
  data() {
    return {
      NumFormat,
      payVisible: false,
      billAdvice: {}
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getBillAdvice(this.id, () => {
          this.payVisible = val
        })
      } else {
        this.payVisible = val
      }
    },
    payVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    getBillAdvice(id, cb) {
      getBillAdvice(id).then(res => {
        this.billAdvice = res
        cb && cb()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog .el-dialog__body {
  max-height: 0 !important;
}

:deep(.el-dialog__header) {
  height: 100px !important;
}

:deep(.el-form-item--small.el-form-item) {
  margin-bottom: 0 !important;
}

.info {
  color: #999;
}

.label-top {
  font-size: 14px;
  font-weight: 350;
  color: rgba(0, 0, 0, 0.9);
  line-height: 22px;
}
.color-basic {
  color: rgba(0, 0, 0, 0.9);
}

.value-top {
  font-size: 14px;
  font-weight: 350;
  color: #ed7b2f;
  line-height: 22px;
}

:deep(.w-date) {
  width: 140px !important;
}
:deep(.w-date .el-input__inner) {
  padding-right: 10px !important;
}

.black {
  color: #1a1a1a;
}
</style>
