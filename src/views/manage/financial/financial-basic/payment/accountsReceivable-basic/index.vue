<!-- 合同应收 -->
<template>
  <basic-card>
    <template slot="right">
      <div>
        <el-button size="mini" @click="exportExcel" class="m-l-8" type="info">
          <svg-icon icon-class="cloud-download" />
          <span>导出</span></el-button
        >
      </div>
    </template>
    <div>
      <div class="financial-content bg-white">
        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>账单总数</span>
              </div>
              <div
                class="acconutname m-t-17 font-size-24 flex align-items-center"
              >
                <span>{{ numberAll.billNmu | noData }}</span>
                <span class="font-size-18 font-strong-600 m-l-4"> 份</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>应收总金额</span>
              </div>
              <div class="acconut-name m-t-17 font-size-24">
                <span class="font-size-18">￥</span>
                <span>{{ numberAll.receivableMoney | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>实收总金额</span>
              </div>
              <div class="acconut-name m-t-17 font-size-24">
                <span class="font-size-18">￥</span>
                <span>{{ numberAll.receivedMoney | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center pointer">
                <span class="line"></span>
                <span>待收总金额</span>
              </div>
              <div class="payname m-t-17 font-size-24">
                <span class="font-size-18">￥</span>
                <span>{{ numberAll.dueInMoney | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh" style="width: 80%">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>回款率</span>
              </div>
              <div class="w100 m-t-18">
                <el-progress
                  :text-inside="true"
                  :stroke-width="20"
                  :percentage="rate"
                  status="success"
                ></el-progress>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
    />
    <!--    表单-->
    <div class="m-b-6">
      <el-form ref="fromAccountInfo" :model="fromTableInfo">
        <el-row>
          <el-col :span="14">
            <el-form-item class="m-l-8">
              <button-switch
                :label-content="labelContent"
                @toggle="chageTab"
              ></button-switch>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <div class="w100 flex justify-content-end">
              <el-form-item
                class="m-l-8"
                v-if="fromTableInfo.bsType === 1 || fromTableInfo.blType === 1"
              >
                <el-select
                  style="width: 140px"
                  v-model="extralQuerys.openStatus"
                  placeholder="请选择开票状态"
                  @change="openStatusChange"
                  :popper-append-to-body="false"
                  clearable
                >
                  <el-option
                    v-for="item in openStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="m-l-8" v-if="activeName === 1">
                <el-select
                  style="width: 140px"
                  v-model="fromTableInfo.bsType"
                  placeholder="请选择合同类型"
                  @change="changeContractFn"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="item in contractOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="m-l-8" v-if="activeName === 2">
                <el-select
                  style="width: 140px"
                  v-model="fromTableInfo.blType"
                  placeholder="请选择合同类型"
                  @change="changeContractBlTypeFn"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="item in contractAllOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                class="m-l-8"
                v-if="activeName === 3 || activeName === 4"
              >
                <el-select
                  style="width: 140px"
                  v-model="fromTableInfo.blType"
                  placeholder="请选择合同类型"
                  @change="changeContractBlTypeFn"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="item in hydropowerOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <template v-if="!isAllDownload">
                <el-form-item
                  class="m-l-8"
                  v-if="billType === 1 || billType === 2"
                >
                  <el-select
                    style="width: 140px"
                    clearable
                    v-model="fromTableInfo.overdueStatus"
                    placeholder="请选择预警状态"
                    @input="searchInfo($event, 'overdueStatus')"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in overdueList"
                      :key="item.key"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  class="m-l-8"
                  v-if="billType === 1 || billType === 2"
                >
                  <el-select
                    style="width: 140px"
                    clearable
                    v-model="fromTableInfo.status"
                    placeholder="请选择账单状态"
                    @input="searchInfo($event, 'status')"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in billStatusList"
                      :key="item.key"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  class="m-l-8"
                  v-if="billType === 1 || billType === 2"
                >
                  <el-select
                    style="width: 140px"
                    clearable
                    v-model="fromTableInfo.billStatus"
                    @input="searchInfo($event, 'billStatus')"
                    placeholder="请选择核销状态"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in verificationList"
                      :key="item.key"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <div
                  ref="chevron"
                  class="chevron pointer m-l-8"
                  @click="downHandler"
                >
                  <svg-icon icon-class="chevron-up" />
                </div>
                <div ref="showDown" class="show-wrapper bg-white p-8">
                  <el-form-item class="m-l-8">
                    <el-input
                      clearable
                      :placeholder="`请输入${
                        activeName === 1 || activeName === 3 || activeName === 4
                          ? '企业'
                          : '主体'
                      }名称${activeName === 2 ? '或姓名' : ''}`"
                      v-model="fromTableInfo.enterpriseName"
                      @input="searchInfo($event, 'enterpriseName')"
                    >
                      <i
                        slot="prefix"
                        class="el-input__icon el-icon-search"
                      ></i>
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    class="m-l-8"
                    v-if="billType === 1 || billType === 2"
                  >
                    <el-date-picker
                      v-model="fromTableInfo.signDateType"
                      type="daterange"
                      align="right"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd"
                      @change="searchInfo($event, 'signDateType')"
                      :picker-options="pickerOptions"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
              </template>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <drive-table
      v-if="billType === 1"
      ref="drive-table"
      :columns="tableColumnBill"
      :api-fn="getFinanceList"
      :extralQuerys="extralQuerys"
    >
    </drive-table>
    <drive-table
      v-if="billType === 2"
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="payBillPageApi"
      :extralQuerys="extralQuerys"
    >
    </drive-table>
    <drive-table
      v-if="billType === 3"
      ref="drive-table"
      :columns="tableColumnFirm"
      :api-fn="apartmentApi"
      :extralQuerys="extralQuerys"
    >
    </drive-table>

    <!--    弹框-->
    <dialog-cmp
      title="线下核销"
      :visible.sync="downloadVisible"
      width="27%"
      @confirmDialog="downloadVisibleDialog"
    >
      <div>
        <div>
          <span>已选择</span>
          <span class="red">{{ '1项' | noData }}</span>
          <span>确定后将会以</span>
          <span class="color-warning">压缩包（ZIP）形式下载到您的电脑</span>
        </div>
        <div class="color-info font-size-12 m-t-18">
          提示：系统会自动给企业推送缴费通知单，或者您解压已下载的文件，逐个打印后下发
        </div>
      </div>
    </dialog-cmp>
  </basic-card>
</template>

<script>
import BasicTab from '@/views/manage/financial/financial-basic/payment/paymentPlan-basic/components/BasicTab/index'
import { formatGetParams, NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import {
  getFinanceList,
  getEntFinanceList,
  statisticalData,
  getParkList,
  getPayBillPage,
  getSinglePageList,
  getPayBillPower,
  getPayEntPower,
  statisticalDataPower,
  contractListExcel,
  billListExcel,
  contractEntListExcel,
  singleExcel,
  powerExcel,
  powerEntExcel,
  getBillStatus
} from '../accountsReceivable-basic/api/index'
import { getFeePriceTitles } from '@/views/manage/financial/financial-basic/api'
import ButtonSwitch from '@/views/manage/financial/financial-basic/payment/components/ButtonSwitch/index.vue'
import {
  getCostStatus,
  getOverdueStatus
} from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/utils/status'
import downloads from '@/utils/download'
import dayjs from 'dayjs'
export default {
  name: 'contractReceivables',
  components: {
    ButtonSwitch,
    BasicTab
  },
  mixins: [ColumnMixins],
  data() {
    return {
      excelFlag: 1,
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime())
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(
                start.getTime() - 3600 * 1000 * 24 * (start.getDay() - 1)
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本季度',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              const month = start.getMonth()
              if (month < 3) {
                start.setMonth(0)
              } else if (month < 6) {
                start.setMonth(3)
              } else if (month < 9) {
                start.setMonth(6)
              } else {
                start.setMonth(9)
              }
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本年',

            onClick(picker) {
              const end = new Date()
              const start = new Date()
              //起始时间为本年的第一天
              start.setDate(1)
              start.setMonth(0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近3年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '3年以前',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 10)
              end.setTime(end.getTime() - 3600 * 1000 * 24 * 365 * 3)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      labelContent: [
        {
          label: '租赁账单',
          value: 1
        },
        {
          label: '公寓账单',
          value: 2,
          permission: 'APARTMENT'
        },
        // {
        //   label: '水费账单',
        //   value: 3,
        //   permission: 'WATER'
        // },
        // {
        //   label: '电费账单',
        //   value: 4,
        //   permission: 'ELECTRICITY'
        // },
        {
          label: '违约金账单',
          value: 5,
          permission: 'LIQUIDATED_DAMAGES'
        }
      ],
      apartmentApi: null,
      isEnt: 0,
      activeName: 1,
      contractAllOption: [
        {
          label: '按账期',
          value: 0
        },
        {
          label: '按费用',
          value: 1
        },
        {
          label: '按企业',
          value: 2
        },
        {
          label: '按个人',
          value: 3
        }
      ],
      contractOption: [
        {
          label: '按账期',
          value: 0
        },
        {
          label: '按费用',
          value: 1
        },
        {
          label: '按企业',
          value: 2
        }
      ],
      hydropowerOption: [
        {
          label: '按费用',
          value: 1
        },
        {
          label: '按企业',
          value: 2
        }
      ],
      NumFormat,
      getFinanceList,
      payBillPageApi: getPayBillPage,
      getSinglePageList,
      downloadVisible: false,
      offlineVisible: false,
      getEntFinanceList,
      accountInfo: {}, // 账户信息
      extralQuerys: {
        signDateType: [],
        enterpriseName: '',
        status: null,
        billStatus: null,
        overdueStatus: null,
        parkId: null,
        bsType: 0, //合同类型 0-租赁合同；2-公寓合同
        blType: 0, //0全部  1按业 2个人
        type: 10, // 3水费账单-按企业 4电费账单-按企业 10水费账单-按费用 11电费账单-按费用
        openStatus: ''
      },
      dataObservationform: {
        day: ''
      },
      verificationList: [
        {
          label: '未核销',
          value: 1
        },
        {
          label: '部分核销',
          value: 2
        },
        {
          label: '全额核销',
          value: 3
        },
        {
          label: '已作废',
          value: 4
        }
      ], //核销状态
      enterpriseName: '',
      dayList: [
        {
          label: '按日',
          value: 1
        },
        {
          label: '按周',
          value: 2
        },
        {
          label: '按月',
          value: 3
        },
        {
          label: '按季度',
          value: 4
        },
        {
          label: '按年',
          value: 4
        }
      ], //应缴时间
      payTimeTypeList: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '本周',
          value: 2
        },
        {
          label: '本月',
          value: 3
        },
        {
          label: '本季度',
          value: 4
        },
        {
          label: '本年',
          value: 5
        },
        {
          label: '近3年',
          value: 6
        },
        {
          label: '3年前',
          value: 7
        }
      ],
      current: 0,
      list: [],
      fromTableInfo: {
        signDateType: [],
        overdueStatus: null,
        status: null,
        enterpriseName: '',
        billStatus: null,
        bsType: 0, //合同类型 0-租赁合同；2-公寓合同
        blType: 0 //0全部  1按业 2个人
      },
      isAllDownload: false,
      overdueList: [
        {
          label: '未逾期',
          value: 1
        },
        {
          label: '短时逾期',
          value: 2
        },
        {
          label: '长时逾期',
          value: 3
        }
      ], //签订时间
      billStatusList: [
        {
          label: '执行中',
          value: 1
        },
        {
          label: '已完成',
          value: 2
        },
        {
          label: '已终止',
          value: 3
        }
      ], //账单状态
      billType: 1,
      billNmu: null,
      receivableMoney: null,
      dueInMoney: null,
      receivedMoney: null,
      rate: 0,
      flag: true,
      tableKey: Math.random(),
      compare: 1,
      compareList: [
        {
          label: '日',
          value: 1
        },
        {
          label: '周',
          value: 2
        },
        {
          label: '月',
          value: 3
        },
        {
          label: '季度',
          value: 4
        },
        {
          label: '年',
          value: 5
        }
      ],
      numberAll: {},
      down: true,
      openStatus: []
    }
  },
  mounted() {
    this.getParkList()
    this.getBillStatus()
    this.getFeePriceTitles(4)
  },
  methods: {
    getBillStatus() {
      getBillStatus().then(res => {
        this.openStatus = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    downHandler() {
      this.down = !this.down
      this.$refs.chevron.style.transform = !this.down ? 'rotate(180deg)' : ''
      this.$refs.showDown.style.transform = !this.down
        ? 'scaleX(1)'
        : 'scaleX(0)'
    },
    // 导出excel
    exportExcel() {
      const exportMethods = {
        1: {
          0: () => {
            // 调用按账期导出接口 contractListExcel
            let url = contractListExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          1: () => {
            // 调用按费用导出接口 billListExcel
            let url = billListExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          2: () => {
            // 调用按企业导出接口 contractEntListExcel
            let url = contractEntListExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          3: () => {
            // 调用按个人导出接口 singleExcel
            let url = singleExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          }
        },
        2: {
          0: () => {
            // 调用按账期导出接口 contractListExcel
            let url = contractListExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          1: () => {
            // 调用按费用导出接口 billListExcel
            let url = billListExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          2: () => {
            // 调用按企业导出接口 contractEntListExcel
            let url = contractEntListExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          3: () => {
            // 调用按个人导出接口 singleExcel
            let url = singleExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          }
        },
        3: {
          1: () => {
            // 调用水电费按账期导出接口 powerExcel
            let url = powerExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          2: () => {
            // 调用水电费按企业导出接口 powerEntExcel
            let url = powerEntExcel() + '?'
            return (url += formatGetParams({
              ...this.$refs['drive-table'].querys,
              type: 3
            }))
          }
        },
        4: {
          1: () => {
            // 调用水电费按账期导出接口 powerExcel
            let url = powerExcel() + '?'
            return (url += formatGetParams(this.$refs['drive-table'].querys))
          },
          2: () => {
            // 调用水电费按企业导出接口 powerEntExcel
            let url = powerEntExcel() + '?'
            return (url += formatGetParams({
              ...this.$refs['drive-table'].querys,
              type: 4
            }))
          }
        }
      }
      downloads.requestDownload(
        exportMethods[this.excelFlag][this.isEnt](),
        'excel',
          dayjs().format('YYYY-MM-DD') + '应收账单台账.xls'
      )
    },
    changeContractBlTypeFn(e) {
      this.billType = e <= 2 ? e + 1 : e
      this.isEnt = e
      this.fromTableInfo.blType = e
      this.$nextTick(() => {
        if (e === 0) {
          this.extralQuerys.blType = 0
        } else if (e === 1) {
          this.extralQuerys.blType = 1
        } else if (e === 2) {
          this.extralQuerys.blType = 2
        }
        // this.$refs['drive-table']?.triggerSearch()
      })
    },
    openStatusChange() {
      this.$refs['drive-table']?.triggerSearch()
    },
    changeContractFn(e) {
      this.isEnt = e
      this.billType = e + 1
      // this.$refs['drive-table']?.triggerSearch()
    },
    resetExtralQuerys(type) {
      this.extralQuerys = {
        signDateType: [],
        enterpriseName: '',
        status: null,
        billStatus: null,
        overdueStatus: null,
        parkId: this.extralQuerys.parkId,
        bsType: type === 1 ? 0 : 2,
        blType: 0,
        type: this.billType === 2 ? this.activeName : '',
        openStatus: ''
      }
      if (this.billType === 2) {
        this.extralQuerys.type = type === 3 ? 10 : 11
      } else {
        this.extralQuerys.type = this.activeName
      }
    },
    // 处理头部数据
    dataHander(data) {
      if (!data) return 0
      if (data < 0) {
        return `${data}`
      } else {
        return `+${data}`
      }
    },
    compareSelectChange(e) {
      this.compare = e
      if (this.activeName === 1 || this.activeName === 2) {
        this.statisticalData()
      } else {
        this.hydropowerStatistical()
      }
    },
    // 水电费表头
    hydropowerTitles(type) {
      const dynamicHeader = {
        label: type === 3 ? '水费(元)' : '电费(元)',
        prop: 'pwAmount',
        minWidth: '120px',
        render: (h, scope) => {
          return (
            <div class={'color-warning'}>{NumFormat(scope.row.pwAmount)}</div>
          )
        }
      }
      this.getTableColumnBill()
      this.getTableColumnFirm()
      this.tableColumnBill.splice(5, 0, dynamicHeader)
      this.tableColumnFirm.splice(3, 0, dynamicHeader)
      this.tableKey = Math.random()
    },
    // 获取表头
    getFeePriceTitles(type) {
      getFeePriceTitles(type).then(res => {
        const dynamicHeader = res.map(item => {
          return {
            label: item.name,
            prop: item.code,
            minWidth: '120px',
            render: (h, scope) => {
              return (
                <div class={'color-warning'}>
                  {NumFormat(scope.row[item.code])}
                </div>
              )
            }
          }
        })
        this.getTableColumnBill()
        this.getTableColumnFirm()
        this.tableColumnBill.splice(7, 0, ...dynamicHeader)
        this.tableColumnFirm.splice(5, 0, ...dynamicHeader)
        this.tableKey = Math.random()
      })
    },
    getTableColumnBill() {
      this.tableColumnBill = [
        {
          label:
            this.activeName === 1 ||
            this.activeName === 3 ||
            this.activeName === 4
              ? '企业名称'
              : '签约主体',
          width: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                <div>
                  {scope.row.enterStatus === 1 ||
                  scope.row.enterStatus === 2 ? (
                    <div
                      type="primary"
                      onClick={() => {
                        this.goDetail(scope.row)
                      }}
                      class={'pointer line-1 color-primary'}
                    >
                      {scope.row.enterpriseName}
                      {scope.row.enterpriseNameTxt ? (
                        <el-tooltip
                          class="item"
                          effect="dark"
                          content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                          placement="top"
                        >
                          <svg-icon class="m-l-8" icon-class="link-m" />
                        </el-tooltip>
                      ) : (
                        ''
                      )}
                    </div>
                  ) : (
                    <div class={'line-1'}>{scope.row.enterpriseName}</div>
                  )}
                </div>
              </div>
            )
          }
        },
        {
          label: '账期',
          prop: 'contractNo',
          width: 180,
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '应收金额(元)',
          prop: 'amount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '核销金额(元)',
          prop: 'written',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.written)}</div>
            )
          }
        },
        {
          label: '退款金额(元)',
          prop: 'refund',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.refund)}</div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'paidAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'collectStatus',
          render: (h, scope) => {
            return (
              <div>
                {getCostStatus(
                  h,
                  scope.row.collectStatus,
                  scope.row.collectStatusStr
                )}
              </div>
            )
          }
        },
        {
          label: '逾期状态',
          prop: 'overdueStatus',
          render: (h, scope) => {
            return <div>{getOverdueStatus(h, scope.row.overdueStatus)}</div>
          }
        },
        {
          label: '实收进度',
          prop: 'process',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-progress
                  text-inside={true}
                  stroke-width={16}
                  percentage={Number(scope.row.process) || 0}
                  status="success"
                ></el-progress>
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div class={'flex align-items-center'}>
                <el-link
                  type="primary"
                  class="m-r-24"
                  onClick={() => {
                    this.goBillDetail(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DETAIL}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
                {/*<el-dropdown trigger="click">*/}
                {/*  <span class="color-primary pointer">更多</span>*/}
                {/*  <el-dropdown-menu*/}
                {/*    slot="dropdown"*/}
                {/*    style="width:130px;text-align: center;"*/}
                {/*  >*/}
                {/*    <el-dropdown-item>*/}
                {/*      <div class="color-warning">线下核销</div>*/}
                {/*    </el-dropdown-item>*/}
                {/*    <el-dropdown-item>*/}
                {/*      <div class="color-primary w100">下载缴费通知单</div>*/}
                {/*    </el-dropdown-item>*/}
                {/*  </el-dropdown-menu>*/}
                {/*</el-dropdown>*/}
              </div>
            )
          }
        }
      ]
    },
    getTableColumnFirm() {
      this.tableColumnFirm = [
        {
          label:
            this.activeName === 1 ||
            this.activeName === 3 ||
            this.activeName === 4
              ? '企业名称'
              : '签约主体',
          width: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.enterpriseName ? (
                  <div>
                    {scope.row.enterStatus === 1 ||
                    scope.row.enterStatus === 2 ? (
                      <div
                        type="primary"
                        onClick={() => {
                          this.goDetail(scope.row)
                        }}
                        class={'pointer line-1 color-primary'}
                      >
                        {scope.row.enterpriseName}
                        {scope.row.enterpriseNameTxt ? (
                          <el-tooltip
                            class="item"
                            effect="dark"
                            content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                            placement="top"
                          >
                            <svg-icon class="m-l-8" icon-class="link-m" />
                          </el-tooltip>
                        ) : (
                          ''
                        )}
                      </div>
                    ) : (
                      <div class={'line-1'}>{scope.row.enterpriseName}</div>
                    )}
                  </div>
                ) : (
                  '-'
                )}
              </div>
            )
          }
        },
        {
          label: '账单总数',
          prop: 'count'
        },
        {
          label: '应收金额(元)',
          prop: 'amount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '核销金额(元)',
          prop: 'written',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.written)}</div>
            )
          }
        },
        {
          label: '退款金额(元)',
          prop: 'refund',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.refund)}</div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'paidAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '实收进度',
          prop: 'process',
          width: 200,
          render: (h, scope) => {
            return (
              <div>
                <el-progress
                  text-inside={true}
                  stroke-width={16}
                  percentage={Number(scope.row.process) || 0}
                  status="success"
                ></el-progress>
              </div>
            )
          }
        }
      ]
    },
    hydropowerStatistical() {
      statisticalDataPower({
        type: this.compare,
        bsType: this.activeName
      }).then(res => {
        this.numberAll = res
        const receivedMoney = res.receivedMoney || 0
        const receivableMoney = res.receivableMoney || 0
        if (receivedMoney === 0) return (this.rate = 0)
        this.rate = Number(((receivedMoney / receivableMoney) * 100).toFixed(2))
      })
    },
    statisticalData() {
      statisticalData({
        type: this.compare,
        bsType: this.activeName === 1 ? 0 : 2
      }).then(res => {
        const {
          billItem,
          billNmu,
          billRatio,
          dueInMoney,
          dueInMoneyItem,
          dueInMoneyRatio,
          receivableMoney,
          receivableMoneyItem,
          receivableMoneyRatio,
          receivedMoney,
          receivedMoneyItem,
          receivedMoneyRatio
        } = res
        this.numberAll = {
          billItem,
          billNmu,
          billRatio,
          dueInMoney,
          dueInMoneyItem,
          dueInMoneyRatio,
          receivableMoney,
          receivableMoneyItem,
          receivableMoneyRatio,
          receivedMoney,
          receivedMoneyItem,
          receivedMoneyRatio
        }
        this.rate = Number(((receivedMoney / receivableMoney) * 100).toFixed(2))
      })
    },
    getParkList() {
      getParkList().then(res => {
        const arr = [{ label: '全部', value: 0 }]
        const arr1 = res.map(item => {
          return {
            label: item.park,
            value: item.id
          }
        })
        this.list = arr.concat(arr1)
      })
    },
    goOffline() {
      this.offlineVisible = true
    },
    goDownload() {
      this.downloadVisible = true
    },
    downloadVisibleDialog() {
      this.billWriteOffVisible = false
    },
    selectionChange(val) {
      val.length > 0
        ? (this.isAllDownload = true)
        : (this.isAllDownload = false)
    },
    //全选下载
    allDownload() {
      this.downloadVisible = true
    },
    //切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.parkId = e || null
      this.$refs['drive-table'] &&
        this.$refs['drive-table'].triggerSearch(this.extralQuerys)
    },
    chageTab(type) {
      this.excelFlag = type
      this.activeName = type
      this.fromTableInfo = {
        signDateType: [],
        overdueStatus: null,
        status: null,
        enterpriseName: '',
        billStatus: null,
        bsType: 0,
        blType: type === 3 || type === 4 ? 1 : 0
      }
      this.tableColumnBill[0].label =
        type === 1 || type === 3 || type === 4 ? '企业名称' : '签约主体'
      this.tableColumn[0].label =
        type === 1 || type === 3 || type === 4 ? '企业名称' : '签约主体'
      this.tableColumnFirm[0].label =
        type === 1 || type === 3 || type === 4 ? '企业名称' : '签约主体'
      if (type === 3 || type === 4) {
        this.hydropowerSetting()
        this.billType = 2
        this.isEnt = 1
        this.hydropowerTitles(type)
      } else {
        this.isEnt = 0
        this.payBillPageApi = getPayBillPage
        this.getFeePriceTitles(type === 1 ? 4 : 8)
        this.billType = 1
      }
      this.resetExtralQuerys(type)
      this.extralQuerys.bsType = type === 1 ? 0 : 2
      this.$nextTick(() => {
        this.$refs['drive-table']?.triggerSearch()
      })
    },
    hydropowerSetting() {
      this.payBillPageApi = getPayBillPower
      if (this.isEnt === 1) {
        this.extralQuerys.type = this.activeName === 3 ? 10 : 11
        this.apartmentApi = getPayBillPower
      } else {
        this.extralQuerys.type = this.activeName
        this.apartmentApi = getPayEntPower
      }
    },
    goBillDetail(row) {
      let { id, orderId } = row
      this.$router.push({
        path: 'accountsReceivable/accountsReceivableDetails',
        query: {
          id,
          orderId,
          type: this.activeName
        }
      })
    },
    goDetail(row) {
      let { entId, orderId, enterStatus = 0 } = row
      if (enterStatus === 1 || enterStatus === 2) {
        this.$router.push({
          path: '/business/enterpriseDetails',
          query: {
            id: entId,
            orderId
          }
        })
      }
    },
    // 搜索企业名称或者账单编号
    searchInfo(e, type) {
      if (type === 'signDateType') {
        this.extralQuerys[type] = e ? JSON.stringify(e) : ''
      } else {
        this.extralQuerys[type] = e ? e : ''
      }
      this.$refs['drive-table'].triggerSearch()
    }
  },
  watch: {
    isEnt: {
      handler(val) {
        this.apartmentApi = null
        if (this.activeName === 3 || this.activeName === 4) {
          this.hydropowerSetting()
        } else {
          this.payBillPageApi = getPayBillPage
          if (val === 0) {
            this.apartmentApi = getFinanceList
          } else if (val === 1) {
            this.apartmentApi = getPayBillPage
          } else if (val === 2) {
            this.apartmentApi = getEntFinanceList
          } else if (val === 3) {
            this.apartmentApi = getSinglePageList
          }
        }
      },
      immediate: true,
      deep: true
    },
    activeName: {
      handler(val) {
        if (val === 1 || val === 2) {
          this.statisticalData()
        } else {
          this.hydropowerStatistical()
        }
      },
      immediate: true,
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.show-wrapper {
  transform: scaleX(0);
  transition: linear 0.3s;
  position: absolute;
  right: 0;
  top: 48px;
  max-width: 1214px;
  z-index: 9999;
  box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.08),
    0 16px 24px 2px rgba(0, 0, 0, 0.04), 0 6px 30px 5px rgba(0, 0, 0, 0.05);
  opacity: 1;
  border: 1px solid #dcdcdc;
  border-radius: 3px;
  .show-item {
    max-height: 130px;
  }
  &:before {
    display: block;
    content: '';
    width: 10px;
    height: 10px;
    border-radius: 2px;
    @include background_color(--color-white);
    position: absolute;
    top: -3px;
    right: 1%;
    transform: rotate(45deg) translateX(-50%);
    opacity: 1;
    border-left: 1px solid #dcdcdc;
    border-top: 1px solid #dcdcdc;
  }
}
.chevron {
  transition: linear 0.3s;
  height: 25px;
  margin-top: 6px;
}
.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: auto;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 220px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}

//超过宽度显示省略号
.ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.red {
  color: #e34d59;
}
</style>
