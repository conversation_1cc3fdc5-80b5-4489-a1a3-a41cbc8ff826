import { NumFormat } from '@/utils/tools'
import { noData } from '@/filter'

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '企业名称',
          showOverflowTooltip: true,
          search: {
            type: 'input'
          },
          render: (h, scope) => {
            return (
              <span>
                {
                  scope.row.entId ? <span class={'color-primary'} onClick={() => {
                    this.goEntDetail(scope.row)
                  }}>{scope.row.entName}</span> : <span>{scope.row.entName}</span>
                }
              </span>
            )
          }
        },
        {
          label: '实收进度',
          prop: 'payPercent',
          render: (h, scope) => {
            const payPercent = Number(scope.row.payPercent) || 0
            return (
              <el-progress
                text-inside={true}
                stroke-width={16}
                percentage={payPercent > 100 ? 100 : payPercent}
                status="success"
                format={() => {
                    return `${payPercent}%`
                  }
                }
              >
              </el-progress>
            )
          }
        },
        {
          prop: 'area',
          label: '合同总面积(㎡)'
        },
        {
          prop: 'deposit',
          label: '应缴保证金(元)',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.deposit)}</div>
            )
          }
        },
        {
          prop: 'actualDeposit',
          label: '实缴保证金(元)',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.actualDeposit)}</div>
            )
          }
        },
        {
          prop: 'arrearsDeposit',
          label: '合计保证金(元)',
          render: (h, scope) => {
            return (
              <div class={this.getArrearsClass(scope.row.arrearsDeposit)}>{NumFormat(scope.row.arrearsDeposit)}</div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          fixed: 'right',
          render: (h, scope) => {
            return (
              <el-link type="primary" onClick={() => {
                this.goView(scope.row)
              }}>
                查看
              </el-link>
            )
          }
        }
      ],
      // 实缴记录列表
      tableColumnPaidRecord: [
        {
          prop: 'changeTime',
          label: '交易时间',
          minWidth: '150px'
        },
        {
          prop: 'opTypeStr',
          label: '交易类型',
          minWidth: '150px',
          render: (h, scope) => {
            const type = scope.row.opType === 1 ? 'primary' : 'danger'
            return <el-tag type={type}>{scope.row.opTypeStr}</el-tag>
          }
        },
        {
          prop: 'amountChange',
          label: '操作金额(元)',
          minWidth: '150px',
          render: (h, scope) => {
            return (
                <span class={'color-warning'}>
                {NumFormat(scope.row.amountChange)}
              </span>
            )
          }
        },
        {
          prop: 'payer',
          label: '付款方',
          minWidth: '220px',
          showOverflowTooltip: true
        },
        {
          prop: 'afterAmount',
          label: '交易后实缴(元)',
          minWidth: '150px',
          render: (h, scope) => {
            return (
                <span class={'color-warning'}>
                {NumFormat(scope.row.afterAmount)}
              </span>
            )
          }
        },
        {
          prop: 'businessTypeStr',
          label: '业务来源',
          minWidth: '150px',
          render: (h, scope) => {
            return (
                <el-link
                    type={'primary'}
                    onClick={() => {
                      this.goSourceHandle(scope.row)
                    }}
                >
                  {scope.row.businessTypeStr}
                </el-link>
            )
          }
        },
        {
          prop: 'txnStsStr',
          label: '交易状态',
          minWidth: '150px',
          render: (h, scope) => {
            return <div>{scope.row.txnStsStr}</div>
          }
        }
      ],
      // 应缴依据列表
      tableColumnBasisPayable: [
        {
          label: '合同/协议编号',
          prop: 'contractNo',
          minWidth: 230,
          showOverflowTooltip: true,
          render: (h, { row }) => {
            return (
                <div class={'inline'}>
                  {
                    row.contractId ?
                      <el-link class={'inline'} type={'primary'} onClick={() => this.toContract(row)}>
                        {row.contractNo}
                      </el-link> : <span>{ noData(row.contractNo) }</span>
                  }
                </div>
            )
          }
        },
        {
          label: '合同/协议状态',
          prop: 'statusStr',
          minWidth: 120
        },
        {
          label: '租赁房源',
          prop: 'roomStr',
          minWidth: 230,
        },
        {
          label: '合同/协议面积(㎡)',
          prop: 'area',
          minWidth: 150,
          align: 'center',
        },
        {
          label: '保证金基数(元)',
          prop: 'deposit',
          minWidth: 150,
          align: 'right',
          render: (h, scope) => {
            return (
                <span class={'color-warning'}>
                {NumFormat(scope.row.deposit)}
              </span>
            )
          }
        },
        {
          label: '基数状态',
          prop: 'depositStatusStr',
          minWidth: 150,
          align: 'center'
        },
        {
          label: '应缴保证金(元)',
          prop: 'payableDeposit',
          minWidth: 150,
          align: 'right',
          render: (h, scope) => {
            return (
                <span class={'color-warning'}>
                  {
                    scope.row.depositStatus === 2 ?
                      <span class={'color-warning'}>{NumFormat(scope.row.deposit)}</span> :
                      <span class={'color-warning'}>0</span>
                  }
              </span>
            )
          }
        }
      ]
    }
  }
}
