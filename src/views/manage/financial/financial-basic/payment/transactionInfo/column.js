import { NumFormat } from '@/utils/tools'
import { noData } from '@/filter'
export default {
  data() {
    return {
      tableColumn: [
        {
          label: '企业名称',
          width: 300,
          prop: 'enterpriseName',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <div
                onClick={() => {
                  this.goDetail(scope.row)
                }}
                class={'line-1 color-primary pointer'}
              >
                {scope.row.enterpriseName || noData}
              </div>
            )
          }
        },
        {
          label: '登记时间',
          prop: 'checkTime'
        },
        {
          label: '交易方式',
          prop: 'transactionStr'
        },
        {
          label: '登记余额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '财务确认',
          prop: 'acStatusStr'
        },
        {
          label: '确认余额(元)',
          prop: 'acAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.acAmount)}</div>
            )
          }
        },
        {
          label: '到账状态',
          prop: 'payStatusStr'
        },
        {
          label: '登记方式',
          prop: 'registrationStr'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  v-permission={this.routeButtonsPermission.VIEW}
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.previewEvent(scope.row)
                  }}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
