// 费用名称
export const billStatus = [
  { label: '全部', value: null },
  { label: '租赁费', value: 63 },
  { label: '售房款', value: 64 },
  { label: '保证金', value: 65 },
  { label: '定向开发费', value: 67 }
]

// 费用名称
export function getbillStatus(h, val) {
  switch (val) {
    case 63:
      return <basic-tag type="primary" label="租赁费"></basic-tag>
    case 64:
      return <basic-tag type="danger" label="售房款"></basic-tag>
    case 65:
      return <basic-tag type="success" label="保证金"></basic-tag>
    case 66:
      return <basic-tag type="info" label="定向开发费"></basic-tag>
    default:
      return '-'
  }
}

// 归集状态
export const imputationStatus = [
  { label: '不可归集', value: 0 },
  { label: '可归集', value: 1 },
  { label: '已归集', value: 2 }
]

// 归集状态
export function getImputationStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="warning" label="不可归集"></basic-tag>
    case 1:
      return <basic-tag isDot type="danger" label="可归集"></basic-tag>
    case 2:
      return <basic-tag isDot type="info" label="已归集"></basic-tag>
    default:
      return <basic-tag isDot type="warning" label="不可归集"></basic-tag>
  }
}

// 对账状态
export const ReconciliationStatus = [
  { label: '全部', value: null },
  { label: '未对账', value: 0 },
  { label: '平账', value: 1 },
  { label: '不平', value: 2 }
]

export function getReconciliationStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="danger" label="未对账"></basic-tag>
    case 1:
      return <basic-tag isDot type="info" label="平账"></basic-tag>
    case 2:
      return <basic-tag isDot type="warning" label="不平"></basic-tag>
    default:
      return <basic-tag isDot type="danger" label="未对账"></basic-tag>
  }
}

// 交易状态
export const transactionStatus = [
  { label: '不限', value: null },
  { label: '初始', value: 1 },
  { label: '成功', value: 2 },
  { label: '失败', value: 3 },
  { label: '超时', value: 4 }
]

export function getTransactionStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="danger" label="处理中"></basic-tag>
    case 1:
      return <basic-tag isDot type="success" label="成功"></basic-tag>
    case 2:
      return <basic-tag isDot type="warning" label="失败"></basic-tag>
    default:
      return '-'
  }
}

// 核销方式
export const payType = [
  { label: '自动核销', value: 0 },
  { label: '管理核销', value: 1 },
  { label: '企业核销', value: 2 },
  { label: '现金核销', value: 4 },
  { label: '转账核销', value: 6 },
  { label: '承兑汇票', value: 8 },
  { label: '其他核销', value: 10 },
  { label: '疫情减免', value: 15 }
]

export function getPayType(h, val) {
  switch (val) {
    case 0:
      return <basic-tag type="danger" label="自动核销"></basic-tag>
    case 1:
      return <basic-tag type="info" label="管理核销"></basic-tag>
    case 2:
      return <basic-tag type="warning" label="企业核销"></basic-tag>
    case 4:
      return <basic-tag label="现金核销"></basic-tag>
    case 6:
      return <basic-tag type="info" label="转账核销"></basic-tag>
    case 8:
      return <basic-tag type="warning" label="承兑汇票"></basic-tag>
    case 10:
      return <basic-tag type="danger" label="其他核销"></basic-tag>
    case 15:
      return <basic-tag type="danger" label="疫情减免"></basic-tag>
    default:
      return
  }
}
