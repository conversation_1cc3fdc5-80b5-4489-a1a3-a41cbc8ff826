import {
  // billStatus,
  // getbillStatus,
  imputationStatus,
  getImputationStatus,
  ReconciliationStatus,
  getReconciliationStatus,
  transactionStatus,
  getTransactionStatus,
  payType,
  getPayType
} from '../utils/status.js'
import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        // {
        //   prop: 'parkId',
        //   label: '园区',
        //   hidden: true,
        //   search: {
        //     type: 'select',
        //     options: []
        //   }
        // },
        {
          prop: 'entName',
          label: '企业名称',
          fixed: true,
          search: {
            type: 'input'
          },
          width: 320
          // render: (h, scope) => {
          //   return (
          //     <div>
          //       <el-link
          //         type="primary"
          //         onClick={() => {
          //           this.goDetails(scope.row)
          //         }}
          //       >
          //         {scope.row.entName ? scope.row.entName : ''}
          //       </el-link>
          //     </div>
          //   )
          // }
        },
        // {
        //   prop: 'parkName',
        //   label: '园区名称',
        //   // width: 150
        // },
        {
          prop: 'amount',
          label: '交易金额(元)',
          // width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          prop: 'transactTime',
          label: '交易时间',
          // width: 200,
          search: {
            type: 'daterange'
          }
        },
        {
          prop: 'billCode',
          label: '账单编号'
          // width: 150
        },
        // {
        //   prop: 'feeType',
        //   label: '费用名称',
        //   // width: 100,
        //   search: {
        //     type: 'select',
        //     options: billStatus
        //   },
        //   render: (h, scope) => {
        //     return <div>{getbillStatus(h, scope.row.feeType)}</div>
        //   }
        // },
        // {
        //   prop: 'feeAc',
        //   label: '缴费账号',
        //   // width: 120
        // },
        {
          prop: 'isLine',
          label: '核销方式',
          // width: 120,
          search: {
            type: 'select',
            options: payType
          },
          render: (h, scope) => {
            return <div>{getPayType(h, scope.row.isLine)}</div>
          }
        },
        {
          prop: 'isLine',
          label: '核销类型'
          // width: 120,
        },
        {
          prop: 'bankPaySts',
          label: '交易状态',
          // width: 120,
          search: {
            type: 'select',
            options: transactionStatus
          },
          render: (h, scope) => {
            return <div>{getTransactionStatus(h, scope.row.bankPaySts)}</div>
          }
        },
        {
          prop: 'checkSts',
          label: '对账状态',
          // width: 120,
          search: {
            type: 'select',
            options: ReconciliationStatus
          },
          render: (h, scope) => {
            return <div>{getReconciliationStatus(h, scope.row.checkSts)}</div>
          }
        },
        {
          prop: 'backSts',
          label: '归集状态',
          // width: 120,
          search: {
            type: 'select',
            options: imputationStatus
          },
          render: (h, scope) => {
            return <div>{getImputationStatus(h, scope.row.backSts)}</div>
          }
        }
      ]
    }
  }
}
