import { NumFormat, parseTime } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '企业名称',
          minWidth: 220,
          showOverflowTooltip: true,
          prop: 'entName',
          render: (h, scope) => {
            return (
              <span
                onClick={() => {
                  this.goDetail(scope.row)
                }}
                class={'pointer color-primary'}
              >
                {scope.row.entName}
              </span>
            )
          }
        },
        {
          label: '付款总金额(元)',
          prop: 'totalAmount',
          minWidth: 140,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.totalAmount)}
              </div>
            )
          }
        },
        {
          label: '余额付款(元)',
          prop: 'walletAmount',
          minWidth: 140,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.walletAmount)}
              </div>
            )
          }
        },
        {
          label: '保证金付款(元)',
          prop: 'depositAmount',
          minWidth: 140,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.depositAmount)}
              </div>
            )
          }
        },
        {
          label: '状态',
          prop: 'state',
          minWidth: 140,
          render: (h, scope) => {
            const obj = {
              0: 'primary',
              1: 'danger',
              2: 'success',
              3: 'info',
              4: 'info'
            }
            return (
              <basic-tag
                isDot
                type={obj[scope.row.state]}
                label={scope.row.stateStr}
              />
            )
          }
        },
        {
          label: '申请时间',
          prop: 'applyTime',
          minWidth: 140,
          render: (h, scope) => {
            return <div>{parseTime(scope.row.applyTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.previewEvent(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DETAIL}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
