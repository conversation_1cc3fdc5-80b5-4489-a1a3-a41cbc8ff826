<template>
  <div class="draw-details-container min-h100 bg-white">
    <basic-card>
      <div class="draw-title">
        <div class="font-strong">
          {{ detailsInfo.entName | noData }}
        </div>
        <flow-form v-if="detailsInfo.examineFlag" />
      </div>
      <el-row class="font-size-14 line-height-22">
        <el-col :span="5">
          <div class="font-size-12 m-b-8 color-info">所属园区</div>
          <div class="font-strong line-1">
            {{ detailsInfo.parkName | noData }}
          </div>
        </el-col>
        <el-col :span="4">
          <div class="font-size-12 m-b-8 color-info">主要联系人</div>
          <div class="font-strong">{{ detailsInfo.contact | noData }}</div>
        </el-col>
        <el-col :span="4">
          <div class="font-size-12 m-b-8 color-info">联系方式</div>
          <div class="font-strong">{{ detailsInfo.contactPhone | noData }}</div>
        </el-col>
        <el-col :span="4">
          <div class="font-size-12 m-b-8 color-info">申请时间</div>
          <div class="font-strong line-1">
            {{ detailsInfo.applyTime | noData }}
          </div>
        </el-col>
        <el-col :span="4">
          <div class="font-size-12 m-b-8 color-info">申请状态</div>
          <div class="font-strong">{{ detailsInfo.stateStr | noData }}</div>
        </el-col>
      </el-row>
      <div class="m-t-24">
        <div
          class="flex justify-content-between align-items-center m-b-16 p-t-16"
        >
          <div>申请付款金额</div>
        </div>
        <drive-table
          ref="drive-table"
          :table-data="detailsInfo.feeInfoList || []"
          :columns="tableColumn"
        />
      </div>
      <div class="m-t-24">
        <div
          class="flex justify-content-between align-items-center m-b-16 p-t-16"
        >
          <div>收款账户信息</div>
        </div>
        <drive-table
          ref="drive-table"
          :table-data="[detailsInfo]"
          :columns="accountTableColumn"
        />
      </div>
      <div class="font-size-14 line-height-22 m-t-16">
        <div class="m-b-8 color-info">备注内容</div>
        <p style="word-break: break-all">
          {{ detailsInfo.content | noData }}
        </p>
      </div>
      <div class="font-size-14 line-height-22 m-t-16">
        <div class="m-b-8 color-info">相关附件</div>
        <files-list
          v-if="attach && attach.length"
          :files="attach"
          onlyForView
        />
        <div v-else class="value color-text-primary">暂无附件</div>
      </div>
    </basic-card>
  </div>
</template>

<script>
import { NumFormat, parseTime } from '@/utils/tools'
import ColumnMixins from './column/column'
import formConfigureData from './descriptor'
import FlowForm from '@/components/FlowForm/index.vue'
import { ticketDetails } from './api/index'
import FilesList from '@/components/Uploader/files'

export default {
  name: 'DrawMoneyHistoryDetails',
  mixins: [ColumnMixins, formConfigureData],
  components: {
    FilesList,
    FlowForm
  },
  data() {
    return {
      parseTime,
      NumFormat,
      detailsInfo: {}
    }
  },
  computed: {
    attach() {
      const attachMap = this.detailsInfo.attachMap || {}
      return attachMap.drawMoney || []
    }
  },
  mounted() {
    const { id } = this.$route.query
    id && this.ticketDetails(id)
  },
  methods: {
    goDetail(row) {
      const { payType, bodyId } = row
      const url = {
        1: {
          path: '/account/entAccountDetail',
          query: {}
        },
        2: {
          path: '/account/depositCollection',
          query: {
            entId: this.detailsInfo.entId
          }
        }
      }
      this.$router.push({
        path: url[payType].path,
        query: {
          id: bodyId,
          ...url[payType].query
        }
      })
    },
    ticketDetails(e) {
      ticketDetails(e).then(res => {
        if (res) {
          this.detailsInfo = res
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.draw-details-container {
  .draw-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    margin-bottom: 16px;
    border-bottom: 1px solid #e7e7e7;
  }
}
</style>
