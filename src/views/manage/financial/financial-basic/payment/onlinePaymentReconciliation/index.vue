<!-- 在线交费对账 -->
<template>
  <basic-card>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="onlinePayList"
      :search-querys-hook="searchQueryHook"
    >
    </drive-table>

    <dialog-cmp
      title="订单支付明细"
      :visible.sync="visible"
      width="90%"
      :haveOperation="true"
    >
      <drive-table
        v-if="visible"
        ref="drive-table-2"
        :columns="tableColumn2"
        :api-fn="listLocalPay"
        :extral-querys="extralQuerys"
        :search-querys-hook="searchQueryHook2"
      />
    </dialog-cmp>
  </basic-card>
</template>

<script>
import ColumnMixins from './column/column'
import { onlinePayList, listLocalPay } from './api'

export default {
  name: 'onlinePaymentReconciliation',
  mixins: [ColumnMixins],
  data() {
    return {
      onlinePayList,
      listLocalPay,
      visible: false,
      extralQuerys: {
        controlId: null
      }
    }
  },
  methods: {
    // 详情
    goTableDetails(e) {
      this.extralQuerys.controlId = e.id
      this.visible = true
    },

    // 重置搜索参数
    searchQueryHook(e) {
      const temp = e
      const [startDate = '', endDate = ''] = e.billDate || []
      if (e.billDate && e.billDate.length > 0) {
        temp.startDate = startDate
        temp.endDate = endDate
      }
      delete e.billDate
      return temp
    },
    searchQueryHook2(e) {
      e.parkName = e.parkName === '全部' ? '' : e.parkName
      e.tranNo = e.orderNum
      return {
        ...e
      }
    }
  }
}
</script>

<style scoped></style>
