<template>
  <div class="p-b-16">
    <basic-card class="m-b-8">
      <div class="flex align-items-center">
        <div class="font-size-16 line-height-22" style="font-weight: 550">
          {{ planDetailData.entName }}
        </div>
        <div class="m-l-10 font-size-14 color-text-secondary">
          {{ formatRoom(planDetailData.roomInfos) }}
        </div>
      </div>
    </basic-card>

    <basic-card title="计划信息" class="m-b-8">
      <el-tag
        class="m-l-10"
        slot="tag"
        :type="getPlanStatusForTag(planDetailData.status).label"
        >{{ getPlanStatusForTag(planDetailData.status).label }}
      </el-tag>
      <div class="house-wrapper">
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                合同编号
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.contractNum | noData }}
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                本期金额(元)
              </div>
              <div class="font-size-14 color-warning value">
                {{ NumFormat(planDetailData.money) }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                费用名称
              </div>
              <div class="font-size-14 color-primary value">
                {{ planDetailData.planName | noData }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">期数</div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.periodsNum | noData }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                应交日期
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.overdueDate | noData }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                计划编号
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.planNum | noData }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </basic-card>

    <basic-card
      title="账单信息"
      class="m-b-8"
      v-if="planDetailData.status !== 1"
    >
      <div class="house-wrapper">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                账单金额(元)
              </div>
              <div class="font-size-14 color-warning value">
                {{ NumFormat(planDetailData.paymentBill.payAmount) }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-custom">
                已核销金额(元)
              </div>
              <div class="font-size-14 color-warning value">
                {{ NumFormat(planDetailData.paymentBill.payActualAmount) }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                生成时间
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.paymentBill.implTime | noData }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                应交日期
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.paymentBill.overdueDate | noData }}
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                逾期天数(天)
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.paymentBill.overdueDays | noData }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                账单状态
              </div>
              <div class="font-size-14 color-success value">
                {{ getBillStatusLabel(planDetailData.paymentBill.billStatus) }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                开票状态
              </div>
              <div class="font-size-14 color-primary value">
                {{
                  getTicketstatusLabel(planDetailData.paymentBill.isTicketed)
                }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                账单编号
              </div>
              <div class="font-size-14 color-text-primary value">
                {{ planDetailData.paymentBill.billCode | noData }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </basic-card>

    <basic-card
      title="核销记录"
      class="m-b-8"
      v-if="planDetailData.status !== 1"
    >
      <div class="m-b-16 flex align-items-center justify-content-between">
        <div class="flex font-size-14 line-height-22">
          <div class="color-text-secondary">核销金额(元)</div>
          <div class="m-l-8 color-warning">
            {{ '¥:' + NumFormat(planDetailData.payCollectTotalMoney) }}
          </div>
        </div>

        <div
          v-if="planDetailData.payPlanWriteOffProgressVo"
          class="flex align-items-center font-14"
        >
          <div class="m-r-12">核销进度</div>
          <div class="m-r-8 flex">
            <el-popover placement="top-start" width="280" trigger="hover">
              <div class="flex align-items-center font-14">
                <div class="m-r-6">{{ calculationPercent }}%</div>
                <div class="m-r-4">已经核销金额(元) :</div>
                <div class="color-warning">
                  {{ NumFormat(planDetailData.payPlanWriteOffProgressVo.pay) }}
                </div>
              </div>
              <div slot="reference" style="width: 120px">
                <el-progress
                  :percentage="calculationPercent"
                  color="#42d379"
                  class="pointer"
                  :show-text="false"
                  :width="8"
                />
              </div>
            </el-popover>
          </div>
          <div class="text-wrapper">
            未核销金额(元) :
            <span class="text-success">
              {{ NumFormat(planDetailData.payPlanWriteOffProgressVo.unPay) }}
            </span>
          </div>
        </div>
      </div>
      <drive-table
        height="342px"
        :columns="tableColumnRecord"
        :table-data="planDetailData.collectVoList"
      />
    </basic-card>

    <basic-card
      title="退款记录"
      class="m-b-8"
      v-if="planDetailData.status !== 1"
    >
      <div class="m-b-16 flex align-items-center justify-content-between">
        <div class="flex font-size-14 line-height-22">
          <div class="color-text-secondary">退款金额(元)</div>
          <div class="m-l-8 color-warning">
            {{ '¥:' + NumFormat(planDetailData.payCollectTotalMoney) }}
          </div>
        </div>
      </div>
      <drive-table
        height="342px"
        :columns="tableColumnRefund"
        :table-data="planDetailData.returnVoList"
      />
    </basic-card>
  </div>
</template>

<script>
import ColumnMixins from './column/column'
import { decimalDivision, NumFormat } from '@/utils/tools'
import {
  getBillStatusLabel,
  getPlanStatusForTag,
  getTicketstatusLabel
} from './utils/status'
import { getPayPlanInfoById } from './api'
import { noData } from '@/filter'

export default {
  name: 'detail',
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      getPlanStatusForTag,
      getBillStatusLabel,
      getTicketstatusLabel,
      planDetailData: {
        paymentBill: {},
        payPlanWriteOffProgressVo: {
          pay: 0,
          total: 0
        },
        collectVoList: [],
        returnVoList: []
      },
      paymentBillData: {},
      calculationPercent: 0
    }
  },
  filters: {
    noData
  },
  created() {
    if (this.$route.query.id) {
      let id = this.$route.query.id
      this.getPayPlanInfoById(id)
    }
  },
  methods: {
    // 根据ID获取详情数据
    getPayPlanInfoById(id) {
      getPayPlanInfoById(id).then(res => {
        this.planDetailData = res
        this.paymentBillData = res.paymentBill
        if (
          res.payPlanWriteOffProgressVo &&
          res.payPlanWriteOffProgressVo.pay
        ) {
          this.calculationPercent =
            decimalDivision(
              res.payPlanWriteOffProgressVo.pay,
              res.payPlanWriteOffProgressVo.total
            ).toFixed(2) * 100
        }
      })
    },
    formatRoom(list) {
      if (!list) return ''
      const arr = []
      list.forEach(x => {
        arr.push(x.parkName + x.build + x.room)
      })
      return arr.toString().replaceAll(',', '，')
    }
  }
}
</script>

<style lang="scss" scoped>
.house-wrapper {
  .label {
    width: 85px;
    text-align-last: right;
    margin-right: 16px;
  }

  .label-custom {
    width: 136.01px;
  }

  .value {
    flex: 1;
  }

  &.no-border {
    border: none;
  }

  &.healthy-wrapper {
    padding-top: 24px;
    padding-bottom: 0;
  }

  .member-table {
    padding: 0 6px;
  }
}
</style>
