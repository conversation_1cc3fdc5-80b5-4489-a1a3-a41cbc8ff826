import {
  getPayCycle,
  getPayType,
  getPlanStatus,
  getPlanType,
  // getTicketstatus,
  planStatusOptions,
  planTypeOptions
} from '../utils/status.js'
import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '计划编号',
          prop: 'parkName'
        },
        {
          label: '企业名称',
          prop: 'entName',
          search: {
            type: 'input'
          }
          // render: (h, scope) => {
          //   return (
          //     <el-link
          //       type="primary"
          //       onClick={() => {
          //         this.goContractDetails(scope.row)
          //       }}
          //     >
          //       {scope.row.contractNo}
          //     </el-link>
          //   )
          // }
        },
        // {
        //   prop: 'parkId',
        //   label: '园区',
        //   hidden: true,
        //   search: {
        //     type: 'select',
        //     options: []
        //   }
        // },
        // {
        //   label: '园区名称',
        //   prop: 'parkName'
        // },
        {
          label: '费用名称',
          prop: 'planNameId',
          search: {
            type: 'select',
            options: planTypeOptions
          },
          render: (h, scope) => {
            return <div>{getPlanType(h, scope.row.planNameId)}</div>
          }
        },
        {
          label: '期数',
          prop: 'periodsNum'
        },
        {
          label: '付款方式',
          prop: 'payType',
          render: (h, scope) => {
            return <div>{getPayCycle(h, scope.row.payType)}</div>
          }
        },
        {
          label: '起始日期',
          prop: 'startDate'
        },
        {
          label: '终止日期',
          prop: 'endDate'
        },
        {
          label: '本期金额(元)',
          prop: 'money',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.money)}</div>
            )
          }
        },
        {
          // 筛选条件
          label: '计划状态',
          prop: 'status',
          search: {
            type: 'select',
            options: planStatusOptions
          },
          render: (h, scope) => {
            return <div>{getPlanStatus(h, scope.row.status)}</div>
          }
        },
        {
          label: '账单金额(元)',
          prop: 'billMoney',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.billMoney)}
              </div>
            )
          }
        },
        // {
        //   label: '已核销金额(元)',
        //   prop: 'amountReceived',
        //   render: (h, scope) => {
        //     return (
        //       <div class={'color-warning'}>
        //         {NumFormat(scope.row.amountReceived)}
        //       </div>
        //     )
        //   }
        // },
        //
        // {
        //   label: '费用周期',
        //   prop: 'feeCycle',
        //   hidden: true,
        //   search: {
        //     type: 'daterange'
        //   }
        // },
        // {
        //   // 筛选条件
        //   label: '开票状态',
        //   prop: 'isTicketed',
        //   render: (h, scope) => {
        //     return <div>{getTicketstatus(h, scope.row.isTicketed)}</div>
        //   }
        // },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onclick={() => {
                    this.goTableDetails(scope.row)
                  }}
                  class="m-r-15"
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ],
      tableColumnRecord: [
        {
          prop: 'amount',
          label: '核销金额(元)',
          align: 'right',
          render: (h, scope) => {
            return <div class="text-warning">{NumFormat(scope.row.amount)}</div>
          }
        },
        {
          prop: 'dealTime',
          label: '核销时间',
          align: 'center'
        },
        {
          prop: 'payType',
          label: '核销方式',
          render: (h, scope) => {
            return <div>{getPayType(h, scope.row.payType)}</div>
          }
        },
        {
          prop: 'result',
          label: '核销状态'
        },
        {
          prop: 'createBy',
          label: '操作人'
        },
        {
          prop: 'comments',
          label: '备注'
        }
      ],
      tableColumnRefund: [
        {
          prop: 'returnAmount',
          label: '退款金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">
                {NumFormat(scope.row.returnAmount)}
              </div>
            )
          }
        },
        {
          prop: 'returnTime',
          label: '退款时间',
          align: 'center'
        },
        {
          prop: 'returnReason',
          label: '退款原因'
        },
        {
          prop: 'returnStatus',
          label: '退款状态',
          render: (h, scope) => {
            // 后端说只查了退款成功的数据，所以这边返回的值都是1。。。
            return <div>{scope.row.returnStatus === 1 ? '已退款' : '-'}</div>
          }
        },
        {
          prop: 'createBy',
          label: '操作人'
        }
      ]
    }
  }
}
