import AccountBalance from './components/AccountBalance'
import MarginDeposit from './components/MarginDeposit'
import PaymentAmount from './components/PaymentAmount'
import { validateBankNumber, validateContact } from '@/utils/validate'

export default {
  components: { AccountBalance, MarginDeposit, PaymentAmount },
  data() {
    return {
      formConfigure: {
        descriptors: {
          entId: {
            form: 'select',
            label: '付款企业',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择付款企业'
              }
            ],
            attrs: {
              filterable: true
            },
            events: {
              change: this.entIdChange
            }
          },
          balance: {
            form: 'component',
            label: '账户余额',
            rule: [
              {
                type: 'array',
                message: '请输入账户余额'
              }
            ],
            render: () => {
              return <account-balance ref={'accountBakance'} />
            }
          },
          marginDeposit: {
            form: 'component',
            label: '保证金缴存',
            rule: [
              {
                type: 'array',
                message: '请输入保证金缴存'
              }
            ],
            render: () => {
              return (
                <margin-deposit
                  ref={'marginDeposit'}
                  entId={this.fromModel.entId}
                />
              )
            }
          },
          acBkNme: {
            form: 'input',
            label: '收款开户行',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入收款开户行'
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          acBkNo: {
            form: 'input',
            label: '银行账户',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输银行账户'
              },
              {
                validator: validateBankNumber
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          feeInfo: {
            form: 'component',
            label: '付款金额',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请输入付款金额'
              }
            ],
            render: () => {
              return (
                <payment-amount
                  ref={'paymentAmount'}
                  v-model={this.fromModel.feeInfo}
                />
              )
            }
          },
          content: {
            form: 'input',
            label: '备注内容',
            rule: [
              {
                type: 'string',
                message: '请输入备注内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true
            }
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'drawMoney'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 20,
              limit: 3
            }
          },
          contact: {
            form: 'input',
            label: '主要联系人',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主要联系人'
              }
            ],
            attrs: {
              maxLength: 15
            },
            customTips: () => {
              return (
                <div>默认引用了该企业常用联系人信息，具体以输入信息为准</div>
              )
            }
          },
          contactPhone: {
            form: 'select',
            label: '联系方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式',
              },
              { validator: validateContact }
            ],
            attrs: {
              filterable: true,
              allowCreate: true,
              defaultFirstOption: true,
            },
            options: []
          },
          examineFlag: {
            form: 'switch',
            label: '需要审核',
            hidden: true,
            rule: [
              {
                required: false,
                type: 'boolean',
                message: '请选择需要审核'
              }
            ],
            customRight: () => {
              return <div class={'audit-tips'}>是</div>
            },
            customTips: () => {
              return (
                <div>
                  根据企业此次实际申请情况来确定是否需要审核。如果选择否则直接进入办理阶段
                </div>
              )
            }
          }
        }
      }
    }
  }
}
