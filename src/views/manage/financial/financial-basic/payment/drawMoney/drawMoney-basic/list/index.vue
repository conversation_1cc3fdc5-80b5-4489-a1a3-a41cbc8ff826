<!-- 付款申请 -->
<template>
  <basic-card>
    <div>
      <div class="financial-content bg-white">
        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>申请总数</span>
              </div>
              <div
                class="acconutname m-t-17 font-size-24 flex align-items-center"
              >
                <span>{{ accountInfo.applyNum | noData }}</span>
                <span class="font-size-18 font-strong-600 m-l-4"> 次</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>申请已付款金额</span>
              </div>
              <div
                class="acconut-name m-t-17 font-size-24"
                style="color: #ed7b2f"
              >
                <span class="font-size-18">￥</span>
                <span>{{
                  NumFormat(accountInfo.withdrawalMoney) | noData
                }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center pointer">
                <span>剩余可付款预存</span>
              </div>
              <div class="payname m-t-17 font-size-24" style="color: #ed7b2f">
                <span class="font-size-18">￥</span>
                <span>{{
                  NumFormat(accountInfo.remainingMoney) | noData
                }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center pointer">
                <span>剩余可付款保证金</span>
              </div>
              <div class="payname m-t-17 font-size-24" style="color: #ed7b2f">
                <span class="font-size-18">￥</span>
                <span>{{
                  NumFormat(accountInfo.remainDepositMoney) | noData
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
      :disabled="reqLoading"
    >
      <template v-slot:right>
        <el-switch
          @change="searchHandle"
          :disabled="reqLoading"
          v-model="extralQuerys.selfFlag"
          active-text="只看我录入的"
        >
        </el-switch>
      </template>
    </basic-tab>
    <!--    表单-->
    <div class="m-b-6">
      <el-form ref="fromAccountInfo" :model="fromTableInfo">
        <div class="flex justify-content-between">
          <div class="flex">
            <el-form-item>
              <el-input
                style="width: 288px"
                clearable
                @input="handleInput"
                placeholder="请输入企业名称"
                v-model="fromTableInfo.entName"
              >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </el-form-item>
            <el-form-item class="m-l-12">
              <el-select
                @change="sourceChange"
                clearable
                v-model="fromTableInfo.submitUnit"
                placeholder="请选择来源"
              >
                <el-option
                  v-for="item in sourceOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="flex">
            <el-button
              size="mini"
              style="height: 30px"
              type="primary"
              @click="applyPaymentHandle"
              >申请付款</el-button
            >
            <el-button
              v-permission="routeButtonsPermission.LEADING_OUT"
              size="mini"
              style="height: 30px"
              type="primary"
              @click="exportExcel"
            >
              <svg-icon icon-class="cloud-upload" />
              <span>{{ routeButtonsTitle.LEADING_OUT }}台账</span></el-button
            >
          </div>
        </div>
      </el-form>
    </div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="getList"
      :extral-querys="extralQuerys"
      @setLoading="setLoading"
    />
    <basic-drawer
      title="申请付款"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        v-if="drawerVisible"
        ref="driven-form"
        v-model="fromModel"
        label-position="top"
        :formConfigure="formConfigure"
      />
    </basic-drawer>
  </basic-card>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import { formatGetParams, NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import DescriptorMixin from './descriptor'
import {
  getTop,
  getList,
  getExportExcel,
  getWithdrawalRefundApply,
  getEntList,
  withdrawalCreate,
  getSelectState
} from './api/index'
import downloads from '@/utils/download'
import dayJs from 'dayjs'
import { getContacts } from '@/api/common'

export default {
  name: 'DrawMoneyList',
  components: {
    BasicTab
  },
  mixins: [ColumnMixins, DescriptorMixin],
  data() {
    return {
      sourceOptions: [
        {
          label: '园区发起',
          value: 1
        },
        {
          label: '企业发起',
          value: 2
        }
      ],
      NumFormat,
      getList,
      accountInfo: {}, // 账户信息
      current: -1,
      list: [],
      extralQuerys: {
        type: 0,
        entName: '',
        state: -1,
        selfFlag: false
      },
      fromTableInfo: {
        entName: ''
      },
      compare: 1,
      drawerVisible: false,
      fromModel: {
        examineFlag: true
      },
      reqLoading: true
    }
  },
  watch: {
    drawerVisible(val) {
      if (!val) {
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  deactivated() {
    this.reqLoading = true
  },
  activated() {
    this.getEntList()
    this.getSelectState()
    this.getTop()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    setLoading(val) {
      this.reqLoading = val
    },
    searchHandle() {
      this.$refs['drive-table'].triggerSearch()
    },
    sourceChange(val) {
      this.extralQuerys.submitUnit = val
      this.$refs['drive-table'].triggerSearch()
    },
    getSelectState() {
      getSelectState().then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.key }
        })
        this.list = [{ label: '全部', value: -1 }, ...list]
      })
    },
    getContacts(row) {
      getContacts(row).then(res => {
        this.formConfigure.descriptors.contactPhone.options = res.map(item => {
          return {
            label: `${item.contact}-(${item.phone})`,
            value: item.phone
          }
        })
      })
    },
    async entIdChange(e) {
      if (!e) return false
      await this.getContacts(e)
      const row = this.formConfigure.descriptors.entId.options.find(
        item => item.value === e
      )
      if (row.contact) {
        this.$set(this.fromModel, 'contact', row.contact)
      }
      if (row.phone) {
        this.$set(this.fromModel, 'contactPhone', row.phone)
      }
      getWithdrawalRefundApply({ entId: e }).then(res => {
        const list = res || []
        const balances = list.filter(item => item.payType === 1) || []
        this.$refs.accountBakance.initData(balances)
        const marginDeposit = list.filter(item => item.payType === 2) || []
        this.$refs.marginDeposit.initData(marginDeposit)
        this.$refs.paymentAmount.initData(list)
      })
    },
    getEntList() {
      getEntList().then(res => {
        this.formConfigure.descriptors.entId.options = res.map(item => {
          return {
            ...item,
            label: item.enterpriseName,
            value: item.id
          }
        })
      })
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const feeInfo = this.fromModel.feeInfo || []
        const row = feeInfo.find(item => item.payAmount > item.payableAmount)
        if (row) return this.$toast.warning('申请付款金额不能超出可付款金额')
        const attachIds = this.fromModel.attachIds || []
        const params = {
          ...this.fromModel,
          attachIds: attachIds.map(item => item.id)
        }
        withdrawalCreate(params).then(() => {
          this.$toast.success('提交成功')
          this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
          this.getTop()
          this.drawerVisible = false
        })
      })
    },
    applyPaymentHandle() {
      this.drawerVisible = true
    },
    //导出excel
    exportExcel() {
      const time = dayJs().format('YYYY-MM-DD')
      let url = getExportExcel() + '?'
      url += formatGetParams(this.$refs['drive-table'].querys)
      downloads.requestDownload(url, 'excel', `${time}台账明细.xls`)
    },
    handleInput() {
      this.extralQuerys.entName = this.fromTableInfo.entName
      this.$refs['drive-table'].triggerSearch()
    },
    getTop() {
      getTop({ topType: this.compare, type: 0 }).then(res => {
        if (res) {
          this.accountInfo = {
            ...res
          }
        }
      })
    },
    previewEvent(e) {
      this.$router.push({
        path: 'drawMoneyList/drawMoneyDetails',
        query: {
          id: e.id,
          orderId: e.orderId
        }
      })
    },
    //切换
    tabsChange(e) {
      if (e === this.current) return false
      this.reqLoading = true
      this.current = e
      this.extralQuerys.state = e
      this.$refs['drive-table'].triggerSearch()
    },
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form) {
  .empty-content {
    padding: 20px 0 !important;
  }
}
:deep(.examineFlagKey) {
  position: relative;
  .custom-right {
    position: absolute;
    left: 56px;
    top: 50px;
  }
}
.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: auto;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}
</style>
