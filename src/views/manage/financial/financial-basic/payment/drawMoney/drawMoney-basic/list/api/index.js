import request from '@/utils/request'

export function getTop(params) {
  return request({
    url: `/bill/withdrawal/admin/top`,
    method: 'get',
    params
  })
}

// 获取所有园区
export function getPark() {
  return request({
    url: `/bill/withdrawal/admin/parks`,
    method: 'get'
  })
}

// 列表
export function getList(params) {
  return request({
    url: `/bill/withdrawal/admin/page_v1`,
    method: 'get',
    params
  })
}
//导出项目信息 Excel
export function getExportExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/bill/withdrawal/admin/export_v1`
}
// 获取付款申请选择企业账户信息
export function getWithdrawalRefundApply(params) {
  return request({
    url: `bill/withdrawal/admin/get_refund_apply`,
    method: 'get',
    params
  })
}
// 企业列表
export function getEntList(params) {
  return request({
    url: `/enterprise/info/list_all_enterprise`,
    method: 'get',
    params
  })
}
// 付款申请提交
export function withdrawalCreate(data) {
  return request({
    url: `/bill/withdrawal/admin/withdrawal`,
    method: 'post',
    data
  })
}
// 获取状态
export function getSelectState(params) {
  return request({
    url: `/bill/withdrawal/admin/get_select_state`,
    method: 'get',
    params
  })
}
