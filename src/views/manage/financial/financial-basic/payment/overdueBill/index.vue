<!-- 逾期账单 -->
<template>
  <basic-card>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="findAllOverDateBillList"
      :search-querys-hook="searchQueryHook"
    >
      <template v-slot:operate-right>
        <el-button type="info" size="mini" @click="exportExcel">
          <svg-icon icon-class="arrow-down" />
          <span>导出</span>
        </el-button>
      </template>
    </drive-table>
  </basic-card>
</template>

<script>
import ColumnMixins from './column/column'
import downloads from '@/utils/download'
import { findAllOverDateBillList, saveExportAllOverDateBillExcel } from './api'

export default {
  name: 'overdueBill',
  mixins: [ColumnMixins],
  data() {
    return {
      findAllOverDateBillList
    }
  },
  methods: {
    goTableDetails(e) {
      this.$router.push({
        path: '/paymentManagement/paymentManagementDetails',
        query: {
          planId: e.planId
        }
      })
    },

    exportExcel() {
      saveExportAllOverDateBillExcel(
        this.$refs['drive-table'].getRequestQuerys()
      ).then(res => {
        if (res) {
          downloads.requestDownload(res, 'excel')
        }
      })
    },

    // 重置搜索参数
    searchQueryHook(e) {
      const [startTime = '', endTime = ''] = e.feeCycle || []
      if (e.feeCycle && e.feeCycle.length > 1) {
        e.startTime = startTime
        e.endTime = endTime
      }
      delete e.feeCycle
      return {
        ...e
      }
    }
  }
}
</script>

<style scoped></style>
