// 获取账单类型
export function getBillType(h, val) {
  switch (val) {
    case 2:
      return <basic-tag type="primary" label="租赁费" />
    case 3:
      return <basic-tag type="success" label="保证金" />
    default:
      return '-'
  }
}

export const billTypeOptions = [
  {
    label: '全部',
    value: -1
  },
  {
    label: '租赁费',
    value: 1
  },
  {
    label: '保证金',
    value: 2
  }
]

export function getBillStatus(h, val) {
  switch (val) {
    case 2:
      return <basic-tag isDot type="warning" label="待核销" />
    case 3:
      return <basic-tag isDot type="success" label="已核销" />
    default:
      return '-'
  }
}

export const billStatusOptions = [
  {
    label: '全部',
    value: -1
  },
  {
    label: '待核销',
    value: 2
  },
  {
    label: '已核销',
    value: 3
  }
]
