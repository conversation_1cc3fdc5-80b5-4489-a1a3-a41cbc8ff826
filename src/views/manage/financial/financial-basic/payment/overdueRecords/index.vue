<template>
  <basic-card>
    <template slot="right">
      <div>
        <el-popover placement="bottom" width="120" trigger="hover">
          <div v-if="registerUrl !== null">
            <div style="width: 120px; height: 120px" class="m-b-10">
              <el-image :src="registerUrl"></el-image>
            </div>
            <div class="font-size-12 color-text-secondary text-align-center">
              使用小程序仅需1分钟
            </div>
          </div>
          <div v-else>
            <empty-data description="暂无二维码"></empty-data>
          </div>
          <el-button
            v-permission="routeButtonsPermission.SCAN_CODE"
            size="mini"
            slot="reference"
            type="primary"
          >
            <svg-icon icon-class="cloud-upload" />
            <span>{{ routeButtonsTitle.SCAN_CODE }}</span></el-button
          >
        </el-popover>

        <el-button
          v-permission="routeButtonsPermission.LEADING_OUT"
          size="mini"
          @click="exportExcel"
          class="m-l-8"
          type="info"
        >
          <svg-icon icon-class="cloud-download" />
          <span>{{ routeButtonsTitle.LEADING_OUT }}</span></el-button
        >
      </div>
    </template>
    <div>
      <div class="financial-content bg-white">
        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>逾期记录</span>
              </div>
              <div
                class="acconutname m-t-17 font-size-24 flex align-items-center"
              >
                <span>{{ statisticsData.total | noData }}</span>
                <span class="font-size-18 font-strong-600 m-l-4"> 条</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>应收总额</span>
              </div>
              <div class="acconut-name m-t-17 font-size-24">
                <span class="font-size-18">¥</span>
                <span class="omit">{{
                  NumFormat(statisticsData.amount) | noData
                }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>违约金</span>
              </div>
              <div class="acconut-name m-t-17 font-size-24">
                <span class="font-size-18">¥</span>
                <span>{{ NumFormat(statisticsData.confirmed) | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center pointer">
                <span class="line"></span>
                <span>已出账</span>
              </div>
              <div class="payname m-t-17 font-size-24">
                <span class="font-size-18">¥</span>
                <span>{{ NumFormat(statisticsData.arrived) | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh" style="width: 80%">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>违约金出账</span>
              </div>
              <div class="w100 m-t-18">
                <el-progress
                  :text-inside="true"
                  :stroke-width="20"
                  :percentage="rate"
                  status="success"
                ></el-progress>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <basic-tab
      :tabsData="tabsData"
      :current="extralQuerys.status"
      @tabsChange="tabsChange"
    >
    </basic-tab>
    <driven-form
      ref="driven-formBasic"
      v-model="extralQuerys"
      :formConfigure="formConfigure"
    />
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="getPayPage"
      :extral-querys="extralQuerys"
    />
  </basic-card>
</template>

<script>
import { formatGetParams, NumFormat } from '@/utils/tools'
import { getTenant } from '@/utils/auth'
import {
  getRegisterPng,
  getRecordSelect,
  headStatistics,
  getPayPage,
  arrivedSelect,
  registrationSelect,
  transactionSelect,
  getRecordExcel
} from './api'
import BasicTab from '@/components/BasicTab'
import MixinsFormConfigure from './descriptor'
import MixinsColumn from './column'
import downloads from '@/utils/download'
import dayjs from 'dayjs'
export default {
  name: 'OverdueRecords',
  components: { BasicTab },
  mixins: [MixinsFormConfigure, MixinsColumn],
  props: {
    numberAll: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      getPayPage,
      statisticsData: {},
      extralQuerys: {
        status: -1,
        entName: ''
      },
      registerUrl: null,
      NumFormat,
      formatGetParams,
      rate: 0,
      tabsData: []
    }
  },
  created() {
    this.registerPng()
    this.getRecordSelect()
    this.headStatistics()
    this.selectDataFn()
  },
  methods: {
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    },
    // 查看详情
    previewEvent(row) {
      let { id, orderId } = row
      this.$router.push({
        path: '/account/transactionInfo/transactionInfoDetails',
        query: {
          id,
          orderId
        }
      })
    },
    exportExcel() {
      let url = getRecordExcel() + '?'
      url += formatGetParams(this.extralQuerys)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '交易登记台账.xls')
    },
    inputEntName(val) {
      this.extralQuerys.entName = val
      this.updateTable()
    },
    selectDataFn() {
      Promise.all([
        transactionSelect(),
        arrivedSelect(),
        registrationSelect()
      ]).then(res => {
        let obj = {
          0: 'transaction',
          1: 'payTimeStart',
          2: 'registration'
        }
        res.forEach((item, index) => {
          this.formConfigure.descriptors[obj[index]].options = item.map(
            item => {
              return {
                label: item.label,
                value: item.key
              }
            }
          )
        })
      })
    },
    headStatistics() {
      headStatistics().then(res => {
        this.statisticsData = res || {}
        this.rate = Number(res.rate) || 0
      })
    },
    getRecordSelect() {
      getRecordSelect().then(res => {
        this.tabsData = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    tabsChange(e) {
      this.extralQuerys.status = e
      this.updateTable()
    },
    updateTable() {
      this.$refs['drive-table'].refreshTable()
    },
    async registerPng() {
      // 判断是否有租户信息
      const tenantId = getTenant()
      this.registerUrl = await getRegisterPng({ tenantId })
    }
  }
}
</script>

<style scoped lang="scss">
.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;

  .percent {
    width: auto;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }
}
</style>
