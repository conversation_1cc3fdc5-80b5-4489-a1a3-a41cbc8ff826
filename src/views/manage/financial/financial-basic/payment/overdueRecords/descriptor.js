export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '0',
        descriptors: {
          entName: {
            form: 'input',
            span: 5,
            label: '',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxlength: 35,
              clearable: true
            },
            events: {
              input: val => this.inputEntName(val)
            }
          },
          entId: {
            span: 13
          },
          transaction: {
            form: 'select',
            span: 2,
            label: '',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '交易方式'
              }
            ],
            attrs: {
              clearable: true
            },
            events: {
              change: () => this.updateTable()
            }
          },
          payTimeStart: {
            form: 'select',
            span: 2,
            label: '',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '到账状态'
              }
            ],
            attrs: {
              clearable: true
            },
            events: {
              change: () => this.updateTable()
            }
          },
          registration: {
            form: 'select',
            span: 2,
            label: '',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '登记方式'
              }
            ],
            attrs: {
              clearable: true
            },
            events: {
              change: () => this.updateTable()
            }
          }
        }
      }
    }
  }
}
