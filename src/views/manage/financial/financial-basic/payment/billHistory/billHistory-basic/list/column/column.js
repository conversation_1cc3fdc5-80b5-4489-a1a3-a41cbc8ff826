import { NumFormat } from '@/utils/tools'
import { getFeeTypeType, getTicketType } from '../status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '企业名称',
          width: 300,
          prop: 'entName',
          render: (h, scope) => {
            return (
              <div>
                <el-tooltip
                  className="item"
                  effect="dark"
                  content={scope.row.entName}
                  placement="top"
                >
                  <div
                    onClick={() => {
                      this.goDetail(scope.row)
                    }}
                    class={'line-1 pointer color-primary'}
                  >
                    {scope.row.entName}
                  </div>
                </el-tooltip>
              </div>
            )
          }
        },
        {
          label: '账单编号',
          prop: 'billCode'
        },

        {
          label: '票据类型',
          prop: 'ticketType',
          render: (h, scope) => {
            return <div>{getTicketType(h, scope.row.ticketType)}</div>
          }
        },
        {
          label: '发票类目',
          prop: 'feeType',
          render: (h, scope) => {
            return <div>{getFeeTypeType(h, scope.row.feeType)}</div>
          }
        },
        {
          label: '合计金额(元)',
          prop: 'ticketPay',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.ticketPay)}
              </div>
            )
          }
        },
        {
          label: '合计税额(元)',
          prop: 'ticketTax',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.ticketTax)}
              </div>
            )
          }
        },
        {
          label: '开票时间',
          prop: 'checkTime'
        },
        {
          label: '操作',
          width: 150,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.goDetailHistory(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DETAIL}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
              </div>
            )
          }
        }
      ],
      //  按企业
      tableColumnFirm: [
        {
          label: '企业名称',
          width: 300,
          prop: 'entName',
          render: (h, scope) => {
            return (
              <div>
                <el-tooltip
                  className="item"
                  effect="dark"
                  content={scope.row.entName}
                  placement="top"
                >
                  <div
                    onClick={() => {
                      this.goDetail(scope.row)
                    }}
                    class={'line-1 pointer color-primary'}
                  >
                    {scope.row.entName}
                  </div>
                </el-tooltip>
              </div>
            )
          }
        },
        {
          label: '历史开票账单总数',
          prop: 'totalCount'
        },
        {
          label: '发票类目',
          prop: 'feeTypeStr'
        },
        {
          label: '专用发票开票数',
          prop: 'specialUseCount'
        },
        {
          label: '普通电子发票开票数',
          prop: 'commonCount'
        },
        {
          label: '合计金额(元)',
          prop: 'totalFee',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.totalFee)}</div>
            )
          }
        },
        {
          label: '合计税额(元)',
          prop: 'totalTax',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.totalTax)}</div>
            )
          }
        },
        {
          label: '最近开票时间',
          prop: 'checkTime'
        }
      ]
    }
  }
}
