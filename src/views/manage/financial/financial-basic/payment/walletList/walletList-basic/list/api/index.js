import request from '@/utils/request'

// 获得所有个体选择
export function getBody() {
  return request({
    url: `/bill/purse/admin/parks`,
    method: 'get'
  })
}

// 获得所有个体选择
export function getStates() {
  return request({
    url: `/bill/purse/admin/states`,
    method: 'get'
  })
}

// 获得虚拟钱包表
export function getPurseList(params) {
  return request({
    url: `/bill/purse/admin/page`,
    method: 'get',
    params
  })
}

//导出项目信息 Excel
export function getExportExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/bill/purse/admin/export_excel`
}
