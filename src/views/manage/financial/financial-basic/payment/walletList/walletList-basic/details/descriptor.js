export default {
  data() {
    return {
      formConfigureSendBack: {
        descriptors: {
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          },
          returnReason: {
            form: 'input',
            label: '退回原因',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入备注信息'
              }
            ],
            props: {
              type: 'textarea'
            }
          }
        }
      },
      formConfigureDrawbill: {
        descriptors: {
          ticketType: {
            label: '票据类型',
            form: 'radio',
            disabled: true,
            rule: [
              {
                required: true,
                message: '请选择票据类型',
                type: 'number'
              }
            ],
            options: [
              {
                label: '普通电子发票',
                value: 1
              },
              {
                label: '专用发票',
                value: 2
              }
            ]
          },
          ticketCode: {
            form: 'input',
            label: '发票号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入发票号'
              }
            ]
          },
          comment: {
            form: 'input',
            label: '备注信息',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入备注信息'
              }
            ],
            props: {
              type: 'textarea'
            }
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                required: true,
                message: '请上传附件',
                type: 'array'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      }
    }
  }
}
