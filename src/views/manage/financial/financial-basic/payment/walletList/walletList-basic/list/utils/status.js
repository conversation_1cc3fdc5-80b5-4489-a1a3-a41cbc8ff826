// 获取政策发布状态
export function statusType(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="success" label="正常" />
    case 1:
      return <basic-tag isDot type="danger" label="异常" />
    default:
      return '-'
  }
}

// 获取政策发布状态
export function ticketTypeStatus(h, val) {
  switch (val) {
    case 1:
      return '房租费'
    case 2:
      return '保证金'
    case 3:
      return '服务费'
    default:
      return '-'
  }
}

// 获取政策发布状态
export function feeTypeTypeStatus(h, val) {
  switch (val) {
    case 1:
      return '普通电子发票'
    case 2:
      return '专用发票'
    default:
      return '-'
  }
}

// 政策发布状态
export const informationReleaseStatus = [
  {
    label: '未发布',
    value: 1
  },
  {
    label: '已发布',
    value: 2
  }
]
