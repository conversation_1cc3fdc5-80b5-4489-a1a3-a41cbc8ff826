<template>
  <basic-card>
    <template v-slot:right>
      <div class="flex align-items-center justify-content-end m-b-16">
        <el-switch
          v-model="extralQuerys.charge"
          active-color="#ed7b2f"
          inactive-color="#dcdfe6"
          @change="chargeHandle"
          :disabled="reqLoading"
        >
        </el-switch>
        <span class="m-l-15 font-size-15">仅看我负责的</span>
      </div>
    </template>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="overdueRecordPage"
      :extral-querys="extralQuerys"
      @getTotal="reqLoading = false"
    />
  </basic-card>
</template>

<script>
import ColumnMixin from './column'
import { overdueRecordPage } from '../api'

export default {
  name: 'OverdueRecord',
  mixins: [ColumnMixin],
  data() {
    return {
      reqLoading: true,
      overdueRecordPage,
      extralQuerys: {
        charge: false
      }
    }
  },
  activated() {
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  deactivated() {
    this.reqLoading = true
  },
  methods: {
    chargeHandle() {
      this.reqLoading = true
      this.$refs['drive-table'] && this.$refs['drive-table'].triggerSearch()
    },
    goEnterpriseDetail(row) {
      let { entId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId
        }
      })
    },
    detailHandle(row) {
      this.$router.push({
        path: '/payment/overdueRecord/overdueDetails',
        query: {
          entId: row.entId,
          enterpriseName: row.enterpriseName
        }
      })
    }
  }
}
</script>

<style scoped></style>
