<template>
  <drive-table
    ref="drive-table"
    :columns="billTableColumn"
    :table-data="tableData"
  />
</template>

<script>
import ColumnMixin from './column'
import { overdueRecordBillList } from '../api'

export default {
  name: 'OverdueBill',
  mixins: [ColumnMixin],
  data() {
    return {
      tableData: []
    }
  },
  created() {
    this.overdueRecordBillList()
  },
  methods: {
    goBillDetails(row) {
      this.$router.push({
        path: '/payment/accountsReceivable/accountsReceivableDetails',
        query: {
          id: row.totalId,
          type: row.type
        }
      })
    },
    overdueRecordBillList() {
      const entId = this.$route.query.entId
      if (!entId) return false
      overdueRecordBillList({ entId }).then(res => {
        this.tableData = res || []
      })
    }
  }
}
</script>

<style scoped></style>
