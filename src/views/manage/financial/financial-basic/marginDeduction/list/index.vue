<template>
  <basic-card>
    <basic-tab
      ref="basicTab"
      :tabs-data="tabsData"
      :current="extralQuerys.status"
      @tabsChange="tabsChange"
      :disabled="reqLoading"
    />
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="depositAccountApplyPage"
      :extral-querys="extralQuerys"
      @getTotal="reqLoading = false"
      :search-querys-hook="searchQueryHook"
    >
      <template v-slot:operate-right>
        <el-button v-permission="routeButtonsPermission.APPLY" type="primary" @click="drawerVisible = true">
          {{ routeButtonsTitle.APPLY }}
        </el-button>
      </template>
    </drive-table>
    <basic-drawer
      title="企业账户抵扣申请"
      :visible.sync="drawerVisible"
      @confirmDrawer="confirmDrawer"
    >
      <driven-form
        ref="driven-form"
        v-if="drawerVisible"
        v-model="fromModel"
        :formConfigure="formConfigure"
        label-position="top"
      />
    </basic-drawer>
  </basic-card>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import ColumnMixin from './column'
import DescriptorMixin from './descriptor'
import {
  depositAccountApplyCreate,
  depositAccountApplyDeptSelect,
  depositAccountApplyEntList,
  depositAccountApplyPage,
  depositAccountApplyStatus,
  getAccountSelect,
  getMatterSelect,
  getCapitalFlowSelect,
  getRefundApply
} from '../api'
import { NumFormat } from '@/utils/tools'

export default {
  name: 'MarginDeduction',
  components: { BasicTab },
  mixins: [ColumnMixin, DescriptorMixin],
  data() {
    return {
      depositAccountApplyPage,
      tabsData: [],
      extralQuerys: {
        status: -1
      },
      reqLoading: false,
      drawerVisible: false,
      fromModel: {
        account: 1
      },
      balance: 0 // 申请缴存金额
    }
  },
  watch: {
    drawerVisible(val) {
      if (!val) {
        this.fromModel = this.$options.data().fromModel
      }
    },
    'fromModel.entId': {
      handler(val) {
        if (val){
          this.getRefundApplyFn(val)
        }
      }
    },
    'fromModel.account': {
      handler(val) {
        this.$set(this.fromModel, 'capitalFlow', '')
        this.$set(this.fromModel, 'money', '')
        if (this.fromModel.entId) {
          this.getRefundApplyFn(this.fromModel.entId)
        }
        this.getCapitalFlowSelect(val)
      },
      immediate: true
    },
    'fromModel.matterType': {
      handler(val) {
        this.formConfigure.descriptors.matterOther.hidden = val !== 4
      }
    },
    'fromModel.capitalFlow': {
      handler(val) {
        console.log('val',val)
        this.formConfigure.descriptors.capitalFlowOther.hidden = val !== '5'
      }
    }
  },
  activated() {
    this.depositAccountApplyStatus()
    this.depositAccountApplyDeptSelect()
    this.depositAccountApplyEntList()
    this.getMatterSelect()
    this.getAccountSelect()
    if (this.executeActivated) {
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    }
  },
  methods: {
    getRefundApplyFn(val) {
      getRefundApply(val).then(res => {
        const [data1, data2] = res || []

        const createColumnConfig = (data, isMargin = false) => [
          ...(isMargin
            ? [
                {
                  prop: 'depositBaseAmount',
                  label: `缴存基数(元)`,
                  render: (h, scope) => (
                    <span class="color-warning">
                      {NumFormat(scope.row.depositBaseAmount)}
                    </span>
                  )
                },
                {
                  prop: 'payableAmount',
                  label: `缴存余额(元)`,
                  render: (h, scope) => (
                    <span class="color-warning">
                        {NumFormat(scope.row.payableAmount)}
                      </span>
                  )
                }
              ]
            : [          {
              prop: 'payableAmount',
              label: `账户余额(元)`,
              render: (h, scope) => (
                <span class="color-warning">
                {NumFormat(scope.row.payableAmount)}
              </span>
              )
            },]),
          {
            prop: 'amount',
            label: '申请抵扣金额(元)',
            render: (h, scope) => (
              <el-input
                v-model={scope.row.amount}
                placeholder="请输入金额"
                onInput={value => {
                  // 限制只能输入数字，不超过 8 位整数，并保留最多 2 位小数
                  const formattedValue = value
                    .replace(/[^0-9.]/g, '') // 仅保留数字和小数点
                    .replace(/^(\d{1,8})(\.\d{0,2})?.*$/, '$1$2') // 限制整数 8 位，小数 2 位

                  scope.row.amount = formattedValue
                  this.$set(this.fromModel, 'money', formattedValue)
                }}
              />
            )
          }
        ]

        const marginAccountRef = this.$refs.marginAccount

        marginAccountRef.rentAccountColumn = createColumnConfig(data1)
        marginAccountRef.rentAccountData = [
          { payableAmount: data1.payableAmount, amount: '' }
        ]

        marginAccountRef.marginAccountColumn = createColumnConfig(data2, true)
        marginAccountRef.marginAccountData = [
          {
            payableAmount: data2.payableAmount,
            depositBaseAmount: data2.depositBaseAmount,
            amount: ''
          }
        ]
      })
    },
    searchQueryHook(e) {
      let [beginApplyTime = '', endApplyTime = ''] = e.applyTime || []
      delete e.applyTime
      return {
        ...e,
        beginApplyTime,
        endApplyTime
      }
    },
    depositAccountApplyEntList() {
      depositAccountApplyEntList().then(res => {
        this.formConfigure.descriptors.entId.options = res.map(item => {
          return {
            ...item,
            label: item.enterpriseName,
            value: item.id
          }
        })
      })
    },
    getAccountSelect() {
      getAccountSelect().then(res => {
        const data = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
        this.tableColumn[1].search.options = data
        this.formConfigure.descriptors.account.options = data
      })
    },
    getMatterSelect() {
      getMatterSelect().then(res => {
        const data = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
        this.tableColumn[2].search.options = data
        this.formConfigure.descriptors.matterType.options = data
      })
    },
    getCapitalFlowSelect(val) {
      getCapitalFlowSelect(val).then(res => {
        this.formConfigure.descriptors.capitalFlow.options = res.map(item => {
          return {
            label: item.label,
            value: String(item.key)
          }
        })
      })
    },
    depositAccountApplyDeptSelect() {
      depositAccountApplyDeptSelect().then(res => {
        this.tableColumn[6].search.options = res.map(item => {
          return {
            label: item.name,
            value: item.value
          }
        })
      })
    },
    depositAccountApplyStatus() {
      depositAccountApplyStatus().then(res => {
        this.tabsData = res.map(item => {
          return {
            label: `${item.statusStr} (${item.count})`,
            value: item.status
          }
        })
      })
    },
    confirmDrawer() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false

        const { marginAccountData, rentAccountData } = this.$refs.marginAccount;
        const { account, money } = this.fromModel;

        const accountDataMap = {
          1: rentAccountData,
          2: marginAccountData,
        };
        const [accountObj] = accountDataMap[account] || [];

        if (!this.validateAmount(accountObj)) return;

        const attaches = this.fromModel.attaches || []
        const params = {
          ...this.fromModel,
          accountType:account,
          amount:money,
          attaches: attaches.length ? attaches.map(item => item.id) : []
        }
        depositAccountApplyCreate(params).then(() => {
          this.$toast.success('提交成功')
          this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
          this.depositAccountApplyStatus()
          this.drawerVisible = false
        })
      })
    },
    validateAmount(accountObj) {
      if (!accountObj || !accountObj.amount) return true;

      if (Number(accountObj.amount) > Number(accountObj.payableAmount)) {
        this.$toast.warning('抵扣金额超出, 请重新输入!');
        return false;
      }
      return true;
    },
    detailHandle(row) {
      this.$router.push({
        path: '/account/marginDeduction/detail',
        query: {
          id: row.id,
          orderId: row.orderId
        }
      })
    },
    goEnterpriseDetail(row) {
      let { entId, enterStatus = 0 } = row
      if (enterStatus === 1 || enterStatus === 2) {
        this.$router.push({
          path: '/business/enterpriseDetails',
          query: {
            id: entId
          }
        })
      }
    },
    tabsChange(e) {
      this.reqLoading = true
      this.extralQuerys.status = e
      this.$refs['drive-table'].resetPageNoRefreshTable()
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.moneyKey) {
  position: relative;
  .custom-tips {
    position: absolute;
    right: 12px;
    top: 0;
  }
}
</style>
