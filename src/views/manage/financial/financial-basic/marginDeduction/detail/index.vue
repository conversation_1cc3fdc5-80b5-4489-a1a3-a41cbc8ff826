<template>
  <basic-card>
    <div
      class="w100 flex justify-content-between align-items-center deduction-header"
    >
      <div class="font-strong font-size-18 flex align-items-center">
        <span>{{ detailInfo.entName | noData }}</span>
        <el-tag class="m-l-8" :type="statusType()">{{
          detailInfo.statusStr
        }}</el-tag>
      </div>
      <div class="flex align-items-center">
        <flow-form  v-permission="routeButtonsPermission.AUDIT" class="m-l-8" ref="flowForm" />
      </div>
    </div>
    <div class="info">
      <div class="flex justify-content-between m-t-24">
        <div class="w-100">
          <div class="font-size-14 color-999 m-b-16">抵扣账户</div>
          <div class="font-size-16">
            {{ detailInfo.accountTypeStr }}
          </div>
        </div>
        <div class="m-l-32" :class="detailInfo.matterType === 4? 'w-200' : 'w-100'">
          <div class="font-size-14 color-999 m-b-16">抵扣事项</div>
          <div class="font-size-16 line-height-22">
            {{ matterTypeCom }}
          </div>
        </div>
        <div class="w-100">
          <div class="font-size-14 color-999 m-b-16">申请抵扣金额(元)</div>
          <div class="font-size-16 color-warning">
            {{ NumFormat(detailInfo.amount) }}
          </div>
        </div>
        <div class="m-l-32" :class="detailInfo.capitalFlow === 5? 'w-200' : 'w-100'">
          <div class="font-size-14 color-999 m-b-16">金额去向</div>
          <div class="font-size-16 line-height-22">
            {{ capitalFlowCom }}
          </div>
        </div>
        <div class="w-100 m-l-32">
          <div class="font-size-14 color-999 m-b-16">申请部门</div>
          <div class="font-size-16 color-333">
            {{ detailInfo.deptName | noData }}
          </div>
        </div>
        <div class="w-100 m-l-32">
          <div class="font-size-14 color-999 m-b-16">申请人</div>
          <div class="font-size-16 color-333">
            {{ detailInfo.applyUser | noData }}
          </div>
        </div>
        <div class="w-100 m-l-32">
          <div class="font-size-14 color-999 m-b-16">申请时间</div>
          <div class="font-size-16 color-333">
            {{ detailInfo.applyTime | noData }}
          </div>
        </div>
      </div>
    </div>
    <div class="m-t-42">
      <div class="font-size-14">账户情况</div>
      <drive-table
        class="m-t-16"
        ref="drive-table"
        :columns="tableColumn"
        :table-data="tableData"
      />
    </div>
    <div class="m-t-20 font-size-14">
      <div class="m-b-12 color-999">备注内容</div>
      <div class="line-3 line-height-22 color-333">
        {{ detailInfo.remark | noData }}
      </div>
    </div>
    <div class="m-t-20 font-size-14">
      <div class="m-b-12 color-999">相关附件</div>
      <files-list
        style="width: 50%"
        v-if="fileList && fileList.length"
        :files="fileList"
        :onlyForView="true"
      />
      <div v-else class="line-3 line-height-22 color-333">暂无附件</div>
    </div>
  </basic-card>
</template>

<script>
import FlowForm from '@/components/FlowForm/index.vue'
import { NumFormat } from '@/utils/tools'
import ColumnMixin from './column'
import FilesList from '@/components/Uploader/files'
import { depositAccountApplyDetail } from '../api'

export default {
  name: 'MarginDeductionDetail',
  components: { FilesList, FlowForm },
  mixins: [ColumnMixin],
  data() {
    return {
      NumFormat,
      detailInfo: {},
      tableData: []
    }
  },
  computed: {
    capitalFlowCom() {
      const {capitalFlow,capitalFlowStr,capitalFlowOther} = this.detailInfo
      if (capitalFlow === 5){
        return capitalFlowOther
      }else  {
        return capitalFlowStr
      }
    },
    matterTypeCom() {
      const {matterType,matterTypeStr,matterOther} = this.detailInfo
      if (matterType === 4){
        return matterOther
      }else  {
        return matterTypeStr
      }
    },
    fileList() {
      const attach = this.detailInfo.attach || {}
      return attach.deduction || []
    }
  },
  created() {
    this.depositAccountApplyDetail()
  },
  methods: {
    statusType() {
      const status = this.detailInfo.status
      const obj = {
        1: 'primary',
        2: 'success',
        3: 'danger'
      }
      return obj[status]
    },
    depositAccountApplyDetail() {
      const id = this.$route.query.id
      if (!id) return false
      depositAccountApplyDetail(id).then(res => {
        this.detailInfo = res || {}
        this.tableData = [this.detailInfo]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.deduction-header {
  padding-bottom: 24px;
  border-bottom: 1px solid #e5e5e5;
}
.w-200 {
  width: 300px;
}
.w-100 {
  width: 150px;
}
.color-999 {
  color: #999;
}
.color-333 {
  color: #333;
}
</style>
