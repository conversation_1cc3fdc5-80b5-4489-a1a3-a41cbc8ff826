import request from '@/utils/request'

// 园区端退余额记录列表
export function parkRefundBalanceList(params) {
  return request({
    url: `/refundBalance/parkRefundBalanceList`,
    method: 'get',
    params
  })
}

// 园区端退费用结算列表
export function list(params) {
  return request({
    url: `/entSettlement/parkGet/list`,
    method: 'get',
    params
  })
}

// 获取所有园区
export function queryParkInfo(parkType) {
  return request({
    url: `dataStation/queryParkInfo/${parkType}`,
    method: 'get'
  })
}

// 园区端退余额统计
export function parkRefundBalanceCalc(data) {
  return request({
    url: `/refundBalance/parkRefundBalanceCalc`,
    method: 'post',
    data
  })
}

// 保存导出园区退余额查询条件
export function saveExportRefundBalance(data) {
  return request({
    url: `/refundBalance/saveExportRefundBalance`,
    method: 'post',
    data
  })
}

// 园区退结算详情
export function parkGetExitSettleDetail(id) {
  return request({
    url: `/entSettlement/parkGetExitSettleDetail/${id}`,
    method: 'get'
  })
}

// 园区端查看退余额详情
export function parkRefundBalanceInfo(id) {
  return request({
    url: `/refundBalance/parkRefundBalanceInfo/${id}`,
    method: 'get'
  })
}

// 交费管理-应收账单-查询应收账单列表
export function findAllPayBillList(params) {
  return request({
    url: `/payAmountCount/findAllPayBillList`,
    method: 'get',
    params
  })
}
