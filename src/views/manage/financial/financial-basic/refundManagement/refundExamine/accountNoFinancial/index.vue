<!--
 * @Author: your name
 * @Date: 2021-10-19 11:22:32
 * @LastEditTime: 2021-12-23 01:19:22
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \rat-platform-web\src\views\workspace\departureManagement\accountNoFinancial\index.vue
-->
<template>
  <div class="main-wrapper">
    <basic-card title="应收账单">
      <div class="p-t-14 p-b-14">
        <drive-table
          ref="driveTable"
          :columns="billColumn"
          :api-fn="findAllPayBillList"
          :is-need-title="false"
          :extral-querys="extralQuerys"
        />
      </div>
      <!-- 账单核销 -->
      <!-- <dialog-cmp title="账单核销" :visible.sync="visible" width="80%" :is-show-btns="false">
        <div>
          <account-write ref="accoun" @colse="colse" />
        </div>
      </dialog-cmp> -->
    </basic-card>
  </div>
</template>

<script>
import CloumnMixins from './column'
import { findAllPayBillList } from '../../api/index'
export default {
  name: 'AccountNoFinancial',
  // prop: {
  //   extralQuerys: {
  //     entName: this.exitEntInfo.entName
  //   }
  // },
  mixins: [CloumnMixins],
  data() {
    return {
      visible: false,
      // detailData: [],
      id: null,
      findAllPayBillList,
      exitEntInfo: '',
      extralQuerys: null
    }
  },
  watch: {},
  created() {
    const { entName } = this.$route.query
    this.extralQuerys = { entName }
    // this.getParkExitHouseDetail()
  },
  methods: {
    // 核销
    writeOff(e) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs.accoun.init(e)
      })
    },

    // 账单详情
    goBillDetails(idx, row) {
      this.$router.push({
        path: '/paymentManagement/accountNoFinancialDetail',
        query: {
          id: row.id,
          entId: row.entId
        }
      })
    },

    // 账单详情
    billSee(e) {
      this.$router.push({
        path: '/paymentManagement/accountNoFinancialDetail',
        query: {
          id: e.id,
          entId: e.entId
        }
      })
    },

    // 获取账单信息
    // getParkExitHouseDetail() {
    //   getParkExitHouseDetail(this.id).then((res) => {
    //     console.log(res, '0')
    //     // this.detailData = res.exitEntFinanceInfo
    //     this.exitEntInfo = res.exitEntInfo.entName
    //     console.log(this.exitEntInfo)
    //     // this.detailData = res.exitEntFinanceInfo.billList.filter((item) => {
    //     //   return item.billStatus === 1 || item.billStatus === 2
    //     // })
    //     // console.log(this.detailData)
    //   })
    // },

    // 取消
    colse() {
      this.visible = false
      this.findAllPayBillList()
    }
  }
}
</script>

<style></style>
