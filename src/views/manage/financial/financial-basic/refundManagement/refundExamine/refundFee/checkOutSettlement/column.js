/*
 * @Author: your name
 * @Date: 2021-09-13 17:36:54
 * @LastEditTime: 2021-12-02 15:34:19
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \rat-platform-web\src\views\workspace\departureManagement\checkOutDetails\column.js
 */
import {
  getCostStatus,
  getContractTypeOptions,
  getfeeType
  //  feeTypeStatus
} from './status'
import { NumFormat } from '@/utils/tools'
const column = {
  data() {
    return {
      tableColumn: [
        {
          prop: 'contractNum',
          label: '合同编号'
        },
        { prop: 'parkName', label: '园区' },
        {
          prop: 'isNewSign',
          label: '合同类型',
          render: (h, scope) => {
            return <div>{getContractTypeOptions(h, scope.row.isNewSign)}</div>
          }
        },

        { prop: 'totalArea', label: '面积(m²)' },
        { prop: 'room', label: '楼栋房号', showOverflowTooltip: true },
        {
          prop: 'signDate',
          label: '签约日期'
        },
        {
          prop: 'startDate',
          label: '起始日期'
        },
        {
          prop: 'endDate',
          label: '终止日期'
        }
      ],
      billColumn: [
        {
          prop: 'billCode',
          label: '账单编号',
          minWidth: '280%'
        },
        {
          prop: 'feeAcNme',
          label: '费用名称'
        },
        { prop: 'feeCycle', label: '费用周期', align: 'center' },
        {
          prop: 'payAmount',
          label: '账单金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        {
          prop: 'billStatus',
          label: '账单状态',
          align: 'center',
          render: (h, scope) => {
            return <div>{getCostStatus(h, scope.row.billStatus)} </div>
          }
        },
        {
          prop: 'checkStatus',
          label: '操作',
          minwidth: '80%',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="primary"
                  size="mini"
                  onClick={() => {
                    this.billSee(scope.row)
                  }}
                >
                  账单详情
                </el-button>
                <el-button
                  type="success"
                  size="mini"
                  v-show={
                    scope.row.billStatus === 1 || scope.row.billStatus === 2
                  }
                  onClick={() => {
                    this.writeOff(scope.row)
                  }}
                >
                  核销
                </el-button>
              </div>
            )
          }
        }
      ],
      dueColumn: [
        {
          id: 1,
          type: 'selection'
        },

        {
          prop: 'billCode',
          label: '账单编号',
          minWidth: '160%'
          // search: {
          //   type: 'input'
          // }
        },
        {
          prop: 'feeType',
          label: '账单类型',
          // hidden: true,
          // search: {
          //   type: 'select',
          //   options: feeTypeStatus
          // },
          render: (h, scope) => {
            return <div>{getfeeType(h, scope.row.feeType)}</div>
          }
        },
        // {
        //   prop: 'feeType',
        //   label: '账单类型',

        // },
        {
          prop: 'billName',
          label: '期数'
        },
        {
          prop: 'feeCycle',
          label: '账单周期',
          align: 'center',
          minWidth: '120%',
          /*  search: {
              type: 'time',
              format: 'yyyy-MM',
              dateType: 'month'
            } */
          // search: {
          //   type: 'rangeTime'
          // },
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} ~ {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          prop: 'payAmount',
          label: '账单金额',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        /*         {
            prop: 'payAmount',
            label: '本期应收(元)',
            render: (h, scope) => {
              return (
                <div class='text-warning'>{NumFormat(scope.row.payAmount)}</div>
              )
            }
          }, */
        /*         {
            prop: 'payActualAmount',
            label: '实缴金额(元)',
            render: (h, scope) => {
              return (
                <div class='text-warning'>
                  {NumFormat(scope.row.payActualAmount)}
                </div>
              )
            }
          }, */
        {
          prop: 'payActualAmount',
          label: '已核销金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">
                {NumFormat(scope.row.payActualAmount)}
              </div>
            )
          }
        },
        {
          prop: 'billStatus',
          label: '账单状态',
          align: 'center',
          render: (h, scope) => {
            return <div>{getCostStatus(h, scope.row.billStatus)}</div>
          }
        },
        {
          prop: 'startDate',
          label: '操作',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="primary"
                  size="mini"
                  onClick={() => {
                    this.getDetails(scope.$index, scope.row)
                  }}
                >
                  查看
                </el-button>
              </div>
            )
          }
        }
      ],
      refundColumn: [
        {
          prop: 'billCode',
          label: '账单编号',
          minWidth: '200%'
        },
        {
          prop: 'feeType',
          label: '账单类型',
          render: (h, scope) => {
            return <div>{getfeeType(h, scope.row.feeType)}</div>
          }
        },
        {
          prop: 'billName',
          label: '期数'
        },
        {
          prop: 'feeCycle',
          label: '账单周期',
          align: 'center',
          minWidth: '120%',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} ~ {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          prop: 'payAmount',
          label: '账单金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        {
          prop: 'payActualAmount',
          label: '已核销金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">
                {NumFormat(scope.row.payActualAmount)}
              </div>
            )
          }
        },
        {
          prop: 'returnMoney',
          label: '退款金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">
                <el-input
                  v-model={scope.row.returnMoney}
                  onchange={() =>
                    this.change(
                      scope.row.returnMoney,
                      scope.row.payActualAmount,
                      scope.row.id
                    )
                  }
                  onblur={() =>
                    this.blurs(
                      scope.row.returnMoney,
                      scope.row.payActualAmount,
                      scope.row.id
                    )
                  }
                ></el-input>
              </div>
            )
          }
        },
        {
          prop: 'startDate',
          label: '操作',
          minwidth: '80%',
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  type="success"
                  size="mini"
                  onClick={() => {
                    this.getDelete(scope.$index, scope.row)
                  }}
                >
                  移除
                </el-button>
              </div>
            )
          }
        }
      ],
      refundBill: [
        {
          prop: 'billCode',
          label: '账单编号',
          minWidth: '200%'
        },
        {
          prop: 'feeType',
          label: '账单类型',
          render: (h, scope) => {
            return <div>{getfeeType(h, scope.row.feeType)}</div>
          }
        },
        {
          prop: 'billName',
          label: '期数'
        },
        {
          prop: 'feeCycle',
          label: '账单周期',
          minWidth: '110%',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} ~ {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          prop: 'payAmount',
          label: '账单金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        {
          prop: 'payActualAmount',
          label: '已核销金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">
                {NumFormat(scope.row.payActualAmount)}
              </div>
            )
          }
        },
        {
          prop: 'returnMoney',
          label: '退款金额(元)',
          align: 'right',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.returnMoney)}</div>
            )
          }
        }
      ],
      signatureColumn: [
        { prop: 'periodsNum', label: '附件名称' },
        {
          label: '操作',
          width: 200,
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="primary"
                  onClick={() => {
                    this.goTableData(scope.row)
                  }}
                >
                  查看
                </el-button>
              </div>
            )
          }
        }
      ]
    }
  }
}
export default column
