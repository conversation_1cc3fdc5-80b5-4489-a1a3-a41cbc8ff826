import { getStatus } from '../refundBalance/status'
import { NumFormat } from '@/utils/tools'
const column = {
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '企业名称',
          search: {
            type: 'input'
          }
          // render: (h, scope) => {
          //   return (
          //     <div>
          //       <el-link
          //         type='primary'
          //         onClick={() => {
          //           this.handleSkipToEntDetail(scope.$index, scope.row)
          //         }}
          //       >
          //         {scope.row.entName}
          //       </el-link>
          //     </div>
          //   )
          // }
        },
        {
          prop: 'parkId',
          label: '园区名称',
          hidden: true,
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'process',
          label: '状态',
          hidden: true,
          search: {
            type: 'select',
            options: [
              {
                value: 1,
                label: '待结算'
              },
              {
                value: 5,
                label: '已退回'
              },
              {
                value: 7,
                label: '已拒绝'
              },
              {
                value: 9,
                label: '已通过'
              },
              {
                value: -2,
                label: '已初审'
              },
              {
                value: -3,
                label: '已复审'
              },
              {
                value: -4,
                label: '副总审核'
              },
              {
                value: -5,
                label: '终审'
              },
              {
                value: -6,
                label: '财务审核'
              }
            ]
          }
        },
        {
          prop: 'leaveTime',
          hidden: true,
          sortable: true,
          label: '搬离截止日期',
          search: {
            type: 'daterange'
          }
        },
        {
          prop: 'parkName',
          label: '园区',
          minWidth: '30%'
        },
        {
          prop: 'rooms',
          label: '退房房号',
          showOverflowTooltip: true,
          search: {
            type: 'input'
          },
          minWidth: '55%'
        },
        {
          prop: 'area',
          label: '退房面积(m²)',
          hidden: true,
          search: {
            type: 'InputRange'
          }
        },
        {
          prop: 'area',
          label: '退房面积(m²)',
          minWidth: '55%'
        },
        {
          prop: 'leaveTime',
          sortable: true,
          label: '搬离截止日期',
          minWidth: '42%'
        },
        /*    {
          prop: 'updateTime',
          label: '退房类型',
          minWidth: '30%'
        }, */
        {
          prop: 'returnMoney',
          label: '退款金额(元)',
          minWidth: '55%',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.returnMoney)}
              </div>
            )
          }
        },
        {
          prop: 'process',
          label: '账单状态',
          minWidth: '34%',
          render: (h, scope) => {
            return <div>{getStatus(h, scope.row.process)}</div>
          }
        },
        {
          prop: 'applyStatus',
          label: '操作',
          width: 70,
          render: (h, scope) => {
            return (
              <div>
                <el-button
                  size="mini"
                  type="text"
                  onClick={() => {
                    this.goTableData(scope.row)
                  }}
                >
                  查看
                </el-button>
              </div>
            )
          }
        }
      ],
      tableColumn1: [
        {
          prop: 'parkId',
          label: '入驻园区',
          hidden: true,
          search: {
            type: 'select',
            options: []
          }
        },
        {
          prop: 'parkName',
          minWidth: '30%',
          label: '入驻园区'
        },
        {
          prop: 'entName',
          label: '企业名称',
          search: {
            type: 'input'
          }
          //   render: (h, scope) => {
          //     return (
          //       <div>
          //         <el-link
          //           type='primary'
          //           onClick={() => {
          //             this.goTableData(scope.row)
          //           }}
          //         >
          //           {scope.row.entName}
          //         </el-link>
          //       </div>
          //     )
          //   }
        },
        {
          prop: 'demandArea',
          label: '需求面积(m²)',
          minWidth: '55%'
        },
        {
          prop: 'enterType',
          label: '入驻类型',
          minWidth: '30%',
          search: {
            type: 'select',
            options: [
              {
                label: '租房入驻',
                value: 1
              },
              {
                label: '购房入驻',
                value: 2
              },
              {
                label: '租房入驻',
                value: 1
              },
              {
                label: '购房入驻',
                value: 2
              }
            ]
          },
          render: (h, scope) => {
            return (
              <div>{scope.row.enterType === 1 ? '租房入驻' : '购房入驻'}</div>
            )
          }
        },
        {
          prop: 'contactPerson',
          label: '常用联系人',
          minWidth: '37%'
        },
        {
          prop: 'contactPhone',
          label: '联系方式',
          minWidth: '45%'
        },
        {
          prop: 'isBusinessChecked',
          label: '工商注册情况',
          minWidth: '45%',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.isBusinessChecked === 1 ? '已注册' : '未注册'}
              </div>
            )
          }
        },
        {
          prop: 'applyTime',
          sortable: true,
          label: '申请日期',
          search: {
            type: 'rangeTime'
          },
          minWidth: '42%'
        },
        {
          prop: 'updateTime',
          sortable: true,
          label: '更新时间',
          minWidth: '70%'
        },
        {
          prop: 'applyStatus',
          label: '申请状态',
          minWidth: '34%',
          render: (h, scope) => {
            return <div>{(h, scope.row.applyStatus)}</div>
          }
        },
        {
          prop: 'applyStatus',
          label: '操作',
          width: 70
          //   render: (h, scope) => {
          //     return (
          //       <div>
          //         <el-button
          //           size='mini'
          //           type='primary'
          //           onClick={() => {
          //             this.goTableData(scope.row)
          //           }}
          //         >
          //           查看
          //         </el-button>
          //         {/* <el-button
          //           size='mini'
          //           type='success'
          //           v-show={scope.row.applyStatus === 2}
          //           onClick={() => {
          //             this.examineEvent(scope.row)
          //           }}
          //         >
          //           审核
          //         </el-button> */}
          //       </div>
          //     )
          //   }
        }
      ]
    }
  }
}
export default column
