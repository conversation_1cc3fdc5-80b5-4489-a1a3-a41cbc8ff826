import moment from 'moment'

// 获取交易状态
export function tradeType(h, val) {
  switch (val) {
    case 1:
      return '初始'
    case 2:
      return '成功'
    case 3:
      return '失败'
    case 4:
      return '超时'
    default:
      return '-'
  }
}

// 借贷标志
export function getLoanFlag(h, val) {
  switch (val) {
    case 1:
      return <el-tag type="primary">进账</el-tag>
    case 2:
      return <el-tag type="danger">出账</el-tag>
    default:
      return '-'
  }
}

export function getDcFlag(h, val) {
  switch (val) {
    case 'C':
      return <el-tag type="primary">贷</el-tag>
    case 'D':
      return <el-tag type="danger">借</el-tag>
    default:
      return '-'
  }
}

export function formatTime(val) {
  if (!val) return ''
  const time = val.acDte + val.txnTime
  return moment(time, 'YYYYMMDDHHmmss').format('YYYY-MM-DD HH:mm:ss')
}

export function getDcTxnAmt(h, val) {
  switch (val) {
    case 'C':
      return <span type="primary">+</span>
    case 'D':
      return <span type="danger">-</span>
    default:
      return '-'
  }
}
export function getTxnAmt(h, val) {
  switch (val) {
    case 1:
      return <span type="primary">+</span>
    case 2:
      return <span type="danger">-</span>
    default:
      return '-'
  }
}
// 获取账单类型
export function getBillType(h, val, str) {
  let type = ''
  const primaryColor = [1, 4, 7]
  const successColor = [2, 5, 8]
  const warningColor = [3, 6, 10]
  const dangerColor = [-1]
  if (primaryColor.includes(val)) type = 'primary'
  if (successColor.includes(val)) type = 'success'
  if (warningColor.includes(val)) type = 'warning'
  if (dangerColor.includes(val)) type = 'danger'
  return <basic-tag type={type} label={str}></basic-tag>
}
