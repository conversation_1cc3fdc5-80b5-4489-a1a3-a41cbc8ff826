<template>
  <div class="basic-card">
    <div v-if="isTitle" class="card-title flex flex-center-between">
      <p v-if="title">{{ title }}<slot name="tag"></slot></p>
      <breadcrumb v-else />
      <slot name="right"></slot>
    </div>
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb'

export default {
  name: 'BasicCard',
  components: { Breadcrumb },
  props: {
    title: {
      type: String,
      default: ''
    },
    isTitle: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-card {
  display: flex;
  flex-direction: column;
  @include background_color(--color-white);
  border-radius: 3px;
  width: 100%;
  //高度自适应屏幕高度
  height: 100%;
  .card-title {
    font-size: 16px;
    line-height: 24px;
    padding: 18px 24px;
  }
  .content {
    width: 100%;
    height: 80%;
    box-sizing: border-box;
    flex: 1;
    padding: 0 24px;
    margin-bottom: 24px;
    border-right: 1px solid #e7e7e7;
  }
}
</style>
