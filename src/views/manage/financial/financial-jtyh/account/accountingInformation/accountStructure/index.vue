<template>
  <div class="tree-wrapper">
    <div class="left-body h100">
      <div class="structure">账户体系</div>
      <div class="account-apex">
        <div class="h100">
          <div class="account">
            <el-scrollbar class="scrollbar h100">
              <div>
                <template v-if="structureData && structureData.length > 0">
                  <el-tree
                    class="filter-tree"
                    :highlight-current="true"
                    :data="structureData"
                    :props="defaultProps"
                    node-key="value"
                    :default-checked-keys="defaultCheckedKeys"
                    @node-click="nodeClick"
                    default-expand-all
                    :render-content="renderContent"
                    ref="tree"
                  >
                  </el-tree>
                </template>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAccStatic } from '../api'
export default {
  name: 'AccountStructure',
  props: {
    menuDisabled: {
      type: Boolean,
      default: false
    }
  },
  inject: ['main'],
  data() {
    return {
      flag: false,
      isLoading: false,
      defaultProps: {
        children: 'children',
        label: 'label',
        value: 'value'
      },
      defaultCheckedKeys: [],
      structureData: []
    }
  },
  mounted() {
    this.getAccountStructure()
  },
  methods: {
    getAccountStructure() {
      this.isLoading = true
      getAccStatic()
        .then(res => {
          this.structureData = res || []
          this.setDefaultChecked()
          this.isLoading = false
        })
        .catch(() => {
          this.isLoading = false
        })
    },
    nodeClick(data, node) {
      node.expanded = !node.expanded
      this.$emit('change', data)
    },
    renderContent(h, { node, data }) {
      const isFirstLevelFirstNode =
        node.level === 1 && node.parent.childNodes.indexOf(node) === 0
      return (
        <div class="custom-tree-node">
          {isFirstLevelFirstNode ? (
            <svg-icon class="main" icon-class="enterprise-name" />
          ) : null}
          <div class="name">{data.label}</div>
          {node.childNodes && node.childNodes.length > 0 ? (
            <svg-icon class="icon-right" icon-class="chevron-down" />
          ) : null}
        </div>
      )
    },
    setDefaultChecked() {
      if (this.structureData.length > 0) {
        this.defaultCheckedKeys = [this.structureData[0].value]
        this.main.accountData = this.structureData[0]
        this.$emit('change', this.structureData[0])
        this.$nextTick(() => {
          this.$refs.tree.setCurrentKey(...this.defaultCheckedKeys)
        })
      }
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-wrapper {
  height: 100%;
  @include border_color(--border-color-base);

  .structure {
    font-weight: 350;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.8);
    line-height: 20px;
    margin-bottom: 8px;
  }

  .account-apex {
    height: 100%;
    :deep(.el-skeleton) {
      height: 100%;
    }
    .skeleton-label {
      padding-left: 20px;
    }
    .skeleton-height {
      height: 28px;
    }
    .account {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    :deep(.el-tree) {
      .el-tree-node::after,
      .el-tree-node::before {
        display: none;
      }

      .el-tree-node__expand-icon {
        display: none;
      }

      .el-tree-node__content {
        padding-left: 16px !important;
        padding-right: 16px !important;
        margin-bottom: 4px;
        height: 36px;
        border-radius: 3px;
        &:hover {
          background-color: #f4f4f4;
        }
      }
    }

    :deep(
        .el-tree--highlight-current
          .el-tree-node.is-current
          > .el-tree-node__content
      ) {
      color: #ed7b2f;
      background-color: #fef4ee;
    }

    .filter-tree {
      .label {
        margin-left: 10px;
        font-size: 14px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        display: inline-block;
        width: 100%;
        height: 100%;
      }
      .icon-right {
        position: absolute;
        right: 10px;
        font-size: 14px;
      }
    }
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.9);
  }

  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }
}
</style>
<style>
.custom-tree-node {
  width: 100%;
  display: flex;
  align-items: center;
  .main {
    padding-right: 6px !important;
  }
  .name {
    font-size: 14px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .icon-right {
    width: 20px;
  }
}
</style>
