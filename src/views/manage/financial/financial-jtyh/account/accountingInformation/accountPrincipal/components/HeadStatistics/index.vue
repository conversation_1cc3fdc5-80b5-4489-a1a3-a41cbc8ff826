<template>
  <div class="w100">
    <BasicTab
      :tabsData="tabsData"
      :current="current"
      @tabsChange="tabsChange"
    >
      <template v-slot:right>
        <div class="flex align-items-center">
          <svg-icon class="color-primary font-size-15" icon-class="time"/>
          <span class="color-info m-l-6 font-size-12">统计周期: {{period}}</span>
        </div>
      </template>
    </BasicTab>
    <div class="h100 financial-content m-b-24 bg-white flex align-items-end">
      <div
        class="w100 flex account-left bg-white justify-content-between align-items-center"
      >
        <div class="xx">
          <div class="tx inline-block"></div>
          <div class="zh">
            <div class="flex align-items-center font-size-16 m-t-10">
              <svg-icon
                icon-class="account-name"
                class="font-size-24 account-name-icon color-success"
              ></svg-icon>
              <span class="income m-r-6">户名</span>
            </div>
            <div class="account-name enter m-t-8 font-size-24">
              <el-tooltip
                class="item"
                effect="dark"
                :content="accountName"
                placement="top-start"
              >
                <div class="font-strong line-1">
                  {{ accountName | noData }}
                </div>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="xx">
          <div class="tx inline-block"></div>
          <div class="zh">
            <div class="flex align-items-center font-size-16 m-t-10">
              <svg-icon
                icon-class="user-circle"
                class="font-size-24 account-name-icon color-warning"
              ></svg-icon>
              <span class="income">应收金额</span>
            </div>
            <div class="pay-name m-t-8 font-size-24">
              <div class="color-warning font-strong line-1">{{
                  ('￥' + NumFormat(payAmount)) | noData
                }}</div>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="xx">
          <div class="tx inline-block"></div>
          <div class="zh">
            <div class="flex align-items-center font-size-16 m-t-10">
              <svg-icon
                icon-class="gross-receipts"
                class="font-size-24 account-name-icon color-warning"
              ></svg-icon>
              <span class="income">实收总额(元)</span>
            </div>
            <div class="pay-name m-t-8 font-size-24">
              <div class="color-warning font-strong line-1">{{
                ('￥' + NumFormat(actualAmount)) | noData
              }}</div>
            </div>
          </div>
        </div>
        <div class="line"></div>
        <div class="xx">
          <div class="tx inline-block"></div>
          <div class="zh">
            <div class="flex align-items-center font-size-16 m-t-10">
              <svg-icon
                icon-class="percentage"
                class="font-size-24 account-name-icon color-warning"
              ></svg-icon>
              <span class="income">回款率</span>
            </div>
            <div class="pay-name m-t-8 font-size-24">
              <span class="color-warning font-strong">{{returnRate}}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import { getHeadInfo } from '../../../api'
import BasicTab from '@/components/BasicTab'
import dayjs from 'dayjs'
export default  {
  name: 'HeadStatistics',
  components: { BasicTab },
  props: {
    type: {
      type: Number,
      default: 1
    },
  },
  data() {
    return {
      NumFormat,
      isLoading: false,
      accountName: '',
      accountNo: '',
      acBal: 0,
      avaBal: 0,
      actualAmount: 0,
      payAmount:0,
      period: '',
      returnRate: 0,
      typeInfo: 1,
      tabsData: [
        {
          label: '账户总览',
          value: 0
        }
      ],
      current: 0
    }
  },
  computed: {
    skeletonNum() {
      return this.typeInfo === 1 ? 4 : 3
    }
  },
  inject: ['accountInfo'],
  methods: {
    async tabsChange(value) {
      this.current = value

    },
    getYearStartEnd(year) {
      const start = dayjs(`${year}-01-01`).startOf('day').format('YYYY-MM-DD')
      const end = dayjs(`${year}-12-31`).endOf('day').format('YYYY-MM-DD')
      return {
        start,
        end
      }
    },
    getInfo(queryLevel, typeInfo) {
      this.isLoading = true
      this.typeInfo = typeInfo

      const { type, year, date1, date2 } = this.accountInfo.fromModel || {}


      let startTime = ''
      let endTime = ''

      if (Array.isArray(date1) && date1.length > 0) {
        const [startPart, endPart] = date1[date1.length - 1].split('~').map(item => item.trim())
        startTime = `${year}-${startPart}`
        endTime = `${year}-${endPart}`
      } else if (Array.isArray(date2) && date2.length > 0) {
        startTime = date2[0]
        endTime = date2[1]
      } else {
        const currentYear = this.getYearStartEnd(year)
        startTime = currentYear.start
        endTime = currentYear.end
      }

      const params = {
        queryLevel,
        year,
        type,
        startTime,
        endTime
      }

      getHeadInfo(params)
        .then(res => {
          const { accountName, accountNo, actualAmount, payAmount,period,returnRate		 } = res || {}
          this.accountName = accountName
          this.accountNo = accountNo
          this.actualAmount = actualAmount
          this.payAmount = payAmount
          this.period = period
          this.returnRate = returnRate
          this.isLoading = false
        })
        .catch(() => {
          this.isLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.financial-content {
  height: auto;
  border-radius: 3px 3px 0 0;
  opacity: 1;
}
.color-p {
  color: #ed7b2f;
}

.line {
  width: 1px;
  height: 60px;
  background: #ebedf1;
  border-radius: 0 0 0 0;
  opacity: 1;
  //position: absolute;
  //left: -25px;
  //top: 30px;
}
.xx {
  position: relative;
  width: 25%;

  .zh {
    position: relative;
    width: 100%;
    padding: 20px 32px;
    z-index: 999;

    .enter {
      width: 97%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .account-name-icon {
      width: 24px;
      height: 24px;
    }
    .font-strong {
      font-weight: 600;
    }

    .introduction {
      font-weight: 400;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      line-height: 20px;
    }
  }
}

.account-name {
  font-weight: 500;
  color: #000;
}

.income {
  margin: 0 4px;
}
.tx {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(207deg, #f0f2f5 0%, #ffffff 100%);
  position: absolute;
  top: 20px;
  left: 50px;
}
</style>
