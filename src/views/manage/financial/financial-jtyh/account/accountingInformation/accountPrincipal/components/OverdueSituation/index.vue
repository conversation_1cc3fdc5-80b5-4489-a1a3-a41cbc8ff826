<template>
  <div class="overdue-situation">
    <div class="head">
      <div class="title">逾期情况</div>
      <div class="flex align-items-center">
        <svg-icon class="color-primary font-size-15" icon-class="time" />
        <span class="color-info m-l-4 font-size-12"
          >统计周期:  {{period}}</span
        >
      </div>
    </div>
    <div class="content">
      <div class="left">
        <div class="item">
          <div class="label">逾期企业数/家</div>
          <div class="value">{{detailData.entCount || 0}}</div>
          <div class="overdue">
            <img src="./image/overdue-1.png" alt="overdue" />
          </div>
        </div>
        <div class="item">
          <div class="label">逾期账单数/个</div>
          <div class="value">{{detailData.billCount || 0}}</div>
          <div class="overdue">
            <img src="./image/overdue-2.png" alt="overdue" />
          </div>
        </div>
        <div class="item">
          <div class="label">逾期金额</div>
          <div class="value">￥{{NumFormat(detailData.overdueAmount)}}</div>
          <div class="overdue">
            <img src="./image/overdue-3.png" alt="overdue" />
          </div>
        </div>
      </div>
      <div class="right">
        <drive-table
          ref="drive-table"
          height="300px"
          :columns="tableColumn"
          v-if="extralQuerys.queryLevel"
          :api-fn="getList"
          :extral-querys="extralQuerys"
          :scroll-top="false"
        >
          <template v-slot:operate-left>
            <el-radio-group v-model="extralQuerys.queryType" size="small" @change="change">
              <el-radio-button :label="-1">全部</el-radio-button>
              <el-radio-button :label="1">只看房租逾期</el-radio-button>
            </el-radio-group>
          </template>
          <template v-slot:operate-right>
            <el-button type="info" size="small" @click="exportExcels">
              <i class="el-icon-download m-r-4"></i>
              导出</el-button
            >
          </template>
        </drive-table>
      </div>
    </div>
  </div>
</template>

<script>
import Column from './column'
import { getList, getOverdueInfo,overdue_export } from './api'
import dayjs from 'dayjs'
import { formatGetParams, NumFormat } from '@/utils/tools'
import downloads from '@/utils/download'
export default {
  mixins: [Column],
  name: 'OverdueSituation',
  data() {
    return {
      getList,
      extralQuerys:{
        endTime:'',
        startTime:'',
        type:'',
        queryLevel:'',
        queryType:-1
      },
      detailData: {},
    }
  },
  inject: ['accountInfo','main'],
  computed: {
    period() {
      const data = this.accountInfo.$refs.headStatistics?.period
      return data
    },
    params() {
      const { type, year, date1, date2 } = this.accountInfo.fromModel || {}

      let startTime = ''
      let endTime = ''

      if (Array.isArray(date1) && date1.length > 0) {
        const [startPart, endPart] = date1[date1.length - 1].split('~').map(item => item.trim())
        startTime = `${year}-${startPart}`
        endTime = `${year}-${endPart}`
      } else if (Array.isArray(date2) && date2.length > 0) {
        startTime = date2[0]
        endTime = date2[1]
      } else {
        const currentYear = this.getYearStartEnd(year)
        startTime = currentYear.start
        endTime = currentYear.end
      }

      return {
        queryLevel: this.extralQuerys.queryLevel,
        year,
        type,
        startTime,
        endTime
      }
    }
  },
  methods:{
    change(){
      this.$refs['drive-table'] && this.$refs['drive-table']?.refreshTable()
    },
    NumFormat,
    getYearStartEnd(year) {
      const start = dayjs(`${year}-01-01`).startOf('day').format('YYYY-MM-DD')
      const end = dayjs(`${year}-12-31`).endOf('day').format('YYYY-MM-DD')
      return {
        start,
        end
      }
    },
    getInfo(queryLevel) {
      const data = this.params
      this.extralQuerys.startTime = data.startTime
      this.extralQuerys.endTime = data.endTime
      this.extralQuerys.type = data.type
      this.extralQuerys.queryLevel = queryLevel
      this.getOverdueInfo()
      this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    },
    getOverdueInfo(){
      getOverdueInfo(this.params).then(res => {
        this.detailData = res || {}
      })
    },
    exportExcels(){
      const { startTime,endTime,queryLevel,type } = this.params
      const params = {
        ...this.$refs['drive-table'].querys,
         startTime,
         endTime,
        queryLevel,
        type
      }
      let url = overdue_export() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(url, 'excel', `逾期情况记录.xls`)
    }
  }
}
</script>

<style scoped lang="scss">
.overdue-situation {
  padding: 42px 40px;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e7e7e7;

  .head {
    display: flex;
    margin-bottom: 32px;
    .title {
      font-weight: 500;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.85);
      line-height: 16px;
      margin-right: 15px;
    }
  }

  .content {
    display: flex;
    .left {
      width: 260px;
      flex: 0 0 260px;
      display: flex;
      flex-direction: column;
      gap: 14px;
      margin-right: 16px;

      .item {
        width: 100%;
        padding: 24px 32px;
        background: #ffffff;
        box-shadow: 0px 8px 12px 0px rgba(237, 123, 47, 0.05);
        border-radius: 6px 6px 6px 6px;
        border: 1px solid rgba(237, 123, 47, 0.1);
        position: relative;

        .label {
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.6);
          line-height: 22px;
          margin-bottom: 14px;
        }

        .value {
          font-weight: 600;
          font-size: 22px;
          color: #ec7b2f;
          line-height: 30px;
        }

        .overdue {
          position: absolute;
          bottom: 0;
          right: 0;

          img {
            width: 116px;
            height: 116px;
          }
        }

        &:hover {
          background: #fffaf5;
        }
      }
    }

    .right {
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
