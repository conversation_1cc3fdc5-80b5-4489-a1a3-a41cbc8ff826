<!--
 * @Author: your name
 * @Date: 2021-10-26 18:11:51
 * @LastEditTime: 2021-12-29 20:07:41
 * @LastEditors: <PERSON>
 * @Description: In User Settings Edit
 * @FilePath: \rat-platform-web\src\views\workspace\financialManagement\currentInformation\index.vue
-->
<template>
  <div>
    <!-- <basic-card title="当日账务信息"> -->
    <div class="p-t-14 p-b-14">
      <div>
        <drive-table
          ref="driveTable"
          height="calc(100vh - 582px)"
          :columns="tableColumnToday"
          :table-data="tableData"
          :scroll-top="false"
          :default-page-size="10"
          is-need-pagination
          @onSearch="handleSearch"
        />
      </div>
    </div>
    <!-- </basic-card> -->
  </div>
</template>

<script>
import {
  findPayAccTree,
  getToDayAccountingList,
  getPayAccInfoById
} from '../api/index'
import CloumnMixins from '../../column'
// import AccountInforFilter from '../components/accountInforFilter'
export default {
  name: 'CurrentInformation',
  mixins: [CloumnMixins],
  components: {
    // AccountInforFilter
  },
  data() {
    return {
      getToDayAccountingList,
      dataSource: {},
      visible: true,
      id: null,
      accountInfo: {}, // 账户信息
      // extralQuerys: {
      //   cusAc: ''
      // },
      tableData: [],
      // eslint-disable-next-line vue/no-reserved-keys
      _tableData: [] // 全部数据
    }
  },
  created() {
    this.findPayAccTree()
  },
  methods: {
    // 查询参数钩子
    // searchQuerysHook(e) {
    //   return {
    //     ...e,
    //     txnTime: moment().format('YYYY-MM-DDT00:00:00.000'),
    //     endTxnTime: moment().format('YYYY-MM-DDT23:59:59.999'),
    //     cusAc: this.extralQuerys.cusAc
    //   }
    // },

    // 批量刷新
    // batchRefresh() {},

    // 获取账户树
    findPayAccTree() {
      findPayAccTree().then(res => {
        // this.getChildren(res)
        this.dataSource = this.getChildren(res)
      })
    },

    // 替换childs为children
    getChildren(res) {
      for (let key in res) {
        res.id = res.id + ''
        if (key === 'childs') {
          res.children = res[key]
        }
        if (typeof res[key] === 'object') {
          this.getChildren(res[key])
        }
      }

      return res
    },

    // 刷新查询时间
    // refreshInfo() {},

    // 打开右侧列表
    open(e, obj) {
      this.visible = false
      this.id = parseInt(e)
      this.getData(obj.cusAc)
      this.$nextTick(() => {
        this.$refs.driveTable.resetSearchForm()
      })
      // this.$set(this.extralQuerys, 'cusAc', obj.cusAc)
      // if (obj.cusAc && this.$refs.driveTable) {
      //   this.$refs.driveTable.resetSearch()
      // }
    },

    // 根据id获取账户信息
    getPayAccInfoById() {
      getPayAccInfoById(this.id).then(res => {
        this.accountInfo = res
      })
    },

    async getData(cusAc) {
      const params = { cusAc, pageNum: 1 }
      const res = await getToDayAccountingList(params)
      this.tableData = res.list
      this._tableData = [...res.list]
    },

    // 前端处理查询操作
    handleSearch(query) {
      if (!query || query === {}) {
        this.tableData = [...this._tableData]
        return
      }
      let tableData = [...this._tableData]
      for (const key in query) {
        if (key === 'dcFlag') {
          tableData = tableData.filter(
            x => Number(x[key]) === Number(query[key])
          )
        } else {
          tableData = tableData.filter(
            x => x[key] !== null && x[key].includes(query[key])
          )
        }
      }
      this.tableData = tableData
    }
  }
}
</script>

<style lang="scss" scoped></style>
