<template>
  <div class="main-wrapper h100 flex">
    <account-structure
      :menu-disabled="menuDisabled"
      @change="changeAccountData"
      ref="accountStructure"
      class="account-structure"
    />

    <account-principal ref="accountPrincipal" class="account-principal" />
  </div>
</template>

<script>
import AccountStructure from './accountStructure'
import AccountPrincipal from './accountPrincipal'
import { NumFormat } from '@/utils/tools'

export default {
  name: 'AccountInformation',
  components: {
    AccountStructure,
    AccountPrincipal
  },
  provide() {
    return {
      main: this
    }
  },
  data() {
    return {
      menuDisabled: false,
      isLoading: false,
      NumFormat,
      accountType: 1,
      accountData: {}
    }
  },
  methods: {
    async changeAccountData(data) {
      this.accountType = data.lv
      this.accountData = data
      await this.$nextTick()
      const element = this.$refs.accountPrincipal.$refs.corporateStructure
      if (element) {
        element.updateTabs(this.accountType)
        element.updateHeadData(data.value, data.lv)
        await element.updateChartData(data.value)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-wrapper {
  height: calc(100vh - 90px);
  padding: 24px;
  background: #fff;
  .account-structure {
    width: 264px;
    height: 100%;
    padding: 0 24px 24px 0;
    border-right: 1px solid #e7e7e7;
  }
  .account-principal {
    flex: 1;
    overflow: hidden;
  }
}
</style>
