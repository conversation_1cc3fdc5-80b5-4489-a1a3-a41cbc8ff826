<template>
  <div>
    <div class="m-b-16">
      <el-form ref="form" :model="formModel" label-width="80px">
        <el-col :span="6">
          <el-radio-group v-model="formModel.reconciliation">
            <el-radio-button
              :label="item.value"
              v-for="item in reconciliationOptions"
              :key="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </el-col>
        <el-col :span="18" class="flex justify-content-end">
          <el-col :span="10">
            <el-form-item label="企业名称">
              <el-input
                v-model="formModel.entName"
                placeholder="请输入企业名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="9.5">
            <el-form-item label="对账时间">
              <el-date-picker
                v-model="formModel.time"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                value-format="YYYY-MM-DD"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-col>
      </el-form>
    </div>
    <drive-table
      ref="driveTable"
      height="calc(100vh - 420px)"
      :columns="reconciliationRecordsColumn"
      :table-data="tableData"
      :scroll-top="false"
      :default-page-size="10"
      is-need-pagination
    />
  </div>
</template>

<script>
import CommonMixin from '../column'
export default {
  name: 'ReconciliationRecords',
  mixins: [CommonMixin],
  data() {
    return {
      reconciliationOptions: [
        {
          label: '昨日对账',
          value: 1
        },
        {
          label: '历史对账',
          value: 2
        }
      ],
      formModel: {
        reconciliation: 1,
        entName: '',
        time: ''
      },
      tableData: [
        {
          id: 1,
          entName: '安徽中安创谷科技园有限公司',
          cusAc: 464545,
          acBal: 100000,
          flushTime: '2021-01-01',
          acSts: '正常'
        },
        {
          id: 2,
          entName: '安徽中安创谷科技园有限公司',
          cusAc: 464545,
          acBal: 100000,
          flushTime: '2021-01-01',
          acSts: '平账'
        }
      ]
    }
  },
  methods: {
    getInfo(val) {
      console.log('ReconciliationRecords---', val)
    }
  }
}
</script>

<style scoped lang="scss"></style>
