<template>
  <div>
    <basic-switch-tab width="80" :tabs="tabs" @change="change" />
    <div class="grid">
      <div class="account-overview" v-for="item in accountData" :key="item.id">
        <div class="p-t-24 p-l-24 p-r-24">
          <div class="title">{{ item.title }}</div>
          <div class="font-strong font-size-24 line-height-40">
            <span class="font-size-18">￥</span>
            <span>
              {{ NumFormat(item.totalAmount) }}
            </span>
          </div>
          <div class="line"></div>
        </div>
        <overview-echarts
          ref="overviewEcharts"
          :title="item.title"
          :time="item.time"
          :chartData="item.data"
          :key="item.id"
        />
      </div>
    </div>
  </div>
</template>

<script>
import OverviewEcharts from '../../../components/OverviewEcharts'
import { getAccountOverview } from '../../../api'
import BasicSwitchTab from '@/components/BasicSwitchTab'
import { NumFormat } from '@/utils/tools'
export default {
  name: 'AccountOverview',
  inject: ['accountInfo'],
  data() {
    return {
      NumFormat,
      value: '',
      tabs: [
        {
          label: '年',
          value: 2
        },
        {
          label: '月',
          value: 1
        },
        {
          label: '日',
          value: 0
        }
      ],
      current: 2,
      accountData: []
    }
  },
  components: {
    BasicSwitchTab,
    OverviewEcharts
  },
  methods: {
    change(value) {
      this.current = value
      this.getInfo(this.accountInfo.value)
    },
    getInfo(value) {
      const data = {
        value,
        time: this.current
      }
      getAccountOverview(data).then(res => {
        this.accountData = res || []
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.account-overview {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 6px 6px 6px 6px;
  box-sizing: border-box;
  opacity: 1;
  border: 1px solid #e7e7e7;

  .title {
    width: 100%;
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
  }

  .line {
    width: 100%;
    margin-top: 24px;
    height: 1px;
    background: #e7e7e7;
    opacity: 1;
  }
}
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  grid-gap: 16px;
  margin-top: 24px;
}

.account-overview:hover {
  background-color: #fffaf5;
}
</style>
