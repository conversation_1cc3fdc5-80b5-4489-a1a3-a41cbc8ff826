// 获取账单状态
export function getBillStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag type="primary" label="执行中"></basic-tag>
    case 2:
      return <basic-tag type="success" label="已完成"></basic-tag>
    case 3:
      return <basic-tag type="danger" label="已终止"></basic-tag>
    default:
      return '-'
  }
}
export function getVerificationType(h, val, label) {
  switch (val) {
    case 1:
      return <basic-tag type="primary" label={label || '未核销'}></basic-tag>
    case 2:
      return <basic-tag type="primary" label={label || '部分核销'}></basic-tag>
    case 3:
      return <basic-tag type="success" label={label || '全额核销'}></basic-tag>
    case 4:
      return <basic-tag type="danger" label={label || '已作废'}></basic-tag>
    case 5:
      return (
        <basic-tag type="warning" label={label || '在线支付挂起'}></basic-tag>
      )
    case 6:
      return (
        <basic-tag type="success" label={label || '在线支付完成'}></basic-tag>
      )
    default:
      return <basic-tag type="success" label={label || '--'}></basic-tag>
  }
}
export function getOverdueStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag type="success" label="未逾期"></basic-tag>
    case 2:
      return <basic-tag type="warning" label="短时逾期"></basic-tag>
    case 3:
      return <basic-tag type="danger" label="长时逾期"></basic-tag>
    default:
      return '-'
  }
}
// 支付状态
export function getCostStatus(h, val) {
  switch (val) {
    case 1:
      return <el-tag type="primary">未核销</el-tag>
    case 2:
      return <el-tag type="danger">部分核销</el-tag>
    case 3:
      return <el-tag type="success">全额核销</el-tag>
    case 4:
      return <el-tag type="info">已作废</el-tag>
    case 5:
      return <el-tag type="primary">在线支付挂起</el-tag>
    case 6:
      return <el-tag type="success">在线支付完成</el-tag>
    default:
      return '-'
  }
}

// 支付方式
export function getPayType(h, val) {
  switch (val) {
    case 1:
      return <el-tag type="primary">账户支付</el-tag>
    case 2:
      return <el-tag type="primary">钱包支付</el-tag>
    default:
      return '-'
  }
}

// 修改类型
export function getChangeType(h, val) {
  switch (val) {
    case 1:
      return <el-tag type="danger">作废</el-tag>
    case 2:
      return <el-tag type="success">修改</el-tag>
    case 3:
      return <el-tag type="primary">拆分</el-tag>
    default:
      return '-'
  }
}
// 获取交易状态
export function tradeType(h, val) {
  switch (val) {
    case 1:
      return '初始'
    case 2:
      return '成功'
    case 3:
      return '失败'
    case 4:
      return '超时'
    default:
      return '-'
  }
}

export function getWriteOffStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag type="info" label="待核销"></basic-tag>
    case 2:
      return <basic-tag type="success" label="部分核销"></basic-tag>
    case 3:
      return <basic-tag type="success" label="已核销"></basic-tag>
    default:
      return '-'
  }
}

export function getWriteOffList() {
  return [
    {
      label: '待核销',
      value: 1
    },
    {
      label: '部分核销',
      value: 2
    },
    {
      label: '已核销',
      value: 3
    }
  ]
}

export function getOpenStatusType(h, val, label) {
  switch (val) {
    case 1:
      return <basic-tag type="primary" label={label || '未开'}></basic-tag>
    case 2:
      return <basic-tag type="primary" label={label || '部分开票'}></basic-tag>
    case 3:
      return <basic-tag type="success" label={label || '已开'}></basic-tag>
    default:
      return <basic-tag type="success" label={label || '--'}></basic-tag>
  }
}
