<!-- 合同应收 -->
<template>
  <basic-card>
    <template slot="right">
      <div>
        <el-button size="mini" @click="exportExcel" class="m-l-8" type="info">
          <svg-icon icon-class="cloud-download" />
          <span>导出</span></el-button
        >
      </div>
    </template>
    <div>
      <div class="financial-content bg-white">
        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>合同总数</span>
              </div>
              <div
                class="acconutname m-t-17 font-size-24 flex align-items-center"
              >
                <span>{{ numberAll.contractNmu | noData }}</span>
                <span class="font-size-18 font-strong-600 m-l-4"> 份</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>合同总金额</span>
              </div>
              <div class="acconut-name m-t-17 font-size-24">
                <span class="font-size-18">￥</span>
                <span>{{ NumFormat(numberAll.sumRent) | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>应收总金额</span>
              </div>
              <div class="acconut-name m-t-17 font-size-24">
                <span class="font-size-18">￥</span>
                <span>{{ NumFormat(numberAll.sumReceivable) | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center pointer">
                <span class="line"></span>
                <span>实收总金额</span>
              </div>
              <div class="payname m-t-17 font-size-24">
                <span class="font-size-18">￥</span>
                <span>{{ NumFormat(numberAll.sumReceived) | noData }}</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh" style="width: 80%">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>回款率</span>
              </div>
              <div class="w100 m-t-18">
                <el-progress
                  :text-inside="true"
                  :stroke-width="20"
                  :percentage="rate"
                  status="success"
                ></el-progress>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <basic-tab
      ref="basicTab"
      :tabs-data="parkList"
      :current="current"
      @tabsChange="tabsChange"
    />
    <!--    表单-->

    <div class="m-b-6">
      <el-form ref="fromAccountInfo" :model="fromTableInfo">
        <el-row>
          <el-col :span="16">
            <el-form-item class="m-l-8">
              <!--              按合同按企业-->
              <button-switch
                :label-content="labelContent"
                @toggle="chageTab"
              ></button-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div class="w100 flex justify-content-end">
              <el-form-item class="m-l-8" v-if="activeName === 1">
                <el-select
                  style="width: 140px"
                  v-model="fromTableInfo.bsType"
                  placeholder="请选择合同类型"
                  @change="changeContractFn"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="item in contractOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="m-l-8" v-if="activeName === 2">
                <el-select
                  style="width: 140px"
                  v-model="fromTableInfo.blType"
                  placeholder="请选择合同类型"
                  @change="changeContractBlTypeFn"
                  :popper-append-to-body="false"
                >
                  <el-option
                    v-for="item in contractAllOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="m-l-8" v-if="contractType === 1">
                <el-date-picker
                  v-model="fromTableInfo.signDateType"
                  type="daterange"
                  align="right"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="yyyy-MM-dd"
                  @change="searchQuery($event, 'signDateType')"
                  :picker-options="pickerOptions"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item class="m-l-8" v-if="contractType === 1">
                <el-select
                  style="width: 140px"
                  clearable
                  v-model="fromTableInfo.queryStatus"
                  placeholder="请选择合同状态"
                  :popper-append-to-body="false"
                  @change="searchQuery($event, 'queryStatus')"
                >
                  <el-option
                    v-for="item in contractStatusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item class="m-l-8" v-if="contractType === 1">
                <el-date-picker
                  v-model="fromTableInfo.startDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="yyyy-MM-dd"
                  @change="searchQuery($event, 'startDate')"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item class="m-l-8">
                <el-input
                  clearable
                  style="width: 288px"
                  @input="searchQuery($event, 'queryParam')"
                  :placeholder="`请输入${
                    activeName === 1 ? '企业' : '主体'
                  }名称或合同编号${activeName === 2 ? '或姓名' : ''}`"
                  v-model="fromTableInfo.queryParam"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <drive-table
      v-if="contractType === 1"
      ref="drive-table"
      :api-fn="getContractRecordListAsTypeOne"
      :columns="tableColumn"
      :extralQuerys="extralQuerys"
    >
    </drive-table>
    <drive-table
      v-if="contractType === 2"
      ref="drive-table2"
      :api-fn="apartmentApi"
      :columns="tableColumnFirm"
      :extralQuerys="extralQuerys"
    >
      <!--            :api-fn="getContractReceivables"-->
    </drive-table>
  </basic-card>
</template>

<script>
import ButtonSwitch from '../components/ButtonSwitch'
import BasicTab from './components/BasicTab'
import { formatGetParams, NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import downloads from '@/utils/download'
import {
  getContractRecordListAsTypeOne,
  getContractRecordListAsTypeTwo,
  getAllParkApi,
  getNumberDetail,
  getContractFindSinglePage
} from './api'
import {
  contractEntListExcel,
  contractIndListExcel,
  contractListExcel
} from '@/views/manage/financial/financial-basic/payment/contractReceivables-basic/api'
import dayjs from 'dayjs'
export default {
  name: 'contractReceivables',
  components: {
    BasicTab,
    ButtonSwitch
  },
  mixins: [ColumnMixins],
  data() {
    return {
      pickerOptions: {
        shortcuts: [
          {
            text: '今日',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime())
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(
                start.getTime() - 3600 * 1000 * 24 * (start.getDay() - 1)
              )
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本季度',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              const month = start.getMonth()
              if (month < 3) {
                start.setMonth(0)
              } else if (month < 6) {
                start.setMonth(3)
              } else if (month < 9) {
                start.setMonth(6)
              } else {
                start.setMonth(9)
              }
              start.setDate(1)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '本年',

            onClick(picker) {
              const end = new Date()
              const start = new Date()
              //起始时间为本年的第一天
              start.setDate(1)
              start.setMonth(0)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '近3年',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 3)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '3年以前',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365 * 10)
              end.setTime(end.getTime() - 3600 * 1000 * 24 * 365 * 3)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      labelContent: [
        {
          label: '租赁合同',
          value: 1
        },
        {
          label: '公寓合同',
          value: 2,
          permission: 'APARTMENT'
        }
      ],
      apartmentApi: null,
      isEnt: 0,
      activeName: 1,
      getContractFindSinglePage,
      compare: 1,
      extralQuerys: {
        contractType: null,
        signDateType: [],
        startDate: '',
        queryStatus: '',
        queryParam: '',
        parkNames: '',
        bsType: 0, //合同类型 0-租赁合同；2-公寓合同
        blType: 0 //0全部  1按业 2个人
      },
      contractAllOption: [
        {
          label: '按全部',
          value: 0
        },
        {
          label: '按企业',
          value: 1
        },
        {
          label: '按个人',
          value: 2
        }
      ],
      contractOption: [
        {
          label: '按合同',
          value: 0
        },
        {
          label: '按企业',
          value: 1
        }
      ],
      compareList: [
        {
          label: '日',
          value: 1
        },
        {
          label: '周',
          value: 2
        },
        {
          label: '月',
          value: 3
        },
        {
          label: '季度',
          value: 4
        },
        {
          label: '年',
          value: 5
        }
      ],
      NumFormat,
      getContractRecordListAsTypeOne,
      getContractRecordListAsTypeTwo,
      // getContractReceivables,
      accountInfo: {}, // 账户信息
      dataObservationform: {
        day: ''
      },
      // contractList: [],
      // contractListTwo: [],
      dayList: [
        { label: '全部', value: null },
        { label: '今日', value: 1 },
        { label: '本周', value: 2 },
        { label: '本月', value: 3 },
        { label: '本季度', value: 4 },
        { label: '本年', value: 5 },
        { label: '近三年', value: 6 },
        { label: '三年前', value: 7 }
      ],
      current: 0,
      parkList: [{ label: '全部', value: 0 }],
      fromTableInfo: {
        contractType: null,
        signDateType: [],
        startDate: '',
        queryStatus: '',
        queryParam: '',
        parkNames: '',
        bsType: 0,
        blType: 0
      }, //签约状态
      contractStatusList: [
        { label: '全部', value: null },
        { label: '待生效', value: 4 },
        { label: '执行中', value: 5 },
        { label: '已终止', value: 6 },
        { label: '已到期', value: 9 }
      ], //合同状态
      contractType: 1, //合同类型
      numberAll: {},
      rate: 0
    }
  },
  mounted() {
    //获取全部园区
    this.getAllPark()
    //获取显示数据
    // this.getNumber()
    //按合同查询应收列表
    // this.getChangeConcartData()
  },
  methods: {
    changeContractBlTypeFn(e) {
      e === 0 ? (this.contractType = 1) : ''
      e === 1 || e === 2 ? (this.contractType = 2) : ''
      this.isEnt = e
      this.fromTableInfo.blType = e
      this.$nextTick(() => {
        if (e === 0) {
          this.extralQuerys.blType = 0
        } else if (e === 1) {
          this.extralQuerys.blType = 1
          this.$refs['drive-table']?.triggerSearch()
        } else if (e === 2) {
          this.extralQuerys.blType = 2
          this.$refs['drive-table2']?.triggerSearch()
        }
      })
    },
    changeContractFn(e) {
      this.fromTableInfo.blType = e
      this.isEnt = e === 0 ? 0 : 1
      this.contractType = e === 0 ? 1 : 2
      if (this.contractType === 1 || e === 1) {
        this.$refs['drive-table']?.triggerSearch()
      } else {
        this.$refs['drive-table2']?.triggerSearch()
      }
    },
    // 处理头部数据
    dataHander(data) {
      if (!data) return 0
      if (data < 0) {
        return `${data}`
      } else {
        return `+${data}`
      }
    },
    compareSelectChange(e) {
      this.compare = e
      this.getNumber()
    },
    //切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.parkNames = e === 0 ? '' : e
      if (this.contractType === 1) {
        this.$refs['drive-table'].triggerSearch()
      } else {
        this.$refs['drive-table2'].triggerSearch()
      }
    },
    chageTab(type) {
      this.activeName = type
      this.extralQuerys.bsType = type === 1 ? 0 : 2
      this.fromTableInfo = {
        contractType: null,
        signDateType: [],
        startDate: '',
        queryStatus: '',
        queryParam: '',
        parkNames: '',
        bsType: 0,
        blType: 0
      }
      this.tableColumn[1].label = type === 1 ? '企业名称' : '签约主体'
      this.tableColumnFirm[0].label = type === 1 ? '企业名称' : '签约主体'
      // this.extralQuerys = this.$options.data().extralQuerys
      this.resetExtralQuerys(type)
      this.extralQuerys.bsType = type === 1 ? 0 : 2
      this.contractType = 1
      this.isEnt = 0
      this.$nextTick(() => {
        if (this.contractType === 1) {
          this.$refs['drive-table']?.triggerSearch()
        } else {
          this.$refs['drive-table2']?.triggerSearch()
        }
      })
    },
    resetExtralQuerys(type) {
      this.extralQuerys = {
        contractType: null,
        signDateType: [],
        startDate: '',
        queryStatus: '',
        queryParam: '',
        parkNames: this.extralQuerys.parkNames,
        bsType: type === 1 ? 0 : 2, //合同类型 0-租赁合同；2-公寓合同
        blType: 0 //0全部  1按业 2个人
      }
    },
    searchQuerysHook() {
      return this.fromTableInfo
    },
    // 搜索查询
    searchQuery(e, type) {
      if (type === 'searchQuery') {
        this.extralQuerys[type] = JSON.stringify(e)
      } else {
        this.extralQuerys[type] = e
      }
      if (this.contractType === 1) {
        this.$refs['drive-table'].triggerSearch()
      } else {
        this.$refs['drive-table2'].triggerSearch()
      }
    },
    goDetail(row) {
      let { entId, orderId, enterStatus = 0 } = row
      if (enterStatus === 1 || enterStatus === 2) {
        this.$router.push({
          path: '/business/enterpriseDetails',
          query: {
            id: entId,
            orderId
          }
        })
      }
    },
    async getNumber() {
      const {
        contractNmu,
        sumReceivable,
        sumReceived,
        sumRent,
        contractItem,
        contractRatio,
        sumReceivableItem,
        sumReceivableRatio,
        sumReceivedItem,
        sumReceivedRatio,
        sumRentItem,
        sumRentRatio
      } = await getNumberDetail({
        type: this.compare,
        bsType: this.activeName === 1 ? 0 : 2
      })
      this.numberAll = {
        contractItem,
        contractNmu,
        contractRatio,
        sumReceivable,
        sumReceivableItem,
        sumReceivableRatio,
        sumReceived,
        sumReceivedItem,
        sumReceivedRatio,
        sumRent,
        sumRentItem,
        sumRentRatio
      }
      this.rate = Number(((sumReceived / sumReceivable) * 100).toFixed(2)) || 0
    },
    //更改查询条件查询列表
    // async getChangeConcartData() {
    //   if (this.contractType === 1) {
    //     const info = await getContractRecordListAsTypeOne(this.fromTableInfo)
    //     this.contractList = info.list
    //     return info
    //   } else {
    //     const info = await getContractRecordListAsTypeTwo(this.fromTableInfo)
    //     this.contractListTwo = info.list
    //     return info
    //   }
    // },
    //获取全部园区
    async getAllPark() {
      const info = await getAllParkApi()
      info.forEach(item => {
        this.parkList.push({ label: item.park, value: item.id })
      })
    },
    // 导出excel
    exportExcel() {
      if (this.fromTableInfo.blType === 0) {
        let url = contractListExcel() + '?'
        url += formatGetParams(this.$refs['drive-table'].querys)
        downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '合同应收明细台账.xls')
      } else if (this.fromTableInfo.blType === 1) {
        let url = contractEntListExcel() + '?'
        url += formatGetParams(this.$refs['drive-table2'].querys)
        downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '合同应收企业台账.xls')
      } else {
        let url = contractIndListExcel() + '?'
        url += formatGetParams(this.$refs['drive-table2'].querys)
        downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '合同应收个人台账.xls')
      }
    }
  },
  watch: {
    isEnt: {
      handler(val) {
        if (val === 0) {
          this.apartmentApi = getContractRecordListAsTypeOne
        } else if (val === 1) {
          this.apartmentApi = getContractRecordListAsTypeTwo
        } else if (val === 2) {
          this.apartmentApi = getContractFindSinglePage
        }
      },
      immediate: true,
      deep: true
    },
    activeName: {
      handler() {
        this.getNumber()
      },
      immediate: true,
      deep: true
    }
  },
  activated() {
    this.$refs['drive-table'] && this.$refs['drive-table'].refreshTable()
    this.$refs['drive-table2'] && this.$refs['drive-table2'].refreshTable()
  }
}
</script>

<style lang="scss" scoped>
.tabs {
  display: flex;
  //过渡
  transition: all 0.3s;
  overflow-x: hidden;
  overflow-y: hidden;
  white-space: nowrap;
}
.tabs:hover {
  transition: all 0.3s;
  overflow-x: auto;
}

.tabs-item {
  width: 200px;
  flex: 0.4;
  height: 45px;
  line-height: 45px;
  position: relative;
  cursor: pointer;

  &.active {
    @include font_color(--color-primary);
    &::before {
      content: '';
      width: 100%;
      height: 4px;
      @include background-color(--color-primary);
      position: absolute;
      bottom: -2px;
      left: 0;
    }
  }
}
.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: auto;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}
.qe {
  width: 100%;
  height: 32px;
}
</style>
