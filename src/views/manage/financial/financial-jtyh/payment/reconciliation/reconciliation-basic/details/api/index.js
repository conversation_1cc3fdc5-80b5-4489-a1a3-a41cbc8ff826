import request from '@/utils/request'

// 获得详情
export function getDetails(id) {
  return request({
    url: `/pj/consult-record/getDetail?id=${id}`,
    method: 'get'
  })
}

// 审核接口
export function examine(data) {
  return request({
    url: `/pj/consult-record/examine`,
    method: 'post',
    data
  })
}

// 修改路演接口
export function updateShow(data) {
  return request({
    url: `/pj/consult-record/updateShow`,
    method: 'post',
    data
  })
}

//查看开票申请信息详情 /admin-api/ticket/apply/get
export function ticketDetails(params) {
  return request({
    url: '/ticket/apply/get',
    method: 'get',
    params
  })
}

// 园区退回接口/admin-api
export function returnExamine(data) {
  return request({
    url: `/ticket/apply/examine`,
    method: 'post',
    data
  })
}
