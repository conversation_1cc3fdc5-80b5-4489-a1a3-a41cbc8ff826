import { validateDecimal } from '@/utils/validate'
import ProjectTree from './ProjectTree'

export default {
  components: { ProjectTree },
  data() {
    return {
      formConfigure: {
        descriptors: {
          projectName: {
            form: 'input',
            label: '分组名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入分组名称'
              }
            ],
            attrs: {
              maxlength: 15
            }
          },
          projectIds: {
            form: 'component',
            label: '关联楼栋',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择关联楼栋'
              }
            ],
            render: () => {
              return (
                <project-tree
                  ref={'projectTree'}
                  options={this.projectTreeData}
                  v-model={this.fromModel.projectIds}
                />
              )
            }
          }
        }
      },
      taxFormConfigure: {
        descriptors: {
          typeKey: {
            form: 'select',
            label: '费用类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择费用类型'
              }
            ],
            options: []
          },
          freeTax: {
            form: 'radio',
            label: '是否免税',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择是否免税'
              }
            ],
            options: [
              { label: '是', value: true },
              { label: '否', value: false }
            ]
          },
          taxPercent: {
            form: 'input',
            label: '税率',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入税率'
              },
              {
                validator: (rule, value, callback) => {
                  validateDecimal(rule, value, callback)
                }
              }
            ],
            attrs: {
              maxlength: 15
            },
            customRight: () => {
              return <div class="line-height-32 font-size-14">%</div>
            }
          },
          invModel: {
            form: 'input',
            label: '规格型号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入规格型号'
              }
            ],
            attrs: {
              maxlength: 15
            }
          },
          invUnit: {
            form: 'input',
            label: '计量单位',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入计量单位'
              }
            ],
            attrs: {
              maxlength: 10
            }
          },
          price: {
            form: 'input',
            label: '单价',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入单价'
              },
              {
                validator: (rule, value, callback) => {
                  const val = Number(value)
                  if (isNaN(val)) return callback(new Error('请输入数字'))
                  const regex = /^(\d+\.?\d{0,8})$/
                  if (!regex.test(value))
                    return callback(new Error('最多保留8位小数'))
                  callback()
                }
              }
            ],
            attrs: {
              maxlength: 15
            },
            customRight: () => {
              return <div class="line-height-32 font-size-14">元</div>
            }
          }
        }
      }
    }
  }
}
