<template>
  <dialog-cmp
    title="确认开票"
    :visible.sync="dialogVisible"
    @confirmDialog="confirmDialog"
    width="40%"
  >
    <driven-form
      v-if="dialogVisible"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
      label-position="top"
    />
  </dialog-cmp>
</template>

<script>
import DescriptorMixin from './descriptor'
import { invApplyRecordConfirm } from '../../../api'

export default {
  name: 'ConfirmBill',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      dialogVisible: false,
      fromModel: {
        type: 1
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.fromModel = this.$options.data().fromModel
      }
    }
  },
  methods: {
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel,
          applyId: this.$route.query.id
        }
        invApplyRecordConfirm(params).then(() => {
          this.$emit('updateHandle')
          this.$toast.success('开票成功')
          this.dialogVisible = false
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.custom-tips) {
  margin-left: 0 !important;
}
</style>
