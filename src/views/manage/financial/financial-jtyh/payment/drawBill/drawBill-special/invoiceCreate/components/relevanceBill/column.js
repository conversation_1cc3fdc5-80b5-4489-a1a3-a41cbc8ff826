import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '主体名称',
          prop: 'bodyName',
          minWidth: 180,
          render: (h, scope) => {
            return (
              <div>
                {scope.row.bodyName}
                {scope.row.acBodyName ? (
                  <el-tooltip
                    effect="dark"
                    content={'实际入驻: ' + scope.row.acBodyName}
                    placement="top"
                  >
                    <svg-icon class="m-l-8 color-primary" icon-class="link-m" />
                  </el-tooltip>
                ) : (
                  ''
                )}
              </div>
            )
          }
        },
        {
          label: '账期',
          prop: 'date',
          minWidth: 180,
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          label: '费用类型',
          prop: 'feeTypeStr'
        },
        {
          label: '逾期状态',
          prop: 'overdueStatusStr',
          render: (h, scope) => {
            const obj = {
              1: 'success',
              2: 'warning',
              3: 'danger'
            }
            return (
              <basic-tag
                type={obj[scope.row.overdueStatus]}
                label={scope.row.overdueStatusStr}
              ></basic-tag>
            )
          }
        },
        {
          label: '逾期天数',
          prop: 'overdueDays'
        },
        {
          label: '应收金额(元)',
          prop: 'payAmount',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payAmount)}
              </div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'collectAmount',
          minWidth: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.collectAmount)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'billStatusStr',
          render: (h, scope) => {
            const obj = {
              1: 'primary',
              2: 'primary',
              3: 'success',
              4: 'danger',
              5: 'warning',
              6: 'success'
            }
            return (
              <basic-tag
                type={obj[scope.row.billStatus]}
                label={scope.row.billStatusStr}
              ></basic-tag>
            )
          }
        },
        {
          label: '剩余可开票金额(元)',
          prop: 'residueInvAmount',
          minWidth: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.residueInvAmount)}
              </div>
            )
          }
        },
        {
          label: '开票状态',
          prop: 'openStatusStr'
        }
      ]
    }
  }
}
