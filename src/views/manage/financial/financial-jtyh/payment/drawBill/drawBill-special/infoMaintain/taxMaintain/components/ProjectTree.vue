<template>
  <el-tree
    :data="options"
    show-checkbox
    node-key="id"
    ref="tree"
    highlight-current
    :props="defaultProps"
    @check-change="checkChange"
  >
  </el-tree>
</template>

<script>
export default {
  name: 'ProjectTree',
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      required: true
    }
  },
  data() {
    return {
      defaultProps: {
        children: 'buildTree',
        label: 'label'
      }
    }
  },
  watch: {
    value: {
      async handler(val) {
        if (Array.isArray(val) && val.length) {
          await this.$nextTick()
          this.$refs.tree.setCheckedKeys(val)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    checkChange() {
      const ids = this.options.map(item => item.id)
      const keys = this.$refs.tree.getCheckedKeys()
      keys.forEach((item, index) => {
        if (ids.includes(item)) {
          keys.splice(index, 1)
        }
      })
      this.$emit('input', keys)
    }
  }
}
</script>

<style scoped></style>
