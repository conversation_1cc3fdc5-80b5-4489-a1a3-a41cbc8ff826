<template>
  <div class="invoice-container">
    <div class="code-wrapper">
      <div class="flex">
        <div class="label w-70 text-justify">发票代码：</div>
        <div class="content">XXXXXXXXXXX</div>
      </div>
      <div class="flex m-t-5">
        <div class="label w-70 text-justify">发票号码：</div>
        <div class="content">XXXXXXXXXXX</div>
      </div>
      <div class="flex m-t-5">
        <div class="label w-70 text-justify">开票日期：</div>
        <div class="content">XXXXXXXXXXX</div>
      </div>
      <div class="flex m-t-5">
        <div class="label w-70 text-justify">检验码：</div>
        <div class="content">XXXXXXXXXXX</div>
      </div>
    </div>
    <div class="title">xx增值税xx票xx发票</div>
    <div class="invoice-table m-t-32">
      <div class="border-bottom flex">
        <div class="vertical-text label border-right w-32">购买方</div>
        <div class="border-right p-t-10 p-l-10 p-b-10 info-wrapper">
          <div class="flex">
            <div class="label w-98 text-justify">名称：</div>
            <div class="content">{{ info.bodyName }}</div>
          </div>
          <div class="flex m-t-5">
            <div class="label w-98 text-justify">纳税人识别号：</div>
            <div class="content">{{ info.buyerCode }}</div>
          </div>
          <div class="flex m-t-5">
            <div class="label w-98 text-justify">地址、电话：</div>
            <div class="content">
              {{ info.buyerAddress }} {{ info.buyerContact }}
            </div>
          </div>
          <div class="flex m-t-5">
            <div class="label w-98 text-justify">开户行及账号：</div>
            <div class="content">
              {{ info.buyerBankName }} {{ info.buyerBankNo }}
            </div>
          </div>
        </div>
        <div class="vertical-text label border-right w-32">密码区</div>
        <div class="p-t-10 p-l-10 p-b-10 password-wrapper"></div>
      </div>
      <div class="tax-wrapper border-bottom">
        <div class="w100 flex">
          <div class="label span-4">订单名称/订单号</div>
          <div class="label span-4">规格型号</div>
          <div class="label span-3">单位</div>
          <div class="label span-3">数量</div>
          <div class="label span-3">单价</div>
          <div class="label span-3">金额</div>
          <div class="label span-3">税率</div>
          <div class="label span-3">税额</div>
        </div>
        <div
          class="w100 m-t-5 flex"
          v-for="(item, index) in info.billList || []"
          :key="index"
        >
          <div class="content span-4">{{ item.feeTypeStr }}</div>
          <div class="content span-4">{{ item.invModel }}</div>
          <div class="content span-3">{{ item.invUnit }}</div>
          <div class="content span-3">{{ item.number }}</div>
          <div class="content span-3">{{ item.price }}</div>
          <div class="content span-3">￥{{ NumFormat(item.amount || 0) }}</div>
          <div class="content span-3">
            {{ item.freeTax ? '免税' : item.tax + '%' }}
          </div>
          <div class="content span-3">
            ￥{{ NumFormat(item.taxAmount || 0) }}
          </div>
        </div>
        <!-- 合计 -->
        <div class="w100 flex" style="margin-top: 50px">
          <div class="content span-4">合计</div>
          <div class="content span-4"></div>
          <div class="content span-3"></div>
          <div class="content span-3"></div>
          <div class="content span-3"></div>
          <div class="content span-3">
            ￥{{ NumFormat(info.sumAmount || 0) }}
          </div>
          <div class="content span-3"></div>
          <div class="content span-3">￥{{ NumFormat(info.sumTax || 0) }}</div>
        </div>
      </div>
      <div class="upper-lower border-bottom label flex align-items-center">
        <div class="upper-label border-right">价税合计（大写）</div>
        <div class="upper-label upper-content content">
          <span class="m-r-2">ⓧ</span>
          {{ numberToChinese(info.sumTaxAmount || 0) }}
        </div>
        <div class="upper-label upper-content">
          （小写）
          <span class="content">￥{{ NumFormat(info.sumTaxAmount || 0) }}</span>
        </div>
      </div>
      <div class="flex">
        <div class="vertical-text label border-right w-32">销售方</div>
        <div class="border-right p-t-10 p-l-10 p-b-10 info-wrapper">
          <div class="flex">
            <div class="label w-98 text-justify">名称：</div>
            <div class="content">{{ info?.invSaleConfigRespVO?.saleName }}</div>
          </div>
          <div class="flex m-t-5">
            <div class="label w-98 text-justify">纳税人识别号：</div>
            <div class="content">
              {{ info?.invSaleConfigRespVO?.taxpayerNumber }}
            </div>
          </div>
          <div class="flex m-t-5">
            <div class="label w-98 text-justify">地址、电话：</div>
            <div class="content">
              {{ info?.invSaleConfigRespVO?.saleAddress }}
              {{ info?.invSaleConfigRespVO?.contactNumber }}
            </div>
          </div>
          <div class="flex m-t-5">
            <div class="label w-98 text-justify">开户行及账号：</div>
            <div class="content">
              {{ info?.invSaleConfigRespVO?.saleBankName }}
              {{ info?.invSaleConfigRespVO?.saleBankNo }}
            </div>
          </div>
        </div>
        <div class="vertical-text label border-right w-32">备注</div>
        <div class="p-t-10 p-l-10 p-b-10 password-wrapper">
          <div class="content comment-item">
            {{ info.invRemark }}
          </div>
        </div>
      </div>
    </div>
    <el-row class="m-t-20">
      <el-col :span="6">
        <span class="label">收款人：</span>
        <span class="content">****公司</span>
      </el-col>
      <el-col :span="6">
        <span class="label">复核：</span>
        <span class="content"></span>
      </el-col>
      <el-col :span="6">
        <span class="label">开票人：</span>
        <span class="content"></span>
      </el-col>
      <el-col :span="6">
        <span class="label">销售方：（章）</span>
        <span class="content"></span>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { numberToChinese, NumFormat } from '@/utils/tools'

export default {
  name: 'InvoicePreview',
  props: {
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      NumFormat,
      numberToChinese
    }
  }
}
</script>

<style scoped lang="scss">
.invoice-container {
  padding: 65px 20px 86px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  position: relative;
  font-size: 14px;
  .code-wrapper {
    position: absolute;
    top: 65px;
    right: 67px;
  }
  .title {
    font-size: 28px;
    font-weight: 400;
    color: #ac6367;
    line-height: 28px;
    letter-spacing: 1px;
    text-align: center;
    padding-top: 23px;
  }
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #a68164;
    line-height: 14px;
  }
  .content {
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 14px;
  }
  .w-32 {
    width: 32px;
  }
  .w-70 {
    width: 70px;
  }
  .w-98 {
    width: 98px;
  }
  .text-justify {
    text-align: justify;
    text-align-last: justify;
    flex-shrink: 0;
  }
  .invoice-table {
    border: 1px solid #ac6367;
  }
  .vertical-text {
    padding: 0 8px;
    writing-mode: vertical-rl;
    text-align: center;
  }
  .border-right {
    border-right: 1px solid #ac6367;
  }
  .border-bottom {
    border-bottom: 1px solid #ac6367;
  }
  .info-wrapper {
    width: calc((100% - 64px) * 0.6);
  }
  .password-wrapper {
    width: calc((100% - 64px) * 0.4);
  }
  .tax-wrapper {
    padding: 10px 44px;
  }
  .upper-lower {
    .upper-label {
      width: 30%;
      padding: 32px 20px;
    }
    .upper-content {
      width: 35%;
    }
  }
  .comment-item {
    & + .comment-item {
      margin-top: 5px;
    }
  }
  .span-6 {
    width: calc(100% / 4);
  }
  .span-2 {
    width: calc(100% / 12);
  }
  .span-4 {
    width: calc(100% / 6);
  }
  .span-3 {
    width: calc(100% / 8);
  }
}
</style>
