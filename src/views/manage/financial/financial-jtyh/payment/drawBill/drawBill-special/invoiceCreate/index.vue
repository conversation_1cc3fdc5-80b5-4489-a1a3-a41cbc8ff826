<template>
  <div>
    <basic-card>
      <div class="flex justify-content-center step-container">
        <basic-steps :steps="stepsData" :current="current" />
      </div>
    </basic-card>
    <div class="invoice-wrapper m-t-8 bg-white">
      <div v-show="current === 1">
        <!-- 发票种类 -->
        <div>
          <div class="title">发票种类</div>
          <el-radio-group v-model="kind">
            <el-radio
              v-for="item in kindList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </div>
        <!-- 开票类型 -->
        <div class="m-t-24">
          <div class="title">开票类型</div>
          <div class="flex">
            <div
              v-for="item in openTypeList"
              :key="item.value"
              class="type-item pointer"
              :class="{ active: item.value === openType }"
              @click="openTypeChange(item.value)"
            >
              <div class="flex align-items-center justify-content-between">
                <span class="font-size-14 line-height-22">{{
                  item.label
                }}</span>
                <el-checkbox
                  :value="item.value === openType"
                  @change="openTypeChange(item.value)"
                ></el-checkbox>
              </div>
              <div class="font-size-12 color-text-secondary m-t-8">
                申请后生成电子发票，企业自行下载发票
              </div>
            </div>
          </div>
        </div>
        <!-- 发票信息 -->
        <div class="m-t-24 flex justify-content-between">
          <div class="info-item">
            <div class="title">购买方信息</div>
            <div class="info-item-wrapper">
              <invoice-info
                ref="purchaserInfo"
                :purchaser="true"
                :open-type="openType"
                @getInfo="getPurchaserInfo"
              />
            </div>
          </div>
          <div class="info-item">
            <div class="title">销售方信息</div>
            <div class="info-item-wrapper">
              <invoice-info
                ref="saleInfo"
                :purchaser="false"
                :open-type="openType"
                @getInfo="getSaleInfo"
              />
            </div>
          </div>
        </div>
        <!-- 关联账单 -->
        <div class="m-t-24">
          <relevance-bill
            ref="relevanceBill"
            :purchaser-info="purchaserNameInfo"
            @getBillData="getBillData"
          />
        </div>
        <!-- 开票信息 -->
        <div class="m-t-24">
          <bill-info
            ref="billInfo"
            :bill-data="billData"
            @getRemark="getRemark"
          />
        </div>
        <!-- 备注信息 -->
        <div class="m-t-24">
          <div class="title remark-title">备注信息</div>
          <el-input
            v-model="invRemark"
            placeholder="请填写备注信息"
            type="textarea"
            maxlength="100"
            show-word-limit
            :rows="4"
          ></el-input>
        </div>
        <!-- 发票接收地址 -->
        <div class="m-t-24">
          <div class="title">发票接收地址</div>
          <driven-form
            ref="invoiceAddress"
            :formConfigure="formConfigure"
            v-model="formModel"
          />
        </div>
        <!-- 非合同主体开票证明材料 -->
        <div class="m-t-24">
          <div class="title">非合同主体开票证明材料</div>
          <uploader
            v-model="material"
            :uploadData="uploadData"
            :max-length="3"
            :limit="3"
            :max-size="10"
            :mulity="true"
          />
        </div>
      </div>
      <invoice-preview
        v-show="current === 2"
        class="invoice-preview"
        :info="invoiceData"
      />
      <div
        v-show="current === 3"
        class="audit-tips-wrapper flex flex-direction-column align-items-center"
      >
        <svg-icon icon-class="check-circle" />
        <div class="font-size-14 line-height-22">您的开票申请正在审核中</div>
        <div
          class="font-size-14 line-height-22 m-t-8 color-text-secondary flex align-items-center"
        >
          可在<el-link type="primary" @click="goInvoiceHandle"
            >开票管理列表</el-link
          >查看本次提交的申请和开票进度
        </div>
        <el-button type="primary" class="m-t-24" @click="goBack"
          >返回</el-button
        >
      </div>
      <div
        v-if="current === 1 || current === 2"
        class="bottom-wrapper flex justify-content-end m-t-24 p-t-24"
      >
        <el-button v-if="current === 1" type="primary" @click="nextHandle"
          >下一步</el-button
        >
        <template v-if="current === 2">
          <el-button type="primary" @click="current = 1">上一步</el-button>
          <el-button type="primary" @click="submitHandle">确认</el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import BasicSteps from '@/components/BasicSteps'
import InvoiceInfo from './components/invoiceInfo'
import RelevanceBill from './components/relevanceBill'
import BillInfo from './components/billInfo'
import DescriptorMixin from './descriptor'
import InvoicePreview from '../components/InvoicePreview'
import {
  invApplyRecordCreate,
  invApplyRecordGet,
  invApplyRecordKind,
  invApplyRecordOpenType,
  invApplyRecordUpdate
} from '../api'

export default {
  name: 'InvoiceCreate',
  components: {
    InvoicePreview,
    BillInfo,
    RelevanceBill,
    InvoiceInfo,
    BasicSteps
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      current: 1,
      stepsData: [
        {
          title: '填写开票内容',
          desc: '正确填写开票内容',
          value: 1
        },
        {
          title: '确认发票信息',
          desc: '核对发票信息',
          value: 2
        },
        {
          title: '提交开票审核',
          desc: '审核通过后,等待系统开票完成',
          value: 3
        }
      ],
      // 发票种类
      kindList: [],
      kind: 1,
      // 开票类型
      openTypeList: [],
      openType: 1,
      invRemark: '',
      formModel: {},
      material: [],
      uploadData: {
        type: 'invoice'
      },
      purchaserNameInfo: {}, // 购买方选择名称下拉信息
      saleNameInfo: {}, // 销售方选择名称下拉信息
      billData: [], // 关联账单列表数据
      invoiceData: {}, // 整合的数据
      detailInfo: {} // 编辑详情信息
    }
  },
  provide() {
    return {
      InvoiceCreate: this
    }
  },
  watch: {
    invRemark(val) {
      if (val && val.length > 100) {
        this.invRemark = val.substring(0, 100)
      }
    }
  },
  mounted() {
    this.invApplyRecordKind()
    this.invApplyRecordOpenType()
    // 编辑
    if (this.$route.query.id) {
      this.invApplyRecordGet()
    }
  },
  methods: {
    // 由于购买方信息会更新关联账单，所以此处等待购买方信息回显完成后通知关联账单回显
    editInitComplete() {
      this.$refs.relevanceBill.initData(this.detailInfo)
    },
    invApplyRecordGet() {
      invApplyRecordGet({ id: this.$route.query.id }).then(res => {
        this.detailInfo = res || {}
        this.kind = res.kind
        this.openType = res.openType
        this.$nextTick(() => {
          this.$refs.purchaserInfo.initData(res)
          this.$refs.saleInfo.initData(res)
        })
        this.$set(this.formModel, 'receiveEmail', res.receiveEmail)
        this.$set(this.formModel, 'receivePhone', res.receivePhone)
        const attachMap = res.attachMap || {}
        this.material = attachMap.invoice || []
      })
    },
    invApplyRecordOpenType() {
      invApplyRecordOpenType().then(res => {
        this.openTypeList = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
        if (this.kind.length) this.openType = this.openTypeList[0].value
      })
    },
    // 获取发票种类
    invApplyRecordKind() {
      invApplyRecordKind().then(res => {
        this.kindList = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
        if (this.kind.length) this.kind = this.kindList[0].value
      })
    },
    submitHandle() {
      const params = {
        ...this.getData()
      }
      let url = invApplyRecordCreate
      if (this.$route.query.id) {
        url = invApplyRecordUpdate
        params.id = this.$route.query.id
      }
      url(params).then(() => {
        this.current = 3
      })
    },
    // 发票接收地址表单验证
    invoiceAddressValidate() {
      return new Promise((resolve, reject) => {
        this.$refs.invoiceAddress.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    // 下一步
    nextHandle() {
      if (!this.billData.length) return this.$toast.warning('请选择关联账单')
      Promise.all([
        this.$refs.purchaserInfo.validateHandle(),
        this.$refs.saleInfo.validateHandle(),
        this.$refs.billInfo.validateHandle(),
        this.invoiceAddressValidate()
      ]).then(() => {
        const invRemark = this.invRemark.trim()
        if (!invRemark) return this.$toast.warning('请填写备注信息')
        const billInfo = this.$refs.billInfo
        const tableDta = billInfo.formModel.tableData || []
        const freeTax = tableDta.filter(item => item.freeTax === true)
        if (
          this.openType === 2 &&
          freeTax.length &&
          freeTax.length === tableDta.length
        ) {
          return this.$toast.warning('当前账单全为免租税率，不可开具专票')
        }
        this.invoiceData = this.getData()
        this.current = 2
      })
    },
    // 获取请求数据
    getData() {
      const purchaserInfo = this.$refs.purchaserInfo.fromModel
      const billInfo = this.$refs.billInfo
      const tableDta = billInfo.formModel.tableData || []
      const billList = tableDta.map(item => {
        return {
          billId: item.billId,
          invModel: item.invModel,
          invUnit: item.invUnit,
          number: item.count,
          openAmount: item.openAmount,
          openCycle: item.feeCycle.toString(),
          price: item.price,
          tax: item.freeTax ? 0 : item.taxPercent,
          taxAmount: item.taxAmount,
          feeTypeStr: item.feeTypeStr,
          amount: item.paidAmount,
          freeTax: item.freeTax
        }
      })
      const params = {
        kind: this.kind,
        openType: this.openType,
        invHeader: this.purchaserNameInfo.type,
        bodyId: this.purchaserNameInfo.bodyId,
        bodyName: this.purchaserNameInfo.bodyName,
        buyerAddress: purchaserInfo.address,
        buyerBankName: purchaserInfo.bankName,
        buyerBankNo: purchaserInfo.bankNo,
        buyerCode: purchaserInfo.taxpayerNumber,
        buyerContact: purchaserInfo.contactNumber,
        invSaleConfigRespVO: this.saleNameInfo,
        ...this.formModel,
        attachIds: this.material.map(item => item.id),
        billList,
        invRemark: this.invRemark,
        sumTaxAmount: billInfo.openAmountTotal,
        sumTax: billInfo.taxAmountTotal,
        sumAmount: billInfo.paidAmountTotal
      }
      return params
    },
    // 生成默认备注信息
    getRemark(editInit) {
      // 编辑备注回显特殊处理，直接在返回值赋值会被开票信息覆盖
      if (editInit) return (this.invRemark = this.detailInfo.invRemark)
      const tableDta = this.$refs.billInfo?.formModel?.tableData
      if (!tableDta || !tableDta.length) return (this.invRemark = '')
      let text = ''
      if (this.purchaserNameInfo.address) {
        text = `地址：${this.purchaserNameInfo.address}\r`
      }
      tableDta.forEach(item => {
        text += `${item.feeTypeStr}：${item.openAmount}元，期间：${item.rcvAmtSdt} ~ ${item.rcvAmtEdt}\r`
      })
      this.invRemark = text
    },
    getBillData(list) {
      this.billData = list || []
    },
    // 销售方信息
    getSaleInfo(row) {
      this.saleNameInfo = row || {}
    },
    // 购买方信息
    getPurchaserInfo(row) {
      this.purchaserNameInfo = row || {}
      this.$refs.relevanceBill && this.$refs.relevanceBill.clearHandle()
    },
    openTypeChange(e) {
      this.openType = e
    },
    goInvoiceHandle() {
      this.$router.replace('/drawBill/drawBillList')
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.step-container {
  padding: 42px 0 32px 0;
}
.invoice-wrapper {
  padding: 24px;
  border-radius: 3px;
  .type-item {
    border-radius: 6px;
    border: 1px solid;
    @include border_color(--border-color-light);
    padding: 16px;
    & + .type-item {
      margin-left: 18px;
    }
    &.active {
      @include background_color_mix(--color-primary, #ffffff, 95%);
    }
  }
  .info-item {
    width: calc(50% - 18px);
    .info-item-wrapper {
      border-radius: 6px;
      border: 1px solid;
      @include border_color(--border-color-light);
      padding: 24px;
    }
  }
  .invoice-preview {
    width: 953px;
    margin: 0 auto;
  }
  .audit-tips-wrapper {
    padding: 80px 0;
    .svg-icon {
      font-size: 110px;
      @include font_color(--color-success);
    }
  }
}
.bottom-wrapper {
  border-top: 1px solid;
  @include border_color(--border-color-base);
}
.remark-title {
  padding-left: 12px;
  position: relative;
  &::before {
    display: inline-block;
    content: '*';
    color: #e34d59;
    position: absolute;
    left: 0;
  }
}
:deep(.title) {
  line-height: 22px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
  margin-bottom: 16px;
}
</style>
