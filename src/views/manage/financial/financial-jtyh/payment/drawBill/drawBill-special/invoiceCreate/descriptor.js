import { validateContact, validateEmail } from '@/utils/validate'

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          receiveEmail: {
            form: 'input',
            label: '邮箱',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入邮箱'
              },
              {
                validator: validateEmail
              }
            ],
            customTips: () => {
              return (
                <div>请确认邮箱无误，电子发票将在系统开具后发送至所填邮箱</div>
              )
            }
          },
          receivePhone: {
            form: 'input',
            label: '手机号',
            span: 12,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入手机号'
              },
              {
                validator: (rule, value, callback) => {
                  validateContact(rule, value, callback, false)
                }
              }
            ]
          }
        }
      }
    }
  }
}
