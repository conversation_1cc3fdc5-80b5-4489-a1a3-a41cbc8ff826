<template>
  <el-select
    v-model="_value"
    filterable
    remote
    placeholder="请输入名称"
    :remote-method="remoteMethod"
    @change="changeHandle"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="item.disabled"
    >
      <div v-html="item.labelHtml"></div>
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'RemoteSelect',
  props: {
    value: {
      required: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    changeHandle(e) {
      this.$emit('changeHandle', e)
    },
    remoteMethod(e) {
      this.$emit('remoteMethod', e)
    }
  }
}
</script>

<style scoped></style>
