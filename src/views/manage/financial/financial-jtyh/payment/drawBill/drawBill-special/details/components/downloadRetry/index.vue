<template>
  <dialog-cmp
    title=""
    :visible.sync="dialogVisible"
    :haveOperation="false"
    width="900px"
  >
    <iframe class="w100" height="600px" :src="info.pdfUrl" />
    <div slot="footer">
      <el-button v-if="info.kind === 1" type="primary" @click="sendMailHandle"
        >发送邮箱</el-button
      >
      <el-button type="primary" @click="downloadHandle">下载</el-button>
    </div>
  </dialog-cmp>
</template>

<script>
import downloads from '@/utils/download'
import { invApplyRecordResend } from '../../../api'

export default {
  name: 'DownloadRetry',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      show: false
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    downloadHandle() {
      const row = {
        name: +new Date() + ((Math.random() * 1000).toFixed(0) + '') + '.pdf',
        path: this.info.pdfUrl
      }
      downloads.addressDownload(row)
    },
    sendMailHandle() {
      this.$prompt('请输入邮箱地址', '发送邮箱', {
        confirmButtonText: '发送',
        cancelButtonText: '取消',
        inputPattern:
          /[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
        inputErrorMessage: '请输入正确的邮箱地址'
      }).then(({ value }) => {
        const params = {
          applyId: this.info.id,
          email: value
        }
        invApplyRecordResend(params).then(() => {
          this.$toast.success('发送成功')
        })
      })
    }
  }
}
</script>

<style scoped></style>
