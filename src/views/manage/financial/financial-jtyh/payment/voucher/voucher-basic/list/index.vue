<!-- 合同应收 -->
<template>
  <basic-card class="h100">
    <div>
      <div class="financial-content bg-white">
        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>交易笔数</span>
              </div>
              <div
                class="acconut-name m-t-17 font-size-24"
                style="color: #ed7b2f"
              >
                <span>{{ NumFormat(applyCountData.totalFee) | noData }}</span>
                <span class="font-size-18 font-strong-600 m-l-4"> 笔</span>
              </div>
              <div class="percentage flex">
                <span class="font-size-14 line-height-22">+10009.28</span>
                <span class="percent">+7.53%</span>
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>交易总金额</span>
              </div>
              <div
                class="acconutname m-t-17 font-size-24 flex align-items-center"
              >
                <span class="font-size-18">￥</span>
                <span>{{ applyCountData.totalApply | noData }}</span>
              </div>
              <div class="percentage flex">
                <span class="font-size-14 line-height-22">+10009.28</span>
                <span class="percent">+7.53%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--    <basic-tab-->
    <!--      ref="basicTab"-->
    <!--      :tabs-data="list"-->
    <!--      :current="current"-->
    <!--      @tabsChange="tabsChange"-->
    <!--    />-->
    <!--    表单-->
    <!--    <div class="m-b-6">-->
    <!--      <el-form ref="fromAccountInfo" :model="fromTableInfo">-->
    <!--        <el-row>-->
    <!--          <el-col :span="12">-->
    <!--            <el-form-item class="m-l-8">-->
    <!--              &lt;!&ndash;              按合同按企业&ndash;&gt;-->
    <!--              <div class="choose">-->
    <!--                <div class="move-bgc"></div>-->
    <!--                <div class="item-btn" @click="chageTab(1)">昨日</div>-->
    <!--                <div class="item-btn" @click="chageTab(2)">历史</div>-->
    <!--              </div>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :span="12">-->
    <!--            <div class="w100 flex justify-content-end">-->
    <!--              <el-form-item class="m-l-8">-->
    <!--                <el-select-->
    <!--                  style="width: 140px"-->
    <!--                  v-model="fromTableInfo.feeType"-->
    <!--                  clearable-->
    <!--                  placeholder="请选择对账结果"-->
    <!--                  :popper-append-to-body="false"-->
    <!--                >-->
    <!--                  <el-option-->
    <!--                    v-for="item in feeTypeList"-->
    <!--                    :key="item.value"-->
    <!--                    :label="item.label"-->
    <!--                    :value="item.value"-->
    <!--                  ></el-option>-->
    <!--                </el-select>-->
    <!--              </el-form-item>-->
    <!--              <el-form-item class="m-l-8">-->
    <!--                <el-select-->
    <!--                  style="width: 140px"-->
    <!--                  v-model="fromTableInfo.ticketType"-->
    <!--                  clearable-->
    <!--                  placeholder="请选择费用名称"-->
    <!--                  :popper-append-to-body="false"-->
    <!--                >-->
    <!--                  <el-option-->
    <!--                    v-for="item in ticketTypeList"-->
    <!--                    :key="item.value"-->
    <!--                    :label="item.label"-->
    <!--                    :value="item.value"-->
    <!--                  ></el-option>-->
    <!--                </el-select>-->
    <!--              </el-form-item>-->
    <!--            </div>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--      </el-form>-->
    <!--    </div>-->
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :height="tableHeight"
      :api-fn="ticketApplyPagebyents"
      :extralQuerys="fromTableInfo"
    />

    <basic-drawer
      :size="1000"
      title="查看缴费明细"
      :visible.sync="visible"
      :haveOperation="false"
    >
      <div class="content">
        <div class="font-strong">明珠产业园</div>
        <div class="flex justify-content-between m-t-20 font-size-14">
          <span>对账总笔数: 1</span>
          <span
            >对账总金额: <span>￥{{ NumFormat(147096.75) }}</span></span
          >
          <span>对账时间: <span>2023-04-04 02:30:00</span></span>
        </div>
        <div class="flex m-t-20 font-size-14">
          <span class="label">不平账笔数: 1</span>
          <span
            >不平账金额: <span>￥{{ NumFormat(147096.75) }}</span></span
          >
        </div>
        <div class="m-t-20">
          <drive-table ref="drive-tables" :columns="tableColumnAccount" />
        </div>
      </div>
      <template slot="footer">
        <el-button type="info" @click="visible = false">取消</el-button>
        <el-button type="primary" @click="visible = false">确定</el-button>
      </template>
    </basic-drawer>
  </basic-card>
</template>

<script>
// import BasicTab from './components/basicTab'
import { NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
// import downloads from '@/utils/download'
// import { getContractReceivables } from './api'
import {
  applyCount,
  getPark,
  ticketApplyPagebyent,
  ticketApplyPagebyents
} from './api/index'
export default {
  name: 'VoucherList',
  components: {
    // BasicTab
  },
  mixins: [ColumnMixins],
  data() {
    return {
      visible: false,
      tableHeight: 'calc(100vh - 370px)',
      NumFormat,
      ticketApplyPagebyent,
      ticketApplyPagebyents,
      // getContractReceivables,
      accountInfo: {}, // 账户信息
      dataObservationform: {
        day: ''
      },
      dayList: [],
      current: 0,
      list: [
        {
          label: '全部',
          value: 0
        }
      ],
      fromTableInfo: {
        day: '',
        status: null,
        name: ''
      },
      signList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '已签约',
          value: 1
        },
        {
          label: '未签约',
          value: 2
        }
      ],
      //签订时间
      contractStatusList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '已签约',
          value: 1
        },
        {
          label: '未签约',
          value: 2
        }
      ], //合同状态
      //  开票类型
      drawBillType: 1,
      // 统计数据
      applyCountData: {},
      dayStatus: [
        {
          label: '待提交',
          value: -1
        },
        {
          label: '待开票',
          value: 1
        },
        {
          label: '已退回',
          value: 2
        },
        {
          label: '已开票',
          value: 3
        }
      ],
      ticketTypeList: [
        {
          label: '普通电子发票',
          value: 1
        },
        {
          label: '专用发票',
          value: 2
        }
      ],
      feeTypeList: [
        {
          label: '租房费',
          value: 1
        },
        {
          label: '保证金',
          value: 2
        },
        {
          label: '服务费',
          value: 3
        }
      ],
      dateType: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '本周',
          value: 2
        },
        {
          label: '本月',
          value: 3
        },
        {
          label: '本季度',
          value: 4
        },
        {
          label: '本年',
          value: 5
        },
        {
          label: '近3年',
          value: 6
        },
        {
          label: '3年以前',
          value: 7
        }
      ]
    }
  },
  mounted() {
    this.chageTab(1)
    this.applyCount()
    this.getPark()
  },
  watch: {
    fromTableInfo: {
      handler(val) {
        console.log(val)
        if (this.drawBillType === 1) {
          this.$nextTick(() => {
            this.$refs['drive-table'].triggerSearch()
          })
        } else {
          this.$nextTick(() => {
            this.$refs['drive-tables'].triggerSearch()
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getPark() {
      getPark().then(res => {
        console.log(res)
        res.forEach(item => {
          this.list.push({
            label: item.park,
            value: item.id
          })
        })
      })
    },
    previewEvent(e) {
      console.log(e)
      this.visible = true
    },
    applyCount() {
      applyCount().then(res => {
        this.applyCountData = res
      })
    },
    //切换
    tabsChange(e) {
      this.current = e
      this.fromTableInfo.parkId = e
      if (this.drawBillType === 1) {
        this.$nextTick(() => {
          this.$refs['drive-table'].triggerSearch()
        })
      } else {
        this.$nextTick(() => {
          this.$refs['drive-tables'].triggerSearch()
        })
      }
    },
    chageTab(type) {
      this.drawBillType = type
      let moveBgc = document.querySelector('.move-bgc')
      let item = document.querySelectorAll('.item-btn')
      item.forEach((item, index) => {
        if (index === type - 1) {
          item.style.color = '#fff'
        } else {
          item.style.color = '#000'
        }
      })
      if (type === 1) {
        //  向X左方向移动
        moveBgc.style.transform = 'translateX(0%)'
      } else {
        moveBgc.style.transform = 'translateX(91%)'
      }
      if (type === 1) {
        this.$nextTick(() => {
          this.$refs['drive-table'].refreshResetTable()
        })
      } else {
        this.$nextTick(() => {
          this.$refs['drive-tables'].refreshResetTable()
        })
      }
    },
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    }
  }
  // computed:{
  //   tableColumnData() {
  //     if(this.drawBillType === 1) {
  //       return this.tableColumn
  //     } else {
  //       return this.tableColumnFirm
  //     }
  //   },
  //   tableColumnDataApi() {
  //     if(this.drawBillType == 1) {
  //       return this.ticketApplyPagebyent
  //     } else {
  //       return this.ticketApplyPagebyents
  //     }
  //   },
  // }
}
</script>

<style lang="scss" scoped>
.content {
  .label {
    width: 334px;
    font-size: 14px;
  }
}
.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}
.acconutname {
  font-weight: 600;
  //color: #000;
  @include font_color(--color-warning);
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: 57px;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}
</style>
