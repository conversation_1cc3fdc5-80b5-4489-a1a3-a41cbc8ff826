<!-- 合同应收 -->
<template>
  <div>
    <basic-card>
      <div>
        <el-row>
          <el-col :span="6">
            <div class="font-size-18 font-strong">企业线下交易记录对账</div>
          </el-col>
          <el-col :span="18">
            <div class="flex justify-content-end">
              <el-form ref="fromAccountInfo" :model="fromTableInfo">
                <div class="w100 flex justify-content-end">
                  <el-form-item class="m-l-8">
                    <el-select
                      style="width: 140px"
                      clearable
                      v-model="dataFormat.acStatus"
                      placeholder="请选择确认状态"
                      :popper-append-to-body="false"
                      @change="changeTime"
                    >
                      <el-option
                        v-for="item in checkStatusList"
                        :key="item.key"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item class="m-l-8">
                    <el-select
                      style="width: 140px"
                      clearable
                      v-model="dataFormat.payStatus"
                      placeholder="请选择到账状态"
                      :popper-append-to-body="false"
                      @change="changeTime"
                    >
                      <el-option
                        v-for="item in accountStatusList"
                        :key="item.key"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item class="m-l-8">
                    <el-date-picker
                      clearbale
                      v-model="selectTime"
                      type="daterange"
                      range-separator="-"
                      start-placeholder="请选择交易开始时间"
                      @change="changeTime"
                      end-placeholder="请选择交易结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
              </el-form>
              <el-popover placement="bottom" width="120" trigger="hover">
                <div v-if="registerUrl !== null">
                  <div style="width: 120px; height: 120px" class="m-b-10">
                    <el-image :src="registerUrl"></el-image>
                  </div>
                  <div
                    class="font-size-12 color-text-secondary text-align-center"
                  >
                    使用小程序仅需1分钟
                  </div>
                </div>
                <div v-else>
                  <empty-data description="暂无二维码"></empty-data>
                </div>
                <el-button slot="reference" type="primary" class="m-l-8"
                  >登记交易记录</el-button
                >
              </el-popover>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="font-size-14 m-b-16 m-t-19" v-show="!typeShowAllTable">
        今日记录
      </div>
      <drive-table
        v-show="!typeShowAllTable"
        ref="drive-table"
        height="204"
        :api-fn="getRecordDetailPage"
        :columns="tableColumnToday"
        :extralQuerys="{ dateType: 1 }"
      >
      </drive-table>
    </basic-card>
    <basic-card
      :is-title="false"
      class="m-b-10 m-t-10 p-t-24"
      v-show="!typeShowAllTable"
    >
      <div class="font-size-14 m-b-16">昨日记录</div>
      <drive-table
        ref="drive-table"
        height="204"
        :columns="tableColumnFirmYesterday"
        :api-fn="getRecordDetailPage"
        :extralQuerys="{ dateType: 2 }"
      >
      </drive-table>
    </basic-card>
    <basic-card :is-title="false" class="p-t-24" v-show="!typeShowAllTable">
      <div class="font-size-14 m-b-16">历史记录</div>
      <drive-table
        ref="drive-table"
        height="204"
        :api-fn="getRecordDetailPage"
        :extralQuerys="{ dateType: 3 }"
        :columns="tableColumnFirmHistory"
      >
      </drive-table>
    </basic-card>

    <!-- 查询时获取所有记录 -->
    <basic-card :is-title="false" class="p-t-24" v-show="typeShowAllTable">
      <drive-table
        ref="drive-table"
        height="704"
        :api-fn="getRecordDetailPage"
        :columns="tableColumnFirmHistory"
        :extralQuerys="fromTableInfo"
      >
      </drive-table>
    </basic-card>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
// import downloads from '@/utils/download'
import { getRecordDetailPage, getRegisterPng } from './api'
import { getTenant } from '@/utils/auth'

export default {
  name: 'contractReceivables',
  mixins: [ColumnMixins],
  data() {
    return {
      getRecordDetailPage,
      NumFormat,
      registerUrl: null,
      typeShowAllTable: false, //是否显示所有列表
      selectTime: '', //查询时间
      //请求数据
      dataFormat: {
        dateType: '', //类型 1今天 2昨天 3历史
        payStatus: '', //到账状态
        acStatus: '', //确认状态
        beginTraceTime: '',
        endTraceTime: ''
      },
      checkStatusList: [
        { label: '全部', value: '' },
        { label: '未确认', value: '0' },
        { label: '已确认', value: '1' }
      ],
      accountStatusList: [
        { label: '全部', value: '' },
        { label: '未到账', value: '0' },
        { label: '已到账', value: '1' }
      ],
      fromTableInfo: {
        day: '',
        status: null,
        name: ''
      }
    }
  },
  mounted() {
    // this.getRecordDetailAll()
  },
  created() {
    this.registerPng()
  },
  methods: {
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    },
    goTransactionDetails(row) {
      let { id, orderId } = row
      this.$router.push({
        path: 'transactionInfo/transactionInfoDetails',
        query: {
          id,
          orderId
        }
      })
    },
    async registerPng() {
      // 判断是否有租户信息
      const tenantId = getTenant()
      this.registerUrl = await getRegisterPng({ tenantId })
    },
    //时间更改 判断是否显示全部表格 有时间时候显示全部
    async changeTime() {
      if (
        !this.selectTime &&
        !this.dataFormat.acStatus &&
        !this.dataFormat.payStatus
      ) {
        this.typeShowAllTable = false
      } else {
        this.typeShowAllTable = true
        this.dataFormat.dateType = ''
        if (this.selectTime?.length > 0) {
          this.dataFormat.beginTraceTime = this.selectTime[0]
          this.dataFormat.endTraceTime = this.selectTime[1]
        }
        this.typeAll = true
        this.$refs['drive-table'].triggerSearch(this.dataFormat)
      }
    },
    //获取今日/昨日/历史数据 并赋值
    changeCheckStatus() {
      if (this.typeShowAllTable) {
        this.changeTime()
      } else {
        // this.getRecordDetailAll()
      }
    },
    getPayType(val) {
      switch (val) {
        case 1:
          return '转账支付'
        case 2:
          return '现金支付'
        case 3:
          return '承诺汇票'
        case 4:
          return '其他'
        default:
          return '未知'
      }
    },
    getCheckType(val) {
      switch (val) {
        case 0:
          return '未确认'
        case 1:
          return '已确认'
        default:
          return '未知'
      }
    },
    getTransferStatus(val) {
      switch (val) {
        case 0:
          return '未到账'
        case 1:
          return '已到账'
        default:
          return '未知'
      }
    },
    getEntryType(val) {
      switch (val) {
        case 0:
          return '企业登记'
        case 1:
          return '管理登记'
        default:
          return '未知'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.el-date-editor) {
  display: flex;
  justify-content: center;
}

::v-deep(.el-range-editor--small .el-range__close-icon) {
  opacity: 1;
}

:deep(.el-form-item--small.el-form-item) {
  margin-bottom: 0 !important;
}
</style>
