import { NumFormat, parseTime } from '@/utils/tools'
import { getStatus } from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '业务ID号',
          prop: 'traceNo'
        },
        {
          label: '创建时间',
          prop: 'txnTime',
          render: (h, scope) => {
            return (
              <div>{parseTime(scope.row.txnTime, '{y}-{m}-{d} {h}:{i}')}</div>
            )
          }
        },
        {
          label: '操作类型',
          prop: 'loanFlag',
          render: (h, scope) => {
            return <div>{scope.row.loanFlag === 0 ? '转入' : '转出'}</div>
          }
        },
        {
          label: '操作金额(元)',
          prop: 'txnAmt',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.txnAmt)}</div>
            )
          }
        },
        {
          label: '余额类型',
          prop: 'billType',
          render: (h, scope) => {
            return <div>{scope.row.billTypeStr}</div>
          }
        },
        {
          label: '付款方',
          prop: 'oppAccountName'
        },
        {
          label: '收款方',
          prop: 'rcvAccountName'
        },
        {
          label: '交易后余额(元)',
          prop: 'remain',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.remain)}</div>
            )
          }
        },
        {
          label: '业务来源',
          prop: 'businessSourceStr',
          render: (h, scope) => {
            return (
              <div onClick={() => this.goPage(scope.row)}>
                <el-link type="primary">{scope.row.businessSourceStr}</el-link>
              </div>
            )
          }
        },
        {
          label: '状态',
          prop: 'txnSts',
          width: 120,
          render: (h, scope) => {
            return <div>{getStatus(h, scope.row.txnSts)}</div>
          }
        }
      ],
      tableColumnIns: [
        {
          label: '机构名称',
          prop: 'label'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.insEvent(scope.row)
                  }}
                >
                  切换
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
