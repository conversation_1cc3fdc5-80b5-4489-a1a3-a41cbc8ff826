<!-- 钱包管理 -->
<template>
  <basic-card>
    <div>
      <!--    表单-->
      <div class="m-b-6">
        <el-form ref="fromAccountInfo" :model="fromTableInfo">
          <el-row>
            <el-col :span="16">
              <el-form-item>
                <el-input
                  style="width: 288px"
                  clearable
                  placeholder="请输入企业名称"
                  @input="handleInputChange"
                  v-model="fromTableInfo.entName"
                >
                  <i slot="prefix" class="el-input__icon el-icon-search"></i>
                </el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <div class="flex justify-content-end">
                <el-form-item>
                  <el-select
                    style="width: 140px"
                    v-model="fromTableInfo.ticketStatus"
                    clearable
                    placeholder="请选择账户状态"
                    @change="handleSelectChange"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in accountStatus"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item class="m-l-8">
                  <el-button
                    v-permission="routeButtonsPermission.LEADING_OUT"
                    size="mini"
                    style="height: 32px"
                    type="primary"
                    @click="exportExcel"
                  >
                    <svg-icon icon-class="cloud-upload" />
                    <span
                      >{{ routeButtonsTitle.LEADING_OUT }}台账</span
                    ></el-button
                  >
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
    />

    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="getPurseList"
      :extral-querys="extralQuerys"
    />
  </basic-card>
</template>

<script>
import BasicTab from './components/basicTab'
import { formatGetParams, NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import downloads from '@/utils/download'
import { getBody, getPurseList, getStates, getExportExcel } from './api'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
// import xlsx from 'xlsx'
export default {
  name: 'WalletList',
  components: {
    BasicTab
  },
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      getPurseList,
      dayList: [],
      current: 0,
      list: [],
      extralQuerys: {
        entName: '',
        parkId: 0,
        state: null
      },
      fromTableInfo: {
        entName: '',
        ticketStatus: ''
      },
      //  开票类型
      drawBillType: 1,
      // 统计数据
      applyCountData: {},
      accountStatus: []
    }
  },
  created() {
    this.getBody()
    this.getStates()
  },
  computed: {
    ...mapGetters(['wholeRoutes'])
  },
  // mounted() {
  //   let index = 1
  //
  //   const obj = []
  //   console.log('dataAA---', this.wholeRoutes)
  //
  //   function _genObj(route) {
  //     const tempObj = {
  //       id: route.id,
  //       name: route.meta ? route.meta.title : '未知',
  //       path: route.path,
  //       permission: route.meta
  //         ? Array.isArray(route.meta.role)
  //           ? route.meta.role[0]
  //           : route.meta.role
  //         : '未知',
  //       type: route.type
  //     }
  //     if (route.parent_id) {
  //       tempObj.parent_id = route.parent_id
  //     }
  //     obj.push(tempObj)
  //   }
  //
  //   function _genBtns(btns, parent_id) {
  //     for (const k in btns) {
  //       _genObj({
  //         id: index,
  //         meta: { title: btns[k]?.title, role: btns[k]?.permission },
  //         parent_id,
  //         type: 3
  //       })
  //       ++index
  //     }
  //   }
  //
  //   function _genTree(routes, parent_id, parent_path) {
  //     routes.forEach(route => {
  //       if (route.meta && (route.meta.allow || route.meta.role === '*:*:*')) {
  //         return
  //       }
  //
  //       route.id = index
  //       route.type = 2
  //       if (parent_id) {
  //         route.parent_id = parent_id
  //       }
  //
  //       if (parent_path) {
  //         if (route.path.substring(0, 1) === '/') {
  //           route.path = route.path.substring(1)
  //         }
  //         route.path = `${parent_path}/${route.path}`
  //       }
  //
  //       ++index
  //
  //       _genObj(route)
  //
  //       if (route.meta && route.meta.btns) {
  //         _genBtns(route.meta.btns, route.id)
  //       }
  //
  //       if (route.children) {
  //         _genTree(route.children, route.id, route.path)
  //       }
  //     })
  //
  //     return routes[1]
  //   }
  //   _genTree(this.wholeRoutes)
  //   console.log('dataBB---', obj)
  //   const ws = xlsx.utils.json_to_sheet(obj)
  //   const wb = xlsx.utils.book_new()
  //   xlsx.utils.book_append_sheet(wb, ws, 'Sheet1')
  //   xlsx.writeFile(wb, '菜单.xlsx')
  // },
  methods: {
    //导出excel
    exportExcel() {
      let url = getExportExcel() + '?'
      url += formatGetParams(this.$refs['drive-table'].querys)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '台账明细.xls')
    },
    handleInputChange(e) {
      this.extralQuerys.entName = e
      this.$refs['drive-table'].triggerSearch()
    },
    handleSelectChange(e) {
      this.extralQuerys.state = e
      this.$refs['drive-table'].triggerSearch()
    },
    getStates() {
      getStates().then(res => {
        res.forEach(item => {
          this.accountStatus.push({
            label: item.label,
            value: item.key
          })
        })
      })
    },
    getBody() {
      getBody().then(res => {
        res.forEach(item => {
          this.list.push({
            label: item.label,
            value: item.key
          })
        })
        this.current = res[0].key
        this.extralQuerys.parkId = res[0].key
        this.$refs['drive-table'].triggerSearch()
      })
    },
    previewEvent(e) {
      this.$router.push({
        path: 'walletList/walletDetails?id=' + e.id
      })
    },
    //切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.parkId = e
      this.$refs['drive-table'].triggerSearch()
    },
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.el-form .el-form-item) {
  margin-bottom: 10px;
}

.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}

.acconutname {
  font-weight: 600;
  color: #000;
}

.acconut-name {
  font-weight: 600;
  color: #000;
}

.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}

.tx {
  width: 80px;
  height: 80px;
}

.xx {
  position: relative;
  width: 100%;
  height: 100px;

  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}

.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}

.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;

  .percent {
    width: 57px;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }

  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;

  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }

  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}

.qe {
  width: 100%;
  height: 32px;
}
</style>
