<!-- 合同应收 -->
<template>
  <basic-card>
    <div>
      <!-- top-->
      <div class="financial-content bg-white p-24">
        <div class="flex justify-content-between m-b-32 font-size-14">
          <div class="flex1">
            <div class="flex justify-content-end font-14 time">
              <div class="color-text-secondary">
                下级账户个数：
                <span class="color-primary">
                  {{ accountInfo.accountCount || 0 }}
                </span>
              </div>
              <div v-if="depth === 1" class="color-text-secondary">
                下级账户个数：
                <span class="color-primary">
                  {{ accountInfo.accountCount || 0 }}
                </span>
              </div>
              <div v-if="depth === 2" class="color-text-secondary">
                下级账户个数：
                <span class="color-primary">
                  {{ accountInfo.accountCount || 0 }}
                </span>
              </div>
            </div>
          </div>
          <div class="flex justify-content-end">
            <div class="color-text-secondary pointer" @click="refreshInfo">
              查询时间： {{ accountInfo.flushTime | noData }}
            </div>
            <i class="el-icon-refresh m-l-4 pointer tb" />
          </div>
        </div>

        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="tx inline-block"></div>
            <div class="zh">
              <div class="font-size-16 m-t-10">
                <svg-icon
                  icon-class="user-circle"
                  class="font-size-24 color-primary"
                ></svg-icon>
                账号
              </div>
              <div class="acconutname m-t-8 font-size-24">
                {{ accountInfo.cusAc | noData }}
                <!-- 310066661013004281158 -->
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="tx inline-block"></div>
            <div class="zh">
              <div class="font-size-16 m-t-10">
                <svg-icon
                  icon-class="account-name"
                  class="font-size-24 color-success"
                ></svg-icon>
                户名
              </div>
              <div class="acconut-name m-t-8 font-size-24">
                {{ accountInfo.cusAcName | noData }}
                <!-- 上海分行营业部测试五三 -->
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="tx inline-block"></div>
            <div class="zh">
              <div class="font-size-16 m-t-10">
                <svg-icon
                  icon-class="user-purse"
                  class="font-size-24 color-warning"
                ></svg-icon>
                实时余额（元）
              </div>
              <div class="payname m-t-8 font-size-24">
                ￥{{ NumFormat(accountInfo.acBal) }}
                <!-- ￥145,215.55 -->
              </div>
            </div>
          </div>
          <div class="xx">
            <div class="tx inline-block"></div>
            <div class="zh">
              <div class="font-size-16 m-t-10">
                <svg-icon
                  icon-class="user-wallet"
                  class="font-size-24 color-warning"
                ></svg-icon>
                可用余额（元）
              </div>
              <div class="payname m-t-8 font-size-24">
                ￥{{ NumFormat(accountInfo.avaBal) }}
                <!-- ￥145,215.55 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="getContractReceivables"
      :search-querys-hook="searchQueryHook"
    >
      <template v-slot:operate-right>
        <el-button type="info" size="mini" @click="exportReplace">
          <svg-icon icon-class="arrow-down" />
          <span>导出</span>
        </el-button>
      </template>
    </drive-table>
  </basic-card>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import { exportReplace } from '@/views/manage/house/replace/replace-basic/api/list'
import downloads from '@/utils/download'
import { getContractReceivables } from './api'
export default {
  name: 'contractReceivables',
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      getContractReceivables,
      accountInfo: {} // 账户信息
    }
  },
  methods: {
    goContractDetails(row) {
      let { id, orderId } = row
      this.$router.push({
        path: '/process/contractDetails',
        query: {
          id,
          orderId
        }
      })
    },

    exportReplace() {
      exportReplace(this.$refs['drive-table'].getRequestQuerys()).then(res => {
        if (res) {
          downloads.requestDownload(res, 'excel')
        }
      })
    },

    // 重置搜索参数
    searchQueryHook(e) {
      let [startTime = '', endTime = ''] = e.signTime || []
      if (e.signTime && e.signTime.length > 1) {
        e.startTime = startTime
        e.endTime = endTime
      }

      delete e.signTime
      return {
        ...e
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.financial-content {
  height: 210px;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  border: 1px solid #dcdcdc;
}
.acconutname {
  font-weight: 500;
  @include font_color(--color-primary);
}
.acconut-name {
  font-weight: 500;
  @include font_color(--color-success);
}
.payname {
  font-weight: 500;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
  margin-left: 44px;
  border-radius: 50%;
  background: linear-gradient(207deg, #f0f2f5 0%, #fff 100%);
  opacity: 1;
}
.xx {
  position: relative;
  width: 400px;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}
</style>
