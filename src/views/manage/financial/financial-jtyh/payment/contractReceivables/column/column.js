import { NumFormat } from '@/utils/tools'
import {
  // contractStatusOptions,
  getContractStatus,
  getContractType
} from '@/views/manage/house/contract/contract-basic/utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '合同编号',
          prop: 'contractNum',
          search: {
            type: 'input'
          }
          // render: (h, scope) => {
          //   return (
          //     <el-link
          //       type="primary"
          //       onClick={() => {
          //         this.goContractDetails(scope.row)
          //       }}
          //     >
          //       {scope.row.contractNo}
          //     </el-link>
          //   )
          // }
        },
        {
          label: '企业名称',
          prop: 'entName',
          search: {
            type: 'input'
          }
          // render: (h, scope) => {
          //   return (
          //     <el-link
          //       type="primary"
          //       onClick={() => {
          //         this.goContractDetails(scope.row)
          //       }}
          //     >
          //       {scope.row.contractNo}
          //     </el-link>
          //   )
          // }
        },

        {
          label: '签约日期',
          prop: 'signTime'
        },
        {
          label: '起始日期',
          prop: 'startTime'
        },
        {
          label: '结束日期',
          prop: 'endTime'
        },
        {
          label: '合同金额(元)',
          prop: 'contractMoney',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.contractMoney)}
              </div>
            )
          }
        },
        {
          label: '应收总金额(元)',
          prop: 'billMoney',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.billMoney)}
              </div>
            )
          }
        },
        {
          label: '已收总金额(元)',
          prop: 'billPayMoney',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.billPayMoney)}
              </div>
            )
          }
        },
        // {
        //   label: '已退总金额(元)',
        //   prop: 'returnMoney',
        //   render: (h, scope) => {
        //     return (
        //       <div class={'color-warning'}>
        //         {NumFormat(scope.row.returnMoney)}
        //       </div>
        //     )
        //   }
        // },
        {
          label: '应收进度',
          prop: 'returnMoney',
          width: 200,
          render: (h, scope) => {
            return <div>{getContractType(h, scope.row.returnMoney)}</div>
          }
        },
        {
          label: '合同状态',
          prop: 'contractStatus',
          width: 200,
          render: (h, scope) => {
            return <div>{getContractStatus(h, scope.row.contractStatus)}</div>
          }
        }
      ]
    }
  }
}
