import request from '@/utils/request'

// 获取交费计划列表
export function findPayPlanList(params) {
  return request({
    url: '/payPlan/findPayPlanList',
    method: 'get',
    isTable: true,
    type: 'finance',
    params
  })
}

// 保存导出应收账单列表查询条件
export function saveExportPayPlanExcel(data) {
  return request({
    url: '/payPlan/saveExportPayPlanExcel',
    method: 'post',
    data
  })
}

// 根据planId获取交费计划详情
export function getPayPlanInfoById(id) {
  return request({
    url: `/payPlan/getPayPlanInfoById/${id}`,
    method: 'get'
  })
}
