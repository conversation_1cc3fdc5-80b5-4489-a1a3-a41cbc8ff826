import request from '@/utils/request'

// 获得财务合同缴费计划分页
export function getPayPlan(params) {
  return request({
    url: `/contract/fee-plan/pay_plan`,
    method: 'get',
    isTable: true,
    params
  })
}

//根据企业获得财务合同缴费计划分页
export function getPayPlanEnt(params) {
  return request({
    url: `/contract/fee-plan/pay_plan_ent`,
    method: 'get',
    isTable: true,
    params
  })
}

// 获取所有园区
export function getPark() {
  return request({
    url: `/housing/park/listAll`,
    method: 'get'
  })
}

// 统计数据
export function getStatisticalData(params) {
  return request({
    url: `/contract/fee-plan/statistical_data`,
    method: 'get',
    params
  })
}
//按个人查找
export function getContractPayPlanSingle(params) {
  return request({
    url: `/contract/fee-plan/pay_plan_single`,
    method: 'get',
    isTable: true,
    type: 'admin-api',
    params
  })
}

//合同应收-按合同导出
export function contractListExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/contract/fee-plan/pay_plan_export`
}

//合同应收-按企业导出
export function contractEntListExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/contract/fee-plan/pay_plan_ent_export`
}

//合同应收-按个人导出
export function contractIndListExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/contract/fee-plan/pay_plan_single_export`
}
