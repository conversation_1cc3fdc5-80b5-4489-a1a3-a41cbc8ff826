import { NumFormat } from '@/utils/tools'
// import {
//   // ticketTypeStatus,
//   statusType,
//   feeTypeTypeStatus
// } from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '园区名称',
          prop: 'parkName'
        },
        {
          label: '账户类型',
          prop: 'accTypeStr'
        },
        {
          label: '账号',
          width: 300,
          prop: 'cusAc'
        },
        {
          label: '费用期间',
          prop: 'feeCycle'
        },
        {
          label: '归集时间',
          prop: 'collectTime'
        },
        {
          label: '归集金额(元)',
          prop: 'collectAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.collectAmount)}
              </div>
            )
          }
        },
        {
          label: '操作类型',
          prop: 'collectType',
          render: (h, scope) => {
            return (
              <div>{scope.row.collectType === 1 ? '系统归集' : '人工归集'}</div>
            )
          }
        },
        {
          label: '归集结果',
          prop: 'collectSts',
          width: 120,
          render: (h, scope) => {
            return (
              <div>{scope.row.collectSts === 1 ? '归集成功' : '归集失败'}</div>
            )
          }
        }
      ]
    }
  }
}
