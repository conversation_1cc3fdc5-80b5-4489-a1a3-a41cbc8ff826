import { NumFormat } from '@/utils/tools'
// import {
//   // contractTypeOptions,
//   // contractStatusOptions,
//   //  getContractType
//   // getContractStatus,
// } from '@/views/manage/process/contract/contract-basic/utils/status'
// import {NumFormat} from "@/utils/tools";

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'feeType',
          label: '费用名称',
          minWidth: '20%'
        },
        {
          prop: 'ticketFee',
          label: '金额',
          minWidth: '20%',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.ticketFee)}</div>
            )
          }
        },
        {
          prop: 'taxRate',
          label: '税率',
          minWidth: '20%'
        },
        {
          prop: 'ticketTax',
          label: '税额',
          minWidth: '20%',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.ticketTax)}</div>
            )
          }
        }
      ]
    }
  }
}
