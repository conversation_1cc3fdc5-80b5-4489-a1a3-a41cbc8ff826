<!-- 付款申请 -->
<template>
  <basic-card>
    <div>
      <!--      <div class="flex justify-content-between m-b-15">-->
      <!--        <div class="flex align-items-center">-->
      <!--          <span class="m-r-10">同比观察</span>-->

      <!--          <el-select-->
      <!--            style="width: 140px"-->
      <!--            v-model="compare"-->
      <!--            placeholder="请选择"-->
      <!--            @change="compareSelectChange"-->
      <!--            :popper-append-to-body="false"-->
      <!--          >-->
      <!--            <el-option-->
      <!--              v-for="item in compareList"-->
      <!--              :key="item.value"-->
      <!--              :label="item.label"-->
      <!--              :value="item.value"-->
      <!--            ></el-option>-->
      <!--          </el-select>-->
      <!--        </div>-->
      <!--        &lt;!&ndash;            <div>&ndash;&gt;-->
      <!--        &lt;!&ndash;                <el-button&ndash;&gt;-->
      <!--        &lt;!&ndash;                        size="mini"&ndash;&gt;-->
      <!--        &lt;!&ndash;                        type="primary"&ndash;&gt;-->
      <!--        &lt;!&ndash;                >&ndash;&gt;-->
      <!--        &lt;!&ndash;                    <svg-icon icon-class="cloud-upload" />&ndash;&gt;-->
      <!--        &lt;!&ndash;                    <span&ndash;&gt;-->
      <!--        &lt;!&ndash;                    >导出台账</span&ndash;&gt;-->
      <!--        &lt;!&ndash;                    ></el-button&ndash;&gt;-->
      <!--        &lt;!&ndash;                >&ndash;&gt;-->
      <!--        &lt;!&ndash;            </div>&ndash;&gt;-->
      <!--      </div>-->
      <div class="financial-content bg-white">
        <div class="flex account-left bg-white justify-content-around">
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>申请总数</span>
              </div>
              <div
                class="acconutname m-t-17 font-size-24 flex align-items-center"
              >
                <span>{{ accountInfo.applyNum | noData }}</span>
                <span class="font-size-18 font-strong-600 m-l-4"> 次</span>
              </div>
              <!--              <div class="percentage flex">-->
              <!--                <span-->
              <!--                  class="font-size-14 line-height-22"-->
              <!--                  :class="{ reduce: accountInfo.applyNumItem < 0 }"-->
              <!--                  >{{ dataHander(accountInfo.applyNumItem) }}</span-->
              <!--                >-->
              <!--                <span-->
              <!--                  class="percent"-->
              <!--                  :class="{-->
              <!--                    'percent-reduce': accountInfo.applyNumItem < 0-->
              <!--                  }"-->
              <!--                  >{{ dataHander(accountInfo.applyNumRatio) }}%</span-->
              <!--                >-->
              <!--              </div>-->
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center">
                <span>付款申请金额</span>
              </div>
              <div
                class="acconut-name m-t-17 font-size-24"
                style="color: #ed7b2f"
              >
                <span class="font-size-18">￥</span>
                <span>{{
                  NumFormat(accountInfo.withdrawalMoney) | noData
                }}</span>
              </div>
              <!--              <div class="percentage flex">-->
              <!--                <span-->
              <!--                  class="font-size-14 line-height-22"-->
              <!--                  :class="{ reduce: accountInfo.withdrawalMoneyItem < 0 }"-->
              <!--                  >{{ dataHander(accountInfo.withdrawalMoneyItem) }}</span-->
              <!--                >-->
              <!--                <span-->
              <!--                  class="percent"-->
              <!--                  :class="{-->
              <!--                    'percent-reduce': accountInfo.withdrawalMoneyItem < 0-->
              <!--                  }"-->
              <!--                  >{{ dataHander(accountInfo.withdrawalMoneyRatio) }}%</span-->
              <!--                >-->
              <!--              </div>-->
            </div>
          </div>
          <div class="xx">
            <div class="zh">
              <div class="font-size-16 m-t-10 flex align-items-center pointer">
                <span>剩余可付款金额</span>
              </div>
              <div class="payname m-t-17 font-size-24" style="color: #ed7b2f">
                <span class="font-size-18">￥</span>
                <span>{{
                  NumFormat(accountInfo.remainingMoney) | noData
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <basic-tab
      ref="basicTab"
      :tabs-data="list"
      :current="current"
      @tabsChange="tabsChange"
    />
    <!--    表单-->
    <div class="m-b-6">
      <el-form ref="fromAccountInfo" :model="fromTableInfo">
        <div class="flex justify-content-between">
          <div>
            <el-form-item>
              <el-input
                style="width: 288px"
                clearable
                @input="handleInput"
                placeholder="请输入企业名称"
                v-model="fromTableInfo.entName"
              >
                <i slot="prefix" class="el-input__icon el-icon-search"></i>
              </el-input>
            </el-form-item>
          </div>
          <div>
            <el-button
              v-permission="routeButtonsPermission.LEADING_OUT"
              size="mini"
              style="height: 32px"
              type="primary"
              @click="exportExcel"
            >
              <svg-icon icon-class="cloud-upload" />
              <span>{{ routeButtonsTitle.LEADING_OUT }}台账</span></el-button
            >
          </div>
        </div>
      </el-form>
    </div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :api-fn="getList"
      :extral-querys="extralQuerys"
    />
  </basic-card>
</template>

<script>
import BasicTab from './components/basicTab'
import { formatGetParams, NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import { getTop, getPark, getList, getExportExcel } from './api/index'
import downloads from '@/utils/download'
import dayjs from 'dayjs'
export default {
  name: 'DrawMoneyList',
  components: {
    BasicTab
  },
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      getList,
      accountInfo: {}, // 账户信息
      dataObservationform: {
        day: ''
      },
      dayList: [],
      current: 0,
      list: [],
      extralQuerys: {
        parkId: 0,
        type: 0,
        entName: ''
      },
      fromTableInfo: {
        entName: ''
      },
      signList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '已签约',
          value: 1
        },
        {
          label: '未签约',
          value: 2
        }
      ],
      //签订时间
      contractStatusList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '已签约',
          value: 1
        },
        {
          label: '未签约',
          value: 2
        }
      ], //合同状态
      //  开票类型
      drawBillType: 1,
      // 统计数据
      applyCountData: {},
      dayStatus: [
        {
          label: '待提交',
          value: -1
        },
        {
          label: '待开票',
          value: 1
        },
        {
          label: '已退回',
          value: 2
        },
        {
          label: '已开票',
          value: 3
        }
      ],
      ticketTypeList: [
        {
          label: '普通电子发票',
          value: 1
        },
        {
          label: '专用发票',
          value: 2
        }
      ],
      feeTypeList: [
        {
          label: '租房费',
          value: 1
        },
        {
          label: '保证金',
          value: 2
        },
        {
          label: '服务费',
          value: 3
        }
      ],
      dateType: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '本周',
          value: 2
        },
        {
          label: '本月',
          value: 3
        },
        {
          label: '本季度',
          value: 4
        },
        {
          label: '本年',
          value: 5
        },
        {
          label: '近3年',
          value: 6
        },
        {
          label: '3年以前',
          value: 7
        }
      ],
      compare: 1,
      compareList: [
        {
          label: '日',
          value: 1
        },
        {
          label: '周',
          value: 2
        },
        {
          label: '月',
          value: 3
        },
        {
          label: '季度',
          value: 4
        },
        {
          label: '年',
          value: 5
        }
      ]
    }
  },
  created() {
    this.getPark()
    this.getTop()
  },
  methods: {
    // 处理头部数据
    dataHander(data) {
      if (!data) return 0
      if (data < 0) {
        return `${data}`
      } else {
        return `+${data}`
      }
    },
    compareSelectChange(e) {
      this.compare = e
      this.getTop()
    },
    //导出excel
    exportExcel() {
      let url = getExportExcel() + '?'
      url += formatGetParams(this.$refs['drive-table'].querys)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '台账明细.xls')
    },
    handleInput() {
      this.extralQuerys.entName = this.fromTableInfo.entName
      this.$refs['drive-table'].triggerSearch()
    },
    getTop() {
      getTop({ topType: this.compare, type: 0 }).then(res => {
        if (res) {
          this.accountInfo = {
            ...res
          }
          this.accountInfo.progress = this.accountInfo.progress.toFixed(2)
        }
      })
    },
    getPark() {
      getPark().then(res => {
        res.forEach(item => {
          this.list.push({
            label: item.label,
            value: item.key
          })
        })
        this.current = res[0].key
        this.extralQuerys.parkId = res[0].key
        this.$refs['drive-table'].triggerSearch()
      })
    },
    previewEvent(e) {
      this.$router.push({
        path: 'drawMoneyList/drawMoneyDetails',
        query: {
          id: e.id,
          orderId: e.orderId
        }
      })
    },
    //切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.parkId = e
      this.$refs['drive-table'].triggerSearch()
    },
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    }
  }
  // computed:{
  //   tableColumnData() {
  //     if(this.drawBillType === 1) {
  //       return this.tableColumn
  //     } else {
  //       return this.tableColumnFirm
  //     }
  //   },
  //   tableColumnDataApi() {
  //     if(this.drawBillType == 1) {
  //       return this.ticketApplyPagebyent
  //     } else {
  //       return this.ticketApplyPagebyents
  //     }
  //   },
  // }
}
</script>

<style lang="scss" scoped>
.financial-content {
  border-radius: 3px 3px 3px 3px;
  padding-bottom: 40px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: auto;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}
</style>
