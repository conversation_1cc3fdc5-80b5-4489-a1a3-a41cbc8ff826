<template>
  <div class="w100 tabs-wrapper">
    <div class="tabs">
      <div
        v-for="(item, index) in tabsData"
        :key="index"
        class="font-size-14 color-text-regular p-l-16 p-r-16 tabs-item"
        :class="item.value === current ? 'active' : ''"
        @click="tabsChange(item.value)"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicTab',
  props: {
    tabsData: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  mounted() {
    console.log(this.current)
  },
  methods: {
    tabsChange(e) {
      this.$emit('tabsChange', e)
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  margin-bottom: 24px;
  .tabs {
    display: flex;
    height: 45px;
    //过渡
    transition: all 0.3s;
    overflow-x: hidden;
    overflow-y: hidden;
    white-space: nowrap;
  }
  .tabs:hover {
    transition: all 0.3s;
    overflow-x: auto;
  }

  .tabs-item {
    height: 45px;
    line-height: 45px;
    position: relative;
    cursor: pointer;

    &.active {
      @include font_color(--color-primary);
      &::before {
        content: '';
        width: 100%;
        height: 4px;
        @include background-color(--color-primary);
        position: absolute;
        bottom: -2px;
        left: 0;
      }
    }
  }
}
</style>
