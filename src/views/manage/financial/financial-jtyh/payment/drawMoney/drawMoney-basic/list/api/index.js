import request from '@/utils/request'

export function getTop(params) {
  return request({
    url: `/bill/withdrawal/admin/top`,
    method: 'get',
    params
  })
}

// 获取所有园区
export function getPark() {
  return request({
    url: `/bill/withdrawal/admin/parks`,
    method: 'get'
  })
}

// 列表
export function getList(params) {
  return request({
    url: `/bill/withdrawal/admin/page`,
    method: 'get',
    params
  })
}
//导出项目信息 Excel
export function getExportExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/bill/withdrawal/admin/export`
}
