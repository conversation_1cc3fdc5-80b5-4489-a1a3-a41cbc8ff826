import request from '@/utils/request'

// 保证金申请分页
export function depositAccountApplyPage(params) {
  return request({
    url: `/deposit/account-apply/page`,
    method: 'get',
    params
  })
}
// 保证金状态选择
export function depositAccountApplyStatus(params) {
  return request({
    url: `/deposit/account-apply/status_select`,
    method: 'get',
    params
  })
}
// 保证金部门选择
export function depositAccountApplyDeptSelect(params) {
  return request({
    url: `/deposit/account-apply/dept_select`,
    method: 'get',
    params
  })
}
// 保证金企业列表
export function depositAccountApplyEntList(params) {
  return request({
    url: `/deposit/account-apply/ent_list`,
    method: 'get',
    params
  })
}
// 保证金申请
export function depositAccountApplyCreate(data) {
  return request({
    url: `/deposit/account-apply/create`,
    method: 'post',
    data
  })
}
// 保证金详情
export function depositAccountApplyDetail(id) {
  return request({
    url: `/deposit/account-apply/detail/${id}`,
    method: 'get'
  })
}
