<template>
  <basic-card>
    <div class="p-b-40 bg-white">
      <div class="flex account-left bg-white justify-content-around">
        <div class="xx">
          <div class="zh">
            <div class="font-size-16 m-t-10 flex align-items-center">
              <span>企业名称</span>
            </div>
            <div
              class="font-strong m-t-17 font-size-24 flex align-items-center"
            >
              <span class="line-1">{{ accountInfo.entName | noData }}</span>
            </div>
          </div>
        </div>
        <!--        <div class="xx">-->
        <!--          <div class="zh">-->
        <!--            <div class="font-size-16 m-t-10 flex align-items-center">-->
        <!--              <span>账号</span>-->
        <!--            </div>-->
        <!--            <div class="font-strong m-t-17 font-size-24">-->
        <!--              <span>{{ accountInfo.accountNo | noData }}</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
        <div class="xx">
          <div class="zh">
            <div class="font-size-16 m-t-10 flex align-items-center pointer">
              <span>通用金额</span>
              <el-tooltip
                class="item"
                effect="dark"
                content="可用于支付任意类型账单的余额"
                placement="top"
              >
                <svg-icon icon-class="prompt" class="m-l-4 pointer" />
              </el-tooltip>
            </div>
            <div class="m-t-17 font-size-24 color-primary font-strong">
              <span class="font-size-18">￥</span>
              <span>{{ NumFormat(accountInfo.purse) | noData }}</span>
            </div>
          </div>
        </div>
        <!--        <div class="xx">-->
        <!--          <div class="zh">-->
        <!--            <div class="font-size-16 m-t-10 flex align-items-center pointer">-->
        <!--              <span>保证金金额</span>-->
        <!--              <el-tooltip-->
        <!--                class="item"-->
        <!--                effect="dark"-->
        <!--                content="仅支持支付指定类型账单的余额"-->
        <!--                placement="top"-->
        <!--              >-->
        <!--                <svg-icon icon-class="prompt" class="m-l-4 pointer" />-->
        <!--              </el-tooltip>-->
        <!--            </div>-->
        <!--            <div class="m-t-17 font-size-24 color-primary font-strong">-->
        <!--              <span class="font-size-18">￥</span>-->
        <!--              <span>{{ NumFormat(accountInfo.funds) | noData }}</span>-->
        <!--            </div>-->
        <!--          </div>-->
        <!--        </div>-->
      </div>
    </div>

    <basic-tab
      ref="basicTab"
      :tabs-data="tabsData"
      :current="current"
      @tabsChange="tabsChange"
    />

    <div class="m-b-16">
      <el-form ref="form" :model="formModel" label-width="80px">
        <el-col :span="6">
          <el-radio-group
            v-model="formModel.tradeType"
            @change="tradeTypeChange"
          >
            <el-radio-button
              :label="item.value"
              v-for="item in options"
              :key="item.value"
              >{{ item.label }}</el-radio-button
            >
          </el-radio-group>
        </el-col>
        <el-col :span="18" class="flex justify-content-end">
          <el-col :span="6.5">
            <el-form-item label="业务来源">
              <el-select
                clearable
                v-model="formModel.sources"
                placeholder="请选择业务来源"
                @change="sourcesChange"
              >
                <el-option
                  v-for="item in sourcesOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6.5" v-if="formModel.tradeType !== 1">
            <el-form-item label="付款方">
              <el-input
                clearable
                @input="changName"
                v-model="formModel.entName"
                placeholder="请输入付款方名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="11">
            <el-form-item label="交易时间">
              <el-date-picker
                v-model="formModel.time"
                type="daterange"
                clearable
                range-separator="至"
                start-placeholder="开始日期"
                @change="changeDate"
                value-format="yyyy-MM-dd"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-col>
      </el-form>
    </div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :key="key"
      :api-fn="getDetailsList"
      :extral-querys="extralQuerys"
    />
  </basic-card>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import BasicTab from '@/views/manage/financial/financial-basic/payment/drawMoneyHistory/drawMoneyHistory-basic/list/components/basicTab.vue'
import column from './column'
import {
  getDetails,
  getBalanceType,
  getTurnTab,
  getDetailsList,
  getSourceType
} from './api/index'
export default {
  name: 'EntAccountDetail',
  components: { BasicTab },
  mixins: [column],
  data() {
    return {
      key: 1,
      extralQuerys: {
        purseId: this.$route.query.id,
        turnId: -1,
        balanceType: 0
      },
      getDetailsList,
      sourcesOptions: [],
      options: [],
      formModel: {
        tradeType: -1,
        entName: '',
        time: [],
        sources: ''
      },
      NumFormat,
      current: 0,
      tabsData: [],
      accountInfo: {
        entName: '',
        accountNo: '',
        purse: 0,
        funds: 0
      }
    }
  },
  created() {
    this.getDetails()
    this.getBalanceType()
    this.getTurnTab()
    this.getSourceType()
  },
  mounted() {
    const { type = 0 } = this.$route.query
    if (type) {
      this.current = Number(type)
      this.key = Math.random()
      this.extralQuerys.balanceType = Number(type)
      this.$refs['drive-table'].triggerSearch()
    }
  },
  methods: {
    changName(val) {
      this.extralQuerys.entName = val
      this.$refs['drive-table'].triggerSearch()
    },
    changeDate() {
      const { time = [] } = this.formModel
      if (time) {
        this.extralQuerys.startTime = time[0]
        this.extralQuerys.endTime = time[1]
      } else {
        delete this.extralQuerys.startTime
        delete this.extralQuerys.endTime
      }
      this.$refs['drive-table'].triggerSearch()
    },
    sourcesChange(val) {
      this.extralQuerys.source = val
      this.$refs['drive-table'].triggerSearch()
    },
    getSourceType() {
      getSourceType().then(res => {
        this.sourcesOptions = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    tradeTypeChange(val) {
      this.extralQuerys.turnId = val
      this.$refs['drive-table'].triggerSearch()
    },
    goPage(row) {
      const { businessUrlType, businessOrderId, businessId, type } = row
      if (businessUrlType === 99) return false
      const paths = new Map()
      paths.set(1, '/account/transactionInfo/transactionInfoDetails') // 转账登记1
      paths.set(2, '/payment/accountsReceivable/accountsReceivableDetails') // 账单详情
      paths.set(3, '/contract/index/contractDetails') // 证金退款3
      paths.set(4, '/wallet/walletList/walletDetails') // 政策补贴4
      paths.set(5, '/payment/accountsReceivable/accountsReceivableDetails') // 账单详情
      paths.set(6, '/drawMoney/drawMoneyList/drawMoneyDetails') // 付款详情6
      paths.set(7, '/drawMoney/drawMoneyList/drawMoneyDetails') // 付款历史详情 7
      paths.set(8, '/rentOut/replace/replaceDetail') // 调房详情 8
      paths.set(9, '/journal/journalDetail') // 流水认领详情 9
      paths.set(10, '/account/marginDeduction/detail') // 保证金抵扣申请详情 10
      paths.set(11, '/account/marginDeduction/detail') // 租金详情 11

      this.$router.push({
        path: paths.get(businessUrlType),
        query: {
          id: businessId,
          orderId: businessOrderId,
          type:
            businessUrlType === 2 || businessUrlType === 3
              ? type
              : businessUrlType
        }
      })
    },
    goEntDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    },
    getTurnTab() {
      getTurnTab().then(res => {
        this.options = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getBalanceType() {
      getBalanceType().then(res => {
        this.tabsData = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getDetails() {
      getDetails({ id: this.$route.query.id }).then(res => {
        this.accountInfo = res || {}
      })
    },
    tabsChange(val) {
      this.current = val
      this.extralQuerys.balanceType = val
      this.$refs['drive-table'].triggerSearch()
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-radio-button__inner) {
  width: 79px;
}
:deep(.el-date-editor) {
  width: 100%;
}
.p-b-40 {
  padding-bottom: 40px;
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    width: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 32px;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
</style>
