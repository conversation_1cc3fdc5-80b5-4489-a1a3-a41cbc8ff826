<template>
  <div class="p-t-24 w100 flex align-items-center justify-content-between">
    <div class="flex align-items-center" style="width: calc(100% - 320px)">
      <div class="xx">
        <div class="zh">
          <div class="font-size-16 m-t-10 flex align-items-center">
            企业名称
          </div>
          <div
            class="font-strong m-t-17 font-size-24 flex align-items-center pointer"
            @click="goBack"
          >
            <svg-icon style="flex-shrink: 0" icon-class="chevron-left" />
            <div>{{ accountInfo.bodyName | noData }}</div>
          </div>
        </div>
      </div>
      <div class="xx">
        <div class="zh">
          <div class="font-size-16 m-t-10 flex align-items-center pointer">
            <span>缴存基数总额</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="根据当前企业实际租赁的房间数量、面积和签约年份综合计算所得的应缴金额（会随着增减房，实时调整基数）"
              placement="top"
            >
              <svg-icon icon-class="prompt" class="m-l-4 pointer" />
            </el-tooltip>
          </div>
          <div class="m-t-17 font-size-24 color-primary font-strong">
            <span class="font-size-18">￥</span>
            <span>{{ NumFormat(accountInfo.depositedTotal) | noData }}</span>
          </div>
        </div>
      </div>
      <div class="xx">
        <div class="zh">
          <div class="font-size-16 m-t-10 flex align-items-center pointer">
            <span>已缴存总额</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="扣减已抵扣的缴存后，实时累计保证金收款的总金额（根据审批通过后的收款单自动入账）"
              placement="top"
            >
              <svg-icon icon-class="prompt" class="m-l-4 pointer" />
            </el-tooltip>
          </div>
          <div class="m-t-17 font-size-24 color-primary font-strong">
            <span class="font-size-18">￥</span>
            <span>{{ NumFormat(accountInfo.alreadyDeposit) | noData }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="xx" style="width: 320px; max-width: 320px">
      <div class="zh" style="left: 0">
        <div class="font-size-16 m-t-10 flex align-items-center">
          <span>缴存到账率</span>
        </div>
        <div class="w100 m-t-18">
          <el-progress
            :text-inside="true"
            :stroke-width="20"
            :percentage="accountInfo.arrivalRate || 0"
            status="success"
          ></el-progress>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import { depositAccountEntHead } from '../api'

export default {
  name: 'MarginAccountDetailHeader',
  data() {
    return {
      NumFormat,
      accountInfo: {}
    }
  },
  created() {
    this.depositAccountEntHead()
  },
  methods: {
    depositAccountEntHead() {
      const entId = this.$route.query.entId
      if (!entId) return false
      depositAccountEntHead(entId).then(res => {
        this.accountInfo = res || {}
      })
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped lang="scss">
.xx {
  width: 100%;
  max-width: max-content;
  position: relative;
  height: 100px;
  padding-left: 32px;
  & + .xx {
    margin-left: 32px;
  }
  .zh {
    width: 100%;
    overflow: hidden;

    .line {
      position: absolute;
      top: 30px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
</style>
