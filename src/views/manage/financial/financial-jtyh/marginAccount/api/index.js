// 获得园区
import request from '@/utils/request'

export function getParks() {
  return request({
    url: `/bill/purse/admin/parks`,
    method: 'get'
  })
}
// 头部统计
export function depositAccountHead() {
  return request({
    url: `/deposit/account/head`,
    method: 'get'
  })
}
// 保证金账户分页
export function depositAccountPage(params) {
  return request({
    url: `/deposit/account/page`,
    method: 'get',
    params
  })
}
// 详情头部统计
export function depositAccountEntHead(id) {
  return request({
    url: `/deposit/account/ent_head/${id}`,
    method: 'get'
  })
}
// 选择 - 缴存基数 - tab切换
export function depositAccountBaseOptSelect() {
  return request({
    url: `/deposit/account/base_opt_select`,
    method: 'get'
  })
}
// 缴存基数分页
export function depositAccountBasePage(params) {
  return request({
    url: `/deposit/account/ent_page_base`,
    method: 'get',
    params
  })
}
// 选择 - 缴存基数 - 变动依据
export function depositAccountBaseChangeSelect() {
  return request({
    url: `/deposit/account/base_change_select`,
    method: 'get'
  })
}
// 选择 - 已缴存 - tab切换
export function depositAccountAlreadyChangeSelect() {
  return request({
    url: `/deposit/account/already_change_select`,
    method: 'get'
  })
}
// 选择 - 已缴存 - 业务来源
export function depositAccountAlreadyBusinessSelect() {
  return request({
    url: `/deposit/account/already_business_select`,
    method: 'get'
  })
}
// 已缴存分页
export function depositAccountEntPageAlready(params) {
  return request({
    url: `/deposit/account/ent_page_already`,
    method: 'get',
    params
  })
}
// 缴存状态获取
export function depositAccountPayOverSelect(params) {
  return request({
    url: `/deposit/account/pay_over_select`,
    method: 'get',
    params
  })
}
