import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '企业名称',
          minWidth: '220px',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <span
                onClick={() => {
                  this.goEnterpriseDetail(scope.row)
                }}
                class={`${
                  scope.row?.enterStatus === 1 || scope.row?.enterStatus === 2
                    ? 'color-primary pointer'
                    : ''
                }`}
              >
                {scope.row.entName}
              </span>
            )
          }
        },
        {
          prop: 'depositBase',
          label: '缴存基数(元)',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.depositBase)}
              </span>
            )
          }
        },
        {
          prop: 'actualDeposit',
          label: '已缴存(元)',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.actualDeposit)}
              </span>
            )
          }
        },
        // {
        //   prop: 'depositDeduction',
        //   label: '缴存抵扣(元)',
        //   render: (h, scope) => {
        //     return (
        //       <span class={'color-warning'}>
        //         {NumFormat(scope.row.depositDeduction)}
        //       </span>
        //     )
        //   }
        // },
        {
          prop: 'operation',
          label: '操作',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.detailHandle(scope.row)
                  }}
                >
                  详情
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
