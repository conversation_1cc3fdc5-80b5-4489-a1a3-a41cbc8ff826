// 费用名称
export const billType = [
  { label: '全部', value: null },
  { label: '租赁费', value: 63 },
  { label: '售房款', value: 64 },
  { label: '保证金', value: 65 },
  { label: '定向开发费', value: 67 }
]

// 费用名称
export function getBillType(h, val) {
  switch (val) {
    case 63:
      return <basic-tag type="primary" label="租赁费"></basic-tag>
    case 64:
      return <basic-tag type="danger" label="售房款"></basic-tag>
    case 65:
      return <basic-tag type="success" label="保证金"></basic-tag>
    case 66:
      return <basic-tag type="info" label="定向开发费"></basic-tag>
    default:
      return '-'
  }
}

// 费用名称
export function getFeeType(h, val) {
  switch (val) {
    case 63:
      return <basic-tag type="primary" label="租赁费"></basic-tag>
    case 64:
      return <basic-tag type="danger" label="售房款"></basic-tag>
    case 65:
      return <basic-tag type="success" label="保证金"></basic-tag>
    case 66:
      return <basic-tag type="info" label="定向开发费"></basic-tag>
    case 67:
      return <basic-tag type="info" label="归集"></basic-tag>
    case 68:
      return <basic-tag type="info" label="余额退款"></basic-tag>
    default:
      return '-'
  }
}

// 对账结果
export const costResult = [
  { label: '全部', value: null },
  { label: '不平', value: 2 },
  { label: '平账', value: 1 }
]

// 对账结果
export function getcostResult(h, val) {
  switch (val) {
    case 2:
      return <basic-tag type="danger" label="不平"></basic-tag>
    case 1:
      return <basic-tag type="info" label="平账"></basic-tag>
    default:
      return '-'
  }
}

// 核销方式0-自动核销 1-管理核销 2-企业核销 4-现金核销 6-转账核销 8-承兑汇票 10-其他核销、
export function getPayType(h, val) {
  // eslint-disable-next-line default-case
  switch (val) {
    case 0:
      return <div>自动核销</div>
    case 1:
      return <div>管理核销</div>
    case 2:
      return <div>企业核销</div>
    case 4:
      return <div>现金核销</div>
    case 6:
      return <div>转账核销</div>
    case 8:
      return <div>承兑汇票</div>
    case 10:
      return <div>其他核销</div>
    case 15:
      return <div>疫情减免</div>
    default:
      return '-'
  }
}

// 交易状态
export function getTxn(h, val) {
  switch (val) {
    case 0:
      return <basic-tag type="danger" label="处理中"></basic-tag>
    case 1:
      return <basic-tag type="success" label="成功"></basic-tag>
    case 2:
      return <basic-tag type="warning" label="失败"></basic-tag>
    default:
      return '-'
  }
}

// 账户类型
export const accountType = [
  { label: '全部', value: null },
  { label: '总账户', value: 1 },
  { label: '园区账户', value: 2 },
  { label: '企业汇缴账户', value: 3 },
  { label: '租赁费账户', value: 4 },
  { label: '售房款账户', value: 5 },
  { label: '保证金账户', value: 6 },
  { label: '定向开发费账户', value: 7 },
  { label: '企业缴款账户', value: 8 }
]

export function getAccountType(h, val) {
  switch (val) {
    case 1:
      return '总账户'
    case 2:
      return '园区账户'
    case 3:
      return '企业汇缴账户'
    case 4:
      return '租赁费账户'
    case 5:
      return '售房款账户'
    case 6:
      return '保证金账户'
    case 7:
      return '定向开发费账户'
    case 8:
      return '企业缴款账户'
    default:
      return '-'
  }
}

// 归集类型
export const collectionType = [
  { label: '人工归集', value: 2 },
  { label: '系统归集', value: 1 }
]

export function getCollectionType(h, val) {
  switch (val) {
    case 1:
      return '系统归集'
    case 2:
      return '人工归集'
    default:
      return '-'
  }
}

// 归集状态
export const collectionStatus = [
  { label: '归集成功', value: 1 },
  { label: '归集失败', value: 2 }
]

export function getCollectionStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag type="success" label="归集成功"></basic-tag>
    case 2:
      return <basic-tag type="danger" label="归集失败"></basic-tag>
    default:
      return '-'
  }
}

// 审核状态
export const auditStatus = [
  { label: '未对账', value: 0 },
  { label: '平账', value: 1 },
  { label: '不平', value: 2 }
]
// 1-企业 2 项目经办人 3 部门经理 4 财务  5 财务退余额状态 1-退回 2-拒绝 3-通过 4-待经办人审核 5-部门经理 6-财务
export function getAuditStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag type="warning" label="未对账"></basic-tag>
    case 1:
      return <basic-tag type="info" label="平账"></basic-tag>
    case 2:
      return <basic-tag type="success" label="不平"></basic-tag>
    default:
      return '-'
  }
}
