<!-- 昨日对账 -->
<template>
  <basic-card>
    <basic-tab
      :tabsData="tabsData"
      :current="current"
      @tabsChange="tabsChange"
    ></basic-tab>
    <drive-table
      ref="drive-table"
      :columns="tableColumnTemp"
      :api-fn="findAmountCountList"
      :extral-querys="extralQuerys"
      :search-querys-hook="searchQueryHook"
    >
      <template v-slot:operate-right>
        <el-button type="info" size="mini" @click="exportExcel">
          <svg-icon icon-class="arrow-down" />
          <span>导出</span>
        </el-button>
      </template>
    </drive-table>
  </basic-card>
</template>

<script>
import ColumnMixins from './column/column'
import downloads from '@/utils/download'
import BasicTab from '@/components/BasicTab'
import { findAmountCountList, saveAmountCheckExcel } from './api'

export default {
  name: 'paymentPlan',
  components: { BasicTab },
  mixins: [ColumnMixins],
  data() {
    return {
      findAmountCountList,
      tableColumnTemp: [],
      tabsData: [
        { label: '账户核销', value: 1 },
        { label: '资金归集', value: 2 },
        { label: '余额退款', value: 3 }
      ],
      extralQuerys: {
        isYesterdayCheck: 1,
        type: 1
      },
      current: 1
    }
  },
  created() {
    this.tableColumnTemp = this.tableColumn1
  },
  methods: {
    tabsChange(e) {
      this.current = this.tabsData[e].value
      this.extralQuerys.type = this.current
      this.tableColumnTemp = this['tableColumn' + this.current]
      this.$refs['drive-table'].triggerSearch(this.extralQuerys)
    },

    // 计划详情
    goTableDetails(e) {
      this.$router.push({
        path: '/reconciliateManagement/reconciliateManagementDetails',
        query: {
          id: e.id
        }
      })
    },
    // 资金归集详情
    goCashSweep(e) {
      this.$router.push({
        path: '/reconciliateManagement/cashSweep',
        query: {
          id: e.id
        }
      })
    },
    // 余额退回
    goReturn(e) {
      this.$router.push({
        path: '/reconciliateManagement/payBanlance',
        query: {
          id: e.id
        }
      })
    },

    exportExcel() {
      saveAmountCheckExcel(this.$refs['drive-table'].getRequestQuerys()).then(
        res => {
          if (res) {
            downloads.requestDownload(res, 'excel')
          }
        }
      )
    },

    // 重置搜索参数
    searchQueryHook(e) {
      const [startTime = '', endTime = ''] = e.submitTime || []
      let params = {}
      if (e.submitTime && e.submitTime.length > 1) {
        params = {
          startTime,
          endTime
        }
      }
      delete e.submitTime
      return {
        ...e,
        result: e.checkSts === undefined ? '' : e.checkSts,
        params
      }
    }
  }
}
</script>

<style scoped></style>
