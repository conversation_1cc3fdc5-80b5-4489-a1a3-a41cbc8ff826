/*
 * @Author: your name
 * @Date: 2021-11-27 14:06:11
 * @LastEditTime: 2021-12-23 01:18:01
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \rat-platform-web\src\views\workspace\refundManagement\accountNoFinancial\column.js
 */
import { getcostStatus } from './status'
// import { interceptString } from '@/utils/util'
import { NumFormat } from '@/utils/tools'

const column = {
  data() {
    return {
      billColumn: [
        // {
        //   prop: 'contractNum',
        //   label: '合同编号'
        // },
        {
          prop: 'billCode',
          label: '账单编号',
          //  minWidth: '180%'
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.goBillDetails(scope.$index, scope.row)
                  }}
                >
                  {scope.row.billCode ? scope.row.billCode : ''}
                </el-link>
              </div>
            )
          }
        },
        {
          prop: 'feeAcNme',
          label: '费用名称',
          minWidth: '35%'
        },
        {
          prop: 'feeCycle',
          label: '账单周期',
          minWidth: '45%',
          align: 'center'
        },
        {
          prop: 'payAmount',
          label: '账单金额(元)',
          align: 'right',
          minWidth: '35%',
          render: (h, scope) => {
            return (
              <div class="text-warning">{NumFormat(scope.row.payAmount)}</div>
            )
          }
        },
        {
          prop: 'billStatus',
          label: '账单状态',
          align: 'center',
          minWidth: '30%',
          render: (h, scope) => {
            return <div>{getcostStatus(h, scope.row.billStatus)} </div>
          }
        }
      ]
    }
  }
}
export default column
