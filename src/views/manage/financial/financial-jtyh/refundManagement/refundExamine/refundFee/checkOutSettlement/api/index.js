import request from '@/utils/request'

// 计算保证金租赁费税费
export function calculateMoney(id) {
  return request({
    url: `/entSettlement/calculateMoney/${id}`,
    method: 'get',
    type: 'finance'
  })
}

// 计算税费
export function calculateTaxMoney(id) {
  return request({
    url: `/entSettlement/calculateTaxMoney/${id}`,
    method: 'get',
    type: 'finance'
  })
}

// 经办人保存结算
export function saveExitSettlement(data) {
  return request({
    url: `/entSettlement/saveExitSettlement`,
    method: 'post',
    type: 'finance',
    data
  })
}

// 查看经办人保存结算
export function getSaveExitSettlement(id) {
  return request({
    url: `/entSettlement/getSaveExitSettlement/${id}`,
    method: 'get',
    type: 'finance'
  })
}

// 经办人提交结算
export function submitExitSettlement(data) {
  return request({
    url: `/entSettlement/submitExitSettlement`,
    method: 'post',
    type: 'finance',
    data
  })
}

// 查询应收账单信息列表
export function parkFindPaymentBillList(params) {
  return request({
    url: `/entSettlement/parkFindPaymentBillList`,
    method: 'get',
    type: 'finance',
    params
  })
}

// 园区查看退款账单列表
export function parkFindExitSettleBillList(id) {
  return request({
    url: `/entSettlement/parkFindExitSettleBillList/${id}`,
    method: 'get',
    type: 'finance'
  })
}

// 选择应收账单生成退款账账单
export function generateSettleBillList(data) {
  return request({
    url: `/entSettlement/generateSettleBillList`,
    method: 'post',
    type: 'finance',
    data
  })
}
