<template>
  <div>
    <el-row class="m-b-10">
      <el-button type="text" class="color-primary" @click="getBill()"
        ><svg-icon icon-class="books" class="m-r-5" />获取账单</el-button
      >
    </el-row>
    <el-form
      ref="settlementInformation"
      label-width="130px"
      :model="formModel"
      :rules="rules"
    >
      <el-form-item label="保证金退款额：" prop="bond">
        <div class="flex">
          <div><el-input v-model="formModel.bond" :disabled="true" /></div>
          <div class="m-l-8">元</div>
        </div>
      </el-form-item>
      <el-form-item label="租赁费退款额：" prop="rentMoney">
        <div class="flex">
          <div>
            <el-input v-model="formModel.rentMoney" :disabled="true" />
          </div>
          <div class="m-l-8">元</div>
        </div>
      </el-form-item>
      <el-form-item label="税费：" prop="taxMoney">
        <div class="flex">
          <div><el-input v-model="formModel.taxMoney" /></div>
          <div class="m-l-8">元</div>
          <div class="m-l-20">
            <el-button @click="calculation">计算</el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="退款总金额：" prop="returnMoney">
        <div class="flex">
          <div>
            <el-input v-model="formModel.returnMoney" :disabled="true" />
          </div>
          <div class="m-l-8">元</div>
        </div>
      </el-form-item>
      <el-form-item label="备注信息：">
        <el-input v-model="formModel.comments" type="textarea" />
      </el-form-item>
    </el-form>

    <!-- 获取账单 -->
    <dialog-cmp
      title="获取账单"
      :visible.sync="visible"
      width="70%"
      :have-operation="false"
    >
      <div>
        <get-bill v-if="visible" @closeVisible="closeVisible" @close="close" />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import DescriptorsMixins from './descriptors'
import CloumnMixins from '../column'
import getBill from '../getBill/index.vue'
import {
  calculateMoney,
  calculateTaxMoney,
  saveExitSettlement,
  submitExitSettlement,
  getSaveExitSettlement
} from '../api/index'
export default {
  name: 'SettlementInformation',
  mixins: [DescriptorsMixins, CloumnMixins],
  components: {
    getBill
  },
  data() {
    return {
      formModel: {},
      rules: {
        bond: [
          { required: true, message: '请输入保证金退款额', trigger: 'blur' }
        ],
        rentMoney: [
          { required: true, message: '请输入租赁费退款额', trigger: 'blur' }
        ],
        taxMoney: [{ required: true, message: '请输入税费', trigger: 'blur' }],
        returnMoney: [
          { required: true, message: '请输入退款总金额', trigger: 'blur' }
        ]
      },
      visible: false,
      visibleRefund: false,
      detailId: null,
      disdate: {}, // 保证金租赁费税费
      visdate: {}, // 计算税费
      preservationList: {}, // 保存结算
      saveSettlement: {},
      determine: {} // 提交结算
    }
  },
  mounted() {
    this.id = this.$route.query.id
    this.id && this.getSaveExitSettlement()
  },
  methods: {
    // 获取账单
    getBill() {
      this.visible = true
    },
    getRefund() {
      this.visibleRefund = true
    },
    closeVisible() {
      this.visible = false

      calculateMoney(this.id).then(res => {
        this.disdate = res
        this.formModel = res
      })
    },
    // 取消
    close() {
      this.visible = false
    },
    // 计算税费
    calculation() {
      calculateTaxMoney(this.id).then(res => {
        this.visdate = res
        this.formModel = { ...this.disdate, ...res }
      })
    },
    // 保存
    preservation() {
      this.$refs.settlementInformation.validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.formModel))
          params.id = this.id
          saveExitSettlement(params).then(res => {
            this.preservationList = res
            this.getSaveExitSettlement()
          })
        }
      })
    },
    // 查看经办人保存结算
    getSaveExitSettlement() {
      getSaveExitSettlement(this.id).then(res => {
        this.saveSettlement = res
      })
    },
    // 提交结算
    settlementSubmit() {
      this.$refs.settlementInformation.validate(valid => {
        if (valid) {
          const params = JSON.parse(JSON.stringify(this.formModel))
          params.id = this.id
          submitExitSettlement(params).then(res => {
            this.determine = res
            this.$router.go(-1)
          })
        }
      })
    }
  }
}
</script>

<style></style>
