/*
 * @Author: your name
 * @Date: 2021-09-15 15:24:28
 * @LastEditTime: 2021-09-29 14:54:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \rat-platform-web\src\views\workspace\financialManagement\collectionResults\descriptors.js
 */
import { accountType } from './status'
export default {
  data() {
    return {
      descriptors: {
        parkName: {
          type: 'string',
          form: 'input',
          label: '园区名称',
          span: 24,
          message: '请输入园区名称'
        },
        accTyp: {
          type: 'number',
          form: 'select',
          label: '账户类型',
          span: 24,
          message: '请输入账户类型',
          options: accountType
        },
        feeCycle: {
          type: 'string',
          form: 'input',
          label: '费用期间',
          span: 24,
          message: '请输入费用期间'
        },
        direct: {
          type: 'string',
          form: 'input',
          label: '归集去向',
          span: 24,
          message: '请输入归集去向'
        },
        backRecordTotalMoney: {
          type: 'number',
          form: 'input',
          label: '允许归集金额（元）',
          span: 24,
          message: '请输入允许归集金额'
        },
        backRecordMoney: {
          type: 'number',
          form: 'input',
          label: '归集金额',
          span: 24,
          message: '请输入归集金额'
        }
      },
      descriptorsFirst: {
        parkName: {
          type: 'string',
          form: 'input',
          label: '园区名称',
          span: 24,
          message: '请输入园区名称'
        },
        accTyp: {
          type: 'number',
          form: 'select',
          label: '账户类型',
          span: 24,
          message: '请输入账户类型',
          options: accountType
        },
        direct: {
          type: 'string',
          form: 'input',
          label: '归集去向',
          span: 24,
          message: '请输入归集去向'
        },
        backRecordTotalMoney: {
          type: 'number',
          form: 'input',
          label: '允许归集金额（元）',
          span: 24,
          message: '请输入允许归集金额'
        },
        backRecordMoney: {
          type: 'number',
          form: 'input',
          label: '归集金额',
          span: 24,
          message: '请输入归集金额'
        }
      }
    }
  }
}
