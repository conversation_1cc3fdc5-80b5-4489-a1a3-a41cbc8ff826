<template>
  <div>
    <module-header
      type="primary"
      title="检查详情"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    >
    </module-header>
    <div class="lateral-wrapper">
      <!-- 内容区域 -->
      <div class="module-list">
        <div class="info">
          <div class="flex m-t-28">
            <div style="flex: 0.25">
              <span class="label text">检查主题</span>
              <span class="value text">{{ detail.checkTopic | noData }}</span>
            </div>
            <div style="flex: 0.25">
              <span class="label text label-w">检查时间</span>
              <span class="value text">{{ detail.checkTime | noData }}</span>
            </div>
            <div style="flex: 0.25">
              <span class="label text">检查结果</span>
              <span
                class="value text color-warning"
                v-if="detail.checkResult < 2"
                >{{ detail.checkResultStr | noData }}</span
              >
              <span class="value text color-success" v-else>{{
                detail.checkResultStr | noData
              }}</span>
            </div>
            <div style="flex: 0.25">
              <span class="label text">检查部门</span>
              <span class="value text">{{ detail.deptName | noData }}</span>
            </div>
          </div>
          <div class="m-t-24">
            <div class="des">
              <span class="label">检查内容</span>
              <span class="value">{{ detail.checkContentStr | noData }}</span>
            </div>
          </div>
          <div class="m-t-24">
            <div class="des">
              <span class="label">检查企业</span>
              <span class="value">
                <span v-for="item in detail.entList" :key="item.id">
                  {{ item.enterpriseName }}
                </span>
              </span>
            </div>
          </div>
        </div>
        <div v-if="detail.checkRecordList && detail.checkRecordList.length > 0">
          <div
            class="info record"
            v-for="(item, index) in detail.checkRecordList"
            :key="item.id"
          >
            <div class="box-three">
              <div v-if="index === 0">
                <div class="font-size-16 m-b-24">检查记录</div>
                <div class="line"></div>
              </div>
              <div class="font-size-14">
                <span>基本信息</span>
              </div>
              <div class="firm m-b-16 m-t-16">
                <div class="flex flex-wrap">
                  <div
                    class="description m-r-24 line-height-28 font-size-14"
                    v-for="t in item.entList"
                    :key="t.id"
                  >
                    <svg-icon icon-class="building" />
                    <span class="m-l-10">{{ t.enterpriseName }}</span>
                  </div>
                </div>
              </div>
              <div class="m-b-24">
                <el-descriptions
                  class="half-width"
                  :column="2"
                  size="medium"
                  border
                >
                  <el-descriptions-item>
                    <template slot="label"> 检查时间 </template>
                    {{ item.checkTime | noData }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 录入时间 </template>
                    {{ item.createTime | noData }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 检查人员 </template>
                    {{ item.checkerName | noData }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 检查地点 </template>
                    {{ item.location | noData }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 下次检验时间 </template>
                    <span>{{ item.nextCheckTime | noData }}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 检查结果 </template>
                    <span
                      :class="{
                        'color-success': item.checkResult === 1,
                        'color-warning': item.checkResult === 2
                      }"
                      v-if="item.checkResultStr"
                    >
                      {{ item.checkResultStr }}
                    </span>
                    <span v-else>
                      {{ '暂无数据' }}
                    </span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 检查内容 </template>
                    {{ item.checkContent | noData }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <div class="line"></div>
              <div class="box-three m-b-24">
                <div class="font-size-14 m-b-16">重大事项情况</div>
                <div class="description color-b">
                  {{ item.majorIssues | noData }}
                </div>
              </div>
              <div class="line"></div>
              <div
                class="box-three m-b-24"
                v-if="item.attach && Object.keys(item.attach).length > 0"
              >
                <div class="font-size-14 m-b-16">附件信息</div>
                <div>
                  <drive-table
                    ref="drive-table"
                    :columns="fileColumn"
                    :table-data="item.attach.informationAttach"
                  >
                  </drive-table>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="record-info">
          <div class="font-size-16 m-b-24">检查记录</div>
          <div class="line"></div>
          <el-empty description="暂无数据"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from './column'
import { getDetail, getDownload } from './api'
import downloads from '@/utils/download'
export default {
  name: 'CheckDetail',
  components: {
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      fileData: [],
      id: this.$route.query.id,
      detail: {}
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    downloadHander(file) {
      let type = ''
      const imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif']
      const excelist = ['xls', 'xlsx']
      type = imglist.includes(file.extentionName.substring(1))
        ? 'jpeg'
        : excelist.includes(file.extentionName.substring(1))
        ? 'excel'
        : ''
      downloads.requestDownload(getDownload(file.id), type, file.name)
    },
    getDetail() {
      getDetail(this.$route.query.id).then(res => {
        this.detail = res
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.color-b {
  color: #575757;
  line-height: 22px;
}
.line {
  width: 100%;
  height: 1px;
  background: #e9f0ff;
  margin-bottom: 32px;
}
:deep(.el-descriptions-item__label.is-bordered-label) {
  background-color: #f3f3f3;
  color: #9d9d9d;
}
:deep(.half-width) {
  .el-descriptions-item__content {
    width: 40%;
  }
  .el-descriptions-item__label {
    width: 15%;
  }
}
.module-list {
  margin-top: 24px;
  .info {
    margin-top: 16px;
    padding: 0px 24px 24px 24px;
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #e9f0ff;
    .label {
      font-size: 14px;
      color: #bcbcbc;
      margin-right: 13px;
      width: 60px;
      display: inline-block;
    }
    .des {
      display: grid;
      grid-template-columns: 60px 1fr;
      grid-gap: 13px;
    }
    .value {
      font-size: 14px;
      display: inline-block;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
      overflow: hidden;
    }
    .label-w {
      width: 90px;
      text-align: right;
    }
    .text {
      //文字与文字底线对齐
      display: inline-block;
      vertical-align: middle;
    }
  }
  .record-info {
    padding: 24px;
    background: #ffffff;
    border: 1px solid #e9f0ff;
    margin-top: 16px;
    margin-bottom: 16px;
    border-radius: 6px 6px 6px 6px;
  }
  .record {
    padding: 32px 24px 24px 24px;
    background: #ffffff;
    margin-top: 10px;
    .firm {
      //超出换行
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
      overflow: hidden;
    }
  }
}
</style>
