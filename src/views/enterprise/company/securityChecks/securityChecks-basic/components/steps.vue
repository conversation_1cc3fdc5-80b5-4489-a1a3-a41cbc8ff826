<template>
  <div class="steps-container flex align-items-center justify-content-between">
    <div
      class="flex steps-wrapper"
      v-for="(item, index) in stepsData"
      :key="index"
    >
      <div class="steps-item flex">
        <div class="left flex align-items-center">
          <div
            v-if="active === index"
            class="circle active-circle font-size-16 flex align-items-center justify-content-center font-strong"
          >
            <span>{{ index + 1 }}</span>
          </div>
          <div
            v-else
            class="circle font-size-16 flex align-items-center justify-content-center font-strong"
            :class="getCircleStyle(index)"
          >
            <svg-icon
              v-if="active > index"
              class="color-primary"
              icon-class="check"
            />
            <span v-else>{{ index + 1 }}</span>
          </div>
        </div>
        <div class="right">
          <div
            class="font-size-16 color-text-placeholder title font-strong"
            :class="getLabelStyle(index)"
          >
            {{ item.label }}
          </div>
          <div
            v-if="active > index"
            class="font-14 color-text-placeholder status"
          >
            {{ item.completed }}
          </div>
          <div
            v-else-if="active < index"
            class="font-14 color-text-placeholder status"
          >
            {{ item.uncompleted }}
          </div>
          <div v-else class="font-14 color-primary status">进行中</div>
        </div>
      </div>

      <div
        v-if="index < stepsData.length - 1"
        class="steps-line"
        :class="getLineBackground(index)"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterParkApplyBasicSteps',
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      stepsData: [
        {
          label: '申请表提交',
          uncompleted: '填写中',
          completed: '已完成'
        },
        {
          label: '园区审批',
          uncompleted: '待审批',
          completed: '已完成'
        }
      ]
    }
  },
  methods: {
    getCircleStyle(index) {
      return this.active > index ? 'circle-primary' : ''
    },

    getLineBackground(index) {
      return this.active > index ? 'line-primary' : ''
    },

    getLabelStyle(index) {
      const { active } = this
      return active === index
        ? 'label-primary'
        : active > index
        ? 'label-text-primary'
        : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.steps-container {
  width: 400px;

  .steps-wrapper {
    flex: 1;

    &:nth-last-child(1) {
      flex: 0 0 104px;
      width: 104px;
    }

    .steps-item {
      flex: 0 0 124px;

      .left {
        margin-right: 16px;
        height: 30px;
        line-height: 30px;

        .circle {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid;
          @include font_color(--color-text-placeholder);
          @include border_color(--color-text-placeholder);
        }

        .active-circle {
          @include font_color(--color-white);
          @include background_color(--color-primary);
          @include border_color(--color-primary);
        }

        .circle-primary {
          @include font_color(--color-primary);
          @include border_color(--color-primary);
        }
      }
      .right {
        .title {
          height: 30px;
          line-height: 30px;
          margin-bottom: 6px;
        }

        .label-text-primary {
          @include font_color(--color-text-primary);
        }

        .label-primary {
          @include font_color(--color-primary);
        }

        .status {
          line-height: 22px;
        }
      }
    }

    .steps-line {
      flex: 1;
      height: 2px;
      @include background_color(--color-text-placeholder);
      margin: 13px 16px 0;
    }

    .line-primary {
      @include background_color(--color-primary);
    }
  }
}
</style>
