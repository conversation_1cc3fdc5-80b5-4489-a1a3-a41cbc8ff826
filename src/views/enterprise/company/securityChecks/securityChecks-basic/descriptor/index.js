import DictPopover from '../components/DictPopover/index.vue'
export default {
  components: {
    DictPopover
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          name: {
            form: 'input',
            label: '设备名称',
            width: '50%',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入设备名称'
              },
              {
                max: 20,
                message: '请输入设备名称、不超过20字'
              }
            ]
          },
          allocation: {
            form: 'select',
            label: '设备类型',
            span: 13,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择设备类型'
              }
            ],
            options: [],
            props: {
              multiple: true
            },
            customRight: () => {
              return (
                <div class="line-height-30">
                  <dict-popover
                    title={'设备类型'}
                    type="course_type"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          brand: {
            form: 'select',
            label: '设备品牌',
            span: 13,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择设备品牌'
              }
            ],
            options: [],
            props: {
              multiple: true
            },
            customRight: () => {
              return (
                <div class="line-height-30">
                  <dict-popover
                    title={'设备品牌'}
                    type="course_type"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          date: {
            form: 'input',
            label: '生产日期',
            span: 13,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入生产日期'
              },
              {
                max: 20,
                message: '请输入生产日期、不超过20字'
              }
            ]
          },
          price: {
            form: 'input',
            label: '使用年限',
            span: 13,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入使用年限',
                pattern: /^([1-9][\d]{0,7}|0)(\.[\d]{1,2})?$/
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">年</span>
                </div>
              )
            }
          },
          inspectionCycle: {
            form: 'select',
            label: '检验周期',
            span: 13,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择检验周期'
              }
            ],
            options: [],
            props: {
              multiple: true
            }
          },
          nextCheckTime: {
            form: 'input',
            label: '下次检验时间',
            span: 11,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入下次检验时间'
              }
            ]
          },
          state: {
            form: 'select',
            label: '设备状态',
            span: 13,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择设备状态'
              }
            ],
            options: [],
            props: {
              multiple: true
            }
          },
          address: {
            form: 'select',
            label: '存放地点',
            span: 13,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择园区'
              }
            ],
            options: [],
            props: {
              multiple: true
            }
          },
          floor: {
            form: 'select',
            span: 11,
            label: '',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择楼层'
              }
            ],
            options: [],
            props: {
              multiple: true
            }
          },
          location: {
            form: 'input',
            label: '',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入具体位置'
              }
            ]
          },
          parameter: {
            form: 'input',
            label: '设备参数',
            rule: [
              {
                type: 'string',
                message: '请输入设备参数'
              }
            ],
            //类型是textarea时，需要设置rows
            props: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 6 }
            }
          }
        }
      }
    }
  }
}
