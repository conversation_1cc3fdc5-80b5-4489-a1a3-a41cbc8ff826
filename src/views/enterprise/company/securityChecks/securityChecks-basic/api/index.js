import request from '@/utils/request'

// 获得安全检查分页
export function getPage(params) {
  return request({
    url: `/ck/ent/security_check/page`,
    method: 'get',
    params
  })
}
// 详情
export function getDetail(params) {
  return request({
    url: `/ck/ent/security_check/get?id=${params}`,
    method: 'get'
  })
}
// 下载
export function getDownload(attachId) {
  return `${process.env.VUE_APP_URL_PREFIX}/main-attachment/download/${attachId}`
}
