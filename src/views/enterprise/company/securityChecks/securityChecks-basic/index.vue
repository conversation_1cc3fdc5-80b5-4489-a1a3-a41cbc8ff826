<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="安全检查"
      desc="积极响应园区下发安全检查任务,保障安全生产"
      :img="require('@/assets/images/enterprise/company/safety/header-bg.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper card">
      <basic-tab
        ref="basicTab"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
      />
      <!-- 内容区域 -->
      <div class="module-list">
        <drive-table
          ref="drive-table"
          :extral-querys="extralQuerys"
          :api-fn="getPage"
          :columns="tableColumn"
        >
        </drive-table>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader/index.vue'
import ColumnMixins from './column'
import { getPage } from './api'
import BasicTab from '@/components/BasicTab/index.vue'
export default {
  name: 'SecurityChecks',
  components: {
    BasicTab,
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      current: 0,
      getPage,
      list: [
        {
          label: '待检查',
          value: 0
        },
        {
          label: '待整改',
          value: 1
        },
        {
          label: '已完成',
          value: 2
        }
      ],
      extralQuerys: {
        type: 2,
        checkResult: 0
      },
      isEnterpriseAuth: false,
      showApply: false,
      enterParkStatus: null
    }
  },
  methods: {
    addHander() {
      this.$router.push({
        path: '/company/safety/addDevice'
      })
    },
    tabsChange(val) {
      this.current = val
      this.extralQuerys.checkResult = val
      this.$refs['drive-table'].refreshTable()
    },

    goDetail(val) {
      this.$router.push({
        path: '/company/safety/checkDetail',
        query: {
          id: val.id,
          entStatus: val.entStatus
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin: 40px 48px;
}
.card {
  margin-top: 24px;
  position: relative;
  background: #ffffff;
  border-radius: 6px 6px 6px 6px;
  padding-top: 10px;
  border: 1px solid #e9f0ff;
}
.table-data {
  margin: 0 48px;
}
</style>
