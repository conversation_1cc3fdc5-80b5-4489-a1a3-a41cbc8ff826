function matchFileSuffixType(fileName) {
  // 后缀获取
  let suffix = ''
  // 获取类型结果
  let result = ''
  try {
    let flieArr = fileName.split('.')
    suffix = flieArr[flieArr.length - 1]
  } catch (err) {
    suffix = ''
  }
  // fileName无后缀返回 false
  if (!suffix) {
    result = false
    return result
  }
  // 图片格式
  let imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif']
  // 进行图片匹配
  result = imglist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'image'
    return result
  }
  // 匹配txt
  let txtlist = ['txt']
  result = txtlist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'txt'
    return result
  }
  // 匹配 excel
  let excelist = ['xls', 'xlsx']
  result = excelist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'excel'
    return result
  }
  // 匹配 word
  let wordlist = ['doc', 'docx']
  result = wordlist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'word'
    return result
  }
  // 匹配 pdf
  let pdflist = ['pdf']
  result = pdflist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'pdf'
    return result
  }
  // 匹配 ppt
  let pptlist = ['ppt']
  result = pptlist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'ppt'
    return result
  }
  // 匹配 视频
  let videolist = ['mp4', 'm2v', 'mkv']
  result = videolist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'video'
    return result
  }
  // 匹配 音频
  let radiolist = ['mp3', 'wav', 'wmv']
  result = radiolist.some(item => {
    return item === suffix
  })
  if (result) {
    result = 'radio'
    return result
  }
  // 其他 文件类型
  result = 'other'
  return result
}

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'checkTopic',
          label: '检查主题'
        },
        {
          prop: 'checkTime',
          label: '检查时间'
        },
        {
          prop: 'deptName',
          label: '检查部门'
        },
        {
          label: '操作',
          fixed: 'right',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="text"
                  class="color-primary"
                  onClick={() => {
                    this.goDetail(scope.row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ],
      fileColumn: [
        {
          prop: 'name',
          label: '附件名称'
        },
        {
          prop: 'size',
          label: '附件大小',
          render: (h, { row }) => {
            return (
              <div>
                {row.size
                  ? (row.size / 1024 / 1024).toFixed(2) + 'MB'
                  : '暂无数据'}
              </div>
            )
          }
        },
        {
          prop: 'extentionName',
          label: '文件类型',
          render: (h, { row }) => {
            return <div>{matchFileSuffixType(row.extentionName)}</div>
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 150,
          render: (h, { row }) => {
            return (
              <div>
                <el-link
                  type="text"
                  class="color-primary"
                  onClick={() => {
                    this.downloadHander(row)
                  }}
                >
                  下载
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
