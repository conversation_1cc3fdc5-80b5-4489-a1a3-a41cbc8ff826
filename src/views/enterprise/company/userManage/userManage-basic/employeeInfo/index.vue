<template>
  <div class="employee-info">
    <div class="w100 flex justify-content-end m-b-12">
      <el-button type="primary" @click="handleClick">选择员工</el-button>
    </div>
    <drive-table
      class="w100 overflow-auto"
      ref="drive-table"
      max-height="350"
      :columns="tableColumns"
      :tableData="tableData"
    />

    <dialog-cmp
      title="选择在职员工"
      :visible.sync="visible"
      width="50%"
      @close="closeEmp"
      @confirmDialog="confirmDialog"
    >
      <div>
        <el-form ref="form" :model="formModel" label-width="80px">
          <el-col :span="8">
            <el-form-item label="员工姓名">
              <el-input
                clearable
                @input="handleInput($event, 'name')"
                v-model="formModel.name"
                placeholder="请输入员工姓名"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号">
              <el-input
                clearable
                @input="handleInput($event, 'phoneNumber')"
                v-model="formModel.phoneNumber"
                placeholder="请输入手机号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="类型">
              <el-select
                clearable
                v-model="formModel.empType"
                placeholder="请选择类型"
                @change="empTypeChange"
              >
                <el-option
                  v-for="item in empTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-form>
        <div class="color-info m-b-12 m-t-12 font-size-12">
          已选择{{selectionData.length}}位员工/最多10位
        </div>
        <drive-table
          ref="drive-table-emp"
          v-if="visible"
          max-height="350"
          @selection-change="handleSelectionChange"
          :columns="tableColumnsEmp"
          :api-fn="getEmp"
          @getData="getData"
          :extral-querys="formModel"
          :isNeedPagination="true"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import { getEmp } from '../api'
export default {
  name: 'EmployeeInfo',
  props: {
    value: {
      type: Array,
      default: () => ([])
    },
    empTypeOptions: {
      type: Array,
      default: () => ([])
    },
    positionOptions: {
      type: Array,
      default: () => ([])
    },
  },
  data() {
    return {
      getEmp,
      debounceTimeout: null,
      formModel: {},
      visible: false,
      tableColumns: [
        {
          label: '员工姓名',
          prop: 'name',
          minWidth: 80,
        },
        {
          label: '性别',
          prop: 'sexStr',
          minWidth: 80,
        },
        {
          label: '手机号',
          prop: 'phone',
          minWidth: 120,
        },
        {
          label: '学历',
          prop: 'educationStr',
          minWidth: 80,
        },
        {
          label: '员工类型',
          prop: 'empTypeStr',
          minWidth: 80,
        },
        {
          label: '职位',
          prop: 'position',
          minWidth: 120,
          render: (h, { row }) => {
            const positionOptions = this.positionOptions
            return (
              <div>
                <el-select v-model={row.positioned} placeholder="请选择">
                  {
                    positionOptions.map(item => (
                      <el-option label={item.label} value={item.value} />
                    ))
                  }
                </el-select>
              </div>
            )
          }
        },
        {
          label: '操作',
          width: 80,
          render: (h, { row }) => {
            return (
              <div>
              <el-link
                  type="text"
                  class="color-primary"
                  onClick={() => {
                    this.moveOut(row)
                  }}
                >
                  移出
                </el-link>
              </div>
            )
          }
        }
      ],
      tableColumnsEmp: [
        {
          type:'selection',
        },
        {
          label: '员工姓名',
          prop: 'name',
        },
        {
          label: '性别',
          prop: 'sexStr'
        },
        {
          label: '手机号',
          prop: 'phone',
        },
        {
          label: '学历',
          prop: 'educationStr'
        },
        {
          label: '员工类型',
          prop: 'empTypeStr',
        },
      ],
      tableData: [],
      selectionData: [],
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    moveOut(row) {
      this.tableData = this.tableData.filter(item => item.id !== row.id)
      this._value = this.tableData
    },
    handleSelectionChange(selection) {
      this.selectionData = selection
    },
    handleInput(event,key) {
      if (this.debounceTimeout) {
        clearTimeout(this.debounceTimeout);
      }

      this.debounceTimeout = setTimeout(() => {
        this.formModel[key] = event;
        this.$refs['drive-table-emp']?.refreshTable();
      }, 300);
    },
    empTypeChange() {
      this.$refs['drive-table-emp']?.refreshTable()
    },
    confirmDialog() {
      if (this.selectionData.length > 10) return this.$message.warning('最多只能选择10位员工')
      this.tableData = this.selectionData
      this._value = this.selectionData
      this.closeEmp()
    },
    closeEmp() {
      this.visible = false
      this.formModel = {}
      setTimeout(() => {
        this.$refs['drive-table-emp']?.clearSelection();
        this.selectionData = [];
      },300);
    },
    getData(res) {
      if (res && res.list && res.list.length){
        this.$nextTick(() => {
          this.value.forEach(row => {
            const index = res.list.findIndex(item => item.id === row.id)
            if (index > -1) {
              this.$refs['drive-table-emp']?.toggleRowSelection(res.list[index], true)
            }
          });
        })
      }
    },
    handleClick() {
      this.visible = true
      this.formModel = {}

    }
  }
}
</script>
