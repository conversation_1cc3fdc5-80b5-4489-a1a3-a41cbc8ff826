import request from '@/utils/request'

export function getManager(params) {
  return request({
    url: `/ent/account/get_enterprise_account`,
    method: 'get',
    params
  })
}

//员工类型字典
export function getEmployeeType() {
  return request({
    url: `/dict/tenantDictData/getByDictType?dictType=emp_type`,
    method: 'get'
  })
}

//性别
export function getSex() {
  return request({
    url: `/system/dict-data/getByDictType?dictType=system_user_sex`,
    method: 'get'
  })
}

//学历
export function getEducation() {
  return request({
    url: `/dict/tenantDictData/getByDictType?dictType=tenant_user_education`,
    method: 'get'
  })
}

//职位
export function getPosition() {
  return request({
    url: `/ent/account/get_position_select`,
    method: 'get'
  })
}

//新增企业账号
export function accountCreate(data) {
  return request({
    url: `/ent/account/create`,
    method: 'post',
    data
  })
}

//批量员工升级企业账号
export function batchUpdate(data) {
  return request({
    url: `/ent/account/batch_update`,
    method: 'post',
    data
  })
}

//获取企业可升级账号的员工
export function getEmp(params) {
  return request({
    url: `/ent/account/get_emp_select`,
    method: 'get',
    params
  })
}

//启用-停用账号
export function enableAccount(params) {
  return request({
    url: `/ent/account/enable_account`,
    method: 'get',
    params
  })
}

//重置密码
export function resetPassword(params) {
  return request({
    url: `/ent/account/reset_password`,
    method: 'get',
    params
  })
}

//修改企业账号职位
export function modifyPosition(params) {
  return request({
    url: `/ent/account/modify_position`,
    method: 'get',
    params
  })
}

//收回权限
export function removeAccount(params) {
  return request({
    url: `/ent/account/remove_account`,
    method: 'get',
    params
  })
}

//离职
export function removeEmp(params) {
  return request({
    url: `/ent/account/remove_emp`,
    method: 'get',
    params
  })
}
