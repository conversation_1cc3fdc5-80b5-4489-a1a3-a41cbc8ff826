import EmployeeInfo from './employeeInfo'

export default {
  components: {
    EmployeeInfo
  },
  data() {
    return {
      formConfigure: {
        descriptors: {
          manner: {
            form: 'radio',
            label: '创建方式',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择创建方式'
              }
            ],
            options: [
              {
                label: '新增',
                value: 0
              },
              {
                label: '员工升级',
                value: 1
              }
            ],
          },
          name: {
            form: 'input',
            label: '姓名',
            hidden: false,
            rule: [
              {
                required: true,
                type:'string',
                message: '请输入姓名'
              }
            ],
            attrs: {
              maxlength: 20
            }
          },
          phoneNumber: {
            form: 'input',
            label: '手机号',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入手机号'
              },
              {
                validator: 'validatePhone'
              }
            ]
          },
          sex: {
            form: 'radio',
            label: '性别',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择创性别'
              }
            ],
            options: [],
          },
          education: {
            form: 'select',
            label: '学历',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message:'请选择学历'
              }
            ],
            options: []
          },
          empType: {
            form: 'select',
            label: '员工类型',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message:'请选择员工类型'
              }
            ],
            options: []
          },
          position: {
            form: 'select',
            label: '职位',
            hidden: false,
            rule: [
              {
                required: true,
                type: 'number',
                message:'请选择职位'
              }
            ],
            options: []
          },
          employee: {
            form: 'component',
            label: '员工信息',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择员工信息'
              }
            ],
            render: () => {
              return (
                <EmployeeInfo
                  empTypeOptions={this.empTypeOptions}
                  positionOptions={this.positionOptions}
                  ref="employeeInfo"
                  v-model={this.formModel.employee}
                />
              )
            }
          },
          create: {
            form: 'checkbox',
            label: '创建同时',
            hidden: false,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'array',
                message:'请选择创建同时'
              }
            ],
            options: [
              {
                label: '自动设定密码',
                value: 0
              },
              {
                label: '认证为在职员工',
                value: 1
              }
            ]
          },
          changePassword: {
            form: 'checkbox',
            hidden: true,
            label: '创建同时',
            rule: [
              {
                required: true,
                type: 'boolean',
                message:'请选择创建同时'
              }
            ],
            options: [
              {
                label: '重置登录密码',
                value: true
              },
            ],
          },
        }
      },
      formConfigurePosition: {
        labelWidth: '80px',
        descriptors: {
          name: {
            form: 'input',
            label: '姓名',
            disabled: true,
            rule: [
              {
                required: true,
                type:'string',
                message: '请输入姓名'
              }
            ],
            attrs: {
              maxlength: 20
            }
          },
          phoneNumber: {
            form: 'input',
            label: '手机号',
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入手机号'
              },
              {
                validator: 'validatePhone'
              }
            ]
          },
          position: {
            form: 'select',
            label: '职位',
            rule: [
              {
                required: true,
                type: 'number',
                message:'请选择职位'
              }
            ],
            options: []
          },
        }
      },
    }
  }
}
