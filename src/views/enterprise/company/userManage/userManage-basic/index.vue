<template>
  <div class="enter-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="用户管理"
      desc="新增或将在职员工升级为企业账号，不同企业职位具备不同的权限范围"
      :img="require('./images/search.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper p-t-24">
      <module-list ref="ModuleList"  :api-fn="getManager">
        <template slot="right">
          <el-button
            v-permission="routeButtonsPermission.ADD_ENT_ACCOUNT"
            type="primary"
            @click="visible = true;createStep = 1"
          >
            {{ routeButtonsTitle.ADD_ENT_ACCOUNT }}
          </el-button>
        </template>
        <template slot-scope="scope">
          <div class="user-card-container">
            <div class="user-card" v-for="user in
             scope.data" :key="user.id">
              <div class="user-info">
                <div class="flex justify-content-between m-b-24">
                  <div class="title">
                    {{ user.name }}
                  </div>
                  <div class="phone">
                    {{ user.phone }}
                  </div>
                </div>
                <div class="flex align-items-center justify-content-between m-b-12">
                  <div class="w-50">
                    <span class="label">性别: </span>
                    <span class="value">{{ user.sexStr }}</span>
                  </div>
                  <div class="w-50">
                    <span class="label">职位: </span>
                    <span  class="value">{{ user.positionStr }}</span>
                  </div>
                </div>
                <div class="flex align-items-center justify-content-between m-b-28">
                  <div class="w-50">
                    <span class="label">学历: </span>
                    <span class="value">{{ user.educationStr }}</span>
                  </div>
                  <div class="toggle w-50">
                    <span class="label">启用:</span>
                    <el-switch
                      :value="user.positionFlag"
                      active-color="#ED7B2F"
                      inactive-color="#dcdcdc"
                      @change="changeSwitch(user)"
                    />
                  </div>
                </div>
              </div>
              <div class="user-actions">
                <el-button
                  v-permission="routeButtonsPermission.MOVED_OUT"
                  size="small"
                  type="default"
                  class="btn-small"
                  @click="handleRemove(user)"
                >
                  {{ routeButtonsTitle.MOVED_OUT }}
                </el-button>
                <el-button
                  v-permission="routeButtonsPermission.POSITION_ADJUST"
                  size="small"
                  type="primary"
                  class="btn-large"
                  @click="handlePosition(user)"
                >
                  {{ routeButtonsTitle.POSITION_ADJUST }}
                </el-button>
                <el-button
                  v-permission="routeButtonsPermission.RESET_PASSWORD"
                  size="small"
                  type="warning"
                  class="btn-large"
                  @click="handleResetPassword(user.id)"
                >
                  {{ routeButtonsTitle.RESET_PASSWORD }}
                </el-button>
              </div>
            </div>
          </div>

        </template>
      </module-list>
    </div>

    <FeedBackCmp
      re="feedBackCmp"
      :title="title"
      :haveOperation="isPwsStep"
      :visible.sync="feedBackVisible"
      @confirm="confirmCmp"
    >
      <div v-if="feedBackCmpType === 1" class="color-info font-size-14">确认禁用该企业账号？禁用后当前账号无法登录企业端</div>
      <div v-if="feedBackCmpType === 2" class="color-info font-size-14">
        <div v-if="pwsStep === 1">
          确认重置密码？
        </div>
        <div v-else class="color-info font-size-14">
          密码已重置，重置后的密码为 <span class="color-primary pointer" v-copy="psw"></span>
        </div>
      </div>
      <div v-if="feedBackCmpType === 3">
        <driven-form
          v-if="feedBackVisible"
          ref="driven-form-position"
          v-model="formModelPosition"
          :formConfigure="formConfigurePosition"
        />
        <div class="color-info font-size-14">
          职位调整后，当前账号在企业端的操作权限相应调整
        </div>
      </div>
      <div v-if="feedBackCmpType === 4">
        <div class="color-info font-size-14 m-b-24">
          <p class="m-b-12">请选择移出当前企业账号的方式：</p>
          <p class="m-b-8">1、收回权限：降级为员工账号，不具备企业端登录使用权限</p>
          <p class="m-b-8">2、标记离职：降级为游客账号，无法查看获取企业信息</p>
        </div>
        <div class="w-100 flex justify-content-end gap-12">
          <el-button @click="feedBackVisible = false">取 消</el-button>
          <el-button type="danger" @click="revokePermissions">标记离职</el-button>
          <el-button type="primary"  @click="markDepartures">收回权限</el-button>
        </div>
      </div>
    </FeedBackCmp>

    <dialog-cmp
      :title="titleCom"
      :visible.sync="visible"
      width="760px"
      :haveOperation="createStep ===1"
      @confirmDialog="confirmDialog"
    >
      <div v-if="createStep === 1">
        <div>
          <el-alert class="m-b-24" type="warning" :closable="false" show-icon>
            <template>
              <div>
                <div>
                  1、员工升级企业账号后，可登录企业端查看和操作企业运营信息
                </div>
                <div class="m-t-8 m-b-8">2、不同职位具备不同的功能和通知权限</div>
                <div>3、员工离职或修改认证企业后企业账号自动注销</div>
              </div>
            </template>
          </el-alert>
          <driven-form
            v-if="visible"
            ref="driven-form"
            v-model="formModel"
            :formConfigure="formConfigure"
          />
        </div>
      </div>
      <div v-else>
        <div class="color-info m-b-24 font-size-14">已成功添加企业账号：</div>
        <el-table
          :data="tableDataCreate"
          border
          class="w100 m-b-24">
          <el-table-column
            prop="username"
            label="账号"
            >
          </el-table-column>
          <el-table-column
            prop="password"
            label="密码"
            >
          </el-table-column>
        </el-table>
        <div class="color-primary font-size-14 pointer" @click="copy">
          复制全部账号密码
        </div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import {
  getManager,
  getEmployeeType,
  getSex,
  getEducation,
  getPosition,
  accountCreate,
  batchUpdate,
  enableAccount,
  resetPassword, modifyPosition, removeAccount, removeEmp
} from './api'
import Descriptors from './descriptor'
import ModuleList from '@/components/Lateral/ModuleList/index.vue'
import FeedBackCmp from '../FeedBackCmp/index'

export default {
  name: 'UserManageBasic',
  mixins: [Descriptors],
  components: {
    ModuleList,
    ModuleHeader,
    FeedBackCmp
  },
  data() {
    return {
      getManager,
      title: '',
      feedBackVisible: false,
      visible: false,
      formModel: {
        changePassword: true,
        create: [0, 1],
        manner: 0,
        sex: 1
      },
      empTypeOptions: [],
      positionOptions: [],
      tableDataCreate: [],
      formModelPosition:{
        name: '',
        phoneNumber: '',
        position: undefined,
      },
      feedBackId: '',
      feedBackCmpType: 1,
      pwsStep: 1,
      createStep: 1,
      psw: '',
      haveOperation: true,
    }
  },
  computed: {
    isPwsStep() {
      return this.pwsStep === 1
    },
    titleCom() {
      if (this.createStep === 1){
        return '创建企业账号'
      }else {
        return '成功创建'
      }
    },
  },
  watch: {
    visible(val) {
      if(val) {
        this.formModel= {
          changePassword: true,
            create: [0, 1],
            manner: 0,
            sex: 1
        }
      }
    },
    'formModel.manner': {
      handler(val) {
        this.mannerChange(val)
      },
    }
  },
  mounted() {
    this.getEmployeeType()
    this.getSex()
    this.getEducation()
    this.getPosition()
  },
  methods: {
    changeSwitch(row) {
      const originalValue = row.positionFlag;

      const str = originalValue
        ? '确认禁用该企业账号？禁用后当前账号无法登录企业端'
        : '确认启用该企业账号？启用后当前账号可登录企业端';

      this.$confirm(str, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enableAccount({ id: row.id }).then(() => {
          this.$set(row, 'positionFlag', !originalValue);
        }).catch(() => {
          this.$message.error('操作失败，请重试');
        });
      }).catch(() => {
        this.$set(row, 'positionFlag', originalValue);
      });
    },
    revokePermissions(){
      removeEmp({id: this.feedBackId}).then(() => {
        this.feedBackVisible = false
        this.$refs.ModuleList.refresh()
        this.$message.success('标记离职成功')
      })
    },
    markDepartures() {
      removeAccount({id: this.feedBackId}).then(() => {
        this.feedBackVisible = false
        this.$refs.ModuleList.refresh()
        this.$message.success('收回权限成功')
      })
    },
    copy() {
      const textArea = document.createElement('textarea');
      textArea.value = this.tableDataCreate.map(item => {
        return `账号: ${item.username}  密码: ${item.password}`;
      }).join('\n\n');
      document.body.appendChild(textArea);

      textArea.select();
      textArea.setSelectionRange(0, textArea.value.length);

      const successful = document.execCommand('copy');
      if (successful) {
        this.$message.success('复制成功！');
      } else {
        this.$message.error('复制失败！');
      }

      document.body.removeChild(textArea);
    },
    handleRemove({id}) {
      this.pwsStep++
      this.feedBackCmpType = 4
      this.feedBackId = id
      this.title = '移出账号'
      this.feedBackVisible = true
    },
    handlePosition({id, position,name,phone}){
      this.pwsStep = 1
      this.title = '职位调整'
      this.feedBackCmpType = 3
      this.feedBackId = id
      this.feedBackVisible = true
      this.formModelPosition= {
        position,
        name,
        phoneNumber: phone,
      }
    },
    handleResetPassword(id) {
      this.pwsStep = 1
      this.feedBackCmpType = 2
      this.feedBackId = id
      this.title = '重置密码'
      this.feedBackVisible = true
    },
    mannerChange(val) {
      const showForVal0 = [
        'phoneNumber', 'sex', 'education', 'empType', 'position', 'create', 'name'
      ];

      const hideForVal0 = [
        'changePassword', 'employee'
      ];

      const showForVal1 = [
        'changePassword', 'employee'
      ];

      const hideForVal1 = [
        'phoneNumber', 'sex', 'education', 'empType', 'position', 'create', 'name'
      ];

      const resetFields = (fields, hidden) => {
        fields.forEach(field => {
          this.formConfigure.descriptors[field].hidden = hidden;
        });
      };

      if (val === 0) {
        resetFields(showForVal0, false);
        resetFields(hideForVal0, true);
        this.$set(this.formModel, 'create', [0, 1])
        this.$set(this.formModel, 'sex', 1)
      } else if (val === 1) {
        resetFields(showForVal1, false);
        resetFields(hideForVal1, true);
      } else {
        resetFields(showForVal1, false);
        resetFields(hideForVal0, true);
      }

    },
    getPosition() {
      getPosition().then(res => {
        this.positionOptions = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
        this.formConfigure.descriptors.position.options = this.positionOptions
        this.formConfigurePosition.descriptors.position.options = this.positionOptions
      })
    },
    getEducation() {
      getEducation().then(res => {
        this.formConfigure.descriptors.education.options = res.map(item => {
          return {
            label: item.label,
            value: item.id
          }
        })
      })
    },
    getSex() {
      getSex().then(res => {
        this.formConfigure.descriptors.sex.options = res.map(item => {
          return {
            label: item.label,
            value: Number(item.value)
          }
        })
      })
    },
    getEmployeeType() {
      getEmployeeType().then(res => {
        this.empTypeOptions = res.map(item => {
          return {
            label: item.label,
            value: item.id
          }
        })
        this.formConfigure.descriptors.empType.options = this.empTypeOptions
      })
    },
   async confirmCmp() {
      const obj = {
        2:resetPassword,
        3:modifyPosition
      }
      const data = { id:this.feedBackId }
      if (this.feedBackCmpType === 3){
        const valid = await new Promise((resolve) => {
          this.$refs['driven-form-position'].validate(resolve);
        });
        if (!valid) return

        data.position = this.formModelPosition.position;
      }
      obj[this.feedBackCmpType](data).then((res) => {
        if (this.feedBackCmpType === 2){
          this.psw = res.password || ''
          this.pwsStep++
          this.title = '新密码'
        }else {
          this.subsequent()
        }
      })
    },
    subsequent(){
      this.$message.success(this.title + '成功')
      this.$refs.ModuleList.refresh()
      this.feedBackVisible = false
    },
    isValidId(positioned) {
      return positioned !== null && positioned !== undefined && positioned !== '';
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return;

        const { manner, changePassword, employee = [] } = this.formModel;

        const positioned = employee.map(item => ({
          id: item.id,
          position: item.positioned
        }));

        if (this.hasInvalidPosition(positioned)) {
          this.$message.error(`未选择职位，请先选择职位`);
          return;
        }

        const data = {
          changePassword,
          list: positioned
        };

        const action = this.getActionByManner(manner, data);
        this.executeAction(action);
      });
    },
    hasInvalidPosition(positioned) {
      return positioned.some(item => !this.isValidId(item.position));
    },
    getActionByManner(manner, data) {
      const actions = {
        0: {
          text: '创建成功',
          api: accountCreate,
          data: this.formModel,
          createSuccessData: ({ username, password }) => {
            if (username && password) {
              this.tableDataCreate = [{ username, password }];
            }
          }
        },
        1: {
          text: '升级成功',
          api: batchUpdate,
          data,
          createSuccessData: (res) => {
            this.tableDataCreate = res || [];
          }
        }
      };

      return actions[manner];
    },
    executeAction(action) {
      if (!action) return;

      action.api(action.data).then((res) => {
        action.createSuccessData(res);
        this.createStep++;
        this.$refs.ModuleList.refresh();
      });
    },

  }
}
</script>

<style lang="scss" scoped>
.user-card-container {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;

  .user-card {
    width: 287px;
    height: 190px;
    background: #FFFFFF;
    padding: 16px;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #FFF2E5;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.3s;

    &:hover {
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      border: 1px solid #ED7B2F;
    }

    .user-info {
      margin-bottom: 15px;

      .title{
        max-width: 154px;
        font-weight: 500;
        font-size: 16px;
        color: rgba(0,0,0,0.8);
        line-height: 22px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .phone{
        font-weight: 500;
        font-size: 16px;
        color: rgba(0,0,0,0.8);
        line-height: 22px;
      }

      .label {
        font-weight: 350;
        font-size: 12px;
        color: rgba(0,0,0,0.6);
        line-height: 20px;
      }

      .value {
        font-weight: 350;
        font-size: 12px;
        color: rgba(0,0,0,0.9);
        line-height: 20px;
      }
      .w-50 {
        width: 50%;
      }

      p {
        margin: 5px 0;
        font-size: 14px;
        color: #333;

        &:last-child {
          margin-top: 10px;
        }
      }

      .toggle {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
      }
    }

    .user-actions {
      display: flex;

      .btn-small {
        flex: 0 0 60px;
        display: flex;
        justify-content: center;
      }

      .btn-large {
        flex: 1;
        display: flex;
        justify-content: center;
      }
    }
  }
}
</style>
