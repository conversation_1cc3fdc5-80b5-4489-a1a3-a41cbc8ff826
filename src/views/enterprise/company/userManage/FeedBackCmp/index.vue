<template>
  <div>
    <dialog-cmp
      :title="title"
      :visible.sync="_visible"
      width="35%"
      :haveOperation="haveOperation"
      @confirmDialog="confirmDialog"
    >
      <div>
        <slot></slot>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
export default {
  name: 'FeedBackCmp',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '创建企业账号'
    },
    haveOperation: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    _visible: {
      get() {
        return this.visible
      },
        set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    confirmDialog() {
      this.$emit('confirm')
    }
  }
}
</script>

<style scoped lang="scss">

</style>
