<template>
  <dialog-cmp
    title="续约申请"
    :visible.sync="renewVisible"
    width="560px"
    @confirmDialog="confirmDialog"
  >
    <el-form
      :model="formModel"
      :rules="formRules"
      ref="formRef"
      label-width="80px"
    >
      <el-form-item label="选择房间" prop="roomId">
        <div class="flex">
          <el-select
            class="w100"
            v-model="formModel.roomId"
            placeholder="请选择房间"
            @change="roomChange"
          >
            <el-option
              v-for="item in rooms"
              :key="item.roomId"
              :label="item.room"
              :value="item.roomId"
            />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item label="续约时间">
        <div class="flex justify-between">
          <el-date-picker
            v-model="formModel.renewalStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开始时间"
            disabled
          ></el-date-picker>
          <span class="p-l-20 p-r-20">-</span>
          <el-date-picker
            v-model="formModel.renewalEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择结束时间"
            disabled
          ></el-date-picker>
        </div>
      </el-form-item>
      <el-form-item label="入住人" v-if="personList && personList.length">
        <el-checkbox-group v-model="formModel.personList" disabled>
          <el-checkbox
            v-for="item in personList"
            :key="item.id"
            :label="item.id"
            >{{ item.label }}</el-checkbox
          >
        </el-checkbox-group>
        <div class="occupant-tips">
          续约无法变更入住人和房间，如果需要更换请重新发起租房申请
        </div>
      </el-form-item>
      <el-form-item label="续约说明">
        <el-input
          v-model="formModel.illustrate"
          placeholder="请输入续约说明"
          type="textarea"
          :autosize="{ minRows: 4 }"
          maxlength="200"
          show-word-limit
        ></el-input>
      </el-form-item>
    </el-form>
  </dialog-cmp>
</template>

<script>
import {
  apartmentRenewal,
  getApartmentList,
  getApartmentRenewalInfo
} from '../api'

export default {
  name: 'RenewVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      renewVisible: false,
      formModel: {
        room: '',
        roomId: '',
        renewalStartTime: '',
        renewalEndTime: '',
        illustrate: '',
        personList: []
      },
      formRules: {
        roomId: [{ required: true, message: '请选择房间', trigger: 'blur' }]
      },
      rooms: [],
      personList: []
    }
  },
  watch: {
    visible(val) {
      this.renewVisible = val
      if (val) this.getApartmentList()
    },
    renewVisible(val) {
      if (!val) {
        this.formModel = this.$options.data().formModel
        this.$refs.formRef.clearValidate()
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    roomChange(val) {
      this.formModel.room = this.rooms.find(item => item.roomId === val).room
      this.getApartmentRenewalInfo(val)
    },
    getApartmentRenewalInfo(roomId) {
      getApartmentRenewalInfo({ roomId }).then(res => {
        this.formModel.renewalStartTime = res.startDate
        this.formModel.renewalEndTime = res.endDate
        this.personList = res.occupantList.map(item => {
          return {
            label: item.name,
            id: item.id
          }
        })
        this.formModel.personList = this.personList.map(item => item.id)
      })
    },
    getApartmentList() {
      getApartmentList().then(res => {
        this.rooms = res || []
      })
    },
    confirmDialog() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return false
        apartmentRenewal(this.formModel).then(() => {
          this.$message.success('续租成功')
          this.renewVisible = false
          this.$emit('update')
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.occupant-tips {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.4);
  line-height: 20px;
}
</style>
