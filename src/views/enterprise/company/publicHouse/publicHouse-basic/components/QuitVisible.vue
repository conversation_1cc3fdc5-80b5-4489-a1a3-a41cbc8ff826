<template>
  <div>
    <dialog-cmp
      title="退房申请"
      :visible.sync="quitVisible"
      width="800px"
      @confirmDialog="confirmDialog"
    >
      <el-form
        :model="formModel"
        ref="formRef"
        label-width="100px"
        :rules="formRules"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="选择房间" prop="roomInfo">
              <el-input
                v-model="formModel.roomInfo"
                placeholder="请选择房间"
                readonly
                @focus="roomsVisible = true"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="退房时间" prop="leavingDate">
              <el-date-picker
                style="width: 100%"
                v-model="formModel.leavingDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择退房时间"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="退房原因" prop="leavingReason">
          <el-input
            v-model="formModel.leavingReason"
            placeholder="请输入退房原因"
            type="textarea"
            :autosize="{ minRows: 4 }"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="银行账号" prop="leavingAccount">
              <el-input
                v-model="formModel.leavingAccount"
                placeholder="请输入银行账号"
                maxlength="50"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开户行" prop="leavingBank">
              <el-input
                v-model="formModel.leavingBank"
                placeholder="请输入开户行"
                maxlength="50"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="账户名称" prop="accountName">
          <el-input
            v-model="formModel.accountName"
            placeholder="请输入账户名称"
            maxlength="50"
          ></el-input>
        </el-form-item>
        <el-form-item label="意见和建议" prop="advice">
          <el-input
            v-model="formModel.advice"
            placeholder="请输入意见和建议"
            type="textarea"
            :autosize="{ minRows: 4 }"
            maxlength="200"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
    </dialog-cmp>

    <dialog-cmp
      title="选择房间"
      :visible.sync="roomsVisible"
      width="800px"
      @confirmDialog="roomConfirmDialog"
    >
      <div class="p-l-8 p-r-8">
        <div
          class="m-b-16 flex justify-content-between align-items-center font-size-14"
        >
          <div>
            <span class="m-r-8">切换合同</span>
            <el-select
              class="contract-option"
              v-model="contract"
              placeholder="请选择"
              @change="contractChange"
            >
              <el-option
                v-for="item in conOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="color-info">暂不支持跨合同选择房间</div>
        </div>
        <el-table
          ref="drive-table"
          :data="tableData"
          border
          :height="height"
          tooltip-effect="dark"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column prop="contractNo" label="合同编号" width="220">
          </el-table-column>
          <el-table-column prop="park" label="园区" width="200">
          </el-table-column>
          <el-table-column prop="buildingFloor" label="楼栋楼层" width="120">
          </el-table-column>
          <el-table-column prop="room" label="房号"> </el-table-column>
        </el-table>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import { apartmentReturn, getRoomList } from '../api'

export default {
  name: 'QuitVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      height: "calc('100vh', -200)",
      contract: undefined,
      conOptions: [],
      quitVisible: false,
      formModel: {
        room: '',
        roomInfo: '',
        leavingDate: '',
        leavingReason: '',
        leavingAccount: '',
        leavingBank: '',
        accountName: '',
        advice: ''
      },
      formRules: {
        roomInfo: [{ required: true, message: '请选择房间', trigger: 'blur' }],
        leavingDate: [
          { required: true, message: '请选择退房时间', trigger: 'blur' }
        ],
        leavingReason: [
          { required: true, message: '请输入退房原因', trigger: 'blur' }
        ],
        leavingAccount: [
          { required: true, message: '请输入银行账号', trigger: 'blur' },
          {
            validator(rule, value, callBack) {
              if (/^([1-9]{1})(\d{15}|\d{18}|\d{16})$/.test(value)) {
                callBack()
              } else {
                callBack('请输入正确的银行卡号')
              }
            }
          }
        ],
        leavingBank: [
          { required: true, message: '请输入开户行', trigger: 'blur' }
        ],
        accountName: [
          { required: true, message: '请输入账户名称', trigger: 'blur' }
        ],
        advice: [
          { required: true, message: '请输入意见和建议', trigger: 'blur' }
        ]
      },
      rooms: [],
      tableData: [],
      multipleSelection: [],
      roomsVisible: false
    }
  },
  watch: {
    visible(val) {
      this.quitVisible = val
    },
    quitVisible(val) {
      if (!val) {
        this.formModel = this.$options.data().formModel
        this.$refs.formRef.clearValidate()
        this.$emit('update:visible', val)
      }
    }
  },
  mounted() {
    this.getRoomList()
  },
  methods: {
    getRoomList() {
      getRoomList().then(res => {
        if (res && res.length > 0) {
          const data = res[0]
          this.contract = data.contractId
          this.tableData = data.roomList || []
          this.conOptions = res.map(item => {
            return {
              value: item.contractId,
              label: item.contractNo,
              roomList: item.roomList
            }
          })
        }
      })
    },
    contractChange(val) {
      this.conOptions.forEach(item => {
        if (item.value === val) {
          this.tableData = item.roomList
        }
      })
    },
    roomConfirmDialog() {
      if (this.multipleSelection.length === 0) {
        this.formModel.roomInfo = ''
        this.$refs.formRef.validateField('roomInfo')
        return (this.roomsVisible = false)
      }

      const remainingRooms =
        this.tableData.length - this.multipleSelection.length
      if (remainingRooms === 0) {
        this.formModel.roomInfo = '全部退租'
      } else {
        this.formModel.roomInfo = `部分退租（${this.multipleSelection.length}）`
      }

      if (this.formModel.roomInfo.length > 0) {
        this.$refs.formRef.clearValidate('roomInfo')
      } else {
        this.$refs.formRef.validateField('roomInfo')
      }

      this.roomsVisible = false
    },
    handleSelectionChange(val) {
      if (val.length > 0) {
        this.multipleSelection = val?.map(item => item.roomId)
      } else {
        this.multipleSelection = []
      }
    },
    confirmDialog() {
      this.$refs.formRef.validate(valid => {
        if (!valid) return false
        const params = {
          ...this.formModel,
          contractId: this.contract,
          room: this.multipleSelection
        }
        apartmentReturn(params).then(() => {
          this.$message.success('提交成功')
          this.quitVisible = false
          this.$emit('update')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.contract-option {
  width: 265px;
}
</style>
