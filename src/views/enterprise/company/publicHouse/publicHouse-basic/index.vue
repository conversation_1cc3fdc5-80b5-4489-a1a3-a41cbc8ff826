<template>
  <div>
    <!-- 头部 -->
    <module-header
      type="primary"
      title="公租房申请"
      desc="为企业员工在线申请公租房"
      :img="require('./images/search.png')"
      :imgOpacity="1"
    >
      <template slot="title-right">
        <el-dropdown @command="handleCommand" class="m-r-32">
          <el-button type="primary">
            <span>申请<i class="el-icon-arrow-down el-icon--right"></i></span>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="1">租房申请</el-dropdown-item>
            <el-dropdown-item :command="2">续约申请</el-dropdown-item>
            <el-dropdown-item :command="3">退房申请</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
    </module-header>
    <div class="lateral-wrapper p-t-24">
      <basic-tab
        :current="extralQuerys.status"
        :tabs-data="tabsData"
        :disabled="disabled"
        @tabsChange="tabsChange"
      />
      <drive-table
        v-if="tabsData.length"
        ref="driveTable"
        :columns="tableColumn"
        :api-fn="getApartmentPage"
        :extral-querys="extralQuerys"
        @getTotal="disabled = false"
      />
    </div>
    <dialog-cmp
      title="选择申请主体"
      :visible.sync="visibleDialog"
      width="560px"
      :haveOperation="false"
    >
      <div class="apply-select flex justify-between">
        <div class="apply-select-item">
          <div class="apply-item-wrapper">
            <div class="select-item-title">个人申请</div>
            <div class="select-item-tips">个人公租房申请办理入口</div>
          </div>
          <el-button type="primary" class="m-t-16" @click="personApply"
            >去申请</el-button
          >
        </div>
        <div class="apply-select-item ent-select-item">
          <div class="apply-item-wrapper">
            <div class="select-item-title">企业/单位申请</div>
            <div class="select-item-tips">企业及单位公租房申请办理入口</div>
          </div>
          <el-button type="primary" class="m-t-16" @click="enterpriseApply"
            >去申请</el-button
          >
        </div>
      </div>
    </dialog-cmp>
    <!-- 续约申请 -->
    <renew-visible :visible.sync="renewVisible" @update="getApplyPage" />
    <!-- 退房申请 -->
    <quit-visible :visible.sync="quitVisible" @update="getApplyPage" />
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab'
import ColumnMixin from './column'
import { getAllEnum, getApartmentPage } from './api'
import { mapGetters } from 'vuex'
import RenewVisible from './components/RenewVisible'
import QuitVisible from './components/QuitVisible'

export default {
  name: 'PublicHouse',
  components: {
    QuitVisible,
    RenewVisible,
    BasicTab,
    ModuleHeader
  },
  mixins: [ColumnMixin],
  data() {
    return {
      getApartmentPage,
      extralQuerys: {
        status: 0
      },
      tabsData: [],
      disabled: false,
      applyType: 1, // 申请类型：1：租房申请；2：续约申请；3：退房申请
      visibleDialog: false,
      renewVisible: false,
      quitVisible: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.getStatus()
  },
  methods: {
    getApplyPage() {
      this.$refs.driveTable.refreshTable()
    },
    // 个人入口
    personApply() {
      window.open(this.userInfo.website + '/publicHouse/applyCenter', '_blank')
    },
    // 企业入口
    enterpriseApply() {
      this.$router.push('/company/manage/publicHouseCreate')
    },
    handleCommand(val) {
      this.applyType = val
      if (val === 1) {
        this.visibleDialog = true
      } else if (val === 2) {
        this.renewVisible = true
      } else {
        this.quitVisible = true
      }
    },
    detailClick(row) {
      const type = row.type
      const paths = ['/applyDetail', '/renewDetail', '/quitHouseDetail']
      this.$router.push({
        path: `/company/manage${paths[type - 1]}`,
        query: {
          id: row.id
        }
      })
    },
    typeStyle(status) {
      if (status === 0) return 'color-primary'
      if (status === 1) return 'color-warning'
      if (status === 2) return 'color-success'
      if (status === 3) return 'color-danger'
      return ''
    },
    getStatus() {
      getAllEnum({ type: 'status' }).then(res => {
        const list = res || []
        this.tabsData = list
        if (list && list.length) {
          this.extralQuerys.status = list[0].value
        }
      })
    },
    tabsChange(val) {
      this.disabled = true
      this.extralQuerys.status = val
      this.$refs.driveTable?.refreshResetTable()
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.title) {
  h2 {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
.apply-select {
  .apply-select-item {
    width: 240px;
    height: 240px;
    border-radius: 3px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.3);
    .apply-item-wrapper {
      width: 120px;
      height: 120px;
      margin: 36px auto 0;
      display: flex;
      flex-direction: column;
      justify-content: end;
      background: url('./images/person-bg.png');
      .select-item-title {
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.9);
        text-align: center;
      }
      .select-item-tips {
        font-size: 12px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.4);
        text-align: center;
        margin-top: 8px;
        white-space: nowrap;
        width: min-content;
        margin-left: -6px;
      }
    }
    &.ent-select-item {
      background: rgba(213, 226, 255, 0.3);
      .apply-item-wrapper {
        background: url('./images/ent-bg.png');
        .select-item-tips {
          margin-left: -24px;
        }
      }
    }
  }
}
</style>
