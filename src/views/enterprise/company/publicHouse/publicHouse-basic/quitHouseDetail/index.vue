<template>
  <div class="w100">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="退房详情"
      desc="为企业员工在线退房"
      :img="require('../images/search.png')"
      :imgOpacity="1"
    >
      <template slot="title-right">
        <el-button class="status-btn" :type="typeStyle(detailInfo.status)">{{
          detailInfo.statusStr
        }}</el-button>
      </template>
    </module-header>
    <div class="quit-house-wrapper">
      <el-row class="quit-house-table-wrapper">
        <el-row>
          <el-col :span="4">退租类型</el-col>
          <el-col :span="8">{{ detailInfo.leaseType }}</el-col>
          <el-col :span="4">退房日期</el-col>
          <el-col :span="8">{{ detailInfo.leavingDate }}</el-col>
          <el-col :span="4">退房原因</el-col>
          <el-col :span="20">{{ detailInfo.leavingReason }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="4">银行账号</el-col>
          <el-col :span="8">{{ detailInfo.leavingAccount }}</el-col>
          <el-col :span="4">开户行</el-col>
          <el-col :span="8">{{ detailInfo.leavingBank }}</el-col>
          <el-col :span="4">账户名称</el-col>
          <el-col :span="20">{{ detailInfo.accountName }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="4">意见和建议</el-col>
          <el-col :span="20">{{ detailInfo.advice || '无' }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="4">申请人</el-col>
          <el-col :span="8">{{ detailInfo.applyer }}</el-col>
          <el-col :span="4">申请时间</el-col>
          <el-col :span="8">{{ detailInfo.applyTime }}</el-col>
          <el-col :span="4">申请主体</el-col>
          <el-col :span="20">{{ detailInfo.applyTypeStr }}</el-col>
        </el-row>
      </el-row>
      <div>
        <div class="m-t-12 m-b-12">退租房间</div>
        <el-table
          ref="drive-table"
          :data="detailInfo.roomDetails || []"
          border
          height="260"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column prop="contractNo" label="合同编号">
          </el-table-column>
          <el-table-column prop="address" label="园区楼栋楼层">
          </el-table-column>
          <el-table-column prop="room" label="房号"> </el-table-column>
          <el-table-column prop="area" label="房间面积(m²)"> </el-table-column>
          <el-table-column prop="executionArea" label="合同执行面积(m²)">
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { typeStyle } from '../configure'
import { getReturnDetail } from '../api'
import ModuleHeader from '@/components/Lateral/ModuleHeader'

export default {
  name: 'QuitHouseDetail',
  components: { ModuleHeader },
  data() {
    return {
      detailInfo: {}
    }
  },
  created() {
    this.getReturnDetail()
  },
  methods: {
    typeStyle(status) {
      return typeStyle(status)
    },
    getReturnDetail() {
      const id = this.$route.query.id
      if (!id) return
      getReturnDetail({ id }).then(res => {
        this.detailInfo = res || {}
      })
    }
  }
}
</script>

<style scoped lang="scss">
.status-btn {
  cursor: default;
  margin-right: 18px;
}
.quit-house-wrapper {
  width: 1200px;
  margin: 0 auto;
  padding-top: 24px;
  .quit-house-table-wrapper {
    border-left: 1px solid;
    border-top: 1px solid;
    border-color: #e7e7e7;
  }
}
:deep(.title) {
  h2 {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
:deep(.el-row) {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
:deep(.el-col) {
  padding: 14px 25px;
  line-height: 22px;
  font-size: 14px;
  color: #666;
  background: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
}
:deep(.el-col-4) {
  color: #999;
  background: rgba(231, 231, 231, 0.5);
}
</style>
