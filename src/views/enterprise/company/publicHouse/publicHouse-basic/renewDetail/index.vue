<template>
  <div class="w100">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="续约详情"
      desc="为企业员工在线续约"
      :img="require('../images/search.png')"
      :imgOpacity="1"
    >
      <template slot="title-right">
        <el-button class="status-btn" :type="typeStyle(detailInfo.status)">{{
          detailInfo.statusStr
        }}</el-button>
      </template>
    </module-header>
    <div class="renew-wrapper">
      <el-row class="renew-table-wrapper">
        <el-row>
          <el-col :span="4">续房房间</el-col>
          <el-col :span="8">{{ detailInfo.room }}</el-col>
          <el-col :span="4">续约时间</el-col>
          <el-col :span="8"
            >{{ detailInfo.renewalStartTime }} -
            {{ detailInfo.renewalEndTime }}</el-col
          >
          <el-col :span="4">入住人</el-col>
          <el-col :span="20">{{ detailInfo.occupant }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="4">申请人</el-col>
          <el-col :span="8">{{ detailInfo.applyer }}</el-col>
          <el-col :span="4">申请时间</el-col>
          <el-col :span="8">{{ detailInfo.applyTime }}</el-col>
          <el-col :span="4">申请主体</el-col>
          <el-col :span="20">{{ detailInfo.applyTypeStr }}</el-col>
        </el-row>
        <el-row>
          <el-col :span="4">续约说明</el-col>
          <el-col :span="20">{{ detailInfo.illustrate || '无' }}</el-col>
        </el-row>
      </el-row>
    </div>
  </div>
</template>

<script>
import { getRenewalDetail } from '../api'
import { typeStyle } from '../configure'
import ModuleHeader from '@/components/Lateral/ModuleHeader'
export default {
  name: 'RenewDetail',
  components: { ModuleHeader },
  data() {
    return {
      detailInfo: {}
    }
  },
  created() {
    this.getRenewalDetail()
  },
  methods: {
    typeStyle(status) {
      return typeStyle(status)
    },
    getRenewalDetail() {
      const id = this.$route.query.id
      if (!id) return
      getRenewalDetail({ id }).then(res => {
        this.detailInfo = res || {}
      })
    }
  }
}
</script>

<style scoped lang="scss">
.status-btn {
  cursor: default;
  margin-right: 18px;
}
.renew-wrapper {
  width: 1200px;
  margin: 0 auto;
  padding-top: 24px;
  .renew-table-wrapper {
    border-left: 1px solid;
    border-top: 1px solid;
    border-color: #e7e7e7;
  }
}
:deep(.title) {
  h2 {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
:deep(.el-row) {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
:deep(.el-col) {
  padding: 14px 25px;
  line-height: 22px;
  font-size: 14px;
  color: #666;
  background: #fff;
  border-bottom: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
}
:deep(.el-col-4) {
  color: #999;
  background: rgba(231, 231, 231, 0.5);
}
</style>
