import request from '@/utils/request'

// 获取字典
export function getAllEnum(params) {
  return request({
    url: `/housing/ent/apartment/get_all_enum_list`,
    method: 'get',
    params
  })
}
// 获取申请列表
export function getApartmentPage(params) {
  return request({
    url: `/housing/ent/apartment/page`,
    method: 'get',
    params
  })
}
// 获取当前登录人企业
export function getUserEnt() {
  return request({
    url: `/housing/ent/apartment/get_by_user`,
    method: 'get'
  })
}
// 获取公寓园区
export function getApartment() {
  return request({
    url: `/housing/park/get_apartment_park`,
    method: 'get'
  })
}
// 获取材料
export function getApartmentMaterial() {
  return request({
    url: `/gw/aparment/information/display/get`,
    method: 'get'
  })
}
// 提交租房申请
export function apartmentSubmit(data) {
  return request({
    url: `/housing/ent/apartment/submit`,
    method: 'post',
    data
  })
}
// 续约房源公寓申请
export function apartmentRenewal(data) {
  return request({
    url: `/housing/ent/apartment/renewal`,
    method: 'post',
    data
  })
}
// 获取登录人所有申请公寓
export function getApartmentList() {
  return request({
    url: `/housing/ent/apartment/get_apply_aparment`,
    method: 'get'
  })
}

// 续约前获取房间、入住人信息
export function getApartmentRenewalInfo(params) {
  return request({
    url: `/housing/ent/apartment/renewal_before`,
    method: 'get',
    params
  })
}
// 退房房源公寓申请
export function apartmentReturn(data) {
  return request({
    url: `/housing/ent/apartment/return`,
    method: 'post',
    data
  })
}
// 获取租房申请详情
export function getApartmentDetail(params) {
  return request({
    url: `/housing/ent/apartment/get`,
    method: 'get',
    params
  })
}
// 续约详情
export function getRenewalDetail(params) {
  return request({
    url: `/housing/ent/apartment/get_renewal`,
    method: 'get',
    params
  })
}
// 退房详情
export function getReturnDetail(params) {
  return request({
    url: `/housing/ent/apartment/get_return`,
    method: 'get',
    params
  })
}

export function getRoomList(params) {
  return request({
    url: `/housing/ent/apartment/get_room_list`,
    method: 'get',
    params
  })
}
