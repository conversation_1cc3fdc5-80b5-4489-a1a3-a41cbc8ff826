<template>
  <div class="apply-container">
    <div class="apply-wrapper">
      <apply-directory
        style="width: 17%"
        ref="directory"
        containerRef="createContainer"
      />
      <el-scrollbar id="el-scroll" class="apply-content" ref="elScrollbar">
        <create-container style="width: 79%" ref="createContainer" />
        <create-notice style="width: 21%" ref="createNotice" />
      </el-scrollbar>
    </div>
    <create-footer ref="createFooter" />
  </div>
</template>

<script>
import ApplyDirectory from '../applyDirectory'
import CreateNotice from './createNotice'
import CreateFooter from './createFooter'
import CreateContainer from './createContainer'
export default {
  name: 'HouseApplyCreate',
  components: {
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    C<PERSON>Footer,
    CreateNotice,
    ApplyDirectory
  },
  provide() {
    return {
      HouseApplyCreate: this
    }
  },
  methods: {
    // dom滚动
    navPageHandle(dom) {
      this.$refs.elScrollbar.wrap.scrollTo({
        top: dom.offsetTop,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.apply-container {
  position: fixed;
  left: 0;
  top: 55px;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(234, 240, 255, 0.2) 0%,
    rgba(246, 249, 255, 0.2) 100%
  );
  .apply-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    padding-bottom: 64px;
    .apply-content {
      width: 100%;
      height: 100%;
    }
  }
}
::v-deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
    .el-scrollbar__view {
      padding: 32px 40px 64px 0;
      display: flex;
      width: 100%;
    }
  }
  .is-horizontal {
    display: none;
  }
}
</style>
