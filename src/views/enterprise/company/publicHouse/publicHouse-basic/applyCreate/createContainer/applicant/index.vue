<template>
  <div class="item-form-container">
    <h1 label="申请人" class="info-title DirectoryLabel">申请人</h1>
    <div class="flex">
      <enterprise-select
        class="DirectoryLabel flex-1"
        v-model="formData.entName"
        id="entName"
        label="所在企业（已认证的不可修改）"
        prop="entName"
        type="Select"
        rulesProp="entName"
        :rules="entRules"
      />
      <div class="flex-1 margin-left-32"></div>
    </div>
    <div
      class="flex DirectoryLabel"
      id="applyName"
      prop="applyName"
      label="申请人及联系方式"
    >
      <form-item
        class="flex-1"
        v-model="formData.applyName"
        label="申请人"
        prop="applyName"
        type="Input"
        @input="applyUserChange"
        disabled
      >
        <div class="flex text-12px mt-8px" v-if="!userInfo.acName">
          <span class="text-hex-000/40">当前账号未实名，请前往进行</span>
          <el-link
            class="ml-6px text-12px"
            type="primary"
            :underline="false"
            @click="goUserInfo"
            >实名认证</el-link
          >
        </div>
      </form-item>
      <form-item
        class="flex-1 margin-left-32"
        v-model="formData.applyPhone"
        label="联系方式"
        prop=""
        type="Input"
        :rules="contactsRules('applyName', 'applyPhone', 'applyName')"
        rulesProp="applyPhone"
        @input="applyUserChange"
        ref="applyPhone"
        disabled
      ></form-item>
    </div>
  </div>
</template>

<script>
import FormItem from '../components/FormItemCom'
import RulesMixins from '../rules'
import EnterpriseSelect from './EnterpriseSelect'
import { mapGetters } from 'vuex'

export default {
  name: 'Applicant',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  components: { EnterpriseSelect, FormItem },
  mixins: [RulesMixins],
  data() {
    return {
      enterpriseOptions: []
    }
  },
  inject: ['rootMain', 'HouseApplyCreate', 'CreateForm'],
  computed: {
    ...mapGetters(['userInfo'])
  },
  created() {
    this.CreateForm.formData.applyName = this.userInfo.acName
    this.CreateForm.formData.applyPhone = this.userInfo.mobile
  },
  methods: {
    goUserInfo() {
      this.rootMain.$refs.navbar.$refs.userInfo.userDialogVisible = true
    },
    // 申请人
    applyUserChange() {
      const isPass = this.formData.applyName
      this.HouseApplyCreate.$refs.directory.validatorHandle(
        'applyName',
        !!isPass
      )
    }
  }
}
</script>

<style scoped></style>
