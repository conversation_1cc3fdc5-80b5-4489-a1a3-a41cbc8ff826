<template>
  <uploader
    v-model="_value"
    :uploaderText="$attrs.uploadText"
    :uploadData="uploadData"
    :mulity="$attrs.mulity"
    :maxLength="$attrs.maxLength"
    :limit="$attrs.limit"
  />
</template>

<script>
import Uploader from '@/components/Uploader'
import { uploaderType } from '../../../configure'

export default {
  name: 'UploadCom',
  props: {
    value: {
      required: true
    }
  },
  components: { Uploader },
  data() {
    return {
      uploadData: {
        type: uploaderType
      }
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>
