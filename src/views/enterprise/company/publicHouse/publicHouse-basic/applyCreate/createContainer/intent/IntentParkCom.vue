<template>
  <el-form-item class="item-form-container" v-bind="$attrs" v-on="$listeners">
    <div class="park-container">
      <div
        class="park-item"
        v-for="item in parkSelect"
        :key="item.key"
        :class="{ active: _value === item.key }"
        @click="clickHandle(item)"
      >
        <div class="item-radio"></div>
        <img class="item-img" :src="getImg(item)" @error="defaultImg" alt="" />
        <div class="item-title">{{ item.label }}</div>
      </div>
    </div>
    <div class="park-tips">
      <span class="tips-phone" v-if="mobile">联系电话：{{ mobile }}</span>
      <el-link
        class="park-notice-text"
        type="primary"
        :underline="false"
        @click="goNoticeHandle"
      >
        <span>入住须知</span>
        <i class="el-icon-arrow-right"></i>
      </el-link>
    </div>
  </el-form-item>
</template>

<script>
import defaultEnterpriseImg from '../../../images/default-enterprise.png'
import { getApartment } from '../../../api'

export default {
  name: 'IntentParkCom',
  props: {
    value: {
      required: true
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      parkSelect: [],
      parkInfo: {}
    }
  },
  inject: ['CreateForm', 'HouseApplyCreate'],
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    mobile() {
      const item = this.parkSelect.find(item => item.key === this._value)
      return item && item.mobile
    }
  },
  mounted() {
    this.getApartment()
  },
  methods: {
    defaultImg(event) {
      let img = event.srcElement
      img.src = defaultEnterpriseImg
      img.onerror = null //防止闪图
    },
    getImg(item) {
      const { attachMap = {} } = item
      if (JSON.stringify(attachMap) === '{}' || !attachMap)
        return defaultEnterpriseImg
      if (!attachMap.parkAttach || !attachMap.parkAttach.length)
        return defaultEnterpriseImg
      return attachMap.parkAttach[0].path
    },
    // 获取园区列表
    getApartment() {
      getApartment().then(res => {
        this.parkSelect = res || []
      })
    },
    goNoticeHandle() {
      this.HouseApplyCreate.$refs.createNotice.initData(this.parkInfo)
    },
    clickHandle(item) {
      this.parkInfo = item
      this._value = item.key
      this.CreateForm.formData.parkName = item.label
      this.HouseApplyCreate.$refs.createNotice.show = false
      if (this.$attrs.prop)
        this.CreateForm.$refs.ruleForm.validateField(this.$attrs.prop)
    }
  }
}
</script>

<style scoped lang="scss">
.item-form-container {
  position: relative;
  .park-container {
    width: 100%;
    display: grid;
    justify-content: space-between;
    grid-template-columns: repeat(auto-fill, 212px);
    grid-gap: 20px 0;
    .park-item {
      width: 212px;
      height: 172px;
      background: rgba(213, 226, 255, 0.4);
      border-radius: 8px;
      padding: 4px 4px 0;
      position: relative;
      cursor: pointer;
      .item-radio {
        width: 16px;
        height: 16px;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid #dcdcdc;
        border-radius: 50%;
        position: absolute;
        left: 8px;
        top: 8px;
      }
      .item-img {
        width: 100%;
        height: 133px;
        border-radius: 4px;
        overflow: hidden;
      }
      .item-title {
        margin-top: -4px;
        width: 100%;
        font-size: 16px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.9);
        line-height: 20px;
        text-align: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      &.active {
        border: 1px solid #ed7b2f;
        .item-radio {
          border: 1px solid #ed7b2f;
          &:after {
            display: block;
            content: '';
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #0052d9;
            position: absolute;
            left: 3px;
            top: 3px;
          }
        }
      }
    }
  }
  .park-tips {
    position: absolute;
    top: -32px;
    right: 0;
    display: flex;
    align-items: center;
    font-size: 14px;
    .tips-phone {
      margin-right: 16px;
    }
  }
}
</style>
