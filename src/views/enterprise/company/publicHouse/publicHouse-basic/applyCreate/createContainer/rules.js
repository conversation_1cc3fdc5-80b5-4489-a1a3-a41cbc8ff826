export default {
  data() {
    return {
      // 工作单位
      enterpriseAddressRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            this.HouseApplyCreate.$refs.directory.validatorHandle(
              rule.field,
              !!value
            )
            if (!value) return callback(new Error(`请选择工作单位地址`))
            callback()
          },
          target: ['blur', 'change']
        }
      ],
      // 户籍所在地
      householdRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            this.HouseApplyCreate.$refs.directory.validatorHandle(
              rule.field,
              !!value
            )
            if (!value) return callback(new Error(`请选择户籍所在地`))
            callback()
          },
          target: ['blur', 'change']
        }
      ],
      // 企业校验
      entRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            this.HouseApplyCreate.$refs.directory.validatorHandle(
              rule.field,
              !!value
            )
            if (!value) return callback(new Error(`请输入所在企业`))
            callback()
          },
          target: ['blur', 'change']
        }
      ],
      // 园区校验
      parkRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            this.HouseApplyCreate.$refs.directory.validatorHandle(
              rule.field,
              !!value
            )
            if (!value) return callback(new Error(`请选择申请入住公寓`))
            callback()
          },
          target: ['blur', 'change']
        }
      ],
      // 入住人-个人月收入
      incomeRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (!value) return callback(new Error('请输入个人月收入'))
            const val = Number(value)
            if (isNaN(val)) return callback(new Error('请输入数字'))
            if (value <= 0) return callback(new Error('个人月收入不能小于0'))
            if (value > 1000000000)
              return callback(new Error('数字不能超出1000000000'))
            if (
              /(^[0-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
                +val
              )
            ) {
              callback()
            } else {
              callback(new Error('最多保留两位小数'))
            }
          },
          target: ['blur', 'change']
        }
      ],
      // 入住人-联系方式
      stayContactRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            const regPone = /^(1[3-9]\d{9}|(0\d{2,3}-\d{7,8})|(1[3584]\d{9})|(400|800)-?\d{3}-?\d{4})$/
            if (!regPone) {
              return callback(
                new Error("请输入联系电话,其中座机格式'区号-座机号码'")
              )
            } else if (!regPone.test(value)) {
              return callback(
                new Error("请输入联系电话,其中座机格式'区号-座机号码'")
              )
            } else {
              callback()
            }
          },
          target: ['blur', 'change']
        }
      ],
      // 入住人-身份证号码
      idCardRules: [
        {
          required: true,
          validator: (rule, value, callback) => {
            if (!value && value !== 0) {
              callback(new Error('请输入身份证号码'))
              return
            }
            if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(value)) {
              callback(new Error('请输入正确的身份证号码'))
            } else {
              callback()
            }
          },
          target: ['blur', 'change']
        }
      ]
    }
  },
  methods: {
    // 联系方式校验 关联父级联动字段的parentProp, prop本身,同级必填的字段sibling
    contactsRules(parentProp, prop, sibling) {
      return [
        {
          required: false,
          validator: (rule, value, callback) => {
            const name = this.formData[sibling]
            const regPone = /^(1[3-9]\d{9}|(0\d{2,3}-\d{7,8})|(1[3584]\d{9})|(400|800)-?\d{3}-?\d{4})$/
            if (!value) {
              if (name)
                this.HouseApplyCreate.$refs.directory.validatorHandle(
                  parentProp,
                  true
                )
              callback()
            } else if (!regPone.test(value)) {
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                parentProp,
                false
              )
              return callback(
                new Error("请输入正确的联系方式,其中座机格式'区号-座机号码'")
              )
            } else {
              callback()
            }
          },
          target: ['blur', 'change']
        }
      ]
    }
  }
}
