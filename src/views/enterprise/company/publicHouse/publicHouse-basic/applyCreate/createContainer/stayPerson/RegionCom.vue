<template>
  <el-form-item class="region-container" v-bind="$attrs" v-on="$listeners">
    <region-cascader v-model="_value" valueType="id" />
  </el-form-item>
</template>

<script>
import RegionCascader from '@/components/RegionCascader'
export default {
  name: 'RegionCom',
  components: { RegionCascader },
  props: {
    value: {
      required: true
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  inject: ['CreateForm', 'HouseApplyCreate'],
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.region-container {
  :deep(.el-form-item__label) {
    text-align: left;
    display: block;
    float: inherit;
  }
}
:deep(.el-input__inner) {
  background: transparent !important;
}
</style>
