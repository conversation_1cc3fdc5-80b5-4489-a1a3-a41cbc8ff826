<template>
  <div class="item-form-container">
    <h1 label="入住人" class="info-title DirectoryLabel">入住人</h1>
    <div class="tabs-container">
      <div
        class="tabs-item DirectoryLabel"
        v-for="item in stayPerson"
        :key="item.id"
        :class="{ 'is-active': item.id === activeName }"
        :id="'stayPersonModel' + item.id"
        :prop="'stayPersonModel' + item.id"
        :label="'入住人' + item.id"
        @click="activeName = item.id"
      >
        <div class="item-label">
          <span>{{ '入住人' + item.id }}</span>
          <svg-icon
            v-if="stayPerson.length > 1"
            icon-class="close"
            @click.stop="removeTab(item.id)"
          />
        </div>
      </div>
      <el-button
        type="text"
        v-if="stayPerson.length < 6"
        @click="addTab(activeName)"
      >
        <i class="el-icon-plus"></i>
        <span>添加</span>
      </el-button>
    </div>
    <el-form
      v-for="item in stayPerson"
      :key="item.id"
      v-show="item.id === activeName"
      class="stay-form"
      :model="item.stayPersonModel"
      :ref="'stayPersonForm' + item.id"
      @submit.native.prevent
    >
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.name"
          label="入住人姓名"
          prop="name"
          type="Input"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.card"
          label="身份证号码"
          prop="card"
          type="Input"
          :rules="idCardRules"
          rulesProp="card"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.phone"
          label="联系电话"
          prop="phone"
          type="Input"
          :rules="stayContactRules"
          rulesProp="phone"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.gender"
          label="性别"
          prop="gender"
          type="Radio"
          :options="genderOptions"
        ></form-item>
      </div>
      <div class="flex">
        <region-com
          class="flex-1"
          v-model="item.stayPersonModel.householdAddress"
          label="户籍所在地"
          prop="householdAddress"
          rulesProp="householdAddress"
          :rules="householdRules"
          @input="householdInput($event, item)"
        />
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.applyType"
          label="申请人类型"
          prop="applyType"
          type="Select"
          :options="applyTypeOptions"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.marriage"
          label="婚姻状况"
          prop="marriage"
          type="Select"
          rulesProp="marriage"
          :options="marriageOptions"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.politicalOutlook"
          label="政治面貌"
          prop="politicalOutlook"
          type="Select"
          :options="politicalOutlookOptions"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.school"
          label="毕业院校"
          prop="school"
          type="Input"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.graduationTime"
          label="毕业日期"
          prop="graduationTime"
          type="Date"
          dateType="date"
          valueFormat="yyyy-MM-dd"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.education"
          label="最高学历"
          prop="education"
          type="Select"
          :options="educationOptions"
        ></form-item>
        <div class="flex-1 margin-left-32"></div>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.socialSecurity"
          label="是否缴纳社保"
          prop="socialSecurity"
          type="Radio"
          :options="options"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.socialSecurityTime"
          label="社保缴纳日期"
          :prop="
            item.stayPersonModel.socialSecurity === false
              ? ''
              : 'socialSecurityTime'
          "
          type="Date"
          dateType="date"
          valueFormat="yyyy-MM-dd"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.workUnit"
          label="是否单位在高新区"
          prop="workUnit"
          type="Radio"
          :options="options"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.workType"
          label="单位类型"
          prop="workType"
          type="Select"
          :options="workTypeOptions"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.workName"
          label="单位名称"
          prop="workName"
          type="Input"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.workCode"
          label="统一社会信用代码"
          prop="workCode"
          type="Input"
        ></form-item>
      </div>
      <div class="flex">
        <region-com
          class="flex-1"
          v-model="item.stayPersonModel.enterpriseAddress"
          label="工作单位地址"
          prop="enterpriseAddress"
          rulesProp="enterpriseAddress"
          :rules="enterpriseAddressRules"
          @input="enterpriseAddressInput($event, item)"
        />
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.workDetailAddress"
          label="详细地址"
          prop="workDetailAddress"
          type="Input"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.yearLife"
          label="劳动合同签订年限"
          prop="yearLife"
          type="Date"
          dateType="daterange"
          valueFormat="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="yearLifeChange($event, item)"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.monthlyIncome"
          label="个人月收入"
          prop="monthlyIncome"
          type="Input"
          :rules="incomeRules"
          rulesProp="monthlyIncome"
        ></form-item>
      </div>
      <div class="flex">
        <form-item
          class="flex-1"
          v-model="item.stayPersonModel.privateRights"
          label="是否有私有产权房（含配偶）"
          prop="privateRights"
          type="Radio"
          :options="options"
        ></form-item>
        <form-item
          class="flex-1 margin-left-32"
          v-model="item.stayPersonModel.lessee"
          label="是否承租公房或廉租房"
          prop="lessee"
          type="Radio"
          :options="options"
        ></form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
import FormItem from '../components/FormItemCom'
import RulesMixins from '../rules'
import { getAllEnum } from '../../../api'
import RegionCom from './RegionCom'

export default {
  name: 'StayPerson',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  components: { RegionCom, FormItem },
  mixins: [RulesMixins],
  data() {
    return {
      activeName: '1',
      stayPerson: [
        {
          id: '1',
          stayPersonModel: {}
        }
      ],
      tabsKey: Math.random(),
      genderOptions: [
        { value: 1, label: '男' },
        { value: 2, label: '女' }
      ],
      applyTypeOptions: [],
      marriageOptions: [],
      politicalOutlookOptions: [],
      educationOptions: [],
      workTypeOptions: [],
      options: [
        { value: true, label: '是' },
        { value: false, label: '否' }
      ]
    }
  },
  watch: {
    stayPerson: {
      handler(val) {
        this.HouseApplyCreate.$refs.createContainer.formData.occupantList = []
        val.forEach(item => {
          this.HouseApplyCreate.$refs.createContainer.formData.occupantList.push(
            item.stayPersonModel
          )
          if (
            this.$refs[`stayPersonForm${item.id}`] &&
            this.$refs[`stayPersonForm${item.id}`].length
          ) {
            this.$refs[`stayPersonForm${item.id}`][0].validate(valid => {
              this.HouseApplyCreate.$refs.createContainer.formData[
                `stayPersonModel${item.id}`
              ] = valid ? item.stayPersonModel : []
              this.$refs[`stayPersonForm${item.id}`][0].clearValidate()
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                `stayPersonModel${item.id}`,
                !!valid
              )
            })
          }
        })
      },
      deep: true
    }
  },
  inject: ['HouseApplyCreate'],
  created() {
    this.getApplyType()
    this.getMarriage()
    this.getPoliticalOutlook()
    this.getEducation()
    this.getWorkType()
  },
  methods: {
    yearLifeChange(val, item) {
      if (!val || !val.length) {
        item.stayPersonModel.yearLifeStart = ''
        item.stayPersonModel.yearLifeEnd = ''
      } else {
        item.stayPersonModel.yearLifeStart = val[0]
        item.stayPersonModel.yearLifeEnd = val[1]
      }
    },
    enterpriseAddressInput(val, item) {
      if (!val) {
        item.stayPersonModel.workProvince = ''
        item.stayPersonModel.workCity = ''
        item.stayPersonModel.workCountry = ''
      } else {
        const arr = val.split(',')
        item.stayPersonModel.workProvince = arr[0]
        item.stayPersonModel.workCity = arr[1]
        item.stayPersonModel.workCountry = arr[2]
      }
    },
    householdInput(val, item) {
      if (!val) {
        item.stayPersonModel.domicileProvince = ''
        item.stayPersonModel.domicileCity = ''
        item.stayPersonModel.domicileCountry = ''
      } else {
        const arr = val.split(',')
        item.stayPersonModel.domicileProvince = arr[0]
        item.stayPersonModel.domicileCity = arr[1]
        item.stayPersonModel.domicileCountry = arr[2]
      }
    },
    getWorkType() {
      getAllEnum({ type: 'workType' }).then(res => {
        this.workTypeOptions = res || []
      })
    },
    getEducation() {
      getAllEnum({ type: 'education' }).then(res => {
        this.educationOptions = res || []
      })
    },
    getPoliticalOutlook() {
      getAllEnum({ type: 'politicalOutlook' }).then(res => {
        this.politicalOutlookOptions = res || []
      })
    },
    getMarriage() {
      getAllEnum({ type: 'marriage' }).then(res => {
        this.marriageOptions = res || []
      })
    },
    getApplyType() {
      getAllEnum({ type: 'applyType' }).then(res => {
        this.applyTypeOptions = res || []
      })
    },
    // 表单验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        let list = []
        this.stayPerson.forEach(item => {
          list.push(this.validItem(item))
        })
        Promise.all(list)
          .then(() => {
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    // 验证每一项
    validItem(item) {
      return new Promise((resolve, reject) => {
        if (
          this.$refs[`stayPersonForm${item.id}`] &&
          this.$refs[`stayPersonForm${item.id}`].length
        ) {
          this.$refs[`stayPersonForm${item.id}`][0].validate(valid => {
            this.HouseApplyCreate.$refs.createContainer.formData[
              `stayPersonModel${item.id}`
            ] = valid ? item.stayPersonModel : []
            this.HouseApplyCreate.$refs.directory.validatorHandle(
              `stayPersonModel${item.id}`,
              !!valid
            )
            if (valid) {
              resolve()
            } else {
              reject()
            }
          })
        }
      })
    },
    addTab() {
      this.stayPerson.push({
        id: this.stayPerson.length + 1 + '',
        stayPersonModel: {}
      })
      this.activeName = this.stayPerson.length + ''
      this.tabsKey = Math.random()
      this.directoryHandle()
    },
    removeTab(targetName) {
      let tabs = this.stayPerson
      let activeName = this.activeName
      if (activeName === targetName) {
        tabs.forEach((tab, index) => {
          if (tab.id === targetName) {
            let nextTab = tabs[index + 1] || tabs[index - 1]
            if (nextTab) {
              activeName = nextTab.id
            }
          }
        })
      }
      this.activeName = activeName
      this.stayPerson = tabs.filter(tab => tab.id !== targetName)
      this.stayPerson.forEach((item, index) => {
        if (activeName === item.id) {
          this.activeName = index + 1 + ''
        }
        item.id = index + 1 + ''
      })
      this.tabsKey = Math.random()
      this.directoryHandle()
    },
    directoryHandle() {
      this.HouseApplyCreate.$refs.directory.activeId = `stayPersonModel${this.activeName}`
      this.HouseApplyCreate.$refs.directory.initData()
    }
  }
}
</script>

<style scoped lang="scss">
.tabs-container {
  .tabs-item {
    padding: 0 20px;
    height: 40px;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    cursor: pointer;
    &:first-child {
      padding-left: 0;
    }
    &:last-child {
      padding-right: 0;
    }
    &:hover {
      color: #ed7b2f;
    }
    .item-label {
      display: flex;
      align-items: center;
      .svg-icon {
        margin-left: 5px;
        &:hover {
          background-color: #bdbdbd;
          color: #fff;
          border-radius: 50%;
        }
      }
    }
    &.is-active {
      color: #ed7b2f;
      .item-label {
        border-bottom: 2px solid #ed7b2f;
      }
    }
  }
}
.stay-form {
  margin-top: 20px;
}
</style>
