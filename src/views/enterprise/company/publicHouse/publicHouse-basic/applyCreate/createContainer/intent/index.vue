<template>
  <div class="item-form-container">
    <h1 label="意向" class="info-title DirectoryLabel">意向</h1>
    <intent-park-com
      class="DirectoryLabel"
      id="parkId"
      v-model="formData.parkId"
      label="申请入住"
      prop="parkId"
      :rules="parkRules"
    />
    <div class="flex">
      <form-item
        class="DirectoryLabel flex-1"
        v-model="formData.applyHouseType"
        id="applyHouseType"
        label="申请房型"
        prop="applyHouseType"
        type="Select"
        rulesProp="applyHouseType"
        :options="roomTypeOptions"
      ></form-item>
      <div class="flex-1 margin-left-32"></div>
    </div>
  </div>
</template>

<script>
import FormItem from '../components/FormItemCom'
import IntentParkCom from './IntentParkCom'
import RulesMixins from '../rules'
import { getAllEnum } from '../../../api'

export default {
  name: 'Intent',
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  components: { FormItem, IntentParkCom },
  mixins: [RulesMixins],
  data() {
    return {
      roomTypeOptions: []
    }
  },
  inject: ['HouseApplyCreate'],
  created() {
    this.getApartmentType()
  },
  methods: {
    // 获取申请房型
    getApartmentType() {
      getAllEnum({ type: 'apartmentType' }).then(res => {
        this.roomTypeOptions = res || []
      })
    }
  }
}
</script>

<style scoped></style>
