<template>
  <el-date-picker
    style="width: 100%; background: transparent"
    v-model="_value"
    v-bind="$attrs"
    v-on="$listeners"
    :type="$attrs.dateType || 'date'"
    :value-format="$attrs.valueFormat || 'yyyy-MM-dd'"
    :placeholder="`请选择${$attrs.label || ''}`"
  >
  </el-date-picker>
</template>

<script>
export default {
  name: 'DateCom',
  props: {
    value: {
      required: true
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  background: transparent !important;
}
:deep(.el-range-input) {
  background: transparent !important;
}
</style>
