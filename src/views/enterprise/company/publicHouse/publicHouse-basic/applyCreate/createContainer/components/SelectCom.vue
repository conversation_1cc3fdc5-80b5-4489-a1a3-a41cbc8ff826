<template>
  <el-select
    style="width: 100%"
    v-model="_value"
    :placeholder="`请选择${$attrs.label || ''}`"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
    </el-option>
  </el-select>
</template>

<script>
export default {
  name: 'SelectCom',
  props: {
    value: {
      required: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .el-input__inner {
    background: transparent;
  }
}
</style>
