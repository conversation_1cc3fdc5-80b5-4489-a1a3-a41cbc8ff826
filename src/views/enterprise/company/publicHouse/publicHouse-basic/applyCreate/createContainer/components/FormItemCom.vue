<template>
  <div ref="FormItem" class="form-item-container">
    <el-form-item
      v-bind="attrs"
      v-on="$listeners"
      class="customize-form-item"
      :class="{ 'left-form-item': $attrs.labelPosition === 'left' }"
    >
      <component
        :is="componentName"
        v-model="_value"
        v-bind="$attrs"
        v-on="$listeners"
      ></component>
      <slot />
    </el-form-item>
  </div>
</template>

<script>
import InputCom from './InputCom'
import SelectCom from './SelectCom'
import CheckboxCom from './CheckboxCom'
import RadioCom from './RadioCom'
import SwitchCom from './SwitchCom'
import UploadCom from './UploadCom'
import DateCom from './DateCom'

export default {
  name: 'FormItemCom',
  props: {
    value: {
      required: true
    }
  },
  components: {
    InputCom,
    SelectCom,
    CheckboxCom,
    RadioCom,
    SwitchCom,
    UploadCom,
    DateCom
  },
  inject: ['HouseApplyCreate'],
  computed: {
    componentName() {
      return this.$attrs.type + 'Com'
    },
    placeholder() {
      const types = ['Select', 'Checkbox', 'Radio', 'Switch', 'Date']
      if (types.includes(this.$attrs.type)) {
        return '请选择'
      }
      return '请输入'
    },
    attrs() {
      let { prop, type, label, rules, rulesProp, inputType } = this.$attrs
      let customRules = [
        {
          required: !!prop,
          validator: (rule, value, callback) => {
            if (type === 'Switch') return callback()
            if (Array.isArray(value)) {
              if (!value.length) {
                this.HouseApplyCreate.$refs.directory.validatorHandle(
                  rule.field,
                  false
                )
                return callback(new Error(`${this.placeholder + label}`))
              }
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                rule.field,
                true
              )
              callback()
            } else {
              if (!value && value !== 0 && value !== false && !!prop) {
                this.HouseApplyCreate.$refs.directory.validatorHandle(
                  rule.field,
                  false
                )
                return callback(new Error(`${this.placeholder + label}`))
              }
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                rule.field,
                true
              )
              callback()
            }
          },
          target: ['blur', 'change']
        }
      ]
      if (inputType === 'number') {
        customRules.push({
          required: !!prop,
          validator: (rule, value, callback) => {
            if (!value) {
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                rule.field,
                false
              )
              callback(new Error(`${this.placeholder + label}`))
              return
            }
            if (value <= 0) {
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                rule.field,
                false
              )
              callback(new Error(`${this.placeholder + label}不能小于或等于0`))
              return
            }
            if (
              /(^[0-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
                +value
              )
            ) {
              callback()
            } else {
              callback(new Error('最多保留两位小数'))
            }
          },
          target: ['blur', 'change']
        })
      }
      if (!rules) {
        rules = customRules
      } else {
        prop = rulesProp
      }
      return { rules, ...this.$attrs, prop }
    },
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.form-item-container {
  position: relative;
  .customize-form-item {
    margin-bottom: 24px !important;
  }
}
.left-form-item {
  display: flex;
}
.left-form-item .el-form-item__content {
  width: 100%;
}
::v-deep {
  .el-form-item__label {
    flex-shrink: 0;
    padding-right: 8px;
    color: rgba(0, 0, 0, 0.9);
    float: initial;
  }
  .left-form-item .el-form-item__content {
    width: 100%;
    position: inherit;
  }
}
</style>
