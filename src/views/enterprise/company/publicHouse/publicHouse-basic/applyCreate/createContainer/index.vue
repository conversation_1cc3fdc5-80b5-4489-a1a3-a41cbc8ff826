<template>
  <el-form
    class="form-wrapper"
    :model="formData"
    ref="ruleForm"
    @submit.native.prevent
  >
    <applicant :formData="formData" />
    <intent :formData="formData" />
    <stay-person :formData="formData" ref="stayPerson" />
    <material :formData="formData" />
  </el-form>
</template>

<script>
import RulesMixins from './rules'
import Applicant from './applicant'
import Intent from './intent'
import StayPerson from './stayPerson'
import Material from './material'

export default {
  name: 'CreateContainer',
  components: {
    Material,
    StayPerson,
    Intent,
    Applicant
  },
  mixins: [RulesMixins],
  data() {
    return {
      formData: {}
    }
  },
  provide() {
    return {
      CreateForm: this
    }
  },
  inject: ['HouseApplyCreate'],
  async mounted() {
    this.$refs.ruleForm.clearValidate()
    await this.initData()
  },
  methods: {
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.ruleForm.validate(valid => {
          if (valid) {
            resolve()
          } else {
            reject()
          }
        })
      })
    },
    async initData() {
      await this.$nextTick()
      this.$refs.ruleForm.clearValidate()
      this.HouseApplyCreate.$refs.directory.initData(true)
    }
  }
}
</script>

<style scoped lang="scss">
.form-wrapper {
  padding: 0 24px;
}
:deep(.flex) {
  display: flex;
  .flex-1 {
    flex: 1;
  }
}
:deep(.info-title) {
  height: 28px;
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.9);
  line-height: 28px;
  margin-bottom: 24px;
}
:deep(.item-form-container) {
  display: flex;
  flex-direction: column;
  margin-bottom: 24px !important;
  .el-form-item {
    display: flex;
    flex-direction: column;
    .el-form-item__label {
      display: flex;
      text-align: left;
    }
  }
  .add-btn {
    padding: 5px 16px;
    font-size: 14px;
    line-height: 20px;
    position: absolute;
    top: -32px;
    right: 0;
    .el-icon-plus {
      margin-right: 3px;
    }
  }
  .table-container {
    margin-top: 16px;
    border: 1px solid #e7e7e7;
    border-bottom: none;
    font-size: 14px;
    .warning-item {
      color: #ed7b2f;
    }
    .danger-item {
      margin-left: 20px;
    }
  }
}
:deep(.custom-item-form) {
  .el-form-item__label {
    display: block;
  }
}
:deep(.max-width-100 .input-wrapper) {
  max-width: 100% !important;
}
:deep(.margin-left-32) {
  margin-left: 32px;
}
::v-deep {
  .el-table {
    thead {
      th.el-table__cell {
        padding: 10px 0;
        .cell {
          font-weight: 400;
          color: rgba(0, 0, 0, 0.4);
          line-height: 22px;
        }
      }
    }
  }
}
</style>
