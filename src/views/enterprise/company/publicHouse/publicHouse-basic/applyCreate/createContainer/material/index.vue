<template>
  <div
    class="apply-material-container item-form-container"
    v-if="tableData && tableData.length"
  >
    <h1 label="材料" class="info-title DirectoryLabel">材料</h1>
    <div class="material-wrapper">
      <div class="material-header">
        <div class="serial-num">序号</div>
        <div class="material">相关材料</div>
        <div class="example">下载</div>
        <div class="serial-num primary-num">附件个数</div>
        <div class="operate">操作</div>
      </div>
      <div class="material-body">
        <div
          class="material-item"
          v-for="(item, index) in tableData"
          :key="item.value"
        >
          <div class="serial-num primary-num">{{ index + 1 }}</div>
          <div
            class="material DirectoryLabel"
            :id="item.value"
            :prop="item.prop"
            :label="item.label"
          >
            <el-form-item
              :prop="item.prop"
              :label="item.label"
              :rules="fileRules(item.prop)"
            >
            </el-form-item>
          </div>
          <div class="example">
            <el-link
              v-if="hasTemplate(item.attach)"
              class="example-download"
              type="primary"
              :underline="false"
              @click="downloadTemplate(item.attach)"
            >
              下载模板
            </el-link>
          </div>
          <div class="serial-num" style="padding-left: 20px">
            <el-link
              v-if="formData[item.value] && formData[item.value].length"
              type="primary"
              :underline="false"
              @click="fileClick(item.value)"
            >
              {{ formData[item.value].length }}
            </el-link>
            <el-link
              v-else
              type="primary"
              :underline="false"
              @click="fileClick(item.value)"
              >0
            </el-link>
          </div>
          <div class="operate">
            <el-link
              type="primary"
              :underline="false"
              @click="fileClick(item.value)"
              >上传附件</el-link
            >
          </div>
        </div>
      </div>
    </div>
    <basic-dialog
      custom-class="apply-material-dialog"
      :title="title"
      :visible.sync="dialogVisible"
      width="560px"
      @confirmDialog="confirmDialog"
    >
      <div class="uploader-wrapper">
        <uploader
          v-model="formData[currentValue]"
          uploaderText="上传文件"
          :uploadData="uploadData"
          :mulity="true"
          :maxLength="9"
          :limit="9"
          :key="uploaderKey"
        />
        <div class="uploader-tips">最多支持上传9个文件</div>
      </div>
      <empty-data
        v-if="!formData[currentValue] || !formData[currentValue].length"
        :width="120"
        :height="120"
        description="暂无附件，请上传文件"
      />
    </basic-dialog>
  </div>
</template>

<script>
import BasicDialog from '@/components/BasicDialog'
import Uploader from '@/components/Uploader'
import { uploaderType } from '../../../configure'
import download from '@/utils/download'
import { getApartmentMaterial } from '../../../api'

export default {
  name: 'Material',
  components: { BasicDialog, Uploader },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      uploadData: {
        type: uploaderType
      },
      tableData: [],
      dialogVisible: false,
      title: '附件详情',
      currentValue: '',
      uploaderKey: Math.random()
    }
  },
  inject: ['HouseApplyCreate', 'CreateForm'],
  watch: {
    formData: {
      handler(value) {
        const itemFile = value[this.currentValue]
        if (!itemFile) return
        this.CreateForm.$refs.ruleForm &&
          this.CreateForm.$refs.ruleForm.validateField(this.currentValue)
        this.HouseApplyCreate.$refs.directory &&
          this.HouseApplyCreate.$refs.directory.validatorHandle(
            this.currentValue,
            itemFile && itemFile.length
          )
        const apartmentFileList = []
        for (const key in value) {
          if (key.includes('material')) {
            apartmentFileList.push({
              materialsId: this.tableData.find(item => item.value === key).id,
              materialsName: this.tableData.find(item => item.value === key)
                .label,
              attach: value[key].map(item => item.id)
            })
          }
        }
        this.CreateForm.formData.apartmentFileList = apartmentFileList
      },
      deep: true
    }
  },
  mounted() {
    this.getApartmentMaterial()
  },
  methods: {
    hasTemplate(attach) {
      return !(!attach || !attach.path)
    },
    downloadTemplate(attach) {
      download.addressDownload(attach)
    },
    getApartmentMaterial() {
      getApartmentMaterial().then(res => {
        const materials = res.materialsList || []
        this.tableData = []
        materials.forEach(item => {
          const attach = item.attach || {}
          const attachIds = attach.attachIds || []
          const itemAttach = attachIds[0] || {}
          this.tableData.push({
            label: item.materialsName,
            prop: `material${item.id}`,
            value: `material${item.id}`,
            id: item.id,
            attach: itemAttach
          })
        })
        this.HouseApplyCreate.$refs.directory &&
          this.HouseApplyCreate.$refs.directory.initData(true)
      })
    },
    // 确定
    confirmDialog() {
      this.dialogVisible = false
    },
    fileClick(val) {
      this.currentValue = val
      this.uploaderKey = Math.random()
      this.dialogVisible = true
    },
    fileRules(prop) {
      return [
        {
          required: !!prop,
          validator: (rule, value, callback) => {
            if (!value || !value.length) {
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                rule.field,
                false
              )
              callback(new Error(`请上传相关材料`))
            } else {
              this.HouseApplyCreate.$refs.directory.validatorHandle(
                rule.field,
                true
              )
              callback()
            }
          },
          target: ['blur', 'change']
        }
      ]
    }
  }
}
</script>

<style scoped lang="scss">
.apply-material-container {
  margin-top: 40px;
  .material-wrapper {
    margin-top: 16px;
    border: 1px solid #e7e7e7;
    border-radius: 3px;
    font-size: 14px;
    .material-header {
      border-bottom: 1px solid #e7e7e7;
      color: rgba(0, 0, 0, 0.4);
      display: flex;
      height: 42px;
      padding: 10px 24px;
    }
    .serial-num {
      width: 80px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }
    .material {
      width: 100%;
      display: flex;
      align-items: center;
    }
    .example {
      width: 140px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      .example-download {
        margin-right: 10px;
      }
    }
    .operate {
      width: 80px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .material-body {
      .material-item {
        border-bottom: 1px solid #e7e7e7;
        display: flex;
        min-height: 50px;
        padding: 20px 24px;
        &:nth-child(2n) {
          background: #fafafa;
        }
        &:hover {
          background-color: #eee;
        }
        &:last-child {
          border-bottom: none;
        }
        .primary-num {
          color: #ed7b2f;
        }
      }
    }
  }
}
.uploader-wrapper {
  display: flex;
  position: relative;
  .uploader-tips {
    font-size: 12px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.4);
    line-height: 20px;
    position: absolute;
    left: 100px;
    top: 8px;
  }
}
::v-deep {
  .el-form-item__label {
    text-align: left;
  }
  .el-form-item {
    width: 100%;
    margin-bottom: 0 !important;
  }
}
</style>
<style lang="scss">
.apply-material-dialog {
  .close-btn {
    display: none;
  }
}
</style>
