<template>
  <el-form-item v-bind="$attrs" v-on="$listeners">
    <el-autocomplete
      v-model="_value"
      :fetch-suggestions="querySearch"
      placeholder="请输入所在企业"
      @select="handleSelect"
      :disabled="disabled"
      @input="inputChange"
    ></el-autocomplete>
  </el-form-item>
</template>

<script>
import { getUserEnt } from '../../../api'

export default {
  name: 'EnterpriseSelect',
  props: {
    value: {
      required: true
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  inject: ['CreateForm', 'HouseApplyCreate'],
  data() {
    return {
      enterpriseOptions: [],
      disabled: false
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  mounted() {
    this.getUserEnt()
  },
  methods: {
    inputChange(e) {
      const item = this.enterpriseOptions.find(row => row.value === e)
      if (item) {
        this.CreateForm.formData.entId = item.id
      } else {
        this.CreateForm.formData.entId = ''
      }
    },
    querySearch(queryString, cb) {
      const restaurants = this.enterpriseOptions
      const results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      cb(results)
    },
    createFilter(queryString) {
      return restaurant => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        )
      }
    },
    async handleSelect(item) {
      this._value = item.value
      this.CreateForm.formData.entId = item.id
      await this.$nextTick()
      if (this.$attrs.prop) {
        this.CreateForm.$refs.ruleForm.validateField(this.$attrs.prop)
      }
    },
    getUserEnt() {
      getUserEnt().then(res => {
        if (res && res.id) {
          this.disabled = true
          const params = {
            value: res.enterpriseName,
            id: res.id
          }
          this.handleSelect(params)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-autocomplete) {
  width: 100%;
  max-width: 584px;
}
:deep(.el-input__inner) {
  background: transparent;
}
</style>
