<template>
  <div class="input-container">
    <div class="input-wrapper">
      <el-input
        style="width: 100%"
        v-model="_value"
        :placeholder="`请输入${$attrs.label || ''}`"
        :type="inputType"
        :maxlength="$attrs.maxlength || 50"
        v-bind="$attrs"
        v-on="$listeners"
      >
        <div v-if="isNumber" class="suffix-wrapper" slot="suffix"></div>
      </el-input>
      <div v-if="isNumber && !isInteger" class="suffix-wrapper">
        <i class="pointer el-icon-arrow-up" @click="computeHandle('add')"></i>
        <i
          class="pointer el-icon-arrow-down"
          @click="computeHandle('subtract')"
        ></i>
      </div>
    </div>
    <div class="sort-text" v-if="$attrs.sortText">{{ $attrs.sortText }}</div>
  </div>
</template>

<script>
export default {
  name: 'InputCom',
  props: {
    value: {
      required: true
    }
  },
  computed: {
    isInteger() {
      return this.$attrs.inputType === 'number' && this.$attrs.integer
    },
    inputType() {
      return this.$attrs.inputType || 'string'
    },
    isNumber() {
      return this.$attrs.inputType === 'number'
    },
    _value: {
      get() {
        return this.value
      },
      set(val) {
        if (typeof val === 'number') val = val.toString()
        this.$emit('input', val)
      }
    }
  },
  methods: {
    computeHandle(type) {
      if (type === 'add') {
        if (!this._value) return (this._value = 1)
        if (typeof this._value === 'number') return (this._value += 1)
        if (this._value.includes('.'))
          return (this._value = Math.ceil(parseFloat(this._value)))
        this._value = parseFloat(this._value) + 1
      } else {
        if (this.isInteger)
          if (!this._value || this._value <= 1) return (this._value = 1)
        if (!this._value) return (this._value = -1)
        if (typeof this._value === 'number') return (this._value -= 1)
        if (this._value.includes('.'))
          return (this._value = Math.floor(parseFloat(this._value)))
        this._value = parseFloat(this._value) - 1
      }
    }
  }
}
</script>

<style scoped lang="scss">
.input-container {
  display: flex;
  .input-wrapper {
    width: 100%;
    display: flex;
    position: relative;
    .suffix-wrapper {
      width: 16px;
      height: calc(100% - 5px);
      display: flex;
      flex-direction: column;
      position: absolute;
      background: transparent;
      right: 16px;
      top: 3px;
      padding-top: 3px;
    }
  }
  .sort-text {
    flex-shrink: 0;
    margin-left: 8px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
  }
  .pointer {
    cursor: pointer;
    height: 10px;
  }
}
::v-deep {
  .el-input__inner,
  .el-textarea__inner {
    background: transparent;
  }
  .el-textarea .el-input__count {
    background: transparent;
  }
  // 取消input的上下箭头
  .el-input__inner::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  .el-input__inner::-webkit-outer-spin-button {
    -webkit-appearance: none !important;
  }
  .el-input__inner[type='number'] {
    -moz-appearance: textfield;
  }
}
</style>
