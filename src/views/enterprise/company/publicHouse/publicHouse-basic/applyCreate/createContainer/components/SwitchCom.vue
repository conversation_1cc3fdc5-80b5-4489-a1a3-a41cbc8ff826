<template>
  <el-switch
    v-model="_value"
    :validate-event="false"
    v-bind="$attrs"
    v-on="$listeners"
  >
  </el-switch>
</template>

<script>
export default {
  name: 'SwitchCom',
  props: {
    value: {
      required: true
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped></style>
