<template>
  <el-radio-group v-model="_value" v-bind="$attrs" v-on="$listeners">
    <el-radio v-for="item in options" :key="item.value" :label="item.value">{{
      item.label
    }}</el-radio>
  </el-radio-group>
</template>

<script>
export default {
  name: 'RadioCom',
  props: {
    value: {
      required: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style scoped></style>
