<template>
  <div class="footer-container">
    <div class="footer-wrapper">
      <el-progress
        class="progress-container"
        :stroke-width="4"
        :percentage="progress"
        text-color="rgba(5,76,232,0.5)"
        define-back-color="#e7e7e7"
      ></el-progress>
      <div class="footer-right">
        <div class="tips">
          您输入的任何信息，被视为最值得保护的隐私不会有任何泄漏和电话骚扰
        </div>
        <el-button size="mini" type="success" @click="submitForm"
          >提交评估</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import { apartmentSubmit } from '../../api'

export default {
  name: 'CreateFooter',
  data() {
    return {
      progress: 0,
      formData: {}
    }
  },
  inject: ['HouseApplyCreate'],
  methods: {
    async formDataHandle(data) {
      this.formData = data || {}
    },
    async initData(data) {
      await this.$nextTick()
      const list = JSON.parse(JSON.stringify(data))
      const allData = list.filter(item => item && item.prop).length
      const fillData = list.filter(
        item => item && item.prop && item.requiredPass
      ).length
      this.progress = Math.floor(parseFloat(fillData / allData) * 100)
      if (this.progress <= 0) this.progress = 0
      if (this.progress >= 100) this.progress = 100
    },
    // 格式化数据
    dataFormatter() {
      const formData = JSON.parse(
        JSON.stringify(this.HouseApplyCreate.$refs.createContainer.formData)
      )
      for (let item in formData) {
        if (
          JSON.stringify(formData[item]) === '{}' ||
          item.includes('stayPersonModel')
        ) {
          delete formData[item]
        }
      }
      // 数组附件数组取ids
      const attachKeys = [
        'certificateAttach',
        'applicationAttach',
        'cardAttach',
        'paymentAttach',
        'laborContractAttach',
        'commitmentAttach',
        'businessAttach',
        'contractAttach',
        'graduationAttach'
      ]
      attachKeys.forEach(item => {
        formData[item] = formData[item] && formData[item].map(val => val.id)
      })
      formData.progress = this.progress
      return formData
    },
    promptHandle(tips) {
      this.$message.success(tips + '成功')
      this.$router.go(-1)
    },
    // 提交评估
    submitForm() {
      const list = []
      list.push(this.HouseApplyCreate.$refs.createContainer.validateHandle())
      list.push(
        this.HouseApplyCreate.$refs.createContainer.$refs.stayPerson.validateHandle()
      )
      Promise.all(list).then(() => {
        apartmentSubmit(this.dataFormatter()).then(() => {
          this.promptHandle('提交')
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.footer-container {
  width: 100%;
  height: 64px;
  background: #ffffff;
  border: 1px solid #e9f0ff;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 100;
  .footer-wrapper {
    width: 100%;
    max-width: 1200px;
    height: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .progress-container {
      width: 100%;
      max-width: 350px;
      display: flex;
      align-items: center;
    }
    .footer-right {
      display: flex;
      align-items: center;
      .tips {
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.4);
        line-height: 20px;
        margin-right: 16px;
      }
    }
  }
}
::v-deep {
  .el-progress-bar {
    .el-progress-bar__outer {
      overflow: initial;
    }
    .el-progress-bar__inner {
      position: relative;
      &::before {
        display: inline-block;
        content: '';
        width: 18px;
        height: 18px;
        border: 2px solid #ed7b2f;
        border-radius: 50%;
        position: absolute;
        right: 0;
        top: -7px;
        background: #ffffff;
      }
    }
  }
  .el-button + .el-button {
    margin-left: 16px;
  }
}
</style>
