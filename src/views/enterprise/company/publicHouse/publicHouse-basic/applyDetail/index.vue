<template>
  <div class="details-container">
    <div class="details-wrapper">
      <apply-directory
        style="width: 17%"
        ref="directory"
        containerRef="detailsContainer"
      />
      <el-scrollbar id="el-scroll" class="details-content" ref="elScrollbar">
        <details-container ref="detailsContainer" />
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import ApplyDirectory from '../applyDirectory'
import DetailsContainer from './detailsContainer'
import { getApartmentDetail } from '../api'
export default {
  name: 'ApplyDetail',
  components: { DetailsContainer, ApplyDirectory },
  provide() {
    return {
      HouseApplyCreate: this
    }
  },
  async mounted() {
    await this.initData()
  },
  methods: {
    async initData() {
      try {
        const id = this.$route.query.id
        const res = await getApartmentDetail({ id })
        await this.$refs.detailsContainer.initData(res)
      } catch (e) {
        console.error(e)
      }
    },
    // dom滚动
    navPageHandle(dom) {
      this.$refs.elScrollbar.wrap.scrollTo({
        top: dom.offsetTop,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.details-container {
  position: fixed;
  left: 0;
  top: 55px;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 99;
  .details-wrapper {
    display: flex;
    width: 100%;
    height: 100%;
    padding-bottom: 32px;
    background: linear-gradient(
      180deg,
      rgba(234, 240, 255, 0.2) 0%,
      rgba(246, 249, 255, 0.2) 100%
    );
    .details-content {
      width: 100%;
      height: 100%;
    }
  }
}
::v-deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
    .el-scrollbar__view {
      padding: 32px 40px 64px 0;
      display: flex;
      width: 100%;
    }
  }
  .is-horizontal {
    display: none;
  }
}
</style>
