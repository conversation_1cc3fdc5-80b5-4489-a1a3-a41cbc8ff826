<template>
  <div class="info-container">
    <h1 label="申请信息" class="info-title DirectoryLabel">申请信息</h1>
    <div class="apply-info">
      <div class="info-title-2 apply-info-title">申请人</div>
      <div
        class="item-wrapper DirectoryLabel"
        id="entName"
        label="所在企业"
        prop="entName"
      >
        <div class="item-label">所在企业</div>
        <div class="item-content">{{ formData.entName }}</div>
      </div>
      <div class="item-wrapper">
        <div
          class="item-label DirectoryLabel"
          id="applyName"
          label="申请人"
          prop="applyName"
        >
          申请人
        </div>
        <div class="item-content">{{ formData.applyName }}</div>
      </div>
      <div class="item-wrapper">
        <div
          class="item-label DirectoryLabel"
          id="applyPhone"
          label="联系方式"
          prop="applyPhone"
        >
          联系方式
        </div>
        <div class="item-content">{{ formData.applyPhone || noData }}</div>
      </div>
      <div class="info-title-2">意向</div>
      <div class="item-wrapper">
        <div
          class="item-label DirectoryLabel"
          id="parkName"
          label="申请入住"
          prop="parkName"
        >
          申请入住
        </div>
        <div class="item-content">{{ formData.parkName }}</div>
      </div>
      <div class="item-wrapper">
        <div
          class="item-label DirectoryLabel"
          id="applyHouseTypeStr"
          label="申请房型"
          prop="applyHouseTypeStr"
        >
          申请房型
        </div>
        <div class="item-content">{{ formData.applyHouseTypeStr }}</div>
      </div>
    </div>
    <h1 label="入住人" class="info-title mar-top-40 DirectoryLabel">入住人</h1>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in occupantList"
        :key="item.id"
        :label="'入住人' + (index + 1)"
        :name="(index + 1).toString()"
      ></el-tab-pane>
    </el-tabs>
    <div class="basic-info">
      <div class="table-item">
        <div
          class="table-label table-item-title DirectoryLabel span-24"
          id="basicInfo"
          label="基本信息"
          prop="basicInfo"
        >
          基本信息
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="name"
          label="姓名"
          prop="name"
        >
          姓名
        </div>
        <div class="table-content span-8">{{ currentOccupant.name }}</div>
        <div
          class="table-label DirectoryLabel span-4"
          id="card"
          label="身份证号码"
          prop="card"
        >
          身份证号码
        </div>
        <div class="table-content span-8">{{ currentOccupant.card }}</div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="phone"
          label="联系方式"
          prop="phone"
        >
          联系方式
        </div>
        <div class="table-content span-8">{{ currentOccupant.phone }}</div>
        <div
          class="table-label DirectoryLabel span-4"
          id="genderStr"
          label="性别"
          prop="genderStr"
        >
          性别
        </div>
        <div class="table-content span-8">{{ currentOccupant.genderStr }}</div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="domicile"
          label="户籍所在地"
          prop="domicile"
        >
          户籍所在地
        </div>
        <div class="table-content span-8">{{ currentOccupant.domicile }}</div>
        <div
          class="table-label DirectoryLabel span-4"
          id="applyTypeStr"
          label="申请人类型"
          prop="applyTypeStr"
        >
          申请人类型
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.applyTypeStr }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="marriageStr"
          label="婚姻状况"
          prop="marriageStr"
        >
          婚姻状况
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.marriageStr }}
        </div>
        <div
          class="table-label DirectoryLabel span-4"
          id="politicalOutlookStr"
          label="政治面貌"
          prop="politicalOutlookStr"
        >
          政治面貌
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.politicalOutlookStr }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label table-item-title DirectoryLabel span-24"
          id="educationInfo"
          label="学历信息"
          prop="educationInfo"
        >
          学历信息
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="school"
          label="毕业院校"
          prop="school"
        >
          毕业院校
        </div>
        <div class="table-content span-8">{{ currentOccupant.school }}</div>
        <div
          class="table-label DirectoryLabel span-4"
          id="graduationTime"
          label="毕业时间"
          prop="graduationTime"
        >
          毕业时间
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.graduationTime }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="educationStr"
          label="最高学历"
          prop="educationStr"
        >
          最高学历
        </div>
        <div class="table-content span-20">
          {{ currentOccupant.educationStr }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label table-item-title DirectoryLabel span-24"
          id="unitInfo"
          label="单位信息"
          prop="unitInfo"
        >
          单位信息
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="workTypeStr"
          label="单位类型"
          prop="workTypeStr"
        >
          单位类型
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.workTypeStr }}
        </div>
        <div
          class="table-label DirectoryLabel span-4"
          id="workUnit"
          label="是否在高新区"
          prop="workUnit"
        >
          是否在高新区
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.workUnit ? '是' : '否' }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="socialSecurity"
          label="是否缴纳社保"
          prop="socialSecurity"
        >
          是否缴纳社保
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.socialSecurity ? '是' : '否' }}
        </div>
        <div
          class="table-label DirectoryLabel span-4"
          id="socialSecurityTime"
          label="社保缴纳日期"
          prop="socialSecurityTime"
        >
          社保缴纳日期
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.socialSecurityTime || noData }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="workName"
          label="单位名称"
          prop="workName"
        >
          单位名称
        </div>
        <div class="table-content span-8">{{ currentOccupant.workName }}</div>
        <div
          class="table-label DirectoryLabel span-4"
          id="workCode"
          label="统一社会信用代码"
          prop="workCode"
        >
          统一社会信用代码
        </div>
        <div class="table-content span-8">{{ currentOccupant.workCode }}</div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="workAddress"
          label="工作单位地址"
          prop="workAddress"
        >
          工作单位地址
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.workAddress }}
        </div>
        <div
          class="table-label DirectoryLabel span-4"
          id="workDetailAddress"
          label="详细地址"
          prop="workDetailAddress"
        >
          详细地址
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.workDetailAddress }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="yearLife"
          label="劳动合同签订年限"
          prop="yearLife"
        >
          劳动合同签订年限
        </div>
        <div class="table-content span-8">{{ currentOccupant.yearLife }}</div>
        <div
          class="table-label DirectoryLabel span-4"
          id="monthlyIncome"
          label="个人月收入"
          prop="monthlyIncome"
        >
          个人月收入
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.monthlyIncome }}
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label table-item-title DirectoryLabel span-24"
          id="propertyInfo"
          label="房产信息"
          prop="propertyInfo"
        >
          房产信息
        </div>
      </div>
      <div class="table-item">
        <div
          class="table-label DirectoryLabel span-4"
          id="privateRights"
          label="是否有私有产权房(含配偶)"
          prop="privateRights"
        >
          是否有私有产权房(含配偶)
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.privateRights ? '是' : '否' }}
        </div>
        <div
          class="table-label DirectoryLabel span-4"
          id="lessee"
          label="是否承租公房或廉租房"
          prop="lessee"
        >
          是否承租公房或廉租房
        </div>
        <div class="table-content span-8">
          {{ currentOccupant.lessee ? '是' : '否' }}
        </div>
      </div>
    </div>
    <h1
      class="info-title mar-top-40 DirectoryLabel"
      label="材料"
      v-if="formData.apartmentFileList && formData.apartmentFileList.length"
    >
      材料
    </h1>
    <div
      class="basic-info"
      v-if="formData.apartmentFileList && formData.apartmentFileList.length"
    >
      <div class="table-item">
        <div class="table-label span-12">相关材料</div>
        <div class="table-label span-12">附件</div>
      </div>
      <div
        class="table-item"
        v-for="item in formData.apartmentFileList || []"
        :key="item.id"
      >
        <div
          class="table-content span-12 DirectoryLabel"
          :id="'material' + item.id"
          :label="item.materialsName"
          :prop="'material' + item.id"
        >
          {{ item.materialsName }}
        </div>
        <div class="table-content attach-wrapper span-12">
          <el-link
            v-for="(row, index) in attachList(item.attach)"
            :key="index"
            type="primary"
            :underline="false"
            @click="newOpenHandle(row.path)"
            >{{ row.name }}</el-link
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { uploaderType } from '../../configure'

export default {
  name: 'DetailsContainer',
  data() {
    return {
      noData: '-',
      formData: {},
      occupantList: [],
      activeName: '1',
      currentOccupant: {}
    }
  },
  inject: ['HouseApplyCreate'],
  methods: {
    attachList(attach) {
      if (!attach || !attach[uploaderType]) return []
      else return attach[uploaderType]
    },
    handleClick() {
      const index = Number(this.activeName) - 1
      this.currentOccupant = this.formData.occupantList[index]
    },
    newOpenHandle(url) {
      window.open(url)
    },
    async initData(data) {
      const res = data
      // 附件相关需要的格式
      const apartmentFileList = res.apartmentFileList || []
      apartmentFileList.forEach(item => {
        res[`material${item.id}`] =
          JSON.stringify(item.attach) === '{}' || !item.attach
            ? []
            : item.attach[uploaderType]
      })
      this.formData = { ...res }
      this.occupantList = res.occupantList || []
      if (this.occupantList.length) {
        this.currentOccupant = this.occupantList[0]
        this.formData = {
          ...this.formData,
          ...this.currentOccupant
        }
      }
      this.HouseApplyCreate.$refs.directory.initData(true)
    }
  }
}
</script>

<style scoped lang="scss">
.info-container {
  width: 100%;
  padding: 0 24px;
  .info-title {
    height: 28px;
    font-size: 20px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
    line-height: 28px;
    margin-bottom: 24px;
  }
  .info-title-2 {
    font-size: 16px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.9);
    line-height: 24px;
    margin-bottom: 16px;
    margin-top: 24px;
  }
  .apply-info-title {
    margin-top: 0;
  }
  .mar-top-40 {
    margin-top: 40px;
  }
  .item-wrapper {
    display: flex;
    margin-bottom: 16px;
  }
  .item-label {
    font-size: 14px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.4);
    line-height: 22px;
    margin-right: 16px;
    text-align: right;
    width: 80px;
    flex-shrink: 1;
  }
  .item-content {
    font-size: 14px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    flex-shrink: 1;
  }
  .table-item {
    display: flex;
    flex-shrink: 1;
  }
  .table-label {
    background: rgb(244, 246, 250);
    padding: 0 24px;
    text-align: left;
    min-height: 50px;
    font-size: 14px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    flex-shrink: 1;
  }
  .table-content {
    padding: 0 24px;
    font-size: 14px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.9);
    background: #ffffff;
    display: flex;
    align-items: center;
    word-break: break-all;
    min-height: 50px;
    flex-shrink: 1;
  }
  .apply-info {
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9f0ff;
    padding: 24px 32px 8px;
  }
  .basic-info {
    border-radius: 3px;
    border: 1px solid #e7e7e7;
    overflow: hidden;
    .table-item {
      border-bottom: 1px solid #e7e7e7;
      flex-shrink: 1;
      &:last-child {
        border-bottom: none;
      }
      .table-label,
      .table-content {
        border-right: 1px solid #e7e7e7;
        &:last-child {
          border-right: none;
        }
      }
    }
  }
  .table-item-title {
    color: rgba(0, 0, 0, 0.9);
  }
  .attach-wrapper {
    display: flex;
    flex-wrap: wrap;
    .el-link {
      width: fit-content;
      margin: 0 10px;
    }
  }
  .flex {
    display: flex;
  }
  .span-2 {
    width: calc(100% / 12);
  }
  .span-3 {
    width: calc(100% / 8);
  }
  .span-4 {
    width: calc(100% / 6);
  }
  .span-6 {
    width: 25%;
  }
  .span-8 {
    width: calc(100% / 3);
  }
  .span-12 {
    width: 50%;
  }
  .span-18 {
    width: 75%;
  }
  .span-20 {
    width: calc(100% / 1.2);
  }
  .span-24 {
    width: 100%;
  }
}
:deep(.el-tabs) {
  .el-tabs__nav-wrap::after {
    display: none;
  }
}
:deep(.el-link) {
  width: 100%;
}
:deep(.el-link--inner) {
  width: 100%;
  display: flex;
  align-items: center;
  word-break: break-all;
}
</style>
