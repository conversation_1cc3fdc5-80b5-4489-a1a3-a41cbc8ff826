export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'typeStr',
          label: '申请类型',
          align: 'center'
        },
        {
          prop: 'applyTypeStr',
          label: '申请主体',
          align: 'center'
        },
        {
          prop: 'applyName',
          label: '申请人',
          align: 'center'
        },
        {
          prop: 'applyTime',
          label: '申请时间',
          align: 'center'
        },
        {
          prop: 'statusStr',
          label: '申请状态',
          align: 'center',
          render: (h, scope) => {
            return (
              <span class={this.typeStyle(scope.row.status)}>
                {scope.row.statusStr}
              </span>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          align: 'center',
          width: 100,
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.detailClick(scope.row)
                }}
                class="m-r-15"
              >
                查看
              </el-link>
            )
          }
        }
      ]
    }
  }
}
