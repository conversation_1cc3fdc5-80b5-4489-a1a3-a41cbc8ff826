<template>
  <div>
    <!-- 头部 -->
    <module-header
      type="primary"
      title="走访调查"
      desc="参与填写园区相关走访调查问卷，积极反馈相关需求"
      :img="require('./images/search.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper p-t-24">
      <basic-tab
        :tabsData="tabsData"
        :current="inStatus"
        @tabsChange="tabsChange"
        :disabled="disabled"
      />
      <div class="module-list">
        <module-list ref="ModuleList" :table-data="tableData">
          <template slot-scope="scope">
            <div class="card-wrapper">
              <div class="card-list" v-for="data in scope.data" :key="data.id">
                <visit-card :item="data" />
              </div>
            </div>
          </template>
        </module-list>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab'
import ModuleList from '@/components/Lateral/ModuleList'
import VisitCard from './components/Card'
import { getVisitList } from './api'

export default {
  name: 'VisitBasic',
  components: {
    VisitCard,
    ModuleList,
    BasicTab,
    ModuleHeader
  },
  data() {
    return {
      inStatus: -1,
      tabsData: [
        { label: '全部', value: -1 },
        { label: '可填写', value: 2 },
        { label: '已结束', value: 3 },
        { label: '不可填写', value: 4 }
      ],
      tableData: [],
      disabled: false
    }
  },
  created() {
    this.getVisitList()
  },
  methods: {
    // 获取列表
    getVisitList() {
      const params = {
        inStatus: this.inStatus
      }
      getVisitList(params)
        .then(res => {
          this.tableData = res || []
          this.disabled = false
        })
        .catch(() => {
          this.disabled = false
        })
    },
    tabsChange(e) {
      this.disabled = true
      this.inStatus = e
      this.getVisitList()
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.card-wrapper) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: space-between;
  .card-list {
    width: 584px;
    margin-bottom: 32px;
  }
}
</style>
