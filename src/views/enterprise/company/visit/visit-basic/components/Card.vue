<template>
  <div class="card" @click="detailHandle">
    <img v-if="coverImg(item)" class="card-img" :src="coverImg(item)" alt="" />
    <img v-else class="card-img" src="../images/card-img.png" alt="" />
    <div class="card-content flex flex-direction-column justify-content-center">
      <div class="card-title line-1">{{ item.paperName }}</div>
      <div class="m-t-8 card-fill flex align-items-center">
        <svg-icon class="color-primary" icon-class="edit-1" />
        <span class="m-l-4">{{ item.writeRangeName }}</span>
      </div>
      <div class="m-t-8 card-fill flex align-items-center">
        <svg-icon class="color-warning" icon-class="time" />
        <span class="m-l-4" v-if="item.pedStTime || item.pedEndTime"
          >{{ item.pedStTime }} - {{ item.pedEndTime }}</span
        >
        <span v-else class="m-l-4">未设置截止日期</span>
      </div>
      <div class="m-t-8 card-fill flex align-items-center">
        <svg-icon class="color-success" icon-class="app" />
        <span class="m-l-4">{{ item.typeName || '未分类' }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VisitCard',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    coverImg(item) {
      const { attachMap = {} } = item
      if (attachMap.cover && attachMap.cover.length) {
        return attachMap.cover[0].path
      } else {
        return false
      }
    },
    detailHandle() {
      this.$router.push({
        path: '/company/manage/visitDetail',
        query: {
          id: this.item.paperId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #fef3e6;
  padding: 8px;
  display: flex;
  cursor: pointer;
  .card-img {
    width: 248px;
    height: 148px;
    border-radius: 3px;
    overflow: hidden;
    flex-shrink: 0;
  }
  .card-content {
    width: calc(100% - 248px);
    padding: 8px 8px 8px 16px;
    .card-title {
      font-size: 16px;
      font-weight: 350;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
    }
    .card-fill {
      font-size: 14px;
      font-weight: 350;
      color: rgba(0, 0, 0, 0.6);
      line-height: 22px;
    }
  }
}
</style>
