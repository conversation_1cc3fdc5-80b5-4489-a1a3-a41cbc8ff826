<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="recommend">
        <div class="flex align-items-center justify-content-between">
          <span
            class="item"
            v-for="item in 10"
            :key="item"
            :class="{ active: item === formModel.recommend, disabled }"
            @click="itemClick(item)"
            >{{ item }}</span
          >
        </div>
        <div class="view-tips flex justify-content-between">
          <span>{{ minText }}</span>
          <span>{{ maxText }}</span>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Recommend',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean
    }
  },
  data() {
    return {
      formModel: {
        recommend: ''
      },
      rules: {
        recommend: [
          {
            required: this.required,
            message: '此问题必须填写',
            trigger: 'change'
          }
        ]
      },
      questionId: -1,
      minText: '',
      maxText: '',
      isPass: false
    }
  },
  watch: {
    question: {
      handler(val) {
        this.questionId = val.questionId
        const [minText, maxText] = val.suggestName.split(',')
        this.minText = minText
        this.maxText = maxText
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  methods: {
    itemClick(val) {
      if (this.disabled) return false
      this.formModel.recommend = this.formModel.recommend === val ? '' : val
      this.validateHandle()
    },
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        content: this.formModel.recommend
      }
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss">
.item {
  width: 54px;
  height: 36px;
  background: #f5f6f7;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  border-radius: 2px;
  cursor: pointer;
  &:not(.disabled, .active):hover {
    background: #eff0f1;
  }
  &.active {
    background: #ed7b2f;
    color: #fff;
  }
  &.disabled {
    cursor: not-allowed;
  }
}
.view-tips {
  font-size: 14px;
  color: #646a73;
}
</style>
