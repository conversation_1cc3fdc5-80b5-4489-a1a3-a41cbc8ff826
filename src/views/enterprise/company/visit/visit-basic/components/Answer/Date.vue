<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="date">
        <el-date-picker
          v-if="dateType === 1"
          v-model="formModel.date"
          type="date"
          value-format="yyyy-MM-dd"
          :placeholder="placeholder"
        >
        </el-date-picker>
        <el-date-picker
          v-else
          v-model="formModel.date"
          type="datetime"
          format="yyyy-MM-dd HH:mm"
          value-format="yyyy-MM-dd HH:mm"
          :placeholder="placeholder"
        >
        </el-date-picker>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Date',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean
    }
  },
  data() {
    return {
      formModel: {
        date: ''
      },
      rules: {
        date: [
          {
            required: this.required,
            message: '此问题必须填写',
            trigger: 'change'
          }
        ]
      },
      questionId: -1,
      placeholder: '请输入',
      dateType: 1, // 1年月日；2年月日时分
      isPass: false
    }
  },
  watch: {
    question: {
      handler(val) {
        this.questionId = val.questionId
        this.placeholder = val.placeholder
        this.dateType = Number(val.dateType)
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  methods: {
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        content: this.formModel.date
      }
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss"></style>
