<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="image">
        <Uploader
          :class="{ disabled }"
          v-model="formModel.image"
          type="avatar"
          mulity
          :limit="9"
          accept="image/*"
          :uploadData="{ type: 'Tinymce' }"
          :disabled="disabled"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'ImageUpdate',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: <PERSON>olean
    }
  },
  data() {
    const validateImage = (rule, value, callback) => {
      if (!this.required) return callback()
      if (!value || !value.length) return callback(new Error(`此问题必须填写`))
      callback()
    }
    return {
      formModel: {
        image: []
      },
      rules: {
        image: [
          {
            validator: validateImage,
            trigger: 'change'
          }
        ]
      },
      questionId: -1,
      placeholder: '请输入',
      isPass: false
    }
  },
  watch: {
    'formModel.image': {
      handler() {
        this.validateHandle()
      },
      deep: true
    },
    question: {
      handler(val) {
        this.questionId = val.questionId
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  methods: {
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        attaches: this.formModel.image.map(item => item.id)
      }
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss">
.disabled {
  :deep(.default-wrapper) {
    cursor: not-allowed;
  }
}
</style>
