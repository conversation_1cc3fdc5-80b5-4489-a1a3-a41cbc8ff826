<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="rank">
        <el-rate v-model="formModel.rank" allow-half></el-rate>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Rank',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean
    }
  },
  data() {
    const validateHandle = (rule, value, callback) => {
      if (!this.required) return callback()
      if (!value) return callback(new Error('此问题必须填写'))
      callback()
    }
    return {
      formModel: {
        rank: 0
      },
      rules: {
        rank: [
          {
            validator: validateHandle,
            trigger: 'change'
          }
        ]
      },
      questionId: -1,
      isPass: false
    }
  },
  watch: {
    question: {
      handler(val) {
        this.questionId = val.questionId
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  methods: {
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        content: this.formModel.rank
      }
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss">
:deep(.el-rate) {
  height: 30px;
  .el-rate__icon {
    font-size: 30px;
  }
}
</style>
