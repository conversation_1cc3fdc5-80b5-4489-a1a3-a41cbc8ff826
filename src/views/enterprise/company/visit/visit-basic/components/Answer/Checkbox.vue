<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="checkbox">
        <el-checkbox-group
          v-model="formModel.checkbox"
          v-if="qsStyle === 'radio'"
          :min="0"
          :max="chooseCountEnd"
        >
          <el-checkbox
            v-for="item in options"
            :key="item.optionId"
            :label="item.optionId"
          >
            <div class="option-item">
              <span class="flex flex-direction-column">{{
                item.opDescribe
              }}</span>
              <el-image
                @click.prevent
                v-if="hasImage(item)"
                :src="item.attachMap.checkbox[0].path"
                :preview-src-list="[item.attachMap.checkbox[0].path]"
              />
            </div>
          </el-checkbox>
        </el-checkbox-group>
        <el-select
          v-model="formModel.checkbox"
          multiple
          placeholder="请选择"
          v-else-if="qsStyle === 'select'"
          :multiple-limit="chooseCountEnd"
        >
          <el-option
            v-for="item in options"
            :key="item.optionId"
            :value="item.optionId"
            :label="item.opDescribe"
          />
        </el-select>
        <el-checkbox-group
          v-else
          v-model="formModel.checkbox"
          :min="0"
          :max="chooseCountEnd"
        >
          <el-checkbox-button
            v-for="item in options"
            :key="item.optionId"
            :label="item.optionId"
            >{{ item.opDescribe }}</el-checkbox-button
          >
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Checkbox',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean
    }
  },
  data() {
    const validateCheckbox = (rule, value, callback) => {
      const len = value.length
      if (len && this.chooseCount && len < this.chooseCount) {
        return callback(new Error(`至少选择${this.chooseCount}项`))
      }
      if (!this.required) return callback()
      if (!len) return callback(new Error(`此问题必须填写`))
      callback()
    }
    return {
      formModel: {
        checkbox: []
      },
      rules: {
        checkbox: [
          {
            validator: validateCheckbox,
            trigger: 'change'
          }
        ]
      },
      options: [],
      qsStyle: 'radio',
      questionId: -1,
      chooseCount: 0,
      chooseCountEnd: 10000,
      isPass: false
    }
  },
  watch: {
    question: {
      handler(val) {
        this.options = val.options || []
        this.qsStyle = val.qsStyle || 'radio'
        this.questionId = val.questionId
        this.chooseCount = val.chooseCount || 0
        this.chooseCountEnd = val.chooseCountEnd || 10000
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  methods: {
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        answer: this.formModel.checkbox.toString()
      }
    },
    hasImage(item) {
      return !!item?.attachMap?.checkbox?.length
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss"></style>
