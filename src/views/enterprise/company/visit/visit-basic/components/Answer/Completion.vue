<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="completion">
        <el-input
          v-model="formModel.completion"
          :placeholder="placeholder"
          :autosize="{ minRows: 2 }"
          type="textarea"
          maxlength="300"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Completion',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean
    }
  },
  data() {
    const validateCompletion = (rule, value, callback) => {
      const numericReg = /^-?\d+(.\d+)?$/
      const emailReg = /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/
      const urlReg =
        /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~/])+$/
      const type = this.contentType
      if (value && type === 'numeric' && !numericReg.test(value))
        return callback(new Error('请输入有效的数值'))
      if (value && type === 'email' && !emailReg.test(value))
        return callback(new Error('请输入有效的电子邮箱'))
      if (value && type === 'url' && !urlReg.test(value))
        return callback(new Error('请输入有效的网址'))
      if (!this.required) return callback()
      if (!value) return callback(new Error(`此问题必须填写`))
      callback()
    }
    return {
      formModel: {
        completion: ''
      },
      rules: {
        completion: [
          {
            validator: validateCompletion,
            trigger: 'blur'
          }
        ]
      },
      questionId: -1,
      placeholder: '请输入',
      contentType: '', // numeric数值；email电子邮箱；url网址
      isPass: false
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  watch: {
    question: {
      handler(val) {
        this.questionId = val.questionId
        this.placeholder = val.placeholder
        this.contentType = val.contentType
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        content: this.formModel.completion
      }
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss"></style>
