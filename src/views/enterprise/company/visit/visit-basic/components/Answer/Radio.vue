<template>
  <div>
    <el-form
      :model="formModel"
      :rules="rules"
      ref="formModel"
      label-position="top"
      :disabled="disabled"
    >
      <el-form-item prop="radio">
        <el-radio-group v-model="formModel.radio" v-if="qsStyle === 'radio'">
          <el-radio
            v-for="item in options"
            :key="item.optionId"
            :label="item.optionId"
          >
            <div class="option-item">
              <span class="flex flex-direction-column">{{
                item.opDescribe
              }}</span>
              <el-image
                @click.prevent
                v-if="hasImage(item)"
                :src="item.attachMap.radio[0].path"
                :preview-src-list="[item.attachMap.radio[0].path]"
              />
            </div>
          </el-radio>
        </el-radio-group>
        <el-select
          v-model="formModel.radio"
          placeholder="请选择"
          v-else-if="qsStyle === 'select'"
        >
          <el-option
            v-for="item in options"
            :key="item.optionId"
            :value="item.optionId"
            :label="item.opDescribe"
          />
        </el-select>
        <el-radio-group v-else v-model="formModel.radio">
          <el-radio-button
            v-for="item in options"
            :key="item.optionId"
            :label="item.optionId"
            >{{ item.opDescribe }}</el-radio-button
          >
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'Radio',
  props: {
    question: {
      type: Object,
      default: () => ({})
    },
    required: {
      type: Boolean
    }
  },
  data() {
    return {
      formModel: {
        radio: ''
      },
      rules: {
        radio: [
          {
            required: this.required,
            message: '此问题必须填写',
            trigger: 'change'
          }
        ]
      },
      options: [],
      qsStyle: 'radio',
      questionId: -1,
      isPass: false
    }
  },
  watch: {
    question: {
      handler(val) {
        this.options = val.options || []
        this.qsStyle = val.qsStyle || 'radio'
        this.questionId = val.questionId
      },
      deep: true,
      immediate: true
    }
  },
  inject: ['AnswerRoot'],
  computed: {
    disabled() {
      return this.AnswerRoot.disabled
    }
  },
  methods: {
    // 验证
    validateHandle() {
      return new Promise((resolve, reject) => {
        this.$refs.formModel.validate(valid => {
          if (valid) {
            this.isPass = true
            resolve()
          } else {
            this.isPass = false
            reject()
          }
        })
      })
    },
    // 数据抛出
    getData() {
      if (!this.isPass) return false
      return {
        questionId: this.questionId,
        answer: this.formModel.radio
      }
    },
    hasImage(item) {
      return !!item?.attachMap?.radio?.length
    }
  },
  created() {
    this.$emit('init', this)
  }
}
</script>

<style scoped lang="scss"></style>
