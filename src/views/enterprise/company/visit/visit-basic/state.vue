<template>
  <div class="state-container">
    <img class="success-icon" src="./images/success-icon.svg" alt="" />
    <div class="font-size-18 font-strong">提交成功</div>
    <div v-if="time" class="font-size-14 time">{{ time }} 提交</div>
  </div>
</template>

<script>
export default {
  name: 'state',
  data() {
    return {
      time: ''
    }
  },
  created() {
    this.time = this.$route.query.time
  }
}
</script>

<style scoped lang="scss">
.state-container {
  max-width: 760px;
  min-height: calc(100vh - 96px);
  background: #fff;
  margin: 0 auto;
  padding: 40px;
  color: #1f2329;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .success-icon {
    width: 125px;
    height: 125px;
  }
  .time {
    color: #646a73;
    margin-top: 80px;
  }
}
</style>
