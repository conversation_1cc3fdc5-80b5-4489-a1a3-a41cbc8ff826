<template>
  <div class="wh100 visit-detail" v-if="!reqLoad">
    <!-- banner图片 -->
    <div class="banner">
      <img v-if="coverPath" class="wh100" :src="coverPath" alt="" />
      <img v-else class="wh100" src="./images/default-pc.jpg" alt="" />
    </div>
    <div class="detail-wrapper">
      <!-- 标题区域 -->
      <div class="title">
        {{ detailInfo.paperName }}
      </div>
      <!-- 描述区域 -->
      <div class="descriptor">
        {{ detailInfo.describeStr }}
      </div>
      <!-- 问题列表 -->
      <div
        class="answer-list"
        v-for="(question, index) in interviewQuestionVos"
        :key="question.questionId"
      >
        <!-- 标题区域 -->
        <div class="font-size-16 font-strong m-b-12">
          {{ index + 1 }}/{{ interviewQuestionVos.length }}.
          <span class="item-required" v-show="question.required">*</span>
          {{ question.qsTitle }}
          <span class="qs-type">[{{ questionMap[question.qsType] }}]</span>
          <span
            v-if="questionPlaceholder(question)"
            class="font-size-12 question-placeholder"
            >{{ questionPlaceholder(question) }}</span
          >
        </div>
        <!-- 选项区域 -->
        <div class="check">
          <component
            :is="questionCom[question.qsType]"
            :question="question"
            :required="question.required"
            @init="componentInit"
          />
        </div>
      </div>
      <!-- 提交 -->
      <el-button
        v-if="!disabled"
        type="primary"
        class="submit-btn"
        @click="submitHandle"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import { answerPaper, getPaperDetail } from './api'
import AnswerRadio from './components/Answer/Radio'
import AnswerCheckbox from './components/Answer/Checkbox'
import AnswerCompletion from './components/Answer/Completion'
import AnswerRank from './components/Answer/Rank'
import AnswerImage from './components/Answer/Image'
import AnswerDate from './components/Answer/Date'
import AnswerRecommend from './components/Answer/Recommend'
import AnswerContact from './components/Answer/Contact'
import dayjs from 'dayjs'

export default {
  name: 'VisitDetail',
  components: {
    AnswerRadio
  },
  data() {
    return {
      detailInfo: {},
      coverPath: '',
      interviewQuestionVos: [],
      questionMap: {
        radio: '单选题',
        checkbox: '多选题',
        completion: '问答题',
        rank: '评分题',
        image: '图片上传',
        date: '日期选择',
        recommend: 'NPS净推荐值',
        contact: '联系人'
      },
      questionCom: {
        radio: AnswerRadio,
        checkbox: AnswerCheckbox,
        completion: AnswerCompletion,
        rank: AnswerRank,
        image: AnswerImage,
        date: AnswerDate,
        recommend: AnswerRecommend,
        contact: AnswerContact
      },
      vms: [],
      submitData: {
        paperId: '',
        answerIdxToList: []
      },
      disabled: false,
      reqLoad: true
    }
  },
  provide() {
    return {
      AnswerRoot: this
    }
  },
  methods: {
    questionPlaceholder(row) {
      const { chooseCount, chooseCountEnd } = row
      if (
        !chooseCount &&
        chooseCount !== 0 &&
        chooseCount !== '0' &&
        !chooseCountEnd &&
        chooseCountEnd !== 0 &&
        chooseCountEnd !== '0'
      ) {
        return false
      }
      if (chooseCount && chooseCountEnd) {
        return `（请选择${chooseCount} - ${chooseCountEnd}个选项）`
      }
      if (chooseCount && !chooseCountEnd) {
        return `（请选择至少${chooseCount}个选项）`
      }
      if (!chooseCount && chooseCountEnd) {
        return `（请选择最多${chooseCountEnd}个选项）`
      }
    },
    submitConfirm() {
      this.$confirm('是否确认提交本次所填写的信息?', '确认提交', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        answerPaper(this.submitData).then(() => {
          this.$toast.success('提交成功')
          this.$router.replace({
            path: '/company/manage/visitState',
            query: {
              time: dayjs(new Date()).format('YYYY-MM-DD HH:mm')
            }
          })
        })
      })
    },
    submitHandle() {
      this.submitData.answerIdxToList = []
      let list = []
      for (let i = 0; i < this.vms.length; i++) {
        const vm = this.vms[i]
        list.push(vm?.validateHandle())
      }
      Promise.all(list).then(() => {
        for (let i = 0; i < this.vms.length; i++) {
          const vm = this.vms[i]
          if (vm.getData()) {
            this.submitData.answerIdxToList.push(vm.getData())
          }
        }
        this.submitConfirm()
      })
    },
    componentInit(vm) {
      this.vms.push(vm)
    },
    getPaperDetail() {
      const id = this.$route.query?.id
      getPaperDetail(id)
        .then(res => {
          this.disabled = !res.checkResult
          this.submitData.paperId = res.id
          const paperPage = res.paperPage?.cover || []
          this.coverPath = paperPage[0]?.path
          this.detailInfo = res || {}
          this.interviewQuestionVos = res.interviewQuestionVos || []
          this.reqLoad = false
        })
        .catch(() => {
          this.reqLoad = false
        })
    }
  },
  created() {
    this.getPaperDetail()
  }
}
</script>

<style scoped lang="scss">
.visit-detail {
  max-width: 760px;
  min-height: calc(100vh - 96px);
  background: #fff;
  margin: 0 auto;
  padding: 40px 0;
  color: #1f2329;
  .detail-wrapper {
    padding: 0 40px;
    .title {
      font-size: 24px;
      line-height: 29px;
      text-align: center;
      margin-top: 20px;
    }
    .descriptor {
      font-size: 14px;
      line-height: 18px;
      text-align: center;
      margin-top: 8px;
      margin-bottom: 20px;
    }
    .answer-list {
      padding-bottom: 22px;
      .item-required {
        color: #e34d59;
        font-weight: normal;
      }
      .qs-type {
        font-weight: normal;
      }
      .question-placeholder {
        color: #8f959e;
        font-weight: normal;
      }
    }
    .submit-btn {
      width: 335px;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto;
      font-size: 14px;
    }
  }
}
:deep(.el-form-item__label) {
  display: flex;
  &::before {
    display: none;
  }
}
:deep(.el-select) {
  width: 100%;
}
:deep(.el-radio) {
  display: flex;
  padding: 0;
  line-height: 22px;
  margin: 0 0 16px;
  .el-radio__input {
    padding-top: 4px;
  }
}
:deep(.el-radio-button) {
  & + .el-radio-button {
    margin-left: 20px;
  }
  .el-radio-button__inner {
    border: 1px solid #dcdcdc;
    border-radius: 4px;
  }
  &.is-active .el-radio-button__inner {
    border-color: #ed7b2f;
  }
}
:deep(.el-checkbox) {
  display: flex;
  padding: 0;
  line-height: 22px;
  margin: 0 0 16px;
  .el-checkbox__input {
    padding-top: 4px;
  }
}
:deep(.el-checkbox-button) {
  & + .el-checkbox-button {
    margin-left: 20px;
  }
  .el-checkbox-button__inner {
    border: 1px solid #dcdcdc;
    border-radius: 4px;
  }
  &.is-checked .el-checkbox-button__inner {
    border-color: #ed7b2f;
  }
}
</style>
