export default {
  data() {
    return {
      tableColumn: [
        {
          label: '员工姓名',
          prop: 'name'
        },
        {
          label: '所有人',
          prop: 'applyName'
        },
        {
          label: '车牌号',
          prop: 'carNo'
        },
        {
          label: '联系方式',
          prop: 'phone'
        },
        {
          label: '申请时间',
          prop: 'applyTime'
        },
        {
          label: '操作',
          prop: 'operation',
          render: (h, scope) => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.consentHandler(scope.row, 'consent')
                    }}
                  >
                    同意
                  </el-button>
                }
                <span class="m-r-4 m-l-4">|</span>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.consent<PERSON><PERSON><PERSON>(scope.row, 'refuse')
                    }}
                  >
                    拒绝
                  </el-button>
                }
              </div>
            )
          }
        }
      ],
      tableColumn1: [
        {
          label: '园区',
          prop: 'parkName'
        },
        {
          label: '合同类型',
          prop: 'type'
        },
        {
          label: '费用类型',
          prop: 'expenseType'
        },
        {
          label: '已生效的合同数',
          prop: 'effectiveContract'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '操作',
          prop: 'operation',
          render: () => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.expenseType()
                    }}
                  >
                    查看
                  </el-button>
                }
                <span class="m-r-4 m-l-4">|</span>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.drawerVisible = true
                    }}
                  >
                    编辑
                  </el-button>
                }
                <span class="m-r-4 m-l-4">|</span>
                <el-button type="text">删除</el-button>
              </div>
            )
          }
        }
      ],
      tableColumn2: [
        {
          label: '费用名称',
          prop: 'expenseName'
        },
        {
          label: '单位',
          prop: 'company'
        },
        {
          label: '是否合并到租房总价',
          prop: 'totalPrice'
        },
        {
          label: '费用计划',
          prop: 'expensePlan'
        },
        {
          label: '创建时间',
          prop: 'createTime'
        },
        {
          label: '操作',
          prop: 'operation',
          render: () => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.visible = true
                    }}
                  >
                    编辑
                  </el-button>
                }
                <span class="m-r-4 m-l-4">|</span>
                <el-button type="text">删除</el-button>
              </div>
            )
          }
        }
      ],
      dataList: [
        {
          text: '固定标识',
          value: 'HX',
          type: 'Fixed@'
        },
        {
          text: '园区缩写',
          value: 'MZCCY',
          type: 'Park@',
          length: 5
        },
        {
          text: '分隔符',
          value: '-',
          type: 'Symbol@'
        },
        {
          text: '年月日',
          value: '20221001',
          type: 'Date@',
          length: 'yyMMdd'
        },
        {
          text: '输入框',
          value: '( xxx )',
          type: 'Blank@'
        },
        {
          text: '分隔符',
          value: '-',
          type: 'Symbol@'
        },
        {
          text: '随机数(5)',
          value: '0116',
          type: 'Random@',
          length: 4
        }
      ]
    }
  }
}
