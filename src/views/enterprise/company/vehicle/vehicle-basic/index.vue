<template>
  <div class="enter-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="车辆管理"
      desc="查看并管理企业下所有员工已登记的车辆"
      :img="require('./images/search.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper p-t-24">
      <basic-tab
        :tabsData="tabsData"
        :current="current"
        @tabsChange="tabsChange"
      ></basic-tab>
      <div class="p-b-18 flex justify-content-end">
        <div>
          <span class="p-r-20" style="color: #999"
            >总额度
            <span class="p-l-8" style="color: #6c6c6c">{{ total }}</span></span
          >
          <span style="color: #999"
            >可用额度
            <span class="p-l-8" style="color: #ed7b2f">{{
              availableCredit
            }}</span></span
          >
        </div>
      </div>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        height="550"
        :api-fn="getCarManager"
        :extral-querys="extralQuerys"
      >
      </drive-table>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from './column'
import { getCarManager, getTotalQuota, updateCarStatus } from './api'
import BasicTab from '@/components/BasicTab'

export default {
  name: 'EnterpriseEnterParkBasic',
  mixins: [ColumnMixins],
  data() {
    return {
      getCarManager,
      total: 0,
      availableCredit: 0,
      enterParkStatus: null,
      current: 1,
      extralQuerys: {
        status: 1
      },
      tabsData: [
        { label: '待审核', value: 1 },
        { label: '执行中', value: 3 },
        { label: '已禁用', value: 5 }
      ]
    }
  },
  components: {
    ModuleHeader,
    BasicTab
  },
  created() {
    this.getTotalQuota()
  },

  methods: {
    getTotalQuota() {
      getTotalQuota().then(res => {
        this.total = res.totalQuota
        this.availableCredit = res.avaliableQuota
      })
    },
    tabsChange(e) {
      this.current = e
      this.extralQuerys.status = e
      this.$refs['drive-table'].triggerSearch(this.extralQuerys.status)
    },
    consentHandler(row, type) {
      if (type === 'consent') {
        updateCarStatus({ id: row.id, status: 3 }).then(() => {
          this.$message.success('操作成功')
          this.$refs['drive-table'].triggerSearch(this.extralQuerys.status)
          this.getTotalQuota()
        })
      } else if (type === 'refuse') {
        this.$confirm('是否拒绝该员工？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateCarStatus({ id: row.id, status: 2 }).then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.$refs['drive-table'].triggerSearch(this.extralQuerys.status)
            this.getTotalQuota()
          })
        })
      } else if (type === 'cancelVehicle') {
        this.$confirm('是否取消车辆？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          updateCarStatus({ id: row.id, status: 6 }).then(() => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.$refs['drive-table'].triggerSearch(this.extralQuerys.status)
            this.getTotalQuota()
          })
        })
      }
    }
  },
  watch: {
    'extralQuerys.status'(val) {
      if (val === 1) {
        this.tableColumn[4] = {
          label: '申请时间',
          prop: 'applyTime'
        }
        this.tableColumn[5] = {
          label: '操作',
          prop: 'operation',
          render: (h, scope) => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.consentHandler(scope.row, 'consent')
                    }}
                  >
                    同意
                  </el-button>
                }
                <span class="m-r-4 m-l-4">|</span>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.consentHandler(scope.row, 'refuse')
                    }}
                  >
                    拒绝
                  </el-button>
                }
              </div>
            )
          }
        }
      } else if (val === 3) {
        this.tableColumn[4] = {
          label: '执行时间',
          prop: 'checkTime'
        }
        delete this.tableColumn[5]
        this.tableColumn[5] = {
          label: '操作',
          prop: 'operation',
          render: (h, scope) => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.consentHandler(scope.row, 'cancelVehicle')
                    }}
                  >
                    取消车辆
                  </el-button>
                }
              </div>
            )
          }
        }
      } else if (val === 5) {
        this.tableColumn[4] = {
          label: '禁用时间',
          prop: 'forbidenTime'
        }
        delete this.tableColumn[this.tableColumn.length - 1]
        // this.tableColumn[4] = {
        //   label: '操作',
        //   prop: 'operation',
        //   render: (h,scope) => {
        //     return (
        //         <div>
        //           {
        //             <el-button
        //                 type="text"
        //                 onClick={() => {
        //                   this.consentHandler(scope.row,'cancelVehicle')
        //                 }}
        //             >
        //               取消车辆
        //             </el-button>
        //           }
        //         </div>
        //     )
        //   }
        // }
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
