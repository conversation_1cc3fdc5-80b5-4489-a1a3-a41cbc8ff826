import request from '@/utils/request'

// 获取车辆管理-车辆列表
export function getCarManager(params) {
  return request({
    url: `/carManager/entPageList`,
    method: 'get',
    params
  })
}

// 企业端-车辆管理-总额度-可用额度
export function getTotalQuota() {
  return request({
    url: `/carManager/totalQuota`,
    method: 'get'
  })
}

// 企业端-（同意，拒绝,企业禁用）
export function updateCarStatus(data) {
  return request({
    url: `/carManager/updateCarStatus`,
    method: 'post',
    data
  })
}
