<template>
  <div class="p-b-32">
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      :table-data="tableData"
    >
      <template v-slot:operate-left>
        <span class="p-b-20">材料列表</span>
      </template>
      <template v-slot:operate-right>
        <div>
          <el-popover
            placement="bottom-end"
            width="352"
            trigger="manual"
            v-model="visible"
            ref="popoverRef"
          >
            <div class="p-t-20">
              <driven-form
                ref="driven-form"
                v-model="formModel"
                :formConfigure="formConfigure"
              />
              <div class="flex align-items-center justify-content-end">
                <el-button type="info" size="mini" @click="visible = false"
                  >取消</el-button
                >
                <el-button type="primary" size="mini" @click="confirmDialog"
                  >确定</el-button
                >
              </div>
            </div>
            <el-button type="primary" slot="reference" @click="visible = true">
              <!-- 加号的icon -->
              <i class="el-icon-plus"></i>
              <span>新增材料</span>
            </el-button>
          </el-popover>
        </div>
      </template>
    </drive-table>

    <!-- 查看附件详情 -->
    <dialog-cmp
      title="附件详情"
      :visible.sync="visibleSee"
      width="30%"
      :haveOperation="false"
    >
      <div>
        <Uploader v-model="viewData" type="avatar" mulity onlyForView />
      </div>
    </dialog-cmp>

    <!-- 上传附件弹框 -->
    <dialog-cmp
      title="附件详情"
      :visible.sync="attachmentVisible"
      width="30%"
      @confirmDialog="attachmentConfirmDialog"
    >
      <div>
        <Uploader
          v-model="uploadAttachList"
          mulity
          :uploadData="uploadData"
          :maxLength="3"
          :limit="3"
          :maxSize="20"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
import descriptorMixins from './descriptor'
export default {
  name: 'EnterParkBasicFile',
  mixins: [ColumnMixins, descriptorMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      defaultAttachList: [
        '项目情况介绍(仅工商注册项目提供简介即可)',
        '公司法人代表及股东身份证明复印件如有法人股东需提供加盖公章的营业执照副本复印件',
        '入园申请材料真实性声明需签字盖章后上传'
      ], // 默认上传的附件
      attachmentVisible: false,
      visible: false,
      formModel: {
        attachName: ''
      },
      visibleSee: false, // 查看附加详情
      uploadData: {
        type: 'enterParkFile',
        flag: null
      },
      uploadAttachList: [], //当前打开弹框的数据
      currentIndex: 0,
      tableData: [],
      viewData: [] //查看附件的数据
    }
  },
  watch: {
    applyData: {
      handler(val) {
        if (val.attachMap) {
          const { enterParkFile = [] } = val.attachMap
          if (enterParkFile.length) {
            const flags = []
            this.tableData = enterParkFile.reduce((pre, current) => {
              const { flag } = current
              if (flags.includes(flag)) {
                pre[flags.indexOf(flag)].list.push(current)
              } else {
                flags.push(flag)
                pre.push({
                  attachName: flag,
                  list: [current]
                })
              }
              return pre
            }, [])
          } else {
            this.tableData = this.defaultAttachList.map(item => {
              return {
                attachName: item,
                list: []
              }
            })
          }
        }
      },
      immediate: true,
      deep: true
    },
    attachmentVisible(val) {
      if (!val) {
        this.uploadAttachList = []
      }
    },
    visibleSee(val) {
      if (!val) {
        this.viewData = []
      }
    },
    visible(val) {
      if (!val) {
        this.formModel = {}
        this.clearValidate()
      }
    }
  },
  methods: {
    // 廷加
    attachmentConfirmDialog() {
      if (this.uploadAttachList.length === 0)
        return this.$toast.warning('请先上传附件')
      this.tableData[this.currentIndex].list = this.uploadAttachList
      this.attachmentVisible = false
    },

    // 清除校验
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
    },

    // 添加附件名称
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { attachName } = this.formModel
          if (this.defaultAttachList.includes(attachName)) {
            return this.$toast.warning('当前已存在相同的附件名称')
          } else {
            this.tableData.push({
              attachName,
              list: []
            })
          }
          this.visible = false
        }
      })
    },

    // 查看附件详情
    seeAttachDetails(scope) {
      const { row } = scope
      this.viewData = row.list
      this.visibleSee = true
    },

    // 打开附件上传
    openUploadDialog(scope) {
      const { $index, row } = scope
      this.attachmentVisible = true
      this.currentIndex = $index
      this.uploadAttachList = row.list
      this.uploadData.flag = row.attachName
    },

    // 删除自定义附件加
    deleteCustomAttach(row, index) {
      const { list } = row
      if (list.length)
        return this.$toast.warning('请先清空该附件名称下所有附件再进行此操作')
      this.$confirm('确定删除该附件名称？').then(() => {
        this.tableData.splice(index, 1)
      })
    },

    // 提交
    submitForm() {
      return new Promise(resolve => {
        const len = this.tableData.length
        let flag = null
        const ids = []
        for (let i = 0; i < len; i++) {
          if (this.tableData[i].list.length === 0) {
            flag = i
            continue
          } else {
            this.tableData[i].list.forEach(item => {
              ids.push(item.id)
            })
          }
        }
        if (flag !== null) {
          return this.$toast.warning(
            `请先上传${this.tableData[flag].attachName}的附件`
          )
        } else {
          resolve(ids)
        }
      })
    }
  }
}
</script>

<style></style>
