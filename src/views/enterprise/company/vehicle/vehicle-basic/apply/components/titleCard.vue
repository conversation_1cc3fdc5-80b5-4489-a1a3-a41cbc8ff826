<template>
  <div :class="haveBottomLine ? 'title-card-wrapper' : ''" class="p-b-32">
    <div class="font-size-16 font-strong color-text-primary m-b-28">
      {{ title }}
    </div>
    <div>
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterParkBasicTitleCard',
  props: {
    haveBottomLine: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.title-card-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-light);
}
</style>
