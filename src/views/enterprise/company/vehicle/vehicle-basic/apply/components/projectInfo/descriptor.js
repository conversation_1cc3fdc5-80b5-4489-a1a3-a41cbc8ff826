export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '130px',
        descriptors: {
          name: {
            form: 'input',
            span: 12,
            label: '项目名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入项目名称'
              }
            ]
          },
          technicalField: {
            form: 'select',
            label: '所属技术领域',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入所属技术领域'
              }
            ],
            options: []
          },
          introduction: {
            form: 'input',
            label: '项目描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入项目描述'
              }
            ],
            props: {
              type: 'textarea'
            },
            customTips: () => {
              return (
                <div>
                  请简要描述项目信息，例如：技术、团队、市场、商业模式和企业发展规划等
                </div>
              )
            }
          },
          annualOutput: {
            form: 'input',
            label: '预计年产值',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入预计年产值'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">万元</div>
            }
          },
          annualTax: {
            form: 'input',
            label: '预计年税收额',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入预计年税收额'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">万元</div>
            }
          },
          jobNum: {
            form: 'input',
            label: '预计提供就业数',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入预计提供就业数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">个</div>
            }
          }
        }
      },
      formConfigureEnergy: {
        labelWidth: '130px',
        descriptors: {
          useWater: {
            form: 'radio',
            label: '项目是否用水',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目生产中是否需要用水'
              }
            ],
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          annualWater: {
            form: 'input',
            label: '项目年用水量',
            hidden: true,
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入项目年用水量'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">吨/年</div>
            }
          },
          annualElectric: {
            form: 'input',
            label: '项目年用电量',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入项目年用电量'
              },
              {
                pattern: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
                message: '请输入有效的项目年用电量',
                trigger: 'change'
              }
            ],
            events: {
              input: event => {
                this.$set(
                  this.formModel,
                  'annualElectric',
                  event.replace(/^\D*(\d*(?:\.\d{0,2})?).*$/g, '$1')
                )
              }
            },
            customRight: () => {
              return <div class="line-height-32 font-size-14">万度/年</div>
            }
          }
        }
      },
      formConfigureContaminated: {
        labelWidth: '130px',
        descriptors: {
          hasPollution: {
            form: 'radio',
            label: '项目生产中是否产生污染',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择项目生产中是否需要用水'
              }
            ],
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          pollution: {
            form: 'input',
            label: '项目生产中所产生的污染',
            hidden: true,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入项目生产中所产生的污染情况'
              }
            ],
            customTips: () => {
              return (
                <div class="font-size-14">
                  请选择项目生产中所产生的所有污染物
                </div>
              )
            }
          },
          monthlyDischarge: {
            form: 'input',
            label: '每月排放量',
            span: 24,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请简要说明所选污染每月排放情况'
              }
            ],
            props: {
              type: 'textarea'
            },
            customTips: () => {
              return (
                <div class="font-size-14">
                  简要说明所选污染每月排放量，例如“废水每月排放12吨；粉尘每月排放73kg”
                </div>
              )
            }
          },
          energySavingEffect: {
            form: 'input',
            label: '节能减排措施/成效',
            hidden: true,
            rule: [
              {
                type: 'string',
                message: '请简要说明节能减排方面所作出的措施及成效'
              }
            ],
            props: {
              type: 'textarea'
            }
          }
        }
      }
    }
  }
}
