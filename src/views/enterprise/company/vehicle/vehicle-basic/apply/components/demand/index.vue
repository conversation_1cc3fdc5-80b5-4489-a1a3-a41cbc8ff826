<template>
  <div>
    <div
      v-if="
        applyData.businessInfoRespVO &&
        applyData.businessInfoRespVO.registered === 1
      "
      class="m-b-32"
    >
      <title-card title="房源诉求">
        <driven-form
          ref="driven-form"
          v-model="formModel"
          :formConfigure="formConfigure"
        />
      </title-card>
    </div>

    <div>
      <title-card title="其他诉求" :haveBottomLine="false">
        <driven-form
          ref="driven-form-other"
          v-model="formModel"
          :formConfigure="formConfigureOther"
        />
      </title-card>
    </div>
  </div>
</template>

<script>
import TitleCard from '../titleCard'
import descriptorMixins from './descriptor'
import { getByTenantDictType } from '@/api/common'

export default {
  name: 'EnterParkBasicDemand',
  mixins: [descriptorMixins],
  components: {
    TitleCard
  },
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formModel: {}
    }
  },
  watch: {
    'formModel.financingDemand': {
      handler(val) {
        if (val === 0) {
          this.triggerFormHidden(true)
        } else if (val === 1) {
          this.triggerFormHidden(false)
        }
      }
    },
    applyData: {
      handler(val) {
        if (Object.keys(val).length) {
          const {
            expectArea,
            expectEnterDate,
            housingPurpose,
            bearing,
            houseAdditionDemand = '',
            financingDemand,
            acceptableFinancingWay = '',
            policyDemand,
            serverDemand = ''
          } = val

          this.formModel = {
            expectArea,
            expectEnterDate,
            housingPurpose,
            bearing,
            houseAdditionDemand: houseAdditionDemand
              ? houseAdditionDemand.split(',')
              : [],
            financingDemand,
            acceptableFinancingWay: acceptableFinancingWay
              ? acceptableFinancingWay.split(',')
              : [],
            policyDemand,
            serverDemand: serverDemand ? serverDemand.split(',') : []
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getHouseUse()
    this.getAdditionDemand()
    this.getFinanceType()
    this.getPolicyDemand()
    this.getServerDemand()
  },
  methods: {
    // 表单切换隐藏
    triggerFormHidden(e) {
      this.formConfigureOther.descriptors.acceptableFinancingWay.hidden = e
      this.clearValidate()
    },

    // 清除校验
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['driven-form-other'].clearValidate()
      })
    },

    // 获取房屋用途
    getHouseUse() {
      getByTenantDictType('house_use').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigure.descriptors.housingPurpose.options = list
      })
    },

    // 获取附加诉求
    getAdditionDemand() {
      getByTenantDictType('house_addition_demand').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigure.descriptors.houseAdditionDemand.options = list
      })
    },

    // 获取融资方式
    getFinanceType() {
      getByTenantDictType('financing_way').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigureOther.descriptors.acceptableFinancingWay.options =
          list
      })
    },

    // 获取政策需求
    getPolicyDemand() {
      getByTenantDictType('policy_demand').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigureOther.descriptors.policyDemand.options = list
      })
    },

    // 获取服务需求
    getServerDemand() {
      getByTenantDictType('server_demand').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigureOther.descriptors.serverDemand.options = list
      })
    },

    drivenFormSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormOtherSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-other'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    // 提交
    async saveEvent() {
      try {
        await Promise.all([
          this.drivenFormSubmit(),
          this.drivenFormOtherSubmit()
        ])
      } catch (e) {
        return e
      }
    }
  }
}
</script>

<style></style>
