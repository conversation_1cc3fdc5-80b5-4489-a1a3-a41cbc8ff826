<template>
  <div class="m-t-8">
    <el-card>
      <div class="garden-wrapper">
        <div class="m-b-16 line-height-22">项目基础信息</div>
        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label">
                项目信息
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.name || '-' }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                所属技术领域
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.technicalField || '-' }}
              </div>
            </div>
          </el-col>
          <!--          <el-col :span="8">-->
          <!--            <div class="flex line-height-22 m-b-16">-->
          <!--              <div class="font-size-14 color-text-secondary label label-custom">-->
          <!--                项目类型-->
          <!--              </div>-->
          <!--              <div class="font-size-14 text-black value">-->
          <!--                {{ projectInfoRespVO.type || '-' }}-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </el-col>-->
          <!-- <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                是否是高新技术项目
              </div>
              <div class="font-size-14 text-black value">
                {{ parkData.highTech }}
              </div>
            </div>
          </el-col> -->
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label">
                项目描述
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.introduction || '-' }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label">
                预计年产值
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.annualOutput || '-' }} 万元
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-custom">
                预计年税收额
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.annualTax || '-' }}万元
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-custom">
                预计提供就业数
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.jobNum || '-' }}个
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="garden-wrapper p-t-24">
        <div class="m-b-16 line-height-22">能耗情况</div>
        <el-row :gutter="20">
          <el-col :span="7" v-if="projectInfoRespVO.useWater">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-w">
                项目年用水量
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.annualWater || '-' }} 吨/年
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-custom">
                项目年用电量
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.annualElectric || '-' }} 万度/年
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div
        class="garden-wrapper garden-wrapper-custom p-t-24"
        v-if="projectInfoRespVO.hasPollution"
      >
        <div class="m-b-16">污染排放</div>
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-long">
                项目生产中所产生的污染
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.pollution || '-' }}
              </div>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="flex line-height-22 m-b-24">
              <div class="font-size-14 color-text-secondary label label-long">
                每月投放量
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.monthlyDischarge || '-' }}
              </div>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label label-long">
                节能减排措施/成效
              </div>
              <div class="font-size-14 text-black value">
                {{ projectInfoRespVO.energySavingEffect || '-' }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'ProjectBasisInfo',
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    projectInfoRespVO() {
      return this.applyData.projectInfoRespVO || {}
    }
  }
}
</script>

<style lang="scss" scoped>
.garden-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);

  .label {
    width: 76.01px;
    text-align-last: right;
    margin-right: 16px;
  }

  .label-long {
    width: 155px;
    text-align: right;
  }

  .label-w {
    width: 84.01px;
  }

  .label-b {
    width: 154.01px;
  }

  .label-custom {
    width: 136.01px;
  }

  .value {
    flex: 1;
  }

  &.no-border {
    border: none;
  }

  &.healthy-wrapper {
    padding-top: 24px;
    padding-bottom: 0;
  }

  .member-table {
    padding: 0 6px;
  }
}
.garden-wrapper-custom {
  border-bottom: none;
}
</style>
