<template>
  <div>
    <el-card>
      <div class="garden-wrapper p-b-24">
        <div class="m-b-16 line-height-22">意向园区</div>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label">
                意向园区
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.park }}
              </div>
            </div>
          </el-col>

          <el-col :span="7">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label">
                入驻类型
              </div>
              <div class="font-size-14 text-black value">
                {{
                  applyData.enterType === 1
                    ? '企业入驻'
                    : applyData.enterType === 2
                    ? '购房入驻'
                    : applyData.enterType === 3
                    ? '工商入驻'
                    : '其他'
                }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label label-custom">
                是否为推荐企业
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.recommend ? '是' : '否' }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row class="m-t-16" v-if="applyData.recommend" :gutter="20">
          <el-col :span="8">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label">
                推荐机构
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.recommendOrganization }}
              </div>
            </div>
          </el-col>

          <el-col :span="7">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label">推荐人</div>
              <div class="font-size-14 text-black value">
                {{ applyData.recommendContacts }}
              </div>
            </div>
          </el-col>

          <el-col
            v-if="enterParkRecommend && enterParkRecommend.length"
            :span="8"
          >
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label label-custom">
                推荐文件
              </div>
              <div class="font-size-14 text-black value">
                <el-button
                  size="mini"
                  @click="downloadFile(enterParkRecommend)"
                >
                  下载
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="garden-wrapper p-t-24">
        <div class="m-b-16 line-height-22">工商信息</div>
        <div v-if="businessInfoRespVO && businessInfoRespVO.registered === 1">
          <el-row :gutter="20">
            <el-col :span="7">
              <div class="flex line-height-22">
                <div class="font-size-14 color-text-secondary label">
                  企业名称
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.name }}
                </div>
              </div>
              <!-- <div class="enterprise-report font-size-14 line-height-22 m-b-16">
              <el-link type="primary"> 查看企业报告 </el-link>
            </div> -->
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  统一社会信用代码
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.creditCode }}
                </div>
              </div>
            </el-col>

            <el-col :span="7">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  注册资本
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.capital }}
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="7">
              <div class="flex line-height-22 m-b-16">
                <div class="font-size-14 color-text-secondary label">
                  注册时间
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.registerDate }}
                </div>
              </div>
            </el-col>

            <el-col :span="14">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  注册地址
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.address }}
                </div>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="7">
              <div class="flex line-height-22 m-b-16">
                <div class="font-size-14 color-text-secondary label">
                  所属行业
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.industry }}
                </div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  企业性质
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.property }}
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="7">
              <div class="flex line-height-22 m-b-24">
                <div class="font-size-14 color-text-secondary label">
                  纳税归属地
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.taxProvince
                  }}{{ businessInfoRespVO.taxCity
                  }}{{ businessInfoRespVO.taxDistrict }}
                </div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-24">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  资质荣誉
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.honor }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div
          v-if="
            applyData.businessInfoRespVO &&
            applyData.businessInfoRespVO.registered === 0
          "
        >
          <el-row :gutter="20">
            <el-col :span="7">
              <div class="flex line-height-22">
                <div class="font-size-14 color-text-secondary label">
                  拟定名称
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.name }}
                </div>
              </div>
            </el-col>

            <el-col :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  拟定注册时间
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.registerDate }}
                </div>
              </div>
            </el-col>

            <el-col v-if="enterParkNotice && enterParkNotice.length" :span="8">
              <div class="flex line-height-22 m-b-16">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  名称核准通知书
                </div>
                <div class="font-size-14 text-black value">
                  <el-button size="mini" @click="downloadFile(enterParkNotice)">
                    下载
                  </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="7">
              <div class="flex line-height-22 m-b-16">
                <div class="font-size-14 color-text-secondary label">
                  所属行业
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.industry }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="flex line-height-22 m-b-24">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  企业性质
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.property }}
                </div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="flex line-height-22 m-b-24">
                <div
                  class="font-size-14 color-text-secondary label label-custom"
                >
                  拟注册资本
                </div>
                <div class="font-size-14 text-black value">
                  {{ businessInfoRespVO.capital }}
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="garden-wrapper p-t-24 garden-wrapper-custom">
        <div class="m-b-16 line-height-22">主要成员构成</div>
        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label">
                法定代表人
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.legalPerson
                }}<span class="p-l-5 p-r-5">|</span
                >{{ basicInfoReqVO.legalPersonPhone }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                总经理或项目负责人
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.manager }}<span class="p-l-5 p-r-5">|</span
                >{{ basicInfoReqVO.managerPhone }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                企业常用联系人
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.contacts }}<span class="p-l-5 p-r-5">|</span
                >{{ basicInfoReqVO.contactsPhone }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label">
                在职员工数
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.employeeNum }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                大专以上人数
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.juniorNum }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                留学生人数
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.overseasNum }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label">
                研发人数
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.researchNum }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                研发占比
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.researchNumPercent }}%
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                高级职称人数
              </div>
              <div class="font-size-14 text-black value">
                {{ basicInfoReqVO.seniorNum }}
              </div>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="flex line-height-22 p-b-15">
              <div class="font-size-14 color-text-secondary label">
                员工证明
              </div>
              <div class="font-size-14 text-black value">
                <el-button size="mini" @click="downloadFile(enterParkStaff)">
                  下载
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="member-table">
              <drive-table
                ref="drive-table"
                :columns="tableColumn"
                :table-data="applyData.stockRightsRespVOS || []"
              >
              </drive-table>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import ColumnMixins from './column'
import downloads from '@/utils/download'
export default {
  name: 'IntentionGarden',
  mixins: [ColumnMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    enterParkRecommend() {
      return this.applyData.attachMap.enterParkRecommend || []
    },
    enterParkNotice() {
      return this.applyData.attachMap.enterParkNotice || []
    },
    enterParkStaff() {
      return this.applyData.attachMap.enterParkStaff || []
    },
    businessInfoRespVO() {
      return this.applyData.businessInfoRespVO || {}
    },
    basicInfoReqVO() {
      return this.applyData.basicInfoRespVO || {}
    }
  },
  methods: {
    // 下载
    downloadFile(list) {
      if (list.length === 0) return this.$toast.warning('暂无数据')
      downloads.addressDownload(list[0])
    }
  }
}
</script>

<style lang="scss" scoped>
.garden-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);

  .label {
    width: 76.01px;
    text-align-last: right;
    margin-right: 16px;
  }

  .label-custom {
    width: 136.01px;
  }

  .value {
    flex: 1;
  }

  &.no-border {
    border: none;
  }

  &.healthy-wrapper {
    padding-top: 24px;
    padding-bottom: 0;
  }

  .member-table {
    padding: 0 6px;
  }
  .enterprise-report {
    width: 176px;
    text-align: right;
  }
}
.garden-wrapper-custom {
  border-bottom: none;
}
</style>
