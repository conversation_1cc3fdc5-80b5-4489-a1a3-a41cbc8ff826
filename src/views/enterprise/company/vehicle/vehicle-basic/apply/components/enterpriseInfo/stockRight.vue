<template>
  <div>
    <drive-table
      ref="driveTable"
      :columns="tableColumn"
      :table-data="tableData"
    >
      <template v-slot:operate-left>
        <div class="font-size-14 title">股权占比</div>
      </template>
      <template v-slot:operate-right>
        <el-button type="primary" size="mini" @click="visible = true">
          <svg-icon icon-class="add" />
          <span>新增股东</span>
        </el-button>
      </template>
    </drive-table>

    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="30%"
      @confirmDialog="confirmDialog"
    >
      <div>
        <driven-form
          ref="driven-form"
          v-model="formModel"
          :formConfigure="formConfigure"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
export default {
  name: 'EnterParkBasicStockRight',
  data() {
    return {
      tableData: [],
      title: '新增股东',
      visible: false,
      tableColumn: [
        {
          prop: 'stockholder',
          label: '股东名称'
        },
        {
          prop: 'stockRatio',
          label: '持股比例（%）'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.editEvent(scope)
                  }}
                  class="m-r-15"
                >
                  编辑
                </el-link>
                <el-link
                  type="danger"
                  onClick={() => {
                    this.deleteEvent(scope)
                  }}
                >
                  删除
                </el-link>
              </div>
            )
          }
        }
      ],
      formModel: {},
      formConfigure: {
        descriptors: {
          stockholder: {
            form: 'input',
            label: '股东名称',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入股东名称'
              }
            ]
          },
          stockRatio: {
            form: 'input',
            label: '持股比例',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入持股比例'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">%</div>
            }
          }
        }
      },
      editIndex: null
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.formModel = {}
        this.title = '新增股东'
        this.clearValidate()
        this.editIndex = null
      }
    }
  },
  methods: {
    // 编辑
    editEvent({ $index, row }) {
      this.formModel = { ...row }
      this.editIndex = $index
      this.title = '编辑股东'
      this.visible = true
    },

    // 清除校验
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
    },

    deleteEvent({ $index }) {
      this.tableData.splice($index, 1)
    },

    // 保存
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          this.editIndex !== null
            ? this.tableData.splice(this.editIndex, 1, this.formModel)
            : this.tableData.push(this.formModel)
          this.visible = false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  &::before {
    content: '*';
    @include font_color(--color-danger);
    margin-right: 4px;
  }
}
</style>
