export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'index',
          label: '序号',
          width: 60,
          render: (h, scope) => {
            return <div>{scope.$index + 1}</div>
          }
        },
        {
          prop: 'attachName',
          label: '附件名称',
          render: (h, scope) => {
            return (
              <div>
                <span class="color-danger">*</span>
                {scope.row.attachName}
              </div>
            )
          }
        },
        {
          prop: 'attachmentNum',
          label: '附件数',
          width: 120,
          render: (h, scope) => {
            return (
              <div
                class="flex align-items-center pointer"
                onClick={() => {
                  this.seeAttachDetails(scope)
                }}
              >
                <span>
                  {scope.row.list?.length ? scope.row.list?.length : 0}
                </span>
                <svg-icon
                  class="color-primary"
                  icon-class="caret-right-small"
                />
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 130,
          render: (k, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="p-r-20"
                  onclick={() => {
                    this.openUploadDialog(scope)
                  }}
                >
                  上传附件
                </el-link>
                {!this.defaultAttachList.includes(scope.row.attachName) && (
                  <el-link
                    type="danger"
                    onclick={() => {
                      this.deleteCustomAttach(scope.row, scope.$index)
                    }}
                  >
                    删除
                  </el-link>
                )}
              </div>
            )
          }
        }
      ]
    }
  }
}
