<template>
  <div>
    <div>
      <title-card title="工商信息">
        <driven-form
          ref="driven-form"
          v-model="formModel.businessInfoReqVO"
          :formConfigure="formConfigure"
        />
      </title-card>
    </div>

    <div class="p-t-32">
      <title-card title="企业主要人员信息">
        <div>
          <driven-form
            ref="driven-form-personnel"
            v-model="formModel.basicInfoReqVO"
            :formConfigure="formConfigurePersonnel"
          />
        </div>

        <div>
          <stock-right ref="stock-right" />
        </div>
      </title-card>
    </div>

    <div class="p-t-32">
      <title-card title="企业员工信息">
        <driven-form
          ref="driven-form-staff"
          v-model="formModel.basicInfoReqVO"
          :formConfigure="formConfigureStaff"
        />
      </title-card>
    </div>

    <div v-show="formModel.businessInfoReqVO.registered === 1" class="p-t-32">
      <title-card title="上年度主要经济指标" :haveBottomLine="false">
        <div class="m-b-24">
          <economic-indicators ref="economic-indicators" />
        </div>
        <driven-form
          ref="driven-form-turnover"
          v-model="formModel"
          :formConfigure="formConfigureTurnover"
        />
      </title-card>
    </div>
  </div>
</template>

<script>
import TitleCard from '../titleCard'
import descriptorMixins from './descriptor'
import StockRight from './stockRight'
import EconomicIndicators from './economicIndicators'
import { getEconomicQuarter } from '../../api'
import { getByTenantDictType, businessLicense } from '@/api/common'

const License = {
  社会信用代码: 'creditCode',
  注册资本: 'capital',
  成立日期: 'registerDate',
  单位名称: 'name',
  地址: 'address'
}

let timer = null

export default {
  name: 'EnterParkBasicEnterpriseInfo',
  mixins: [descriptorMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    TitleCard,
    StockRight,
    EconomicIndicators
  },
  data() {
    return {
      formModel: {
        businessInfoReqVO: {
          address: ''
        },
        basicInfoReqVO: {},
        turnoverAttach: []
      }
    }
  },
  watch: {
    'formModel.businessInfoReqVO.registered': {
      handler(val) {
        if (val === 0) {
          this.triggerForm(true)
          this.formConfigure.descriptors.name.label = '企业名称'
        } else if (val === 1) {
          this.triggerForm(false)
          this.formConfigure.descriptors.name.label = '拟定名称'
        }
      },
      immediate: true
    },
    'formModel.businessInfoReqVO.registerDate': {
      handler(val) {
        if (val) {
          timer && clearTimeout(timer)
          timer = setTimeout(() => {
            this.getEconomicQuarter(val)
          }, 2000)
        }
      }
    },
    applyData: {
      handler(val) {
        if (Object.keys(val).length) {
          const {
            economicInfoRespVOS,
            stockRightsRespVOS,
            basicInfoRespVO,
            attachMap,
            businessInfoRespVO
          } = val

          const {
            registered,
            name,
            creditCode,
            registerDate,
            address,
            capital,
            industry,
            property,
            honor,
            regProvince,
            regCity,
            regDistrict,
            taxProvince,
            taxCity,
            taxDistrict
          } = businessInfoRespVO || {}

          const {
            enterParkBusiness = [],
            enterParkStaff = [],
            enterParkTurnover = [],
            enterParkNotice = []
          } = attachMap
          this.$refs['economic-indicators'] &&
            (this.$refs['economic-indicators'].tableData = economicInfoRespVOS)
          this.$refs['stock-right'] &&
            (this.$refs['stock-right'].tableData = stockRightsRespVOS)
          this.formModel.basicInfoReqVO = {
            ...basicInfoRespVO,
            attachStaff: enterParkStaff
          }
          this.formModel.turnoverAttach = enterParkTurnover || []

          if (registered === 1) {
            this.formModel.businessInfoReqVO = {
              registered,
              name,
              creditCode,
              registerDate,
              address,
              capital,
              industry,
              property,
              honor,
              addressSelect: [regProvince, regCity, regDistrict].join(','),
              taxAscription: [taxProvince, taxCity, taxDistrict].join(','),
              enterParkBusiness
            }
          } else {
            this.formModel.businessInfoReqVO = {
              registered,
              name,
              registerDateDraft: registerDate,
              capitalDraft: capital,
              industry,
              property,
              noticeAttach: enterParkNotice
            }
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getHonorType()
    this.getNatureType()
    this.getIndustryType()
  },
  methods: {
    // 工商注册选择切换
    triggerForm(e) {
      const descriptors = this.formConfigure.descriptors
      descriptors.enterParkBusiness.hidden = e
      descriptors.noticeAttach.hidden = !e
      descriptors.registerDateDraft.hidden = !e
      descriptors.creditCode.hidden = e
      descriptors.registerDate.hidden = e
      descriptors.addressSelect.hidden = e
      descriptors.capital.hidden = e
      descriptors.taxAscription.hidden = e
      descriptors.honor.hidden = e
      descriptors.capitalDraft.hidden = !e
      descriptors.seat.hidden = e
    },

    // 获取主要经济指标
    getEconomicQuarter(e) {
      if (this.applyData.economicInfoRespVOS.length) return
      getEconomicQuarter(e).then(res => {
        this.$refs['economic-indicators'].getEconomicQuarter(res)
      })
    },

    // 营业执照识别
    businessLicense(e) {
      const attachId = e[0].id
      businessLicense(attachId).then(res => {
        let data = JSON.parse(res)
        for (const key in License) {
          this.$set(
            this.formModel.businessInfoReqVO,
            License[key],
            data[key].words
          )
        }
        this.$refs['driven-form'].$refs['driven-form'].clearValidate()
      })
    },

    // 获取活动级别
    getHonorType() {
      getByTenantDictType('enterprise_qualification_honor').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigure.descriptors.honor.options = list
      })
    },

    // 获取企业性质
    getNatureType() {
      getByTenantDictType('enterprise_nature').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigure.descriptors.property.options = list
      })
    },

    // 获取所属行业
    getIndustryType() {
      getByTenantDictType('enterprise_industry').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigure.descriptors.industry.options = list
      })
    },

    drivenFormSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormPersonnelSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-personnel'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormStaffSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-staff'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormTurnoverSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-turnover'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    // 提交
    async saveEvent() {
      const list = [
        this.drivenFormSubmit(),
        this.drivenFormPersonnelSubmit(),
        this.drivenFormStaffSubmit()
      ]

      this.formModel.businessInfoReqVO.registered === 1 &&
        list.push(this.drivenFormTurnoverSubmit())

      try {
        await Promise.all(list)
      } catch (e) {
        return e
      }
    }
  }
}
</script>

<style></style>
