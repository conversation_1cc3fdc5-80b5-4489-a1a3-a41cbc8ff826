<template>
  <div>
    <div>
      <title-card title="项目基础信息">
        <driven-form
          ref="driven-form"
          v-model="formModel"
          :formConfigure="formConfigure"
        />
      </title-card>
    </div>

    <div class="p-t-32">
      <title-card title="能耗情况">
        <driven-form
          ref="driven-form-energy"
          v-model="formModel"
          :formConfigure="formConfigureEnergy"
        />
      </title-card>
    </div>

    <div class="p-t-32">
      <title-card title="污染排放" :haveBottomLine="false">
        <driven-form
          ref="driven-form-contaminated"
          v-model="formModel"
          :formConfigure="formConfigureContaminated"
        />
      </title-card>
    </div>
  </div>
</template>

<script>
import TitleCard from '../titleCard'
import descriptorMixins from './descriptor'
import { getByTenantDictType } from '@/api/common'

export default {
  name: 'EnterParkBasicProjectInfo',
  mixins: [descriptorMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    TitleCard
  },
  data() {
    return {
      formModel: {}
    }
  },
  watch: {
    'formModel.useWater': {
      handler(val) {
        if (val === 1) {
          this.triggerFormHidden(false)
        } else {
          delete this.formModel.annualWater
          this.triggerFormHidden(true)
        }
      },
      immediate: true
    },
    'formModel.hasPollution': {
      handler(val) {
        if (val === 1) {
          this.triggerFormPollutionHidden(false)
        } else {
          delete this.formModel.pollution
          delete this.formModel.monthlyDischarge
          delete this.formModel.energySavingEffect
          this.triggerFormPollutionHidden(true)
        }
      },
      immediate: true
    },
    applyData: {
      handler(val) {
        if (Object.keys(val).length) {
          const {
            name,
            technicalField,
            introduction,
            annualOutput,
            annualTax,
            jobNum,
            useWater,
            annualWater,
            annualElectric,
            hasPollution,
            pollution,
            monthlyDischarge,
            energySavingEffect
          } = val.projectInfoRespVO || {}
          this.formModel = {
            name,
            technicalField,
            introduction,
            annualOutput,
            annualTax,
            jobNum,
            useWater,
            annualWater,
            annualElectric,
            hasPollution,
            pollution,
            monthlyDischarge,
            energySavingEffect
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getTechnicalField()
  },
  methods: {
    triggerFormHidden(e) {
      this.formConfigureEnergy.descriptors.annualWater.hidden = e
    },

    triggerFormPollutionHidden(e) {
      this.formConfigureContaminated.descriptors.pollution.hidden = e
      this.formConfigureContaminated.descriptors.monthlyDischarge.hidden = e
      this.formConfigureContaminated.descriptors.energySavingEffect.hidden = e
    },

    // 所属技术领域
    getTechnicalField() {
      getByTenantDictType('technical_field').then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.label }
        })
        this.formConfigure.descriptors.technicalField.options = list
      })
    },

    drivenFormSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormEnergySubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-energy'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormContaminatedSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-contaminated'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    // 提交
    async saveEvent() {
      try {
        await Promise.all([
          this.drivenFormSubmit(),
          this.drivenFormEnergySubmit(),
          this.drivenFormContaminatedSubmit()
        ])
      } catch (e) {
        return e
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
