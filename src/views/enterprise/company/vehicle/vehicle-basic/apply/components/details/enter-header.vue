<template>
  <div class="pos-relative">
    <div class="flex justify-content-center">
      <div class="steps-container">
        <steps :applyData="applyData" />
      </div>

      <div class="pos-absolute operation">
        <el-button class="m-r-8" type="info" @click="goBack">
          <svg-icon icon-class="rollback" />
          <span>返回</span>
        </el-button>
        <el-button
          v-if="applyData.result === 2"
          class="m-r-8"
          type="primary"
          @click="withdrawApply"
        >
          <svg-icon icon-class="attach" />
          <span>撤回</span>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Steps from './steps'
import { withdrawApply } from './api'

export default {
  name: 'enterParkHeaderBasic',
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    Steps
  },
  data() {
    return {}
  },
  methods: {
    goBack() {
      this.$router.go(-1)
    },

    // 撤回申请
    withdrawApply() {
      this.$confirm('确定撤回此入园申请？').then(() => {
        const { id } = this.$route.query
        withdrawApply(id).then(() => {
          this.$toast.success('撤回成功')
          this.$emit('getEnterParkApplyInfo')
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.operation {
  right: 0;
  top: 50%;
  transform: translateY(-50%);
}

.steps-container {
  flex: 0 0 480px;
}
</style>
