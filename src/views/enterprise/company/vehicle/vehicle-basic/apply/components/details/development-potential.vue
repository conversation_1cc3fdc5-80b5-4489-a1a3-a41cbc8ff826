<template>
  <div class="m-t-8">
    <el-card>
      <div
        v-if="
          applyData.businessInfoRespVO &&
          applyData.businessInfoRespVO.registered === 1
        "
        class="garden-wrapper garden-wrapper-custom"
      >
        <div class="m-b-16">主要经济指标</div>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="member-table">
              <drive-table
                ref="drive-table"
                :columns="energyTableColumn"
                :table-data="applyData.economicInfoRespVOS || []"
              >
              </drive-table>
            </div>
          </el-col>
        </el-row>
        <el-row class="m-t-16" :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label label-w">
                营业额证明材料
              </div>
              <div class="font-size-14 text-black value">
                <el-button size="mini" @click="downloadFile(enterParkTurnover)">
                  下载
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import ColumnMixins from './column'
import downloads from '@/utils/download'
export default {
  name: 'DevelopmentPotential',
  mixins: [ColumnMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    enterParkTurnover() {
      return this.applyData.attachMap.enterParkTurnover || []
    },
    enterParkStaff() {
      return this.applyData.attachMap.enterParkStaff || []
    }
  },
  methods: {
    // 下载
    downloadFile(list) {
      if (list.length === 0) return this.$toast.warning('暂无数据')
      downloads.addressDownload(list[0])
    }
  }
}
</script>

<style lang="scss" scoped>
.garden-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);

  .label {
    width: 76.01px;
    text-align-last: right;
    margin-right: 16px;
  }

  .label-custom {
    width: 136.01px;
  }

  .label-w {
    width: 100.01px;
  }

  .value {
    flex: 1;
  }

  &.no-border {
    border: none;
  }

  &.healthy-wrapper {
    padding-top: 24px;
    padding-bottom: 0;
  }

  .member-table {
    padding: 0 6px;
  }
}
.garden-wrapper-custom {
  border-bottom: none;
}
</style>
