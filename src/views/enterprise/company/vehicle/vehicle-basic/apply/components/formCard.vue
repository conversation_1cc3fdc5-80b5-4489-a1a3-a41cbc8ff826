<template>
  <div class="form-card-container border-color-light bg-white">
    <div class="slot-wrapper">
      <slot />
    </div>
    <div
      class="footer-wrapper border-color-light flex align-items-center justify-content-end"
    >
      <el-button v-if="active > 0" type="info" @click="previousStep"
        >上一步</el-button
      >
      <el-button type="primary" @click="saveEvent">保存</el-button>
      <el-button v-if="active < 4" type="success" @click="nextStep"
        >下一步</el-button
      >
      <el-button v-if="active === 4" type="success" @click="submitApply"
        >提交审批</el-button
      >
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnterParkBasicFormCard',
  props: {
    active: {
      type: Number,
      default: 0
    }
  },
  methods: {
    // 上一步
    previousStep() {
      this.$emit('previousStep')
    },

    // 下一步
    nextStep() {
      this.$emit('nextStep')
    },

    // 保存
    saveEvent() {
      this.$emit('saveEvent')
    },

    // 提交审批
    submitApply() {
      this.$emit('submitApply')
    }
  }
}
</script>

<style lang="scss" scoped>
.form-card-container {
  border-radius: 6px;
  border: 1px solid;
  backdrop-filter: blur(11px);

  .slot-wrapper {
    padding: 32px 32px 0;
  }

  .footer-wrapper {
    height: 64px;
    border-top-width: 1px;
    border-style: solid;
    padding: 0 32px;
  }
}
</style>
