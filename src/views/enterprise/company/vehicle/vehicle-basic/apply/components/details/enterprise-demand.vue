<template>
  <div>
    <el-card>
      <div class="garden-wrapper garden-wrapper-custom">
        <div class="m-b-16 line-height-22">企业诉求</div>
        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-b">
                需求面积
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.expectArea }} m²
              </div>
            </div>
          </el-col>

          <el-col :span="9">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                期望入住时间
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.expectEnterDate }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label">
                房屋用途
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.housingPurpose }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-b">
                附加诉求
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.houseAdditionDemand }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                楼板承重
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.bearing }} kg/m²
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="applyData.financingDemand">
          <el-col :span="7">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-b">
                政策需求
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.policyDemand }}
              </div>
            </div>
          </el-col>

          <el-col :span="8">
            <div class="flex line-height-22 m-b-16">
              <div class="font-size-14 color-text-secondary label label-custom">
                服务需求
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.serverDemand }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="applyData.financingDemand">
          <el-col :span="8">
            <div class="flex line-height-22">
              <div class="font-size-14 color-text-secondary label label-b">
                可接受的融资方式
              </div>
              <div class="font-size-14 text-black value">
                {{ applyData.acceptableFinancingWay }}
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'EnterpriseDemand',
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.garden-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);

  .label {
    width: 76.01px;
    text-align-last: right;
    margin-right: 16px;
  }

  .label-w {
    width: 84.01px;
  }
  .label-b {
    width: 86.01px;
  }

  .label-custom {
    width: 136.01px;
  }

  .value {
    flex: 1;
  }

  &.no-border {
    border: none;
  }

  &.healthy-wrapper {
    padding-top: 24px;
    padding-bottom: 0;
  }

  .member-table {
    padding: 0 6px;
  }
}
.garden-wrapper-custom {
  border-bottom: none;
}
</style>
