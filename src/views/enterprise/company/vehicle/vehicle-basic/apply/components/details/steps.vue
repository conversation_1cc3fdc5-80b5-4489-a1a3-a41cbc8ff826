<template>
  <div class="steps-container flex align-items-center justify-content-between">
    <div
      class="flex steps-wrapper"
      v-for="(item, index) in stepsData"
      :key="index"
    >
      <div class="steps-item flex">
        <div class="left flex align-items-center">
          <div
            v-if="active === index"
            class="circle active-circle font-size-16 flex align-items-center justify-content-center font-strong"
            :class="getStyle(index).iconStyle"
          >
            <svg-icon
              v-if="getStyle(index).iconStyle === 'icon-danger'"
              icon-class="close"
            />
            <span v-else class="color-white">{{ index + 1 }}</span>
          </div>
          <div
            v-else
            class="circle font-size-16 flex align-items-center justify-content-center font-strong"
            :class="getStyle(index).iconStyle"
          >
            <svg-icon v-if="active > index" icon-class="check" />
            <span v-else>{{ index + 1 }}</span>
          </div>
        </div>
        <div class="right">
          <div
            class="font-size-16 color-text-placeholder title font-strong"
            :class="getStyle(index).labelStyle"
          >
            {{ item.label }}
          </div>
          <div class="font-size-14 status" :class="getStyle(index).valueStyle">
            {{ getVale(index, item) || '-' }}
          </div>
        </div>
      </div>

      <div v-if="index < stepsData.length - 1" class="steps-line"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContractBasicSteps',
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      stepsData: [
        {
          label: '申请表提交',
          prop: 'applyDate',
          statusText: '待提交'
        },
        {
          label: '园区审批',
          prop: 'wfCompleteTime',
          statusText: '审核中'
        }
      ],
      dangerStatue: [5, 6]
    }
  },
  computed: {
    active() {
      const status = new Map()
      status.set(1, 0)
      status.set(2, 1)
      status.set(3, 1)
      status.set(4, 3)
      status.set(5, 3)
      status.set(6, 3)
      status.set(7, 0)
      return status.get(this.applyData.result)
    }
  },
  methods: {
    getStyle(index) {
      const { active, dangerStatue, applyData } = this
      const result = applyData.result
      if (active > index) {
        return {
          iconStyle: 'icon-primary',
          labelStyle: 'label-text-primary',
          valueStyle: 'color-text-regular'
        }
      } else if (active === index) {
        if (dangerStatue.includes(result)) {
          return {
            iconStyle: 'icon-danger',
            labelStyle: 'text-danger',
            valueStyle: 'color-text-regular'
          }
        } else {
          return {
            iconStyle: 'icon-primary active-circle',
            labelStyle: 'label-text-primary',
            valueStyle: 'color-text-regular'
          }
        }
      } else if (active < index) {
        return {
          iconStyle: '',
          labelStyle: '',
          valueStyle: 'color-text-placeholder'
        }
      } else {
        return {}
      }
    },

    _genResultText() {
      const status = new Map()
      status.set(1, '待提交')
      status.set(2, '待审核')
      status.set(3, '审核中')
      status.set(4, '已通过')
      status.set(5, '已拒绝')
      status.set(6, '已退回')
      status.set(7, '已撤回')
      return status.get(this.applyData.result)
    },

    getVale(index, { prop, statusText }) {
      const { active, applyData, dangerStatue } = this
      if (active > index) {
        return applyData[prop]
      } else if (active === index) {
        if (dangerStatue.includes(applyData.result)) {
          return applyData[prop]
        } else {
          return this._genResultText()
        }
      } else {
        return statusText
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.steps-container {
  width: 100%;
  padding-top: 80px;

  .steps-wrapper {
    flex: 1;

    .steps-item {
      .left {
        margin-right: 16px;
        height: 30px;
        line-height: 30px;

        .circle {
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid;
          @include font_color(--color-text-placeholder);
          @include border_color(--color-text-placeholder);
        }

        .active-circle {
          @include font_color(--color-white);
          @include background_color(--color-primary);
          @include border_color(--color-primary);
        }

        .icon-primary {
          @include font_color(--color-primary);
          @include border_color(--color-primary);
        }

        .icon-danger {
          @include font_color(--color-danger);
          @include border_color(--color-danger);
        }
      }
      .right {
        .title {
          height: 30px;
          line-height: 30px;
          margin-bottom: 6px;
        }

        .label-text-primary {
          @include font_color(--color-text-primary);
        }

        .label-primary {
          @include font_color(--color-primary);
        }

        .text-danger {
          @include font_color(--color-danger);
        }

        .status {
          line-height: 22px;
        }
      }
    }

    .steps-line {
      flex: 1;
      height: 2px;
      @include background_color(--color-text-placeholder);
      margin: 13px 16px 0;
    }

    .line-primary {
      @include background_color(--color-primary);
    }
  }
}
</style>
