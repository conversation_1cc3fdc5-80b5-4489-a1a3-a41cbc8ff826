export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '140px',
        descriptors: {
          expectArea: {
            form: 'input',
            span: 12,
            label: '所需房源面积',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入所需房源面积'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">㎡</div>
            }
          },
          expectEnterDate: {
            form: 'date',
            label: '期望入住时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择期望入住时间'
              }
            ]
          },
          housingPurpose: {
            form: 'select',
            label: '房屋用途',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择房屋使用用途'
              }
            ],
            options: []
          },
          bearing: {
            form: 'input',
            label: '楼板承重',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入楼板承重需求'
              }
            ],
            customTips: () => {
              return (
                <div class="font-size-14">
                  请输入楼板承重需求，如无特殊需求请填写0
                </div>
              )
            },
            customRight: () => {
              return <div class="line-height-32 font-size-14"> kg/m²</div>
            }
          },
          houseAdditionDemand: {
            form: 'select',
            label: '附加诉求',
            span: 12,
            rule: [
              {
                type: 'array',
                message: '请选择附加诉求'
              }
            ],
            props: {
              multiple: true
            },
            options: []
          }
        }
      },
      formConfigureOther: {
        labelWidth: '140px',
        descriptors: {
          financingDemand: {
            form: 'radio',
            label: '是否有融资需求',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择是否有融资需求'
              }
            ],
            options: [
              {
                label: '是',
                value: 1
              },
              {
                label: '否',
                value: 0
              }
            ]
          },
          acceptableFinancingWay: {
            form: 'select',
            label: '可接受的融资方式',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择可接受的融资方式'
              }
            ],
            options: [],
            props: {
              multiple: true
            }
          },
          policyDemand: {
            form: 'select',
            label: '政策需求',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择您所需的政策需求'
              }
            ],
            options: []
          },
          serverDemand: {
            form: 'select',
            label: '服务需求',
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择服务需求'
              }
            ],
            options: [],
            props: {
              multiple: true
            }
          }
        }
      }
    }
  }
}
