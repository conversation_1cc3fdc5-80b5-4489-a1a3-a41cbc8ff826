// 获取入园申请状态
export function getEnterParkStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag isDot type="info" label="待提交" />
    case 2:
      return <basic-tag isDot type="warning" label="待审核" />
    case 3:
      return <basic-tag isDot type="warning" label="审核中" />
    case 4:
      return <basic-tag isDot type="success" label="已通过" />
    case 5:
      return <basic-tag isDot type="danger" label="已拒绝" />
    case 6:
      return <basic-tag isDot type="danger" label="已退回" />
    case 7:
      return <basic-tag isDot type="info" label="已撤回" />
    default:
      return '-'
  }
}

// 获取企业类型
export function getEnterType(h, val) {
  switch (val) {
    case 1:
      return '租赁入驻'
    case 2:
      return '购房入驻'
    case 3:
      return '工商入驻'
    default:
      return '其他'
  }
}
