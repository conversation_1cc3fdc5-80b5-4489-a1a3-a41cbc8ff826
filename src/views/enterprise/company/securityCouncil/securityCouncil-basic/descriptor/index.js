import SecurityTeam from '../addDevice/securityTeam.vue'
export default {
  components: {
    SecurityTeam
  },
  data() {
    return {
      formConfigure: {
        descriptors: {
          establishTime: {
            form: 'date',
            label: '成立时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择成立时间'
              }
            ]
          },
          teamList: {
            form: 'component',
            label: '安全小组',
            rule: [
              {
                type: 'array',
                required: true,
                message: '请输入安全小组'
              }
            ],
            componentName: 'teamList',
            render: () => {
              return (
                <div>
                  <security-team
                    ref="securityTeam"
                    v-model={this.fromModel.teamList}
                    // refreshDict={() => {
                    //     this.getDictList()
                    // }}
                  />
                </div>
              )
            }
          },
          remark: {
            form: 'input',
            label: '备注描述',
            rule: [
              {
                type: 'string',
                message: '请输入备注描述'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 6 }
            }
          },
          attach: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <div class="line-height-32 color-info font-size-14">
                  <div style={'position: absolute; left: 186px'}>
                    请上传不大于10MB的附件,附件不得超过三个
                  </div>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      }
    }
  }
}
