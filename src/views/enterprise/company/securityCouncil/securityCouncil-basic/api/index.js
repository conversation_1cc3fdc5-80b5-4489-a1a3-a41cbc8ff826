import request from '@/utils/request'

// 新增设备类型，设备品牌
export function postLabelCreate(data) {
  return request({
    url: `/ck/ent/committee/create`,
    method: 'post',
    data
  })
}

// 编辑设备类型，设备品牌
export function postLabelUpdate(data) {
  return request({
    url: `/ck/ent/committee/update`,
    method: 'put',
    data
  })
}
// 详情接口;
export function getDetail() {
  return request({
    url: `/ck/ent/committee/get`,
    method: 'get'
  })
}
