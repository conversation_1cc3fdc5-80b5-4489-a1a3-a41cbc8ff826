<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      :id="id.toString()"
      title="安全委员会"
      desc="组建安全委员会有序安全生产"
      :img="require('@/assets/images/enterprise/company/safety/header-bg.png')"
      :imgOpacity="1"
    >
    </module-header>

    <div class="lateral-wrapper card">
      <div class="detail">
        <div class="detail-box">
          <span class="label">企业名称</span>
          <span class="value">{{ detail.entName | noData }}</span>
        </div>
        <div class="detail-box">
          <span class="label">在园状态</span>
          <span
            class="value"
            :class="{ 'color-success': detail.status === 1 }"
            >{{ detail.statusStr | noData }}</span
          >
        </div>
        <div class="detail-box">
          <span class="label">更新时间</span>
          <span class="value">{{ detail.updateTime | noData }}</span>
        </div>
        <div class="m-b-20">
          <div class="label m-b-20 font-size-14">组员情况</div>
          <div class="value">
            <drive-table
              ref="drive-table"
              max-height="calc(100vh - 700px)"
              :columns="detailColumn"
              :table-data="detail.teamList"
            />
          </div>
        </div>
        <div class="detail-box">
          <span class="label">成立时间</span>
          <span class="value">{{ detail.establishTime | noData }}</span>
        </div>
        <div class="detail-box">
          <span class="label">相关附件</span>
          <span class="value">
            <files-list
              v-if="detail.attach && detail.attach.informationAttach"
              :files="detail.attach.informationAttach"
              onlyForView
            />
            <span v-else class="value color-text-primary">暂无附件</span>
          </span>
        </div>
        <div class="detail-box">
          <span class="label p-t-4">备注描述</span>
          <span class="value line-height-22">{{ detail.remark | noData }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from './components/ModuleHeader/index.vue'
import ColumnMixins from './column'
import { getDetail } from './api'
import FilesList from '@/components/Uploader/files.vue'
export default {
  name: 'Equipment',
  components: {
    FilesList,
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      detail: {},
      id: ''
    }
  },
  created() {
    this.initData()
  },
  methods: {
    addHander() {
      this.$router.push({
        path: '/company/safety/addDevice'
      })
    },
    tabsChange(val) {
      this.current = val
      this.extralQuerys.deviceStatus = val
      this.$refs['drive-table']?.refreshTable()
    },
    async initData() {
      getDetail().then(res => {
        if (res.id) {
          this.detail = res
          this.id = res.id
        } else {
          this.$router.push({
            path: '/company/safety/addSecurityCouncil'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin-top: 16px;
}
.label {
  width: 70px;
  color: rgba(0, 0, 0, 0.4);
}
.card {
  margin-top: 24px;
  padding: 24px;
  background: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
  .detail {
    .detail-box {
      display: flex;
      margin-bottom: 26px;
      font-size: 14px;
      .label {
        width: 70px;
        color: rgba(0, 0, 0, 0.4);
      }
      .value {
        flex: 1;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
}
:deep(.el-table__body-wrapper) {
  max-height: calc(100vh - 743px);
}
</style>
