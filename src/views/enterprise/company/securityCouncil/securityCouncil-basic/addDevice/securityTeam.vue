<template>
  <div>
    <drive-table
      ref="drive-table"
      :columns="tableColumn"
      max-height="calc(100vh - 700px)"
      :table-data="tableList"
    >
      <template v-slot:operate-right>
        <el-popover
          placement="top-start"
          :title="title"
          width="352"
          trigger="manual"
          v-model="visible"
          ref="popoverRef"
        >
          <el-button
            type="primary"
            size="small"
            class="m-b-10"
            slot="reference"
            @click="visible = true"
          >
            <span>添加</span>
          </el-button>
          <driven-form
            v-if="visible"
            ref="driven-form"
            v-model="fromModel"
            :formConfigure="formConfigure"
          />
          <div class="flex align-items-center justify-content-end">
            <el-button type="info" size="mini" @click="visible = false"
              >取消</el-button
            >
            <el-button type="primary" @click="confirmPopover" size="mini"
              >确定</el-button
            >
          </div>
        </el-popover>
      </template>
    </drive-table>
  </div>
</template>

<script>
export default {
  name: 'SecurityTeam',
  props: {
    value: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      title: '添加安全小组',
      visible: false,
      fromModel: {},
      formConfigure: {
        descriptors: {
          name: {
            form: 'input',
            label: '姓名',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入姓名'
              },
              {
                max: 20,
                message: '最多输入20个字符'
              }
            ]
          },
          phone: {
            form: 'input',
            label: '联系方式',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              },
              {
                pattern: /^1[3-9]\d{9}$/,
                message: '手机号格式不正确',
                trigger: 'blur'
              }
            ]
          },
          type: {
            form: 'select',
            label: '组员类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择组员类型'
              }
            ],
            options: [
              {
                label: '组长',
                value: 1
              },
              {
                label: '组员',
                value: 2
              }
            ]
          }
        }
      },
      tableColumn: [
        {
          prop: 'name',
          label: '姓名'
        },
        {
          prop: 'phone',
          label: '联系方式'
        },
        {
          prop: 'type',
          label: '组员类型',
          render: (h, params) => {
            return <div>{params.row.type === 1 ? '组长' : '组员'}</div>
          }
        },
        {
          label: '操作',
          render: (h, params) => {
            return (
              <div class="operate">
                <el-button
                  type="text"
                  class="color-danger"
                  size="mini"
                  onClick={() => {
                    this.delFn(params.row)
                  }}
                >
                  删除
                </el-button>
              </div>
            )
          }
        }
      ],
      tableList: []
    }
  },
  methods: {
    delFn(row) {
      this.tableList = this.tableList.filter(item => item.id !== row.id)
      this.$emit('input', this.tableList)
    },
    confirmPopover() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          this.visible = false
          this.tableList.push({
            ...this.fromModel,
            id: Math.random().toString(36).substr(2)
          })
          this.$emit('input', this.tableList)
        }
      })
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.fromModel = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-table__body-wrapper) {
  max-height: calc(100vh - 743px);
}
</style>
