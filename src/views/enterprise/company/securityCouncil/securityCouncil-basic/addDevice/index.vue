<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="安全委员会"
      :isBtn="false"
      desc="组建安全委员会有序安全生产"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <div class="m-t-20 card">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
        <div class="flex justify-content-end m-b-20">
          <el-button type="success" v-if="!id" @click="goBack">重置</el-button>
          <el-button type="success" v-else @click="$router.go(-1)"
            >返回</el-button
          >
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DrivenForm from '@/components/DrivenForm'
import descriptorMixins from '../descriptor'
import ModuleHeader from '../components/ModuleHeader/index.vue'
import { postLabelCreate, postLabelUpdate, getDetail } from '../api/index'
export default {
  name: 'AddDevice',
  components: {
    ModuleHeader,
    DrivenForm
  },
  mixins: [descriptorMixins],
  data() {
    return {
      fromModel: {},
      id: this.$route.query.id
    }
  },
  mounted() {
    if (this.id) {
      this.getDetail()
    }
  },
  methods: {
    goBack() {
      this.fromModel = {}
      this.$refs.securityTeam.tableList = []
    },
    getDetail() {
      getDetail(this.id).then(res => {
        this.fromModel = {
          ...res,
          attach: res.attach && res.attach.informationAttach
        }
        if (this.$refs.securityTeam) {
          this.$refs.securityTeam.tableList = res.teamList
        }
      })
    },

    submit() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { attach } = this.fromModel
          const params = {
            ...this.fromModel
          }
          if (attach && attach.length > 0) {
            params.attach = attach.map(item => item.id)
          }
          if (this.id) {
            postLabelUpdate(params).then(() => {
              this.$toast.success('编辑成功')
              this.$router.go(-1)
            })
          } else {
            postLabelCreate(params).then(() => {
              this.$toast.success('添加成功')
              this.$router.go(-1)
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card {
  padding: 32px 66px 32px 26px;
  background-color: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
</style>
