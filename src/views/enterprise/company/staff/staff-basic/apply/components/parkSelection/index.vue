<template>
  <div class="p-b-32 p-b-16">
    <div class="line-wrapper">
      <driven-form
        ref="driven-form"
        v-model="formModel"
        :formConfigure="formConfigure"
      />
    </div>

    <div class="p-t-32">
      <driven-form
        ref="driven-form-recommend"
        v-model="formModel"
        :formConfigure="formConfigureRecommend"
      />
    </div>

    <!-- 入园须知 -->
    <dialog-cmp
      title="入园须知"
      :visible.sync="visible"
      width="35%"
      :haveOperation="false"
    >
      <div v-if="currentParkData">
        <div v-html="currentParkData.enterInfo"></div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import { getPark } from '../../api'

export default {
  name: 'EnterParkBasicSelection',
  mixins: [descriptorMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formModel: {},
      parkList: [],
      visible: false
    }
  },
  created() {
    this.getPark()
  },
  watch: {
    'formModel.recommend': {
      handler(val) {
        if (val === 0) {
          this.triggerFormHidden(true)
          this.clearFormValue()
        } else if (val === 1) {
          this.triggerFormHidden(false)
        }
      },
      immediate: true
    },
    applyData: {
      handler(val) {
        if (Object.keys(val).length) {
          this.formModel = {
            ...val,
            attachList: val.attachMap.enterParkRecommend || []
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    currentParkData() {
      const [list] = this.parkList.filter(
        item => item.id === this.formModel.parkId
      )
      return list
    }
  },
  methods: {
    triggerFormHidden(e) {
      this.formConfigureRecommend.descriptors.recommendOrganization.hidden = e
      this.formConfigureRecommend.descriptors.recommendContacts.hidden = e
      this.formConfigureRecommend.descriptors.attachList.hidden = e
    },

    clearFormValue() {
      delete this.recommendOrganization
      delete this.recommendContacts
      delete this.attachList
    },

    // 获取园区
    getPark() {
      getPark().then(res => {
        this.parkList = res
        this.formConfigure.descriptors.parkId.options = res.map(item => {
          return {
            label: item.park,
            value: item.id
          }
        })
      })
    },

    drivenFormSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    drivenFormRecommendSubmit() {
      return new Promise(resolve => {
        this.$refs['driven-form-recommend'].validate(valid => {
          if (valid) {
            resolve()
          } else {
            return false
          }
        })
      })
    },

    // 提交
    async saveEvent() {
      try {
        await Promise.all([
          this.drivenFormSubmit(),
          this.drivenFormRecommendSubmit()
        ])
      } catch (e) {
        return e
      }
    },

    // 查看入园须知
    openEnterInfo() {
      if (!this.formModel.parkId) return this.$toast.warning('请先选择意向园区')
      this.visible = true
    }
  }
}
</script>

<style lang="scss" scoped>
.line-wrapper {
  padding-bottom: 24px;
  border-bottom: 1px solid;
  @include border_color(--border-color-light);
}

.c {
  text-decoration: underline;
}
</style>
