<template>
  <div>
    <div class="flex justify-content-end m-b-17">
      <p class="font-size-12 flex align-items-center color-danger">
        <svg-icon icon-class="error-circle" class="m-r-4" />
        <span
          >注：零值或者负值也需如实填写；成立不满一年的企业填写上个季度经济指标</span
        >
      </p>
    </div>
    <el-table border stripe :data="tableData" style="width: 100%">
      <el-table-column prop="quarter" label="季度"></el-table-column>
      <el-table-column prop="turnover" label="营业额（万元）">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.turnover"
            placeholder="请输入营业额"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column prop="taxYield" label="税收总额（万元）">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.taxYield"
            placeholder="请输入税收总额"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column prop="netProfit" label="净利润（万元）">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.netProfit"
            placeholder="请输入净利润"
          ></el-input>
        </template>
      </el-table-column>

      <el-table-column prop="researchInput" label="研发投入（万元）">
        <template slot-scope="scope">
          <el-input
            v-model="scope.row.researchInput"
            placeholder="请输入研发投入"
          ></el-input>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'EnterParkBasicEconomicIndicators',
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    getEconomicQuarter(e) {
      this.tableData = e.map(item => {
        return {
          quarter: item,
          netProfit: '',
          researchInput: '',
          taxYield: '',
          turnover: ''
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
