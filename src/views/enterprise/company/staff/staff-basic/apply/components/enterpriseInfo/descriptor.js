import RegionCascader from '@/components/RegionCascader'

export default {
  components: {
    RegionCascader
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '150px',
        descriptors: {
          registered: {
            form: 'radio',
            label: '工商注册',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择工商注册'
              }
            ],
            options: [
              {
                label: '已工商注册',
                value: 1
              },
              {
                label: '暂未工商注册',
                value: 0
              }
            ]
          },
          enterParkBusiness: {
            form: 'component',
            label: '营业执照',
            span: 12,
            hidden: false,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传营业执照'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div>
                  请上传企业营业执照复印件（盖章版），要求扫描件清晰可辨
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'enterParkBusiness'
              },
              uploaderText: '上传营业执照',
              maxSize: 10
            },
            events: {
              success: e => {
                this.businessLicense(e)
              }
            }
          },
          noticeAttach: {
            form: 'component',
            label: '名称核准通知书',
            span: 12,
            hidden: true,
            rule: [
              {
                type: 'array',
                message: '请上传名称核准通知书'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return <div>要求扫描件清晰可辨</div>
            },
            props: {
              uploadData: {
                type: 'enterParkNotice'
              },
              uploaderText: '上传名称核准通知书',
              maxSize: 10
            }
          },
          name: {
            form: 'input',
            label: '企业名称',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ]
          },
          // name: {
          //   form: 'input',
          //   label: '拟定名称',
          //   span: 12,
          //   rule: [
          //     {
          //       required: true,
          //       type: 'string',
          //       message: '请输入拟定名称'
          //     }
          //   ]
          // },
          registerDateDraft: {
            form: 'date',
            label: '拟定注册时间',
            hidden: true,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择拟定注册时间'
              }
            ]
          },
          creditCode: {
            form: 'input',
            label: '统一社会信用代码',
            hidden: false,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入统一社会信用代码'
              }
            ]
          },
          registerDate: {
            form: 'input',
            label: '成立日期',
            hidden: false,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入工商成立日期'
              }
            ]
          },
          addressSelect: {
            form: 'input',
            label: '注册地址',
            hidden: false,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择注册地址'
              }
            ],
            render: (h, scope) => {
              return (
                <div>
                  <div class="m-b-22">
                    <RegionCascader
                      value={scope._value}
                      onInput={value => (scope._value = value)}
                    ></RegionCascader>
                  </div>
                  <div>
                    <el-input
                      value={this.formModel.businessInfoReqVO.address}
                      onInput={value =>
                        (this.formModel.businessInfoReqVO.address = value)
                      }
                      placeholder="请输入现注册地址"
                    ></el-input>
                  </div>
                </div>
              )
            }
          },
          capital: {
            form: 'input',
            label: '注册资本',
            hidden: false,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业注册资本'
              }
            ]
            // customRight: () => {
            //   return <div class="line-height-32 font-size-14">万元</div>
            // }
          },
          seat: {
            span: 12,
            hidden: false
          },
          industry: {
            form: 'select',
            label: '所属行业',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择所属行业'
              }
            ],
            options: []
          },
          property: {
            form: 'select',
            label: '企业性质',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择企业性质'
              }
            ],
            options: []
          },
          taxAscription: {
            form: 'input',
            label: '纳税归属地',
            hidden: false,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择公司纳税归属地'
              }
            ],
            options: [],
            render: (h, scope) => {
              return (
                <div>
                  <RegionCascader
                    value={scope._value}
                    onInput={value => (scope._value = value)}
                  ></RegionCascader>
                </div>
              )
            },
            customTips: () => {
              return (
                <div class="line-height-24 font-size-14">
                  <p>居民企业以企业登记注册地为纳税归属地</p>
                  <p>非居民企业，以机构、场所所在地为纳税归属地</p>
                </div>
              )
            }
          },
          honor: {
            form: 'select',
            label: '资质荣誉',
            hidden: false,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择企业所拥有的所有资质荣誉'
              }
            ],
            options: []
          },
          capitalDraft: {
            form: 'input',
            label: '拟注册资本',
            hidden: true,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入拟注册资本'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">万元</div>
            }
          }
        }
      },
      formConfigurePersonnel: {
        labelWidth: '150px',
        descriptors: {
          contacts: {
            form: 'input',
            label: '常用联系人姓名',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入常用联系人姓名'
              }
            ]
          },
          contactsPhone: {
            form: 'input',
            label: '常用联系人联系方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入常用联系人联系手机号',
                validator: 'validatePhone'
              }
            ]
          },
          manager: {
            form: 'input',
            label: '总经理或项目负责人姓名',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入总经理或项目负责人姓名'
              }
            ]
          },
          managerPhone: {
            form: 'input',
            label: '总经理或项目负责人联系方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入总经理或项目负责人联系方式',
                validator: 'validatePhone'
              }
            ]
          },
          legalPerson: {
            form: 'input',
            label: '法定代表人姓名',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入法定代表人姓名'
              }
            ]
          },
          legalPersonPhone: {
            form: 'input',
            label: '法定代表人联系方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入法定代表人手机号',
                validator: 'validatePhone'
              }
            ]
          }
        }
      },
      formConfigureStaff: {
        labelWidth: '150px',
        descriptors: {
          employeeNum: {
            form: 'input',
            label: '公司员工数',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入公司员工数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">人</div>
            }
          },
          attachStaff: {
            form: 'component',
            label: '员工证明',
            span: 12,
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div>
                  请提供近两个月社保缴纳证明（社保局官网查询打印并盖章）
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'enterParkStaff'
              },
              uploaderText: '点击上传证明材料',
              maxSize: 10
            }
          },
          juniorNum: {
            form: 'input',
            label: '大专及以上员工数',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入大专及以上员工数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">人</div>
            }
          },
          overseasNum: {
            form: 'input',
            label: '留学生人数',
            span: 12,
            rule: [
              {
                type: 'number',
                message: '请输入公司留学生人数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">人</div>
            }
          },
          researchNum: {
            form: 'input',
            label: '研发人数',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入公司研发人数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">人</div>
            }
          },
          seniorNum: {
            form: 'input',
            label: '高级职称人数',
            span: 12,
            rule: [
              {
                type: 'number',
                message: '请输入公司高级职称人数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">人</div>
            }
          }
        }
      },
      formConfigureTurnover: {
        labelWidth: '130px',
        descriptors: {
          turnoverAttach: {
            form: 'component',
            label: '营业额证明材料',
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '点击上传营业额证明材料'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div class="line-height-20 font-size-14">
                  营业额（证明材料）
                  产值数据需要提供企业的年度第三方财务审计报告或年度企业所得税申报表或年度企业所得税汇算清缴报告等第三方相关证明材料；要求扫描件清晰可辨；
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'enterParkTurnover'
              },
              uploaderText: '点击上传证明材料',
              maxSize: 10
            }
          }
        }
      }
    }
  }
}
