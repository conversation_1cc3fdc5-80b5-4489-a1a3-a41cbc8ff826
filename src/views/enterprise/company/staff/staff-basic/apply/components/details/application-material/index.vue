<template>
  <div class="p-b-32">
    <basic-card title="申请材料">
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :table-data="tableData"
      >
      </drive-table>
    </basic-card>

    <!-- 查看附件详情 -->
    <dialog-cmp
      title="附件详情"
      :visible.sync="visibleSee"
      width="30%"
      :haveOperation="false"
    >
      <div>
        <Uploader v-model="viewData" type="avatar" mulity onlyForView />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from './column'
export default {
  name: 'EnterParkBasicFile',
  mixins: [ColumnMixins],
  props: {
    applyData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      defaultAttachList: [
        '项目情况介绍(仅工商注册项目提供简介即可)',
        '公司法人代表及股东身份证明复印件如有法人股东需提供加盖公章的营业执照副本复印件',
        '入园申请材料真实性声明需签字盖章后上传'
      ], // 默认上传的附件
      visibleSee: false, // 查看附加详情
      currentIndex: 0,
      tableData: [],
      viewData: []
    }
  },
  watch: {
    applyData: {
      handler(val) {
        if (val.attachMap) {
          const { enterParkFile = [] } = val.attachMap
          if (enterParkFile.length) {
            const flags = []
            this.tableData = enterParkFile.reduce((pre, current) => {
              const { flag } = current
              if (flags.includes(flag)) {
                pre[flags.indexOf(flag)].list.push(current)
              } else {
                flags.push(flag)
                pre.push({
                  attachName: flag,
                  list: [current]
                })
              }
              return pre
            }, [])
          } else {
            this.tableData = this.defaultAttachList.map(item => {
              return {
                attachName: item,
                list: []
              }
            })
          }
        }
      },
      immediate: true,
      deep: true
    },
    visibleSee(val) {
      if (!val) {
        this.viewData = []
      }
    }
  },
  methods: {
    // 查看附件详情
    seeAttachDetails(scope) {
      const { row } = scope
      this.viewData = row.list
      this.visibleSee = true
    }
  }
}
</script>

<style></style>
