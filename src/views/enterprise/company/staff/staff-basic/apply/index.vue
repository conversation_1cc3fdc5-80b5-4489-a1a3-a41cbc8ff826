<template>
  <div>
    <module-header
      type="primary"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    >
      <div class="steps">
        <div
          class="m-b-12 font-size-20 font-strong color-text-primary line-height-28"
        >
          入园申请
        </div>
        <div>
          <steps :active="active" />
        </div>
      </div>
    </module-header>

    <div class="lateral-wrapper p-t-24 p-b-24">
      <form-card
        @previousStep="previousStep"
        @nextStep="nextStep"
        @saveEvent="saveEvent"
        @submitApply="submitApply"
        :active="active"
      >
        <component
          ref="componentForm"
          :is="componentName"
          :applyData="applyData"
        />
      </form-card>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Steps from './components/steps'
import ParkSelection from './components/parkSelection'
import FormCard from './components/formCard'
import EnterpriseInfo from './components/enterpriseInfo'
import ProjectInfo from './components/projectInfo'
import Demand from './components/demand'
import File from './components/file'
import {
  saveParkSelection,
  getEnterParkApplyInfo,
  saveEnterpriseInfo,
  saveProjectInfo,
  saveDemandInfo,
  submitApply,
  saveFileInfo
} from './api'

export default {
  name: 'EnterParkApply',
  components: {
    ModuleHeader,
    Steps,
    FormCard
  },
  data() {
    return {
      active: 0,
      applyData: {},
      componentName: null,
      id: null
    }
  },
  watch: {
    active: {
      handler() {
        const names = new Map()
        names.set(0, ParkSelection) //File
        names.set(1, EnterpriseInfo)
        names.set(2, ProjectInfo)
        names.set(3, Demand)
        names.set(4, File) //ParkSelection
        this.componentName = names.get(this.active)
      },
      immediate: true
    }
  },
  created() {
    const { id } = this.$route.query
    this.id = id
    this.id && this.getEnterParkApplyInfo()
  },
  methods: {
    // 上一步
    previousStep() {
      this.getEnterParkApplyInfo()
      this.active = this.active - 1
    },

    // 下一步
    nextStep() {
      this.saveEvent().then(() => {
        this.active = this.active + 1
      })
    },

    // 提交申请
    submitApply() {
      this.saveApplyFile().then(() => {
        this.$confirm('确定提交入园申请？').then(() => {
          const { id } = this.applyData
          submitApply(id).then(() => {
            this.$toast.success('提交成功')
            this.$router.go(-1)
          })
        })
      })
    },

    // 保存
    saveEvent() {
      return new Promise(resolve => {
        const saveForm = this.$refs.componentForm
        const { active } = this
        const { formModel } = saveForm
        const { id = '', attachMap } = this.applyData
        if (active === 0) {
          saveForm.saveEvent().then(() => {
            const { attachList } = formModel
            const params = {
              id,
              parkApplyReqVO: { ...formModel }
            }

            let list = attachList || []
            for (let key in attachMap) {
              if (key !== 'enterParkRecommend') {
                list = list.concat(attachMap[key])
              }
            }
            if (list && list.length > 0) {
              params.attachIds = list.map(item => item.id)
            }
            saveParkSelection(params).then(res => {
              this.id = res
              this.$toast.success('保存成功')
              this.getEnterParkApplyInfo()
              resolve()
            })
          })
        } else if (active === 1) {
          saveForm.saveEvent().then(() => {
            const stockRightTableData = saveForm.$refs['stock-right'].tableData
            if (stockRightTableData.length === 0)
              return this.$toast.warning('请先完善股东信息')
            const total = stockRightTableData.reduce((prev, curr) => {
              return prev + curr.stockRatio
            }, 0)
            if (total > 100)
              return this.$toast.warning('持股比例总和不能大于100%')

            const { businessInfoReqVO, basicInfoReqVO, turnoverAttach } =
              formModel
            const {
              registered,
              name,
              enterParkBusiness,
              noticeAttach,
              registerDateDraft,
              creditCode,
              registerDate,
              addressSelect,
              address,
              capital,
              industry,
              property,
              taxAscription,
              honor,
              capitalDraft
            } = businessInfoReqVO

            const economicIndicatorsTableData =
              saveForm.$refs['economic-indicators'].tableData
            const param = {
              applyId: id,
              basicInfoReqVO,
              businessInfoReqVO: {},
              economicInfoReqVO: economicIndicatorsTableData,
              stockRightsReqVO: stockRightTableData
            }
            const { attachStaff = [] } = basicInfoReqVO
            const { enterParkRecommend = [], enterParkFile = [] } = attachMap
            if (registered === 1) {
              const [regProvince, regCity, regDistrict] =
                addressSelect.split(',')
              const [taxProvince, taxCity, taxDistrict] =
                taxAscription.split(',')
              param.businessInfoReqVO = {
                registered,
                name,
                creditCode,
                registerDate,
                address,
                capital,
                industry,
                property,
                honor,
                regProvince,
                regCity,
                regDistrict,
                taxProvince,
                taxCity,
                taxDistrict
              }

              const attachList = [
                ...enterParkBusiness,
                ...turnoverAttach,
                ...attachStaff,
                ...enterParkRecommend,
                ...enterParkFile
              ]
              param.attachIds = attachList.map(item => item.id)
            } else {
              param.businessInfoReqVO = {
                registered,
                name,
                capital: capitalDraft,
                registerDate: registerDateDraft,
                industry,
                property
              }

              param.attachIds = [
                ...attachStaff,
                ...noticeAttach,
                ...enterParkRecommend,
                ...enterParkFile
              ].map(item => item.id)
            }
            if (registered === 1) {
              const len = economicIndicatorsTableData.length
              let flag = false
              for (let i = 0; i < len; i++) {
                for (let key in economicIndicatorsTableData[i]) {
                  if (
                    economicIndicatorsTableData[i][key] === '' ||
                    (economicIndicatorsTableData[i][key] === null &&
                      key !== 'remark')
                  ) {
                    flag = true
                    continue
                  }
                }
              }
              if (flag) return this.$toast.warning('请先完善主要经济指标信息')
            }

            saveEnterpriseInfo(param).then(() => {
              this.$toast.success('保存成功')
              this.getEnterParkApplyInfo()
              resolve()
            })
          })
        } else if (active === 2) {
          saveForm.saveEvent().then(() => {
            const params = {
              applyId: id,
              projectInfoReqVO: {
                ...formModel
              }
            }
            saveProjectInfo(params).then(() => {
              this.$toast.success('保存成功')
              this.getEnterParkApplyInfo()
              resolve()
            })
          })
        } else if (active === 3) {
          saveForm.saveEvent().then(() => {
            const {
              expectArea,
              expectEnterDate,
              housingPurpose,
              bearing,
              houseAdditionDemand,
              financingDemand,
              acceptableFinancingWay,
              policyDemand,
              serverDemand
            } = formModel
            const params = {
              parkApplyReqVO: {
                id,
                expectArea,
                expectEnterDate,
                housingPurpose,
                bearing,
                houseAdditionDemand: houseAdditionDemand
                  ? houseAdditionDemand.join(',')
                  : '',
                financingDemand,
                acceptableFinancingWay: acceptableFinancingWay
                  ? acceptableFinancingWay.join(',')
                  : '',
                policyDemand,
                serverDemand: serverDemand ? serverDemand.join(',') : ''
              }
            }
            saveDemandInfo(params).then(() => {
              this.$toast.success('保存成功')
              this.getEnterParkApplyInfo()
              resolve()
            })
          })
        } else if (active === 4) {
          this.saveApplyFile().then(() => {
            this.$toast.success('保存成功')
            this.getEnterParkApplyInfo()
          })
        }
      })
    },

    // 保存申请材料
    saveApplyFile() {
      return new Promise(resolve => {
        const saveForm = this.$refs.componentForm
        const { id = '', attachMap } = this.applyData
        saveForm.submitForm().then(res => {
          let list = []
          for (let key in attachMap) {
            if (key !== 'enterParkFile') {
              list = list.concat(attachMap[key])
            }
          }
          const params = {
            applyId: id,
            attachIds: list.map(item => item.id).concat(res)
          }
          saveFileInfo(params).then(() => {
            resolve()
          })
        })
      })
    },

    // 获取入园申请详情
    getEnterParkApplyInfo() {
      getEnterParkApplyInfo(this.id).then(res => {
        this.applyData = res || {}
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.steps {
  padding-top: 45px;
}
</style>
