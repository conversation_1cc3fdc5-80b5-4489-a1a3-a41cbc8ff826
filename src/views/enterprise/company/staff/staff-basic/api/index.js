import request from '@/utils/request'

// 获取入园申请列表
export function getEmployeeManager(params) {
  return request({
    url: `/employeeManager/entPageList`,
    method: 'get',
    params
  })
}

// 同意，离职，拒绝），园区端（拉黑）
export function postStatus(data) {
  return request({
    url: `/employeeManager/updateEmpStatus`,
    method: 'post',
    data
  })
}

//员工类型字典
export function getEmployeeType() {
  return request({
    url: `/dict/tenantDictData/getByDictType?dictType=emp_type`,
    method: 'get'
  })
}
