export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          theme: {
            form: 'input',
            label: '演练主题',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入演练主题'
              },
              {
                max: 20,
                message: '请输入演练主题、不超过20字'
              }
            ]
          },
          contact: {
            form: 'input',
            label: '演练负责人',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入演练负责人'
              },
              {
                max: 20,
                message: '请输入演练负责人、不超过20字'
              }
            ]
          },
          type: {
            form: 'select',
            label: '演练类型',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择演练类型'
              }
            ],
            options: []
          },
          drillTime: {
            form: 'date',
            label: '演练时间',
            span: 12,
            rule: [
              {
                type: 'string',
                message: '请选择演练时间'
              }
            ]
          },
          parkId: {
            form: 'select',
            label: '演练地点',

            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择园区'
              }
            ],
            options: [],
            events: {
              change: e => this.changePark(e)
            }
          },
          buildingId: {
            form: 'select',
            label: ' ',
            span: 12,
            rule: [
              {
                type: 'number',
                message: '请选择楼栋'
              }
            ],
            options: [],
            events: {
              change: e => this.changeBuilding(e)
            }
          },
          floorId: {
            form: 'select',
            span: 12,
            label: ' ',
            rule: [
              {
                type: 'number',
                message: '请选择楼层'
              }
            ],
            options: []
          },
          address: {
            form: 'input',
            label: '具体位置',
            span: 24,
            rule: [
              {
                required: true,
                type: 'string',
                message: '可补充详细的区域描述'
              },
              {
                max: 50,
                message: '请输入不超过50字'
              }
            ]
          },
          objective: {
            form: 'input',
            label: '演练目的',
            rule: [
              {
                type: 'string',
                message: '请输入演练目的'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 6 }
            }
          }
        }
      },
      fooFormConfigure: {
        labelWidth: '110px',
        descriptors: {
          description: {
            form: 'input',
            label: '主要内容',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主要内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 6 }
            }
          },
          attach: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <div class="line-height-32 color-info font-size-14">
                  <div style={'position: absolute; left: 216px'}>
                    请上传不大于10MB的附件,附件不得超过三个
                  </div>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          }
        }
      }
    }
  }
}
