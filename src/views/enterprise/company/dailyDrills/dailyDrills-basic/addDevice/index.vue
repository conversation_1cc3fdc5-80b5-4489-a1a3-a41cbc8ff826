<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      :title="title"
      desc="长伴安全演练有限保障"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <div class="m-t-20 card">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
      </div>
      <div class="m-t-20 card-foo">
        <basic-tab
          ref="basicTab"
          :tabs-data="list"
          :current="current"
          @tabsChange="tabsChange"
        />
        <div class="p-r-24">
          <div v-for="item in list" :key="item.value">
            <driven-form
              v-if="item.value === current"
              ref="driven-form-foo"
              v-model="item.fooFromModel"
              :formConfigure="fooFormConfigure"
            />
          </div>
          <div class="flex justify-content-end m-b-20">
            <el-button type="info" @click="goBack">取消</el-button>
            <el-button type="primary" @click="submit">确定</el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DrivenForm from '@/components/DrivenForm'
import descriptorMixins from '../descriptor'
import ModuleHeader from '@/components/Lateral/ModuleHeader/index.vue'
import {
  getBuildingSelect,
  getFloorSelect,
  getParkSelect,
  postLabelCreate,
  postLabelUpdate,
  getDetail,
  getEnumList,
  getFlowList
} from '../api/index'
import BasicTab from '@/components/BasicTab/index.vue'
export default {
  name: 'DailyDrillsAdd',
  components: {
    BasicTab,
    ModuleHeader,
    DrivenForm
  },
  mixins: [descriptorMixins],
  data() {
    return {
      isShow: true,
      fromModel: {},
      current: 0,
      list: [],
      detail: {},
      // fooFromModel: {},
      title: this.$route.query.id ? '编辑演练' : '添加演练'
    }
  },
  created() {
    this.getFlowList()
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  mounted() {
    this.getParkSelect()
    this.getEnumList()
  },
  methods: {
    getFlowList() {
      getFlowList().then(res => {
        this.list = res.map(item => {
          return {
            label: item.label,
            value: item.value,
            fooFromModel: {}
          }
        })
        this.current = this.list[0].value
      })
    },
    tabsChange(e) {
      this.current = e
      //清除driven-form-foo表单校验
    },
    getEnumList() {
      getEnumList().then(res => {
        this.formConfigure.descriptors.type.options = res.map(item => {
          return { label: item.label, value: item.value }
        })
      })
    },
    getDetail() {
      getDetail(this.$route.query.id).then(res => {
        this.detail = res
        if (res.parkId) {
          this.buildingSelect(res.parkId)
        }
        if (res.buildingId) {
          this.getFloorSelect(res.buildingId)
        }
        this.fromModel = {
          ...res,
          parkId: res.parkId,
          buildingId: res.buildingId,
          floorId: res.floorId
        }
        this.list = this.list.map((item, inx) => {
          item.fooFromModel = {
            description: res.flowList[inx].description,
            attach:
              res.flowList[inx].attach &&
              res.flowList[inx].attach.informationAttach
          }
          return item
        })
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    // 获取园区
    async getParkSelect() {
      const res = await getParkSelect()
      this.formConfigure.descriptors.parkId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 获取楼层
    async getFloorSelect(e) {
      const res = await getFloorSelect(e)
      this.formConfigure.descriptors.floorId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    async buildingSelect(e) {
      const res = await getBuildingSelect(e)
      this.formConfigure.descriptors.buildingId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 切换园区
    async changePark(e) {
      const res = await getBuildingSelect(e)
      this.formConfigure.descriptors.buildingId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.fromModel.buildingId = ''
      this.fromModel = {
        ...this.fromModel,
        floorId: ''
      }
    },
    // 切换楼栋
    async changeBuilding(e) {
      const res = await getFloorSelect(e)
      this.formConfigure.descriptors.floorId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.fromModel = {
        ...this.fromModel,
        floorId: ''
      }
    },
    submit() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          this.$refs['driven-form-foo'][0].validate(val => {
            if (val) {
              const { id } = this.fromModel
              const params = {
                ...this.fromModel,
                buildingId:
                  typeof this.fromModel.buildingId === 'string'
                    ? this.detail.buildingId
                    : this.fromModel.buildingId,
                floorId:
                  typeof this.fromModel.floorId === 'string'
                    ? this.detail.floorId
                    : this.fromModel.floorId
              }
              this.list.map(item => {
                const { description, attach } = item.fooFromModel
                item.name = item.label
                item.description = description
                if (attach && attach.length > 0) {
                  item.attach = attach.map(t => t.id)
                }
                return item
              })
              console.log(this.list)
              params.flowList = this.list
              if (id) {
                postLabelUpdate(params).then(() => {
                  this.$toast.success('编辑成功')
                  this.$router.go(-1)
                })
              } else {
                postLabelCreate(params).then(() => {
                  this.$toast.success('添加成功')
                  this.$router.go(-1)
                })
              }
            }
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card {
  padding: 32px 66px 32px 26px;
  background-color: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
.card-foo {
  padding: 10px 0px 10px 0px;
  margin-bottom: 24px;
  background-color: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
</style>
