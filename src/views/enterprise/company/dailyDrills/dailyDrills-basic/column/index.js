export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'name',
          label: '设备名称'
        },
        {
          prop: 'deviceBrandStr',
          label: '设备品牌'
        },
        {
          prop: 'deviceTypeStr',
          label: '设备类型'
        },
        {
          prop: 'addressInfo',
          label: '存放地点'
        },
        {
          prop: 'inspectionStr',
          label: '检验周期'
        },
        {
          prop: 'nextInspection',
          label: '下次检验时间'
        },
        {
          prop: 'deviceStatus',
          label: '状态',
          render: (h, { row }) => {
            return (
              <div
                class={row.deviceStatus < 2 ? 'color-success' : 'color-danger'}
              >
                {row.deviceStatusStr}
              </div>
            )
          }
        },
        {
          label: '操作',
          fixed: 'right',
          width: 80,
          render: (h, { row }) => {
            return (
              <div>
                <el-link
                  type="text"
                  class="color-primary"
                  onClick={() => {
                    this.goDetail(row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
