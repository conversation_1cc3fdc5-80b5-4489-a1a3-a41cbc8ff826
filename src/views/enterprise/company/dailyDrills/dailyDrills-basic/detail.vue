<template>
  <div>
    <module-header
      type="primary"
      title="演练详情"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    >
    </module-header>
    <div class="lateral-wrapper">
      <!-- 内容区域 -->
      <div class="module-list">
        <div class="info">
          <div class="detail">
            <div class="detail-box">
              <span class="label">演练主题</span>
              <span class="value">{{ detail.theme | noData }}</span>
            </div>
            <div class="flex">
              <div class="detail-box" style="flex: 1">
                <span class="label">演练类型</span>
                <span class="value">{{ detail.typeStr | noData }}</span>
              </div>
              <div class="detail-box" style="width: 400px">
                <span class="label">演练时间</span>
                <span class="value">{{ detail.drillTime | noData }}</span>
              </div>
            </div>
            <div class="flex">
              <div class="detail-box" style="flex: 1">
                <span class="label">演练地点</span>
                <span class="value">{{ detail.location | noData }}</span>
              </div>
              <div class="detail-box" style="width: 412px">
                <span class="label m-r-13">演练负责人</span>
                <span class="value">{{ detail.contact | noData }}</span>
              </div>
            </div>
            <div class="detail-box">
              <span class="label">演练目的</span>
              <span class="value">{{ detail.objective | noData }}</span>
            </div>
            <div class="p-t-12">
              <div class="label">演练基本流程</div>
              <div v-for="item in detail.flowList" :key="item.id">
                <div class="label m-t-32 m-b-32">{{ item.name }}</div>
                <div class="value m-b-24 line-height-22">
                  {{ item.description }}
                </div>
                <files-list
                  v-if="item.attach && item.attach.informationAttach"
                  :files="item.attach.informationAttach"
                  onlyForView
                />
                <div v-else class="value color-text-primary">暂无附件</div>
              </div>
            </div>
          </div>
          <div class="footer">
            <div>
              <el-button type="danger" @click="goDel">删除</el-button>
              <el-button type="primary" @click="goAdd">编辑</el-button>
              <el-button type="success" @click="submit" v-if="status === '1'"
                >提交</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FilesList from '@/components/Uploader/files'
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import { getDetail, delDevice, postSubmit } from './api'

export default {
  name: 'DailyDrillsDetail',
  components: {
    ModuleHeader,
    FilesList
  },
  data() {
    return {
      id: this.$route.query.id,
      detail: {},
      status: this.$route.query.status
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    submit() {
      postSubmit(this.id).then(() => {
        this.$message({
          type: 'success',
          message: '提交成功!'
        })
        this.$router.push({
          path: '/company/safety/dailyDrills'
        })
      })
    },
    goAdd() {
      this.$router.push({
        path: '/company/safety/dailyDrillsAdd',
        query: {
          id: this.id
        }
      })
    },
    getDetail() {
      getDetail(this.id).then(res => {
        this.detail = res
      })
    },
    goDel() {
      this.$confirm('是否删除该条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDevice(this.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.$router.push({
            path: '/company/safety/dailyDrills'
          })
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.label {
  width: 120px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
.value {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.module-list {
  margin-top: 16px;
  .info {
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #e9f0ff;
    .detail {
      padding: 28px 32px 28px 32px;
      .detail-box {
        display: flex;
        margin-bottom: 22px;
        font-size: 14px;
        line-height: 22px;
        .label {
          width: 70px;
          color: rgba(0, 0, 0, 0.4);
        }
        .value {
          flex: 1;
          color: rgba(0, 0, 0, 0.9);
        }
      }
    }
    .footer {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      padding: 16px 32px 16px 32px;
      border-top: 1px solid #e9f0ff;
    }
  }
}
</style>
