import request from '@/utils/request'

// 列表
export function getPage(params) {
  return request({
    url: `/ck/ent/drills/page`,
    method: 'get',
    params
  })
}

//详情
export function getDetail(id) {
  return request({
    url: `/ck/ent/drills/get?id=${id}`,
    method: 'get'
  })
}

// 删除
export function delDevice(id) {
  return request({
    url: `/ck/ent/drills/delete?id=${id}`,
    method: 'delete'
  })
}
export function postLabelCreate(data) {
  return request({
    url: `/ck/ent/drills/create`,
    method: 'post',
    data
  })
}
export function postLabelUpdate(data) {
  return request({
    url: `/ck/ent/drills/update`,
    method: 'put',
    data
  })
}
export function postSubmit(data) {
  return request({
    url: `/ck/ent/drills/submit?id=${data}`,
    method: 'get'
  })
}
// 获得楼栋
export function getBuildingSelect(params) {
  return request({
    url: `/housing/building/select/${params}`,
    method: 'get'
  })
}
// 获得楼层
export function getFloorSelect(params) {
  return request({
    url: `/housing/floor/select/${params}`,
    method: 'get'
  })
}

// 获得园区
export function getParkSelect(params) {
  return request({
    url: `housing/park/select`,
    method: 'get',
    params
  })
}
// 演练类型
export function getEnumList() {
  return request({
    url: `/ck/drills/get_enum_list`,
    method: 'get'
  })
}

// 获取演练流程
export function getFlowList() {
  return request({
    url: `/ck/drills/get_flow_list`,
    method: 'get'
  })
}
