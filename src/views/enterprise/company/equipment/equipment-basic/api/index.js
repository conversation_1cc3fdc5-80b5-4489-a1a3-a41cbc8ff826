import request from '@/utils/request'

// 获取设备列表
export function getPage(params) {
  return request({
    url: `/device/info/ent/page`,
    method: 'get',
    params
  })
}
// 获得设备状态;
export function getStatus(params) {
  return request({
    url: `/device/common/status`,
    method: 'get',
    params
  })
}
// 新增设备类型，设备品牌
export function postListLog(data) {
  return request({
    url: `/device/common/label_create`,
    method: 'post',
    data
  })
}

// 获取设备类型，设备品牌
export function getLabelList(data) {
  return request({
    url: `/device/common/label_list`,
    method: 'post',
    data
  })
}
// 获得楼栋
export function getBuildingSelect(params) {
  return request({
    url: `/housing/building/select/${params}`,
    method: 'get'
  })
}
// 获得楼层
export function getFloorSelect(params) {
  return request({
    url: `/housing/floor/select/${params}`,
    method: 'get'
  })
}

// 获得园区
export function getParkSelect(params) {
  return request({
    url: `housing/park/select`,
    method: 'get',
    params
  })
}

// 获得检验周期;
export function getInspection(params) {
  return request({
    url: `/device/common/inspection`,
    method: 'get',
    params
  })
}
// 新增设备类型，设备品牌
export function postLabelCreate(data) {
  return request({
    url: `/device/info/ent/create`,
    method: 'post',
    data
  })
}

// 编辑设备类型，设备品牌
export function postLabelUpdate(data) {
  return request({
    url: `/device/info/ent/update`,
    method: 'post',
    data
  })
}
// 详情接口;
export function getDetail(id) {
  return request({
    url: `/device/info/ent/detail/${id}`,
    method: 'get'
  })
}
// 获取设备记录
export function getHistoryLog(data) {
  return request({
    url: `/device/common/list_log`,
    method: 'post',
    data
  })
}
