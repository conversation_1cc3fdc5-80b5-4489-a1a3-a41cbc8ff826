// 企业端状态【1-方案起草中；2-已撤销；3-方案审核中；4-申请已拒绝；5-已通过】
export function getReplaceEntStatus(h, val) {
  switch (val) {
    case 1:
      return { type: 'success', label: '方案起草中' }
    case 3:
      return { type: 'primary', label: '方案审核中' }
    case 5:
      return { type: 'success', label: '已通过' }
    case 2:
      return { type: 'danger', label: '已撤销' }
    case 4:
      return { type: 'danger', label: '申请已拒绝' }
    default:
      return { type: 'primary', label: '-' }
  }
}

//管理端状态【1-方案起草中；2-申请已拒绝；3-审核中；4-已通过；5-方案已退回】
export function getReplaceBsStatus(h, val) {
  switch (val) {
    case 1:
      return { type: 'success', label: '方案起草中' }
    case 3:
      return { type: 'primary', label: '审核中' }
    case 4:
      return { type: 'success', label: '已通过' }
    case 2:
      return { type: 'danger', label: '申请已拒绝' }
    case 5:
      return { type: 'danger', label: '方案已退回' }
    default:
      return { type: 'primary', label: '-' }
  }
}

export function getReplaceType(val) {
  switch (val) {
    case 1:
      return '新增房源'
    case 2:
      return '调整房源'
    case 3:
      return '退房'
    default:
      return '-'
  }
}

//变更类型【1-新增房源；2-调整房源；3-退房】
export const typeOptions = [
  {
    label: '新增房源',
    value: 1
  },
  {
    label: '调整房源',
    value: 2
  },
  {
    label: '退房',
    value: 3
  }
]
