import DictPopover from '../components/DictPopover'
export default {
  components: {
    DictPopover
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          name: {
            form: 'input',
            label: '设备名称',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入设备名称'
              },
              {
                max: 20,
                message: '请输入设备名称、不超过20字'
              }
            ]
          },
          deviceStatus: {
            form: 'select',
            label: '设备状态',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择设备状态'
              }
            ],
            options: []
          },
          deviceType: {
            form: 'select',
            label: '设备类型',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择设备类型'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <dict-popover
                    title={'设备类型'}
                    type="course_type"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          deviceBrand: {
            form: 'select',
            label: '设备品牌',
            span: 12,
            rule: [
              {
                type: 'number',
                message: '请选择设备品牌'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <dict-popover
                    title={'设备品牌'}
                    type="course_type"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          manufacture: {
            form: 'date',
            label: '生产日期',
            span: 12,
            rule: [
              {
                type: 'string',
                message: '请选择生产日期'
              }
            ]
          },
          yearLimit: {
            form: 'input',
            label: '使用年限',
            span: 12,
            rule: [
              {
                type: 'string',
                message: '请输入使用年限'
              },
              {
                pattern: /^[1-9][0-9]?$/,
                message: '请输入1-99的整数'
              }
            ],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <span class="m-r-10">年</span>
                </div>
              )
            }
          },
          inspection: {
            form: 'select',
            label: '检验周期',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择检验周期'
              }
            ],
            options: []
          },
          nextInspection: {
            form: 'date',
            label: '下次检验时间',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择下次检验时间'
              }
            ]
          },
          parkId: {
            form: 'select',
            label: '存放地点',

            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择园区'
              }
            ],
            options: [],
            events: {
              change: e => this.changePark(e)
            }
          },
          buildingId: {
            form: 'select',
            label: ' ',
            span: 12,
            rule: [
              {
                type: 'number',
                message: '请选择楼栋'
              }
            ],
            options: [],
            events: {
              change: e => this.changeBuilding(e)
            }
          },
          floorId: {
            form: 'select',
            span: 12,
            label: ' ',
            rule: [
              {
                type: 'number',
                message: '请选择楼层'
              }
            ],
            options: []
          },
          address: {
            form: 'input',
            label: '具体位置',
            span: 24,
            rule: [
              {
                required: true,
                type: 'string',
                message: '可补充详细的区域描述'
              },
              {
                max: 50,
                message: '请输入不超过50字'
              }
            ]
          },
          parameters: {
            form: 'input',
            label: '设备参数',
            rule: [
              {
                type: 'string',
                message: '请输入设备参数'
              }
            ],
            //类型是textarea时，需要设置rows
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 6 }
            }
          }
        }
      }
    }
  }
}
