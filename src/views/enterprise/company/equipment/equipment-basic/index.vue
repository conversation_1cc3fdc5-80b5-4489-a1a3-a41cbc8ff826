<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="设备管理"
      desc="及时更新设施设备是每个企业安全生产的首要责任"
      :img="require('@/assets/images/enterprise/company/safety/header-bg.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper card">
      <basic-tab
        ref="basicTab"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
      />
      <div class="add">
        <el-button type="primary" size="small" @click="addHander">
          <svg-icon icon-class="add" />
          <span>添加设备</span>
        </el-button>
      </div>
      <!-- 内容区域 -->
      <div class="module-list">
        <div class="table-data">
          <drive-table
            ref="drive-table"
            :columns="tableColumn"
            height="calc(100vh - 480px)"
            :extral-querys="extralQuerys"
            :api-fn="getPage"
          >
          </drive-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from './column'
import { getPage, getStatus } from './api'
import BasicTab from '@/components/BasicTab/index.vue'
export default {
  name: 'Equipment',
  components: {
    BasicTab,
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      current: 0,
      extralQuerys: {
        type: 1,
        deviceStatus: 0
      },
      list: [],
      getPage,
      isEnterpriseAuth: false,
      showApply: false,
      enterParkStatus: null
    }
  },
  created() {
    this.initData()
  },
  methods: {
    addHander() {
      this.$router.push({
        path: '/company/safety/addDevice'
      })
    },
    tabsChange(val) {
      this.current = val
      this.extralQuerys.deviceStatus = val
      this.$refs['drive-table']?.refreshTable()
    },
    async initData() {
      const res = await getStatus()
      this.list = res.map(item => {
        return {
          label: item.label,
          value: item.key
        }
      })
    },
    goDetail(val) {
      this.$router.push({
        path: '/company/safety/equipmentDetail',
        query: {
          id: val.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin-top: 16px;
}
.card {
  margin-top: 24px;
  position: relative;
  background: #ffffff;
  border-radius: 6px 6px 6px 6px;
  padding-top: 35px;
  padding-bottom: 35px;
  opacity: 1;
  border: 1px solid #e9f0ff;
}
.table-data {
  margin: 0 48px;
}
.add {
  position: absolute;
  right: 46px;
  top: 30px;
}
</style>
