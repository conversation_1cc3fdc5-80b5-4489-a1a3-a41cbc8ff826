<template>
  <div>
    <module-header
      type="primary"
      title="设备详情"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    >
    </module-header>
    <div class="lateral-wrapper">
      <!-- 内容区域 -->
      <div class="module-list">
        <div class="info">
          <div class="flex align-items-center justify-content-between">
            <span>{{ detail.name | noData }}</span>
            <span>
              <el-button type="primary" @click="goAdd">编辑</el-button>
            </span>
          </div>
          <div class="flex m-t-28">
            <div style="flex: 0.33">
              <span class="label">设备品牌</span>
              <span class="value">{{ detail.deviceBrandStr | noData }}</span>
            </div>
            <div style="flex: 0.33">
              <span class="label label-w">设备类型</span>
              <span class="value">{{ detail.deviceTypeStr | noData }}</span>
            </div>
            <div style="flex: 0.33">
              <span class="label">设备状态</span>
              <span class="value">{{ detail.deviceStatusStr | noData }}</span>
            </div>
          </div>
          <div class="flex m-t-24">
            <div style="flex: 0.33">
              <span class="label">检验周期</span>
              <span class="value">{{ detail.inspectionStr | noData }}</span>
            </div>
            <div style="flex: 0.33">
              <span class="label label-w">下次检验时间</span>
              <span class="value">{{ detail.nextInspection | noData }}</span>
            </div>
            <div style="flex: 0.33">
              <span class="label">所属企业</span>
              <span class="value">{{ detail.entName | noData }}</span>
            </div>
          </div>
          <div class="m-t-24">
            <div class="flex">
              <span class="label">存放地点</span>
              <span class="value">{{ detail.addressInfo | noData }}</span>
            </div>
          </div>
          <div class="m-t-24">
            <div class="des">
              <span class="label">设备描述</span>
              <span class="value">{{ detail.deviceInfo | noData }}</span>
            </div>
          </div>
        </div>
        <div class="his-info">
          <basic-tab
            ref="basicTab"
            :tabs-data="list"
            :current="current"
            @tabsChange="tabsChange"
          />
          <div class="his-main" v-if="isShow">
            <record :history-list="historyList" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab/index.vue'
import Record from './components/record'
import { getDetail, getHistoryLog } from './api'

export default {
  name: 'EquipmentDetail',
  components: {
    BasicTab,
    ModuleHeader,
    Record
  },
  data() {
    return {
      isShow: true,
      list: [
        {
          label: '巡检历史',
          value: 0
        },
        {
          label: '维保历史',
          value: 1
        },
        {
          label: '安全督察',
          value: 2
        }
      ],
      historyList: [],
      current: 0,
      id: this.$route.query.id,
      detail: {}
    }
  },
  created() {
    this.getDetail()
    this.getHistoryLog()
  },
  methods: {
    goAdd() {
      this.$router.push({
        path: '/company/safety/addDevice',
        query: {
          id: this.id
        }
      })
    },
    getHistoryLog() {
      getHistoryLog({ type: this.current, deviceId: this.id }).then(res => {
        this.historyList = res || []
        this.isShow = true
      })
    },
    getDetail() {
      getDetail(this.id).then(res => {
        this.detail = res
      })
    },
    tabsChange(index) {
      this.isShow = false
      this.current = index
      this.getHistoryLog()
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin-top: 16px;
  .info {
    padding: 28px 32px;
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #e9f0ff;
    .label {
      font-size: 14px;
      color: #bcbcbc;
      margin-right: 13px;
      width: 60px;
      display: inline-block;
      vertical-align: top;
    }
    .des {
      display: grid;
      grid-template-columns: 60px 1fr;
      grid-gap: 13px;
      line-height: 22px;
    }
    .value {
      font-size: 14px;
      display: inline-block;
      word-break: break-all;
      word-wrap: break-word;
      white-space: normal;
      overflow: hidden;
      color: #1f1f1f;
    }
    .label-w {
      width: 90px;
      text-align: right;
    }
  }
  .his-info {
    margin-top: 10px;
    padding: 10px 0;
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #e9f0ff;
    .his-main {
      padding: 0 24px;
    }
  }
}
</style>
