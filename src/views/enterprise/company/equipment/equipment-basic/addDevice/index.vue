<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      :title="title"
      desc="及时更新设施设备是每个企业安全生产的首要责任"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <div class="m-t-20 card">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
        <div class="flex justify-content-end m-b-20">
          <el-button type="success" @click="goBack">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DrivenForm from '@/components/DrivenForm'
import descriptorMixins from '../descriptor'
import ModuleHeader from '@/components/Lateral/ModuleHeader/index.vue'
import {
  getLabelList,
  getBuildingSelect,
  getFloorSelect,
  getInspection,
  getParkSelect,
  getStatus,
  postLabelCreate,
  postLabelUpdate,
  getDetail
} from '../api/index'
export default {
  name: 'AddDevice',
  components: {
    ModuleHeader,
    DrivenForm
  },
  mixins: [descriptorMixins],
  data() {
    return {
      fromModel: {},
      detail: {},
      title: this.$route.query.id ? '编辑设备' : '添加设备'
    }
  },
  created() {
    this.getOptionAll()
    this.getStatus()
    this.getInspection()
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  mounted() {
    this.getParkSelect()
  },
  methods: {
    getDetail() {
      getDetail(this.$route.query.id).then(res => {
        this.detail = res
        if (res.parkId) {
          this.getBuildingSelect(res.parkId)
        }
        if (res.buildingId) {
          this.getFloorSelect(res.buildingId)
        }
        this.fromModel = {
          ...res,
          yearLimit: String(res.yearLimit),
          parkId: res.parkId,
          buildingId: res.buildingId,
          floorId: res.floorId
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    facilityPersonne() {
      this.getOptionAll()
    },
    // 获取园区
    async getParkSelect() {
      const res = await getParkSelect()
      this.formConfigure.descriptors.parkId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 楼栋
    async getBuildingSelect(e) {
      const res = await getBuildingSelect(e)
      this.formConfigure.descriptors.buildingId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 楼层
    async getFloorSelect(e) {
      const res = await getFloorSelect(e)
      this.formConfigure.descriptors.floorId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
    },
    // 切换园区
    async changePark(e) {
      const res = await getBuildingSelect(e)
      this.formConfigure.descriptors.buildingId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.fromModel.buildingId = ''
      this.fromModel = {
        ...this.fromModel,
        floorId: ''
      }
    },
    // 切换楼栋
    async changeBuilding(e) {
      const res = await getFloorSelect(e)
      this.formConfigure.descriptors.floorId.options = res.map(item => {
        return { label: item.label, value: item.key }
      })
      this.fromModel = {
        ...this.fromModel,
        floorId: ''
      }
    },
    getStatus() {
      getStatus().then(res => {
        this.formConfigure.descriptors.deviceStatus.options = res?.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getInspection() {
      getInspection().then(res => {
        this.formConfigure.descriptors.inspection.options = res?.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getOptionAll() {
      Promise.all([getLabelList({ type: 0 }), getLabelList({ type: 1 })]).then(
        res => {
          this.formConfigure.descriptors.deviceType.options = res[0]?.map(
            item => {
              return {
                label: item.name,
                value: item.id
              }
            }
          )
          this.formConfigure.descriptors.deviceBrand.options = res[1]?.map(
            item => {
              return {
                label: item.name,
                value: item.id
              }
            }
          )
        }
      )
    },
    submit() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const params = {
            ...this.fromModel,
            buildingId:
              typeof this.fromModel.buildingId === 'string'
                ? this.detail.buildingId
                : this.fromModel.buildingId,
            floorId:
              typeof this.fromModel.floorId === 'string'
                ? this.detail.floorId
                : this.fromModel.floorId
          }
          if (Object.keys(this.detail).length > 0) {
            postLabelUpdate(params).then(() => {
              this.$toast.success('编辑成功')
              this.$router.go(-1)
            })
          } else {
            postLabelCreate(params).then(() => {
              this.$toast.success('添加成功')
              this.$router.go(-1)
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card {
  padding: 32px 66px 32px 26px;
  background-color: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
</style>
