<template>
  <div>
    <el-popover
      placement="top-start"
      :title="'新增' + title"
      width="352"
      trigger="manual"
      v-model="visible"
      ref="popoverRef"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
      <div class="flex align-items-center justify-content-end">
        <el-button type="info" size="mini" @click="visible = false"
          >取消</el-button
        >
        <el-button type="primary" @click="confirmPopover" size="mini"
          >确定</el-button
        >
      </div>
      <el-button type="primary" slot="reference" @click="addDict"
        >新增</el-button
      >
    </el-popover>
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import { postListLog } from '../../api/index'
export default {
  name: 'DictPopover',
  mixins: [descriptorMixins],
  props: {
    type: {
      type: String
    },
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      visible: false,
      fromModel: {}
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.fromModel = {}
      }
    }
  },
  methods: {
    addDict() {
      this.visible = true
    },

    // 新增
    confirmPopover() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const type = this.title === '设备类型' ? 0 : 1
          postListLog({
            ...this.fromModel,
            type
          }).then(() => {
            this.$toast.success('新增成功')
            this.$emit('refreshDict')
            this.$refs['driven-form'].clearValidate()
            this.visible = false
          })
        }
      })
    }
  }
}
</script>
