<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="应急预案"
      desc="登记安全预案做好风险防备"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <div class="card">
        <basic-tab
          ref="basicTab"
          :tabs-data="list"
          :current="current"
          @tabsChange="tabsChange"
        />
        <div class="btn">
          <el-button type="primary" size="mini" @click="goAdd">
            <svg-icon icon-class="add" />
            <span>新增</span>
          </el-button>
        </div>
      </div>
      <!-- 内容区域 -->
      <div class="module-list">
        <module-list
          ref="ModuleList"
          :extra-query="extralQuerys"
          :api-fn="getPage"
        >
          <template slot-scope="scope">
            <div class="card-wrapper">
              <div
                v-for="data in scope.data"
                :key="data.id"
                class="contract"
                @click="goDetail(data)"
              >
                <angle-status
                  :text="data.uploadTime + ' 上传'"
                  type="primary"
                  class="icon_left"
                />
                <div class="contract_bgc">
                  <img
                    alt=""
                    class="contract_bgc_img"
                    src="@/assets/images/enterprise/company/safety/bg-image.png"
                  />
                </div>
                <div class="card_title line-1">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="data.fileName | noData"
                    placement="top-start"
                  >
                    <span>{{ data.fileName | noData }}</span>
                  </el-tooltip>
                </div>

                <div class="card_title_sub flex align-items-center">
                  <span class="m-r-4">
                    <svg-icon icon-class="plan" />
                  </span>
                  <span style="color: rgba(0, 0, 0, 0.6)">
                    {{ data.typeStr | noData }}
                  </span>
                </div>
              </div>
            </div>
          </template>
        </module-list>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from './column'
import { getPage } from './api'
import BasicTab from '@/components/BasicTab/index.vue'
import AngleStatus from '@/components/Lateral/AngleStatus/index.vue'
import ModuleList from '@/components/Lateral/ModuleList/index.vue'
export default {
  name: 'ContingencyPlanBasic',
  components: {
    ModuleList,
    AngleStatus,
    BasicTab,
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      current: 1,
      extralQuerys: {
        status: 1
      },
      list: [
        {
          label: '待提交',
          value: 1
        },
        {
          label: '已提交',
          value: 2
        }
      ],
      getPage,
      isEnterpriseAuth: false,
      showApply: false,
      enterParkStatus: null
    }
  },
  methods: {
    goAdd() {
      this.$router.push({
        path: '/company/safety/addContingencyPlan'
      })
    },
    tabsChange(val) {
      this.current = val
      this.extralQuerys.status = val
      this.$refs.ModuleList?.refresh()
    },
    goDetail(val) {
      this.$router.push({
        path: '/company/safety/contingencyPlanDetail',
        query: {
          id: val.id,
          status: val.status
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin-top: 16px;
}
.card {
  margin-top: 24px;
  position: relative;
  background: #ffffff;
  border-radius: 6px 6px 6px 6px;
  .btn {
    position: absolute;
    right: 0;
    top: 10px;
  }
}
:deep(.card-wrapper) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .card-list {
    width: 50%;
    margin-bottom: 32px;

    &:nth-child(odd) {
      padding-right: 14px;
    }

    &:nth-child(even) {
      padding-left: 14px;
    }
  }
}

.contract {
  position: relative;
  width: 276px;
  height: 306px;
  @include background_color(--color-white);
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  backdrop-filter: blur(6px);
  margin-right: 32px;
  margin-bottom: 32px;
  cursor: pointer;
  transition: all 100ms linear;
  &:hover {
    box-shadow: 0 6px 30px rgb(0 0 0 / 0.1);
  }
  &:nth-child(4n) {
    margin-right: 0;
  }
}

.contract_bgc {
  margin: 8px;
  width: 260px;
  height: 220px;
  background: #e9f0ff;
  border-radius: 3px;
  border: 1px solid;
  // prettier-ignore
  border-image: linear-gradient(
          34deg,
          rgba(255, 255, 255, 0.4),
          rgba(255, 255, 255, 0)
  )
  1 1;
}

.contract_bgc_img {
  margin: 52px 56px 0;
  width: 148px;
  height: 168px;
}

.card_title {
  margin: 8px 16px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  //超出省略号
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card_title_img {
  height: 16px;
  width: 16px;
  margin: 3px 4px 3px 0;
}

.card_title_sub {
  margin: 8px 16px;
  font-size: 14px;
  line-height: 22px;
}

.icon_left {
  position: absolute;
  top: 16px;
  left: -4px;
}

.icon_right {
  position: absolute;
  top: 16px;
  right: 16px;
  //width: 40px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 3px;
  z-index: 999;

  & .title_text {
    margin: 2px 8px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    line-height: 20px;
  }
}
</style>
