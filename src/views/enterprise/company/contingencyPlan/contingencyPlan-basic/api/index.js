import request from '@/utils/request'

// 获取设备列表
export function getPage(params) {
  return request({
    url: `/ck/ent/emergency_plan/page`,
    method: 'get',
    params
  })
}
// 获得设备状态;
export function getStatus(params) {
  return request({
    url: `/device/common/status`,
    method: 'get',
    params
  })
}
// 详情
export function getDetail(id) {
  return request({
    url: `/ck/ent/emergency_plan/get?id=${id}`,
    method: 'get'
  })
}
// 删除
export function delDevice(id) {
  return request({
    url: `/ck/ent/emergency_plan/delete?id=${id}`,
    method: 'delete'
  })
}
// 预案类型
export function postListLog(data) {
  return request({
    url: `/device/common/label_create`,
    method: 'post',
    data
  })
}
export function getLabelList(data) {
  return request({
    url: `/device/common/label_list`,
    method: 'post',
    data
  })
}
export function postLabelCreate(data) {
  return request({
    url: `/ck/ent/emergency_plan/create`,
    method: 'post',
    data
  })
}
export function postLabelUpdate(data) {
  return request({
    url: `/ck/ent/emergency_plan/update`,
    method: 'put',
    data
  })
}
export function postSubmit(data) {
  return request({
    url: `/ck/ent/emergency_plan/submit`,
    method: 'put',
    data
  })
}
