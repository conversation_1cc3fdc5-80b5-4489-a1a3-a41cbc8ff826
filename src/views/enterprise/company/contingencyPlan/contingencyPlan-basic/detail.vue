<template>
  <div>
    <module-header
      type="primary"
      title="预案详情"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    >
    </module-header>
    <div class="lateral-wrapper">
      <!-- 内容区域 -->
      <div class="module-list">
        <div class="info">
          <div class="detail">
            <div class="detail-box">
              <span class="label">企业名称</span>
              <span class="value">{{ detail.entName | noData }}</span>
            </div>
            <div class="detail-box">
              <span class="label">在园状态</span>
              <span class="value">{{ detail.entStatusStr | noData }}</span>
            </div>
            <div class="detail-box">
              <span class="label">更新时间</span>
              <span class="value">{{ detail.updateTime | noData }}</span>
            </div>
            <div class="detail-box">
              <span class="label">预案类型</span>
              <span class="value">{{ detail.typeStr | noData }}</span>
            </div>
            <div class="detail-box">
              <span class="label">预案文件</span>
              <span class="value">
                <files-list
                  v-if="detail.attach && detail.attach.informationAttach"
                  :files="detail.attach.informationAttach"
                  onlyForView
                />
                <span v-else class="value color-text-primary">暂无附件</span>
              </span>
            </div>
            <div class="detail-box">
              <span class="label">备注描述</span>
              <span class="value text-field">{{ detail.remark | noData }}</span>
            </div>
          </div>
          <div class="footer">
            <div>
              <el-button type="danger" @click="goDel">删除</el-button>
              <el-button type="primary" @click="goAdd">编辑</el-button>
              <el-button type="success" @click="submit" v-if="status === '1'"
                >提交</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import { getDetail, delDevice, postSubmit } from './api'
import FilesList from '@/components/Uploader/files.vue'

export default {
  name: 'EquipmentDetail',
  components: {
    FilesList,
    ModuleHeader
  },
  data() {
    return {
      list: [
        {
          label: '巡检历史',
          value: 0
        },
        {
          label: '维保历史',
          value: 1
        },
        {
          label: '安全督察',
          value: 2
        }
      ],
      historyList: [],
      current: 0,
      id: this.$route.query.id,
      status: this.$route.query.status,
      detail: {}
    }
  },
  created() {
    this.getDetail()
  },
  methods: {
    submit() {
      postSubmit({ id: this.id }).then(() => {
        this.$message({
          type: 'success',
          message: '提交成功!'
        })
        this.$router.push({
          path: '/company/safety/contingencyPlan'
        })
      })
    },
    goAdd() {
      this.$router.push({
        path: '/company/safety/addContingencyPlan',
        query: {
          id: this.id
        }
      })
    },
    getDetail() {
      getDetail(this.id).then(res => {
        this.detail = res
      })
    },
    goDel() {
      this.$confirm('是否删除该条数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delDevice(this.id).then(() => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.$router.push({
            path: '/company/safety/contingencyPlan'
          })
        })
      })
    },
    tabsChange(index) {
      this.current = index
      this.getHistoryLog()
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin-top: 24px;
  .info {
    background: #ffffff;
    border-radius: 6px 6px 6px 6px;
    border: 1px solid #e9f0ff;
    .detail {
      padding: 28px 32px 0 32px;
      .detail-box {
        display: flex;
        margin-bottom: 26px;
        font-size: 14px;
        .label {
          width: 70px;
          color: rgba(0, 0, 0, 0.4);
        }
        .value {
          flex: 1;
          color: rgba(0, 0, 0, 0.9);
        }
        .text-field {
          width: 100%;
          height: 285px;
          display: inline-block;
        }
      }
    }
    .footer {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      padding: 16px 32px 16px 32px;
      border-top: 1px solid #e9f0ff;
    }
  }
}
</style>
