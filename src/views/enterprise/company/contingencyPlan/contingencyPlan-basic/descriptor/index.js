import DictPopover from '../components/DictPopover'
export default {
  components: {
    DictPopover
  },
  data() {
    return {
      formConfigure: {
        descriptors: {
          type: {
            form: 'select',
            span: 12,
            label: '预案类型',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择预案类型'
              }
            ],
            options: [],
            customRight: () => {
              return (
                <div class="line-height-30">
                  <dict-popover
                    title={'预案类型'}
                    type="course_type"
                    onRefreshDict={() => {
                      this.facilityPersonne()
                    }}
                  />
                </div>
              )
            }
          },
          attach: {
            form: 'component',
            label: '上传文件',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传文件'
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <div class="line-height-32 color-info font-size-14">
                  <div style={'position: absolute; left: 186px'}>
                    请上传不大于10MB的附件,附件不得超过三个
                  </div>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'informationAttach'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          },
          remark: {
            form: 'input',
            label: '备注描述',
            rule: [
              {
                type: 'string',
                message: '请输入备注描述'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 6 }
            }
          }
        }
      }
    }
  }
}
