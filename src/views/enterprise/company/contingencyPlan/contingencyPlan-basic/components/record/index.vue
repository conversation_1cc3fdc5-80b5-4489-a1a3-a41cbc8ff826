<template>
  <div class="record-container">
    <div v-if="historyListCopy.length">
      <div
        v-for="(item, index) in historyListCopy"
        :key="index"
        class="record-wrapper flex"
      >
        <div class="left">
          <div
            v-if="index < historyListCopy.length - 1"
            class="record-line"
          ></div>
          <div
            class="record-circle bg-primary"
            :class="getCircleBg(item)"
          ></div>
        </div>
        <div class="right">
          <div
            class="right-header font-size-14 color-text-primary line-height-22 m-b-6"
          >
            <template v-if="item.result">
              <span>{{ getVale(item) }}</span>
              <span>｜</span>
            </template>
            <div class="flex justify-content-between">
              <span>{{ item.deptName }}-{{ item.userName }}</span>
              <span
                class="content font-size-14 color-text-regular line-height-22 m-b-8"
                >{{ item.createTime }}</span
              >
            </div>
          </div>

          <div
            v-if="item.maintenanceName"
            class="font-size-12 line-height-20 color-text-regular m-b-8"
          >
            <span class="m-r-6"> 维保单位 : </span>
            <span>
              {{ item.maintenanceName }}
            </span>
          </div>

          <div
            class="content font-size-14 color-text-regular line-height-22 m-b-8"
          >
            {{ item.content }}
          </div>

          <div v-if="Object.keys(item.attachMap).length > 0" class="m-b-8">
            <Uploader
              :mulity="true"
              type="avatar"
              width="42px"
              height="42px"
              onlyForView
              :value="item.attachMap.informationAttach"
            />
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <empty-data description="暂无记录" />
    </div>
  </div>
</template>

<script>
import EmptyData from '@/components/EmptyData'

export default {
  name: 'FlowRecord',
  components: {
    EmptyData
  },
  props: {
    historyList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      historyListCopy: []
    }
  },
  watch: {
    historyList: {
      handler(val) {
        if (val.length) {
          this.historyListCopy = JSON.parse(JSON.stringify(val))
        }
      },
      immediate: true
    }
  },
  methods: {
    getCircleBg(e) {
      const bgs = new Map()
      bgs.set(1, 'bg-primary')
      bgs.set(2, 'bg-warning')
      bgs.set(3, 'bg-danger')
      return bgs.get(e.result)
    },

    getVale(e) {
      const bgs = new Map()
      bgs.set(1, '通过')
      bgs.set(2, '退回')
      bgs.set(3, '拒绝')
      return bgs.get(e.result)
    }
  }
}
</script>

<style lang="scss" scoped>
.right-header {
  color: #1f1f1f;
}
.content {
  //文字超出换行
  word-break: break-all;
  word-wrap: break-word;
  white-space: normal;
}
.record-container {
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
  padding-right: 12px;
  .record-wrapper {
    .left {
      flex: 0 0 32px;
      position: relative;

      .record-line {
        position: absolute;
        width: 2px;
        height: 100%;
        top: 10px;
        left: 11px;
        background-color: #dcdcdc;
      }

      .record-circle {
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        top: 8px;
        left: 8px;
      }
    }
    .right {
      flex: 1;
      padding-bottom: 24px;
    }
  }
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }
}
</style>
