<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      :title="title"
      desc="登记安全预案做好风险防备"
      :img="
        require('@/assets/images/enterprise/company/safety/add-header-bg.png')
      "
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <div class="m-t-20 card">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
        <div class="flex justify-content-end m-b-20">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="submit">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DrivenForm from '@/components/DrivenForm'
import descriptorMixins from '../descriptor'
import ModuleHeader from '@/components/Lateral/ModuleHeader/index.vue'
import {
  postLabelUpdate,
  postLabelCreate,
  getLabelList,
  getDetail
} from '../api/index'
export default {
  name: 'AddDevice',
  components: {
    ModuleHeader,
    DrivenForm
  },
  mixins: [descriptorMixins],
  data() {
    return {
      fromModel: {},
      detail: {},
      title: this.$route.query.id ? '编辑应急预案' : '添加应急预案'
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getDetail()
    }
  },
  mounted() {
    this.getLabelList()
  },
  methods: {
    getLabelList() {
      getLabelList({ type: 2 }).then(res => {
        this.formConfigure.descriptors.type.options = res.map(item => {
          return { label: item.name, value: item.id }
        })
      })
    },
    facilityPersonne() {
      this.getLabelList()
    },
    getDetail() {
      getDetail(this.$route.query.id).then(res => {
        this.detail = res
        this.fromModel = {
          ...res,
          attach: res.attach && res.attach.informationAttach
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    submit() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          const { attach, id } = this.fromModel
          const params = {
            ...this.fromModel
          }
          if (attach && attach.length > 0) {
            params.attach = attach.map(item => item.id)
          }
          if (id) {
            postLabelUpdate(params).then(() => {
              this.$toast.success('编辑成功')
              this.$router.go(-1)
            })
          } else {
            postLabelCreate(params).then(() => {
              this.$toast.success('添加成功')
              this.$router.go(-1)
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.card {
  padding: 32px 66px 32px 26px;
  background-color: #ffffff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
</style>
