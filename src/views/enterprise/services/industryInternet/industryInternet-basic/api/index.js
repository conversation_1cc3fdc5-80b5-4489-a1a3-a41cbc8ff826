import request from '@/utils/request'

// 获得产业互联企业简介
export function getIndIntroduction() {
  return request({
    url: `/ind/info/get`,
    method: 'get'
  })
}
// 获得产业互联企业主页
export function getIndInfo() {
  return request({
    url: `/ind/info/get_ind`,
    method: 'get'
  })
}
// 创建或更新产业互联企业简介
export function indInfoCreate(data) {
  return request({
    url: `/ind/info/create`,
    method: 'post',
    data
  })
}
// 根据企业简介id获取企业主页
export function indInfoByInfo(params) {
  return request({
    url: `/ind/info/get_by_info`,
    method: 'get',
    params
  })
}
// 企业端-企业主页-获取产品分页
export function indProductionPage(params) {
  return request({
    url: `/ind/production/page`,
    method: 'get',
    params
  })
}
// 创建或更新产业互联产品
export function indProductionCreate(data) {
  return request({
    url: `/ind/production/create`,
    method: 'post',
    data
  })
}
// 企业端-隐藏产品信息
export function indProductionHidden(params) {
  return request({
    url: `/ind/production/hidden`,
    method: 'get',
    params
  })
}
// 企业端-取消隐藏产品信息
export function indProductionCancelHidden(params) {
  return request({
    url: `/ind/production/cancel_hidden`,
    method: 'get',
    params
  })
}
// 企业端-删除产品信息
export function indProductionDelete(params) {
  return request({
    url: `/ind/production/delete`,
    method: 'get',
    params
  })
}
// 获得产业互联产品
export function getProductionDetail(params) {
  return request({
    url: `/ind/production/get`,
    method: 'get',
    params
  })
}

// 企业端-企业主页-获取服务分页
export function indServeGet(params) {
  return request({
    url: `/ind/serve/page`,
    method: 'get',
    params
  })
}

// 创建产业互联服务
export function indServeCreate(data) {
  return request({
    url: `/ind/serve/create`,
    method: 'post',
    data
  })
}
// 更新产业互联服务
export function indServeUpdate(data) {
  return request({
    url: `/ind/serve/update`,
    method: 'post',
    data
  })
}
// 企业端-隐藏服务信息
export function indServeHidden(params) {
  return request({
    url: `/ind/serve/hidden`,
    method: 'get',
    params
  })
}
// 企业端-取消隐藏服务信息
export function indServeCancelHidden(params) {
  return request({
    url: `/ind/serve/cancel_hidden`,
    method: 'get',
    params
  })
}
// 企业端-删除服务信息
export function indServeDelete(params) {
  return request({
    url: `/ind/serve/delete`,
    method: 'get',
    params
  })
}
// 获得产业互联服务
export function getServeDetail(params) {
  return request({
    url: `/ind/serve/get`,
    method: 'get',
    params
  })
}

// 企业端-企业主页-获取需求分页
export function indDemandGet(params) {
  return request({
    url: `/ind/demand/page`,
    method: 'get',
    params
  })
}

// 创建产业互联需求
export function indDemandCreate(data) {
  return request({
    url: `/ind/demand/create`,
    method: 'post',
    data
  })
}
// 更新产业互联需求
export function indDemandUpdate(data) {
  return request({
    url: `/ind/demand/update`,
    method: 'post',
    data
  })
}
// 企业端-隐藏需求信息
export function indDemandHidden(params) {
  return request({
    url: `/ind/demand/hidden`,
    method: 'get',
    params
  })
}
// 企业端-取消隐藏需求信息
export function indDemandCancelHidden(params) {
  return request({
    url: `/ind/demand/cancel_hidden`,
    method: 'get',
    params
  })
}
// 企业端-删除需求信息
export function indDemandDelete(params) {
  return request({
    url: `/ind/demand/delete`,
    method: 'get',
    params
  })
}
// 获得产业互联需求
export function getDemandDetail(params) {
  return request({
    url: `/ind/demand/get`,
    method: 'get',
    params
  })
}

// 企业端-服务大厅-产品大厅
export function indProductionPageHall(params) {
  return request({
    url: `/ind/production/page_hall`,
    method: 'get',
    params
  })
}
// 企业端-服务大厅-服务大厅
export function indServePageHall(params) {
  return request({
    url: `/ind/serve/page_hall`,
    method: 'get',
    params
  })
}
// 企业端-服务大厅-需求大厅
export function indDemandPageHall(params) {
  return request({
    url: `/ind/demand/page_hall`,
    method: 'get',
    params
  })
}
// 企业端-服务大厅-企业大厅
export function indInfoPageHall(params) {
  return request({
    url: `/ind/info/page`,
    method: 'get',
    params
  })
}
// 企业端-服务大厅-获取企业行业类型
export function indInfoIndustry() {
  return request({
    url: `/ind/info/industry`,
    method: 'get'
  })
}

// 创建企业互联咨询
export function indConsultCreate(data) {
  return request({
    url: `/ind/consult/create`,
    method: 'post',
    data
  })
}
// 获得企业互联咨询
export function getConsultDetail(params) {
  return request({
    url: `/ind/consult/get`,
    method: 'get',
    params
  })
}
// 获得企业互联咨询分页
export function IndConsultPage(params) {
  return request({
    url: `/ind/consult/page`,
    method: 'get',
    params
  })
}
// 企业互联咨询读咨询标记
export function indConsultRead(params) {
  return request({
    url: `/ind/consult/read`,
    method: 'get',
    params
  })
}
// 获得企业互联名片下拉
export function indCardList(params) {
  return request({
    url: `/ind/card/list`,
    method: 'get',
    params
  })
}
// 获得企业互联名片分页
export function indCardPage(params) {
  return request({
    url: `/ind/card/page`,
    method: 'get',
    params
  })
}
// 企业端-名片簿
export function indCardPageAll(params) {
  return request({
    url: `/ind/card/page_all`,
    method: 'get',
    params
  })
}
// 新增企业互联名片
export function indCardCreate(data) {
  return request({
    url: `/ind/card/create`,
    method: 'post',
    data
  })
}
// 获得企业互联名片详情
export function getCardDetail(params) {
  return request({
    url: `/ind/card/get`,
    method: 'get',
    params
  })
}
// 更新企业互联名片
export function indCardUpdate(data) {
  return request({
    url: `/ind/card/update`,
    method: 'post',
    data
  })
}
// 删除企业互联名片
export function indCardDelete(params) {
  return request({
    url: `/ind/card/delete`,
    method: 'get',
    params
  })
}
// 企业端-获取我投递的、我收到的列表
export function indDeliveryRecordPage(params) {
  return request({
    url: `/ind/delivery/record/page`,
    method: 'get',
    params
  })
}
// 企业端-投递名片
export function indDeliveryRecordCreate(data) {
  return request({
    url: `/ind/delivery/record/create`,
    method: 'post',
    data
  })
}
// 企业端-获取对话记录
export function indDeliveryRecordTalk(params) {
  return request({
    url: `/ind/delivery/record/get_talk_record`,
    method: 'get',
    params
  })
}
// 企业端-获取回复的企业信息
export function indDeliveryRecordGetReq(params) {
  return request({
    url: `/ind/delivery/record/get_req`,
    method: 'get',
    params
  })
}

// 企业端-回复或回递名片
export function indDeliveryRecordResp(data) {
  return request({
    url: `/ind/delivery/record/resp`,
    method: 'post',
    data
  })
}
// 企业端-企业名片读消息标记
export function indDeliveryRecordRead(params) {
  return request({
    url: `/ind/delivery/record/read`,
    method: 'get',
    params
  })
}
// 企业端-接收方读取消息
export function indDeliveryRecordReadUser(params) {
  return request({
    url: `/ind/delivery/record/read_user`,
    method: 'get',
    params
  })
}
