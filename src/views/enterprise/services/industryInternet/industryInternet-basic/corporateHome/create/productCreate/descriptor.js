export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          pdName: {
            form: 'input',
            label: '产品标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入产品标题'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>产品标题</span>
                  <span class={'label-tips'}>
                    优秀的产品标题更能突出产品价值
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          pdDesc: {
            form: 'component',
            label: '产品描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入产品描述'
              }
            ],
            componentName: 'Tinymce',
            customLabel: () => {
              return (
                <div>
                  <span>产品描述</span>
                  <span class={'label-tips'}>
                    详细的产品描述更好的让人了解并尝试联系
                  </span>
                </div>
              )
            }
          },
          pdProfile: {
            form: 'input',
            label: '产品简介',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入产品简介'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>产品简介</span>
                  <span class={'label-tips'}>
                    简短的简介更具有口口相传的传播性
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          pdWebsite: {
            form: 'input',
            label: '产品官网',
            placeholder: '请输入产品官网',
            rule: [
              {
                required: false,
                type: 'string',
                validator: (rule, value, callback) => {
                  if (!value) return callback()
                  const urlReg =
                    /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~/])+$/
                  if (!urlReg.test(value))
                    return callback(new Error('请输入有效的网址'))
                  callback()
                }
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>产品官网</span>
                  <span class={'label-tips'}>
                    添加产品的官网链接地址，让尝试了解的人更清楚
                  </span>
                </div>
              )
            }
          },
          attachLogo: {
            form: 'component',
            label: '产品封面或logo',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传产品封面或logo'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>产品封面或logo</span>
                  <span class={'label-tips'}>打造产品专业的宣传形象</span>
                </div>
              )
            },
            componentName: 'uploader',
            props: {
              type: 'avatar',
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              uploaderText: '点击上传',
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 10
            }
          },
          attachProduct: {
            form: 'component',
            label: '产品图册或视频',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传产品图册或视频'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>产品图册或视频</span>
                  <span class={'label-tips'}>
                    比文字更具有直观性的一些图片或视频
                  </span>
                </div>
              )
            },
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              accept: 'image/*, .mp4',
              mulity: true,
              maxLength: 9,
              limit: 9,
              maxSize: 100
            }
          }
        }
      }
    }
  }
}
