export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          svName: {
            form: 'input',
            label: '服务标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入服务标题'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>服务标题</span>
                  <span class={'label-tips'}>
                    优秀的服务标题更能突出服务价值
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          svDesc: {
            form: 'component',
            label: '服务描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入服务描述'
              }
            ],
            componentName: 'Tinymce',
            customLabel: () => {
              return (
                <div>
                  <span>服务描述</span>
                  <span class={'label-tips'}>
                    详细的服务描述更好的让人了解并尝试联系
                  </span>
                </div>
              )
            }
          },
          svProfile: {
            form: 'input',
            label: '服务简介',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入服务简介'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>服务简介</span>
                  <span class={'label-tips'}>
                    简短的简介更具有口口相传的传播性
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          svType: {
            form: 'select',
            label: '服务类型',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择服务类型'
              }
            ],
            options: [],
            customLabel: () => {
              return (
                <div>
                  <span>服务类型</span>
                  <span class={'label-tips'}>
                    选择合适的服务类型，会在正确的位置展示
                  </span>
                </div>
              )
            }
          },
          svWay: {
            form: 'radio',
            label: '服务方式',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择服务方式'
              }
            ],
            options: [
              { label: '免费', value: 1 },
              { label: '面议', value: 2 },
              { label: '付费', value: 3 }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>服务方式</span>
                  <span class={'label-tips'}>
                    提前约定的内容会让人有更多的接受程度
                  </span>
                </div>
              )
            }
          },
          svPrice: {
            form: 'input',
            label: '',
            hidden: true,
            placeholder: '请输入价格',
            rule: [
              {
                required: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  if (!value) return callback(new Error(`请输入价格`))
                  if (isNaN(value)) callback(new Error(`请输入数字`))
                  if (value <= 0) return callback(new Error(`价格需大于0`))
                  const reg =
                    /(^[0-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
                  if (!reg.test(value))
                    return callback(new Error(`最多保留两位小数`))
                  callback()
                }
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">元</div>
            }
          },
          attachServeLogo: {
            form: 'component',
            label: '服务封面',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传服务封面'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>服务封面</span>
                  <span class={'label-tips'}>打造服务专业的宣传形象</span>
                </div>
              )
            },
            componentName: 'uploader',
            props: {
              type: 'avatar',
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              uploaderText: '点击上传',
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 10
            }
          },
          attachServe: {
            form: 'component',
            label: '相关图册或视频',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传相关图册或视频'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>相关图册或视频</span>
                  <span class={'label-tips'}>
                    比文字更具有直观性的一些图片或视频
                  </span>
                </div>
              )
            },
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              accept: 'image/*, .mp4',
              mulity: true,
              maxLength: 9,
              limit: 9,
              maxSize: 100
            }
          }
        }
      }
    }
  }
}
