export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          entDesc: {
            form: 'component',
            label: '企业描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业描述'
              }
            ],
            componentName: 'Tinymce',
            customLabel: () => {
              return (
                <div>
                  <span>企业描述</span>
                  <span class={'label-tips'}>
                    例如添加企业的主营业务、资质荣誉、管理团队等信息
                  </span>
                </div>
              )
            }
          },
          profile: {
            form: 'input',
            label: '简介概况',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入简介概况'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>简介概况</span>
                  <span class={'label-tips'}>
                    简短的概况更具有口口相传的传播性
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 150
            }
          },
          address: {
            form: 'input',
            label: '所在地',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入所在地'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>所在地</span>
                  <span class={'label-tips'}>影响您的业务辐射范围</span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          slogan: {
            form: 'input',
            label: '企业slogan',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业slogan'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>企业slogan</span>
                  <span class={'label-tips'}>极具穿透性的一句话</span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          logoAttach: {
            form: 'component',
            label: '企业logo',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传企业logo'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>企业logo</span>
                  <span class={'label-tips'}>
                    精美的企业logo是身份特征的首要一步
                  </span>
                </div>
              )
            },
            componentName: 'uploader',
            props: {
              type: 'avatar',
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              uploaderText: '点击上传',
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 10
            }
          }
        }
      }
    }
  }
}
