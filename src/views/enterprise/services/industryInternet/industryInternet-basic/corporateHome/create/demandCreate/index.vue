<template>
  <div>
    <module-header
      type="primary"
      title="添加企业需求"
      desc="登记安全预案做好风险防备"
      :img="require('../../../images/info-header-bg.png')"
      :imgOpacity="1"
    >
      <breadcrumb class="p-t-32" />
      <h2
        class="p-t-32 flex align-items-center font-size-20 line-height-28 font-strong color-text-primary"
      >
        {{ $route.query.id ? '编辑' : '添加' }}企业需求
      </h2>
      <p class="font-size-12 line-height-22 color-text-regular">
        登记安全预案做好风险防备
      </p>
    </module-header>
    <div class="lateral-wrapper">
      <div class="create-wrapper">
        <driven-form
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
          label-position="top"
        />
        <div class="btn-wrapper">
          <el-button type="primary" @click="goBack">取消</el-button>
          <el-button type="success" @click="submitHandle">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import DescriptorMixin from './descriptor'
import { getByTenantDictType } from '@/api/common'
import {
  getDemandDetail,
  indDemandCreate,
  indDemandUpdate
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'DemandCreate',
  components: { ModuleHeader, Breadcrumb },
  mixins: [DescriptorMixin],
  data() {
    return {
      fromModel: {}
    }
  },
  created() {
    this.getByTenantDictType()
    const id = this.$route.query.id
    this.$route.meta.title = id ? '编辑企业需求' : '添加企业需求'
    if (id) {
      this.getDemandDetail(id)
    }
  },
  methods: {
    getByTenantDictType() {
      getByTenantDictType('ind_de_type').then(res => {
        this.formConfigure.descriptors.deType.options = res || []
      })
    },
    getDemandDetail(id) {
      getDemandDetail({ id }).then(res => {
        const { demandAttach = {} } = res
        this.fromModel = {
          ...res,
          demandAttach: demandAttach.industryInternet || []
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    operateTips(text) {
      this.$toast.success(`${text}成功`)
      this.goBack()
    },
    submitHandle() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel,
          demandAttach: this.fromModel.demandAttach
            ? this.fromModel.demandAttach.map(item => item.id)
            : []
        }
        if (this.$route.query.id) {
          indDemandUpdate(params).then(() => {
            this.operateTips('编辑')
          })
        } else {
          indDemandCreate(params).then(() => {
            this.operateTips('新增')
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.create-wrapper {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  padding: 32px 32px 0;
  margin: 24px 0;
  :deep(.label-tips) {
    padding-left: 12px;
    @include font_color_mix(--color-black, #fff, 60%);
  }
  .btn-wrapper {
    padding: 16px 0;
    border-top: 1px solid #e9f0ff;
    text-align: right;
  }
}
:deep(.deTypeKey) {
  .form-item-container {
    width: 50%;
  }
}
:deep(.demandAttachKey) {
  .form-item-container {
    width: 50%;
  }
}
</style>
