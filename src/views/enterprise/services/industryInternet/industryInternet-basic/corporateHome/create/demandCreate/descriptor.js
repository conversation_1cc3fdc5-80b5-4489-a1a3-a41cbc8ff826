export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          deName: {
            form: 'input',
            label: '需求标题',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入需求标题'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>需求标题</span>
                  <span class={'label-tips'}>
                    优秀的需求标题更能突出需求价值
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          deDesc: {
            form: 'component',
            label: '需求描述',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入需求描述'
              }
            ],
            componentName: 'Tinymce',
            customLabel: () => {
              return (
                <div>
                  <span>需求描述</span>
                  <span class={'label-tips'}>
                    详细的需求描述更好的让人了解并尝试联系
                  </span>
                </div>
              )
            }
          },
          deProfile: {
            form: 'input',
            label: '需求简介',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入需求简介'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>需求简介</span>
                  <span class={'label-tips'}>
                    简短的简介更具有口口相传的传播性
                  </span>
                </div>
              )
            },
            attrs: {
              maxLength: 50
            }
          },
          deType: {
            form: 'select',
            label: '需求类型',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择需求类型'
              }
            ],
            options: [],
            customLabel: () => {
              return (
                <div>
                  <span>需求类型</span>
                  <span class={'label-tips'}>
                    选择合适的需求类型，会在正确的位置展示
                  </span>
                </div>
              )
            }
          },
          demandAttach: {
            form: 'component',
            label: '需求图册或视频',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传需求图册或视频'
              }
            ],
            customLabel: () => {
              return (
                <div>
                  <span>需求图册或视频</span>
                  <span class={'label-tips'}>
                    比文字更具有直观性的一些图片或视频
                  </span>
                </div>
              )
            },
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              accept: 'image/*, .mp4',
              mulity: true,
              maxLength: 9,
              limit: 9,
              maxSize: 100
            }
          }
        }
      }
    }
  }
}
