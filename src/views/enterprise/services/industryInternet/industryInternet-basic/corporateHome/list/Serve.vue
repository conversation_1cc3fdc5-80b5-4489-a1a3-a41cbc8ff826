<template>
  <module-list ref="ModuleList" :api-fn="indServeGet" :extra-query="extraQuery">
    <template slot-scope="scope">
      <div class="flex flex-wrap serve-wrapper">
        <serve-card
          v-for="(item, index) in scope.data"
          :key="index"
          :item="item"
          @detailHandle="detailHandle"
          @handleCommand="handleCommand"
          :has-operate="!$route.query.indId"
        />
      </div>
    </template>
  </module-list>
</template>

<script>
import ModuleList from '@/components/Lateral/ModuleList'
import ServeCard from '../../components/ServeCard'
import {
  indServeCancelHidden,
  indServeDelete,
  indServeGet,
  indServeHidden
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'Serve',
  components: { ServeCard, ModuleList },
  data() {
    return {
      indServeGet,
      extraQuery: {
        indId: this.$route.query.indId
      }
    }
  },
  methods: {
    operateTips(text) {
      this.$toast.success(`${text}成功`)
      this.$refs.ModuleList.refresh()
      this.$emit('update')
    },
    // 显示隐藏
    showHideHandle(row) {
      if (!row.disappear) {
        indServeHidden({ id: row.id }).then(() => {
          this.operateTips('隐藏')
        })
      } else {
        indServeCancelHidden({ id: row.id }).then(() => {
          this.operateTips('取消隐藏')
        })
      }
    },
    // 删除
    deleteHandle(row) {
      this.$confirm('确定删除该服务？').then(() => {
        indServeDelete({ id: row.id }).then(() => {
          this.operateTips('删除')
        })
      })
    },
    // command: 1显示隐藏；2编辑；3删除
    handleCommand(command, row) {
      switch (command) {
        case 1:
          this.showHideHandle(row)
          break
        case 2:
          this.$router.push({
            path: '/services/internet/corporateHomeServeCreate',
            query: {
              id: row.id
            }
          })
          break
        case 3:
          this.deleteHandle(row)
          break
        default:
          break
      }
    },
    detailHandle(row) {
      this.$router.push({
        path: '/services/internet/corporateHomeServeDetail',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
