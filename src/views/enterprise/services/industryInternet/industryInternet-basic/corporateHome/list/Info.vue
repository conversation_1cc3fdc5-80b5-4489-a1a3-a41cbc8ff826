<template>
  <div class="info-container p-l-24 p-r-24">
    <div
      v-if="entDesc"
      v-html="$options.filters.richTextFilter(entDesc)"
      class="rich-text"
    ></div>
    <empty-data v-else class="info-empty-data" />
  </div>
</template>

<script>
export default {
  name: 'Info',
  props: {
    entDesc: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.info-container {
  min-height: 320px;
  .info-empty-data {
    padding-top: 40px;
  }
}
</style>
