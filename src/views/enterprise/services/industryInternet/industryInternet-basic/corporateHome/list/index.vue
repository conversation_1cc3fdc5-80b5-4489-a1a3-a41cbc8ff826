<template>
  <div class="corporate-container">
    <module-header
      type="primary"
      title="企业主页"
      desc="构建靓丽的一道企业风景线"
      :img="require('../../images/header-bg.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper">
      <div class="corporate-header">
        <div class="header-logo-wrapper">
          <div class="header-logo">
            <el-image v-show="logo" class="wh100" :src="logo" fit="cover" />
          </div>
        </div>
        <div class="w100 p-l-16">
          <div class="font-size-16 line-height-24 line-1">
            {{ info.enterpriseName }}
          </div>
          <div class="flex align-items-center flex-wrap">
            <basic-tag
              v-if="info.industry"
              class="m-t-8 m-r-8"
              type="primary"
              :label="info.industry"
            />
            <basic-tag
              v-for="(item, index) in info.honorList || []"
              :key="'tag-' + index"
              class="m-t-8 m-r-8"
              type="success"
              :label="item"
            />
          </div>
          <div class="m-t-8 font-size-14 line-height-22">
            <i class="el-icon-location-outline color-warning m-r-4"></i>
            <span class="color-text-regular">{{
              info.address || '待填写后显示'
            }}</span>
          </div>
          <div
            class="m-t-8 font-size-14 line-height-22 flex align-items-center"
          >
            <div class="color-text-regular">
              <svg-icon icon-class="geometry" class-name="color-success" />
              <span class="p-l-4">特色产品</span>
              <span class="color-success font-strong m-l-8">{{
                info.productCount
              }}</span>
            </div>
            <div class="color-text-regular m-l-16">
              <svg-icon icon-class="love" class-name="color-primary" />
              <span class="p-l-4">企业服务</span>
              <span class="color-primary font-strong m-l-8">{{
                info.serveCount
              }}</span>
            </div>
            <div class="color-text-regular m-l-16">
              <svg-icon icon-class="demand" class-name="color-warning" />
              <span class="p-l-4">项目需求</span>
              <span class="color-warning font-strong m-l-8">{{
                info.demandCount
              }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="corporate-wrapper m-t-10">
        <basic-tab
          :current="current"
          :tabs-data="tabsData"
          @tabsChange="tabsChange"
        >
          <template v-if="!$route.query.indId" slot="right">
            <el-button
              v-if="current === 0"
              type="primary"
              class="m-r-4"
              @click="addHandle"
              >{{ info.entDesc ? '编辑' : '添加' }}</el-button
            >
            <template v-else>
              <el-button
                v-if="!!info.entDesc"
                type="primary"
                class="m-r-4"
                @click="addHandle"
                >添加</el-button
              >
            </template>
          </template>
        </basic-tab>
        <div class="module-wrapper">
          <component
            :is="componentName"
            :entDesc="info.entDesc"
            @update="updateHandle"
          />
          <tips v-if="!$route.query.indId" :is-publish="isPublish" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab'
import Info from './Info'
import Product from './Product'
import Serve from './Serve'
import Demand from './Demand'
import Tips from './Tips'
import { getIndInfo, indInfoByInfo } from '../../api'

export default {
  name: 'CorporateHome',
  components: {
    BasicTab,
    ModuleHeader,
    Info,
    Product,
    Serve,
    Demand,
    Tips
  },
  data() {
    return {
      current: 0,
      tabsData: [
        { label: '简介', value: 0 },
        { label: '产品', value: 1 },
        { label: '服务', value: 2 },
        { label: '需求', value: 3 }
      ],
      info: {}
    }
  },
  computed: {
    logo() {
      const localLogo = this.info.localLogo
      const qccLogo = this.info.qccLogo
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && !qccLogo)
        return require('../../images/default-avatar.png')
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && qccLogo)
        return this.info.qccLogo
      return localLogo.industryInternet[0].path
    },
    isPublish() {
      return (
        this.info.entDesc &&
        !!(
          this.info.productCount ||
          this.info.serveCount ||
          this.info.demandCount
        )
      )
    },
    componentName() {
      const comObj = {
        0: 'Info',
        1: 'Product',
        2: 'Serve',
        3: 'Demand'
      }
      return comObj[this.current]
    }
  },
  created() {
    const { indId = '' } = this.$route.query
    if (indId) return this.byIndGetInfo(indId)
    this.getIndInfo()
  },
  methods: {
    updateHandle() {
      this.getIndInfo()
    },
    // 获取其他企业信息
    byIndGetInfo(indId) {
      indInfoByInfo({ infoId: indId }).then(res => {
        this.info = res || {}
      })
    },
    // 企业信息
    getIndInfo() {
      getIndInfo().then(res => {
        this.info = res || {}
      })
    },
    addHandle() {
      const routes = [
        '/corporateHomeInfoCreate',
        '/corporateHomeProductCreate',
        '/corporateHomeServeCreate',
        '/corporateHomeDemandCreate'
      ]
      this.$router.push(`/services/internet${routes[this.current]}`)
    },
    tabsChange(e) {
      this.current = e
    }
  }
}
</script>

<style scoped lang="scss">
.corporate-container {
  .corporate-header {
    display: flex;
    padding: 24px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e9f0ff;
    margin-top: 24px;
    align-items: center;
    .header-logo-wrapper {
      position: relative;
      .header-logo {
        width: 100px;
        height: 100px;
        border-radius: 3px;
        overflow: hidden;
      }
    }
  }
  .corporate-wrapper {
    padding: 16px 0;
    :deep(.tabs-wrapper) {
      border-color: #e9f0ff !important;
      .tabs-item {
        padding: 0 30px;
      }
    }
  }
}
</style>
