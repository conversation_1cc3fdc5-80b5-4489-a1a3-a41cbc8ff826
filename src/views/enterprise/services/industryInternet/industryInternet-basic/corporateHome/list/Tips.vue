<template>
  <div class="tips-text font-size-12 line-height-24">温馨提示：{{ text }}</div>
</template>

<script>
export default {
  name: 'Tips',
  props: {
    isPublish: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    text() {
      return this.isPublish
        ? '真棒!企业主页已被系统成功发布'
        : '至少应该有完整的企业简介和1条任意产品、服务或需求系统会自动发布企业主页'
    }
  }
}
</script>

<style scoped lang="scss">
.tips-text {
  @include font_color_mix(--color-black, #fff, 60%);
  text-align: center;
  margin-top: 12px;
}
</style>
