<template>
  <div class="enterprises">
    <div class="region">
      <div class="box">
        <div class="border p-r-24">
          <div class="flex">
            <div class="cover m-r-8">
              <el-image v-show="logo" class="wh100" :src="logo" fit="cover" />
            </div>
            <div class="item-content">
              <p class="w100 line-1 font-size-16 p-t-3">
                {{ item.enterpriseName }}
              </p>
              <p class="m-t-8 m-b-8 flex">
                <basic-tag
                  v-if="item.industry"
                  class="m-r-8 info"
                  type="info"
                  :label="item.industry"
                ></basic-tag>
                <basic-tag
                  v-for="(item, index) in item.honorList || []"
                  :key="'honor' + index"
                  type="primary"
                  :label="item"
                  class="m-r-8"
                ></basic-tag>
              </p>
              <p class="m-b-8 flex align-items-center">
                <i class="el-icon-location-outline color-warning"></i>
                <span class="account-color p-l-4">{{ item.address }}</span>
              </p>
              <div class="flex font-size-14">
                <p class="m-r-16">
                  <span class="p-r-4">特色产品</span>
                  <span class="number">{{ item.productCount }}</span>
                </p>
                <p class="m-r-16">
                  <span class="p-r-4">企业服务</span>
                  <span class="number">{{ item.serveCount }}</span>
                </p>
                <p>
                  <span class="p-r-4">项目需求</span>
                  <span class="number">{{ item.demandCount }}</span>
                </p>
              </div>
            </div>
          </div>
          <div class="m-t-8 line-height-22 introduction line-1">
            {{ item.profile }}
          </div>
        </div>
        <div class="flex product-wrapper">
          <div
            class="m-r-16 pointer"
            style="width: 104px"
            v-for="(data, val) in productList"
            :key="'product' + val"
            @click="detailHandle(data)"
          >
            <div class="ent-img">
              <el-image
                v-show="coverImg(data)"
                class="wh100"
                :src="coverImg(data)"
                fit="cover"
              />
            </div>
            <p class="ent-account line-2">{{ data.pdName }}</p>
          </div>
          <div
            class="empty m-r-16"
            v-for="t in 3 - productList.length"
            :key="t"
          >
            <p>暂无更多产品</p>
          </div>
        </div>
        <div class="item-right p-l-16">
          <div class="homepage home" @click="enterpriseHome">
            <div class="flex flex-direction-column">
              <img
                src="../../../images/enterprise-icon.png"
                class="w-24px m-auto"
                alt="alt"
              />
              <span class="font-size-14">查看主页</span>
            </div>
          </div>
          <div
            class="m-t-8 homepage card"
            :class="{ disabled: item.currentEnt }"
            @click="deliveryHandle"
          >
            <div class="flex flex-direction-column">
              <img
                src="../../../images/card-icon.png"
                class="w-24px m-auto"
                alt="alt"
              />
              <span class="font-size-14">投递名片</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <delivery-visible
      :visible.sync="changeVisible"
      :cardList="cardList"
      :info="item"
      :bs-type="4"
    />
  </div>
</template>

<script>
import DeliveryVisible from '../../../components/DeliveryVisible'

export default {
  name: 'EnterpriseCard',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    cardList: {
      type: Array,
      default: () => []
    }
  },
  components: { DeliveryVisible },
  data() {
    return {
      changeVisible: false
    }
  },
  computed: {
    productList() {
      return this.item.productList || []
    },
    logo() {
      const localLogo = this.item.localLogo
      const qccLogo = this.item.qccLogo
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && !qccLogo)
        return require('../../../images/default-avatar.png')
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && qccLogo)
        return this.item.qccLogo
      return localLogo.industryInternet[0].path
    }
  },
  methods: {
    deliveryHandle() {
      if (this.item.currentEnt) return false
      this.changeVisible = true
    },
    detailHandle(row) {
      this.$router.push({
        path: '/services/internet/corporateHomeProductDetail',
        query: { id: row.id }
      })
    },
    enterpriseHome() {
      if (this.item.currentEnt)
        return this.$router.push('/services/internet/corporateHome')
      this.$router.push({
        path: '/services/internet/corporateHome',
        query: { indId: this.item.id }
      })
    },
    coverImg(item) {
      if (item?.logoAttach?.industryInternet) {
        return item?.logoAttach?.industryInternet[0]?.path
      } else {
        return '-'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-tag) {
  border-color: #e6edfd;
}
.number {
  color: #ed7b2f;
}
.border {
  width: 694px;
  border-right: 1px solid #d5e2ff;
}
.empty {
  width: 104px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #eaf0ff;
  border-radius: 3px;
  p {
    font-weight: 350;
    color: #9a9ea8;
    font-size: 12px;
  }
}
.introduction {
  font-size: 14px;
  font-weight: 350;
  color: rgba(0, 0, 0, 0.6);
}
.enterprises {
  width: 100%;
  margin-bottom: 16px;
  .region {
    width: 100%;
    .box {
      width: 100%;
      padding: 24px;
      background: linear-gradient(
        180deg,
        rgba(234, 240, 255, 0.5) 0%,
        #f7f9fb 100%
      );
      border-radius: 6px 6px 6px 6px;
      display: flex;
      align-items: center;
      border: 1px solid #e9f0ff;
      .cover {
        width: 104px;
        height: 104px;
        border-radius: 3px;
      }

      .item-content {
        width: calc(100% - 112px);
      }

      .info {
        :deep(.el-tag) {
          background-color: #eeeeee;
          color: #000000 !important;
        }
      }

      .ent-cover {
        width: 52px;
        height: 52px;
        margin-right: 8px;
      }
    }

    .title-color {
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .product-wrapper {
      width: 345px;
      margin-left: 24px;
    }
    .account-color {
      font-weight: 350;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.4);
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2; //行数
      -webkit-box-orient: vertical;
    }
    .item-right {
      width: calc(100% - 655px - 345px - 48px);
    }
  }
  .ent-img {
    width: 96px;
    height: 96px;
    border-radius: 3px;
  }
  .ent-account {
    margin-top: 10px;
    font-weight: 350;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.9);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; //行数
    -webkit-box-orient: vertical;
  }

  .homepage {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 70px;
    height: 70px;
    background: rgba(255, 255, 255, 0.4);
    border-radius: 3px 3px 3px 3px;
    border: 1px solid #ffffff;
  }
  .home {
    transition: all 300ms linear;
    &:hover {
      background-color: rgba(213, 226, 255, 0.3);
      color: #ed7b2f;
      border: 1px solid rgba(213, 226, 255, 0.3);
      cursor: pointer;
    }
  }
  .card {
    transition: all 300ms linear;
    &:not(.disabled):hover {
      background-color: rgba(237, 123, 47, 0.1);
      color: #ed7b2f;
      border: 1px solid rgba(237, 123, 47, 0.1);
      cursor: pointer;
    }
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
.w-24px {
  width: 24px;
  margin: auto;
}
</style>
