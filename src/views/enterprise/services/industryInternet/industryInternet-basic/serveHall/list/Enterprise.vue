<template>
  <div>
    <module-filter :filters="filterData" @change="filterChange" :flag="false" />
    <module-list v-if="tableVisible" ref="ModuleList" :api-fn="indInfoPageHall">
      <template slot-scope="scope">
        <enterprise-card
          v-for="(item, index) in scope.data"
          :key="index"
          :item="item"
          :cardList="cardList"
        />
      </template>
    </module-list>
  </div>
</template>

<script>
import ModuleList from '@/components/Lateral/ModuleList'
import EnterpriseCard from './components/EnterpriseCard'
import ModuleFilter from '@/components/Lateral/ModuleFilter'
import {
  indCardList,
  indInfoIndustry,
  indInfoPageHall
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'Enterprise',
  components: { ModuleFilter, EnterpriseCard, ModuleList },
  data() {
    return {
      indInfoPageHall,
      filterData: [
        {
          label: '企业状态',
          prop: 'searchType',
          list: [
            { label: '已投递的', dictType: 2 },
            { label: '有反馈的', dictType: 3 }
          ]
        },
        {
          label: '企业类型',
          prop: 'subIndustryCode',
          list: []
        },
        {
          label: '发布时间',
          prop: 'pubDateType',
          list: [
            { label: '3天内', dictType: 1 },
            { label: '一周内', dictType: 2 },
            { label: '一月内', dictType: 3 },
            { label: '园区热推', dictType: 4 }
          ]
        }
      ],
      tableVisible: false,
      cardList: []
    }
  },
  created() {
    this.indInfoIndustry()
    this.indCardList()
  },
  methods: {
    indCardList() {
      indCardList().then(res => {
        this.cardList = res || []
      })
    },
    indInfoIndustry() {
      indInfoIndustry().then(res => {
        const list = res || []
        this.filterData[1].list = list.map(item => {
          return {
            label: item.name,
            dictType: item.code
          }
        })
        this.tableVisible = true
      })
    },
    filterChange(filter) {
      this.$refs.ModuleList.triggerSearch(filter)
    }
  }
}
</script>

<style scoped lang="scss"></style>
