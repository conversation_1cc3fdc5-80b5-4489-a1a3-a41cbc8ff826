<template>
  <div>
    <module-filter :filters="filterData" @change="filterChange" :flag="false" />
    <module-list
      v-if="tableVisible"
      ref="ModuleList"
      :api-fn="indDemandPageHall"
    >
      <template slot-scope="scope">
        <div class="flex flex-wrap justify-content-between">
          <demand-card
            v-for="(item, index) in scope.data"
            :key="index"
            :item="item"
            :has-operate="false"
            :has-tag="false"
            @detailHandle="detailHandle"
          />
        </div>
      </template>
    </module-list>
  </div>
</template>

<script>
import ModuleList from '@/components/Lateral/ModuleList'
import DemandCard from '../../components/DemandCard'
import ModuleFilter from '@/components/Lateral/ModuleFilter'
import { indDemandPageHall } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'
import { getByTenantDictType } from '@/api/common'

export default {
  name: 'Demand',
  components: { Mo<PERSON><PERSON><PERSON>ilter, DemandCard, ModuleList },
  data() {
    return {
      indDemandPageHall,
      filterData: [
        {
          label: '需求状态',
          prop: 'searchType',
          list: [
            { label: '已投递的', dictType: 2 },
            { label: '有反馈的', dictType: 3 }
          ]
        },
        {
          label: '需求类型',
          prop: 'deType',
          list: []
        },
        {
          label: '发布时间',
          prop: 'pubDateType',
          list: [
            { label: '3天内', dictType: 1 },
            { label: '一周内', dictType: 2 },
            { label: '一月内', dictType: 3 },
            { label: '园区热推', dictType: 4 }
          ]
        }
      ],
      tableVisible: false
    }
  },
  created() {
    this.getByTenantDictType()
  },
  methods: {
    getByTenantDictType() {
      getByTenantDictType('ind_de_type').then(res => {
        const list = res || []
        this.filterData[1].list = list.map(item => {
          return {
            label: item.label,
            dictType: item.value
          }
        })
        this.tableVisible = true
      })
    },
    detailHandle(row) {
      this.$router.push({
        path: '/services/internet/corporateHomeDemandDetail',
        query: { id: row.id }
      })
    },
    filterChange(filter) {
      this.$refs.ModuleList.triggerSearch(filter)
    }
  }
}
</script>

<style scoped lang="scss"></style>
