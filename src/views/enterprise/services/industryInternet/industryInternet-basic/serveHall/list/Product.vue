<template>
  <div>
    <module-filter :filters="filterData" @change="filterChange" :flag="false" />
    <module-list ref="ModuleList" :api-fn="indProductionPageHall">
      <template slot-scope="scope">
        <div class="flex flex-wrap justify-content-between">
          <product-card
            v-for="(item, index) in scope.data"
            :key="index"
            :item="item"
            :has-operate="false"
            :has-tag="false"
            @detailHandle="detailHandle"
          />
        </div>
      </template>
    </module-list>
  </div>
</template>

<script>
import ModuleList from '@/components/Lateral/ModuleList'
import ProductCard from '../../components/ProductCard'
import ModuleFilter from '@/components/Lateral/ModuleFilter'
import { indProductionPageHall } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'Product',
  components: { ModuleFilter, ProductCard, ModuleList },
  data() {
    return {
      indProductionPageHall,
      filterData: [
        {
          label: '产品状态',
          prop: 'searchType',
          list: [
            { label: '已投递的', dictType: 2 },
            { label: '有反馈的', dictType: 3 }
          ]
        },
        {
          label: '发布时间',
          prop: 'pubDateType',
          list: [
            { label: '3天内', dictType: 1 },
            { label: '一周内', dictType: 2 },
            { label: '一月内', dictType: 3 },
            { label: '园区热推', dictType: 4 }
          ]
        }
      ]
    }
  },
  methods: {
    detailHandle(row) {
      this.$router.push({
        path: '/services/internet/corporateHomeProductDetail',
        query: { id: row.id }
      })
    },
    filterChange(filter) {
      this.$refs.ModuleList.triggerSearch(filter)
    }
  }
}
</script>

<style scoped lang="scss"></style>
