<template>
  <div>
    <module-header
      type="primary"
      title="服务大厅"
      desc="充分挖掘产业潜力,实现资源链通"
      :img="require('../../images/header-bg.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper p-t-32">
      <basic-tab
        :current="current"
        :tabs-data="tabsData"
        @tabsChange="tabsChange"
      />
      <component :is="componentName" />
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab'
import Product from './Product'
import Serve from './Serve'
import Demand from './Demand'
import Enterprise from './Enterprise'

export default {
  name: 'ServeHall',
  components: {
    Enterprise,
    Demand,
    Serve,
    Product,
    BasicTab,
    ModuleHeader
  },
  data() {
    return {
      current: 0,
      tabsData: [
        { label: '产品大厅', value: 0 },
        { label: '服务大厅', value: 1 },
        { label: '需求大厅', value: 2 },
        { label: '企业大厅', value: 3 }
      ]
    }
  },
  computed: {
    componentName() {
      const comObj = {
        0: 'Product',
        1: 'Serve',
        2: 'Demand',
        3: 'Enterprise'
      }
      return comObj[this.current]
    }
  },
  methods: {
    tabsChange(e) {
      this.current = e
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.module-list) {
  padding-top: 16px;
}
</style>
