<template>
  <div>
    <module-header :img="require('../../images/info-header-bg.png')">
      <breadcrumb class="p-t-32" />
      <div class="detail-header m-t-10 flex align-items-center">
        <div style="max-width: 990px">
          <div class="line-height-24 flex align-items-center">
            <basic-tag
              class="inline-block"
              type="primary"
              :label="info.deTypeStr"
            />
            <svg-icon
              v-if="info.deSts === 2"
              icon-class="auth"
              class-name="color-primary m-l-4"
            />
            <div class="p-l-4 line-1" style="width: 800px">
              {{ info.deName }}
            </div>
          </div>
          <div class="m-t-8 line-height-24 line-2 introduction">
            {{ info.deProfile }}
          </div>
          <div class="m-t-4 flex justify-content-between align-center">
            <div class="font-size-14 line-height-22 line-1">
              <svg-icon
                icon-class="enterprise-name"
                class-name="color-primary"
              />
              <span class="p-l-8">{{ info.enterpriseName }}</span>
            </div>
          </div>
        </div>
        <div class="vertical"></div>
        <div class="header-right flex flex-direction-column align-items-center">
          <el-button type="info" @click="enterpriseHome">企业主页</el-button>
          <el-button type="primary" class="m-t-10" @click="dialogVisible = true"
            >咨询我们</el-button
          >
        </div>
      </div>
    </module-header>
    <div class="lateral-wrapper">
      <div class="detail-wrapper m-t-24">
        <resource-slider
          class="resource-wrapper"
          v-if="resourceList && resourceList.length"
          :list="resourceList"
        />
        <div
          class="detail-content rich-text"
          v-html="$options.filters.richTextFilter(info.deDesc)"
        ></div>
        <div class="detail-card-delivery">
          <card-delivery
            @successHandle="getDemandDetail"
            :info="info"
            :bs-type="3"
          />
        </div>
      </div>
    </div>
    <consult-visible :visible.sync="dialogVisible" :bs-type="3" :info="info" />
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import ResourceSlider from '../components/resourceSlider'
import CardDelivery from '../components/cardDelivery'
import ConsultVisible from '@/views/enterprise/services/industryInternet/industryInternet-basic/detail/components/consultVisible'
import { getServeType } from '@/views/enterprise/services/industryInternet/industryInternet-basic/utils/status'
import { getDemandDetail } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'DemandDetail',
  components: {
    ConsultVisible,
    CardDelivery,
    ResourceSlider,
    ModuleHeader,
    Breadcrumb
  },
  data() {
    return {
      getServeType,
      dialogVisible: false,
      info: {},
      resourceList: []
    }
  },
  mounted() {
    this.getDemandDetail()
  },
  methods: {
    enterpriseHome() {
      if (this.info.currentEnt)
        return this.$router.push('/services/internet/corporateHome')
      this.$router.push({
        path: '/services/internet/corporateHome',
        query: { indId: this.info.indId }
      })
    },
    getDemandDetail() {
      getDemandDetail({ id: this.$route.query.id }).then(res => {
        this.info = res || {}
        this.resourceList = res?.demandAttach?.industryInternet || []
      })
    },
    coverImg(item) {
      if (item?.demandAttach?.industryInternet) {
        return item?.demandAttach?.industryInternet[0]?.path
      } else {
        return '-'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-header {
  .introduction {
    @include font_color_mix(--color-black, #fff, 40%);
  }
  .vertical {
    width: 1px;
    height: 88px;
    background: #ebedf1;
    margin: 0 32px;
    flex-shrink: 0;
  }
  .header-right {
    flex-shrink: 0;
    .el-button + .el-button {
      margin: 16px 0 0;
    }
  }
}
.detail-wrapper {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  padding: 24px;
  .resource-wrapper {
    border-bottom: 1px solid #e9f0ff;
    margin-bottom: 32px;
    padding-bottom: 32px;
  }
  .detail-content {
    padding-bottom: 24px;
    min-height: 300px;
  }
  .detail-card-delivery {
    border-top: 1px solid #e9f0ff;
    padding-top: 21px;
  }
}
</style>
