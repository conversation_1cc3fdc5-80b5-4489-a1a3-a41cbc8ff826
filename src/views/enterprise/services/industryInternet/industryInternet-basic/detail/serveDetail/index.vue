<template>
  <div>
    <module-header :img="require('../../images/info-header-bg.png')">
      <breadcrumb class="p-t-32" />
      <div class="detail-header m-t-10 flex align-items-center">
        <div class="serve-cover">
          <el-image
            v-show="coverImg(info)"
            class="wh100"
            :src="coverImg(info)"
            fit="cover"
          />
          <svg-icon
            v-if="info.svSts === 2"
            icon-class="auth"
            class-name="color-primary"
          />
        </div>
        <div class="p-l-16">
          <div
            class="line-height-24 line-1 flex align-items-center"
            style="width: 803px"
          >
            <basic-tag
              class="inline-block"
              type="primary"
              :label="info.svTypeStr"
            />
            <span class="p-l-4">{{ info.svName }}</span>
          </div>
          <div class="m-t-8 line-height-24 line-2 introduction">
            {{ info.svProfile }}
          </div>
          <div class="m-t-4 flex justify-content-between align-center">
            <div class="font-size-14 line-height-22 line-1">
              <svg-icon
                icon-class="enterprise-name"
                class-name="color-primary"
              />
              <span class="p-l-8">{{ info.enterpriseName }}</span>
            </div>
          </div>
        </div>
        <div class="vertical"></div>
        <div class="header-right flex flex-direction-column align-items-center">
          <div v-if="info.svWay === 3" class="font-size-14 line-height-20">
            ¥{{ NumFormat(info.svPrice) }}元
          </div>
          <div v-else class="font-size-14 line-height-20">
            {{ getServeType(info.svWay) }}
          </div>
          <div class="font-size-12 line-height-22 m-t-10 color-text-regular">
            最后更新时间：{{ info.pubDate }}
          </div>
          <el-button type="primary" class="m-t-10" @click="dialogVisible = true"
            >咨询我们</el-button
          >
        </div>
      </div>
    </module-header>
    <div class="lateral-wrapper">
      <div class="detail-wrapper m-t-24">
        <resource-slider
          class="resource-wrapper"
          v-if="resourceList && resourceList.length"
          :list="resourceList"
        />
        <div
          class="detail-content rich-text"
          v-html="$options.filters.richTextFilter(info.svDesc)"
        ></div>
        <div class="detail-card-delivery">
          <card-delivery
            @successHandle="getServeDetail"
            :info="info"
            :bs-type="2"
          />
        </div>
      </div>
    </div>
    <consult-visible :visible.sync="dialogVisible" :bs-type="2" :info="info" />
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import ResourceSlider from '../components/resourceSlider'
import CardDelivery from '../components/cardDelivery'
import { getServeDetail } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'
import { getServeType } from '@/views/enterprise/services/industryInternet/industryInternet-basic/utils/status'
import ConsultVisible from '@/views/enterprise/services/industryInternet/industryInternet-basic/detail/components/consultVisible'
import { NumFormat } from '@/utils/tools'

export default {
  name: 'ServeDetail',
  components: {
    ConsultVisible,
    CardDelivery,
    ResourceSlider,
    ModuleHeader,
    Breadcrumb
  },
  data() {
    return {
      NumFormat,
      getServeType,
      dialogVisible: false,
      info: {},
      resourceList: []
    }
  },
  mounted() {
    this.getServeDetail()
  },
  methods: {
    getServeDetail() {
      getServeDetail({ id: this.$route.query.id }).then(res => {
        this.info = res || {}
        this.resourceList = res?.serveAttach?.industryInternet || []
      })
    },
    coverImg(item) {
      if (item?.serveLogoAttach?.industryInternet) {
        return item?.serveLogoAttach?.industryInternet[0]?.path
      } else {
        return '-'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-header {
  .serve-cover {
    width: 170px !important;
    height: 102px !important;
    border-radius: 3px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    .svg-icon {
      position: absolute;
      top: 8px;
      left: 8px;
    }
  }
  .introduction {
    @include font_color_mix(--color-black, #fff, 40%);
  }
  .vertical {
    width: 1px;
    height: 88px;
    background: #ebedf1;
    margin: 0 32px;
    flex-shrink: 0;
  }
  .header-right {
    flex-shrink: 0;
  }
}
.detail-wrapper {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  padding: 24px;
  .resource-wrapper {
    border-bottom: 1px solid #e9f0ff;
    margin-bottom: 32px;
    padding-bottom: 32px;
  }
  .detail-content {
    padding-bottom: 24px;
    min-height: 300px;
  }
  .detail-card-delivery {
    border-top: 1px solid #e9f0ff;
    padding-top: 21px;
  }
}
</style>
