<template>
  <div>
    <module-header :img="require('../../images/info-header-bg.png')">
      <breadcrumb class="p-t-32" />
      <div
        class="detail-header m-t-10 flex align-items-center justify-content-between"
      >
        <div class="flex align-items-center">
          <div class="product-cover">
            <el-image
              v-show="coverImg(info)"
              class="wh100"
              :src="coverImg(info)"
              fit="cover"
            />
            <svg-icon
              v-if="info.pdSts === 2"
              icon-class="auth"
              class-name="color-primary"
            />
          </div>
          <div class="p-l-16" style="max-width: 860px">
            <div class="line-height-24 line-1">
              {{ info.pdName }}
            </div>
            <div class="m-t-16 line-height-24 line-2 introduction">
              {{ info.pdProfile }}
            </div>
          </div>
        </div>
        <div class="flex align-items-center">
          <div class="vertical"></div>
          <div class="flex flex-direction-column">
            <el-button type="info" @click="enterpriseHome">企业主页</el-button>
            <el-button
              type="primary"
              @click="websiteHandle"
              :disabled="!info.pdWebsite"
              >官网浏览</el-button
            >
          </div>
        </div>
      </div>
    </module-header>
    <div class="lateral-wrapper">
      <div class="detail-wrapper m-t-24">
        <resource-slider
          class="resource-wrapper"
          v-if="resourceList && resourceList.length"
          :list="resourceList"
        />
        <div
          class="detail-content rich-text"
          v-html="$options.filters.richTextFilter(info.pdDesc)"
        ></div>
        <div class="detail-card-delivery">
          <card-delivery
            @successHandle="getProductionDetail"
            :info="info"
            :bs-type="1"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import ResourceSlider from '../components/resourceSlider'
import CardDelivery from '../components/cardDelivery'
import { getProductionDetail } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'ProductDetail',
  components: { CardDelivery, ResourceSlider, ModuleHeader, Breadcrumb },
  data() {
    return {
      info: {},
      resourceList: []
    }
  },
  mounted() {
    this.getProductionDetail()
  },
  methods: {
    websiteHandle() {
      window.open(this.info.pdWebsite, '_blank')
    },
    enterpriseHome() {
      if (this.info.currentEnt)
        return this.$router.push('/services/internet/corporateHome')
      this.$router.push({
        path: '/services/internet/corporateHome',
        query: { indId: this.info.indId }
      })
    },
    getProductionDetail() {
      getProductionDetail({ id: this.$route.query.id }).then(res => {
        this.info = res || {}
        this.resourceList = res?.productAttach?.industryInternet || []
      })
    },
    coverImg(item) {
      if (item?.logoAttach?.industryInternet) {
        return item?.logoAttach?.industryInternet[0]?.path
      } else {
        return '-'
      }
    }
  }
}
</script>

<style scoped lang="scss">
.detail-header {
  .product-cover {
    width: 170px !important;
    height: 102px !important;
    border-radius: 3px;
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
    .svg-icon {
      position: absolute;
      top: 8px;
      left: 8px;
    }
  }
  .introduction {
    @include font_color_mix(--color-black, #fff, 40%);
  }
  .vertical {
    width: 1px;
    height: 88px;
    background: #ebedf1;
    margin: 0 32px;
    flex-shrink: 0;
  }
  .el-button + .el-button {
    margin: 16px 0 0;
  }
}
.detail-wrapper {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  padding: 24px;
  .resource-wrapper {
    border-bottom: 1px solid #e9f0ff;
    margin-bottom: 32px;
    padding-bottom: 32px;
  }
  .detail-content {
    padding-bottom: 24px;
    min-height: 300px;
  }
  .detail-card-delivery {
    border-top: 1px solid #e9f0ff;
    padding-top: 21px;
  }
}
</style>
