<template>
  <dialog-cmp
    title="咨询我们"
    :visible.sync="dialogVisible"
    width="600px"
    @confirmDialog="confirmDialog"
  >
    <template v-if="dialogVisible">
      <driven-form
        v-if="!submitSuccess"
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
        label-position="top"
      />
      <div
        v-else
        class="flex flex-direction-column align-items-center p-t-24 p-b-24"
      >
        <svg-icon
          icon-class="check-circle-filled"
          class-name="color-success submit-success-icon"
        />
        <span class="m-t-16">提交成功</span>
        <span class="m-t-16"
          >您提交的咨询内容已成功发送给企业，稍后会发送短信再次通知企业</span
        >
      </div>
    </template>
  </dialog-cmp>
</template>

<script>
import DescriptorMixin from './descriptor'
import { indConsultCreate } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'ConsultVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 业务类型;1-产品；2-服务；3-需求；4-企业主页
    bsType: {
      type: Number,
      default: 1
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      dialogVisible: false,
      fromModel: {},
      submitSuccess: false
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.submitSuccess = false
        this.fromModel = this.$options.data().fromModel
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    confirmDialog() {
      if (this.submitSuccess) return (this.dialogVisible = false)
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel,
          bsType: this.bsType,
          bsId: this.info.id,
          entId: this.info.entId,
          attachIds: this.fromModel.attachIds
            ? this.fromModel.attachIds.map(item => item.id)
            : []
        }
        indConsultCreate(params).then(() => {
          this.submitSuccess = true
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.submit-success-icon {
  font-size: 60px;
}
</style>
