<template>
  <div>
    <div class="flex justify-content-between align-items-center">
      <template v-if="info.currentEnt">
        <div class="font-size-14 line-height-22">
          累计
          <span class="color-primary">{{ info.sendCount || 0 }}家</span>
          企业给您投递了名片
          <span class="m-l-16">已回复了</span>
          <span class="color-success m-l-4">{{ info.respCount || 0 }}家</span>
        </div>
        <el-button type="primary" @click="goCardBook">查看名片薄</el-button>
      </template>
      <template v-else>
        <template v-if="info.deliveryStatus === -1">
          <div class="font-size-14 line-height-22">
            如果您对我们的产品感兴趣，可以给我们投递名片
          </div>
          <el-button type="primary" @click="changeVisible = true"
            >投递名片</el-button
          >
        </template>
        <template
          v-else-if="info.deliveryStatus === 1 || info.deliveryStatus === 3"
        >
          <div class="font-size-14 line-height-22">
            您已投递了名片，等待企业反馈
          </div>
          <el-button type="info">已投递</el-button>
        </template>
        <template v-else-if="info.deliveryStatus === 2">
          <div class="font-size-14 line-height-22">
            您投递的名片有新的反馈..请点击查看
          </div>
          <el-button type="primary" @click="goCardDetail">查看名片</el-button>
        </template>
      </template>
    </div>
    <delivery-visible
      :visible.sync="changeVisible"
      @successHandle="successHandle"
      :card-list="cardList"
      :bs-type="bsType"
      :info="info"
    />
  </div>
</template>

<script>
import DeliveryVisible from '../../../components/DeliveryVisible'
import {
  indCardList,
  indDeliveryRecordReadUser
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'CardDelivery',
  props: {
    // 业务类型 1-产品；2-服务；3-需求；4-企业
    bsType: {
      type: Number,
      default: 1
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  components: { DeliveryVisible },
  data() {
    return {
      status: 1,
      changeVisible: false,
      cardList: []
    }
  },
  created() {
    this.indCardList()
  },
  methods: {
    indCardList() {
      indCardList().then(res => {
        this.cardList = res || []
      })
    },
    successHandle() {
      this.$emit('successHandle')
    },
    goCardDetail() {
      indDeliveryRecordReadUser({
        id: this.info.dvId
      })
      this.$router.push({
        path: '/services/internet/deliveryDetails',
        query: {
          id: this.info.dvId
        }
      })
    },
    goCardBook() {
      this.$router.push({
        name: 'EnterpriseCard',
        params: {
          tabType: 3
        }
      })
    }
  }
}
</script>

<style scoped lang="scss"></style>
