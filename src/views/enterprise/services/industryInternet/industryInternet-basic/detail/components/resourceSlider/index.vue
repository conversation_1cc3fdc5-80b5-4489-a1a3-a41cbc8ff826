<template>
  <div class="slider-container w100 flex align-items-center">
    <div class="handoff-wrapper">
      <div
        v-if="handoff"
        class="font-size-17 m-r-24 pointer"
        @click="moveChange('pre')"
      >
        <svg-icon icon-class="chevron-left" />
      </div>
    </div>
    <div class="slider-wrapper" ref="sliderWrapper">
      <div class="slider-content">
        <div
          class="slider-item"
          v-for="item in list"
          :key="'slider-' + item.id"
        >
          <template v-if="isVideoHandle(item)">
            <video-player :source="getSource(item)" />
            <div
              class="video-player-opacity"
              @click="videoPlayer(item.path)"
            ></div>
          </template>
          <el-image
            v-else
            class="item-resource"
            :src="item.path"
            fit="cover"
            alt=""
            @click="previewFile(item.path)"
          />
        </div>
      </div>
    </div>
    <div v-if="handoff" class="handoff-wrapper">
      <div class="font-size-17 m-l-24 pointer" @click="moveChange('next')">
        <svg-icon icon-class="chevron-right" />
      </div>
    </div>
    <img-viewer ref="viewer" />
    <dialog-cmp
      title="视频播放"
      :visible.sync="videoVisible"
      width="1000px"
      :have-operation="false"
    >
      <video-player v-if="videoVisible" :source="source" ref="videoPlayer" />
    </dialog-cmp>
  </div>
</template>

<script>
import BScroll from 'better-scroll'
import ImgViewer from '@/components/ImgViewer'
import VideoPlayer from '@/components/VideoPlayer'
import { judgementImg, judgementVideo } from '@/utils/tools'

export default {
  name: 'ResourceSlider',
  props: {
    list: {
      type: Array,
      default: () => []
    }
  },
  components: {
    VideoPlayer,
    ImgViewer
  },
  data() {
    return {
      slider: null,
      currentPageX: 0,
      source: {
        src: ''
      },
      videoVisible: false,
      imgList: []
    }
  },
  computed: {
    handoff() {
      return this.list && this.list.length > 4
    }
  },
  watch: {
    list: {
      async handler(val) {
        this.imgList = []
        val.forEach(item => {
          if (judgementImg(item.extentionName)) {
            this.imgList.push(item.path)
          }
        })
        await this.$nextTick()
        this.initSlider()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    this.slider && this.slider.destroy()
    this.slider = null
  },
  methods: {
    getSource(row) {
      return {
        src: row.path
      }
    },
    isVideoHandle(row) {
      return judgementVideo(row.extentionName)
    },
    // 视频播放
    async videoPlayer(path) {
      this.videoVisible = true
      this.source.src = path
      await this.$nextTick()
      this.$refs.videoPlayer.player.play()
    },
    // 图片预览
    previewFile(path) {
      const index = this.imgList.map(item => item).indexOf(path)
      this.$refs.viewer.show(this.imgList, index)
    },
    // 左右切换
    moveChange(type) {
      const maxScrollX = this.slider.maxScrollX
      const onceScroll = 268
      this.currentPageX =
        type === 'pre'
          ? this.currentPageX + onceScroll
          : this.currentPageX - onceScroll
      if (this.currentPageX >= onceScroll) this.currentPageX = 0
      if (this.currentPageX <= maxScrollX) this.currentPageX = maxScrollX
      this.slider.scrollTo(this.currentPageX, 0, 300)
    },
    // 滚动初始化
    initSlider() {
      this.slider = new BScroll(this.$refs.sliderWrapper, {
        click: true,
        scrollX: true,
        scrollY: false,
        momentum: false,
        bounce: false,
        stopPropagation: true, //取消冒泡
        probeType: 2
      })
      this.slider.on('scrollEnd', page => {
        this.currentPageX = page.x
      })
    }
  }
}
</script>

<style scoped lang="scss">
.slider-container {
  user-select: none;
  .handoff-wrapper {
    width: 44px;
  }
  .slider-wrapper {
    width: calc(100% - 88px);
    overflow: hidden;
    .slider-content {
      display: inline-block;
      width: max-content;
      height: 148px;
      .slider-item {
        display: inline-block;
        width: 255px;
        height: 148px;
        border-radius: 3px;
        overflow: hidden;
        cursor: pointer;
        position: relative;
        & + .slider-item {
          margin-left: 13px;
        }
        .video-player-opacity {
          position: absolute;
          inset: 0;
        }
        .item-resource {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
</style>
