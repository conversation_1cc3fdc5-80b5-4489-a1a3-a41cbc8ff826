export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          name: {
            form: 'input',
            label: '如何称呼您？',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入称呼'
              }
            ],
            attrs: {
              maxLength: 20
            }
          },
          phone: {
            form: 'input',
            label: '我们联系您的方式是？',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式'
              }
            ],
            attrs: {
              maxLength: 32
            }
          },
          content: {
            form: 'input',
            label: '您主要咨询的内容是？',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入要咨询的内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 8,
              maxlength: 300,
              showWordLimit: true
            }
          },
          attachIds: {
            form: 'component',
            label: '是否需要上传一些附件？',
            rule: [
              {
                required: false,
                type: 'array',
                message: '请上传附件'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'industryInternet'
              },
              mulity: true,
              maxLength: 9,
              limit: 9,
              maxSize: 100
            }
          }
        }
      }
    }
  }
}
