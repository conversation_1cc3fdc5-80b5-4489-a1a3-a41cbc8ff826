<template>
  <div class="delivery-details-container">
    <module-header
      type="primary"
      title="投递详情"
      desc="构建靓丽的一道企业风景线"
      :img="require('../../images/header-bg.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper p-t-24">
      <div
        class="msg-container m-b-24"
        v-for="(item, index) in msgList"
        :key="index"
        :class="{ 'self-container': item.leftOrRight === 2 }"
      >
        <div class="msg-header flex justify-content-between">
          <div class="header-content flex align-items-center">
            <div class="logo">
              <el-image
                v-show="logo(item)"
                class="wh100"
                :src="logo(item)"
                fit="cover"
              />
            </div>
            <div class="header-info p-l-16">
              <div class="font-size-16 line-height-24 line-1">
                {{ item.enterpriseName }}
              </div>
              <div class="font-size-14 line-height-24 m-t-8 base-color line-1">
                {{ item.slogan }}
              </div>
            </div>
          </div>
          <div class="p-t-4" v-if="item.leftOrRight === 1 && item.indId">
            <el-button type="primary" @click="enterpriseHome(item)"
              >企业主页</el-button
            >
          </div>
        </div>
        <div class="p-24 msg-wrapper">
          <div class="msg-content p-16">
            <div v-if="index === 0" class="font-size-14 line-height-22 p-b-24">
              <span>有幸浏览了贵公司的</span>
              <el-link type="primary" @click="businessDetail(item)"
                >#{{ item.titleName }}##</el-link
              >
              <span>这是我的名片请查收。</span>
            </div>
            <div class="flex font-size-12 line-height-20 msg-text">
              <svg-icon class="msg-icon" icon-class="msg" />
              <div class="base-color p-l-10">{{ item.content }}</div>
            </div>
            <div
              class="base-color font-size-12 m-t-8 p-l-22 line-height-20"
              :class="{ 'p-r-22 p-l-0': item.leftOrRight === 2 }"
            >
              {{ item.sendTime }} {{ item.dvSubject === 2 ? '回递' : '投递' }}
            </div>
          </div>
          <div
            v-if="
              item.indCardRespVO && JSON.stringify(item.indCardRespVO) !== '{}'
            "
            class="m-t-24 self-card"
          >
            <business-card
              :item="item.indCardRespVO"
              :logo="cardLogo(item.indCardRespVO)"
              :is-turn="true"
            />
          </div>
        </div>
      </div>
      <div class="footer flex justify-content-between align-items-center">
        <el-input
          v-model="msgContent"
          placeholder="回复或回递名片"
          maxlength="300"
        >
          <template slot="prepend" v-if="cardId && cardId.length"
            >#{{ cardName }}#{{ cardPosition }}#</template
          >
          <div slot="suffix">
            <div class="pointer line-height-32" @click="selectCard">
              <span class="p-r-8 color-black">选择名片</span>
              <svg-icon icon-class="chevron-down" />
            </div>
            <el-select
              ref="cardSelect"
              v-model="cardId"
              placeholder="选择名片"
              class="card-select"
              popper-class="card-select-popper"
              multiple
              :multiple-limit="1"
              @change="cardChange"
            >
              <el-option
                v-for="item in cardList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div>
        </el-input>
        <el-button
          type="primary"
          class="m-l-8"
          :disabled="!msgContent"
          @click="replyHandle"
        >
          回复
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BusinessCard from '../../components/BusinessCard'
import {
  indCardList,
  indDeliveryRecordGetReq,
  indDeliveryRecordResp,
  indDeliveryRecordTalk
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'deliveryDetails',
  components: { BusinessCard, ModuleHeader },
  data() {
    return {
      msgList: [],
      msgContent: '',
      cardId: [],
      cardName: '',
      cardPosition: '',
      cardList: [],
      reqInfo: {}
    }
  },
  beforeDestroy() {
    this.copyrightHandle('block')
  },
  mounted() {
    this.copyrightHandle('none')
    this.indDeliveryRecordTalk()
    this.indCardList()
    this.indDeliveryRecordGetReq()
  },
  methods: {
    indDeliveryRecordGetReq() {
      indDeliveryRecordGetReq({ id: this.$route.query.id }).then(res => {
        this.reqInfo = res || {}
      })
    },
    replyHandle() {
      if (!this.msgContent) return false
      const params = {
        ...this.reqInfo,
        cardId: this.cardId.length ? this.cardId[0] : '',
        content: this.msgContent
      }
      indDeliveryRecordResp(params).then(() => {
        this.indDeliveryRecordTalk()
        this.cardId = []
        this.msgContent = ''
        this.cardName = ''
        this.cardPosition = ''
      })
    },
    indCardList() {
      indCardList().then(res => {
        this.cardList = res || []
      })
    },
    businessDetail(row) {
      const { bsType } = row
      switch (bsType) {
        case 1:
          this.$router.push({
            path: '/services/internet/corporateHomeProductDetail',
            query: { id: row.bsId }
          })
          break
        case 2:
          this.$router.push({
            path: '/services/internet/corporateHomeServeDetail',
            query: { id: row.bsId }
          })
          break
        case 3:
          this.$router.push({
            path: '/services/internet/corporateHomeDemandDetail',
            query: { id: row.bsId }
          })
          break
        case 4:
          this.$router.push({
            path: '/services/internet/corporateHome',
            query: { indId: row.bsId }
          })
          break
        default:
          break
      }
    },
    enterpriseHome(row) {
      this.$router.push({
        path: '/services/internet/corporateHome',
        query: { indId: row.indId }
      })
    },
    cardLogo(row) {
      if (row?.logoList?.industryInternet) {
        return row?.logoList?.industryInternet[0]
      } else {
        return {}
      }
    },
    logo(row) {
      const localLogo = row.logoList
      const qccLogo = row.qccLogo
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && !qccLogo)
        return require('../../images/default-avatar.png')
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && qccLogo)
        return row.qccLogo
      return localLogo.industryInternet[0].path
    },
    indDeliveryRecordTalk() {
      indDeliveryRecordTalk({ id: this.$route.query.id }).then(async res => {
        this.msgList = res || []
        await this.$nextTick()
        this.scrollBottom()
      })
    },
    // 滚动条置底
    scrollBottom() {
      const appMain = document.querySelector('.app-main')
      appMain.scrollTop = appMain.scrollHeight
    },
    // 选中名片
    cardChange(val) {
      if (val && val.length) {
        const id = val[0]
        const row = this.cardList.find(item => item.id === id)
        this.cardName = row.name
        this.cardPosition = row.position
      } else {
        this.cardName = ''
        this.cardPosition = ''
      }
    },
    selectCard() {
      this.$refs.cardSelect.toggleMenu()
    },
    // copyright显示隐藏
    copyrightHandle(display = 'block') {
      const copyright = document.querySelector('.copyright')
      copyright.style.display = display
    }
  }
}
</script>

<style scoped lang="scss">
.delivery-details-container {
  padding-bottom: 74px;
  .msg-container {
    background: #ffffff;
    border: 1px solid #e9f0ff;
    border-radius: 0 36px 36px;
    .msg-header {
      padding: 24px;
      border-bottom: 1px solid #e9f0ff;
      .header-content {
        .logo {
          width: 60px;
          height: 60px;
          border-radius: 3px;
        }
        .header-info {
          max-width: 1000px;
        }
      }
    }
    .msg-wrapper {
      display: flex;
      flex-direction: column;
      .msg-content {
        width: fit-content;
        max-width: 50%;
        background: #f9f9f9;
        box-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.06);
        border-radius: 0 12px 12px;
        .msg-icon {
          flex-shrink: 0;
          padding-top: 3px;
        }
      }
    }
    &.self-container {
      border-radius: 36px 0 36px 36px;
      .msg-header {
        justify-content: flex-end;
        text-align: right;
        .header-content {
          flex-flow: row-reverse;
          .logo {
            margin-left: 16px;
          }
        }
      }
      .msg-wrapper {
        align-items: self-end;
        .msg-content {
          text-align: right;
          border-radius: 12px 0 12px 12px;
          .msg-text {
            flex-flow: row-reverse;
            .base-color {
              padding-right: 10px;
            }
          }
        }
      }
      .self-card {
        display: flex;
        flex-flow: row-reverse;
      }
    }
  }
  .footer {
    width: 1200px;
    position: fixed;
    bottom: 0;
    padding: 16px 24px 24px;
    background: #fff;
    border: 1px solid #e9f0ff;
    :deep(.el-input-group__prepend) {
      background: transparent;
      color: initial;
    }
    :deep(.el-input__inner) {
      padding-right: 90px;
    }
    .el-input__suffix-inner {
      display: flex;
      align-items: center;
      .svg-icon {
        padding-top: 2px;
      }
      & > div {
        display: flex;
        align-items: center;
      }
    }
    .card-select {
      width: 0;
      padding: 0;
      border: 0;
      z-index: -1;
      overflow: hidden;
    }
  }
  .base-color {
    color: rgba(0, 0, 0, 0.6);
  }
}
</style>
<style lang="scss">
.card-select-popper {
  margin-left: -56px;
}
</style>
