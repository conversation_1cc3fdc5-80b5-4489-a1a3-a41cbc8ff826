<template>
  <div>
    <module-header
      type="primary"
      title="咨询详情"
      desc="构建靓丽的一道企业风景线"
      :img="require('../../images/header-bg.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper p-t-24">
      <el-row class="table-wrapper">
        <el-col :span="4">称呼</el-col>
        <el-col :span="8">{{ detailInfo.name }}</el-col>
        <el-col :span="4">联系方式</el-col>
        <el-col :span="8">{{ detailInfo.phone }}</el-col>
        <el-col :span="4">咨询内容</el-col>
        <el-col :span="20">{{ detailInfo.content }}</el-col>
        <el-col :span="4">相关附件</el-col>
        <el-col :span="20">
          <template v-if="attachMap && attachMap.length">
            <el-link
              class="attach-wrapper"
              v-for="item in attachMap"
              :key="item.id"
              type="primary"
              @click="attachHandle(item)"
              >{{ item.name }}</el-link
            >
          </template>
          <span v-else>无</span>
        </el-col>
        <el-col :span="4">咨询时间</el-col>
        <el-col :span="20">{{ detailInfo.createTime }}</el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import { getConsultDetail } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'
export default {
  name: 'ConsultDetails',
  components: { ModuleHeader },
  data() {
    return {
      detailInfo: {}
    }
  },
  computed: {
    attachMap() {
      const { attachMap = {} } = this.detailInfo
      const list = attachMap.informationAttach || attachMap.industryInternet
      if (list && list.length) {
        return list
      } else {
        return []
      }
    }
  },
  created() {
    this.getConsultDetail()
  },
  methods: {
    attachHandle(row) {
      window.open(row.path, '_blank')
    },
    getConsultDetail() {
      getConsultDetail({ id: this.$route.query.id }).then(res => {
        this.detailInfo = res || {}
      })
    }
  }
}
</script>

<style scoped lang="scss">
.table-wrapper {
  border-left: 1px solid;
  border-top: 1px solid;
  border-color: #e7e7e7;
  .attach-wrapper {
    & + .attach-wrapper {
      margin-left: 16px;
    }
  }
}
:deep(.el-row) {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
:deep(.el-col) {
  padding: 14px 25px;
  line-height: 22px;
  font-size: 14px;
  color: #666;
  background: #fff;
  border-bottom: 1px solid #e7e7e7;
  border-right: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
:deep(.el-col-4) {
  color: #999;
  background: rgba(231, 231, 231, 0.5);
}
</style>
