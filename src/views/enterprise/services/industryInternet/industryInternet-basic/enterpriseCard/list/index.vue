<template>
  <div>
    <module-header
      type="primary"
      title="企业名片"
      desc="筑牢企业人脉不可忽略的方式"
      :img="require('../../images/header-bg.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper p-t-32">
      <basic-tab
        :current="current"
        :tabs-data="tabsData"
        @tabsChange="tabsChange"
      >
        <el-button
          v-if="current !== 4"
          slot="right"
          type="primary"
          @click="addHandle"
          >新增名片</el-button
        >
      </basic-tab>
      <component :is="componentName" />
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab'
import EnterpriseBusinessCard from './EnterpriseBusinessCard'
import MyDelivery from './MyDelivery'
import MyReceived from './MyReceived'
import CardBook from './CardBook'
import ConsultCenter from './ConsultCenter'

export default {
  name: 'EnterpriseCard',
  components: {
    ConsultCenter,
    CardBook,
    MyReceived,
    MyDelivery,
    EnterpriseBusinessCard,
    BasicTab,
    ModuleHeader
  },
  data() {
    return {
      current: 0,
      tabsData: [
        { label: '企业名片', value: 0 },
        { label: '我投递的', value: 1 },
        { label: '我收到的', value: 2 },
        { label: '名片簿', value: 3 },
        { label: '咨询中心', value: 4 }
      ]
    }
  },
  computed: {
    componentName() {
      const comObj = {
        0: 'EnterpriseBusinessCard',
        1: 'MyDelivery',
        2: 'MyReceived',
        3: 'CardBook',
        4: 'ConsultCenter'
      }
      return comObj[this.current]
    }
  },
  created() {
    const { tabType } = this.$route.params
    if (tabType) this.tabsChange(Number(tabType))
  },
  methods: {
    addHandle() {
      this.$router.push('/services/internet/enterpriseCardCreate')
    },
    tabsChange(e) {
      this.current = e
    }
  }
}
</script>

<style scoped></style>
