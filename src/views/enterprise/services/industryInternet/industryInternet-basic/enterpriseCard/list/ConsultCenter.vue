<template>
  <drive-table
    ref="drive-table"
    :columns="tableColumn"
    :api-fn="IndConsultPage"
  />
</template>

<script>
import {
  IndConsultPage,
  indConsultRead
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'ConsultCenter',
  data() {
    return {
      IndConsultPage,
      tableColumn: [
        {
          prop: 'createTime',
          label: '咨询时间',
          align: 'center'
        },
        {
          prop: 'name',
          label: '称呼',
          align: 'center'
        },
        {
          prop: 'bsType',
          label: '咨询场景',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.bsType === 1 && <span>找产品</span>}
                {scope.row.bsType === 2 && <span>找服务</span>}
                {scope.row.bsType === 3 && <span>找需求</span>}
                {scope.row.bsType === 4 && <span>找企业</span>}
              </div>
            )
          }
        },
        {
          prop: 'status',
          label: '咨询反馈',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                {!scope.row.status ? (
                  <span class="color-warning">未读</span>
                ) : (
                  <span class="color-success">已读</span>
                )}
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          align: 'center',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onclick={() => {
                  this.detailHandle(scope.row)
                }}
              >
                查看
              </el-link>
            )
          }
        }
      ]
    }
  },
  methods: {
    detailHandle(row) {
      if (!row.status) indConsultRead({ id: row.id })
      this.$router.push({
        path: '/services/internet/consultDetails',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style scoped></style>
