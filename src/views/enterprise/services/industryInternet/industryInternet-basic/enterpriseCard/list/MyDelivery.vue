<template>
  <drive-table
    ref="drive-table"
    :columns="tableColumn"
    :api-fn="indDeliveryRecordPage"
    :extral-querys="extralQuerys"
  />
</template>

<script>
import { indDeliveryRecordPage } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'MyDelivery',
  data() {
    return {
      indDeliveryRecordPage,
      extralQuerys: {
        type: 1
      },
      tableColumn: [
        {
          prop: 'sendTime',
          label: '投递时间',
          align: 'center'
        },
        {
          prop: 'enterpriseName',
          label: '投递企业',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <div>
                {scope.row.indId && (
                  <el-link
                    type="primary"
                    style="display: initial"
                    onclick={() => {
                      this.enterpriseHandle(scope.row)
                    }}
                  >
                    {scope.row.enterpriseName}
                  </el-link>
                )}
                {!scope.row.indId && <span>{scope.row.enterpriseName}</span>}
              </div>
            )
          }
        },
        {
          prop: 'type',
          label: '投递场景',
          align: 'center'
        },
        {
          prop: 'status',
          label: '投递反馈',
          align: 'center',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.respSts === 1 && (
                  <span class="color-warning">{scope.row.status}</span>
                )}
                {scope.row.respSts === 2 && (
                  <span class="color-success">{scope.row.status}</span>
                )}
                {scope.row.respSts === 3 && (
                  <span class="color-primary">{scope.row.status}</span>
                )}
              </div>
            )
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          align: 'center',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onclick={() => {
                  this.detailHandle(scope.row)
                }}
              >
                查看
              </el-link>
            )
          }
        }
      ]
    }
  },
  methods: {
    enterpriseHandle(row) {
      this.$router.push({
        path: '/services/internet/corporateHome',
        query: { indId: row.indId }
      })
    },
    detailHandle(row) {
      this.$router.push({
        path: '/services/internet/deliveryDetails',
        query: { id: row.id }
      })
    }
  }
}
</script>

<style scoped></style>
