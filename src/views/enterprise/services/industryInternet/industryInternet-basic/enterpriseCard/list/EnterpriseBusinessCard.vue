<template>
  <module-list ref="ModuleList" :api-fn="indCardPage">
    <template slot-scope="scope">
      <div class="flex flex-wrap justify-content-between p-t-16">
        <div
          class="item-container"
          v-for="(item, index) in scope.data"
          :key="index"
        >
          <div class="flex align-items-center justify-content-between">
            <tag
              type="info"
              :label="`${item.createUser} | ${item.createTime}制作`"
            />
            <el-dropdown @command="handleCommand($event, item)">
              <span @click.stop class="color-black more-wrapper">
                <svg-icon
                  style="padding-top: 3px; flex-shrink: 0"
                  icon-class="ellipsis"
                  class-name="pointer"
                />
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="1">
                  <span>编辑</span>
                </el-dropdown-item>
                <el-dropdown-item :command="2">
                  <span class="color-danger">删除</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <business-card class="m-t-17" :item="item" :logo="getLogo(item)" />
        </div>
      </div>
    </template>
  </module-list>
</template>

<script>
import ModuleList from '@/components/Lateral/ModuleList'
import Tag from '../../components/Tag'
import BusinessCard from '../../components/BusinessCard'
import {
  indCardDelete,
  indCardPage
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'EnterpriseBusinessCard',
  components: { BusinessCard, Tag, ModuleList },
  data() {
    return {
      indCardPage
    }
  },
  methods: {
    handleCommand(command, row) {
      switch (command) {
        case 1:
          this.$router.push({
            path: '/services/internet/enterpriseCardCreate',
            query: {
              id: row.id
            }
          })
          break
        case 2:
          this.$confirm('确定删除该名片？').then(() => {
            indCardDelete({ id: row.id }).then(() => {
              this.$toast.success(`删除成功`)
              this.$refs.ModuleList.refresh()
            })
          })
          break
        default:
          break
      }
    },
    getLogo(item) {
      return item?.logoList?.industryInternet[0]
    }
  }
}
</script>

<style scoped lang="scss">
.item-container {
  width: 584px;
  margin-top: 24px;
  &:first-child {
    margin-top: 0;
  }
  &:nth-child(2) {
    margin-top: 0;
  }
  .more-wrapper {
    &:hover {
      @include font_color(--color-primary);
    }
  }
}
:deep(.table-wrapper) {
  margin-top: 0;
}
</style>
