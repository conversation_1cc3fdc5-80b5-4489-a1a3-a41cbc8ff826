<template>
  <div>
    <el-form inline :model="formModel" class="m-b-16">
      <el-form-item label="企业名称">
        <el-input
          v-model="formModel.enterpriseName"
          placeholder="请输入企业名称"
          @change="searchHandle"
          clearable
        />
      </el-form-item>
      <el-form-item label="姓名">
        <el-input
          v-model="formModel.name"
          placeholder="请输入姓名"
          @change="searchHandle"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机号">
        <el-input
          v-model="formModel.phone"
          placeholder="请输入手机号"
          @change="searchHandle"
          clearable
        />
      </el-form-item>
    </el-form>
    <module-list ref="ModuleList" :api-fn="indCardPageAll">
      <template slot-scope="scope">
        <div class="flex flex-wrap justify-content-between">
          <div
            class="item-container"
            v-for="(item, index) in scope.data"
            :key="index"
          >
            <business-card
              class="m-t-17"
              :item="item"
              :is-turn="true"
              :logo="logo(item)"
            />
          </div>
        </div>
      </template>
    </module-list>
  </div>
</template>

<script>
import ModuleList from '@/components/Lateral/ModuleList'
import BusinessCard from '../../components/BusinessCard'
import { indCardPageAll } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'CardBook',
  components: { BusinessCard, ModuleList },
  data() {
    return {
      indCardPageAll,
      formModel: {
        enterpriseName: '',
        name: '',
        phone: ''
      }
    }
  },
  methods: {
    logo(row) {
      return row?.logoList?.industryInternet[0] || {}
    },
    searchHandle() {
      this.$refs.ModuleList.triggerSearch(this.formModel)
    }
  }
}
</script>

<style scoped lang="scss">
.item-container {
  width: 584px;
  margin-top: 24px;
  &:first-child {
    margin-top: 0;
  }
  &:nth-child(2) {
    margin-top: 0;
  }
  .more-wrapper {
    &:hover {
      @include font_color(--color-primary);
    }
  }
}
:deep(.el-form) {
  display: flex;
  justify-content: space-between;
  .el-form-item {
    margin-bottom: 0;
    .el-input__inner {
      width: 300px;
    }
  }
}
</style>
