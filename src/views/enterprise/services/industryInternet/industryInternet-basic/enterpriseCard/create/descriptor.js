export default {
  data() {
    return {
      formConfigure: {
        labelWidth: 'auto',
        descriptors: {
          name: {
            form: 'input',
            label: '姓名',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入姓名'
              }
            ],
            attrs: {
              maxLength: 4
            }
          },
          egName: {
            form: 'input',
            label: '英语名或拼音名',
            span: 12,
            placeholder: '请输入英语名或拼音名',
            rule: [
              {
                required: true,
                type: 'string',
                validator: (rule, value, callback) => {
                  if (!value) return callback(new Error('请输入英语名或拼音名'))
                  const reg = /^[a-zA-Z\s]*$/
                  if (!reg.test(value))
                    return callback(new Error('只能输入英文字母'))
                  callback()
                }
              }
            ],
            attrs: {
              maxLength: 30
            }
          },
          position: {
            form: 'input',
            label: '职务',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入职务'
              }
            ],
            attrs: {
              maxLength: 15
            }
          },
          email: {
            form: 'input',
            label: '邮箱',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入邮箱'
              },
              {
                validator: 'validateEmail'
              }
            ]
          },
          phone: {
            form: 'input',
            label: '手机号',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入手机号'
              },
              {
                validator: 'validatePhone'
              }
            ]
          },
          enterpriseName: {
            form: 'input',
            label: '公司名称',
            span: 12,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入公司名称'
              }
            ]
          },
          address: {
            form: 'input',
            label: '地址',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入地址'
              }
            ],
            attrs: {
              maxLength: 25
            }
          },
          slogan: {
            form: 'input',
            label: 'slogan',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入slogan'
              }
            ],
            attrs: {
              maxLength: 15
            }
          },
          userName: {
            form: 'input',
            label: '关联账号',
            span: 12,
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择关联账号'
              }
            ]
          },
          logoAttachId: {
            form: 'component',
            label: '公司logo',
            span: 12,
            rule: [
              {
                required: true,
                type: 'array',
                message: '请上传公司logo'
              }
            ],
            componentName: 'uploader',
            props: {
              uploadData: {
                type: 'industryInternet'
              },
              onlyForView: false,
              accept: 'image/*',
              maxLength: 1,
              limit: 1,
              maxSize: 10
            }
          }
        }
      }
    }
  }
}
