<template>
  <div>
    <module-header
      type="primary"
      title="新增名片"
      desc="筑牢企业人脉不可忽略的方式"
      :img="require('../../images/header-bg.png')"
      :imgOpacity="1"
    >
      <breadcrumb class="p-t-32" />
      <h2
        class="p-t-32 flex align-items-center font-size-20 line-height-28 font-strong color-text-primary"
      >
        {{ $route.query.id ? '编辑名片' : '新增名片' }}
      </h2>
      <p class="font-size-12 line-height-22 color-text-regular">
        筑牢企业人脉不可忽略的方式
      </p>
    </module-header>
    <div class="lateral-wrapper">
      <div class="p-t-32">
        <div class="font-size-16 line-height-24">名片预览</div>
        <tag
          v-if="$route.query.id"
          type="info"
          :label="`${fromModel.createUser} | ${fromModel.createTime}制作`"
          class="m-t-26"
        />
        <div class="flex justify-content-between m-t-17">
          <business-card :item="fromModel" :logo="logo" />
          <business-card :item="fromModel" :is-front="false" :logo="logo" />
        </div>
      </div>
      <div class="create-wrapper">
        <div class="font-size-16 line-height-24">名片编辑</div>
        <driven-form
          class="form-container"
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
        <div class="btn-wrapper">
          <el-button type="primary" @click="goBack">取消</el-button>
          <el-button type="success" @click="submitHandle">确定</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import DescriptorMixin from './descriptor'
import Tag from '../../components/Tag'
import BusinessCard from '../../components/BusinessCard'
import {
  getCardDetail,
  getIndInfo,
  indCardCreate,
  indCardUpdate
} from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'
import { mapGetters } from 'vuex'

export default {
  name: 'EnterpriseCardCreate',
  components: { ModuleHeader, Breadcrumb, Tag, BusinessCard },
  mixins: [DescriptorMixin],
  data() {
    return {
      fromModel: {}
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    logo() {
      if (!this.fromModel.logoAttachId || !this.fromModel.logoAttachId.length)
        return {}
      return this.fromModel.logoAttachId[0]
    }
  },
  created() {
    this.getIndInfo()
    const id = this.$route.query.id
    this.$route.meta.title = id ? '编辑名片' : '新增名片'
    if (id) {
      this.getCardDetail(id)
    }
  },
  methods: {
    getIndInfo() {
      getIndInfo().then(res => {
        this.$set(this.fromModel, 'enterpriseName', res.enterpriseName || '')
        this.$set(
          this.fromModel,
          'userName',
          this.userInfo.acName || this.userInfo.nickname || ''
        )
        this.$set(this.fromModel, 'userId', this.userInfo.userId)
      })
    },
    getCardDetail(id) {
      getCardDetail({ id }).then(res => {
        const { logoList = {} } = res
        this.fromModel = {
          ...res,
          logoAttachId: logoList.industryInternet || []
        }
      })
    },
    goBack() {
      this.$router.go(-1)
    },
    operateTips(text) {
      this.$toast.success(`${text}成功`)
      this.goBack()
    },
    submitHandle() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel,
          logoAttachId: this.fromModel.logoAttachId
            ? this.fromModel.logoAttachId.map(item => item.id)
            : []
        }
        if (this.$route.query.id) {
          indCardUpdate(params).then(() => {
            this.operateTips('编辑')
          })
        } else {
          indCardCreate(params).then(() => {
            this.operateTips('新增')
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.create-wrapper {
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  padding: 32px 64px 0;
  margin: 24px 0;
  .form-container {
    margin-top: 45px;
  }
  .btn-wrapper {
    padding: 16px 0;
    border-top: 1px solid #e9f0ff;
    text-align: right;
  }
}
</style>
