<template>
  <div class="demand-item" @click="detailHandle">
    <div
      class="w100 flex align-items-center justify-content-between line-height-24"
    >
      <div class="flex align-items-center">
        <tag v-if="item.deSts === 3" type="info" label="已被园区下架" />
        <tag
          v-if="item.disappear && item.deSts !== 3"
          type="warning"
          label="隐藏中"
        />
        <svg-icon
          v-if="item.deSts === 2"
          icon-class="auth"
          class-name="color-primary m-l-4"
        />
        <div class="item-title line-1">
          {{ item.deName }}
        </div>
      </div>
      <svg-icon icon-class="swap-right" />
    </div>
    <div class="m-t-8 flex align-items-center">
      <basic-tag type="primary" :label="item.deTypeStr" />
      <div
        class="color-text-placeholder line-1 m-l-8 font-size-12 line-height-20"
      >
        {{ item.parkName }}
      </div>
    </div>
    <div class="m-t-8 font-size-14 line-height-22 line-2 color-text-regular">
      {{ item.deProfile }}
    </div>
    <div
      class="m-t-16 p-t-16 item-bottom flex justify-content-between align-items-center"
    >
      <div class="flex align-items-center">
        <div class="item-cover">
          <el-image v-show="logo" class="wh100" :src="logo" fit="cover" />
        </div>
        <div
          class="flex align-items-center font-size-14 line-height-22 p-l-8 p-r-8"
        >
          <div class="item-enterprise-name line-1 p-r-4">
            {{ item.enterpriseName }}
          </div>
          <svg-icon icon-class="chevron-right" />
        </div>
      </div>
      <operate
        v-if="hasOperate"
        class="m-t-30"
        :item="item"
        @handleCommand="handleCommand"
      />
    </div>
  </div>
</template>

<script>
import Operate from '../components/Operate'
import Tag from '../components/Tag'

export default {
  name: 'DemandCard',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    hasOperate: {
      type: Boolean,
      default: true
    },
    hasTag: {
      type: Boolean,
      default: true
    }
  },
  components: { Operate, Tag },
  computed: {
    logo() {
      const localLogo = this.item.localLogo
      const qccLogo = this.item.qccLogo
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && !qccLogo)
        return require('../images/default-avatar.png')
      if ((!localLogo || JSON.stringify(localLogo) === '{}') && qccLogo)
        return this.item.qccLogo
      return localLogo.industryInternet[0].path
    }
  },
  methods: {
    handleCommand(command, row) {
      this.$emit('handleCommand', command, row)
    },
    detailHandle() {
      this.$emit('detailHandle', this.item)
    }
  }
}
</script>

<style scoped lang="scss">
.demand-item {
  width: 584px;
  background: #ffffff;
  border: 1px solid #fdecee;
  border-radius: 6px;
  padding: 24px 16px;
  margin-bottom: 32px;
  cursor: pointer;
  transition: all 300ms linear;
  &:hover {
    box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.05);
  }
  &:first-child {
    margin-top: 0;
  }
  &:nth-child(2) {
    margin-top: 0;
  }
  .item-title {
    max-width: 414px;
    padding-left: 4px;
  }
  .swap-right {
    float: right;
  }
  .item-bottom {
    border-top: 1px solid #e9f0ff;
    .item-cover {
      width: 52px;
      height: 52px;
      border-radius: 3px;
    }
    .item-enterprise-name {
      max-width: 400px;
    }
  }
}
</style>
