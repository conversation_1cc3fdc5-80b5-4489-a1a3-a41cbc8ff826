<template>
  <el-dropdown @command="handleCommand">
    <span @click.stop>
      <svg-icon
        style="padding-top: 3px; flex-shrink: 0"
        icon-class="ellipsis"
        class-name="pointer color-black"
      />
    </span>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item :command="1" v-if="item.pdSts !== 3">
        <span>{{ item.disappear ? '取消隐藏' : '隐藏' }}</span>
      </el-dropdown-item>
      <el-dropdown-item :command="2">
        <span class="color-primary">编辑</span>
      </el-dropdown-item>
      <el-dropdown-item :command="3">
        <span class="color-danger">删除</span>
      </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: 'Operate',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    handleCommand(command) {
      this.$emit('handleCommand', command, this.item)
    }
  }
}
</script>

<style scoped></style>
