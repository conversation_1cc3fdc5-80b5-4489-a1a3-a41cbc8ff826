<template>
  <div class="tag-container" :class="'tag-' + type">{{ label }}</div>
</template>

<script>
export default {
  name: 'Tag',
  props: {
    type: {
      type: String,
      default: 'primary'
    },
    label: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.tag-container {
  width: fit-content;
  font-size: 12px;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.9);
  padding: 2px 8px;
  border-radius: 3px;
  &.tag-primary {
    @include background_color(--color-primary);
  }
  &.tag-danger {
    @include background_color(--color-danger);
  }
  &.tag-warning {
    @include background_color(--color-warning);
  }
  &.tag-success {
    @include background_color(--color-success);
  }
  &.tag-info {
    @include background_color(--color-text-secondary);
  }
}
</style>
