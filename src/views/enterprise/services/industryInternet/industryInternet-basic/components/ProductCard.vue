<template>
  <div class="product-item" @click="detailHandle">
    <div class="item-bg-wrapper">
      <div class="wh100">
        <el-image
          v-show="coverImg(item)"
          class="wh100"
          :src="coverImg(item)"
          fit="cover"
        />
      </div>
      <svg-icon
        v-if="item.pdSts === 2"
        icon-class="auth"
        class-name="color-primary"
      />
      <div class="item-tag" v-if="hasTag">
        <tag v-if="item.pdSts === 3" type="info" label="已被园区下架" />
        <tag
          v-if="item.disappear && item.pdSts !== 3"
          type="warning"
          label="隐藏中"
        />
      </div>
    </div>
    <div class="item-wrapper">
      <div class="line-1 line-height-24">
        {{ item.pdName }}
      </div>
      <div class="color-text-regular m-t-8 font-size-14 line-2 line-height-22">
        {{ item.pdProfile }}
      </div>
      <div class="m-t-26 flex justify-content-between align-center">
        <div class="font-size-14 line-height-22 line-1">
          <svg-icon icon-class="enterprise-name" class-name="color-primary" />
          <span class="p-l-8 color-text-regular">
            {{ item.enterpriseName }}
          </span>
        </div>
        <operate
          v-if="hasOperate"
          :item="item"
          @handleCommand="handleCommand"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Operate from '../components/Operate'
import Tag from '../components/Tag'

export default {
  name: 'ProductCard',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    hasOperate: {
      type: Boolean,
      default: true
    },
    hasTag: {
      type: Boolean,
      default: true
    }
  },
  components: { Operate, Tag },
  methods: {
    handleCommand(command, row) {
      this.$emit('handleCommand', command, row)
    },
    coverImg(item) {
      if (item?.logoAttach?.industryInternet) {
        return item?.logoAttach?.industryInternet[0]?.path
      } else {
        return '-'
      }
    },
    detailHandle() {
      this.$emit('detailHandle', this.item)
    }
  }
}
</script>

<style scoped lang="scss">
.product-item {
  width: 584px;
  background: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8f8f2;
  padding: 8px;
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  cursor: pointer;
  transition: all 300ms linear;
  &:hover {
    box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.05);
  }
  &:first-child {
    margin-top: 0;
  }
  &:nth-child(2) {
    margin-top: 0;
  }
  .item-bg-wrapper {
    width: 248px;
    height: 148px;
    border-radius: 3px;
    overflow: hidden;
    position: relative;
    .svg-icon {
      position: absolute;
      top: 8px;
      left: 8px;
    }
    .item-tag {
      position: absolute;
      top: 8px;
      right: 8px;
    }
  }
  .item-wrapper {
    margin-left: 16px;
    width: calc(100% - 248px - 16px);
  }
}
</style>
