<template>
  <div class="serve-item" @click="detailHandle">
    <div class="angle-wrapper">
      <angle-status type="primary" :text="item.svTypeStr" />
    </div>
    <div class="item-bg-wrapper">
      <div class="wh100">
        <el-image
          v-show="coverImg(item)"
          class="wh100"
          :src="coverImg(item)"
          fit="cover"
        />
      </div>
      <svg-icon
        v-if="item.svSts === 2"
        icon-class="auth"
        class-name="color-primary"
      />
      <div class="item-tag" v-if="hasTag">
        <tag v-if="item.svSts === 3" type="info" label="已被园区下架" />
        <tag
          v-if="item.disappear && item.svSts !== 3"
          type="warning"
          label="隐藏中"
        />
      </div>
    </div>
    <div class="item-wrapper">
      <div class="line-1 line-height-24 m-t-8">
        {{ item.svName }}
      </div>
      <div class="m-t-8 flex justify-content-between align-center">
        <div class="font-size-14 line-height-22 line-1">
          <svg-icon icon-class="enterprise-name" class-name="color-primary" />
          <span class="p-l-8 color-text-regular">{{
            item.enterpriseName
          }}</span>
        </div>
      </div>
      <div class="m-t-8 flex justify-content-between align-center">
        <div v-if="item.svWay === 3" class="color-warning line-height-24">
          ¥{{ NumFormat(item.svPrice) }} <span class="font-size-12">元</span>
        </div>
        <div v-else class="color-warning line-height-24">
          {{ getServeType(item.svWay) }}
        </div>
        <operate
          v-if="hasOperate"
          class="m-r-10"
          :item="item"
          @handleCommand="handleCommand"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import Operate from '../components/Operate'
import Tag from '../components/Tag'
import AngleStatus from '@/components/Lateral/AngleStatus'
import { getServeType } from '@/views/enterprise/services/industryInternet/industryInternet-basic/utils/status'

export default {
  name: 'ServeCard',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    hasOperate: {
      type: Boolean,
      default: true
    },
    hasTag: {
      type: Boolean,
      default: true
    }
  },
  components: { AngleStatus, Operate, Tag },
  data() {
    return {
      NumFormat,
      getServeType
    }
  },
  methods: {
    handleCommand(command, row) {
      this.$emit('handleCommand', command, row)
    },
    coverImg(item) {
      if (item?.serveLogoAttach?.industryInternet) {
        return item?.serveLogoAttach?.industryInternet[0]?.path
      } else {
        return '-'
      }
    },
    detailHandle() {
      this.$emit('detailHandle', this.item)
    }
  }
}
</script>

<style scoped lang="scss">
.serve-wrapper {
  margin-bottom: -32px;
  .serve-item {
    width: 276px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e8f8f2;
    padding: 8px;
    margin-bottom: 32px;
    cursor: pointer;
    position: relative;
    margin-right: 32px;
    transition: all 300ms linear;
    &:hover {
      box-shadow: 0 6px 10px 0 rgba(0, 0, 0, 0.05);
    }
    &:nth-child(4n) {
      margin-right: 0;
    }
    .angle-wrapper {
      position: absolute;
      top: 16px;
      left: -4px;
    }
    .item-bg-wrapper {
      width: 100%;
      height: 220px;
      border-radius: 3px;
      overflow: hidden;
      position: relative;
      .svg-icon {
        position: absolute;
        top: 8px;
        right: 8px;
      }
      .item-tag {
        position: absolute;
        top: 8px;
        right: 8px;
      }
    }
  }
}
</style>
