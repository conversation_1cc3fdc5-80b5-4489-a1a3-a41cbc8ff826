<template>
  <div
    ref="businessCardContainer"
    @mouseleave="mouseleave"
    class="business-card-container pos-relative"
    :class="{ 'default-reverse': !isFront }"
  >
    <div
      ref="frontContainer"
      class="business-card-wrapper front-container wh100"
    >
      <div
        class="label-color flex justify-content-between align-items-center line-height-24"
      >
        <div class="flex align-items-center">
          <span class="font-size-24">{{ item.name || emptyData }}</span>
          <svg-icon
            v-if="item.idSts === 1"
            class-name="m-l-8 color-primary font-size-20"
            icon-class="auth"
          />
          <tag
            v-if="item.idSts === 2"
            class="m-l-8"
            type="info"
            label="已被园区下架"
          />
        </div>
        <span class="font-size-18">{{ item.position || emptyData }}</span>
      </div>
      <div
        class="font-size-18 line-height-24 m-t-10 content-color flex justify-content-between"
      >
        <div>{{ item.egName || emptyData }}</div>
        <el-tooltip effect="dark" content="查看背面" placement="top">
          <svg-icon
            v-if="isTurn"
            icon-class="swap-border"
            class-name="pointer"
            @click="flipHandle"
          />
        </el-tooltip>
      </div>
      <div class="card-content flex">
        <div class="content-main p-r-16">
          <div class="font-size-14 line-height-22">
            {{ item.enterpriseName || emptyData }}
          </div>
          <div class="font-size-12 line-height-22 m-t-16">
            <svg-icon icon-class="location-1" class-name="color-primary" />
            <span class="p-l-10 content-color"
              >地址: {{ item.address || emptyData }}</span
            >
          </div>
          <div class="font-size-12 line-height-22 m-t-9">
            <svg-icon icon-class="mail-1" class-name="color-primary" />
            <span class="p-l-10 content-color"
              >E-mail: {{ item.email || emptyData }}</span
            >
          </div>
          <div class="font-size-12 line-height-22 m-t-7">
            <svg-icon icon-class="phone" class-name="color-primary" />
            <span class="p-l-10 content-color"
              >Mobile: {{ item.phone || emptyData }}</span
            >
          </div>
        </div>
        <div class="rq-code">
          <img
            class="wh100"
            src="../../../../../../assets/images/layout/qrcode_wechat.jpg"
            alt=""
          />
        </div>
      </div>
    </div>
    <div
      ref="reverseContainer"
      class="business-card-wrapper reverse-container wh100 flex align-items-center justify-content-center"
    >
      <div class="logo-wrapper">
        <div class="logo-content wh100">
          <el-image class="wh100" :src="coverImg" fit="cover" />
        </div>
      </div>
      <div class="m-l-32">
        <div class="font-size-24 line-height-24 label-color">
          {{ item.slogan || emptyData }}
        </div>
        <div class="p-t-16 font-size-18 line-height-24 base-color">
          {{ item.enterpriseName || emptyData }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Tag from './Tag'

export default {
  name: 'BusinessCard',
  props: {
    // 是否正面
    isFront: {
      type: Boolean,
      default: true
    },
    item: {
      type: Object,
      default: () => ({})
    },
    // 是否翻转
    isTurn: {
      type: Boolean,
      default: false
    },
    logo: {
      type: Object,
      default: () => ({})
    }
  },
  components: { Tag },
  data() {
    return {
      emptyData: '****'
    }
  },
  computed: {
    coverImg() {
      if (!this.logo || JSON.stringify(this.logo) === '{}')
        return require('../images/default-avatar.png')
      return this.logo.path
    }
  },
  methods: {
    mouseleave() {
      if (!this.isFront) return false
      this.$refs.frontContainer.style.transform = 'rotateY(0deg)'
      this.$refs.reverseContainer.style.transform = 'rotateY(-180deg)'
    },
    flipHandle() {
      this.$refs.frontContainer.style.transform = 'rotateY(-180deg)'
      this.$refs.reverseContainer.style.transform = 'rotateY(-360deg)'
    }
  }
}
</script>

<style scoped lang="scss">
.business-card-container {
  height: 282px;
  width: 584px;
  .business-card-wrapper {
    background: #eaf1ff;
    border-radius: 6px;
    padding: 24px 33px 26px 44px;
    user-select: none;
    &.front-container {
      position: absolute;
      perspective: 1000px;
      transition: 0.5s ease-in-out;
    }
    &.reverse-container {
      position: absolute;
      backface-visibility: hidden;
      perspective: 1000px;
      transform: rotateY(-180deg);
      transition: 0.5s ease-in-out;
    }
    .svg-icon {
      &:hover {
        @include font_color(--color-primary);
      }
    }
    .label-color {
      @include font_color_mix(--color-black, #fff, 10%);
    }
    .content-color {
      @include font_color_mix(--color-black, #fff, 20%);
    }
    .card-content {
      margin-top: 50px;
      position: relative;
      .content-main {
        width: calc(100% - 104px);
      }
      .rq-code {
        width: 104px;
        height: 104px;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
    .logo-wrapper {
      width: 104px;
      height: 104px;
      border: 1px solid rgba(5, 76, 232, 0.2);
      padding: 4px;
      flex-shrink: 0;
      .logo-content {
        background: rgba(5, 76, 232, 0.06);
        padding: 8px;
      }
    }
    .base-color {
      @include font_color_mix(--color-black, #fff, 40%);
    }
  }
  &.default-reverse {
    .front-container {
      transform: rotateY(-180deg);
    }
    .reverse-container {
      transform: rotateY(-360deg);
    }
  }
}
</style>
