import CardPreview from './CardPreview'

export default {
  components: { CardPreview },
  data() {
    return {
      formConfigure: {
        labelWidth: '120px',
        descriptors: {
          cardId: {
            form: 'select',
            label: '选择名片',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择名片'
              }
            ],
            options: []
          },
          cardPreview: {
            form: 'component',
            label: '当前名片',
            hidden: true,
            rule: [
              {
                required: false,
                type: 'string',
                message: '请选择名片'
              }
            ],
            render: () => {
              return <card-preview item={this.cardInfo} />
            }
          },
          content: {
            form: 'input',
            label: '投递留言',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入投递留言'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 4,
              maxlength: 300,
              showWordLimit: true
            }
          }
        }
      }
    }
  }
}
