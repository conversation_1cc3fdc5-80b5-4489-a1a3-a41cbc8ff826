<template>
  <dialog-cmp
    title="投递名片"
    :visible.sync="deliveryVisible"
    @confirmDialog="confirmDialog"
    width="1248px"
  >
    <driven-form
      v-if="deliveryVisible"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
      label-position="top"
    />
  </dialog-cmp>
</template>

<script>
import DescriptorMixin from './descriptor'
import { indDeliveryRecordCreate } from '@/views/enterprise/services/industryInternet/industryInternet-basic/api'

export default {
  name: 'DeliveryVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cardList: {
      type: Array,
      default: () => []
    },
    // 业务类型 1-产品；2-服务；3-需求；4-企业
    bsType: {
      type: Number,
      default: 1
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [DescriptorMixin],
  data() {
    return {
      deliveryVisible: false,
      fromModel: {},
      cardInfo: {}
    }
  },
  watch: {
    'fromModel.cardId'(val) {
      this.formConfigure.descriptors.cardPreview.hidden = !val
      this.cardInfo = this.cardList.find(item => item.id === val) || {}
    },
    cardList: {
      handler(val) {
        this.formConfigure.descriptors.cardId.options = val.map(item => {
          return {
            label: item.name,
            value: item.id
          }
        })
      },
      immediate: true,
      deep: true
    },
    visible(val) {
      this.deliveryVisible = val
    },
    deliveryVisible(val) {
      if (!val) {
        this.fromModel = this.$options.data().fromModel
        this.$refs['driven-form'].resetFields()
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const params = {
          ...this.fromModel,
          bsType: this.bsType,
          bsId: this.info.id,
          rcvEntId: this.info.entId
        }
        indDeliveryRecordCreate(params).then(() => {
          this.$toast.success('投递成功')
          this.$emit('successHandle')
          this.deliveryVisible = false
        })
      })
    }
  }
}
</script>

<style scoped></style>
