<template>
  <div class="flex justify-content-between">
    <business-card :item="item" :logo="logo" />
    <business-card :item="item" :is-front="false" :logo="logo" />
  </div>
</template>

<script>
import BusinessCard from '../BusinessCard'

export default {
  name: 'CardPreview',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  components: { BusinessCard },
  computed: {
    logo() {
      return this.item?.logoList?.industryInternet[0] || {}
    }
  }
}
</script>

<style scoped></style>
