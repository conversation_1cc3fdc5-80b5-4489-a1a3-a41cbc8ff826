import request from '@/utils/request'

// 获取机构列表
export function getProductList(params) {
  return request({
    url: `/organization/ent/org-product/page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取机构详情
export function getProductDetail(id) {
  return request({
    url: `/organization/ent/org-product/get?id=${id}`,
    method: 'get'
  })
}

// 获取联系方式
export function getContract(id) {
  return request({
    url: `/organization/ent/org-product/getPhone?id=${id}`,
    method: 'get'
  })
}
