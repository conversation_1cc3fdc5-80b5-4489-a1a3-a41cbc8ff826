<template>
  <div class="product-list-wrapper pointer" @click="toProductDetail">
    <div class="title">
      <p class="line-1 font-size-16 line-height-24">{{ product.name }}</p>
      <svg-icon icon-class="swap-right" />
    </div>
    <el-tag type="primary">{{ product.type }}</el-tag>
    <p class="line-2 font-size-14 line-height-22 color-text-regular">
      {{ product.orgProductDesc }}
    </p>
  </div>
</template>

<script>
export default {
  name: 'MechanismProductCmp',
  props: {
    product: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    toProductDetail() {
      this.$router.push({
        path: '/redirect/services/company/product/detail',
        query: {
          id: this.product.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.product-list-wrapper {
  width: 276px;
  height: 148px;
  padding: 16px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  .title {
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    p {
      flex: 1;
    }
    .svg-icon {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
