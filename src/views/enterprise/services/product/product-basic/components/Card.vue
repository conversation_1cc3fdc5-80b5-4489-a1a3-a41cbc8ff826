<template>
  <div class="card" @click="toDetail">
    <div class="card-wrapper">
      <div class="product-info w100">
        <div class="flex align-items-center justify-content-between w100">
          <div class="font-size-14 color-text-primary line-height-24 line-1">
            {{ list.name }}
          </div>

          <div class="font-size-20 color-text-primary">
            <svg-icon icon-class="swap-right" />
          </div>
        </div>

        <div class="m-t-8 m-b-8">
          <basic-tag :label="list.type" />
        </div>

        <div class="content font-size-14 color-text-secondary line-2">
          {{ list.orgProductDesc }}
        </div>
      </div>

      <div
        class="mechanism-info flex align-items-center p-t-16"
        @click.stop="toMechanismDetail"
      >
        <div class="left flex align-items-center">
          <div class="left-icon m-r-8">
            <el-image
              class="w100 h100"
              v-if="list.orgInfoAttachMap"
              :src="mechanismLogo"
              alt=""
            />
          </div>
          <div class="font-size-14 color-text-primary line-1 mechanism-name">
            {{ list.orgInfoName }}
          </div>
        </div>
        <div class="right">
          <svg-icon icon-class="chevron-right" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MechanismCard',
  props: {
    list: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    mechanismLogo() {
      return this.list.orgInfoAttachMap.mechanismLogo
        ? this.list.orgInfoAttachMap.mechanismLogo[0].path
        : ''
    }
  },
  methods: {
    toDetail() {
      this.$router.push({
        path: '/services/company/product/detail',
        query: {
          id: this.list.id
        }
      })
    },
    toMechanismDetail() {
      this.$router.push({
        path: '/services/company/mechanism/detail',
        query: {
          id: this.list.orgInfoId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  cursor: pointer;

  .card-wrapper {
    padding: 16px;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    border-radius: 6px;

    .product-info {
      padding-bottom: 16px;
      border-bottom-width: 1px;
      border-style: solid;
      @include border_color(--border-color-lighter);
      .content {
        height: 44px;
        line-height: 22px;
      }
    }

    .mechanism-info {
      height: 52px;
      width: 100%;

      .left {
        flex: 1;
        min-width: 0;
        .left-icon {
          flex: 0 0 52px;
          width: 52px;
          height: 52px;
          > img {
            width: 100%;
            height: 100%;
          }
        }

        .mechanism-name {
          flex: 1;
        }
      }

      .right {
        flex: 0 0 16px;
        margin-left: 16px;
      }
    }
  }
}
</style>
