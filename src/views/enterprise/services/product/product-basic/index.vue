<template>
  <div>
    <!-- 头部 -->
    <module-header
      type="primary"
      title="机构产品"
      desc="精选优质的机构产品服务，探索无限商机和服务"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <!-- 筛选项 -->
      <div class="module-filter">
        <module-filter :filters="filterData" @change="filterChange" />
      </div>

      <!-- 内容区域 -->
      <div class="module-list">
        <module-list ref="ModuleList" :api-fn="getProductList">
          <template slot-scope="scope">
            <div class="card-wrapper">
              <div class="card-list" v-for="data in scope.data" :key="data.id">
                <product-card :list="data" />
              </div>
            </div>
          </template>
        </module-list>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ModuleFilter from '@/components/Lateral/ModuleFilter'
import ModuleList from '@/components/Lateral/ModuleList'
import ProductCard from './components/Card'

import { getByTenantDictType } from '@/api/common'
import { getProductList } from './api'

export default {
  name: 'ProductBasic',
  components: {
    ModuleHeader,
    ModuleFilter,
    ModuleList,
    ProductCard
  },
  data() {
    return {
      getProductList,
      filterData: [
        {
          label: '产品类型',
          prop: 'type',
          list: []
        }
      ]
    }
  },
  methods: {
    getByTenantDictType() {
      getByTenantDictType('org_product_type').then(res => {
        this.$set(this.filterData[0], 'list', res)
      })
    },
    filterChange(filter) {
      this.$refs.ModuleList.triggerSearch(filter)
    }
  },
  created() {
    this.getByTenantDictType()
  }
}
</script>

<style lang="scss" scoped>
.module-filter {
  margin-top: 24px;
}
.module-list {
  margin-top: 16px;
}
:deep(.card-wrapper) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .card-list {
    width: 50%;
    margin-bottom: 32px;
    &:nth-child(odd) {
      padding-right: 14px;
    }
    &:nth-child(even) {
      padding-left: 14px;
    }
  }
}
</style>
