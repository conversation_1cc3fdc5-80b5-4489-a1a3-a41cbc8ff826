<template>
  <div class="product-basic-detail" v-if="detail">
    <module-header
      type="primary"
      :img="require('./images/header-bg.png')"
      :imgOpacity="0.2"
    >
      <div class="h100">
        <div class="m-t-30">
          <breadcrumb />
        </div>

        <div class="detail-title w100 m-t-16 line-2">
          <el-tag type="primary">{{ detail.type }}</el-tag>
          <span class="font-size-16 line-height-24 font-strong">
            {{ detail.name }}
          </span>
        </div>

        <div class="info-list flex align-items-center m-t-16">
          <div class="list">
            <icon-info
              icon-type="warning"
              icon-class="home"
              type="link"
              label="所属机构："
              :text="detail.orgInfoName + ' >'"
              @tapText="toMechanism"
            />
          </div>
          <div class="list">
            <icon-info
              icon-type="warning"
              icon-class="user"
              label="联系人："
              :text="detail.contact"
            />
          </div>
          <div class="list flex align-items-center">
            <icon-info
              icon-type="success"
              icon-class="call"
              label="联系方式："
              :text="detail.phone"
            />

            <el-button
              class="m-l-8"
              type="primary"
              size="mini"
              @click="getContract"
            >
              点击获取联系方式
            </el-button>
          </div>
        </div>
      </div>
    </module-header>

    <div class="m-t-32 m-b-20">
      <div class="lateral-wrapper">
        <div
          class="rich-text p-b-20"
          v-html="$options.filters.richTextFilter(detail.introduction)"
        ></div>

        <div class="product-content" v-if="showProducts">
          <h4 class="font-size-16 line-height-24 m-b-8 font-strong">
            推荐产品
          </h4>
          <div class="product-wrapper">
            <div
              class="product-list"
              v-for="product in detail.recommendProducts"
              :key="product.id"
            >
              <product-list :product="product" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <dialog-cmp title="联系方式" :visible.sync="visible" :haveOperation="false">
      <div class="dialog-container">
        <p class="font-size-16 line-height-28 color-text-regular m-b-12">
          联系人: {{ detail.contact }}
        </p>
        <p class="font-size-16 line-height-28 color-text-regular">
          联系人: {{ contract }}
        </p>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import IconInfo from '@/components/IconInfo'
import ProductList from './components/Product'
import { richTextFilter } from '@/filter'

import { getProductDetail, getContract } from './api'

export default {
  name: 'ProductDetail',
  components: {
    ModuleHeader,
    Breadcrumb,
    IconInfo,
    ProductList
  },
  data() {
    return {
      id: -1,
      detail: null,
      contract: '',
      visible: false
    }
  },
  filters: { richTextFilter },
  computed: {
    showProducts() {
      const products = this.detail.recommendProducts
      return products && products.length > 0
    }
  },
  methods: {
    getProductDetail() {
      getProductDetail(this.id).then(res => {
        this.detail = res
      })
    },
    getContract() {
      getContract(this.id).then(res => {
        this.contract = res
        this.visible = true
      })
    },
    toMechanism() {
      this.$router.push({
        path: '/services/company/mechanism/detail',
        query: {
          id: this.detail.orgInfoId
        }
      })
    }
  },
  created() {
    this.id = this.$route.query.id
    this.getProductDetail()
  }
}
</script>

<style lang="scss" scoped>
.info-list {
  .list {
    margin-right: 40px;
  }
}

.product-content {
  border-top-width: 1px;
  border-top-style: solid;
  @include border_color(--border-color-light);
  padding-top: 16px;
  .product-wrapper {
    .product-list {
      margin-right: 32px;
      display: inline-block;
      margin-bottom: 12px;
      &:nth-child(4n) {
        margin-right: 0;
      }
    }
  }
}
</style>
