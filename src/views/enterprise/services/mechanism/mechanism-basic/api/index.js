import request from '@/utils/request'

// 获取机构列表
export function getMechanismList(params) {
  return request({
    url: `/org/ent/info/page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取机构详情
export function getMechanismDetail(id) {
  return request({
    url: `/org/ent/info/get?id=${id}`,
    method: 'get'
  })
}

// 获取联系方式
export function getContract(id) {
  return request({
    url: `/org/ent/info/getphone?id=${id}`,
    method: 'get'
  })
}
