<template>
  <div>
    <!-- 头部 -->
    <module-header
      type="primary"
      title="服务机构"
      desc="打破企业间的隔阂，加强业务往来，碰撞出意想不到的商业火花"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <!-- 筛选项 -->
      <div class="module-filter">
        <module-filter :filters="filterData" @change="filterChange" />
      </div>

      <!-- 内容区域 -->
      <div class="module-list">
        <module-list ref="ModuleList" :api-fn="getMechanismList">
          <template slot-scope="scope">
            <div class="card-wrapper">
              <div class="card-list" v-for="data in scope.data" :key="data.id">
                <mechanism-card :list="data" />
              </div>
            </div>
          </template>
        </module-list>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ModuleFilter from '@/components/Lateral/ModuleFilter'
import ModuleList from '@/components/Lateral/ModuleList'
import MechanismCard from './components/Card'

import { getByTenantDictType } from '@/api/common'
import { getMechanismList } from './api'

export default {
  name: 'MechanismBasic',
  components: {
    ModuleHeader,
    ModuleFilter,
    ModuleList,
    MechanismCard
  },
  data() {
    return {
      getMechanismList,
      filterData: [
        {
          label: '机构类型',
          prop: 'type',
          list: []
        }
      ]
    }
  },
  methods: {
    getByTenantDictType() {
      getByTenantDictType('org_info_type').then(res => {
        this.$set(this.filterData[0], 'list', res)
      })
    },
    filterChange(filter) {
      this.$refs.ModuleList.triggerSearch(filter)
    }
  },
  created() {
    this.getByTenantDictType()
  }
}
</script>

<style lang="scss" scoped>
.module-filter {
  margin-top: 24px;
}
.module-list {
  margin-top: 16px;
}
:deep(.card-wrapper) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .card-list {
    width: 50%;
    margin-bottom: 32px;
    &:nth-child(odd) {
      padding-right: 14px;
    }
    &:nth-child(even) {
      padding-left: 14px;
    }
  }
}
</style>
