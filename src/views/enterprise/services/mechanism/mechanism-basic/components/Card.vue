<template>
  <div class="card" @click="toDetail">
    <div class="card-wrapper">
      <div class="card-header w100 p-b-16">
        <div class="card-header-left">
          <img v-if="list.attachMap" :src="getCover(list)" alt="" />
        </div>
        <div class="card-header-right">
          <div class="title flex align-items-center justify-content-between">
            <div
              class="name line-1 font-size-14 color-text-primary line-height-24"
            >
              {{ list.name }}
            </div>

            <div class="icon font-size-20 color-text-primary">
              <svg-icon icon-class="swap-right" />
            </div>
          </div>

          <div class="m-t-8 m-b-8">
            <basic-tag :label="list.type" />
          </div>
        </div>
      </div>

      <div
        class="content font-size-14 line-height-22 color-text-secondary line-2"
      >
        {{ list.orgInfoDesc }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MechanismCard',
  props: {
    list: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    getCover(row) {
      const { attachMap = {} } = row
      if (attachMap.mechanismLogo && attachMap.mechanismLogo.length) {
        return attachMap.mechanismLogo[0].path
      } else {
        return ''
      }
    },
    toDetail() {
      this.$router.push({
        path: '/services/company/mechanism/detail',
        query: {
          id: this.list.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  position: relative;
  cursor: pointer;

  .card-wrapper {
    padding: 16px;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    border-radius: 6px;

    .card-header {
      display: flex;
      align-items: center;
      border-radius: 6px;
      .card-header-left {
        flex: 0 0 80px;
        width: 80px;
        height: 80px;

        > img {
          width: 100%;
          height: 100%;
        }
      }

      .card-header-right {
        flex: 1;
        padding-left: 16px;
        overflow: hidden;
        .title {
          display: flex;
          align-items: center;
          .name {
            flex: 1;
            padding-right: 20px;
          }
          .icon {
            flex: 0 024px;
          }
        }
      }
    }
    .content {
      height: 44px;
    }
  }
}
</style>
