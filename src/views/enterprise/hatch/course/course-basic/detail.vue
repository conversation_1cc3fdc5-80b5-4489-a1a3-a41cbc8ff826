<template>
  <div class="policy-basic-detail">
    <module-header
      type="primary"
      :img="require('./images/header-bg-2.png')"
      :imgOpacity="1"
    >
      <div class="h100">
        <div class="m-t-30">
          <breadcrumb />
        </div>
        <div class="m-t-14 flex">
          <div
            v-if="
              detail.courseMode === 0 || detail.courseMode === 2 || isColumn
            "
          >
            <el-image
              style="width: 170px; height: 102px"
              :preview-src-list="[courseUrl]"
              :src="
                courseUrl ? courseUrl : require('./images/course-default.png')
              "
            />
          </div>
          <div
            v-if="detail.courseMode === 1"
            class="pointer"
            @click="audioHandler"
          >
            <el-image
              style="width: 160px; height: 90px; border-radius: 3px"
              :src="require('./images/course-audio.png')"
            />
            <div class="flex justify-content-center" v-if="isPlay">
              <svg-icon icon-class="play" />
              <div class="m-l-4">播放</div>
            </div>
            <div class="flex justify-content-center" v-else>
              <svg-icon icon-class="stop" />
              <div class="m-l-4">暂停</div>
            </div>
          </div>
          <div class="m-l-16 pos-relative" style="flex: 1">
            <p class="line-height-24">
              <el-tag
                v-if="!isColumn"
                style="border-radius: 12px"
                type="primary"
                >{{ detail.cornerLabels | noData }}</el-tag
              >
              <el-tag v-else style="border-radius: 12px" type="primary">{{
                detail.stateStr | noData
              }}</el-tag>
              {{ detail.title | noData }}
            </p>
            <div class="flex justify-content-between">
              <span class="course-title-tag">
                {{ courseLabels | noData }}
              </span>
              <div
                class="m-t-4 flex align-items-center"
                v-if="detail.approach === 1"
              >
                <el-tag type="success" v-if="detail.application === 4"
                  >已开课</el-tag
                >
                <el-tag type="info" v-if="detail.application === 5"
                  >已结束</el-tag
                >
                <el-tag type="warning" v-if="detail.application === 1"
                  >人数已满</el-tag
                >
                <el-tag type="primary" v-if="detail.application === 3"
                  >已报名</el-tag
                >
                <el-button
                  @click="signUp"
                  v-if="detail.application === 2"
                  size="mini"
                  class="m-l-8"
                  type="primary"
                  >报名关注</el-button
                >
                <el-button
                  size="mini"
                  type="primary"
                  v-if="detail.application === 0"
                  @click="getCourseApplication"
                  >立即报名</el-button
                >
              </div>
            </div>
            <div class="m-t-8 flex justify-content-between">
              <div class="flex align-items-center font-size-12">
                <div class="flex align-items-center">
                  <svg-icon class="color-primary" icon-class="browse" />
                  <span class="m-l-4 color-s">{{
                    detail.numberViews | noData
                  }}</span>
                </div>
                <div class="flex align-items-center m-l-12" v-if="isColumn">
                  <svg-icon class="color-success" icon-class="calendar" />
                  <span class="m-l-4 color-s"
                    >{{ dataList.length | noData }}期</span
                  >
                </div>
                <div class="flex align-items-center" v-if="!isColumn">
                  <div
                    class="flex align-items-center m-l-12"
                    v-if="detail.courseStart"
                  >
                    <svg-icon icon-class="time" />
                    <span class="m-l-4 color-s">{{
                      detail.courseStart | noData
                    }}</span>
                  </div>
                  <div
                    class="flex align-items-center m-l-12"
                    v-if="detail.personStr"
                  >
                    <svg-icon class="color-warning" icon-class="user" />
                    <span class="m-l-4 color-s">{{
                      detail.personStr | noData
                    }}</span>
                  </div>
                </div>
              </div>
              <div class="flex align-items-center font-size-12">
                <div
                  class="flex align-items-center pointer"
                  @click="getCourseLike"
                >
                  <svg-icon
                    :class="detail.isLike ? 'color-primary' : ''"
                    icon-class="thumb-up"
                  />
                  <span class="m-l-4 color-s">{{
                    detail.numberLikes | noData
                  }}</span>
                </div>
                <!--                <el-popover placement="top-end" trigger="hover">-->
                <!--                  <div class="flex">-->
                <!--                    <div>-->
                <!--                      <el-image-->
                <!--                        style="width: 100px; height: 100px"-->
                <!--                        :src="-->
                <!--                          shareData.code-->
                <!--                            ? shareData.code-->
                <!--                            : require('@/assets/images/no-data.png')-->
                <!--                        "-->
                <!--                        fit="cover"-->
                <!--                      >-->
                <!--                      </el-image>-->
                <!--                      <div-->
                <!--                        class="font-size-14 m-t-4"-->
                <!--                        style="text-align: center"-->
                <!--                      >-->
                <!--                        微信扫码查看-->
                <!--                      </div>-->
                <!--                    </div>-->
                <!--                    <div class="m-l-16">-->
                <!--                      <div class="line-height-22">复制链接分享课程</div>-->
                <!--                      <div class="flex align-items-center line-height-22">-->
                <!--                        <div-->
                <!--                          style="width: 200px"-->
                <!--                          class="font-size-12 m-r-8 line-1"-->
                <!--                        >-->
                <!--                          {{ shareData.url | noData }}-->
                <!--                        </div>-->
                <!--                        <el-button type="text" @click="copyUrl(shareData.url)"-->
                <!--                          >复制网页链接</el-button-->
                <!--                        >-->
                <!--                      </div>-->
                <!--                      <div class="flex align-items-center line-height-22">-->
                <!--                        <div-->
                <!--                          style="width: 200px"-->
                <!--                          class="font-size-12 m-r-8 line-1"-->
                <!--                        >-->
                <!--                          {{ isHref }}-->
                <!--                        </div>-->
                <!--                        <el-button type="text" @click="copyUrl(isHref)"-->
                <!--                          >复制网页链接</el-button-->
                <!--                        >-->
                <!--                      </div>-->
                <!--                    </div>-->
                <!--                  </div>-->
                <!--                  <div-->
                <!--                    slot="reference"-->
                <!--                    class="flex align-items-center m-l-12 pointer"-->
                <!--                  >-->
                <!--                    <svg-icon icon-class="forward-right" />-->
                <!--                    <span class="m-l-4 color-s">{{-->
                <!--                      detail.numberShares | noData-->
                <!--                    }}</span>-->
                <!--                  </div>-->
                <!--                </el-popover>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </module-header>

    <div class="m-t-32 m-b-32">
      <div class="lateral-wrapper">
        <div class="wrapper">
          <!--          课程-->
          <div class="content" v-if="!isColumn">
            <!--          线下课程信息-->
            <div class="offline-course" v-if="detail.approach === 1">
              <div class="offlineCourse line-height-22">
                <div style="width: 50%" class="flex align-items-center m-b-8">
                  <svg-icon icon-class="account-name" />
                  <span class="m-l-4">{{ detail.speaker | noData }}</span>
                  <!--                <span>***********</span>-->
                </div>
                <div
                  style="width: 50%"
                  v-if="detail.applyStart && detail.applyEnd"
                  class="flex justify-content-end m-b-8 align-items-center"
                >
                  <svg-icon icon-class="time" />
                  <span class="m-l-4"
                    >{{ detail.applyStart }}~{{ detail.applyEnd }}</span
                  >
                </div>
                <div
                  style="width: 50%"
                  v-if="detail.applyStart && detail.applyEnd"
                  class="flex align-items-center m-b-8 line-height-22"
                >
                  <svg-icon icon-class="location" />
                  <span class="m-l-4">{{ detail.address | noData }}</span>
                </div>
                <div
                  style="width: 50%"
                  v-else
                  class="flex justify-content-end align-items-center m-b-8 line-height-22"
                >
                  <svg-icon icon-class="location" />
                  <span class="m-l-4">{{ detail.address | noData }}</span>
                </div>
              </div>
            </div>
            <div class="m-t-16" v-if="detail.courseMode === 2">
              <video-player :source="source" />
            </div>
            <div class="m-b-16 m-t-16">课程详情</div>
            <div class="line-height-22" v-html="detail.courseDetail"></div>
          </div>
          <div class="content" v-else>
            <basic-tab
              ref="basicTab"
              :tabs-data="list"
              :current="current"
              @tabsChange="tabsChange"
            />
            <div v-if="current === 0">
              <div
                class="font-size-14 line-height-22"
                v-html="detail.courseDetail"
              ></div>
            </div>
            <div v-if="current === 1">
              <div class="card-wrapper" v-if="dataList && dataList.length > 0">
                <div
                  class="card-list"
                  v-for="(data, index) in dataList"
                  :key="index"
                >
                  <course-card :list="data" />
                </div>
              </div>
              <div v-else>
                <empty-data />
              </div>
            </div>
          </div>
          <div class="files p-l-26">
            <div v-if="!isColumn">
              <h4 class="font-size-14 line-height-22 m-b-16">相关附件</h4>
              <file-list
                v-if="hasAttach && hasAttach.length > 0"
                :files="hasAttach"
                onlyForView
              />
              <div v-else class="empty-content">
                <empty-data description="暂无相关附件"> </empty-data>
              </div>
            </div>
            <h4 class="font-size-14 line-height-22 m-b-16 m-t-16">推荐课程</h4>
            <el-scrollbar style="width: 100%; height: 300px">
              <template v-if="courseList && courseList.length">
                <div
                  v-for="item in courseList"
                  :key="item.id"
                  class="m-r-10 pointer"
                  @click="toDetail(item.id, item.isColumn)"
                >
                  <div class="recommend">
                    <el-image
                      style="width: 190px; height: 119px"
                      :src="
                        item.titleUrl
                          ? item.titleUrl
                          : require('./images/course-default.png')
                      "
                    />
                    <div class="course-info">{{ item.courseModeStr }}</div>
                    <div class="course-litter"></div>
                  </div>
                  <div class="line-2 line-height-24 p-8">
                    {{ item.title | noData }}
                  </div>
                  <div class="flex m-b-8">
                    <icon-info
                      class="m-l-8"
                      icon-type="primary"
                      icon-class="browse"
                      label=""
                      :text="String(item.numberViews)"
                    />
                    <icon-info
                      v-if="item.approach === 1"
                      class="m-l-8"
                      icon-type="warning"
                      icon-class="user"
                      :text="`${item.personStr}`"
                    />
                  </div>
                </div>
              </template>
              <div v-else class="empty-content">
                <empty-data description="暂无推荐课程"></empty-data>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </div>

    <!--    报名关注-->
    <el-dialog
      title="报名关注"
      :visible.sync="visible"
      :modal-append-to-body="false"
      :append-to-body="true"
      width="25%"
    >
      <div class="wh100 p-24">
        <div class="qr-code">
          <img
            style="width: 200px; height: 200px"
            :src="attention ? attention : require('./images/example.png')"
          />
        </div>
        <div class="line-height-22 font-size-14 m-8" style="text-align: center">
          请用微信扫码加入课程讨论组！
        </div>
        <div
          class="line-height-22 font-size-16 m-8 font-strong"
          style="text-align: center"
        >
          {{ detail.applyText | noData }}
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BasicTab from '@/components/BasicTab'
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import IconInfo from '@/components/IconInfo'
import FileList from '@/components/Uploader/files'
import {
  getAddColumnShare,
  getAddCourseShare,
  getCourseAdminList,
  getCourseApplication,
  getCourseDetailColum,
  getCourseDetails,
  getCourseLike,
  getCourseLikeColumn,
  getMyColumnShare,
  getMyCourseRecommended,
  getMyCourseShare
} from './api'
import { richTextFilter } from '@/filter'
import VideoPlayer from '@/components/VideoPlayer'
import CourseCard from '@/views/enterprise/hatch/course/course-basic/components/Card'

export default {
  name: 'PolicyDetail',
  components: {
    CourseCard,
    VideoPlayer,
    ModuleHeader,
    Breadcrumb,
    IconInfo,
    FileList,
    BasicTab
  },
  data() {
    return {
      getCourseAdminList,
      extraQuery: {},
      visible: false,
      list: [
        {
          label: '专栏详情',
          value: 0
        },
        {
          label: '专栏内容',
          value: 1
        }
      ],
      current: 0,
      courseUrl: '',
      id: -1,
      detail: {},
      isPlay: true,
      source: {},
      codeUrl: '',
      courseList: [],
      dataList: [],
      isColumn: '',
      hasAttach: [],
      attention: '',
      shareData: {},
      isHref: '',
      audioUrl: null
    }
  },
  computed: {
    courseLabels() {
      const courseLabel = this.detail.labels
      return courseLabel ? courseLabel.join('/') : ''
    }
  },
  filters: { richTextFilter },
  methods: {
    toDetail(id, isColumn) {
      this.$router.push({
        path: '/hatch/interact/course/detail',
        query: {
          id,
          isColumn
        }
      })
    },
    // 专栏tab切换
    async tabsChange(e) {
      this.current = e
      const res = await getCourseAdminList({ columnId: this.id })
      this.dataList = res
    },
    // 专栏分享二维码
    async getMyColumnShare() {
      const res = await getMyColumnShare(this.id)
      this.shareData = res
      this.isHref = window.location.href
    },
    // 分享二维码
    async getMyCourseShare() {
      const res = await getMyCourseShare(this.id)
      this.shareData = res
      this.isHref = window.location.href
    },
    // 复制链接
    async copyUrl(val) {
      // 模拟 输入框
      let cInput = document.createElement('input')
      cInput.value = val
      document.body.appendChild(cInput)
      cInput.select() // 选取文本框内容

      // 执行浏览器复制命令
      // 复制命令会将当前选中的内容复制到剪切板中（这里就是创建的input标签）
      // Input要在正常的编辑状态下原生复制方法才会生效

      document.execCommand('copy')

      this.$toast.success('复制成功')
      // 复制成功后再将构造的标签 移除
      document.body.removeChild(cInput)
      if (this.isColumn) {
        await getAddColumnShare(this.id)
        this.getCourseDetailColum()
      } else {
        await getAddCourseShare(this.id)
        this.getCourseDetails()
      }
    },
    // 课程点赞
    async getCourseLike() {
      if (this.isColumn) {
        await getCourseLikeColumn(this.id)
      } else {
        await getCourseLike(this.id)
      }
      if (this.detail.isLike) {
        this.$toast.success('取消点赞成功')
      } else {
        this.$toast.success('点赞成功')
      }
      if (this.isColumn) {
        this.getCourseDetailColum()
      } else {
        this.getCourseDetails()
      }
    },
    // 打开报名关注弹窗
    signUp() {
      this.visible = true
    },
    // 课程报名
    async getCourseApplication() {
      await getCourseApplication({ id: this.id })
      this.$toast.success('报名成功')
      this.getCourseDetails()
    },
    // 音频
    audioHandler() {
      this.isPlay = !this.isPlay
      if (!this.isPlay) {
        this.audioUrl.play()
      } else {
        this.audioUrl.pause()
      }
    },
    // 课程详情
    getCourseDetails() {
      getCourseDetails(this.id).then(res => {
        this.detail = res
        this.courseUrl = res.coverAttachMap.coverAttachId.path
        // 视频
        if (res.videoAttachMap && Object.keys(res.videoAttachMap).length > 0) {
          this.source.src = res.videoAttachMap.videoAttachIds[0].path
        }
        // 音频
        if (res.audioAttachMap && Object.keys(res.audioAttachMap).length > 0) {
          this.audioUrl = new Audio(res.audioAttachMap.audioAttachIds[0].path)
        }
        // 相关附件
        if (
          res.relatedAttachMap &&
          Object.keys(res.relatedAttachMap).length > 0
        ) {
          this.hasAttach = res.relatedAttachMap.relatedAttachIds
        }

        // 报名关注
        if (res.wxAttachMap && Object.keys(res.wxAttachMap).length > 0) {
          this.attention = res.wxAttachMap.wxAttachId.path
        }
      })
    },
    async getCourseDetailColum() {
      const res = await getCourseDetailColum(this.id)
      this.detail = res
      this.courseUrl = res.coverAttachMap.coverAttachId[0].path
    },
    // 企业推荐课程
    async getMyCourseRecommended() {
      const res = await getMyCourseRecommended()
      this.courseList = res
    }
  },
  created() {
    this.id = this.$route.query.id
    if (this.$route.query.isColumn === 'true') {
      this.isColumn = true
      this.getCourseDetailColum()
      this.getMyColumnShare()
      this.tabsChange(this.current)
    } else {
      this.isColumn = false
      this.getCourseDetails()
      this.getMyCourseShare()
    }
    this.getMyCourseRecommended()
  }
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}

:deep(.card-wrapper) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .card-list {
    width: 50%;
    margin-bottom: 32px;
    &:nth-child(odd) {
      padding-right: 14px;
    }
    &:nth-child(even) {
      padding-left: 14px;
    }
  }
}

.offlineCourse {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}
.qr-code {
  width: 200px;
  height: 200px;
  margin: 0 auto;
}
.offline-course {
  margin-top: 8px;
  background-color: #f7f9fb;
  padding: 8px;
}
.course-title-tag {
  display: inline-block;
  height: 24px;
  font-size: 12px;
  color: #00a870;
  margin-top: 4px;
  padding: 0 8px;
  line-height: 24px;
  background: #e8f8f2;
  border-radius: 3px;
}
.title-btn {
  position: absolute;
  right: 12px;
  top: 25px;
}
.color-s {
  color: rgba(0, 0, 0, 0.6);
}
.info-list {
  .list {
    margin-right: 40px;
  }
}
.wrapper {
  display: flex;
  .content {
    flex: 1;
    overflow: hidden;
    white-space: pre-wrap;
    word-break: break-all;
    padding-right: 24px;
    .label {
      :deep(.el-tag) {
        margin-right: 8px;
      }
    }
  }
  .files {
    flex: 0 0 242px;
    border-left-width: 1px;
    border-style: solid;
    min-height: 100%;
    @include border_color(--border-color-light);
    .empty-content {
      padding-top: 20px;
    }
    .recommend {
      position: relative;
      padding: 8px;
      border-radius: 6px 6px 6px 6px;
      opacity: 1;
      border: 1px solid #e8f8f2;
      .course-info {
        position: absolute;
        left: 5px;
        top: 16px;
        width: 40px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        font-size: 12px;
        color: #e7e7e7;
        @include background_color(--color-primary);
        border-radius: 0 3px 3px 0;
        opacity: 1;
      }
      .course-litter {
        position: absolute;
        left: 5px;
        top: 44px;
        border-top: 2px solid #0038b3;
        border-bottom: 2px solid transparent;
        border-left: 2px solid transparent;
        border-right: 2px solid #0038b3;
      }
    }
  }
}

::v-deep {
  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
  }
  .el-scrollbar__bar.is-vertical > div {
    width: 4px;
    background: rgba(0, 0, 0, 0.26);
    border-radius: 2px;
    opacity: 1;
  }
  .el-scrollbar__bar {
    right: -2px;
  }
}
</style>
