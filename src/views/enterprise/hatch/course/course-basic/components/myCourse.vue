<template>
  <div class="card" @click="toDetail">
    <div class="card-wrapper">
      <div class="banner">
        <el-image class="w100 h100" :src="list.titleUrl"></el-image>
      </div>
      <div class="content">
        <h3 class="line-1 font-size-16 line-height-24 m-b-8">
          {{ list.title }}
        </h3>
        <div class="tag-button" style="margin-bottom: 40px">
          <tag-button
            v-if="courseLabel"
            type="success"
            :tag="courseLabel"
            hideButton
          />
        </div>
        <div class="icon-list flex justify-content-between align-items-center">
          <div class="flex">
            <icon-info
              icon-type="primary"
              icon-class="browse"
              :text="`${list.numberViews}`"
            />
            <icon-info
              v-if="list.approach === 1"
              class="m-l-8"
              icon-type="warning"
              icon-class="user"
              :text="`${list.personStr}`"
            />
          </div>
          <el-popover
            popper-class="link-view-popover"
            @show="handleHover"
            placement="top"
            trigger="hover"
          >
            <div v-loading="loading">
              <el-image
                style="width: 120px; height: 120px"
                :src="
                  codeUrl ? codeUrl : require('@/assets/images/no-data.png')
                "
                fit="cover"
              ></el-image>
            </div>
            <div slot="reference">
              <svg-icon icon-class="qrcode" />
            </div>
          </el-popover>
        </div>
      </div>
    </div>
    <div class="status">
      <angle-status type="primary" :text="list.courseModeStr" />
    </div>
  </div>
</template>

<script>
import IconInfo from '@/components/IconInfo'
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'
import {
  getMyColumnShare,
  getMyCourseShare
} from '@/views/enterprise/hatch/course/course-basic/api'

export default {
  name: 'MyCourse',
  components: {
    IconInfo,
    AngleStatus,
    TagButton
  },
  props: {
    list: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      codeUrl: ''
    }
  },
  computed: {
    courseLabel() {
      return this.list.labels ? this.list.labels.join('/') : ''
    },
    coursePic() {
      const attachMap = this.list.attachMap
      const coursePic =
        attachMap && attachMap.coursePic && attachMap.coursePic[0]
      return coursePic ? coursePic.path : null
    }
  },
  methods: {
    async handleHover() {
      this.loading = true
      if (this.list.isColumn) {
        const res = await getMyColumnShare(this.list.id)
        this.codeUrl = res.code
      } else {
        const res = await getMyCourseShare(this.list.id)
        this.codeUrl = res.code
      }
      this.loading = false
    },
    toDetail() {
      this.$router.push({
        path: '/hatch/interact/course/detail',
        query: {
          id: this.list.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  padding-left: 4px;
  margin-right: 16px;
  position: relative;
  cursor: pointer;
  .card-wrapper {
    padding: 8px 16px 8px 8px;
    display: flex;
    align-items: center;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    border-radius: 6px;
    .banner {
      flex: 0 0 248px;
      height: 148px;
      border-radius: 3px;
      overflow: hidden;
    }
    .content {
      flex: 1;
      overflow: hidden;
      padding-left: 16px;
    }
  }
  .status {
    position: absolute;
    left: 0;
    top: 16px;
  }
}
</style>
<style lang="scss">
.link-view-popover {
  .el-loading-spinner {
    top: 50px !important;
    z-index: 99999999;
  }
}
</style>
