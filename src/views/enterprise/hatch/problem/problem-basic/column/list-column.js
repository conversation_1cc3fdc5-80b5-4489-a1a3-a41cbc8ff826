import { getProblemStatus } from '../utils/status'
// getProblemTypeLabel,
export default {
  data() {
    return {
      tableColumn: [
        {
          label: '单号',
          prop: 'number'
        },
        {
          label: '所属园区',
          prop: 'parkName'
        },
        {
          label: '反馈类型',
          prop: 'type'
        },
        {
          label: '反馈内容',
          prop: 'description',
          showOverflowTooltip: true
        },
        {
          label: '反馈时间',
          prop: 'createTime'
          // sortable: true,
        },
        {
          label: '提醒',
          prop: 'unReadCount', // 有新回复
          render: (h, scope) => {
            return (
              <el-link
                underline={false}
                type={scope.row.unReadCount ? 'primary' : ''}
              >
                {scope.row.unReadCount ? '有新回复' : '暂无回复'}
              </el-link>
            )
          }
        },
        {
          label: '状态',
          prop: 'status',
          render: (h, scope) => {
            return <div>{getProblemStatus(h, scope.row.status)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onclick={() => {
                    this.goDetail(scope.row)
                  }}
                  class="m-r-15"
                  v-permission={this.routeButtonsPermission.VIEW}
                >
                  {this.routeButtonsTitle.FEEDBACK}
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
