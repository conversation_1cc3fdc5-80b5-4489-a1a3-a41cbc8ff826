<template>
  <div class="field-record">
    <module-header
      type="warning"
      title="预定记录"
      desc="一键申请，快速响应。为企业纾困解难、降本增效"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <!-- 内容区域 -->
      <basic-tab
        ref="basicTab"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
      />
      <div class="module-list">
        <module-list
          ref="ModuleList"
          :extra-query="extraQuery"
          :api-fn="getFieldRecords"
        >
          <!--           <template v-slot:right>-->
          <!--            <div class="flex align-items-center pointer">-->
          <!--              <svg-icon class="m-r-4" icon-class="icon-color-time" />-->
          <!--              <span class="font-size-14 line-height-22">预定记录</span>-->
          <!--            </div>-->
          <!--          </template>-->
          <template slot-scope="scope">
            <div class="card-wrapper">
              <div class="card-list" v-for="data in scope.data" :key="data.id">
                <record-card :list="data" @viewOrderDetail="viewOrderDetail" />
              </div>
            </div>
          </template>
        </module-list>
      </div>
    </div>

    <!-- 详情弹窗 -->
    <dialog-cmp
      title="预定详情"
      :visible.sync="visible"
      width="720px"
      :havaConfirm="false"
    >
      <record-detail v-if="visible" :detail="orderDetail" />
    </dialog-cmp>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ModuleList from './components/ModuleList'
import RecordCard from './components/Record'
import RecordDetail from './components/RecordDetail'
import BasicTab from './components/BasicTab'
import { getFieldRecords, getCount } from './api'

export default {
  name: 'FieldRecord',
  components: {
    ModuleHeader,
    ModuleList,
    BasicTab,
    RecordCard,
    RecordDetail
  },
  data() {
    return {
      getFieldRecords,
      visible: false,
      extraQuery: {
        result: ''
      },
      orderDetail: null,
      current: 0,
      list: [
        {
          label: '全部',
          value: 0,
          quantity: 0
        },
        {
          label: '待审核',
          value: 1,
          quantity: 0
        },
        {
          label: '已通过',
          value: 3,
          quantity: 0
        },
        {
          label: '已拒绝',
          value: 4,
          quantity: 0
        },
        {
          label: '已取消',
          value: 5,
          quantity: 0
        },
        {
          label: '已过期',
          value: 7,
          quantity: 0
        }
      ]
    }
  },
  mounted() {
    this.getCount()
  },
  methods: {
    getCount() {
      getCount().then(res => {
        this.list[0].quantity = res.total
        this.list[1].quantity = res.waitExamine
        this.list[2].quantity = res.pass
        this.list[3].quantity = res.reject
        this.list[4].quantity = res.withdraw
        this.list[5].quantity = res.expired
      })
    },
    refresh() {
      this.$refs.ModuleList.refresh()
    },
    tabsChange(val) {
      this.current = val
      this.extraQuery.result = val || ''
      this.$refs.ModuleList.triggerSearch()
    },
    viewOrderDetail(detail) {
      this.visible = true
      this.orderDetail = detail
    }
  }
}
</script>

<style lang="scss" scoped>
.module-list {
  margin-top: 16px;
  .svg-icon {
    width: 20px;
    height: 20px;
  }
}

:deep(.card-wrapper) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .card-list {
    width: 33.33333%;
    margin-bottom: 32px;
    padding-right: 14px;
    &:nth-child(3n) {
      padding-left: 0;
    }
  }
}
</style>
