import TimeSelect from '@/views/manage/hatch/field/field-basic/fieldManage/time-select.vue'
import dayjs from 'dayjs'

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '80px',
        descriptors: {
          applyDate: {
            form: 'date',
            label: '预定日期',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请选择预定日期'
              }
            ],
            customTips: () => {
              return (
                <div class="line-height-20 font-size-14">
                  {this.showOrderedTime && <span>{this.showOrderedTime}</span>}
                </div>
              )
            },
            props: {
              pickerOptions: {
                disabledDate: date => {
                  let day = dayjs(date).day()
                  day === 0 && (day = 7)
                  let timestamp = new Date(date).getTime()
                  if (
                    timestamp + 1 * 1000 * 60 * 60 * 24 <= Date.now() ||
                    timestamp >= Date.now() + 29 * 1000 * 60 * 60 * 24
                  ) {
                    return true
                  }
                  let bookableDate = this.detail.bookableDate
                  if (bookableDate) {
                    bookableDate = JSON.parse(bookableDate)
                  }
                  return !bookableDate.includes(day)
                }
              }
            }
          },
          applyTime: {
            form: 'component',
            label: '预定时段',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择可预约时段'
              }
            ],
            render: (h, scope) => {
              const { bookableStartTime, bookableEndTime } = this.detail
              return (
                <TimeSelect
                  minTime={bookableStartTime}
                  maxTime={bookableEndTime}
                  value={scope._value}
                  onInput={value => {
                    scope._value = value
                  }}
                />
              )
            }
          },
          totalTime: {
            form: 'input',
            label: '预定时长',
            render: () => {
              return (
                <span class="font-size-14 line-height-22 color-primary">
                  {this.orderDuration}
                  {this.orderDurationUnit}
                </span>
              )
            }
          },
          predictPrice: {
            form: 'input',
            label: '预估费用',
            render: () => {
              return (
                <span class="font-size-14 line-height-22 color-primary">
                  {this.orderPrice}
                </span>
              )
            }
          },
          peopleNum: {
            form: 'input',
            label: '参会人数',
            rule: [
              {
                required: true,
                type: 'number',
                message: '请填写参会人数'
              }
            ],
            customRight: () => {
              return <div class="line-height-32 font-size-14">人</div>
            }
          },
          contact: {
            form: 'input',
            label: '联系人',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请填写联系人'
              }
            ]
          },
          phone: {
            form: 'input',
            label: '联系方式',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入联系方式',
                validator: 'validatePhone'
              }
            ]
          },
          reason: {
            form: 'input',
            label: '预定缘由',
            attrs: {
              type: 'textarea',
              rows: 8,
              maxlength: 300,
              showWordLimit: true,
              autosize: { minRows: 4, maxRows: 8 }
            },
            rule: [
              {
                required: true,
                type: 'string',
                message: '请填写预定缘由'
              }
            ]
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array',
                message: '请上传相关附件'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div class="line-height-20 font-size-12">
                  <span>
                    请上传格式为png/jpg/pdf/excel/word不大于10MB的附件，附件不得超过三个
                  </span>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'fieldOrder'
              },
              mulity: true,
              maxSize: 10,
              limit: 3
            }
          }
        }
      }
    }
  }
}
