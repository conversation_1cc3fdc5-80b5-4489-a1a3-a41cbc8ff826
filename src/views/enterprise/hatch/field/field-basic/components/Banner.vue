<template>
  <div>
    <el-carousel height="386px" :interval="6000">
      <el-carousel-item v-for="(banner, index) in banners" :key="banner.id">
        <el-image
          class="wh100"
          :src="banner.path"
          fit="cover"
          @click.stop="priviewFile(banners, index)"
        ></el-image>
      </el-carousel-item>
    </el-carousel>
    <img-viewer ref="viewer" />
  </div>
</template>

<script>
import ImgViewer from '@/components/ImgViewer'

export default {
  name: 'FieldBanner',
  components: { ImgViewer },
  props: {
    banners: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    priviewFile(files, index) {
      const list = files.map(file => file.path)
      console.log(list)
      this.$refs.viewer.show(list, index)
    }
  }
}
</script>

<style lang="scss" scoped></style>
