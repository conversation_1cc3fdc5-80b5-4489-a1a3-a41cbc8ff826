<template>
  <div class="card" @click="toDetail">
    <div class="card-wrapper">
      <div class="banner">
        <el-image class="w100 h100" :src="attachPath" fit="cover" />
        <!-- <img class="w100 h100" src="../images/example.png" alt="" /> -->
      </div>
      <div class="content">
        <h3 class="line-1 font-size-16 line-height-24 m-b-8">
          {{ list.name }}
        </h3>
        <div class="icon-list m-b-8">
          <icon-info
            icon-type="warning"
            icon-class="location"
            :text="list.address"
          />
        </div>
        <div class="icon-list m-b-8">
          <icon-info
            icon-type="primary"
            icon-class="calendar"
            :text="list.bookableDays"
          />
        </div>
        <div class="tag-button">
          <tag-button
            type="warning"
            :tag="list.allocation"
            buttonName="去预定"
            @tapButton="toDetail"
          />
        </div>
      </div>
    </div>
    <div class="status">
      <angle-status type="primary" :text="list.number + '人'" />
    </div>
  </div>
</template>

<script>
import IconInfo from '@/components/IconInfo'
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'

export default {
  name: 'FieldCard',
  components: {
    IconInfo,
    AngleStatus,
    TagButton
  },
  props: {
    list: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    disableSign() {
      return !this.list
    },
    attachPath() {
      return this.list.attachMap.field ? this.list.attachMap.field[0].path : ''
    }
  },
  methods: {
    toDetail() {
      this.$router.push({
        path: '/hatch/interact/field/detail',
        query: {
          id: this.list.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  padding-left: 4px;
  position: relative;
  cursor: pointer;
  .card-wrapper {
    padding: 8px 16px 8px 8px;
    display: flex;
    align-items: center;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    border-radius: 6px;
    .banner {
      flex: 0 0 248px;
      height: 148px;
      border-radius: 3px;
      overflow: hidden;
    }
    .content {
      flex: 1;
      overflow: hidden;
      padding-left: 16px;
    }
  }
  .status {
    position: absolute;
    left: 0;
    top: 16px;
  }
}
</style>
