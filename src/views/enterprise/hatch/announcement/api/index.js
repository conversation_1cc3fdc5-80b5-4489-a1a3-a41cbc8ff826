import request from '@/utils/request'

// 获取园区公告列表
export function getNoticeList(params) {
  return request({
    url: `/hatch/ent/notice/page`,
    method: 'get',
    params: {
      ...params,
      status: true,
      targetAudience: 0
    }
  })
}
// 获取所有公告类型
export function getNoticeTypeAll() {
  return request({
    url: `/hatch/notice_type/list_all`,
    method: 'get'
  })
}
// 获取园区公告详情
export function getDetail(id) {
  return request({
    url: `/hatch/notice/get?id=${id}`,
    method: 'get'
  })
}
// 公告分享接口
export function getShareInfo(id) {
  return request({
    url: `/hatch/place-info/get_share_info/${id}`,
    method: 'get'
  })
}
// 获得场地分享信息
export function getNoticeShareInfo(id) {
  return request({
    url: `/hatch/notice/get_share_info/${id}`,
    method: 'get'
  })
}
// 获得消息列表
export function getApartmentPage(params) {
  return request({
    url: `/housing/apartment/notice/info/page`,
    method: 'get',
    params
  })
}
// 获得消息类型
export function getApartmentType(params) {
  return request({
    url: `/housing/apartment/notice/type/list_all`,
    method: 'get',
    params
  })
}
// 获得消息详情
export function getApartmentInfoDetail(params) {
  return request({
    url: `/housing/apartment/notice/info/get`,
    method: 'get',
    params
  })
}
