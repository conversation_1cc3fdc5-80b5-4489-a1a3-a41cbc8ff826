<template>
  <div>
    <module-header
      type="primary"
      title="新增申请"
      :img="require('./images/img.png')"
      :imgOpacity="0.1"
    >
      <div class="steps">
        <div class="p-t-32" v-if="active === 1">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
            <el-breadcrumb-item :to="{ path: '/finance/account/purse' }"
              >财务管理</el-breadcrumb-item
            >
            <el-breadcrumb-item>
              <span style="color: #000">开票申请</span>
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div
          v-if="active === 0"
          class="m-b-12 font-size-20 font-strong color-text-primary line-height-28 p-t-38"
        >
          开票申请
          <span class="font-size-14 m-l-16" style="color: rgba(0, 0, 0, 0.6)"
            >面向园区提交开票申请信息</span
          >
        </div>
        <div class="flex flex-center-center">
          <steps :active="active" />
        </div>
      </div>
    </module-header>
    <div>
      <div class="lateral-wrapper">
        <div class="cardBorder">
          <div v-if="active === 0">
            <div class="top">
              <span>开票类型</span>
            </div>
            <!--        开票类型选择-->
            <div
              class="select flex p-b-30"
              style="border-bottom: 1px solid #ffe8eefc"
            >
              <div
                class="left"
                style="width: 30%; margin-right: 60px"
                :class="[electronChecked ? 'electronTrue' : '']"
              >
                <div class="flex justify-content-between m-b-16">
                  <div style="color: rgba(57, 57, 57, 1); font-width: 350">
                    增值税普通发票（电子）
                  </div>
                  <div>
                    <el-checkbox
                      v-model="electronChecked"
                      :checked="electronChecked"
                      @click="electronClick(1)"
                      @change="changeTwo($event, 1)"
                    ></el-checkbox>
                  </div>
                </div>
                <div>
                  <span
                    class="font-size-14"
                    style="color: rgba(102, 102, 102, 1)"
                    >申请后生成电子发票，企业自行下载发票</span
                  >
                </div>
              </div>
              <div
                class="left"
                style="width: 30%"
                :class="[paperChecked ? 'electronTrue' : '']"
              >
                <div class="flex justify-content-between m-b-16">
                  <div style="color: rgba(57, 57, 57, 1); font-width: 350">
                    增值税专用发票（纸质）
                  </div>
                  <div>
                    <el-checkbox
                      v-model="paperChecked"
                      :checked="paperChecked"
                      @click="electronClick(2)"
                      @change="change($event, 2)"
                    ></el-checkbox>
                  </div>
                </div>
                <div>
                  <span
                    class="font-size-14"
                    style="color: rgba(102, 102, 102, 1)"
                    >财务人员开具发票后，企业自行前往领取</span
                  >
                </div>
              </div>
            </div>
            <!--        开票类型内容-->
            <div class="m-t-32">
              <div class="flex">
                <div class="m-r-24 font-size-14 blackColor">
                  有{{ ticketCount || '0' }}个订单可申请发票
                </div>
                <div class="font-size-14 blackColor">
                  可开票总金额
                  <span style="color: #ed7b2f"
                    >¥：{{ NumFormat(ticketMoney) || '0' }}</span
                  >
                </div>
              </div>
            </div>
            <div class="m-t-24">
              <drive-table
                ref="driveTable"
                height="calc(100vh - 335px)"
                :columns="tableColumnList"
                :table-data="tableList"
                @selection-change="selectionChange"
              />
            </div>
          </div>
          <div v-if="active === 1">
            <div style="border-bottom: 1px solid #e9f0ff">
              <div class="flex justify-content-between m-b-22">
                <div style="line-height: 30px">
                  <span style="font-weight: 350">发票信息</span>
                </div>
                <div>
                  <el-button type="text" @click="openRaiseClick"
                    >选择发票信息 <i class="el-icon-arrow-right"></i
                  ></el-button>
                </div>
              </div>
              <div class="flex">
                <div class="p-l-10" style="line-height: 28px">
                  <span class="font-size-14">总价</span>
                  <span class="font-size-14" style="color: #ed7b2f"
                    >¥：{{ NumFormat(entData.ticketFee) }}</span
                  >
                </div>
                <div class="m-l-28">
                  <!--                  <el-button type="text">发票预览</el-button>-->
                </div>
              </div>
              <div
                style="margin-top: 50px; padding: 0 50px; margin-bottom: 32px"
              >
                <div class="flex">
                  <div style="width: 50%">
                    <div class="m-b-20 flex">
                      <div class="textLeft">发票抬头</div>
                      <div class="font-size-14">
                        {{ entData.headName || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">注册地址</div>
                      <div class="font-size-14">
                        {{ entData.headRegAddress || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">开户银行</div>
                      <div class="font-size-14">
                        {{ entData.headBankName || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">开票内容</div>
                      <div class="font-size-14">
                        {{ entData.ticketContent || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">数量</div>
                      <div class="font-size-14">
                        {{ entData.billCount || '' }}
                      </div>
                    </div>
                  </div>
                  <div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">公司税号</div>
                      <div class="font-size-14">
                        {{ entData.headCreditCode || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">电话号码</div>
                      <div class="font-size-14">
                        {{ entData.headPhone || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">开户账号</div>
                      <div class="font-size-14">
                        {{ entData.headBankAccount || '' }}
                      </div>
                    </div>
                    <div class="m-b-20 flex">
                      <div class="textLeft">税率</div>
                      <div class="font-size-14">
                        {{ entData.taxRate || '' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="flex m-t-36">
              <div style="width: 4%">
                <span style="font-size: 14px; color: rgba(0, 0, 0, 0.4)">
                  备注
                </span>
              </div>
              <div style="width: 96%">
                <el-input
                  type="textarea"
                  maxlength="350"
                  :rows="10"
                  placeholder="请简要说明备注内容"
                  v-model="textarea"
                  show-word-limit="true"
                >
                </el-input>
                <div class="font-size-14 color-danger m-t-10">
                  建议不要超过40-50(数字+汉字)，否则盖章的时候有可能压到。
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="lateral-wrapper">
        <div class="bottomBorder flex justify-content-between">
          <div class="flex" v-if="active === 0">
            <div class="m-r-24" style="line-height: 30px">
              <span class="blackColor font-size-14"
                >已选{{ countNumber || '0' }}个账单共
                <span style="color: #ed7b2f"
                  >¥:{{ NumFormat(selectTotalMoney) || '0' }}</span
                >
              </span>
            </div>
            <div style="line-height: 30px">
              <span class="blackColor font-size-14"
                >剩余待开票金额
                <span style="color: #ed7b2f"
                  >¥:{{ NumFormat(waitCheckMoney) || '0' }}</span
                >
              </span>
            </div>
          </div>
          <div></div>
          <div>
            <div>
              <span v-if="disabled" class="font-size-14 m-r-10"
                >目前没有任何开票抬头，<el-button
                  type="text"
                  class="font-size-14"
                  @click="addRaise"
                  >请点击添加</el-button
                ></span
              >
              <el-button
                type="primary"
                :disabled="disabled"
                @click="nextSetp"
                v-if="active === 0"
                >下一步</el-button
              >
            </div>
            <div v-if="active === 1">
              <el-button type="info" @click="previousSetp" v-if="reloadFlag"
                >上一步</el-button
              >
              <el-button type="primary" @click="submitSetp">提交</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--    抬头管理弹框-->
    <dialog-cmp
      title="抬头管理"
      :visible.sync="openRaise"
      width="880px"
      :haveOperation="false"
      :isShowBtns="false"
    >
      <template v-slot:title>
        <div>
          <span>抬头管理</span>
        </div>
      </template>
      <div v-if="openRaise">
        <drive-table
          ref="drive-table"
          :columns="tableColumnBottomRaise"
          :table-data="tableListRaise"
        >
        </drive-table>
      </div>
    </dialog-cmp>
    <!--    抬头申请管理弹框-->
    <dialog-cmp
      title="抬头管理"
      :visible.sync="openRaiseDialog"
      width="880px"
      :haveOperation="false"
      :isShowBtns="false"
    >
      <template v-slot:title>
        <div class="flex justify-content-between">
          <div>
            <span>抬头管理</span>
          </div>
          <div class="m-r-20">
            <el-button size="small" type="primary" @click="headCreate"
              ><i class="el-icon-plus" style="margin-right: 8px"></i
              >{{ formRaiseApply.id ? '修改' : '新增' }}</el-button
            >
          </div>
        </div>
      </template>
      <div v-if="openRaiseDialog">
        <driven-form
          ref="driven-raise-form"
          v-model="formRaiseApply"
          :formConfigure="formConfigure"
        />
        <drive-table
          ref="drive-table-raise"
          :columns="tableColumnBottom"
          :table-data="tableRaiseList"
        >
        </drive-table>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import ColumnMixins from '../column'
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import descriptorMixins from '../descriptor'
import Steps from './steps'
import {
  ticketApplyEntFindBillList,
  ticketApplyEntCountBillList,
  ticketApplyCreateBillList,
  ticketHeadList,
  ticketApplyCreateBillTwo,
  ticketDetails,
  ticketApplyCreateBillUpdate,
  headSetUpdate,
  ticketHeadCreate,
  headSetDelete,
  headSetDefault
} from '../api/index'
export default {
  components: { ModuleHeader, Steps },
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      NumFormat,
      tableListRaise: [],
      tableRaiseList: [],
      disabled: false,
      openRaiseDialog: false,
      tableList: [],
      formRaiseApply: {},
      active: 0,
      electronChecked: false,
      paperChecked: false,
      textarea: '',
      openRaise: false,
      formRaise: {},
      ticketCount: 0,
      ticketMoney: 0,
      countNumber: 0,
      selectTotalMoney: 0,
      waitCheckMoney: 0,
      ticketType: null,
      billIds: '',
      entData: {},
      reloadFlag: false
    }
  },
  mounted() {
    this.ticketApplyEntFindBillList()
    this.ticketHeadList()
    const { id, reload } = this.$route.query
    if (reload && id) {
      this.$nextTick(() => {
        this.active = 1
        this.reloadFlag = false
        this.reloadChange(id)
      })
    } else {
      this.reloadFlag = true
    }
  },
  methods: {
    previewDefault(e) {
      headSetDefault({ id: e.id }).then(res => {
        if (res) {
          this.ticketHeadList()
        }
      })
    },
    previewCheng(e) {
      this.formRaiseApply = {
        ...e
      }
    },
    previewDelete(e) {
      this.$confirm('确定删除此条发票？').then(() => {
        headSetDelete({ id: e.id }).then(() => {
          //清空表单并且修改改为新增
          this.formRaiseApply = {}

          this.$toast.success('删除成功')
          this.ticketHeadList()
        })
      })
    },
    addRaise() {
      this.formRaiseApply = {}
      this.openRaiseDialog = true
    },
    //  抬头新增
    headCreate() {
      this.$refs['driven-raise-form'].validate(valid => {
        if (valid) {
          let data = this.formRaiseApply
          this.openRaiseDialog = true
          if (data.id) {
            headSetUpdate(data).then(res => {
              console.log(res)
              this.$toast.success('修改成功')
              this.ticketHeadList()
              this.$refs['driven-raise-form'].resetFields()
              // this.formRaiseApply = {}
              this.$nextTick(() => {
                this.$refs['driven-raise-form'].clearValidate()
              })
            })
          } else {
            ticketHeadCreate(data).then(res => {
              console.log(res)
              this.$toast.success('新增成功')
              this.ticketHeadList()
              this.$refs['driven-raise-form'].resetFields()
              //去除校验

              // this.formRaiseApply = {}
              this.$nextTick(() => {
                this.$refs['drive-table-raise'].clearValidate()
              })
            })
          }
          this.openRaiseDialog = false
        }
      })
    },
    reloadChange(e) {
      console.log(e)
      ticketDetails({ id: e }).then(res => {
        this.entData = res
        this.textarea = res.applyComment
      })
    },
    change(e, h) {
      this.ticketType = h
      if (e) {
        this.electronChecked = false
      }
    },
    changeTwo(e, h) {
      this.ticketType = h
      if (e) {
        this.paperChecked = false
      }
    },
    previewClick(e) {
      console.log(e)
      this.entData.headName = e.headName
      this.entData.headCreditCode = e.creditCode
      this.entData.headRegAddress = e.regAddress
      this.entData.headBankName = e.bankName
      this.entData.headPhone = e.phone
      this.entData.headBankAccount = e.bankAccount
      this.openRaise = false
    },
    submitSetp() {
      if (this.reloadFlag) {
        let data = {
          billIds: this.billIds,
          ticketType: this.ticketType,
          ...this.entData,
          applyComment: this.textarea
        }
        ticketApplyCreateBillTwo(data).then(res => {
          console.log(res)
          if (res) {
            this.$toast.success('提交成功')
            this.$router.go(-1)
          }
        })
      } else {
        let data = {
          ...this.entData,
          applyComment: this.textarea
        }
        ticketApplyCreateBillUpdate(data).then(res => {
          console.log(res)
          if (res) {
            this.$toast.success('修改成功')
            this.$router.go(-1)
          }
        })
      }
    },
    ticketHeadList() {
      ticketHeadList().then(res => {
        this.tableListRaise = res
        this.tableRaiseList = res
        this.disabled = this.tableListRaise.length <= 0
      })
    },
    ticketApplyEntFindBillList() {
      ticketApplyEntFindBillList().then(res => {
        this.tableList = res.billDetailRespVOList
        if (this.tableList.length > 0) {
          this.waitCheckMoney = this.tableList.reduce((total, item) => {
            return total + item.payActualAmount
          }, 0)
        }
        console.log('sss', this.waitCheckMoney)
        this.ticketCount = res.ticketCount
        this.ticketMoney = res.ticketMoney
      })
    },
    openRaiseClick() {
      this.openRaise = true
    },
    selectionChange(e) {
      let dataList = e.map(item => {
        return item.id
      })
      let dataString = dataList.toString()
      this.billIds = dataString
      ticketApplyEntCountBillList({ billIds: dataString }).then(res => {
        console.log(res)
        this.waitCheckMoney = res.waitCheckMoney
        this.selectTotalMoney = res.selectTotalMoney
        this.countNumber = res.count
      })
    },
    electronClick(e) {
      console.log(e)
      this.ticketType = e
      if (e === 1) {
        console.log(e)
        if (this.electronChecked) {
          this.electronChecked = false
        } else {
          this.electronChecked = true
          this.paperChecked = false
        }
      } else {
        console.log(e)
        if (this.paperChecked) {
          this.paperChecked = false
        } else {
          this.electronChecked = false
          this.paperChecked = true
        }
      }
    },
    nextSetp() {
      if (
        (this.electronChecked || this.paperChecked) &&
        this.billIds.length > 0
      ) {
        let data = {
          billIds: this.billIds,
          ticketType: this.ticketType
        }
        ticketApplyCreateBillList(data).then(res => {
          this.entData = res
          this.textarea = res.applyComment
          this.$nextTick(() => {
            this.active = 1
          })
        })
      } else {
        this.$toast.success('请选择开票类型和申请发票')
      }
    },
    previousSetp() {
      this.$nextTick(() => {
        this.active = 0
      })
    }
  }
}
</script>

<style lang="scss" scoped>
//::v-deep .el-checkbox__inner{
//  border-radius: 100% !important;
//}
.cardBorder {
  border: 1px solid #ffe8eefc;
  border-bottom: none;
  margin-top: 20px;
  border-radius: 10px 10px 0 0;
  padding: 32px 32px 24px 32px;
  .top {
    margin-bottom: 24px;
    .span {
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;
      font-weight: 350;
      line-height: 24px;
    }
  }
  .select {
    .left {
      border: 1px solid rgba(233, 240, 255, 1);
      padding: 27px 24px;
    }
  }
}

.blackColor {
  color: rgba(0, 0, 0, 0.9);
  font-weight: 350;
}
.electronTrue {
  border: 1px solid #ed7b2f !important;
}
.bottomBorder {
  border: 1px solid #ffe8eefc;
  border-radius: 0 0 10px 10px;
  padding: 21px 32px 24px 32px;
}
.textLeft {
  width: 80px;
  text-align: right;
  margin-right: 16px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
}
</style>
