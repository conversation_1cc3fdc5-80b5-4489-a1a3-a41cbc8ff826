<template>
  <div>
    <header-cmp
      :img="require('../../../account/account-basic/images/header-bg.png')"
      title="开票历史"
    >
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          查看企业全部已出缴费通知单
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content bg-white">
          <div class="flex account-left bg-white justify-content-around">
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span>已开总数(张)</span>
                  <span class="m-l-16"> </span>
                </div>
                <div
                  class="acconutname m-t-17 font-size-24 flex align-items-center"
                >
                  <span>{{ totalApply | noData }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span class="line"></span>
                  <span>合计金额(元)</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(totalFee) | noData }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span class="line"></span>
                  <span>合计税额(元)</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(totalTax) | noData }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh" style="width: 80%">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span class="line"></span>
                  <span>已开票进度</span>
                </div>
                <div class="w100 m-t-18">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="20"
                    :percentage="rate"
                    status="success"
                  ></el-progress>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>
    <div>
      <!--    表单-->
      <div class="lateral-wrapper">
        <div class="m-t-24">
          <el-form ref="fromAccountInfo" :model="fromTableInfo">
            <div class="w100">
              <el-row>
                <el-col :span="9">
                  <el-form-item class="m-l-8">
                    <el-input
                      clearable
                      @clear="reset"
                      @input="entNameForm"
                      style="width: 288px"
                      placeholder="请输入企业名称"
                      v-model="fromTableInfo.entName"
                    >
                      <i
                        slot="prefix"
                        class="el-input__icon el-icon-search"
                      ></i>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="3" :offset="6">
                  <el-form-item class="m-l-8">
                    <el-select
                      clearable
                      @change="changeTicketType"
                      style="width: 140px"
                      v-model="fromTableInfo.ticketType"
                      placeholder="请选择票据类型"
                      :popper-append-to-body="false"
                    >
                      <el-option
                        v-for="item in ticketTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item class="m-l-8">
                    <el-select
                      clearable
                      @change="changeFeeType"
                      style="width: 140px"
                      v-model="fromTableInfo.feeTypeList"
                      placeholder="请选择发票类目"
                      :popper-append-to-body="false"
                    >
                      <el-option
                        v-for="item in feeTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item class="m-l-8">
                    <el-select
                      clearable
                      @change="changeTimeType"
                      style="width: 140px"
                      v-model="fromTableInfo.checkTimeType"
                      placeholder="请选择开票时间"
                      :popper-append-to-body="false"
                    >
                      <el-option
                        v-for="item in dayList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-form>
        </div>
        <div>
          <drive-table
            ref="drive-table"
            :columns="tableColumn"
            :api-fn="getPageByTicket"
            :extral-querys="extralQuerys"
            :searchQuerysHook="searchQuerysHook"
          >
          </drive-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import ColumnMixins from '../column/list-column'
import HeaderCmp from '../../../account/account-basic/components/headerComponent'
import { getCount, getPageByTicket } from './api'
export default {
  components: { HeaderCmp },
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      getPageByTicket,
      fromTableInfo: {
        ticketType: null,
        feeType: null,
        parkId: null,
        checkTimeType: null,
        entName: null
      },
      openRaise: false,
      formRaise: {},
      rate: 0, //    //  票据类型
      totalFee: null, //    //  票据类型
      totalTax: null, //    //  票据类型
      totalApply: null, //    //  票据类型
      extralQuerys: {
        ticketStatus: 3,
        ticketType: null,
        feeType: null,
        parkId: null,
        checkTimeType: null,
        entName: null
      },
      dayList: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '本周',
          value: 2
        },
        {
          label: '本月',
          value: 3
        },
        {
          label: '本季度',
          value: 4
        },
        {
          label: '本年',
          value: 5
        },
        {
          label: '近3年',
          value: 6
        },
        {
          label: '3年以前',
          value: 7
        }
      ],
      ticketTypeList: [
        {
          label: '普通电子发票',
          value: 1
        },
        {
          label: '专用发票',
          value: 2
        }
      ],
      feeTypeList: [
        {
          label: '房租费',
          value: 1
        },
        {
          label: '保证金',
          value: 2
        },
        {
          label: '服务费',
          value: 3
        }
      ]
    }
  },
  mounted() {
    this.getCount()
  },
  methods: {
    reset() {
      this.fromTableInfo.entName = null
      this.extralQuerys.entName = null
      this.$refs['drive-table'].refreshTable()
    },
    entNameForm() {
      console.log('ssss')
      this.extralQuerys.entName = this.fromTableInfo.entName
      this.updateData()
    },
    changeTicketType(e) {
      this.extralQuerys.ticketType = e
      this.updateData()
    },
    changeFeeType(e) {
      this.extralQuerys.feeType = e
      this.updateData()
    },
    changeTimeType(e) {
      this.extralQuerys.checkTimeType = e
      this.updateData()
    },
    updateData() {
      this.$refs['drive-table'].refreshTable()
    },
    searchQuerysHook(e) {
      const { signingDate } = e
      if (signingDate && signingDate.length) {
        const [beginSignDate, endSignDate] = e.signingDate
        delete e.signingDate
        return {
          ...e,
          beginSignDate,
          endSignDate
        }
      } else {
        return e
      }
    },
    getCount() {
      getCount(3).then(res => {
        this.totalApply = res.totalApply
        this.rate = res.rate
        this.totalFee = res.totalFee
        this.totalTax = res.totalTax
      })
    },
    goDetailHistory(row) {
      this.$router.push({
        path: '/finance/ticket/ticketDetail',
        query: {
          id: row.id
        }
      })
    },
    searchQueryHook(e) {
      console.log(e)
    }
  }
}
</script>

<style lang="scss" scoped>
.financial-content {
  border-radius: 3px 3px 3px 3px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 12px;
    left: 32px;

    .line {
      position: absolute;
      top: 12px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: 57px;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}
</style>
