// import { NumFormat } from '@/utils/tools'
import { statusType } from './utils/status'

export default {
  data() {
    return {
      recordTableColumn: [
        {
          prop: 'entName',
          label: '开票金额(含税)'
        },
        {
          prop: 'billCode',
          label: '发票类型'
        },
        {
          prop: 'ticketType',
          label: '申请时间'
        },
        {
          prop: 'feeType',
          label: '开票类型'
        },
        {
          prop: 'feeType',
          label: '开票依据'
        },
        {
          prop: 'ticketStatus',
          label: '开票状态',
          render: (h, scope) => {
            return <div>{statusType(h, scope.row.ticketStatus)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.previewEvent(scope.row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ],
      downloadTableColumn: [
        {
          prop: 'entName',
          label: '开票金额(含税)'
        },
        {
          prop: 'billCode',
          label: '发票类型'
        },
        {
          prop: 'ticketType',
          label: '开票时间'
        },
        {
          prop: 'feeType',
          label: '开票类型'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.download(scope.row)
                  }}
                >
                  发票下载
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
