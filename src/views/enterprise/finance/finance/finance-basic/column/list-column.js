import {
  getFeeTypeType,
  getTicketType
} from '@/views/manage/financial/financial-basic/payment/billHistory/billHistory-basic/list/status'
import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '企业名称',
          width: 220,
          prop: 'entName'
        },
        {
          width: 200,
          label: '账单编号',
          prop: 'billCode'
        },

        {
          label: '票据类型',
          prop: 'ticketType',
          render: (h, scope) => {
            return <div>{getTicketType(h, scope.row.ticketType)}</div>
          }
        },
        {
          label: '发票类目',
          prop: 'feeType',
          render: (h, scope) => {
            return <div>{getFeeTypeType(h, scope.row.feeType)}</div>
          }
        },
        {
          label: '合计金额(元)',
          prop: 'ticketPay',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.ticketPay)}
              </div>
            )
          }
        },
        {
          label: '合计税额(元)',
          prop: 'ticketTax',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.ticketTax)}
              </div>
            )
          }
        },
        {
          label: '开票时间',
          prop: 'checkTime'
        },
        {
          label: '操作',
          width: 150,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.goDetailHistory(scope.row)
                  }}
                >
                  详情
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
