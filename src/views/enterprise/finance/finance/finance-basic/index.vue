<template>
  <div>
    <header-cmp
      :img="require('./financeApply/images/img.png')"
      title="发票中心"
    >
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          申请和查看全部发票信息
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content bg-white">
          <div class="flex account-left bg-white justify-content-around">
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span>收款剩余可开票金额</span>
                  <span class="m-l-16"> </span>
                </div>
                <div
                  class="payname m-t-17 font-size-24 flex align-items-center"
                >
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(headDetail.totalFee) }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span class="line"></span>
                  <span>收款已开票金额</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(headDetail.totalFee) }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span class="line"></span>
                  <span>已预开票金额</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(headDetail.totalTax) | noData }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div
                  class="font-size-16 m-t-10 flex align-items-center pointer"
                >
                  <span class="line"></span>
                  <span>已获取发票数(张)</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span>{{ 32456 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>

    <div class="lateral-wrapper">
      <div class="m-t-16 position-relative">
        <basic-tab
          ref="basicTab"
          :tabs-data="balanceType"
          :current="current"
          @tabsChange="tabsChange"
        />
        <div class="right-info flex align-items-center color-info">
          <span>抬头信息：</span>
          <span class="m-l-8 m-r-8">还没有添加抬头信息</span>
          <el-link type="primary" @click="visible = true">
            {{ textCom }}
          </el-link>
          <el-button
            type="primary"
            class="m-l-16"
            @click="invoiceVisible = true"
            >申请预开票</el-button
          >
        </div>
      </div>
      <div class="search-info">
        <div class="m-b-16" v-if="current === 0">
          <el-form ref="form" :model="extralQuerys" label-width="80px">
            <el-col :span="9">
              <el-radio-group
                v-model="extralQuerys.tradeType"
                @change="tradeTypeChange"
              >
                <el-radio-button
                  :label="item.value"
                  v-for="item in tradeTypeList"
                  :key="item.value"
                  >{{ item.label }}</el-radio-button
                >
              </el-radio-group>
            </el-col>
            <el-col :span="15">
              <el-col :span="10">
                <el-form-item label="开票类型">
                  <el-select
                    clearable
                    v-model="extralQuerys.sources"
                    placeholder="请选择开票类型"
                    @change="sourcesChange"
                  >
                    <el-option
                      v-for="item in sourceList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item label="开票时间">
                  <el-date-picker
                    v-model="extralQuerys.time"
                    type="daterange"
                    clearable
                    range-separator="至"
                    start-placeholder="开始日期"
                    @change="changeDate"
                    value-format="yyyy-MM-dd"
                    end-placeholder="结束日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-col>
          </el-form>
        </div>
      </div>
      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="ticketApplyPagebyent"
        :extralQuerys="extralQuerys"
      >
      </drive-table>

      <dialog-cmp
        title="抬头信息"
        :visible.sync="visible"
        width="35%"
        @confirmDialog="confirmDialog"
      >
        <driven-form
          v-if="visible"
          ref="driven-form"
          v-model="fromModel"
          :formConfigure="addFormConfigure"
        />
      </dialog-cmp>

      <dialog-cmp
        title="申请预开票"
        :visible.sync="invoiceVisible"
        width="45%"
        @confirmDialog="invoiceConfirmDialog"
      >
        <driven-form
          v-if="invoiceVisible"
          ref="invoice-driven-form"
          label-position="top"
          v-model="invoiceFromModel"
          :formConfigure="invoiceFormConfigure"
        />
      </dialog-cmp>
    </div>
  </div>
</template>

<script>
import HeaderCmp from './components/headerComponent'
import ColumnMixins from './column'
import descriptorMixins from './descriptor'
import { NumFormat } from '@/utils/tools'
import { ticketApplyPagebyent } from './api/index'
import BasicTab from '@/views/enterprise/finance/account/account-upgrade/components/BasicTab/index.vue'
export default {
  name: 'InvoiceCenterIndex',
  components: {
    BasicTab,
    // DialogCmp,
    HeaderCmp
  },
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      invoiceVisible: false,
      fromModel: {},
      invoiceFromModel: {
        notices: [
          {
            date: '2021-01-01',
            name: '张三',
            address: '北京市',
            selected: false
          },
          {
            date: '2021-01-01',
            name: '张三',
            address: '海淀区',
            selected: false
          }
        ]
      },
      visible: false,
      headDetail: {
        totalFee: 10000,
        totalTax: 0,
        totalCount: 0
      },
      balanceType: [
        {
          label: '开票记录',
          value: 0
        },
        {
          label: '开票下载',
          value: 1
        }
      ],
      current: 0,
      NumFormat,
      ticketApplyPagebyent,
      extralQuerys: {
        tradeType: 0
      },
      sourceList: [],
      tradeTypeList: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '审批中',
          value: 1
        },
        {
          label: '开票中',
          value: 2
        },
        {
          label: '已开票',
          value: 3
        }
      ]
    }
  },
  computed: {
    textCom() {
      return this.current === 0 ? '添加' : '修改'
    },
    tableColumn() {
      return this.current === 0
        ? this.recordTableColumn
        : this.downloadTableColumn
    }
  },
  methods: {
    previewEvent({ id }) {
      this.$router.push({
        path: '/finance/invoiceCenter/invoiceDetail',
        query: {
          id
        }
      })
    },
    download(row) {
      console.log('download---', row)
    },
    invoiceConfirmDialog() {
      this.$refs['invoice-driven-form'].validate(valid => {
        if (!valid) return false
      })
    },
    confirmDialog() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
      })
    },
    tradeTypeChange(val) {
      this.extralQuerys.turnId = val
      this.$refs['drive-table'].triggerSearch()
    },
    sourcesChange(val) {
      this.extralQuerys.source = val
      this.$refs['drive-table'].triggerSearch()
    },
    changeDate() {
      const { time = [] } = this.extralQuerys
      if (time) {
        this.extralQuerys.startTime = time[0]
        this.extralQuerys.endTime = time[1]
      } else {
        delete this.extralQuerys.startTime
        delete this.extralQuerys.endTime
      }
      this.$refs['drive-table'].triggerSearch()
    },
    tabsChange(index) {
      this.current = index
    }
  }
}
</script>

<style lang="scss" scoped>
.financial-content {
  border-radius: 3px 3px 3px 3px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 12px;
    left: 32px;

    .line {
      position: absolute;
      top: 12px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: 57px;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}

.position-relative {
  position: relative;
  .right-info {
    position: absolute;
    top: 8px;
    right: 0;
    font-size: 14px;
  }
}
</style>
