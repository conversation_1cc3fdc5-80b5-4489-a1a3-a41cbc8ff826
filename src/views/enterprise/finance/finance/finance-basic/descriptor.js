// 正则校验座机、手机号
import {
  validateBankNumber,
  validateContact,
  validateDecimal,
  validateEmail
} from '@/utils/validate'

const telRules = (rule, value, callback) => {
  if (/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value) || !value) {
    callback()
  } else {
    callback('请输入正确格式的座机、手机号')
  }
}

export default {
  components: {
    notices: () => import('./notices')
  },
  data() {
    return {
      formConfigure: {
        descriptors: {
          headName: {
            form: 'input',
            span: 12,
            label: '发票抬头',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入发票抬头'
              }
            ]
          },
          creditCode: {
            form: 'input',
            span: 12,
            label: '公司税号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入公司税号'
              }
            ]
          },
          regAddress: {
            form: 'input',
            span: 12,
            label: '注册地址',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入注册地址'
              }
            ]
          },
          phone: {
            form: 'input',
            span: 12,
            label: '电话号码',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入电话号码'
              },
              {
                validator: telRules
              }
            ]
          },
          bankName: {
            form: 'input',
            span: 12,
            label: '开户行',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入开户行'
              }
            ]
          },
          bankAccount: {
            form: 'input',
            span: 12,
            label: '银行卡号',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入银行卡号'
              },
              {
                validator: (rule, value, callback) => {
                  if (!value) return callback()
                  if (/^([1-9]{1})(\d{15}|\d{18}|\d{19})$/.test(value)) {
                    callback()
                  } else {
                    callback(new Error('请输入正确的16、19或20位银行卡号'))
                  }
                }
              }
            ]
          }
        }
      },
      addFormConfigure: {
        labelWidth: '120px',
        descriptors: {
          saleName: {
            form: 'input',
            label: '企业名称',
            disabled: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业名称'
              }
            ],
            attrs: {
              maxlength: 15
            }
          },
          taxpayerNumber: {
            form: 'input',
            label: '纳税人识别号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入纳税人识别号'
              }
            ],
            attrs: {
              maxlength: 50
            }
          },
          saleBankName: {
            form: 'input',
            label: '企业开户行银行',
            rule: [
              {
                type: 'string',
                message: '请输入企业开户行银行'
              }
            ],
            attrs: {
              maxlength: 25
            }
          },
          saleBankNo: {
            form: 'input',
            label: '银行账号',
            rule: [
              {
                type: 'string',
                message: '请输入银行账号'
              },
              {
                validator: validateBankNumber
              }
            ]
          },
          saleAddress: {
            form: 'input',
            label: '单位地址',
            rule: [
              {
                type: 'string',
                message: '请输入单位地址'
              }
            ],
            attrs: {
              maxlength: 30
            }
          },
          contactNumber: {
            form: 'input',
            label: '单位电话',
            rule: [
              {
                type: 'string',
                message: '请输入单位电话'
              },
              {
                validator: validateContact
              }
            ]
          },

          email: {
            form: 'input',
            label: '发票接收邮箱',
            rule: [
              {
                type: 'string',
                message: '请输入发票接收邮箱'
              },
              {
                validator: validateEmail
              }
            ]
          }
        }
      },
      invoiceFormConfigure: {
        descriptors: {
          contractNo: {
            form: 'select',
            label: '选择合同',
            options: [],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择选择合同' //请选择选择合同
              }
            ]
          },
          notices: {
            form: 'component',
            label: '选择缴费通知单',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请选择缴费通知单'
              }
            ],
            render: () => {
              return (
                <notices
                  ref="notices"
                  v-model={this.invoiceFromModel.notices}
                />
              )
            }
          },
          amount: {
            form: 'input',
            label: '预开票金额',
            disabled: true,
            rule: [
              {
                type: 'string',
                message: '请输入预开票金额'
              },
              {
                validator: validateDecimal
              }
            ],
            customRight: () => {
              return <div style={{ marginTop: '50px' }}>元</div>
            }
          },
          comment: {
            form: 'input',
            label: '备注信息',
            rule: [
              {
                type: 'string',
                message: '请输入备注信息'
              }
            ],
            props: {
              type: 'textarea'
            },
            attrs: {
              maxlength: 200
            }
          }
        }
      }
    }
  }
}
