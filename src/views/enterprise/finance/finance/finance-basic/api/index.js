import request from '@/utils/request'

// 获取企业标签下拉列表
export function ticketHeadList() {
  return request({
    url: '/ticket/head/list',
    method: 'get'
  })
}

// 新增抬头管理
export function ticketHeadCreate(data) {
  return request({
    url: '/ticket/head/create',
    method: 'post',
    data
  })
}

// 抬头默认
export function headSetDefault(params) {
  return request({
    url: '/ticket/head/set_default',
    method: 'get',
    params
  })
}

// 修改抬头
export function headSetUpdate(data) {
  return request({
    url: '/ticket/head/update',
    method: 'put',
    data
  })
}

// 删除抬头
export function headSetDelete(data) {
  return request({
    url: '/ticket/head/delete?id=' + data.id,
    method: 'Delete'
  })
}

// 发票申请列表
export function ticketApplyPagebyent(params) {
  return request({
    url: '/ticket/apply/page_by_ticket',
    method: 'get',
    params
  })
}

// 申请开票查询列表
export function ticketApplyEntFindBillList(params) {
  return request({
    url: '/ticket/apply/ent_find_bill_list',
    method: 'get',
    params
  })
}

//企业统计勾选的账单列表
export function ticketApplyEntCountBillList(data) {
  return request({
    url: '/ticket/apply/ent_count_Bill_List',
    method: 'post',
    data
  })
}

//企业端-企业申请发票第一步处理
export function ticketApplyCreateBillList(data) {
  return request({
    url: '/ticket/apply/create_step_one',
    method: 'post',
    data
  })
}

//企业端-企业申请发票第二步保存
export function ticketApplyCreateBillTwo(data) {
  return request({
    url: '/ticket/apply/create_step_two',
    method: 'post',
    data
  })
}

// 统计信息/admin-api
export function applyCount() {
  return request({
    url: '/ticket/apply/count',
    method: 'get'
  })
}

//查看开票申请信息详情 /admin-api/ticket/apply/get
export function ticketDetails(params) {
  return request({
    url: '/ticket/apply/get',
    method: 'get',
    params
  })
}

//撤回发票申请信息
export function ticketRecall(params) {
  return request({
    url: '/ticket/apply/recall',
    method: 'get',
    params
  })
}

//更新发票申请信息
export function ticketApplyCreateBillUpdate(data) {
  return request({
    url: '/ticket/apply/update',
    method: 'put',
    data
  })
}
