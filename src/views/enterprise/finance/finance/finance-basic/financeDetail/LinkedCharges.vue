<template>
  <div class="m-t-40 font-size-14">
    <div class="m-b-16">关联收款</div>
    <drive-table
      ref="drive-table"
      max-height="350"
      :columns="tableColumn"
      :table-data="detailsInfo.tableData"
    />
  </div>
</template>

<script>
export default {
  name: 'LinkedCharges',
  props: {
    detailsInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableColumn: [
        {
          prop: 'entName',
          label: '客户名称'
        },
        {
          prop: 'billCode',
          label: '付款方'
        },
        {
          prop: 'ticketType',
          label: '款项名称'
        },
        {
          prop: 'feeType',
          label: '收款金额（含税）'
        },
        {
          prop: 'feeType',
          label: '收款部门'
        },
        {
          prop: 'feeType',
          label: '收款单'
        },
        {
          prop: 'feeType',
          label: '可开票金额（元）'
        },
        {
          prop: 'feeType',
          label: '已开票金额（元）'
        }
      ]
    }
  }
}
</script>

<style scoped lang="scss"></style>
