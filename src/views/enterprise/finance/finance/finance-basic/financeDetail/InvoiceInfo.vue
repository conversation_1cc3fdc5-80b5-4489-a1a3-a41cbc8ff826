<template>
  <div class="font-size-14">
    <h3 class="m-b-16">发票信息</h3>
    <el-descriptions size="medium" :column="2" border>
      <el-descriptions-item label="发票类型">
        增值税普通发票（电子）
      </el-descriptions-item>
      <el-descriptions-item label="购买方名称">
        安徽xxxxx有限公司
      </el-descriptions-item>
      <el-descriptions-item label="税号">
        913401006456367496
      </el-descriptions-item>
      <el-descriptions-item label="开户行及账号">
        徽商银行自贸区合肥片区支行 1025701021000449231
      </el-descriptions-item>
      <el-descriptions-item label="地址、电话">
        合肥市高新区创新产业园一期C3栋307、308、309、310室 66685512
      </el-descriptions-item>
    </el-descriptions>
    <drive-table
      class="m-t-16"
      ref="drive-table"
      max-height="350"
      :columns="tableColumn"
      :table-data="detailsInfo.tableData"
    >
    </drive-table>
    <div class="m-t-16 flex">
      <div class="m-r-40">
        <span class="color-info">价税合计(大写)：</span>
        <span class="color-primary">伍万壹仟元整</span>
      </div>
      <div>
        <span class="color-info">价税合计(大写)：</span>
        <span class="color-primary">￥51000.00</span>
      </div>
    </div>
    <div class="m-t-40">
      <div class="m-b-16">备注信息</div>
      <div class="color-info">
        地址：XXX园区，2栋2层203租金：50000.00，
        收款日期：2023-01-01租金：1000.00， 收入日期：2023-01-01~2023-06-30
      </div>
    </div>
  </div>
</template>

<script>
import CommonMixin from './column'
export default {
  name: 'InvoiceInfo',
  mixins: [CommonMixin],
  props: {
    detailsInfo: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style scoped lang="scss">
.label-info {
  width: 120px;
  background: #e1f3d8;
}
</style>
