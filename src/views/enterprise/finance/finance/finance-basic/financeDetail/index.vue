<template>
  <div>
    <module-header
      type="primary"
      title="发票详情"
      :img="require('../financeApply/images/img.png')"
      :imgOpacity="0.1"
    >
      <div class="header-content">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/finance/invoiceCenter/index' }"
            >发票中心</el-breadcrumb-item
          >
          <el-breadcrumb-item>
            <span>发票详情</span>
          </el-breadcrumb-item>
        </el-breadcrumb>

        <div class="flex justify-content-between align-items-center m-t-12">
          <div class="flex align-items-center">
            <p class="font-strong font-size-18 m-r-8">
              安徽淑梅信息结束有限公司
            </p>
            <el-tag class="m-r-8">已开票</el-tag>
            <el-tag type="success">先票后款</el-tag>
          </div>
          <el-button type="primary">发票下载和重发</el-button>
        </div>

        <div class="flex m-t-12 font-size-14">
          <div class="m-r-16">
            <span class="m-r-8 color-info">申请人：</span>
            <span>收款确认后系统发起</span>
          </div>
          <div class="m-r-16">
            <span class="m-r-8 color-info">申请时间：</span>
            <span>2023-01-01 12:34</span>
          </div>
          <div>
            <span class="color-info">开票日期：</span>
            <span>2023-01-01 14:30</span>
          </div>
        </div>
      </div>
    </module-header>
    <div class="lateral-wrapper p-24">
      <!--      发票信息-->
      <invoice-info :detailsInfo="detailsInfo" />

      <!--      材料-->
      <material />

      <!--     关联收款-->
      <linked-charges />
    </div>

    <!-- 查看附件详情 -->
    <dialog-cmp
      title="发票预览"
      :visible.sync="visibleSee"
      width="30%"
      :haveOperation="false"
    >
      <div>
        <Uploader v-model="viewList" type="avatar" mulity onlyForView />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from '../column'
import { NumFormat } from '@/utils/tools'
import { ticketDetails } from '../api/index'
export default {
  name: 'index',
  components: {
    ModuleHeader,
    InvoiceInfo: () => import('./InvoiceInfo'),
    Material: () => import('./Material'),
    LinkedCharges: () => import('./LinkedCharges')
  },
  mixins: [ColumnMixins],
  data() {
    return {
      detailsInfo: {},
      NumFormat,
      tableList: [],
      id: null,
      visibleSee: false,
      viewList: []
    }
  },
  mounted() {
    // const { id } = this.$route.query
    // this.id = id
    // this.ticketDetails(id)
  },
  methods: {
    billHandler() {
      const { attachMap = {} } = this.detailsInfo
      if (
        attachMap?.informationAttach.length > 0 &&
        attachMap.informationAttach
      ) {
        this.viewList = attachMap.informationAttach
        this.visibleSee = true
      } else {
        this.$message.warning('暂无附件')
      }
    },
    ticketDetails(e) {
      ticketDetails({ id: e }).then(res => {
        this.detailsInfo = res
      })
    }
  }
}
</script>

<style scoped>
.header-content {
  padding: 24px;
  margin-top: 24px;
  background-color: #fff;
  width: 100%;
  height: 135px;
}
</style>
