<template>
  <el-table border max-height="300" :data="tableData">
    <el-table-column prop="date" label="费用类型"> </el-table-column>
    <el-table-column prop="name" label="账期" width="180"> </el-table-column>
    <el-table-column prop="address" width="80" label="期数"> </el-table-column>
    <el-table-column prop="address" label="应收金额(元)"> </el-table-column>
    <el-table-column width="80" label="选择">
      <template slot-scope="scope">
        <el-checkbox v-model="scope.row.selected" />
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
export default {
  name: 'notices',
  props: {
    tableData: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped lang="scss"></style>
