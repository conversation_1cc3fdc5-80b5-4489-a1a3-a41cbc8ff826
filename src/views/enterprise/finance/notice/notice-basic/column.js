export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'data',
          label: '费目',
          minWidth: '20%'
        },
        {
          prop: 'data2',
          label: '应缴金额',
          minWidth: '20%',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data2}</div>
          }
        },
        {
          prop: 'data3',
          label: '已缴金额',
          minWidth: '20%',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data3}</div>
          }
        },
        {
          prop: 'data4',
          label: '未缴金额',
          minWidth: '20%',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data4}</div>
          }
        }
      ],
      tableColumn1: [
        {
          prop: 'data',
          label: '费目',
          minWidth: '20%'
        },
        {
          prop: 'data2',
          label: '户名',
          minWidth: '20%'
        },
        {
          prop: 'data3',
          label: '开户行',
          minWidth: '20%'
        },
        {
          prop: 'data4',
          label: '账户',
          minWidth: '20%'
        }
      ]
    }
  }
}
