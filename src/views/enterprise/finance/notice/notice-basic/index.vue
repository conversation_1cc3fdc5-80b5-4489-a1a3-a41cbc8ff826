<template>
  <div>
    <basic-card>
      <div class="flex align-items-center justify-content-between">
        <div class="flex align-items-center">
          <div class="font-size-14 notice-left">公司名称：</div>
          <div class="font-size-14 notice-title">安徽中安创谷科技园有限公司</div>
        </div>
        <div class="notice-right font-size-14">合肥高创股份有限公司</div>
      </div>
      <div class="flex align-items-center justify-content-between m-t-16">
        <div class="flex align-items-center">
          <div class="font-size-14 notice-left">统一社会信用代码：</div>
          <div class="font-size-14 notice-title">
            GXGF-CXCYYEQ03-XZ-20220001
          </div>
        </div>
        <div class="flex font-size-14 notice-right">
          <div>打印时间：</div>
          <div>2023.02.21</div>
        </div>
      </div>
      <div class="flex align-items-center justify-content-between m-t-16">
        <div class="flex align-items-center">
          <div class="font-size-14 notice-left">合同编号：</div>
          <div class="font-size-14 notice-title">GXGF-GXGF-GXGF</div>
        </div>
      </div>
      <div class="p-t-16 p-b-24">
        <div class="flex m-b-16">
          <div class="font-size-14 line-height-22 notice-table-title">
            账单信息
          </div>
        </div>
        <table class="info-table" border="1" cellpadding="0" cellspacing="0">
          <tr>
            <th>账单编号</th>
            <td>123</td>
            <th>账单生成日期</th>
            <td>2023.02.21</td>
          </tr>
          <tr>
            <th>账单周期周期</th>
            <td>2023.02.21~2023.02.21</td>
            <th>应缴时间</th>
            <td>2023.02.21</td>
          </tr>
          <tr>
            <th>账单总金额</th>
            <td class="notice-title">￥350,000</td>
            <th>已缴总金额</th>
            <td class="notice-title">￥350,000</td>
          </tr>
          <tr :colspan="3">
            <th>未缴总金额</th>
            <td class="notice-title">壹万染仟玖佰贰拾捌元贰角（￥17928.2)</td>
          </tr>
        </table>
      </div>
      <div class="p-t-16 p-b-24">
        <div class="flex m-b-16">
          <div class="font-size-14 line-height-22 notice-table-title">
            费用明细
          </div>
        </div>
        <div>
          <drive-table :columns="tableColumn" :table-data="tableData" />
        </div>
        <div class="font-size-12 m-t-10 line-height-20">
          贵司租用合肥高创股份有限公司管辖的创业中心3号楼107室
          653.52㎡，从2021-09-01至2022-12-31,房屋使用费单价为20元/月/平方米，综合服务费单价为15元/月/平方米，
          季度付款。
          本期应付款合计人民币：陆万捌仟陆佰壹拾玖元陆角（￥68619.6）。
        </div>
      </div>
      <div class="p-t-16 p-b-24">
        <div class="flex m-b-16">
          <div class="font-size-14 line-height-22">收款账户</div>
        </div>
        <div>
          <drive-table :columns="tableColumn1" :table-data="tableData1" />
        </div>
      </div>
      <div class="p-t-16 flex justify-content-end">
        <div class="m-l-8">
          <el-button @click="download">下载</el-button>
        </div>
        <div class="m-l-8">
          <el-button @click="printUp">打印</el-button>
        </div>
        <div class="m-l-8">
          <el-button type="primary" @click="writeOffAccout">账户核销</el-button>
        </div>
      </div>
    </basic-card>
  </div>
</template>
<script>
import ColumnMixins from './column'
export default {
  mixins: [ColumnMixins],
  data() {
    return {
      tableData: [
        {
          data: '租赁费',
          data2: '￥350,000',
          data3: '￥350,000',
          data4: '￥350,000'
        }
      ],
      tableData1: [
        {
          data: '房租费',
          data2: '合肥高创股份有限公司',
          data3: '光大银行长江支行',
          data4: '666666666666'
        }
      ]
    }
  },
  methods: {
    // 下载
    download() {
      console.log('下载')
    },
    // 打印
    printUp() {
      console.log('打印')
    },
    // 账户核销
    writeOffAccout() {
      console.log('账户核销')
    }
  }
}
</script>
<style lang="scss" scoped>
.c000 {
  color: #000;
}
// 信息表格
.info-table {
  width: 100%;
  font-size: 14px;
  border: 1px solid #e7e7e7;
  th {
    // background-color: #fbfafa !important;
    border: 1px solid #e7e7e7;
    width: 14%;
    font-weight: normal;
    text-align: left;
    padding: 8px 12px;
  }

  td {
    border: 1px solid #e7e7e7;
    padding: 8px 12px;
    line-height: 18px;
    width: 36%;
  }
}
.notice-right {
  font-size: 12px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
}
.notice-left {
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
}
.notice-title {
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: #ed7b2f;
}
.notice-table-title {
  font-size: 14px;
  font-family: Source Han Sans CN-Normal, Source Han Sans CN;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
}
</style>
