import { getRefundStatus } from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '退款金额（元）',
          prop: 'data',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data}</div>
          }
        },
        {
          label: '退房房号',
          prop: 'data2'
        },
        {
          label: '退房面积（m²）',
          prop: 'data3'
        },
        {
          label: '状态',
          prop: 'data4',
          render: (h, scope) => {
            return <div>{getRefundStatus(h, scope.row.data4)}</div>
          }
        },
        {
          label: '备注',
          prop: 'data5'
        }
      ]
    }
  }
}
