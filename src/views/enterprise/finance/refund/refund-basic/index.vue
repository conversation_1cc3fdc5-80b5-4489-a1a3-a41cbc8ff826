<template>
  <div class="activity-basic">
    <module-header
      type="primary"
      title="退费信息"
      desc="查看园区退费给当前企业的所有记录"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    >
    </module-header>

    <div
      class="lateral-wrapper form-card-container p-t-32 p-l-32 p-r-32 bg-white"
    >
      <div class="font-size-16 line-height-24">退费记录</div>
      <div class="m-16">
        <drive-table
          :columns="tableColumn"
          :table-data="tableList"
          height="392px"
        >
        </drive-table>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from './column/list-column'

export default {
  name: 'RefundBasic',
  components: {
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      tableList: [
        {
          data: '68,468',
          data2: 'zd10001071',
          data3: '1000',
          data4: 1,
          data5: '发票备注'
        }
      ]
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}
.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}
.form-card-container {
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
</style>
