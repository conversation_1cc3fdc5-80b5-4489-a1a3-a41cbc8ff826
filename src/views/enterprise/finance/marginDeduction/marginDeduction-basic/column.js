import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '申请抵扣金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '缴存余额(元)',
          prop: 'acAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.acAmount)}</div>
            )
          }
        },
        {
          label: '申请时间',
          prop: 'acAmount'
        },
        {
          label: '申请状态',
          prop: 'payStatusStr'
        },
        {
          label: '详情',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goDetails(scope.row)
                }}
              >
                查看
              </el-link>
            )
          }
        }
      ]
    }
  }
}
