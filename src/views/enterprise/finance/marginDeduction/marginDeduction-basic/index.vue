<template>
  <div class="enter-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="保证金抵扣"
      desc="查看企业保证金抵扣申请信息"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    />
    <div class="lateral-wrapper lateral-wrapper-content">
      <basic-tab
        ref="basicTab"
        :tabs-data="list"
        :current="current"
        @tabsChange="tabsChange"
      />
      <drive-table ref="driveTable" :columns="tableColumn"> </drive-table>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import BasicTab from '@/components/BasicTab'
import ColumnMixins from './column'

export default {
  name: 'EnterpriseEnterParkBasic',
  components: {
    BasicTab,
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      current: 0,
      list: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '审批中',
          value: 1
        },
        {
          label: '已同意',
          value: 3
        },
        {
          label: '已拒绝',
          value: 4
        }
      ]
    }
  },
  methods: {
    tabsChange(index) {
      this.current = index
    },
    goDetails(row) {
      this.$router.push({
        path: '/finance/account/marginDeductionDetail',
        query: {
          id: row.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lateral-wrapper-content {
  padding: 24px;
  background-color: #ffffff;
}
</style>
