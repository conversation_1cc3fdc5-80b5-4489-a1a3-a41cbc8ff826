<template>
  <div>
    <module-header
      type="primary"
      title="保证金抵扣申请"
      :img="require('./images/money.png')"
      :imgOpacity="0.1"
    >
      <div class="flex justify-content-between">
        <div>
          <div class="p-t-32">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item
                :to="{ path: '/finance/account/marginDeduction' }"
                >保证金抵扣</el-breadcrumb-item
              >
              <el-breadcrumb-item>
                <span style="color: #000">保证金抵扣申请</span>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="flex align-items-center m-t-24 m-b-16">
            <el-tag type="success">审批中</el-tag>
            <h1 class="p-l-8 font-strong">保证金抵扣申请</h1>
          </div>
          <div class="flex">
            <div>
              <span class="font-size-14" style="color: rgba(0, 0, 0, 0.4)"
                >申请时间:</span
              >
              <span
                class="font-size-12 m-l-8"
                style="color: rgba(0, 0, 0, 0.8); font-weight: 400"
                >{{
                  parseTime(detailsInfo.applyTime, '{y}-{m}-{d} {h}:{i}')
                }}</span
              >
            </div>
            <div class="m-l-16">
              <span class="font-size-14" style="color: rgba(0, 0, 0, 0.4)"
                >申请抵扣金额:</span
              >
              <span
                class="font-size-12 m-l-8"
                style="color: rgba(0, 0, 0, 0.8); font-weight: 400"
                >{{ NumFormat(detailsInfo.applyMoney) }}元</span
              >
            </div>
          </div>
        </div>
      </div>
    </module-header>
    <div class="lateral-wrapper lateral-wrapper-content">
      <div class="m-b-24">
        <div class="m-b-12 font-size-14 info">保证金账户</div>
        <el-table :data="tableData" border>
          <el-table-column prop="name" label="缴存基数(元)">
            <template slot-scope="{ row }">
              <span>{{ NumFormat(row.name) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="缴存余额(元)">
            <template slot-scope="{ row }">
              <span>{{ NumFormat(row.name) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" label="操作">
            <template slot-scope="scope">
              <el-link>查看{{ scope }}</el-link>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="m-b-24">
        <div class="m-b-12 font-size-14 info">备注内容</div>
        <div class="remark line-height-22">
          aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa
        </div>
      </div>
      <div>
        <div class="m-b-12 font-size-14 info">相关附件</div>
        <files-list :files="[]" />
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import { NumFormat, parseTime } from '@/utils/tools'
import { getDetail } from './api/index'
import FilesList from '@/components/Uploader/files.vue'
export default {
  name: 'DrawMoneyDetail',
  components: { FilesList, ModuleHeader },
  data() {
    return {
      parseTime,
      detailsInfo: {},
      NumFormat,
      tableData: [],
      id: null
    }
  },
  mounted() {
    const { id } = this.$route.query
    this.id = id
    this.getDetailInfo(id)
  },
  methods: {
    getDetailInfo(id) {
      getDetail(id).then(res => {
        if (res) {
          this.detailsInfo = res
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lateral-wrapper-content {
  background-color: #ffffff;
  padding: 24px;
}
.info {
  color: rgba(0, 0, 0, 0.4);
}

.remark {
  //文字超出换行
  word-wrap: break-word;
}
</style>
