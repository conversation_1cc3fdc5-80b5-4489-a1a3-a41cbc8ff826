import { NumFormat } from '@/utils/tools'
import { getPayType, getSubmitPart } from './utils/status'
export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'enterpriseName',
          label: '企业名称',
          width: 200,
          showOverflowTooltip: true
        },
        {
          prop: 'amount',
          label: '到账金额',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          prop: 'transactTime',
          label: '交易时间',
          sortable: true,
          width: 120
        },
        {
          prop: 'billCode',
          label: '账单编号',
          width: 200
        },
        {
          prop: 'payType',
          label: '核销方式',
          width: 120,
          render: (h, scope) => {
            return <div>{getPayType(h, scope.row.payType)}</div>
          }
        },
        {
          prop: 'submitPart',
          label: '核销类型',
          width: 120,
          render: (h, scope) => {
            return <div>{getSubmitPart(h, scope.row.submitPart)}</div>
          }
        },
        {
          prop: 'content',
          label: '备注',
          width: 180,
          showOverflowTooltip: true
        },
        {
          label: '相关附件',
          prop: 'attaches',
          render: (h, scope) => {
            return (
              <div
                class="color-primary pointer"
                onClick={() => {
                  this.seeAttachment(scope.row.attaches)
                }}
              >
                {Object.keys(scope.row.attaches ? scope.row.attaches : {})
                  .length > 0 ? (
                  <span class="color-primary">查看</span>
                ) : (
                  <span class="color-info">暂无附件</span>
                )}
              </div>
            )
          }
        }
      ]
    }
  }
}
