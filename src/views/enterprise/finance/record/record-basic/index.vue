<template>
  <div>
    <header-cmp
      class="header-cmp"
      style="padding-bottom: 20px; height: max-content"
      :img="require('./images/header-bg.png')"
      title="缴费记录"
    >
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          汇总查看当前企业全部缴费记录信息
        </div>
      </template>
      <!--      <template v-slot:right>-->
      <!--        <div>-->
      <!--          <el-button size="small" type="primary">导出台账</el-button>-->
      <!--        </div>-->
      <!--      </template>-->
      <!--      <template v-slot:content>-->
      <!--        <div class="financial-content bg-white">-->
      <!--          <div class="flex account-left bg-white justify-content-around">-->
      <!--            <div class="xx">-->
      <!--              <div class="zh">-->
      <!--                <div class="font-size-16 m-t-10 flex align-items-center">-->
      <!--                  <span>交易笔数(笔)</span>-->
      <!--                  <span class="m-l-16"> </span>-->
      <!--                </div>-->
      <!--                <div-->
      <!--                  class="acconutname m-t-17 font-size-24 flex align-items-center"-->
      <!--                >-->
      <!--                  <span>{{ '350' | noData }}</span>-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--            <div class="xx">-->
      <!--              <div class="zh">-->
      <!--                <div class="font-size-16 m-t-10 flex align-items-center">-->
      <!--                  <span class="line"></span>-->
      <!--                  <span>到账金额(元)</span>-->
      <!--                  <span class="m-l-16"> </span>-->
      <!--                </div>-->
      <!--                <div class="payname m-t-17 font-size-24">-->
      <!--                  <span class="font-size-18">￥</span>-->
      <!--                  <span>{{ '350,000' | noData }}</span>-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--            <div class="xx">-->
      <!--              <div class="zh">-->
      <!--                <div class="font-size-16 m-t-10 flex align-items-center">-->
      <!--                  <span class="line"></span>-->
      <!--                  <span>不平帐笔数(笔)</span>-->
      <!--                  <span class="m-l-16"> </span>-->
      <!--                </div>-->
      <!--                <div class="m-t-17 font-size-24">-->
      <!--                  <span class="font-size-18">￥</span>-->
      <!--                  <span>{{ '35' | noData }}</span>-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--            <div class="xx">-->
      <!--              <div class="zh">-->
      <!--                <div-->
      <!--                  class="font-size-16 m-t-10 flex align-items-center pointer"-->
      <!--                >-->
      <!--                  <span class="line"></span>-->
      <!--                  <span>平账笔数(笔)</span>-->
      <!--                  <span class="m-l-16"> </span>-->
      <!--                </div>-->
      <!--                <div class="m-t-17 font-size-24">-->
      <!--                  <span class="font-size-18">￥</span>-->
      <!--                  <span>{{ '0' | noData }}</span>-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--            <div class="xx">-->
      <!--              <div class="zh" style="width: 80%">-->
      <!--                <div class="font-size-16 m-t-10 flex align-items-center">-->
      <!--                  <span class="line"></span>-->
      <!--                  <span>已收进度</span>-->
      <!--                </div>-->
      <!--                <div class="w100 m-t-18">-->
      <!--                  <el-progress-->
      <!--                    :text-inside="true"-->
      <!--                    :stroke-width="20"-->
      <!--                    :percentage="20"-->
      <!--                    status="success"-->
      <!--                  ></el-progress>-->
      <!--                </div>-->
      <!--              </div>-->
      <!--            </div>-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </template>-->
    </header-cmp>
    <!--    表单-->
    <div class="lateral-wrapper">
      <div class="m-t-14">
        <el-form ref="fromAccountInfo" :model="fromTableInfo">
          <div class="w100">
            <div class="flex justify-content-between">
              <div>
                <el-form-item class="m-l-8">
                  <el-input
                    style="width: 288px"
                    placeholder="请输入账单编号搜索"
                    clearable
                    v-model="nameOrNo"
                    @input="searchName"
                  >
                    <i slot="prefix" class="el-input__icon el-icon-search"></i>
                  </el-input>
                </el-form-item>
              </div>
              <div class="flex">
                <el-form-item class="m-l-8">
                  <el-select
                    style="width: 140px"
                    v-model="fromTableInfo.payTimeType"
                    clearable
                    placeholder="交易时间"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in payTimeTypeList"
                      :key="item.key"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item class="m-l-8">
                  <el-select
                    style="width: 140px"
                    v-model="fromTableInfo.submitPart"
                    clearable
                    placeholder="核销类型"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in submitPartList"
                      :key="item.key"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
      </div>
      <div>
        <drive-table
          ref="drive-table"
          :columns="tableColumn"
          :api-fn="getEntCollectList"
          :extral-querys="extralQuerys"
        >
        </drive-table>
      </div>
    </div>
  </div>
</template>

<script>
import ColumnMixins from './column'
// import DialogCmp from "@/components/BasicDialog";
import descriptorMixins from './descriptor'
import HeaderCmp from './components/headerComponent'
import { NumFormat } from '@/utils/tools'
import { getEntCollectList } from './api/index'
export default {
  name: 'AccountInformation',
  components: {
    HeaderCmp
  },
  mixins: [ColumnMixins, descriptorMixins],
  data() {
    return {
      getEntCollectList,
      fromTableInfo: {},
      payTimeTypeList: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '本周',
          value: 2
        },
        {
          label: '本月',
          value: 3
        },
        {
          label: '本季度',
          value: 4
        },
        {
          label: '本年',
          value: 5
        },
        {
          label: '近3年',
          value: 6
        },
        {
          label: '3年前',
          value: 7
        }
      ],
      NumFormat,
      tableData: [],
      nameOrNo: '',
      submitPartList: [
        {
          label: '园区',
          value: 1
        }
      ], // 核销类型
      extralQuerys: {
        nameOrNo: ''
      }
    }
  },
  watch: {
    fromTableInfo: {
      handler(val) {
        if (val) {
          this.$refs['drive-table'] &&
            this.$refs['drive-table'].triggerSearch(val)
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 搜索条件
    searchQueryHook(e) {
      console.log(e)
    },
    searchName(e) {
      this.extralQuerys.nameOrNo = e ? e : null
      this.$refs['drive-table'].triggerSearch(this.extralQuerys)
    },
    //  打开附件
    seeAttachment(row) {
      if (row) {
        window.open(row.informationAttach[0]?.path)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.financial-content {
  border-radius: 3px 3px 3px 3px;
}
.acconutname {
  font-weight: 600;
  color: #000;
}
.acconut-name {
  font-weight: 600;
  color: #000;
}
.payname {
  font-weight: 600;
  @include font_color(--color-warning);
}
.tx {
  width: 80px;
  height: 80px;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;
  .zh {
    position: absolute;
    top: 12px;
    left: 32px;

    .line {
      position: absolute;
      top: 12px;
      left: -23px;
      width: 1px;
      height: 60px;
      background: #ebedf1;
      border-radius: 0 0 0 0;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}

.percentage {
  color: #00a870;
  margin-top: 9px;
  .percent {
    width: 57px;
    height: 24px;
    background: #e8f8f2;
    font-size: 12px;
    display: inline-block;
    margin-left: 6px;
    border-radius: 3px 3px 3px 3px;
    padding: 8px 5px;
    line-height: 6px;
    box-sizing: border-box;
    opacity: 1;
  }

  .reduce {
    color: #e34d59;
  }
  .percent-reduce {
    color: #e34d59;
    background: #f8b9be;
    padding: 8px 8px;
  }
}

.choose {
  width: 150px;
  height: 32px;
  background: #e7e7e7;
  border-radius: 3px 3px 3px 3px;
  opacity: 1;
  padding: 2px;
  font-size: 14px;

  display: flex;
  .item-btn {
    width: 50%;
    height: 100%;
    line-height: 28px;
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    text-align: center;
    cursor: pointer;
    z-index: 99;
    color: #000;
  }
  .move-bgc {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 76px;
    height: 87%;
    color: #fff;
    background: #ed7b2f;
    border-radius: 3px 3px 3px 3px;
    //过渡
    transition: all 0.5s;
    transform: translateX(0%);
    //transform: translateX(0%);
  }
}
.qe {
  width: 100%;
  height: 32px;
}
//
.financial-content {
  padding-top: 10px;
  height: 96px;
  border-radius: 3px 3px 0 0;
  opacity: 1;
  //border: 1px solid #dcdcdc;
}
.right-title {
  color: #616266;
}
.acconutname {
  font-weight: 700;
  color: #191919;
}
.income {
  font-size: 16px;
  font-weight: 700;
}
.payname {
  font-weight: 700;
  @include font_color(--color-warning);
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;

  .line {
    width: 1px;
    height: 60px;
    background: #ebedf1;
    border-radius: 0 0 0 0;
    opacity: 1;
    position: absolute;
    left: 0;
    top: 10px;
  }
  .zh {
    width: 88%;
    position: absolute;
    top: 0;
    left: 32px;

    .enter {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}
.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 16px;
  margin-top: 6px;
}
:deep(.header-cmp img) {
  display: none;
}
</style>
