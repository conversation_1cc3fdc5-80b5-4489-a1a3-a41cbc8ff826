export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          tenantId: {
            form: 'input',
            span: 12,
            label: '发票抬头',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入发票抬头'
              }
            ]
          },
          tenantId1: {
            form: 'input',
            span: 12,
            label: '公司税号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入公司税号'
              }
            ]
          },
          tenantId2: {
            form: 'input',
            span: 12,
            label: '注册地址',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入注册地址'
              }
            ]
          },
          tenantId3: {
            form: 'input',
            span: 12,
            label: '电话号码',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入电话号码'
              }
            ]
          },
          tenantId4: {
            form: 'input',
            span: 12,
            label: '开户行',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入开户行'
              }
            ]
          },
          tenantId5: {
            form: 'input',
            span: 12,
            label: '银行卡号',
            rule: [
              {
                required: false,
                type: 'string',
                message: '请输入银行卡号'
              }
            ]
          }
        }
      }
    }
  }
}
