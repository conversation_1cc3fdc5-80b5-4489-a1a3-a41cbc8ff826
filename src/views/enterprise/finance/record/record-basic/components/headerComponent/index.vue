<template>
  <div class="module-header">
    <div class="lateral-wrapper">
      <div class="title">
        <div
          class="flex align-items-center line-height-28 justify-content-between font-size-20 color-text-primary w100"
        >
          <div
            class="flex align-items-center"
            style="color: #191919; font-weight: 700"
          >
            {{ title }} <slot name="title-right"></slot>
          </div>
          <div class="">
            <slot name="right" />
          </div>
        </div>

        <!-- <h2
          class="flex align-items-center font-size-20 line-height-28 font-strong color-text-primary"
        >
          {{ title }}
          <slot name="title-right"></slot>
        </h2>
        <p class="font-size-12 line-height-22 color-text-regular">
          {{ desc }}
        </p> -->
      </div>
      <div class="m-t-10">
        <slot name="content" />
      </div>

      <img
        width="360px"
        height="220px"
        :src="img"
        :style="imgOpacityStyle"
        alt=""
      />
    </div>
    <div class="module-header-bg" :class="type"></div>
  </div>
</template>

<script>
export default {
  name: 'HeaderComponent',
  props: {
    type: {
      type: String,
      default: 'primary'
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    img: {
      type: String,
      default: ''
    },
    imgOpacity: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      hasSlot: false
    }
  },
  computed: {
    imgOpacityStyle() {
      return {
        opacity: this.imgOpacity
      }
    }
  },
  created() {
    this.hasSlot = !!this.$slots.default
  }
}
</script>

<style lang="scss" scoped>
.module-header {
  height: 230px;
  position: relative;
  .lateral-wrapper {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    position: relative;
    z-index: 2;
    .title {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-top: 40px;
      flex: 1;
      overflow: hidden;
      h2 {
        margin-bottom: 8px;
      }
      p {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    img {
      position: absolute;
      right: 0;
      top: 0;
      width: 360px;
      height: 230px;
      z-index: -1;
    }
  }
  .module-header-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 1;
    &.primary {
      @include background_color_mix(--color-primary, #ffffff, 95%);
    }
    &.success {
      @include background_color_mix(--color-success, #ffffff, 95%);
    }
    &.warning {
      @include background_color_mix(--color-warning, #ffffff, 95%);
    }
    &.danger {
      @include background_color_mix(--color-danger, #ffffff, 95%);
    }
  }
}
</style>
