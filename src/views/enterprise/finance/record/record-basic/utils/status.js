/*
 * @Author: cc <EMAIL>
 * @Date: 2023-02-24 15:44:35
 * @LastEditors: cc <EMAIL>
 * @LastEditTime: 2023-02-24 15:58:23
 * @FilePath: \parallel-cloud-manage\src\views\enterprise\finance\record\record-basic\utils\status.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

// 支付方式
export function getPayType(h, val) {
  switch (val) {
    case 1:
      return <el-tag type="primary">账户支付</el-tag>
    case 2:
      return <el-tag type="primary">钱包支付</el-tag>
    default:
      return '-'
  }
}

// 核销类型
export function getSubmitPart(h, val) {
  switch (val) {
    case 1:
      return '园区'
    case 2:
      return '企业'
    default:
      return '-'
  }
}
