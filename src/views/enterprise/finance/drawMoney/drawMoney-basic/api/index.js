import request from '@/utils/request'

// 获得财务头部信息
export function getDetail() {
  return request({
    url: `/bill/withdrawal/ent/top`,
    method: 'get'
  })
}

export function getTurnTab() {
  return request({
    url: `/bill/withdrawal/ent/body`,
    method: 'get'
  })
}

// 获得分页选择
export function getDetailPage(params) {
  return request({
    url: `/bill/withdrawal/ent/page_v1`,
    method: 'get',
    params
  })
}

export function withdrawal(data) {
  return request({
    url: `/bill/withdrawal/ent/withdrawal`,
    method: 'post',
    data
  })
}

export function getWithdrawalRefundApply(params) {
  return request({
    url: `/bill/withdrawal/ent/get_refund_apply`,
    method: 'get',
    params
  })
}

// 获取状态
export function getSelectState(params) {
  return request({
    url: `/bill/withdrawal/admin/get_select_state`,
    method: 'get',
    params
  })
}

//导出项目信息 Excel
export function getExportExcel() {
  return `${process.env.VUE_APP_URL_PREFIX}/bill/withdrawal/ent/export_v1`
}

// 详情
export function getDetailInfo(params) {
  return request({
    url: `/bill/withdrawal/ent/ent_detail_v1/${params}`,
    method: 'get'
  })
}

// 撤回
export function getWithdraw(params) {
  return request({
    url: `/bill/withdrawal/ent/withdraw/${params}`,
    method: 'get',
    params
  })
}
