<template>
  <div class="activity-basic">
    <header-cmp :img="require('./images/header-bg.png')" title="提现申请">
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          详细记录账户提现记录及进度
        </div>
      </template>
      <template v-slot:right>
        <div>
          <el-button v-permission="routeButtonsPermission.LEADING_OUT" size="mini" type="primary" @click="exportExcel">
            <svg-icon icon-class="cloud-upload" />
            <span>{{ routeButtonsTitle.LEADING_OUT }}台账</span></el-button
          >
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content">
          <div class="flex account-left justify-content-around">
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span>申请总数</span>
                  <span class="m-l-16"> </span>
                </div>
                <div
                  class="acconutname m-t-17 font-size-24 flex align-items-center"
                >
                  <span>{{ statisticsData.applyNum | noData }}</span>
                  <span class="inline-block m-l-10 m-t-2 font-size-16">次</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div class="font-size-16 m-t-10 flex align-items-center">
                  <span class="line"></span>
                  <span>申请已提现金额</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(statisticsData.withdrawalMoney) }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div
                  class="font-size-16 m-t-10 flex align-items-center pointer"
                >
                  <span class="line"></span>
                  <span>剩余可提现预存</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(statisticsData.remainingMoney) }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div
                  class="font-size-16 m-t-10 flex align-items-center pointer"
                >
                  <span class="line"></span>
                  <span>剩余可提现保证金</span>
                  <span class="m-l-16"> </span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{
                    NumFormat(statisticsData.remainDepositMoney)
                  }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>

    <div class="lateral-wrapper">
      <div class="flex justify-content-between">
        <el-radio-group v-model="current" @input="tabsChange">
          <el-radio-button
            :label="item.value"
            v-for="item in list"
            :key="item.value"
            >{{ item.label }}</el-radio-button
          >
        </el-radio-group>

        <el-button
          v-permission="routeButtonsPermission.WITHDRAW_APPLY"
          type="primary"
          @click="$router.push('/finance/account/initiateApply')"
          >{{ routeButtonsTitle.WITHDRAW_APPLY }}</el-button
        >
      </div>

      <drive-table
        class="m-t-16"
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getDetailPage"
        :extral-querys="extralQuerys"
      />
    </div>
  </div>
</template>

<script>
import HeaderCmp from './components/headerComponent'
import ColumnMixins from './column/list-column'
import {
  getDetail,
  getExportExcel,
  getDetailPage,
  getSelectState
} from './api/index'
import { formatGetParams, NumFormat } from '@/utils/tools'
import downloads from '@/utils/download'
import dayjs from 'dayjs'
export default {
  name: 'WithdrawalApply',
  components: {
    HeaderCmp
  },
  mixins: [ColumnMixins],
  data() {
    return {
      getDetailPage,
      NumFormat,
      list: [],
      extralQuerys: {
        type: 0,
        state: -1
      },
      current: -1,
      statisticsData: {}
    }
  },
  mounted() {
    this.getStatisticsData()
    this.getSelectState()
  },
  methods: {
    getSelectState() {
      getSelectState().then(res => {
        const list = res.map(item => {
          return { label: item.label, value: item.key }
        })
        this.list = [{ label: '全部', value: -1 }, ...list]
      })
    },
    //导出excel
    exportExcel() {
      let url = getExportExcel() + '?'
      url += formatGetParams(this.$refs['drive-table'].querys)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '台账明细.xls')
    },
    getStatisticsData() {
      getDetail().then(res => {
        if (res) {
          this.statisticsData = res || {}
        }
      })
    },
    previewEvent(e) {
      this.$router.push({
        path: '/finance/account/withdrawalApplyDetail',
        query: {
          id: e.id
        }
      })
    },
    //切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.state = e
      this.$refs['drive-table'].triggerSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}

:deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
}

.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}

.form-card-container {
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
.w-25 {
  width: 25%;
}
.financial-content {
  padding-top: 10px;
  height: 96px;
  border-radius: 3px 3px 0 0;
  background: rgba(255, 255, 255, 0.8);
  opacity: 1;
  //border: 1px solid #dcdcdc;
}
.right-title {
  color: #616266;
}
.acconutname {
  font-weight: 700;
  color: #191919;
}
.income {
  font-size: 16px;
  font-weight: 700;
}
.payname {
  font-weight: 700;
  @include font_color(--color-warning);
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;

  .line {
    width: 1px;
    height: 60px;
    background: #ebedf1;
    border-radius: 0 0 0 0;
    opacity: 1;
    position: absolute;
    left: -50px;
    top: 10px;
  }
  .zh {
    width: 88%;
    position: absolute;
    top: 0;
    left: 32px;

    .enter {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}
</style>
