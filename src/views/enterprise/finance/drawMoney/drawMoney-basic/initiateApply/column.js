import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumnAccount: [
        {
          label: '钱包类型',
          prop: 'payTypeName'
        },
        {
          label: '可提现金额(元)',
          prop: 'payableAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payableAmount)}
              </div>
            )
          }
        },
        {
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goView(scope.row, 1)
                }}
              >
                查看
              </el-link>
            )
          }
        }
      ],
      tableColumnBail: [
        {
          label: '保证金基数(元)',
          prop: 'depositBaseAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.depositBaseAmount)}
              </div>
            )
          }
        },
        {
          label: '保证金缴存(元)',
          prop: 'payableAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payableAmount)}
              </div>
            )
          }
        },
        {
          label: '缴存状态',
          prop: 'depositStatus'
        },
        {
          label: '可提现金额(元)',
          prop: 'payableAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payableAmount)}
              </div>
            )
          }
        },
        {
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goView(scope.row, 2)
                }}
              >
                查看
              </el-link>
            )
          }
        }
      ]
    }
  }
}
