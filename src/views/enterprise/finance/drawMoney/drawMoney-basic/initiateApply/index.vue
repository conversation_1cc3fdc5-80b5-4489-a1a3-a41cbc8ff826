<template>
  <div>
    <module-header
      type="primary"
      title="提现申请"
      :img="require('../images/header-bg.png')"
      desc="您可发起提现申请，园区方审核通过后会根据申请信息线下付款至您的收款账户"
      :imgOpacity="0.1"
    >
    </module-header>
    <div class="lateral-wrapper lateral-wrapper-content">
      <div class="m-b-24">
        <div class="m-b-8">账户余额</div>
        <drive-table
          class="m-t-16"
          ref="drive-table"
          :columns="tableColumnAccount"
          :tableData="tableDataAccount"
        />
      </div>

      <div class="m-b-24">
        <div class="m-b-8">保证金缴存</div>
        <drive-table
          class="m-t-16"
          ref="drive-table"
          :columns="tableColumnBail"
          :tableData="tableDataBail"
        />
      </div>

      <driven-form
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />

      <div class="footer">
        <el-button type="default" @click="cancel">取消</el-button>
        <el-button type="primary" @click="submit">提交</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Column from './column'
import Descriptors from './descriptor'
import { getWithdrawalRefundApply, withdrawal } from '../api'
export default {
  name: 'InitiateApply',
  components: { ModuleHeader },
  mixins: [Column, Descriptors],
  data() {
    return {
      tableDataAccount: [],
      tableDataBail: [],
      fromModel: {
        feeInfo: []
      }
    }
  },
  mounted() {
    this.getWithdrawalRefundApply()
  },
  methods: {
    getWithdrawalRefundApply() {
      getWithdrawalRefundApply().then(res => {
        const list = res || []
        this.tableDataAccount = list.filter(item => item.payType === 1) || []
        this.tableDataBail = list.filter(item => item.payType === 2) || []
        this.$refs.paymentAmount?.initData(list)
      })
    },
    cancel() {
      this.$router.go(-1)
      this.fromModel = {}
    },
    submit() {
      this.$refs['driven-form'].validate(valid => {
        if (!valid) return false
        const feeInfo = this.fromModel.feeInfo || []
        const row = feeInfo.find(item => item.payAmount > item.payableAmount)
        if (row) return this.$toast.warning('申请付款金额不能超出可付款金额')
        const attachIds = this.fromModel.attachIds || []
        const params = {
          ...this.fromModel,
          attachIds: attachIds.map(item => item.id)
        }
        withdrawal(params)
          .then(() => {
            this.$toast.success('提交成功')
            this.$router.go(-1)
            this.fromModel = {}
          })
          .catch(err => {
            this.$toast.error(err.message)
          })
      })
    },
    goView(row, type) {
      const url = {
        1: `/finance/account/purse`,
        2: `/account/depositCollection`
      }
      this.$router.push(url[type])
    }
  }
}
</script>

<style scoped lang="scss">
.lateral-wrapper-content {
  background-color: #ffffff;
  padding: 22px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>
