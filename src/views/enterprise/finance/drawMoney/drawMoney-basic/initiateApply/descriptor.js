import PaymentAmount from '@/views/manage/financial/financial-basic/payment/drawMoney/drawMoney-basic/list/components/PaymentAmount.vue'
export default {
  components: {
    PaymentAmount
  },
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          acBkNme: {
            form: 'input',
            span: 12,
            label: '开户行',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入开户行'
              }
            ],
            attrs: {
              maxLength: 50
            }
          },
          acBkNo: {
            form: 'input',
            span: 12,
            label: '银行卡号',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入银行卡号'
              },
              {
                validator: (rule, value, callback) => {
                  if (!value) return callback()
                  if (/^([1-9]{1})(\d{15}|\d{18}|\d{19})$/.test(value)) {
                    callback()
                  } else {
                    callback(new Error('请输入正确的16、19或20位银行卡号'))
                  }
                }
              }
            ]
          },
          feeInfo: {
            form: 'component',
            label: '提现金额',
            rule: [
              {
                required: true,
                type: 'array',
                message: '请输入提现金额'
              }
            ],
            render: () => {
              return (
                <payment-amount
                  ref="paymentAmount"
                  v-model={this.fromModel.feeInfo}
                />
              )
            }
          },
          content: {
            form: 'input',
            label: '备注内容',
            rule: [
              {
                type: 'string',
                message: '请输入备注内容'
              }
            ],
            attrs: {
              type: 'textarea',
              rows: 6,
              maxlength: 300,
              showWordLimit: true
            }
          },
          attachIds: {
            form: 'component',
            label: '相关附件',
            rule: [
              {
                type: 'array'
              }
            ],
            componentName: 'uploader',
            customRight: () => {
              return (
                <div class="line-height-32 color-info font-size-14">
                  <div>请上传不大于10MB的附件,附件不得超过三个</div>
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'drawMoney'
              },
              mulity: true,
              maxLength: 3,
              maxSize: 10,
              limit: 3
            }
          },
          contact: {
            form: 'input',
            label: '主要联系人',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入主要联系人'
              },
              {
                max: 20,
                message: '最多输入20个字符'
              }
            ]
          },
          contactPhone: {
            form: 'input',
            label: '联系方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输联系方式',
                pattern: /^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/
              }
            ]
          }
        }
      }
    }
  }
}
