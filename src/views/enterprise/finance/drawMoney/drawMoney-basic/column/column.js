// import { NumFormat } from '@/utils/tools'
import // contractTypeOptions,
// contractStatusOptions,
//  getContractType
// getContractStatus,
'@/views/manage/house/contract/contract-basic/utils/status'
import {
  getPayType,
  getacStatusType,
  getPayStatus,
  getEntryType
} from '../utils/status'
// import moment from 'moment'

export default {
  data() {
    return {
      //  今天
      tableColumnToday: [
        {
          label: '登记时间',
          prop: 'createTime',
          render: (h, scope) => {
            return <div>{scope.row.createTime}</div>
          }
        },
        {
          label: '企业名称',
          prop: 'entName'
        },

        {
          label: '所属园区',
          prop: 'parkName'
        },
        {
          label: '到账金额',
          prop: 'acAmount'
        },
        {
          label: '交易方式',
          render: (h, scope) => {
            return <el-link>{getPayType(scope.row.type)}</el-link>
          }
        },
        {
          label: '确认状态',
          prop: 'acStatus	',
          render: (h, scope) => {
            return <el-link>{getacStatusType(scope.row.acStatus)}</el-link>
          }
        },
        {
          label: '到账状态',
          prop: 'payStatus',
          render: (h, scope) => {
            return <el-link>{getPayStatus(scope.row.payStatus)}</el-link>
          }
        },
        {
          label: '登记类型',
          prop: 'entryType',
          render: (h, scope) => {
            return <el-link>{getEntryType(scope.row.entryType)}</el-link>
          }
        },
        {
          label: '详情',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goTransactionDetails(scope.row)
                }}
              >
                详情
              </el-link>
            )
          }
        }
      ],
      //  昨天
      tableColumnFirmYesterday: [
        {
          label: '登记时间',
          prop: 'createTime'
        },
        {
          label: '企业名称',
          prop: 'entName'
          // render: (h, scope) => {
          //   return (
          //     <el-link
          //       type="primary"
          //       onClick={() => {
          //         this.goContractDetails(scope.row)
          //       }}
          //     >
          //       {scope.row.contractNo}
          //     </el-link>
          //   )
          // }
        },

        {
          label: '所属园区',
          prop: 'parkName'
        },
        {
          label: '到账金额',
          prop: 'acAmount'
        },
        {
          label: '交易方式',
          render: (h, scope) => {
            return <el-link>{getPayType(scope.row.type)}</el-link>
          }
        },
        {
          label: '确认状态',
          prop: 'acStatus	',
          render: (h, scope) => {
            return <el-link>{getacStatusType(scope.row.acStatus)}</el-link>
          }
        },
        {
          label: '到账状态',
          prop: 'payStatus',
          render: (h, scope) => {
            return <el-link>{getPayStatus(scope.row.payStatus)}</el-link>
          }
        },
        {
          label: '登记类型',
          prop: 'entryType',
          render: (h, scope) => {
            return <el-link>{getEntryType(scope.row.entryType)}</el-link>
          }
        },
        {
          label: '详情',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goTransactionDetails(scope.row)
                }}
              >
                详情
              </el-link>
            )
          }
        }
      ],
      tableColumnFirmHistory: [
        {
          label: '登记时间',
          prop: 'createTime'
        },
        {
          label: '企业名称',
          prop: 'entName'
        },

        {
          label: '所属园区',
          prop: 'parkName'
        },
        {
          label: '到账金额',
          prop: 'acAmount'
        },
        {
          label: '交易方式',
          render: (h, scope) => {
            return <el-link>{getPayType(scope.row.type)}</el-link>
          }
        },
        {
          label: '确认状态',
          prop: 'acStatus	',
          render: (h, scope) => {
            return <el-link>{getacStatusType(scope.row.acStatus)}</el-link>
          }
        },
        {
          label: '到账状态',
          prop: 'payStatus',
          render: (h, scope) => {
            return <el-link>{getPayStatus(scope.row.payStatus)}</el-link>
          }
        },
        {
          label: '登记类型',
          prop: 'entryType',
          render: (h, scope) => {
            return <el-link>{getEntryType(scope.row.entryType)}</el-link>
          }
        },
        {
          label: '详情',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goTransactionDetails(scope.row)
                }}
              >
                详情
              </el-link>
            )
          }
        }
      ]
    }
  }
}
