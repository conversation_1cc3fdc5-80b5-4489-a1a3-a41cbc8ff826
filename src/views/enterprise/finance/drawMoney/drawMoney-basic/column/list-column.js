import { NumFormat, parseTime } from '@/utils/tools'
import { statusType } from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '提现总金额(元)',
          prop: 'applyMoney',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.applyMoney)}
              </div>
            )
          }
        },
        {
          label: '余额提现(元)',
          prop: 'walletAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.walletAmount)}
              </div>
            )
          }
        },
        {
          label: '保证金提现(元)',
          prop: 'depositAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.depositAmount)}
              </div>
            )
          }
        },
        {
          label: '申请时间',
          prop: 'applyTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.applyTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '申请状态',
          prop: 'state',
          render: (h, scope) => {
            return <div>{statusType(h, scope.row.state)}</div>
          }
        },
        {
          prop: 'operation',
          label: '操作',
          width: 80,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-15"
                  onClick={() => {
                    this.previewEvent(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.VIEW}
                >
                  {this.routeButtonsTitle.VIEW}
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
