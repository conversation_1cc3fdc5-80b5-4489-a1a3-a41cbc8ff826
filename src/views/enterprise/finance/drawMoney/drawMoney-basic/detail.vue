<template>
  <div>
    <module-header
      type="primary"
      title="提现申请详情"
      :img="require('./images/header-bg.png')"
      :imgOpacity="0.1"
    >
      <div class="flex justify-content-between">
        <div>
          <div class="p-t-32">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item
                :to="{ path: '/finance/account/withdrawalApply' }"
                >提现申请</el-breadcrumb-item
              >
              <el-breadcrumb-item>
                <span style="color: #000">提现申请详情</span>
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="flex align-items-center m-t-24 m-b-16">
            <el-tag :type="stateInfo.type">{{ stateInfo.label }}</el-tag>
            <h1 class="p-l-8 font-strong">提现申请</h1>
          </div>
          <div class="flex">
            <div>
              <span class="font-size-14" style="color: rgba(0, 0, 0, 0.4)"
                >申请时间:</span
              >
              <span class="font-size-14 m-l-8">{{
                parseTime(detailsInfo.applyTime, '{y}-{m}-{d}')
              }}</span>
            </div>
            <div class="m-l-16">
              <span class="font-size-14" style="color: rgba(0, 0, 0, 0.4)"
                >申请提现总金额:</span
              >
              <span class="font-size-14 m-l-8 color-primary"
                >{{ NumFormat(detailsInfo.applyMoney) }}元</span
              >
            </div>
            <div class="m-l-16">
              <span class="font-size-14" style="color: rgba(0, 0, 0, 0.4)"
                >联系人:</span
              >
              <span
                class="font-size-14 m-l-8"
                style="color: rgba(0, 0, 0, 0.8); font-weight: 400"
                >{{ detailsInfo.contact }}</span
              >
            </div>
            <div class="m-l-16">
              <span class="font-size-14" style="color: rgba(0, 0, 0, 0.4)"
                >联系方式:</span
              >
              <span
                class="font-size-14 m-l-8"
                style="color: rgba(0, 0, 0, 0.8); font-weight: 400"
                >{{ detailsInfo.contactPhone }}</span
              >
            </div>
          </div>
        </div>
      </div>
    </module-header>
    <div class="lateral-wrapper lateral-wrapper-content">
      <div class="m-b-24">
        <div class="m-b-12 font-size-14 info">申请提现金额</div>
        <el-table :data="tableDataApply" border>
          <el-table-column prop="payType" label="提现类型">
            <template slot-scope="{ row }">
              <span>{{ row.payType === 1 ? '租金余额' : '保证金缴存' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payableAmount" label="可提现金额(元)">
            <template slot-scope="{ row }">
              <span class="color-primary">{{
                NumFormat(row.payableAmount)
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="payAmount" label="申请提现金额(元)">
            <template slot-scope="{ row }">
              <span class="color-primary">{{ NumFormat(row.payAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="address" width="80" label="操作">
            <template slot-scope="scope">
              <el-link type="primary" @click="goDetail(scope.row)"
                >详情</el-link
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="m-b-24">
        <div class="m-b-12 font-size-14 info">收款账户信息</div>
        <el-table :data="tableDataPayouts" border>
          <el-table-column prop="entName" label="收款方名称" />
          <el-table-column prop="acBkNme" label="收款开户行" />
          <el-table-column prop="acBkNo" label="银行账号" />
        </el-table>
      </div>
      <div class="m-b-24">
        <div class="m-b-12 font-size-14 info">备注内容</div>
        <div class="remark line-height-22">{{ detailsInfo.content }}</div>
      </div>
      <div>
        <div class="m-b-12 font-size-14 info">相关附件</div>
        <files-list :onlyForView="true" :files="filesList" />
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import { NumFormat, parseTime } from '@/utils/tools'
import { getDetailInfo } from './api/index'
import FilesList from '@/components/Uploader/files.vue'
export default {
  name: 'DrawMoneyDetail',
  components: { FilesList, ModuleHeader },
  data() {
    return {
      parseTime,
      detailsInfo: {},
      NumFormat,
      tableDataPayouts: [],
      tableDataApply: [],
      id: null
    }
  },
  mounted() {
    const { id } = this.$route.query
    this.id = id
    this.getDetailInfo(id)
  },
  computed: {
    stateInfo() {
      const { state = 0 } = this.detailsInfo
      const stateInfo = {
        0: {
          type: 'warning',
          label: '审核中'
        },
        1: {
          type: 'danger',
          label: '已拒绝'
        },
        2: {
          type: 'success',
          label: '审核通过'
        },
        3: {
          type: 'info',
          label: '已撤回'
        }
      }
      return stateInfo[state]
    },
    filesList() {
      const { attachMap = {} } = this.detailsInfo
      if (attachMap.drawMoney && attachMap.drawMoney.length > 0) {
        return attachMap.drawMoney
      } else {
        return []
      }
    }
  },
  methods: {
    goDetail(row) {
      const url = {
        1: `/finance/account/purse`,
        2: `/account/depositCollection`
      }
      this.$router.push(url[row.payType])
    },
    getDetailInfo(id) {
      getDetailInfo(id).then(res => {
        if (res) {
          const { entName, acBkNo, acBkNme, feeInfoList = [] } = res || {}
          this.tableDataPayouts = [
            {
              entName,
              acBkNo,
              acBkNme
            }
          ]
          this.detailsInfo = res
          this.tableDataApply = feeInfoList
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.lateral-wrapper-content {
  background-color: #ffffff;
  padding: 24px;
}
.info {
  color: rgba(0, 0, 0, 0.4);
}

.remark {
  //文字超出换行
  word-wrap: break-word;
}
</style>
