<template>
  <div>
    <header-cmp :img="require('./images/header-bg.png')" title="保证金账户">
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          请及时关注保证金缴存基数变化，确保保证金足额缴纳
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content">
          <div class="flex account-left justify-content-around">
            <div class="xx">
              <div class="zh">
                <div
                  class="font-size-16 m-t-10 flex align-items-center justify-content-between"
                >
                  <span>缴存基数（元）</span>
                  <el-tooltip effect="dark" content="" placement="top">
                    <div slot="content" class="line-height-22">
                      按当前正在租赁的房间计算缴存基数，累加每个租赁房间面积*租金单价*租赁年数（取大值）
                    </div>
                    <el-link type="primary">查看基数标准</el-link>
                  </el-tooltip>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(headInfo.depositedTotal) | noData }}</span>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="zh">
                <div
                  class="font-size-16 m-t-10 flex align-items-center pointer"
                >
                  <span class="line"></span>
                  <span>已缴存（元）</span>
                </div>
                <div class="payname m-t-17 font-size-24">
                  <span class="font-size-18">¥</span>
                  <span>{{ NumFormat(headInfo.alreadyDeposit) | noData }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>
    <div class="lateral-wrapper">
      <basic-tab
        ref="basicTab"
        :tabs-data="tabsData"
        :current="current"
        @tabsChange="tabsChange"
        :disabled="reqLoading"
      />
      <deposit-base v-if="current === 1" />
      <paid-balance v-else />
    </div>
  </div>
</template>

<script>
import HeaderCmp from './components/headerComponent'
import { NumFormat } from '@/utils/tools'
import BasicTab from '@/components/BasicTab'
import DepositBase from './components/depositBase'
import PaidBalance from './components/paidBalance'
import { depositEntHead } from '@/views/enterprise/finance/marginAccount/marginAccount-basic/api'

export default {
  name: 'MarginAccount',
  components: { PaidBalance, DepositBase, BasicTab, HeaderCmp },
  data() {
    return {
      NumFormat,
      tabsData: [
        { label: '缴存基数', value: 1 },
        { label: '缴存余额', value: 2 }
      ],
      current: 1,
      reqLoading: false,
      headInfo: {}
    }
  },
  created() {
    this.depositEntHead()
  },
  methods: {
    depositEntHead() {
      depositEntHead().then(res => {
        this.headInfo = res || {}
      })
    },
    tabsChange(e) {
      this.current = e
    }
  }
}
</script>

<style scoped lang="scss">
.financial-content {
  padding-top: 10px;
  height: 96px;
  border-radius: 3px 3px 0 0;
  background: rgba(255, 255, 255, 0.8);
  opacity: 1;
}
.right-title {
  color: #616266;
}
.payname {
  font-weight: 700;
  @include font_color(--color-warning);
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;

  .line {
    width: 1px;
    height: 60px;
    background: #ebedf1;
    border-radius: 0 0 0 0;
    opacity: 1;
    position: absolute;
    left: -50px;
    top: 10px;
  }
  .zh {
    width: 88%;
    position: absolute;
    top: 0;
    left: 32px;

    .enter {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>
