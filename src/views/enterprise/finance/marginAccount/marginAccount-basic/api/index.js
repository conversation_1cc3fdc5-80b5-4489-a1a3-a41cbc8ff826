import request from '@/utils/request'

// 头部统计
export function depositEntHead() {
  return request({
    url: '/deposit/account/ent/ent_head',
    method: 'get'
  })
}
// 选择 - 缴存基数 - 变动依据
export function depositAccountBaseChangeSelect() {
  return request({
    url: `/deposit/account/ent/base_change_select`,
    method: 'get'
  })
}
// 选择 - 缴存基数 - tab切换
export function depositAccountBaseOptSelect() {
  return request({
    url: `/deposit/account/ent/base_opt_select`,
    method: 'get'
  })
}
// 缴存基数分页
export function depositAccountBasePage(params) {
  return request({
    url: `/deposit/account/ent/ent_page_base`,
    method: 'get',
    params
  })
}
// 选择 - 已缴存 - tab切换
export function depositAccountAlreadyChangeSelect() {
  return request({
    url: `/deposit/account/already_change_select`,
    method: 'get'
  })
}
// 选择 - 已缴存 - 业务来源
export function depositAccountAlreadyBusinessSelect() {
  return request({
    url: `/deposit/account/already_business_select`,
    method: 'get'
  })
}
// 已缴存分页
export function depositAccountEntPageAlready(params) {
  return request({
    url: `/deposit/account/ent/ent_page_already`,
    method: 'get',
    params
  })
}
