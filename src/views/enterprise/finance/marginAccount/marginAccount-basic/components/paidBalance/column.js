import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      depositedTableColumn: [
        {
          prop: 'changeTime',
          label: '交易时间',
          minWidth: '150px'
        },
        {
          prop: 'opTypeStr',
          label: '交易类型',
          minWidth: '150px',
          render: (h, scope) => {
            const type = scope.row.opType === 1 ? 'primary' : 'danger'
            return <el-tag type={type}>{scope.row.opTypeStr}</el-tag>
          }
        },
        {
          prop: 'amountChange',
          label: '交易金额(元)',
          minWidth: '150px',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.amountChange)}
              </span>
            )
          }
        },
        {
          prop: 'payer',
          label: '付款方',
          minWidth: '220px',
          showOverflowTooltip: true
        },
        // {
        //   prop: 'payer',
        //   label: '付款账号',
        //   minWidth: '220px',
        //   showOverflowTooltip: true
        // },
        {
          prop: 'afterAmount',
          label: '交易后余额(元)',
          minWidth: '150px',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.afterAmount)}
              </span>
            )
          }
        },
        {
          prop: 'businessTypeStr',
          label: '业务来源',
          minWidth: '150px'
        }
      ]
    }
  }
}
