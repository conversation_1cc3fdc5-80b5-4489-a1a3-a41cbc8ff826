<template>
  <div>
    <div class="flex justify-content-between align-items-center">
      <el-radio-group
        v-model="extralQuerys.optType"
        @change="searchTableHandle"
      >
        <el-radio-button
          v-for="item in switchTabs"
          :key="item.value"
          :label="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>
      <div class="flex align-items-center">
        <el-select
          v-model="extralQuerys.change"
          placeholder="请选择业务来源"
          clearable
          @change="searchTableHandle"
        >
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div class="flex align-items-center m-l-16">
          <span class="font-size-14 color-info m-r-8" style="flex-shrink: 0"
            >付款方</span
          >
          <el-input
            v-model="extralQuerys.payer"
            placeholder="请输入"
            clearable
            @change="searchTableHandle"
          />
        </div>
        <div class="flex align-items-center m-l-16">
          <span class="font-size-14 color-info m-r-8">交易时间</span>
          <el-date-picker
            v-model="variableTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="dateTimeChange"
          >
          </el-date-picker>
        </div>
      </div>
    </div>
    <drive-table
      class="m-t-16"
      ref="drive-table"
      :columns="depositedTableColumn"
      :extral-querys="extralQuerys"
      :api-fn="depositAccountEntPageAlready"
    />
  </div>
</template>

<script>
import ColumnMixin from './column'
import {
  depositAccountAlreadyBusinessSelect,
  depositAccountAlreadyChangeSelect,
  depositAccountEntPageAlready
} from '../../api'

export default {
  name: 'Deposited',
  mixins: [ColumnMixin],
  data() {
    return {
      depositAccountEntPageAlready,
      extralQuerys: {
        optType: -1,
        change: '',
        payer: ''
      },
      variableTime: [],
      switchTabs: [],
      sourceOptions: []
    }
  },
  created() {
    this.depositAccountAlreadyChangeSelect()
    this.depositAccountAlreadyBusinessSelect()
  },
  methods: {
    dateTimeChange(e) {
      const [changeSTime = '', changeETime = ''] = e || []
      this.extralQuerys.changeSTime = changeSTime
      this.extralQuerys.changeETime = changeETime
      this.searchTableHandle()
    },
    searchTableHandle() {
      this.$refs['drive-table'].resetPageNoRefreshTable()
    },
    depositAccountAlreadyBusinessSelect() {
      depositAccountAlreadyBusinessSelect().then(res => {
        this.sourceOptions = res.map(item => {
          return {
            label: item.name,
            value: item.value
          }
        })
      })
    },
    depositAccountAlreadyChangeSelect() {
      depositAccountAlreadyChangeSelect().then(res => {
        this.switchTabs = res.map(item => {
          return {
            label: item.name,
            value: item.value
          }
        })
      })
    }
  }
}
</script>

<style scoped></style>
