import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'changeTime',
          label: '变动时间'
        },
        {
          prop: 'opTypeStr',
          label: '状态',
          render: (h, scope) => {
            const type = scope.row.opType === 1 ? 'primary' : 'danger'
            return <el-tag type={type}>{scope.row.opTypeStr}</el-tag>
          }
        },
        {
          prop: 'amountChange',
          label: '变动基数(元)',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.amountChange)}
              </span>
            )
          }
        },
        {
          prop: 'businessTypeStr',
          label: '变动依据'
        },
        {
          prop: 'afterAmount',
          label: '缴存基数(元)',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.afterAmount)}
              </span>
            )
          }
        }
      ]
    }
  }
}
