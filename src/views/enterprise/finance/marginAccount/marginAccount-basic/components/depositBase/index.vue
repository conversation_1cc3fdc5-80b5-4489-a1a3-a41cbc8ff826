<template>
  <div>
    <div class="flex justify-content-between align-items-center">
      <el-radio-group
        v-model="extralQuerys.optType"
        @change="searchTableHandle"
      >
        <el-radio-button
          v-for="item in switchTabs"
          :key="item.value"
          :label="item.value"
          >{{ item.label }}</el-radio-button
        >
      </el-radio-group>
      <div class="flex align-items-center">
        <el-select
          v-model="extralQuerys.change"
          placeholder="请选择变动依据"
          clearable
          @change="searchTableHandle"
        >
          <el-option
            v-for="item in basisOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div class="flex align-items-center m-l-16">
          <span class="font-size-14 color-info m-r-8">变动时间</span>
          <el-date-picker
            v-model="variableTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="dateTimeChange"
          >
          </el-date-picker>
        </div>
      </div>
    </div>
    <drive-table
      class="m-t-16"
      ref="drive-table"
      :columns="tableColumn"
      :extral-querys="extralQuerys"
      :api-fn="depositAccountBasePage"
    />
  </div>
</template>

<script>
import ColumnMixin from './column'
import {
  depositAccountBaseChangeSelect,
  depositAccountBaseOptSelect,
  depositAccountBasePage
} from '../../api'

export default {
  name: 'DepositBase',
  mixins: [ColumnMixin],
  data() {
    return {
      depositAccountBasePage,
      variableTime: [],
      extralQuerys: {
        optType: -1,
        change: ''
      },
      switchTabs: [],
      basisOptions: []
    }
  },
  created() {
    this.depositAccountBaseOptSelect()
    this.depositAccountBaseChangeSelect()
  },
  methods: {
    depositAccountBaseChangeSelect() {
      depositAccountBaseChangeSelect().then(res => {
        this.basisOptions = res.map(item => {
          return {
            label: item.name,
            value: item.value
          }
        })
      })
    },
    dateTimeChange(e) {
      const [changeSTime = '', changeETime = ''] = e || []
      this.extralQuerys.changeSTime = changeSTime
      this.extralQuerys.changeETime = changeETime
      this.searchTableHandle()
    },
    searchTableHandle() {
      this.$refs['drive-table'].resetPageNoRefreshTable()
    },
    depositAccountBaseOptSelect() {
      depositAccountBaseOptSelect().then(res => {
        this.switchTabs = res.map(item => {
          return {
            label: item.name,
            value: item.value
          }
        })
      })
    }
  }
}
</script>

<style scoped></style>
