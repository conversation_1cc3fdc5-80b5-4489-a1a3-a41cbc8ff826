<template>
  <div class="">
    <el-dialog
      v-drag="drag"
      :class="drag ? 'drag-dialog' : ''"
      :title="title"
      :visible="visible"
      :width="width"
      :modal-append-to-body="false"
      :append-to-body="appendToBody"
      :before-close="handleClose"
      :lock-scroll="!drag"
      :modal="!drag"
      v-bind="$attrs"
      :close-on-click-modal="closeModal"
      :show-close="showClose"
      :close-on-press-escape="showClose"
    >
      <slot name="title" slot="title" />
      <el-scrollbar style="height: inherit">
        <div class="slot-content" :style="!noPadding && 'padding: 24px'">
          <slot />
        </div>
      </el-scrollbar>
      <span v-if="haveOperation" slot="footer" class="dialog-footer">
        <slot name="footer" />
        <el-button @click="closeDialog">取 消</el-button>
        <el-button v-if="havaConfirm" type="primary" @click="confirmDialog">
          <span>确 定</span>
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'BasicDialog',
  inheritAttrs: false,
  props: {
    title: {
      type: String,
      // required: true,
      default: '弹窗'
    },
    width: {
      type: String,
      default: '30%'
    },
    visible: {
      type: Boolean,
      default: false
    },
    haveOperation: {
      type: Boolean,
      default: true
    },
    drag: {
      type: Boolean,
      default: false
    },
    closeModal: {
      type: Boolean,
      default: false
    },
    noPadding: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: true
    },
    havaConfirm: {
      type: Boolean,
      default: true
    },
    appendToBody: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleClose(done) {
      done()
      this.closeDialog()
    },
    closeDialog() {
      this.$emit('update:visible', false)
    },
    confirmDialog() {
      this.$emit('confirmDialog')
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog .el-dialog__body {
  max-height: 85vh;
}

.el-scrollbar {
  width: 100%;
}

:deep(.el-dialog__header) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

:deep(.scrollbar-wrapper) {
  margin-bottom: 0 !important;
}

:deep(.el-dialog__headerbtn) {
  top: 15px;
}
::v-deep {
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
      margin-bottom: 0 !important;
    }
    .is-horizontal {
      display: none;
    }
  }
}
</style>
