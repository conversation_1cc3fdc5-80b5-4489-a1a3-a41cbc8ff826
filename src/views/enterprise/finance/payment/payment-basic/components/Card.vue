<template>
  <div class="card">
    <div @click="toDetail">
      <div class="card-wrapper color-">
        <div class="banner">
          <p class="day font-size-36 line-height-44">
            <img src="../images/money.png" alt="" />
          </p>
          <p class="date font-size-16 line-height-24">
            {{ list.payTime | noData }}
          </p>
        </div>
        <div class="content">
          <h3 class="line-1 font-size-16 line-height-24 m-b-8">
            {{ list.billNo | noData }}
          </h3>
          <div class="icon-list m-b-8">
            <icon-info
              icon-type="primary"
              icon-class="calendar"
              :text="'账单周期：' + list.rcvAmtSdt + '~' + list.rcvAmtEdt"
            />
          </div>
          <div class="m-b-16 flex align-items-center justify-content-between">
            <div class="flex">
              <div class="font-size-14 line-height-22 color-text-secondary">
                账单金额(元)：
              </div>
              <div class="m-l-8 font-size-14 line-height-22 color-warning">
                {{ NumFormat(list.amount) }}
              </div>
            </div>
            <div class="flex">
              <div class="font-size-14 line-height-22 color-text-secondary">
                已核销金额(元)：
              </div>
              <div class="m-l-8 font-size-14 line-height-22 color-warning">
                {{ NumFormat(list.paidAmount) }}
              </div>
            </div>
          </div>
          <div
            class="tag-button tag-button-bg flex justify-content-between align-items-center p-r-8"
          >
            <tag-button
              slot="reference"
              type="primary"
              :tag="'应缴日期：' + list.payTime"
              hide-button
            />
            <span
              class="font-size-14 color-danger"
              v-if="list.overdueStatus !== 1"
              >{{ list.overdueStatusStr }}</span
            >
          </div>
        </div>
      </div>
      <div class="status">
        <angle-status type="primary" :text="list.typeStr" />
      </div>
    </div>
  </div>
</template>

<script>
import IconInfo from '@/components/IconInfo'
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'
import { NumFormat } from '@/utils/tools'
import ColumnMixins from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/column/column'
import formConfigureing from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/descriptor'
export default {
  name: 'ActivityCard',
  mixins: [ColumnMixins, formConfigureing],
  components: {
    IconInfo,
    AngleStatus,
    TagButton
  },
  props: {
    list: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      NumFormat
    }
  },
  methods: {
    toDetail() {
      this.$router.push({
        path: '/finance/fees/payment/detail',
        query: {
          id: this.list.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tag-button-bg {
  background-color: #fdf2ea;
}
.card {
  padding-left: 4px;
  position: relative;
  cursor: pointer;
  .card-wrapper {
    padding: 8px 16px 8px 8px;
    display: flex;
    align-items: center;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    border-radius: 6px;
    .banner {
      flex: 0 0 120px;
      height: 150px;
      border-radius: 3px;
      overflow: hidden;
      & image {
        height: 54px;
        width: 51px;
      }
      p {
        text-align: center;
        @include font_color_mix(--color-warning, #ffffff, 30%);
        &.day {
          padding-top: 48px;
          padding-bottom: 14px;
          @include background_color_mix(--color-warning, #ffffff, 90%);
        }
        &.date {
          padding: 2px 0;
          @include background_color_mix(--color-warning, #ffffff, 80%);
        }
      }
    }
    .content {
      flex: 1;
      overflow: hidden;
      padding-left: 16px;
    }
  }
  .status {
    position: absolute;
    left: 0;
    top: 16px;
  }
}
</style>
