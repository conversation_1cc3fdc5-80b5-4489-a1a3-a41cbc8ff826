/*
 * @Author: cc <EMAIL>
 * @Date: 2023-02-21 11:09:05
 * @LastEditors: cc <EMAIL>
 * @LastEditTime: 2023-02-24 16:32:56
 * @FilePath: \parallel-cloud-manage\src\views\enterprise\finance\payment\payment-basic\api\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
// 获取活动信息
export function getPolicyDetail(id) {
  return request({
    url: `/hatch/ent/policy/get?id=${id}`,
    method: 'get'
  })
}

// 获取企业端交费列表页面
export function getEntBillTotalList(params) {
  return request({
    url: `/pay/bill_total/ent_bill_page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取余额类型
export function getBalanceType() {
  return request({
    url: `/bill/purse/ent/detail`,
    method: 'get'
  })
}

//交行-账单核销获取企业账户信息
export function getEntAcc(id) {
  return request({
    url: `/pay/account/get_ent_acc?entId=${id}`,
    method: 'get'
  })
}
//交行-获取企业账单核销账务信息
export function getEntHisAccountingPage(params) {
  return request({
    url: `/pay/account/get_ent_his_accounting_page`,
    method: 'get',
    params
  })
}
// 账单核销接口 (银行)
export function collectBillBank(data) {
  return request({
    url: `/pay/bill_total/collect_bill_bank`,
    method: 'post',
    data
  })
}

// 获取账单详情
export function getBillDetail(id) {
  return request({
    url: `/pay/bill_total/detail?id=${id}`,
    method: 'get'
  })
}

// 账单核销接口
export function collectBill(data) {
  return request({
    url: `/pay/bill_total/collect_bill_purse`,
    method: 'post',
    data
  })
}

// 获取缴费通知单数据
export function getBillAdvice(id) {
  return request({
    url: `/pay/bill_total/bill_advice?id=${id}`,
    method: 'get'
  })
}
// 获取账单详情开票记录
export function getBillInvoice(params) {
  return request({
    url: `/inv/zacg/apply_record/bill_invoice`,
    method: 'get',
    params
  })
}

// 获取缴费通知单详情
export function getPaymentMemoDetail(id) {
  return request({
    url: `/pay/bill_total/payment_memo_detail/${id}`,
    method: 'get'
  })
}

// 导出pdf缴费通知单 type: 0包含保证金、1不包含
export function paymentMemoExport(id, type) {
  return `${process.env.VUE_APP_URL_PREFIX}/pay/bill_total/payment_memo/${id}/${type}`
}

// 导出word缴费通知单 type: 0包含保证金、1不包含
export function paymentMemoWordExport(id, type) {
  return `${process.env.VUE_APP_URL_PREFIX}/pay/bill_total/payment_memo_word/${id}/${type}`
}
