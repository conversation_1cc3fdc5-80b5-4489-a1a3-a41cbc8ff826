<!-- 合同应收 -->
<template>
  <div class="lateral-wrapper">
    <basic-card v-if="billInfo">
      <div>
        <el-row>
          <el-col :span="8">
            <div class="font-size-18 font-strong">
              {{ billInfo.enterpriseName | noData }}
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="font-size-14 m-b-16 m-t-16">主账单</div>
      <div class="flex m-b-24">
        <div v-if="!isHydropower" class="m-r-16">
          <span class="font-size-14">合同编号</span>
          <span class="color-warning m-l-8 font-size-14">
            {{ billInfo.contractNo | noData }}</span
          >
        </div>
        <div>
          <span class="font-size-14">账单编号</span>
          <span class="color-warning m-l-8 font-size-14">
            {{ billInfo.billNo | noData }}</span
          >
        </div>
      </div>
      <drive-table
        ref="drive-table"
        max-height="300"
        :columns="tableColumnToday"
        :table-data="[billInfo]"
      >
      </drive-table>
    </basic-card>
    <basic-card :is-title="false" class="m-b-10 m-t-10 p-t-24">
      <div class="font-size-14 m-b-16">子账单</div>
      <div class="flex m-b-24">
        <div v-if="!isHydropower" class="m-r-16">
          <span class="font-size-14">入驻房源</span>
          <span class="color-warning m-l-8 font-size-14"
            >{{ billInfo.roomInfo | noData }} |
            {{ billInfo.contractArea }}㎡</span
          >
        </div>
        <div class="flex">
          <div class="font-size-14">实收进度</div>
          <div class="color-warning m-l-8 font-size-14">
            <el-progress
              style="width: 180px"
              :text-inside="true"
              :stroke-width="14"
              :percentage="Number(billInfo.process) || 0"
              status="success"
            ></el-progress>
          </div>
        </div>
      </div>
      <drive-table
        ref="drive-table"
        max-height="300"
        :columns="tableColumnFirmYesterday"
        :table-data="billInfo.billDetailList"
      >
      </drive-table>
    </basic-card>
    <basic-card :is-title="false" class="p-t-24">
      <div class="font-size-14 m-b-16">修改记录</div>
      <drive-table
        ref="drive-table"
        max-height="400"
        :columns="tableColumnEditHistory"
        :table-data="billInfo.billChangeLogList"
      >
      </drive-table>
    </basic-card>
    <basic-card :is-title="false" class="p-t-24">
      <div class="font-size-14 m-b-16">核销记录</div>
      <drive-table
        ref="drive-table"
        max-height="400"
        :columns="tableColumnWriteOffRecord"
        :table-data="billInfo.collectList"
      >
      </drive-table>
    </basic-card>
    <basic-card :is-title="false" class="p-t-24">
      <div class="font-size-14 m-b-16">退款记录</div>
      <drive-table
        ref="drive-table"
        max-height="400"
        :columns="tableColumnRefund"
        :table-data="billInfo.refundList"
      >
      </drive-table>
    </basic-card>
    <basic-card :is-title="false" class="p-t-24">
      <div class="font-size-14 m-b-16">开票记录</div>
      <drive-table
        ref="drive-table"
        max-height="400"
        :columns="tableColumnBillingInvoice"
        :table-data="billingInvoice"
      >
      </drive-table>
    </basic-card>
  </div>
</template>

<script>
import formConfigureing from './descriptor'
import { NumFormat } from '@/utils/tools'
import ColumnMixins from './column/column'
import { getBillDetail, getBillAdvice, getBillInvoice } from './api/index'
import { getFeePriceTitles } from '@/views/manage/financial/financial-basic/api'
import { mapGetters } from 'vuex'
export default {
  name: 'detail',
  mixins: [ColumnMixins, formConfigureing],
  data() {
    return {
      rowInfoId: 0,
      sums: 0,
      balanceType: [],
      NumFormat,
      dayList: [],
      billInfo: {},
      billDetailList: [],
      flushAcc: {}, // 账户信息
      billAdvice: {}, // 缴费通知单
      //跳转对象
      jumpData: {
        path: row => {
          const path = [
            '/rentOut/replace/replaceDetail',
            '/rentOut/leavePark/leaveParkDetail',
            '/decorate/index/decorationDetails'
          ]
          if (row.businessType < 4) {
            if (row.businessId) {
              this.$router.push({
                path: path[row.businessType - 1],
                query: {
                  id: row.businessId,
                  orderId: row.businessOrderId
                }
              })
            }
          } else if (row.businessType === 5 || row.businessType === 6) {
            this.$router.push({
              path: '/rentalHousing/applyHouse/applyQuitDetail',
              query: {
                id: row.businessId,
                orderId: row.businessOrderId
              }
            })
          } else {
            const path = row.bsType
              ? '/rentalHousing/contractSpecial/contractDetails'
              : '/contract/index/contractDetails'
            if (row.businessId) {
              this.$router.push({
                path,
                query: {
                  id: row.businessId,
                  orderId: row.businessOrderId,
                  current: 0,
                  [row.bsType ? 'blType' : '']: row.blType
                }
              })
            }
          }
        }
      },
      billingInvoice: []
    }
  },
  computed: {
    ...mapGetters(['tenantName']),
    // 水电
    isHydropower() {
      const { type } = this.$route.query
      return type === '3' || type === '4'
    }
  },
  mounted() {
    const { type, id } = this.$route.query
    this.rowInfoId = id
    const titleType = type === '1' ? '4' : '8'
    if (type !== '3' && type !== '4') {
      this.getFeePriceTitles(titleType)
    }
  },
  created() {
    const { id } = this.$route.query
    id && this.getBillDetail(id)
    id && this.getBillInvoice(id)
  },
  methods: {
    viewHandle(row) {
      this.$router.push({
        path: '/drawBill/drawBillList/drawBillDetails',
        query: {
          id: row.applyId
        }
      })
    },
    getBillInvoice(id) {
      getBillInvoice({ totalId: id }).then(res => {
        this.billingInvoice = res || []
      })
    },
    getFlushed() {
      this.$message.success('刷新成功')
    },
    goTurnover(row) {
      this.$router.push({
        path: '/account/entAccountDetail',
        query: {
          id: row.purseId
        }
      })
    },
    // 保留两位小数
    formatNum(val) {
      let filtered = val.amount.replace(/[^\d.]/g, '')
      if (filtered.length > 12) {
        filtered = filtered.slice(0, 12)
      }
      this.$set(val, 'amount', filtered)
    },
    // 获取表头
    getFeePriceTitles(type) {
      getFeePriceTitles(type).then(res => {
        const dynamicHeader = res.map(item => {
          return {
            label: item.name,
            prop: item.code,
            minWidth: '120px',
            render: (h, scope) => {
              return (
                <div class="color-warning">
                  {NumFormat(scope.row[item.code])}
                </div>
              )
            }
          }
        })
        this.tableColumnToday.splice(3, 0, ...dynamicHeader)
      })
    },
    goDetail(row) {
      let { entId, orderId } = row
      this.$router.push({
        path: '/business/enterpriseDetails',
        query: {
          id: entId,
          orderId
        }
      })
    },
    getBillDetail(id) {
      getBillDetail(id).then(res => {
        this.billInfo = res
        this.billDetailList = JSON.parse(JSON.stringify(res.billDetailList))
        // this.getFlushAcc()

        if (res.type === 9) {
          const tableColumnTodayHiddenKey = ['overdueStatus', 'overdueDays']
          tableColumnTodayHiddenKey.forEach(item => {
            const index = this.tableColumnToday.findIndex(
              row => row.prop === item
            )
            this.tableColumnToday[index].hidden = true
          })
          const tableColumnFirmYesterdayKey = [
            'detailOverdueStatus',
            'detailOverdueDays'
          ]
          tableColumnFirmYesterdayKey.forEach(item => {
            const index = this.tableColumnFirmYesterday.findIndex(
              row => row.prop === item
            )
            this.tableColumnFirmYesterday[index].hidden = true
          })
        }
        // this.getBillAdvice(id)
      })
    },

    // 获取缴费通知单
    getBillAdvice(id) {
      getBillAdvice(id).then(res => {
        this.billAdvice = res
      })
    },
    // 查看附件
    seeAttach(e) {
      window.open(e.informationAttach[0].path)
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog .el-dialog__body {
  max-height: 0 !important;
}

:deep(.el-dialog__header) {
  height: 100px !important;
}

:deep(.el-form-item--small.el-form-item) {
  margin-bottom: 0 !important;
}

.info {
  color: #999;
}

.label-top {
  font-size: 14px;
  font-weight: 350;
  color: rgba(0, 0, 0, 0.9);
  line-height: 22px;
}
.color-basic {
  color: rgba(0, 0, 0, 0.9);
}

.value-top {
  font-size: 14px;
  font-weight: 350;
  color: #ed7b2f;
  line-height: 22px;
}

.girds {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 20px;
}

.right {
  text-align: right;
  width: 90px;
  display: inline-block;
}

.mony {
  width: 276px;
  display: inline-block;
}
:deep(.w-date) {
  width: 140px !important;
}
:deep(.w-date .el-input__inner) {
  padding-right: 10px !important;
}

.black {
  color: #1a1a1a;
}
</style>
