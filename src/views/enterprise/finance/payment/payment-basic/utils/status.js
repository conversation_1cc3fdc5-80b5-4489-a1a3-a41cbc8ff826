export function getBillStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag type="primary" label="执行中"></basic-tag>
    case 2:
      return <basic-tag type="success" label="已完成"></basic-tag>
    case 3:
      return <basic-tag type="danger" label="已终止"></basic-tag>
    default:
      return '-'
  }
}

export function getBillStatusLabelForTag(val) {
  switch (val) {
    case 1:
      return { type: 'warning', label: '待核销' }
    case 2:
      return { type: 'warning', label: '部分核销' }
    case 3:
      return { type: 'success', label: '已核销' }
    case 4:
      return { type: 'danger', label: '已作废' }
    default:
      return { type: 'primary', label: '-' }
  }
}

export function getTransactionStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="danger" label="处理中"></basic-tag>
    case 1:
      return <basic-tag isDot type="success" label="成功"></basic-tag>
    case 2:
      return <basic-tag isDot type="warning" label="失败"></basic-tag>
    default:
      return '-'
  }
}

export function getOverdueStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag type="success" label="未逾期"></basic-tag>
    case 2:
      return <basic-tag type="warning" label="短时逾期"></basic-tag>
    case 3:
      return <basic-tag type="danger" label="长时逾期"></basic-tag>
    default:
      return '-'
  }
}

// 支付方式
export function getPayType(h, val) {
  switch (val) {
    case 1:
      return <el-tag type="primary">账户支付</el-tag>
    case 2:
      return <el-tag type="primary">钱包支付</el-tag>
    default:
      return '-'
  }
}
