import { NumFormat } from '@/utils/tools'
import { getBillStatus, getOverdueStatus, getPayType } from '../utils/status'
import {
  getBillType,
  tradeType
} from '@/views/manage/financial/financial-basic/status'
import { getVerificationType } from '@/views/manage/financial/financial-jtyh/payment/accountsReceivable-basic/utils/status'
export default {
  data() {
    return {
      // 新版财务数据表格
      tableColumn: [
        {
          label: '企业名称',
          width: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                <div
                  onClick={() => {
                    this.goDetail(scope.row)
                  }}
                  class={'line-1'}
                  class={`${
                    scope.row?.enterStatus === 1 || scope.row?.enterStatus === 2
                      ? 'color-primary pointer'
                      : ''
                  }`}
                >
                  {scope.row.enterpriseName}
                  {scope.row.enterpriseNameTxt ? (
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                      placement="top"
                    >
                      <svg-icon class="m-l-8" icon-class="link-m" />
                    </el-tooltip>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            )
          }
        },
        {
          label: '账期',
          prop: 'contractNo',
          width: 180,
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          label: '费用类型',
          prop: 'feeTypeStr'
        },
        {
          label: '逾期状态',
          prop: 'overdueStatus',
          render: (h, scope) => {
            return <div>{getOverdueStatus(h, scope.row.overdueStatus)}</div>
          }
        },
        {
          label: '逾期天数',
          prop: 'overdueDays',
          render: (h, scope) => {
            return <div>{scope.row.overdueDays || '-'}</div>
          }
        },
        {
          label: '应收金额(元)',
          prop: 'payAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payAmount)}
              </div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'collectAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.collectAmount)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'billStatus',
          render: (h, scope) => {
            return (
              <div>
                {getVerificationType(
                  h,
                  scope.row.billStatus,
                  scope.row.billStatusStr
                )}
              </div>
            )
          }
        },
        {
          label: '实收进度',
          prop: 'payProcess',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-progress
                  text-inside={true}
                  stroke-width={16}
                  percentage={Number(scope.row.payProcess) || 0}
                  status="success"
                ></el-progress>
              </div>
            )
          }
        },
        {
          label: '可开票金额(元)',
          prop: 'readyInvoiceAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.readyInvoiceAmount)}
              </div>
            )
          }
        },
        {
          label: '已开票金额(元)',
          prop: 'invoicedAmount',
          minWidth: 180,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.invoicedAmount)}
              </div>
            )
          }
        },
        {
          label: '开票状态',
          prop: 'openStatusStr',
          width: 120
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div class={'flex align-items-center'}>
                <el-link
                  type="primary"
                  class="m-r-24"
                  onClick={() => {
                    this.goBillDetail(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DETAIL}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
              </div>
            )
          }
        }
      ],
      // 按周期
      tableColumnBill: [
        {
          label: '企业名称',
          width: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                <div
                  type="primary"
                  onClick={() => {
                    this.goDetail(scope.row)
                  }}
                  class={'line-1'}
                  class={`${
                    scope.row?.enterStatus === 1 || scope.row?.enterStatus === 2
                      ? 'color-primary pointer'
                      : ''
                  }`}
                >
                  {scope.row.enterpriseName}
                  {scope.row.enterpriseNameTxt ? (
                    <el-tooltip
                      class="item"
                      effect="dark"
                      content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                      placement="top"
                    >
                      <svg-icon class="m-l-8" icon-class="link-m" />
                    </el-tooltip>
                  ) : (
                    ''
                  )}
                </div>
              </div>
            )
          }
        },
        {
          label: '账期',
          prop: 'contractNo',
          width: 180,
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '应收金额(元)',
          prop: 'amount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'paidAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'status',
          render: (h, scope) => {
            return <div>{getBillStatus(h, scope.row.status)}</div>
          }
        },
        {
          label: '逾期状态',
          prop: 'overdueStatus',
          render: (h, scope) => {
            return <div>{getOverdueStatus(h, scope.row.overdueStatus)}</div>
          }
        },
        {
          label: '实收进度',
          prop: 'process',
          width: 120,
          render: (h, scope) => {
            return (
              <div>
                <el-progress
                  text-inside={true}
                  stroke-width={16}
                  percentage={Number(scope.row.process) || 0}
                  status="success"
                ></el-progress>
              </div>
            )
          }
        },
        {
          label: '可开票金额(元)',
          prop: 'readyInvoiceAmount',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.readyInvoiceAmount)}
              </div>
            )
          }
        },
        {
          label: '已开票金额(元)',
          prop: 'invoicedAmount',
          width: 180,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.invoicedAmount)}
              </div>
            )
          }
        },
        {
          label: '开票状态',
          prop: 'openStatusStr',
          width: 120
        },
        {
          prop: 'operation',
          label: '操作',
          width: 150,
          fixed: 'right',
          render: (h, scope) => {
            return (
              <div class={'flex align-items-center'}>
                <el-link
                  type="primary"
                  class="m-r-24"
                  onClick={() => {
                    this.goBillDetail(scope.row)
                  }}
                  v-permission={this.routeButtonsPermission.DETAIL}
                >
                  {this.routeButtonsTitle.DETAIL}
                </el-link>
              </div>
            )
          }
        }
      ],
      // 按企业
      tableColumnFirm: [
        {
          label: '企业名称',
          width: 300,
          prop: 'enterpriseName',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.enterpriseName ? (
                  <div
                    onClick={() => {
                      this.goDetail(scope.row)
                    }}
                    class={'line-1'}
                    class={`${
                      scope.row?.enterStatus === 1 ||
                      scope.row?.enterStatus === 2
                        ? 'color-primary pointer'
                        : ''
                    }`}
                  >
                    {scope.row.enterpriseName}
                    {scope.row.enterpriseNameTxt ? (
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content={'实际入驻: ' + scope.row.enterpriseNameTxt}
                        placement="top"
                      >
                        <svg-icon class="m-l-8" icon-class="link-m" />
                      </el-tooltip>
                    ) : (
                      ''
                    )}
                  </div>
                ) : (
                  '-'
                )}
              </div>
            )
          }
        },
        {
          label: '合同总数',
          prop: 'recordCount'
        },
        {
          label: '账单总数',
          prop: 'count'
        },
        {
          label: '应收金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.amount)}</div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'paidAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.paidAmount)}
              </div>
            )
          }
        },
        {
          label: '实收进度',
          prop: 'process',
          width: 200,
          render: (h, scope) => {
            return (
              <div>
                <el-progress
                  text-inside={true}
                  stroke-width={16}
                  percentage={Number(scope.row.process) || 0}
                  status="success"
                ></el-progress>
              </div>
            )
          }
        }
      ],
      tableColumnToday: [
        {
          label: '应付款企业/个人',
          width: 300,
          prop: 'enterpriseName'
        },
        {
          label: '账期',
          width: 200,
          prop: 'contractNo',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.rcvAmtSdt} - {scope.row.rcvAmtEdt}
              </div>
            )
          }
        },
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '应收金额(元)',
          prop: 'amountReceivable',
          width: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.amountReceivable)}
              </div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'amountReceived',
          width: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.amountReceived)}
              </div>
            )
          }
        },
        {
          label: '退款金额(元)',
          prop: 'refundAmount',
          width: 150,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.refundAmount)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'collectStatus',
          render: (h, scope) => {
            return <div>{getVerificationType(h, scope.row.collectStatus)}</div>
          }
        },
        {
          label: '逾期状态',
          hidden: false,
          prop: 'overdueStatus',
          render: (h, scope) => {
            return <div>{getOverdueStatus(h, scope.row.overdueStatus)}</div>
          }
        },
        {
          label: '逾期天数',
          prop: 'overdueDays',
          hidden: false
        },
        {
          label: '账单状态',
          prop: 'status',
          render: (h, scope) => {
            return <div>{getBillStatus(h, scope.row.status)}</div>
          }
        }
      ],
      //  账单明细
      tableColumnFirmYesterday: [
        {
          label: '账单编号',
          prop: 'billCode'
        },
        {
          label: '费用类型',
          prop: 'feeType',
          render: (h, scope) => {
            return (
              <div>
                {getBillType(h, scope.row.feeType, scope.row.feeTypeStr)}
              </div>
            )
          }
        },
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '逾期状态',
          prop: 'detailOverdueStatus',
          hidden: false,
          render: (h, scope) => {
            return (
              <div>{getOverdueStatus(h, scope.row.detailOverdueStatus)}</div>
            )
          }
        },
        {
          label: '逾期天数',
          prop: 'detailOverdueDays',
          hidden: false
        },
        {
          label: '应收金额(元)',
          prop: 'payAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payAmount) || 0}
              </div>
            )
          }
        },
        {
          label: '实收金额(元)',
          prop: 'payActualAmount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.payActualAmount) || 0}
              </div>
            )
          }
        },
        {
          label: '退款金额(元)',
          prop: 'sendBackMoney',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.sendBackMoney)}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'billStatus',
          render: (h, scope) => {
            return <div>{getVerificationType(h, scope.row.billStatus)}</div>
          }
        }
      ],
      //核销记录
      tableColumnWriteOffRecord: [
        {
          label: '费用名称',
          prop: 'feeTypeStr',
          render: (h, scope) => {
            return <div>{scope.row.feeTypeStr || '暂无数据'}</div>
          }
        },
        {
          label: '核销金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.amount) || 0}
              </div>
            )
          }
        },
        {
          label: '核销时间',
          prop: 'createTime'
        },

        {
          label: '支付方式',
          prop: 'payType',
          render: (h, scope) => {
            return <div>{getPayType(h, scope.row.payType)}</div>
          }
        },
        {
          label: '核销方',
          prop: 'submitPart',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {scope.row.submitPart === 1 ? '园区核销' : '企业核销'}
              </div>
            )
          }
        },
        {
          label: '核销状态',
          prop: 'status',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.status === 1 ? (
                  <el-tag type="success">成功</el-tag>
                ) : (
                  <el-tag type="danger">失败</el-tag>
                )}
              </div>
            )
          }
        },
        {
          label: '缴费日期',
          prop: 'transactTime'
        },
        {
          label: '操作人',
          prop: 'opUser',
          width: 200
        },
        {
          label: '备注',
          prop: 'content',
          showOverflowTooltip: true
        },
        {
          label: '相关附件',
          prop: 'attaches',
          render: (h, scope) => {
            return (
              <span
                class="color-primary"
                onClick={() => {
                  this.seeAttach(scope.row.attaches)
                }}
              >
                {Object.keys(scope.row.attaches).length > 0 ? (
                  '查看'
                ) : (
                  <span class="color-info">暂无附件</span>
                )}
              </span>
            )
          }
        }
      ],
      //退款记录
      tableColumnRefund: [
        {
          label: '费用名称',
          prop: 'feeTypeStr',
          render: (h, scope) => {
            return <div>{scope.row.feeTypeStr || '暂无数据'}</div>
          }
        },
        {
          label: '退款金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>
                {NumFormat(scope.row.amount) || 0}
              </div>
            )
          }
        },
        {
          label: '退款时间',
          prop: 'refundTime'
        },
        {
          label: '业务追溯',
          prop: 'businessTypeStr',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.businessTypeStr ? (
                  <el-link
                    type="primary"
                    onClick={() => this.toBusiness(scope.row)}
                  >
                    {scope.row.businessTypeStr}
                  </el-link>
                ) : (
                  '暂无数据'
                )}
              </div>
            )
          }
        },
        {
          label: '退款原因',
          prop: 'remark'
        },
        {
          label: '退款去向',
          prop: 'refundGoesStr'
        },
        {
          label: '退款状态',
          prop: 'refundStatus',
          render: (h, scope) => {
            return (
              <div>
                {scope.row.refundStatus === 1 ? (
                  <el-tag type="success">成功</el-tag>
                ) : (
                  <el-tag type="danger">失败</el-tag>
                )}
              </div>
            )
          }
        },
        {
          label: '操作人',
          prop: 'operator',
          width: 200
        }
      ],

      // 修改记录
      tableColumnEditHistory: [
        {
          label: '账单编号',
          prop: 'billNo'
        },
        {
          label: '修改时间',
          prop: 'changeTime'
        },
        {
          label: '业务追溯',
          prop: 'businessTypeStr'
        },
        {
          label: '修改原因',
          prop: 'changeReason'
        },
        {
          label: '修改内容',
          prop: 'changeInfo'
        },
        {
          label: '修改状态',
          prop: 'status',
          render: (h, scope) => {
            return <div>{scope.row.status === 1 ? '成功' : '失败'}</div>
          }
        },
        {
          label: '操作人',
          prop: 'editUserName'
        }
        // {
        //   label: '相关附件',
        //   prop: 'changeLogAttMap',
        //   render: (h, scope) => {
        //     return (
        //       <div
        //         class="color-primary"
        //         onClick={() => {
        //           this.seeAttach(scope.row.changeLogAttMap)
        //         }}
        //       >
        //         {Object.keys(scope.row.changeLogAttMap).length > 0 ? (
        //           '查看'
        //         ) : (
        //           <span class="color-info">暂无附件</span>
        //         )}
        //       </div>
        //     )
        //   }
        // }
      ],
      //  费用明细
      tableColumnPayDetails: [
        {
          label: '费目',
          prop: 'billName'
        },
        {
          label: '应缴金额(元)',
          prop: 'payAmount',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.payAmount) || 0}
              </div>
            )
          }
        },
        {
          label: '已缴金额(元)',
          prop: 'payActualAmount',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.payActualAmount) || 0}
              </div>
            )
          }
        },
        {
          label: '未缴金额(元)',
          prop: 'contractNo',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.payAmount - scope.row.payActualAmount) ||
                  0}
              </div>
            )
          }
        }
      ],
      //  收账账户Collection account
      tableColumnCollectionAccount: [
        {
          label: '费目',
          prop: 'feeType'
        },
        {
          label: '户名',
          prop: 'cusAcName'
        },
        {
          label: '开户行',
          prop: 'bank'
        },
        {
          label: '账户',
          prop: 'cusAc'
        }
      ],
      //  账单核销费用应收款明细
      tableColumnReceivables: [
        {
          label: '费用类型',
          prop: 'feeType',
          render: (h, scope) => {
            return (
              <div>
                {getBillType(h, scope.row.feeType, scope.row.feeTypeStr)}
              </div>
            )
          }
        },
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '应收金额(元)',
          prop: 'payAmount',
          render: (h, scope) => {
            return <div>{NumFormat(scope.row.payAmount) || 0}</div>
          }
        },
        {
          label: '实收金额(元)',
          prop: 'payActualAmount',
          render: (h, scope) => {
            return <div>{NumFormat(scope.row.payActualAmount) || 0}</div>
          }
        },
        {
          label: '待核销金额(元)',
          prop: 'writtenAmount',
          render: (h, scope) => {
            return <div>{NumFormat(scope.row.writtenAmount) || 0}</div>
          }
        }
      ],
      //  账单核销已收款明细
      tableColumnReceived: [
        {
          label: '可用余额(元)',
          prop: 'money',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.money)}</div>
            )
          }
        },
        {
          label: '最近更新时间',
          prop: 'time'
        },
        {
          label: '流水记录',
          prop: 'time',
          render: (h, scope) => {
            return (
              <el-link
                type="primary"
                onClick={() => {
                  this.goTurnover(scope.row)
                }}
              >
                查看
              </el-link>
            )
          }
        },
        {
          label: '操作',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  class="m-r-24"
                  onClick={() => {
                    this.getFlushed(scope.row)
                  }}
                >
                  刷新
                </el-link>
              </div>
            )
          }
        }
      ],
      //园区对账单明细
      tableColumnCounterpart: [
        {
          label: '对方账号',
          prop: 'counterpartName'
        },
        {
          label: '对方户名',
          prop: 'counterpartName'
        },
        {
          label: '交易时间',
          prop: 'counterpartName'
        },
        {
          label: '交易金额',
          prop: 'counterpartName'
        },
        {
          label: '交易状态',
          prop: 'counterpartName'
        }
      ],
      // 企业端对账单明细
      tableColumnCounterpartEnt: [
        {
          label: '对方账号',
          prop: 'oppAc'
        },
        {
          label: '对方户名',
          prop: 'oppAcNme'
        },
        {
          label: '交易时间',
          prop: 'txnTime'
        },
        {
          label: '交易金额',
          prop: 'txnAmt',
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.txnAmt)}</div>
            )
          }
        },
        {
          label: '交易状态',
          prop: 'txnSts',
          render: (h, scope) => {
            return <div>{tradeType(h, scope.row.txnSts)}</div>
          }
        }
      ],

      // 核销账单明细
      tableColumnBillDetails: [
        {
          label: '费型类型',
          prop: 'name'
        },
        {
          label: '缴费日期',
          prop: 'transactTime',
          render: (h, scope) => {
            return (
              <div>
                <el-date-picker
                  class="w-date"
                  disabled={scope.row.disable}
                  v-model={scope.row.transactTime}
                  type="date"
                  placeholder="请选择缴费日期"
                ></el-date-picker>
              </div>
            )
          }
        },
        {
          label: '实收金额',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div>
                <el-input
                  disabled={scope.row.disable}
                  v-model={scope.row.amount}
                  maxlength={10}
                  onInput={() => {
                    this.formatNum(scope.row)
                  }}
                ></el-input>
              </div>
            )
          }
        }
      ],
      // 开票记录
      tableColumnBillingInvoice: [
        {
          label: '申请时间',
          prop: 'applyDate'
        },
        {
          label: '申请人',
          prop: 'applyUser'
        },
        {
          label: '开票金额(元)',
          prop: 'openAmount',
          render: (h, scope) => {
            return (
              <span class={'color-warning'}>
                {NumFormat(scope.row.openAmount)}
              </span>
            )
          }
        },
        {
          label: '发票类型',
          prop: 'openTypeStr'
        },
        {
          label: '开票状态',
          prop: 'statusStr'
        },
        {
          prop: 'operation',
          align: 'center',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onClick={() => {
                    this.viewHandle(scope.row)
                  }}
                >
                  查看
                </el-link>
              </div>
            )
          }
        }
      ]
    }
  }
}
