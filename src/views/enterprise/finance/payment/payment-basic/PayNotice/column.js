import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '费目',
          prop: 'expense'
        },
        {
          label: '租赁面积(㎡)',
          prop: 'roomArea'
        },
        {
          label: '单价',
          prop: 'price',
          render: (h, scope) => {
            return <div style={'white-space: pre-wrap'}>{scope.row.price}</div>
          }
        },
        {
          label: '数量',
          prop: 'quantity'
        },
        {
          label: '金额(元)',
          prop: 'totalAmount',
          render: (h, scope) => {
            return (
              <div class="color-warning">
                {NumFormat(scope.row.totalAmount) || 0}
              </div>
            )
          }
        },
        {
          label: '期间',
          prop: 'period',
          width: 200
        }
      ]
    }
  }
}
