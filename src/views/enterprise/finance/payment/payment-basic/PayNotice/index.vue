<template>
  <dialog-cmp
    title=""
    :visible.sync="payVisible"
    width="862px"
    custom-class="pay-notice-dialog"
    :have-operation="false"
  >
    <template v-slot:title>
      <div class="flex justify-content-between align-items-center">
        <div>缴费通知单</div>
        <div class="header-right-wrapper">
          <span>编号：{{ billAdvice.payCode | noData }}</span>
        </div>
      </div>
    </template>
    <div class="pay-notice-wrapper">
      <div class="font-size-14 line-height-22">
        致{{ billAdvice.entName | noData }}：
      </div>
      <div class="pay-notice-desc m-t-16 text-indent">
        贵司于
        <span class="desc-weight">{{ billAdvice.deliveryTime | noData }}</span>
        交房入驻，租赁地址：
        <span class="desc-weight">{{ billAdvice.address | noData }}</span>
        ，租赁面积：
        <span class="desc-weight">{{ billAdvice.area | noData }}㎡</span>
        ，本期应缴费用总额：
        <span class="color-warning">￥{{ NumFormat(totalAmount) }} 元</span>
        ，缴费明细如下
      </div>
      <drive-table
        class="m-t-16"
        ref="drive-table"
        :columns="tableColumn"
        :table-data="payList"
      >
      </drive-table>
      <div class="deposit-wrapper m-t-8" v-if="billAdvice.showText">
        <el-checkbox
          class="p-t-4"
          v-model="depositChecked"
          :true-label="0"
          :false-label="1"
        ></el-checkbox>
        <div class="deposit-content">
          当前企业保证金账户余额为:
          <span class="color-warning"
            >￥{{ NumFormat(billAdvice.depositActual) }}</span
          >
          元，基于企业合同的相关信息，该企业的保证金基数为:
          <span class="color-warning"
            >￥{{ NumFormat(billAdvice.depositBase) }}</span
          >
          元，目前企业需补缴差额保证金为:
          <span class="color-warning"
            >￥{{ NumFormat(billAdvice.depositDifference) }}</span
          >
          元，是否与本次账单一起合并下发缴费通知单?
        </div>
      </div>
      <div class="pay-notice-desc m-t-16">
        请贵司将上述款项于
        <span class="desc-weight">{{ billAdvice.payment | noData }}前</span>
        对公转账我司如下账户。
      </div>
      <div class="account-wrapper">
        <div class="account-item">
          <div class="item-label w-60">户名：</div>
          <div>{{ billAdvice.username | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-60">开户行：</div>
          <div>{{ billAdvice.bank | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-60">账号：</div>
          <div>{{ billAdvice.account | noData }}</div>
        </div>
      </div>
      <div class="separation-line"></div>
      <div class="pay-notice-desc m-t-16 flex justify-content-between">
        <div>
          我司将依据贵司到账情况开具发票，请确认无误以下开票信息，如有变化请及时联系变更。感谢配合！
        </div>
        <div>发票类型：{{ billAdvice.invoiceType | noData }}</div>
      </div>
      <div class="account-wrapper">
        <div class="account-item">
          <div class="item-label w-100">公司名称：</div>
          <div>{{ billAdvice.saleName | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-100">纳税人识别号：</div>
          <div>{{ billAdvice.taxpayerNumber | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-100">公司地址：</div>
          <div>{{ billAdvice.saleAddress | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-100">电话：</div>
          <div>{{ billAdvice.contactNumber | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-100">公司银行账号：</div>
          <div>{{ billAdvice.saleBankNo | noData }}</div>
        </div>
        <div class="account-item">
          <div class="item-label w-100">开户行：</div>
          <div>{{ billAdvice.saleBankName | noData }}</div>
        </div>
      </div>
      <div class="pay-notice-desc m-t-8 text-right">
        安徽中安创谷科技园有限公司运营部 {{ currentTime }}
      </div>
    </div>
    <template v-slot:footer>
      <div
        class="flex justify-content-end align-items-center pay-notice-wrapper"
      >
        <!--        <div class="pay-notice-desc">本账单已于{{ billAdvice.payment | noData }}系统自动下发给企业</div>-->
        <div>
          <el-button type="primary" @click="downloadWordHandle"
            >下载Word</el-button
          >
          <el-button type="primary" @click="downloadPdfHandle"
            >下载PDF</el-button
          >
        </div>
      </div>
    </template>
  </dialog-cmp>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import {
  getPaymentMemoDetail,
  paymentMemoExport,
  paymentMemoWordExport
} from '../api'
import ColumnMixins from './column'
import downloads from '@/utils/download'
import dayjs from 'dayjs'

export default {
  name: 'PayNotice',
  mixins: [ColumnMixins],
  props: {
    id: {
      type: Number,
      default: 0
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      NumFormat,
      payVisible: false,
      billAdvice: {},
      currentTime: '',
      feeList: [],
      depositChecked: 1 // 0包含 1不包含
    }
  },
  computed: {
    payList() {
      if (this.depositChecked) {
        return this.feeList.filter(item => !item.isDeposit)
      } else {
        return this.feeList
      }
    },
    totalAmount() {
      const total = this.payList.reduce((total, item) => {
        return total + Number(item.totalAmount || 0) * 100
      }, 0)
      return total / 100
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getPaymentMemoDetail(this.id, () => {
          this.payVisible = val
        })
      } else {
        this.payVisible = val
      }
    },
    payVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    downloadWordHandle() {
      downloads.requestDownload(
        paymentMemoWordExport(this.id, this.depositChecked),
        'word',
        `${this.billAdvice.fileName}.docx`
      )
    },
    downloadPdfHandle() {
      downloads.requestDownload(
        paymentMemoExport(this.id, this.depositChecked),
        'pdf',
        `${this.billAdvice.fileName}.pdf`
      )
    },
    getPaymentMemoDetail(id, cb) {
      getPaymentMemoDetail(id).then(res => {
        const currentTime = dayjs(new Date()).format('YYYY年MM月DD日')
        this.billAdvice = res
        this.feeList = res.feeList || []
        this.currentTime = currentTime
        cb && cb()
      })
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.empty-content) {
  padding: 20px 0 !important;
}
.header-right-wrapper {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
  padding-right: 24px;
  display: flex;
  align-items: center;
  .line {
    display: inline-block;
    width: 1px;
    height: 12px;
    background: #d8d8d8;
    margin: 0 8px;
  }
}
.pay-notice-wrapper {
  color: rgba(0, 0, 0, 0.9);
  .deposit-wrapper {
    background: #fef3e6;
    border-radius: 3px;
    padding: 8px;
    display: flex;
    .deposit-content {
      margin-left: 8px;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.4);
      line-height: 22px;
    }
  }
  .pay-notice-desc {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.4);
    line-height: 22px;
    &.text-indent {
      text-indent: 2em;
    }
    .desc-weight {
      color: #000;
      text-decoration: underline;
    }
  }
  .account-wrapper {
    margin-top: 8px;
    padding: 16px;
    background: #f0f2f5;
    border-radius: 3px;
    .account-item {
      display: flex;
      font-size: 14px;
      line-height: 22px;
      .item-label {
        flex-shrink: 0;
        color: rgba(0, 0, 0, 0.4);
        text-align: justify;
        text-align-last: justify;
        &.w-60 {
          width: 60px;
        }
        &.w-100 {
          width: 100px;
          margin-right: 16px;
        }
      }
    }
  }
  .separation-line {
    width: calc(100% + 48px);
    margin: 24px 0 24px -24px;
    border-bottom: 1px dashed #d8d8d8;
  }
  .text-right {
    text-align: right;
  }
}
</style>
<style lang="scss">
.pay-notice-dialog {
  .el-dialog__header {
    font-weight: 350;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 72px;
    height: 72px;
    .el-dialog__headerbtn {
      top: 28px;
      .el-dialog__close {
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
  .el-dialog__footer {
    padding: 24px;
  }
}
</style>
