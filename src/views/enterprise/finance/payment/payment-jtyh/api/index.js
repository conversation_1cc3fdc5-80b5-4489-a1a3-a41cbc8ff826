/*
 * @Author: cc <EMAIL>
 * @Date: 2023-02-21 11:09:05
 * @LastEditors: cc <EMAIL>
 * @LastEditTime: 2023-02-24 16:32:56
 * @FilePath: \parallel-cloud-manage\src\views\enterprise\finance\payment\payment-basic\api\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'
// 获取活动信息
export function getPolicyDetail(id) {
  return request({
    url: `/hatch/ent/policy/get?id=${id}`,
    method: 'get'
  })
}

// 获取企业端交费列表页面
export function getEntBillTotalList(params) {
  return request({
    url: `/pay/bill_total/ent_bill_page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取余额类型
export function getBalanceType() {
  return request({
    url: `/bill/purse/ent/detail`,
    method: 'get'
  })
}

//交行-账单核销获取企业账户信息
export function getEntAcc(id) {
  return request({
    url: `/pay/account/get_ent_acc?entId=${id}`,
    method: 'get'
  })
}
//交行-获取企业账单核销账务信息
export function getEntHisAccountingPage(params) {
  return request({
    url: `/pay/account/get_ent_his_accounting_page`,
    method: 'get',
    params
  })
}
// 账单核销接口 (银行)
export function collectBillBank(data) {
  return request({
    url: `/pay/bill_total/collect_bill_bank`,
    method: 'post',
    data
  })
}
