<template>
  <div class="activity-basic">
    <module-header
      type="primary"
      title="缴费管理"
      desc="快速查办企业所有缴费的账单记录等信息"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    >
      <template slot="title-right">
        <el-tooltip
          class="item"
          effect="dark"
          content="请查看确认企业钱包中的余额是否足够，可点击“在线缴费”进行账单核销操作"
          placement="top"
        >
          <svg-icon
            icon-class="help-circle"
            class-name="m-l-5 m-r-5 icon-size"
          />
        </el-tooltip>
        <div class="font-size-14 line-height-22">交费指引</div>
      </template>
    </module-header>

    <div class="lateral-wrapper">
      <template v-if="current === 1">
        <!-- 筛选项 -->
        <div class="module-filter">
          <module-filter
            :filters="filterData"
            @change="filterChange"
            :flag="false"
          />
        </div>

        <!-- 内容区域 -->
        <div class="module-list">
          <module-list ref="ModuleList" :api-fn="getEntBillTotalList">
            <template slot-scope="scope">
              <div class="card-wrapper">
                <div
                  class="card-list"
                  v-for="data in scope.data"
                  :key="data.id"
                >
                  <payment-card :list="data" @refreshTable="refreshTable" />
                </div>
              </div>
            </template>
          </module-list>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ModuleFilter from '@/components/Lateral/ModuleFilter'
import ModuleList from '@/components/Lateral/ModuleList'
import PaymentCard from './components/Card'
// import BasicTab from '@/components/BasicTab'
import ColumnMixins from './column/list-column'
import { getByTenantDictType } from '@/api/common'
import { getEntBillTotalList } from './api'

export default {
  name: 'PaymentBasic',
  components: {
    ModuleHeader,
    ModuleFilter,
    ModuleList,
    PaymentCard
    // BasicTab
  },
  mixins: [ColumnMixins],
  data() {
    return {
      filterData: [
        {
          label: '费用名称',
          prop: 'type',
          list: [
            { label: '租房款', dictType: 1 },
            { label: '公寓款', dictType: 2 },
            { label: '水费', dictType: 3 },
            { label: '电费', dictType: 4 }
          ]
        },
        {
          label: '账单状态',
          prop: 'collectStatus',
          list: [
            { label: '待核销', dictType: 1 },
            { label: '部分核销', dictType: 2 },
            { label: '已核销', dictType: 3 }
          ]
        },
        {
          label: '费用周期',
          prop: 'overdueStatus',
          list: [
            { label: '未逾期', dictType: 1 },
            { label: '短时逾期', dictType: 2 },
            { label: '长时逾期', dictType: 3 }
          ]
        }
      ],
      getEntBillTotalList,
      tabsData: [
        { label: '交费账单', value: 1 },
        { label: '核销记录', value: 2 }
      ],
      current: 1,
      tableList: [
        {
          data: '合肥创新产业园',
          data2: '68,458.00',
          data3: '2022-06-08  13:76',
          data4: '1000206300103',
          data5: '1000206300103',
          data6: '100101000002',
          data7: '转账核销',
          data8: 1
        },
        {
          data: '合肥创新产业园',
          data2: '68,458.00',
          data3: '2022-06-08  13:76',
          data4: '1000206300103',
          data5: '1000206300103',
          data6: '100101000002',
          data7: '转账核销',
          data8: 2
        }
      ]
    }
  },
  methods: {
    tabsChange(e) {
      this.current = this.tabsData[e].value
    },
    getByTenantDictType() {
      getByTenantDictType('policy_level').then(res => {
        this.$set(this.filterData[0], 'list', res)
      })
      getByTenantDictType('policy_label').then(res => {
        this.$set(this.filterData[1], 'list', res)
      })
    },
    filterChange(filter) {
      this.$refs.ModuleList.triggerSearch(filter)
    },
    refreshTable() {
      this.$refs.ModuleList.refresh()
    }
  }
}
</script>

<style lang="scss" scoped>
.module-filter {
  margin-top: 24px;
}
.module-list {
  margin-top: 16px;
}
:deep(.card-wrapper) {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  .card-list {
    width: 50%;
    margin-bottom: 32px;
    &:nth-child(odd) {
      padding-right: 14px;
    }
    &:nth-child(even) {
      padding-left: 14px;
    }
  }
}
.icon-size {
  height: 14px;
  width: 14px;
}
</style>
