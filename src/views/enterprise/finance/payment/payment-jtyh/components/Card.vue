<template>
  <div class="card">
    <div @click="toDetail">
      <div class="card-wrapper color-">
        <div class="banner">
          <p class="day font-size-36 line-height-44">
            <img src="../images/money.png" alt="" />
          </p>
          <p class="date font-size-16 line-height-24">
            {{ list.payTime | noData }}
          </p>
        </div>
        <div class="content">
          <h3 class="line-1 font-size-16 line-height-24 m-b-8">
            {{ list.billNo | noData }}
          </h3>
          <div class="icon-list m-b-8">
            <icon-info
              icon-type="primary"
              icon-class="calendar"
              :text="'账单周期：' + list.rcvAmtSdt + '~' + list.rcvAmtEdt"
            />
          </div>
          <div class="m-b-16 flex align-items-center justify-content-between">
            <div class="flex">
              <div class="font-size-14 line-height-22 color-text-secondary">
                账单金额(元)：
              </div>
              <div class="m-l-8 font-size-14 line-height-22 color-warning">
                {{ NumFormat(list.amount) }}
              </div>
            </div>
            <div class="flex">
              <div class="font-size-14 line-height-22 color-text-secondary">
                已核销金额(元)：
              </div>
              <div class="m-l-8 font-size-14 line-height-22 color-warning">
                {{ NumFormat(list.paidAmount) }}
              </div>
            </div>
          </div>
          <div class="tag-button">
            <tag-button
              :disabled="list.status !== 1"
              slot="reference"
              type="primary"
              :tag="'应交日期：' + list.payTime"
              :buttonName="'在线缴费'"
              @tapButton="getWriteOff(list)"
            />
          </div>
        </div>
      </div>
      <div class="status">
        <angle-status type="primary" :text="list.typeStr" />
      </div>
    </div>

    <!--        新版账单核销弹框-->
    <dialog-cmps
      :visible.sync="billWriteOffVisible"
      width="75%"
      title="在线缴费"
      :append-to-body="true"
      modal
      @confirmDialog="billWriteOffDialog"
    >
      <div class="body" v-if="billWriteOffVisible" v-loading="isLoading">
        <div class="font-size-14 m-b-16">应收款</div>
        <drive-table
          ref="drive-table"
          class="m-b-12"
          show-summary
          :summary-method="getSummaries"
          :columns="tableColumnReceivables"
          :table-data="billInfo.billDetailList"
        >
        </drive-table>
        <div
          class="font-size-14 m-b-16 flex justify-content-between align-items-center"
        >
          <span>已收款</span>
        </div>
        <drive-table
          class="m-b-12"
          ref="drive-table"
          :columns="tableColumnReceived"
          :table-data="[balanceType]"
        >
        </drive-table>
        <div
          class="flex justify-content-between align-items-center color-info m-b-25"
        >
          <div class="font-size-12">暂不支持多种余额的组合支付</div>
          <el-radio-group v-model="payType" size="small" @input="payTypeChange">
            <el-radio-button label="账户余额"></el-radio-button>
            <el-radio-button label="钱包余额"></el-radio-button>
            <el-radio-button label="补贴余额"></el-radio-button>
          </el-radio-group>
        </div>
        <div class="flex justify-content-between align-items-center m-b-10">
          <div class="font-size-14 m-b-16 m-t-16">实收款</div>
          <div class="font-size-12 color-info" v-if="isEnough">
            此账户余额不足以支付已选中账单的全部金额，请输入各费用的实收金额
          </div>
          <el-button type="primary" size="small" @click="allVerification" v-else
            >全部核销</el-button
          >
        </div>
        <drive-table
          ref="bill-table"
          class="m-b-15"
          :columns="tableColumnBillDetails"
          :table-data="billDetailList"
        />
        <driven-form
          label-position="top"
          ref="drive-form-writeOff"
          v-model="fromModelWriteOff"
          :formConfigure="formConfigure"
        />
      </div>
    </dialog-cmps>
  </div>
</template>

<script>
import {
  collectBillBank,
  getBalanceType
} from '@/views/enterprise/finance/payment/payment-basic/api/index.js'
import IconInfo from '@/components/IconInfo'
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'
import { NumFormat } from '@/utils/tools'
import { getEntAcc, getEntHisAccountingPage } from '../api/index.js'
import {
  getBillDetail,
  collectBill
  // getFlushAcc
} from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/api/index.js'
import ColumnMixins from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/column/column'
import formConfigureing from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/descriptor'
import DialogCmps from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/components/DialogCmp'
import { Toast } from '@/utils/message'
export default {
  name: 'ActivityCard',
  mixins: [ColumnMixins, formConfigureing],
  components: {
    IconInfo,
    AngleStatus,
    TagButton,
    DialogCmps
  },
  props: {
    list: {
      type: Object,
      default: () => ({})
    }
  },
  mounted() {
    this.getBalanceType()
  },
  data() {
    return {
      payType: '账户余额',
      getEntHisAccountingPage,
      extralQuerys: {
        entId: ''
      },
      changeType: true,
      loading: false,
      isLoading: false,
      balanceType: {},
      NumFormat,
      visible: false, // 预览账单核销弹框
      title: '账单详情',
      uploadUrl: '',
      fromModel: {},
      billWriteOffVisible: false,
      fromModelWriteOff: {
        type: 2,
        amount: ''
      },
      billInfo: {},
      billId: '',
      billDetailList: [],
      flushAcc: {}, // 账户信息
      billAdvice: {}, // 缴费通知单
      avaBal: 0 //账户金额
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.formModel = {}
        this.clearValidate()
      }
    }
  },
  methods: {
    paymentType(row) {
      if (this.payType === '账户余额') {
        return NumFormat(this.avaBal)
      } else if (this.payType === '钱包余额') {
        return NumFormat(row.purse)
      } else if (this.payType === '补贴余额') {
        return NumFormat(row.subsidy)
      }
    },
    allVerification() {
      const writeOffLists = this.billInfo.billDetailList
      writeOffLists.forEach(item => {
        this.billDetailList.forEach(list => {
          if (item.id === list.id) {
            this.$set(list, 'amount', item.writtenAmount)
            const date = new Date()
            const year = date.getFullYear()
            const month = date.getMonth() + 1
            const day = date.getDate()
            this.$set(list, 'transactTime', `${year}-${month}-${day}`)
          }
        })
      })
    },
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '总计'
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (!values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
          sums[index] = NumFormat(sums[index])
        } else {
          sums[index] = 'N/A'
        }
      })
      return sums
    },
    getFlushed() {
      this.getBalanceType(this.billId)
      this.$message.success('刷新成功')
    },
    goTurnover() {
      this.$router.push({
        path: '/finance/fees/payment'
      })
    },
    formatNum(val, row) {
      let temp = val.toString()
      temp = temp.replace(/。/g, '.')
      temp = temp.replace(/[^\d.]/g, '') //清除"数字"和"."以外的字符
      temp = temp.replace(/^\./g, '') //验证第一个字符是数字
      temp = temp.replace(/\.{2,}/g, '') //只保留第一个, 清除多余的
      temp = temp.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      temp = temp.replace(/^()*(\d+)\.(\d\d).*$/, '$1$2.$3') //只能输入两个小数
      this.billDetailList.forEach(item => {
        if (item.id === row.id) {
          item.amount = temp
        }
      })
    },
    getBalanceType() {
      getBalanceType().then(res => {
        this.balanceType = res
      })
    },
    toDetail() {
      this.$router.push({
        path: '/finance/fees/payment/detail',
        query: {
          id: this.list.id
        }
      })
    },
    getBillDetail(id) {
      getBillDetail(id).then(res => {
        this.billInfo = res
        this.billDetailList = JSON.parse(JSON.stringify(res.billDetailList))
        // this.getFlushAcc()
      })
    },
    // 清除校验
    clearValidate() {
      this.$nextTick(() => {
        this.$refs['driven-form'].clearValidate()
      })
    },
    // 账单核销
    getWriteOff(row) {
      this.billWriteOffVisible = true
      this.isLoading = false
      this.getEntAcc(row.entId)
      this.billId = row.id
      this.getBillDetail(row.id)
      this.extralQuerys.entId = row.entId
    },
    getEntAcc(entId) {
      this.loading = true
      getEntAcc(entId).then(res => {
        this.avaBal = res?.avaBal
        this.loading = false
      })
    },
    // 确认
    confirmDialog() {
      console.log('确认弹框')
    },
    payTypeChange(val) {
      if (val === '账户余额') {
        this.getEntAcc(this.extralQuerys.entId)
      } else {
        this.loading = false
      }
    },
    //  查看账户
    getViewAccount() {
      console.log('查看账户')
    },
    billWriteOffDialog() {
      this.$refs['drive-form-writeOff'].validate(valid => {
        if (valid) {
          this.isLoading = true
          const params = JSON.parse(JSON.stringify(this.fromModelWriteOff))
          const arr = params?.attachIds || []
          const id = this.list.id
          params.attaches = arr.map(item => {
            return item.id
          })
          const billDetailList = JSON.parse(JSON.stringify(this.billDetailList))
          params.billList = billDetailList
            .filter(item => item.amount && item.transactTime)
            .map(item => {
              return {
                amount: item.amount,
                billId: item.id,
                transactTime: item.transactTime
              }
            })
          if (!params.billList.length)
            return Toast.error('请填写核销金额和核销时间')
          params.billTotalId = id
          params.purseType =
            this.payType === '账户余额'
              ? 2
              : this.payType === '钱包余额'
              ? 0
              : this.payType === '补贴余额'
              ? 1
              : null
          if (this.payType === '账户余额') {
            params.isLine = 1
            collectBillBank(params).then(() => {
              Toast.success('核销成功')
              this.fromModelWriteOff = {
                type: 2
              }
              this.$emit('refreshTable')
              this.isLoading = false
            })
          } else {
            collectBill(params).then(() => {
              Toast.success('核销成功')
              this.fromModelWriteOff = {
                type: 2
              }
              this.isLoading = false
              this.$emit('refreshTable')
            })
          }
          this.billWriteOffVisible = false
        }
      })
    }

    // 更新账户
    // getFlushAcc() {
    //   getFlushAcc(this.billInfo.cusAc).then(res => {
    //     this.flushAcc = res
    //   })
    // }
  },
  computed: {
    // eslint-disable-next-line vue/return-in-computed-property
    isEnough() {
      const { subsidy, purse } = this.balanceType
      const { billDetailList = [] } = this.billInfo
      if (this.payType === '账户余额') {
        return !(
          this.avaBal >=
          billDetailList[billDetailList.length - 1]?.writtenAmount
        )
      } else if (this.payType === '钱包余额') {
        return !(
          purse >= billDetailList[billDetailList.length - 1]?.writtenAmount
        )
      } else if (this.payType === '补贴余额') {
        return !(
          subsidy >= billDetailList[billDetailList.length - 1]?.writtenAmount
        )
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  padding-left: 4px;
  position: relative;
  cursor: pointer;
  .card-wrapper {
    padding: 8px 16px 8px 8px;
    display: flex;
    align-items: center;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-lighter);
    border-radius: 6px;
    .banner {
      flex: 0 0 120px;
      height: 150px;
      border-radius: 3px;
      overflow: hidden;
      & image {
        height: 54px;
        width: 51px;
      }
      p {
        text-align: center;
        @include font_color_mix(--color-warning, #ffffff, 30%);
        &.day {
          padding-top: 48px;
          padding-bottom: 14px;
          @include background_color_mix(--color-warning, #ffffff, 90%);
        }
        &.date {
          padding: 2px 0;
          @include background_color_mix(--color-warning, #ffffff, 80%);
        }
      }
    }
    .content {
      flex: 1;
      overflow: hidden;
      padding-left: 16px;
    }
  }
  .status {
    position: absolute;
    left: 0;
    top: 16px;
  }
}
.strong {
  font-weight: 700;
  color: #000;
}
:deep(.el-dialog .el-dialog__header) {
  height: 100px;
  margin-bottom: 0;
  padding: 0 24px;
  box-sizing: border-box;
  font-size: 16px;
  font-weight: 900;
  border-bottom-width: 1px;
  border-style: solid;
}
.accout-left {
  font-size: 14px;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.6);
}
.accout-right {
  font-size: 18px;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #ed7b2f;
  margin-top: 17px;
}
.accout-right-amout {
  font-size: 18px;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #000;
  margin-top: 17px;
}
:deep(.custom-tips) {
  margin-left: 0 !important;
}
.date {
  :deep(.el-date-editor.el-input) {
    width: 112px;
  }
  :deep(.el-input--suffix .el-input__inner) {
    padding-right: 14px;
  }
}
</style>
