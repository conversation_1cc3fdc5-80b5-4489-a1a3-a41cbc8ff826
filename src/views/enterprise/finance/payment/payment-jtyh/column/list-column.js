// getTransactionStatus
import { getBillStatus } from '../utils/status'

export default {
  data() {
    return {
      tableColumn: [
        {
          label: '交易编号',
          prop: 'data'
        },
        {
          label: '支付方式',
          prop: 'data2',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data2}</div>
          }
        },
        {
          label: '交易金额（元）',
          prop: 'data4'
        },
        {
          label: '交易时间',
          prop: 'data3',
          sortable: true
        },
        {
          label: '账单金额（元）',
          prop: 'data3'
        },
        {
          label: '记录状态',
          prop: 'data4'
        },
        {
          label: '确认状态',
          prop: 'data4'
        },
        {
          label: '到账状态',
          prop: 'data4'
        },
        {
          prop: 'operation',
          label: '操作',
          width: 100,
          render: (h, scope) => {
            return (
              <div>
                <el-link
                  type="primary"
                  onclick={() => {
                    this.goTableDetails(scope.row)
                  }}
                  class="m-r-15"
                >
                  查看
                </el-link>
                <el-dropdown>
                  <el-link type="primary">更多</el-link>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item>
                      <el-link
                        type="success"
                        onClick={() => {
                          this.createAndEditContract(scope.row)
                        }}
                      >
                        编辑
                      </el-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                      <el-link
                        type="danger"
                        onClick={() => {
                          this.deleteContract(scope.row)
                        }}
                      >
                        删除
                      </el-link>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            )
          }
        }
      ],
      //支付记录
      tableColumnPlan: [
        {
          label: '支付金额（元）',
          prop: 'data',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data5}</div>
          }
        },
        {
          label: '支付时间',
          prop: 'data2'
        },
        {
          label: '支付方式',
          prop: 'data3'
        },
        {
          label: '支付状态',
          prop: 'data3'
        },
        {
          label: '操作人',
          prop: 'data3'
        },
        {
          label: '备注',
          prop: 'data3'
        },
        {
          label: '相关附件',
          prop: 'data3'
        }
      ],
      //账单信息
      tableColumnOrder: [
        {
          label: '合同编号',
          prop: 'contractNo'
        },
        {
          label: '账单编号',
          prop: 'billNo'
        },
        {
          label: '账单类型',
          prop: 'data2',
          render: (h, scope) => {
            return <div>{getBillStatus(h, scope.row.data2)}</div>
          }
        },
        {
          label: '账单金额（元）',
          prop: 'data5',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data5}</div>
          }
        },
        {
          label: '账单周期',
          prop: 'data3'
        },
        {
          label: '应缴日期',
          prop: 'data4'
        }
        //
        // {
        //   label: '实交金额（元）',
        //   prop: 'data6',
        //   render: (h, scope) => {
        //     return <div class={'color-warning'}>{scope.row.data6}</div>
        //   }
        // },
        // {
        //   label: '逾期天数（天）',
        //   prop: 'data7'
        // },
        // {
        //   label: '账单状态',
        //   prop: 'data8',
        //   render: (h, scope) => {
        //     return <div>{getBillStatus(h, scope.row.data8)}</div>
        //   }
        // }
      ],
      tableColumnWriteOff: [
        {
          label: '退款金额（元）',
          prop: 'data',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data}</div>
          }
        },
        {
          label: '退款时间',
          prop: 'data2'
        },
        {
          label: '退款原因',
          prop: 'data3'
        },
        {
          label: '退款状态',
          prop: 'data4'
        },
        {
          label: '操作人',
          prop: 'data5'
        },
        {
          label: '备注',
          prop: 'data6'
        },
        {
          label: '相关附件',
          prop: 'data7'
        }
      ],
      tableColumnRefund: [
        {
          label: '修改时间',
          prop: 'data'
        },
        {
          label: '修改类型',
          prop: 'data2'
        },
        {
          label: '修改原因',
          prop: 'data3'
        },
        {
          label: '修改信息',
          prop: 'data4'
        },
        {
          label: '修改状态',
          prop: 'data4'
        },
        {
          label: '操作人',
          prop: 'data5'
        },
        {
          label: '相关附件',
          prop: 'data7'
        }
      ],
      tableColumnExpense: [
        {
          label: '账单编号',
          prop: 'data'
        },
        {
          label: '账单周期',
          prop: 'data2'
        },
        {
          label: '应缴时间',
          prop: 'data3'
        },
        {
          label: '房租费',
          prop: 'data4'
        },
        {
          label: '服务费',
          prop: 'data5'
        },
        {
          label: '水电费',
          prop: 'data6'
        },
        {
          label: '保证金',
          prop: 'data7'
        },
        {
          label: '账单总金额(元)',
          prop: 'data8',
          render: (h, scope) => {
            return <div class={'color-warning'}>{scope.row.data}</div>
          }
        }
      ]
    }
  }
}
