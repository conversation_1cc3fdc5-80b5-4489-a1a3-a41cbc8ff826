<template>
  <div style="height: 100% !important">
    <!-- 头部 -->
    <module-header
      type="primary"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    >
    </module-header>
    <div class="lateral-wrapper form-card-container bg-white pos-relative">
      <div class="notice-container p-l-20 p-r-20 p-t-20">
        <div class="w100">
          <div class="flex justify-content-between m-b-20">
            <div class="font-size-18 font-strong">缴费通知单</div>
            <div class="font-size-14 p-r-28">
              <span class="info"> {{ billAdvice.parkName | noData }} | </span>
              <span class="info">
                打印时间：{{ billAdvice.printDate | noData }}
              </span>
            </div>
          </div>
          <div class="flex justify-content-between m-b-20">
            <div>
              <span class="font-size-14">公司名称</span>
              <span class="color-warning m-l-8 font-size-14">{{
                billAdvice.enterpriseName | noData
              }}</span>
            </div>
            <div>
              <span class="font-size-14">统一社会信用代码</span>
              <span class="color-warning m-l-8 font-size-14">{{
                billAdvice.creditCode | noData
              }}</span>
            </div>
            <div v-if="!isHydropower">
              <span class="font-size-14">合同编号</span>
              <span class="color-warning m-l-8 font-size-14">{{
                billAdvice.contractNo | noData
              }}</span>
            </div>
          </div>
        </div>

        <div class="body">
          <div v-if="billAdvice.billTotal">
            <div class="font-size-14 m-b-16">账单信息</div>
            <div class="girds m-b-24">
              <div>
                <span class="right font-size-14 info">账单编号</span>
                <span class="inline-block m-l-8 font-size-14">{{
                  billAdvice.billTotal.billNo | noData
                }}</span>
              </div>
              <div class="m-l-16">
                <span class="right font-size-14 info">账单生成日期</span>
                <span class="inline-block m-l-8 font-size-14">{{
                  billAdvice.billTotal.createTime | noData
                }}</span>
              </div>
              <div class="m-l-16">
                <span class="right font-size-14 info">账单周期</span>
                <span class="m-l-8 font-size-14"
                  >{{ billAdvice.billTotal.rcvAmtSdt | noData }} -
                  {{ billAdvice.billTotal.rcvAmtEdt | noData }}</span
                >
              </div>
            </div>
            <div class="girds m-b-24">
              <div>
                <span class="right font-size-14 info">应缴时间</span>
                <span class="inline-block m-l-8 font-size-14">{{
                  billAdvice.billTotal.payTime | noData
                }}</span>
              </div>
              <div class="m-l-16">
                <span class="right font-size-14 info">账单总金额</span>
                <span class="color-warning m-l-8 font-size-14"
                  >¥{{ NumFormat(billAdvice.billTotal.amount) }}</span
                >
              </div>
            </div>
            <div class="flex m-b-24">
              <div>
                <span class="right font-size-14 info">已缴总金额</span>
                <span class="mony color-warning m-l-8 font-size-14"
                  >¥{{ NumFormat(billAdvice.billTotal.paidAmount) }}</span
                >
              </div>
              <div class="m-l-16">
                <span class="right font-size-14 info">未缴总金额</span>
                <span class="color-warning m-l-8 font-size-14"
                  >{{ billAdvice.billTotal.unpaidAmountUp | noData }}（¥{{
                    NumFormat(billAdvice.billTotal.unpaidAmount)
                  }})</span
                >
              </div>
            </div>
          </div>
          <div class="font-size-14 m-b-16">费用明细</div>
          <drive-table
            ref="drive-table"
            :columns="tableColumnPayDetails"
            :table-data="billAdvice.billList"
          >
          </drive-table>
          <div
            v-if="!isHydropower"
            class="info m-b-24 m-t-10 line-height-20 font-size-14"
          >
            {{ billAdvice.prefix | noData }}
          </div>
          <!--          <div class="font-size-14 m-b-16">收账账户</div>-->
          <!--          <drive-table-->
          <!--            ref="drive-table"-->
          <!--            :columns="tableColumnCollectionAccount"-->
          <!--            :table-data="billAdvice.accountList"-->
          <!--          >-->
          <!--          </drive-table>-->
        </div>
        <!--        <div class="flex justify-content-end m-t-15">-->
        <!--          <el-button type="info" @click="payVisible = false">打印</el-button>-->
        <!--          <el-button type="primary" @click="payDialog">下载</el-button>-->
        <!--        </div>-->
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
// import IconInfo from '@/components/IconInfo'
import { NumFormat } from '@/utils/tools'
import ColumnMixins from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/column/column'
import { getBillAdvice } from '@/views/manage/financial/financial-basic/payment/accountsReceivable-basic/api/index.js'
export default {
  name: 'detail',
  components: {
    ModuleHeader
    // IconInfo
  },
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      billAdvice: {},
      isHydropower: false
    }
  },
  created() {
    const { id } = this.$route.query
    id && this.getBillAdvice(id)
  },
  methods: {
    // 获取缴费通知单
    getBillAdvice(id) {
      getBillAdvice(id).then(res => {
        this.billAdvice = res
        this.isHydropower =
          res?.billTotal?.type === 3 || res?.billTotal?.type === 4
      })
    },
    payHander() {
      console.log('缴费通知单')
      this.payVisible = true
    },
    payDialog() {
      console.log('交易确认')
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}
.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}
.notice-container {
  position: relative;
  top: -160px;
  z-index: 3;
  background: #fff;
  border: 1px solid transparent;
  border-radius: 4px;
}
:deep(.el-form-item--small.el-form-item) {
  margin-bottom: 0 !important;
}

.info {
  color: #999;
}

.girds {
  width: 100%;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 20px;
}

.right {
  text-align: right;
  width: 90px;
  display: inline-block;
}

.mony {
  width: 276px;
  display: inline-block;
}

.black {
  color: #1a1a1a;
}
</style>
