// 获取入园状态
export function getStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="success" label="成功" />
    case 1:
      return <basic-tag isDot type="danger" label="失败" />
    case 2:
      return <basic-tag isDot type="warning" label="审核中" />
    case 3:
      return <basic-tag isDot type="info" label="撤回" />
    default:
      return '-'
  }
}

// 获取业务来源
export function getBusinessSourceType(h, val) {
  switch (val) {
    case 1:
      return '转账登记'
    case 2:
      return '账单退款'
    case 3:
      return '保证金退款'
    case 4:
      return '政策补贴'
    case 5:
      return '账单核销'
    case 6:
      return '余额付款'
    default:
      return '-'
  }
}
