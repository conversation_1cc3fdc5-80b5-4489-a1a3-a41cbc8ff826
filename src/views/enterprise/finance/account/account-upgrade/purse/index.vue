<template>
  <div class="activity-basic">
    <header-cmp :img="require('../images/header-bg.png')" title="企业账户">
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          详细记录账户余额的收支往来
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content flex">
          <div class="w100 account-left">
            <div class="xx line">
              <div class="zh">
                <div class="flex align-items-center font-size-16">
                  <span class="font-size-16">户名</span>
                </div>
                <div
                  class="w-292 m-t-12 font-size-24 font-weight-bold line-1 p-r-10"
                >
                  {{ EntInfoToutal.entName | noData }}
                </div>
              </div>
            </div>
            <div class="xx line">
              <div class="zh">
                <div class="flex align-items-center font-size-16">
                  <span class="font-size-16">账号</span>
                </div>
                <div
                  class="w-292 m-t-12 font-size-24 font-weight-bold line-1 p-r-10"
                >
                  {{ EntInfoToutal.accountNo | noData }}
                </div>
              </div>
            </div>
            <div class="xx line">
              <div class="zh">
                <div class="flex align-items-center font-size-16">
                  <span class="font-size-16">租金余额</span>
                  <el-tooltip
                    class="item"
                    effect="dark"
                    content="可用于支付任意类型账单的余额"
                    placement="top"
                  >
                    <svg-icon icon-class="prompt" class="m-l-4 pointer" />
                  </el-tooltip>
                </div>
                <div class="pay-name m-t-12 font-size-16 p-r-10">
                  {{ NumFormat(EntInfoToutal.purse) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>

    <div class="lateral-wrapper">
      <basic-card :is-title="false">
        <div>
          <basic-tab
            ref="basicTab"
            :tabs-data="balanceType"
            :current="current"
            @tabsChange="tabsChange"
          />
          <div class="search-info">
            <div class="m-b-16">
              <el-form ref="form" :model="formModel" label-width="80px">
                <el-col :span="5">
                  <el-radio-group
                    v-model="formModel.tradeType"
                    @change="tradeTypeChange"
                  >
                    <el-radio-button
                      :label="item.value"
                      v-for="item in list"
                      :key="item.value"
                      >{{ item.label }}</el-radio-button
                    >
                  </el-radio-group>
                </el-col>
                <el-col :span="19">
                  <el-col :span="6">
                    <el-form-item label="业务来源">
                      <el-select
                        clearable
                        v-model="formModel.sources"
                        placeholder="请选择业务来源"
                        @change="sourcesChange"
                      >
                        <el-option
                          v-for="item in sourceList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        >
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6" v-if="formModel.tradeType !== 1">
                    <el-form-item label="付款方">
                      <el-input
                        clearable
                        @input="changName"
                        v-model="formModel.entName"
                        placeholder="请输入付款方名称"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="7">
                    <el-form-item label="交易时间">
                      <el-date-picker
                        v-model="formModel.time"
                        type="daterange"
                        clearable
                        range-separator="至"
                        start-placeholder="开始日期"
                        @change="changeDate"
                        value-format="yyyy-MM-dd"
                        end-placeholder="结束日期"
                      >
                      </el-date-picker>
                    </el-form-item>
                  </el-col>
                </el-col>
              </el-form>
            </div>
          </div>
        </div>
      </basic-card>

      <drive-table
        ref="drive-table"
        :columns="tableColumn"
        :api-fn="getPurseDetailPage"
        :extral-querys="extralQuerys"
      />
    </div>
  </div>
</template>

<script>
import BasicCard from '../components/BasicCard/index.vue'
import HeaderCmp from '../components/headerComponent/index.vue'
import formConfigureData from '../descriptor'
import ColumnMixins from '../column/list-column'
import {
  getPurseHeader,
  getPurseDetailPage,
  getTurnTab,
  getBalanceType,
  getSourceType
} from '../api'
import BasicTab from '../components/BasicTab/index.vue'
import { NumFormat } from '@/utils/tools'

export default {
  name: 'AccountBasic',
  components: {
    HeaderCmp,
    BasicCard,
    BasicTab
  },

  mixins: [ColumnMixins, formConfigureData],
  data() {
    return {
      options: [],
      formModel: {
        tradeType: -1,
        entName: '',
        time: [],
        sources: ''
      },
      registerUrl: null,
      fromTableInfo: {
        time: [],
        balanceType: '',
        source: ''
      },
      extralQuerys: {
        turnId: -1,
        balanceType: 0
      },
      getPurseDetailPage,
      NumFormat,
      sourceList: [],
      balanceType: [],
      list: [],
      current: 0,
      EntInfoToutal: {}
    }
  },
  mounted() {
    this.getPurseHeader()
    this.getTurnTab()
    this.getBalanceType()
    this.getSourceType()
  },
  methods: {
    sourcesChange(val) {
      this.extralQuerys.source = val
      this.$refs['drive-table'].triggerSearch()
    },
    changName(val) {
      this.extralQuerys.entName = val
      this.$refs['drive-table'].triggerSearch()
    },
    tradeTypeChange(val) {
      this.extralQuerys.turnId = val
      this.$refs['drive-table'].triggerSearch()
    },
    changeDate() {
      const { time = [] } = this.formModel
      if (time) {
        this.extralQuerys.startTime = time[0]
        this.extralQuerys.endTime = time[1]
      } else {
        delete this.extralQuerys.startTime
        delete this.extralQuerys.endTime
      }
      this.$refs['drive-table'].triggerSearch()
    },

    getSourceType() {
      getSourceType().then(res => {
        this.sourceList = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getBalanceType() {
      getBalanceType().then(res => {
        this.balanceType = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getTurnTab() {
      getTurnTab().then(res => {
        this.list = res.map(item => {
          return {
            label: item.label,
            value: item.key
          }
        })
      })
    },
    getPurseHeader() {
      getPurseHeader().then(res => {
        if (res) {
          this.EntInfoToutal = {
            ...res
          }
        }
      })
    },

    //切换
    tabsChange(e) {
      this.current = e
      this.extralQuerys.balanceType = e
      this.$refs['drive-table'].triggerSearch()
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-radio-button__inner) {
  width: 79px;
}
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}
:deep(.el-date-editor) {
  display: flex;
  justify-content: center;
}

::v-deep(.el-range-editor--small .el-range__close-icon) {
  opacity: 1;
}

:deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
}

:deep(.el-button--info) {
  background-color: #ffffff;
  &:hover {
    border-color: #ed7b2f;
    color: #ed7b2f;
  }
  &:active {
    background: #e7e7e7;
  }
}

.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}

.financial-content {
  height: 96px;
  border-radius: 3px 3px 0 0;
  background: rgba(255, 255, 255, 0.8);
  opacity: 1;
  padding: 16px;
}
.right-title {
  color: #616266;
}

.pay-name {
  font-weight: bold;
  font-size: 24px;
  @include font_color(--color-warning);
}

.account-left {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .xx {
    flex: 1;
    position: relative;
  }
  .line {
    //使用伪元素画一条宽1px高16px的线
    &:before {
      content: '';
      position: absolute;
      top: 0;
      right: 20px;
      width: 1px;
      height: 55px;
      background: #ebedf1;
    }
  }
  .w-292 {
    width: 292px;
  }
}
</style>
