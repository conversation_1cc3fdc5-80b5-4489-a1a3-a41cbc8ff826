import request from '@/utils/request'

// 查询企业账户信息头部
export function getEntAccountinPage(params) {
  return request({
    url: `/pay/account/get_ent_accounting_page`,
    method: 'get',
    params
  })
}

// 查询企业账户信息
export function getDetail() {
  return request({
    url: `/pay/account/ent_acc`,
    method: 'get'
  })
}

// 获得分页选择
export function getDetailPage(params) {
  return request({
    url: `/bill/withdrawal/ent/page`,
    method: 'get',
    params
  })
}

export function getTurnTab() {
  return request({
    url: `/bill/purse/admin/turn`,
    method: 'get'
  })
}

// 获得余额类型选择
export function getBalanceType() {
  return request({
    url: `/bill/purse/admin/balance_type`,
    method: 'get'
  })
}

//详情 - 获得业务来源
export function getSourceType() {
  return request({
    url: `/bill/purse/admin/source`,
    method: 'get'
  })
}

//付款申请接口
export function getWithdrawal(params) {
  return request({
    url: `/bill/withdrawal/ent/withdrawal`,
    method: 'get',
    params
  })
}

//钱包列表接口
export function getPurseDetailPage(params) {
  return request({
    url: `/bill/purse/ent/detail_page`,
    method: 'get',
    params
  })
}

//钱包头部接口
export function getPurseHeader(params) {
  return request({
    url: `/bill/purse/ent/detail`,
    method: 'get',
    params
  })
}
