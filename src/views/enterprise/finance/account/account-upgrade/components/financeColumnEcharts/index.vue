<template>
  <div class="grid">
    <div class="finance-column">
      <div>
        <div class="title">累计收入</div>
        <div
          class="font-weight-500 font-size-28 line-height-40"
          style="font-weight: 600"
        >
          <span class="font-size-18">¥</span>
          <span>
            {{ NumFormat(dataSource.totalPay) }}
          </span>
        </div>
        <div class="percentage flex">
          <span class="font-size-14 line-height-22">+10009.28</span>
          <span class="percent">+7.53%</span>
        </div>
        <!--      <div class="percentage flex">-->
        <!--        <span class="font-size-14 line-height-22 reduce">-10009.28</span>-->
        <!--        <span class="percent percent-reduce">-7.53%</span>-->
        <!--      </div>-->
        <div class="line"></div>
      </div>
      <div class="finance">
        <div ref="finance-data1" class="finance-data"></div>
      </div>
    </div>
    <div class="finance-column">
      <div>
        <div class="title">交易登记中</div>
        <div
          class="font-weight-500 font-size-28 line-height-40"
          style="font-weight: 600"
        >
          <span class="font-size-18">¥</span>
          <span>
            {{ dataSource.recordAmountSum | noData }}
          </span>
        </div>
        <div class="percentage flex">
          <span class="font-size-14 line-height-22">+10009.28</span>
          <span class="percent">+7.53%</span>
        </div>
        <!--      <div class="percentage flex">-->
        <!--        <span class="font-size-14 line-height-22 reduce">-10009.28</span>-->
        <!--        <span class="percent percent-reduce">-7.53%</span>-->
        <!--      </div>-->
        <div class="line"></div>
      </div>
      <div class="finance">
        <div ref="finance-data2" class="finance-data"></div>
      </div>
    </div>
    <div class="finance-column">
      <div>
        <div class="title">待核销</div>
        <div
          class="font-weight-500 font-size-28 line-height-40"
          style="font-weight: 600"
        >
          <span class="font-size-18">¥</span>
          <span>
            {{ NumFormat(dataSource.totalToWriteOff) }}
          </span>
        </div>
        <div class="percentage flex">
          <span class="font-size-14 line-height-22">+10009.28</span>
          <span class="percent">+7.53%</span>
        </div>
        <!--      <div class="percentage flex">-->
        <!--        <span class="font-size-14 line-height-22 reduce">-10009.28</span>-->
        <!--        <span class="percent percent-reduce">-7.53%</span>-->
        <!--      </div>-->
        <div class="line"></div>
      </div>
      <div class="finance">
        <div ref="finance-data3" class="finance-data"></div>
      </div>
    </div>
    <div class="finance-column">
      <div>
        <div class="title">未出账</div>
        <div
          class="font-weight-500 font-size-28 line-height-40"
          style="font-weight: 600"
        >
          <span class="font-size-18">¥</span>
          <span>
            {{ NumFormat(dataSource.totalOutstanding) }}
          </span>
        </div>
        <div class="percentage flex">
          <span class="font-size-14 line-height-22">+10009.28</span>
          <span class="percent">+7.53%</span>
        </div>
        <!--      <div class="percentage flex">-->
        <!--        <span class="font-size-14 line-height-22 reduce">-10009.28</span>-->
        <!--        <span class="percent percent-reduce">-7.53%</span>-->
        <!--      </div>-->
        <div class="line"></div>
      </div>
      <div class="finance">
        <div ref="finance-data4" class="finance-data"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import * as Echarts from 'echarts'
export default {
  name: 'FinanceColumnEcharts',
  props: {
    dataSource: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      financeData: null,
      NumFormat
    }
  },
  mounted() {
    this.initEcharts()
    window.addEventListener('resize', () => {
      this.financeEcharts.resize()
    })
  },
  methods: {
    initEcharts() {
      this.financeEcharts1 = Echarts.init(this.$refs['finance-data1'])
      this.financeEcharts2 = Echarts.init(this.$refs['finance-data2'])
      this.financeEcharts3 = Echarts.init(this.$refs['finance-data3'])
      this.financeEcharts4 = Echarts.init(this.$refs['finance-data4'])
      let option = {
        tooltip: {
          trigger: 'axis',
          backgroundColor: '#0f1218', //设置背景颜色
          axisPointer: {
            type: 'line'
          },
          textStyle: {
            color: 'white' //设置文字颜色
          },
          borderColor: '#0f1218',
          formatter() {
            // console.log(params) // 打印数据
            // let showdata = params[0];
            // 根据自己的需求返回数据
            return `
                   <div style="
                    width: 138px;
                    font-size: 14px;
                    word-break: break-word;">
                    累计收入：¥324,000
                    </div>
                    <div style="font-size: 14px;">2022/11/27</div>
                `
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Tut'],
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          axisLine: {
            show: false
          }
        },
        grid: {
          left: '0%',
          right: '0%',
          bottom: '3%',
          containLabel: true
        },
        yAxis: {
          boundaryGap: false,
          type: 'value',
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: [
          {
            data: [420, 932, 700, 1200],
            type: 'line',
            smooth: true,
            showSymbol: false,
            areaStyle: {},
            lineStyle: {
              color: '#054CE8'
            },
            symbolSize: 10, //折线点的大小
            itemStyle: {
              normal: {
                color: {
                  type: 'linear', // 线性渐变
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#b5caf9' // 0%处的颜色为红色
                    },
                    {
                      offset: 1,
                      color: '#e9f0fd' // 100%处的颜色为蓝
                    }
                  ]
                }
              },
              emphasis: {
                disabled: true,
                focus: 'none',
                color: '#ed7b2f'
              } //***这个字段就是控制鼠标悬浮在圆点上面让整个圆填充满背景色，也可以更改边框颜色等！***
            },
            emphasis: {
              disabled: true,
              focus: 'none'
            }
          }
        ]
      }
      this.financeEcharts1.setOption(option)
      this.financeEcharts2.setOption(option)
      this.financeEcharts3.setOption(option)
      this.financeEcharts4.setOption(option)
    }
  }
}
</script>

<style lang="scss" scoped>
.grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 16px;
  margin-top: 6px;
}

.finance-column {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 6px 6px 6px 6px;
  padding: 24px;
  box-sizing: border-box;
  opacity: 1;
  border: 1px solid #e7e7e7;

  .title {
    width: 100%;
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.6);
  }
  .percentage {
    color: #00a870;
    .percent {
      width: 57px;
      height: 24px;
      background: #e8f8f2;
      font-size: 12px;
      display: inline-block;
      margin-left: 6px;
      border-radius: 3px 3px 3px 3px;
      padding: 8px 5px;
      line-height: 6px;
      box-sizing: border-box;
      opacity: 1;
    }

    .reduce {
      color: #e34d59;
    }
    .percent-reduce {
      color: #e34d59;
      background: #f8b9be;
      padding: 8px 8px;
    }
  }

  .line {
    width: 100%;
    margin-top: 24px;
    height: 1px;
    background: #e7e7e7;
    opacity: 1;
  }
  .finance {
    //position: relative;
    .finance-data {
      width: 100%;
      height: 36.5vh;
      //min-height: 200px;
    }
  }
}

.finance-column:hover {
  background-color: #f3f7ff;
}
</style>
