<template>
  <div class="table-search">
    <div class="search-form">
      <el-form ref="searchForm" :inline="true" :model="formQuerys">
        <el-row class="form-warp" :gutter="36">
          <template v-for="column in filterSearchColumn">
            <el-col :key="column.prop" :span="12">
              <el-form-item
                class="search-item"
                :label="column.label"
                :prop="confirmProp(column)"
              >
                <component
                  :is="inputMap[column.search.type].type"
                  :filterable="column.search.filterable || false"
                  v-model="formQuerys[confirmProp(column)]"
                  v-bind="_compareAttrs(column, inputMap)"
                  v-on="column.search.on"
                >
                  <template v-if="inputMap[column.search.type].slotRender">
                    <drive-table-render
                      :render="inputMap[column.search.type].slotRender"
                      :options="column.search.options"
                    />
                  </template>
                </component>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import DriveTableRender from '@/components/DriveTable/src/render'
import InputRange from '@/components/InputRange'
import { inputDefautConfig, inputMap } from '@/components/DriveTable/src/input'

export default {
  name: 'TableSearch',
  components: { DriveTableRender, InputRange },
  props: {
    columns: {
      type: Array,
      default: () => []
    }
  },
  inject: ['AccountBasic'],
  data() {
    return {
      inputMap,
      inputDefautConfig,
      formQuerys: {},
      formQuerysStr: '',
      rangeProp: [],
      resetEmptyOptionsProp: []
    }
  },
  watch: {
    formQuerys: {
      handler(val) {
        if (val) {
          this.formQuerysStr = JSON.stringify(val)
        }
      },
      deep: true
    },
    formQuerysStr(newVal, oldVal) {
      this.$emit('formQuerys', {
        newVal,
        oldVal
      })
    },
    columns: {
      handler(columns) {
        if (!columns) return
        columns
          .filter(column => column.search)
          .forEach(item => {
            if (
              item.search.type === 'range' ||
              item.search.type === 'daterange' ||
              item.search.type === 'timeSelect'
            ) {
              this.$set(this.formQuerys, item.prop, [])
              this.rangeProp.push(item)
            }
            if (item.search.resetEmptyOptions) {
              this.resetEmptyOptionsProp.push(item)
            }
            if (item.search.default || item.search.default === '') {
              if (this.formQuerys[item.prop]) {
                this.$set(
                  this.formQuerys,
                  item.prop,
                  this.formQuerys[item.prop]
                )
              } else {
                this.$set(this.formQuerys, item.prop, item.search.default)
              }
            }
          })
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // 判断是否有搜索数据
    hasSomeSearch() {
      return this.columns.some(v => v.search)
    },
    // 筛选出有search属性的column
    filterSearchColumn() {
      return this.columns.filter(v => v.search)
    }
  },
  methods: {
    // 搜索
    searchSubmit() {
      const formQuerys = JSON.parse(JSON.stringify(this.formQuerys))
      for (let key in formQuerys) {
        const value = formQuerys[key]
        if (
          value === null ||
          JSON.stringify(value) === '[]' ||
          JSON.stringify(value) === '{}'
        ) {
          delete formQuerys[key]
        }
      }
      this.AccountBasic.triggerSearch(formQuerys)
    },
    // 重置
    resetForm() {
      this.$refs.searchForm.resetFields()
      this.formQuerys = {}
      if (this.rangeProp.length > 0) {
        this.rangeProp.forEach(item => {
          this.$set(this.formQuerys, item.prop, [])
        })
      }
      if (this.resetEmptyOptionsProp.length > 0) {
        this.resetEmptyOptionsProp.forEach(item => {
          item.search.options = []
        })
      }
      this.AccountBasic.resetSearch()
    },
    // 默认属性合并
    _compareAttrs(column, inputMap) {
      const { type } = column.search
      const { placeholderPrefix } = inputMap[type]
      return {
        ...inputDefautConfig[type],
        ...column.search,
        placeholder: placeholderPrefix + column.label
      }
    },
    // 确定查询时的prop
    confirmProp(column) {
      if (column.search && column.search.propAlias) {
        return column.search.propAlias
      } else {
        return column.prop
      }
    }
  }
}
</script>

<style scoped lang="scss">
.table-search {
  .form-warp {
    display: flex;
    flex-wrap: wrap;
    :deep(.el-form-item) {
      width: 100%;
      .el-form-item__label {
        width: 90px;
      }
      .el-form-item__content {
        width: calc(100% - 90px);
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
</style>
