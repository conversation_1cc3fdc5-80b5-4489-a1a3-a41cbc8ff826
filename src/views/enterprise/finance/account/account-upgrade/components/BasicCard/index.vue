<template>
  <div class="basic-card">
    <div v-if="isTitle" class="card-title flex flex-center-between">
      <div class="flex align-items-center" v-if="title">
        {{ title }}
        <slot name="tag"></slot>
      </div>
      <breadcrumb v-else />
      <slot name="right"></slot>
    </div>
    <div class="content">
      <slot />
    </div>
  </div>
</template>

<script>
import Breadcrumb from '@/components/Breadcrumb'

export default {
  name: 'BasicCard',
  components: { Breadcrumb },
  props: {
    title: {
      type: String,
      default: ''
    },
    isTitle: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style lang="scss" scoped>
.basic-card {
  @include background_color(--color-white);
  width: 100%;
  min-height: 100%;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  .card-title {
    width: 100%;
    font-size: 16px;
    line-height: 24px;
    padding: 18px 24px;
    height: 60px;
    flex: 0 0 60px;
  }
  .content {
    width: 100%;
    height: 0;
    flex: 1;
    padding: 0;
  }
}
</style>
