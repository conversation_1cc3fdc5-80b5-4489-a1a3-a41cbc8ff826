export default {
  data() {
    return {
      // formConfigureSendBack: {
      //   descriptors: {
      //     attachIds: {
      //       form: 'component',
      //       label: '相关附件',
      //       rule: [
      //         {
      //           type: 'array'
      //         }
      //       ],
      //       componentName: 'uploader',
      //       props: {
      //         uploadData: {
      //           type: 'informationAttach'
      //         },
      //         mulity: true,
      //         maxLength: 3,
      //         maxSize: 10,
      //         limit: 3
      //       }
      //     },
      //     returnReason: {
      //       form: 'input',
      //       label: '退回原因',
      //       rule: [
      //         {
      //           required: true,
      //           type: 'string',
      //           message: '请输入备注信息'
      //         }
      //       ],
      //       props: {
      //         type: 'textarea'
      //       }
      //     }
      //   }
      // },
    }
  }
}
