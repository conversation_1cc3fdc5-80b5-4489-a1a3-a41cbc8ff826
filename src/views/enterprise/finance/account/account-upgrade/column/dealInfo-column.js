export default {
  data() {
    return {
      tableColumn: [
        {
          label: '交易时间',
          prop: 'data'
        },
        {
          label: '企业名称',
          prop: 'data2'
        },
        {
          label: '所属园区',
          prop: 'data3'
        },
        {
          label: '到账金额',
          prop: 'data4',
          render: (h, scope) => {
            return <div style={'color:#ED7B2F'}>{scope.row.data4}</div>
          }
        },
        {
          label: '交易方式',
          prop: 'data5'
        },
        {
          label: '确认状态',
          prop: 'data6'
        },
        {
          label: '到账状态',
          prop: 'data7',
          render: (h, scope) => {
            return <div style={'color:#00A870'}>{scope.row.data4}</div>
          }
        },
        {
          label: '登记状态',
          prop: 'data8'
        },
        {
          label: '操作',
          prop: 'operation',
          render: (h, scope) => {
            return (
              <div>
                {
                  <el-button
                    type="text"
                    onClick={() => {
                      this.consentHandler(scope.row, 'consent')
                    }}
                  >
                    查看
                  </el-button>
                }
              </div>
            )
          }
        }
      ],
      tableColumnYesterday: [
        {
          label: '交易时间',
          prop: 'data'
        },
        {
          label: '企业名称',
          prop: 'data2'
        },
        {
          label: '所属园区',
          prop: 'data3'
        },
        {
          label: '到账金额',
          prop: 'data4'
        },
        {
          label: '交易方式',
          prop: 'data5'
        },
        {
          label: '确认状态',
          prop: 'data6'
        },
        {
          label: '到账状态',
          prop: 'data7'
        },
        {
          label: '登记状态',
          prop: 'data7'
        },
        {
          label: '操作',
          prop: 'data7'
        }
      ],
      tableColumnHistory: [
        {
          label: '交易时间',
          prop: 'data'
        },
        {
          label: '企业名称',
          prop: 'data2'
        },
        {
          label: '所属园区',
          prop: 'data3'
        },
        {
          label: '到账金额',
          prop: 'data4'
        },
        {
          label: '交易方式',
          prop: 'data5'
        },
        {
          label: '确认状态',
          prop: 'data6'
        },
        {
          label: '到账状态',
          prop: 'data7'
        },
        {
          label: '登记状态',
          prop: 'data7'
        },
        {
          label: '操作',
          prop: 'data7'
        }
      ]
    }
  }
}
