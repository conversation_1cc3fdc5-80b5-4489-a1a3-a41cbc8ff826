import { NumFormat } from '@/utils/tools'

export default {
  data() {
    return {
      tableColumn1: [
        {
          label: '银行流水号',
          prop: 'traceNo'
        },
        {
          label: '对方账号',
          prop: 'oppAc'
        },
        {
          label: '对方户名',
          prop: 'oppAcNme'
        },
        {
          label: '借贷标志',
          prop: 'loanFlag',
          render: (h, scope) => {
            return <div>{scope.row.loanFlag === '1' ? '进账' : '出账'}</div>
          }
        },
        {
          label: '交易金额(元)',
          prop: 'txnAmt',
          render: (h, scope) => {
            return <div>{NumFormat(scope.row.txnAmt)}</div>
          }
        },
        {
          label: '交易时间',
          prop: 'txnTime',
          render: (h, scope) => {
            return <div>{scope.row.txnTime}</div>
          }
        }
      ],
      tableColumn: [
        {
          prop: 'traceNo',
          label: '流水号'
        },
        {
          prop: 'txnTime',
          label: '交易时间',
          width: 160
        },
        {
          prop: 'loanFlag',
          label: '交易类型',
          render: (h, scope) => {
            return <div>{scope.row.loanFlagStr}</div>
          }
        },
        {
          prop: 'txnAmt',
          label: '交易金额（元）',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.txnAmt)}</div>
            )
          }
        },
        {
          prop: 'oppAccountName',
          label: '付款方',
          showOverflowTooltip: true
        },
        {
          prop: 'oppAccountNo',
          label: '付款账号',
          showOverflowTooltip: true
        },
        {
          prop: 'remain',
          label: '交易后余额（元）',
          render: (h, scope) => {
            return (
              <div class="color-warning">{NumFormat(scope.row.remain)}</div>
            )
          }
        },

        {
          label: '业务来源',
          prop: 'businessSourceStr',
          render: (h, scope) => {
            return <div>{scope.row.businessSourceStr}</div>
          }
        }
      ]
    }
  }
}
