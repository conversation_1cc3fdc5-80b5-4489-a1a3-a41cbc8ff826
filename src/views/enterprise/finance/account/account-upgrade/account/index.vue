<template>
  <div class="activity-basic">
    <header-cmp :img="require('../images/header-bg.png')" title="企业账户">
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          详细记录账户余额的收支往来
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content flex">
          <div class="w100 flex account-left justify-content-between">
            <div class="xx">
              <div class="line"></div>
              <div class="tx inline-block"></div>
              <div class="zh">
                <div class="flex align-items-center font-size-16 m-t-13">
                  <span class="income">户名</span>
                  <span>
                    <el-tooltip
                      effect="dark"
                      content="开户行：中国建设银行合肥分行"
                      placement="top-start"
                    >
                      <svg-icon
                        icon-class="help-circle"
                        class-name="tips-icon m-l-4 m-t-4 font-size-16 pointer"
                      />
                    </el-tooltip>
                  </span>
                </div>
                <div class="acconut-name m-t-12">
                  <el-skeleton
                    :rows="1"
                    :loading="cusAcNameLoading(EntInfoToutal.cusAcName)"
                    animated
                  >
                    <template slot="template">
                      <el-skeleton-item variant="text" style="height: 20px">
                      </el-skeleton-item>
                    </template>
                    <template>
                      <div
                        style="
                          font-weight: bold;
                          font-size: 16px;
                          margin-top: 12px;
                          color: #000000;
                        "
                      >
                        {{ NumFormat(EntInfoToutal.cusAcName) }}
                      </div>
                    </template>
                  </el-skeleton>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="line"></div>
              <div class="tx inline-block"></div>
              <div class="zh">
                <div class="flex align-items-center font-size-16 m-t-13">
                  <span class="income">账号</span>
                  <span>
                    <el-tooltip
                      effect="dark"
                      content="该账号是芜湖科创为安徽中安创谷科技园有限公司开设的虚拟云账户"
                      placement="top-start"
                    >
                      <svg-icon
                        icon-class="help-circle"
                        class-name="tips-icon m-l-4 m-t-4 font-size-16 pointer"
                      />
                    </el-tooltip>
                  </span>
                </div>
                <div class="acconut-name m-t-12">
                  <el-skeleton
                    :rows="1"
                    :loading="acBalLoading(EntInfoToutal.cusAc)"
                    animated
                  >
                    <template slot="template">
                      <el-skeleton-item variant="text" style="height: 20px">
                      </el-skeleton-item>
                    </template>
                    <template>
                      <div
                        style="
                          font-weight: bold;
                          font-size: 16px;
                          margin-top: 12px;
                          color: #000000;
                        "
                      >
                        {{ EntInfoToutal.cusAc | noData }}
                      </div>
                    </template>
                  </el-skeleton>
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="line"></div>
              <div class="tx inline-block"></div>
              <div class="zh">
                <div class="flex align-items-center font-size-16 m-t-16">
                  <span class="income">实时余额（元）</span>
                </div>
                <el-skeleton
                  :rows="1"
                  :loading="acBalLoading(EntInfoToutal.acBal)"
                  animated
                >
                  <template slot="template">
                    <el-skeleton-item
                      variant="text"
                      style="height: 20px; margin-top: 15px"
                    >
                    </el-skeleton-item>
                  </template>
                  <template>
                    <div
                      style="
                        font-weight: bold;
                        font-size: 24px;
                        margin-top: 12px;
                        color: #ed7b2f;
                      "
                    >
                      ¥{{ NumFormat(EntInfoToutal.acBal) }}
                    </div>
                  </template>
                </el-skeleton>
              </div>
            </div>
            <div class="xx">
              <div class="line"></div>
              <div class="tx inline-block"></div>
              <div class="zh">
                <div>
                  <div class="flex align-items-center font-size-16 m-t-16">
                    <span class="income">可用余额（元）</span>
                  </div>
                  <el-skeleton
                    :rows="1"
                    :loading="acBalLoading(EntInfoToutal.avaBal)"
                    animated
                  >
                    <template slot="template">
                      <el-skeleton-item
                        variant="text"
                        style="height: 20px; margin-top: 15px"
                      >
                      </el-skeleton-item>
                    </template>
                    <template>
                      <div
                        class="payname m-t-12"
                        style="
                          font-weight: bold;
                          font-size: 24px;
                          margin-top: 12px;
                          color: #ed7b2f;
                        "
                      >
                        ¥{{ NumFormat(EntInfoToutal.avaBal) }}
                      </div>
                    </template>
                  </el-skeleton>
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>

    <div class="lateral-wrapper">
      <div class="m-b-20">
        <div class="flex">
          <div class="m-r-10">
            <el-input
              clearable
              v-model="fromTableInfo.traceNo"
              @input="changeSource($event, 'traceNo')"
              placeholder="请输入银行流水号搜索"
            >
            </el-input>
          </div>
          <div class="m-r-10">
            <el-input
              clearable
              v-model="fromTableInfo.oppAc"
              @input="changeSource($event, 'oppAc')"
              placeholder="请输入对方账号搜索"
            >
            </el-input>
          </div>
          <div class="m-r-10">
            <el-input
              clearable
              v-model="fromTableInfo.oppAcNme"
              @input="changeSource($event, 'oppAcNme')"
              placeholder="请输入对方户名搜索"
            >
            </el-input>
          </div>
          <div class="m-r-10">
            <el-select
              v-model="fromTableInfo.loanFlag"
              placeholder="按借贷标志搜索"
              clearable
              @change="changeSource($event, 'loanFlag')"
            >
              <el-option
                v-for="item in borrowingOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div class="m-r-10">
            <el-date-picker
              v-model="fromTableInfo.time"
              type="daterange"
              range-separator="-"
              @change="changeTime"
              clearable
              value-format="yyyy-MM-dd"
              start-placeholder="交易开始时间"
              end-placeholder="交易结束时间"
            >
            </el-date-picker>
          </div>
        </div>
      </div>

      <drive-table
        ref="drive-table"
        :columns="tableColumn1"
        :api-fn="getEntAccountinPage"
        :extral-querys="fromTableInfo"
      />
    </div>
  </div>
</template>

<script>
// import BasicCard from '../components/BasicCard/index.vue'
import HeaderCmp from '../components/headerComponent/index.vue'
import formConfigureData from '../descriptor'
import ColumnMixins from '../column/list-column'
import { getDetail, getEntAccountinPage } from '../api'
import { NumFormat } from '@/utils/tools'
export default {
  name: 'AccountBasic',
  components: {
    HeaderCmp
    // BasicCard,
  },

  mixins: [ColumnMixins, formConfigureData],
  data() {
    return {
      fromTableInfo: {
        endTxnTime: '',
        startTxnTime: '',
        loanFlag: '',
        time: [],
        oppAcNme: '',
        oppAc: '',
        traceNo: ''
      },
      extralQuerys: {
        turnId: 0
      },
      getEntAccountinPage,
      fromModel: {},
      NumFormat,
      dataSource: {},
      fromAccountInfo: {
        accountType: '',
        tradeTime: '',
        borrowing: ''
      },
      // 晒选条件
      accountTop: {},
      dayOptions: [
        {
          label: '按日',
          value: 2022
        }
      ],
      accountTypeOptions: [
        {
          label: '全部',
          value: 2022
        }
      ],
      borrowingOptions: [
        {
          label: '进账',
          value: 1
        },
        {
          label: '出账',
          value: 2
        }
      ],
      EntInfoToutal: {}
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    cusAcNameLoading(val) {
      return val === undefined || false
    },
    acBalLoading(val) {
      return val === undefined || false || val === ''
    },
    changeTime(e) {
      if (e) {
        this.fromTableInfo.startTxnTime = e[0]
        this.fromTableInfo.endTxnTime = e[1]
        this.$refs['drive-table'].triggerSearch()
      }
    },
    changeSource(e, type) {
      this.fromTableInfo[type] = e
      this.$refs['drive-table'].triggerSearch()
    },
    getDetail() {
      getDetail().then(res => {
        if (res) {
          this.EntInfoToutal = {
            ...res
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}
:deep(.el-date-editor) {
  display: flex;
  justify-content: center;
}

::v-deep(.el-range-editor--small .el-range__close-icon) {
  opacity: 1;
}

:deep(.el-input__inner) {
  height: 30px;
  line-height: 30px;
}
:deep(.el-input--suffix .el-input__inner) {
  padding-right: 15px;
}

:deep(.el-button--info) {
  background-color: #ffffff;
  &:hover {
    border-color: #ed7b2f;
    color: #ed7b2f;
  }
  &:active {
    background: #e7e7e7;
  }
}

.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}

.search-info {
  position: absolute;
  right: 0;
  top: 5px;
  //.el-select {
  //  width: 100px;
  //}
  //.el-date-picker {
  //  width: 100%;
  //}
}
.form-card-container {
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
.w-25 {
  width: 25%;
}
.financial-content {
  padding-top: 10px;
  height: 96px;
  border-radius: 3px 3px 0 0;
  background: rgba(255, 255, 255, 0.8);
  opacity: 1;
  //border: 1px solid #dcdcdc;
}
.right-title {
  color: #616266;
}
.acconutname {
  font-weight: 700;
  color: #191919;
}
.income {
  font-size: 16px;
}
.payname {
  font-weight: bold;
  font-size: 24px;
  @include font_color(--color-warning);
}
.acconut-name {
  font-weight: bold;
  font-size: 16px;
  color: #000000;
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;

  .line {
    width: 1px;
    height: 60px;
    background: #ebedf1;
    border-radius: 0 0 0 0;
    opacity: 1;
    position: absolute;
    left: 0;
    top: 10px;
  }
  .line-last {
    width: 1px;
    height: 60px;
    background: #ebedf1;
    border-radius: 0 0 0 0;
    opacity: 1;
    position: absolute;
    left: -37px;
    top: 10px;
  }
  .zw {
    width: 100%;
    position: absolute;
    top: 0px;
    left: -20px;
  }
  .zh {
    width: 88%;
    position: absolute;
    top: 0px;
    left: 18px;
  }
  .zx {
    width: 88%;
    position: absolute;
    top: 0px;
    left: -20px;
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}
</style>
