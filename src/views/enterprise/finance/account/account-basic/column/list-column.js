import { NumFormat } from '@/utils/tools'
export default {
  data() {
    return {
      tableColumn: [
        {
          label: '流水号',
          prop: 'traceNo',
          width: 180,
          search: {
            type: 'input'
          }
        },
        {
          label: '交易时间',
          prop: 'txnTime',
          width: 120,
          sortable: true,
          search: {
            type: 'daterange'
          }
        },
        {
          label: '到账金额(元)',
          width: 120,
          prop: 'txnAmt'
        },
        {
          label: '账务类型',
          width: 120,
          prop: 'loanFlag',
          search: {
            type: 'select',
            options: [
              {
                label: '进账',
                value: 1
              },
              {
                label: '出账',
                value: 2
              }
            ]
          },
          render: (h, scope) => {
            return <div>{scope.row.loanFlag === 1 ? '进账' : '出账'}</div>
          }
        },
        {
          label: '付款方',
          width: 220,
          prop: 'oppAcNme'
        },
        {
          label: '收款方',
          width: 220,
          prop: 'rcvAccName'
        },
        {
          label: '金额用途',
          width: 120,
          prop: 'purpose',
          renderHeader: () => {
            return (
              <div class="flex">
                <span class="m-r-4">金额用途</span>
                <el-tooltip placement="top">
                  <div slot="content" class="font-14">
                    <p style="text-indent: 10px;margin-bottom:6px">
                      A类：可在园区任意消费场景下支付，支持申请退款且不限定任何时间
                    </p>
                    <p style="text-indent: 10px;margin-bottom:6px">
                      B类：可在园区任意消费场景下支付，支持申请退款但仅限于特定时间
                    </p>
                    <p style="text-indent: 10px;margin-bottom:6px">
                      C类：可在园区任意场景场景下支付，不支持申请退款
                    </p>
                    <p style="text-indent: 10px;margin-bottom:6px">
                      D类：仅限于园区内定向结算，不可申请付款
                    </p>
                    <p style="text-indent: 10px;margin-bottom:6px">
                      E类：不限于园区定向结算，仅离园时可退
                    </p>
                    <p style="text-indent: 10px;margin-bottom:6px">
                      F类：不限于园区定向结算，随时可退
                    </p>
                  </div>
                  <div>
                    <i class="el-icon-info"></i>
                  </div>
                </el-tooltip>
              </div>
            )
          },
          search: {
            type: 'select',
            options: [
              {
                label: 'A类',
                value: 'A'
              },
              {
                label: 'B类',
                value: 'B'
              },
              {
                label: 'C类',
                value: 'C'
              },
              {
                label: 'D类',
                value: 'D'
              },
              {
                label: 'E类',
                value: 'E'
              },
              {
                label: 'F类',
                value: 'F'
              }
            ]
          },
          render: (h, scope) => {
            return <div>{scope.row.purpose}</div>
          }
        },
        {
          label: '历史金额(元)',
          prop: 'bfBal',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.bfBal)}</div>
            )
          }
        },
        {
          label: '当前余额(元)',
          prop: 'remain',
          width: 120,
          render: (h, scope) => {
            return (
              <div class={'color-warning'}>{NumFormat(scope.row.remain)}</div>
            )
          }
        }
      ]
    }
  }
}
