export function getBillStatus(h, val) {
  switch (val) {
    case 1:
      return <basic-tag isDot type="warning" label="待核销"></basic-tag>
    case 2:
      return <basic-tag isDot type="primary" label="部分核销"></basic-tag>
    case 3:
      return <basic-tag isDot type="success" label="已核销"></basic-tag>
    case 4:
      return <basic-tag isDot type="danger" label="已作废"></basic-tag>
    default:
      return '-'
  }
}

export function getBillStatusLabelForTag(val) {
  switch (val) {
    case 1:
      return { type: 'warning', label: '待核销' }
    case 2:
      return { type: 'warning', label: '部分核销' }
    case 3:
      return { type: 'success', label: '已核销' }
    case 4:
      return { type: 'danger', label: '已作废' }
    default:
      return { type: 'primary', label: '-' }
  }
}

export function getTransactionStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="danger" label="处理中"></basic-tag>
    case 1:
      return <basic-tag isDot type="success" label="成功"></basic-tag>
    case 2:
      return <basic-tag isDot type="warning" label="失败"></basic-tag>
    default:
      return '-'
  }
}

export function getPayType(val) {
  switch (val) {
    case 1:
      return '转账支付'
    case 2:
      return '现金支付'
    case 3:
      return '承诺汇票'
    case 4:
      return '其他'
    default:
      return '未知'
  }
}

export function getacStatusType(val) {
  switch (val) {
    case 0:
      return '未确认'
    case 1:
      return '已确认'
    case 2:
      return '已撤回'
    default:
      return '未知'
  }
}

export function getPayStatus(val) {
  switch (val) {
    case 0:
      return '未到账'
    case 1:
      return '已到账'
    default:
      return '未知'
  }
}

export function getEntryType(val) {
  switch (val) {
    case 1:
      return '企业登记'
    case 2:
      return '园区登记'
    default:
      return '未知'
  }
}

export function getEntryStatus(val) {
  switch (val) {
    case 0:
      return '未确认'
    case 1:
      return '已确认'
    default:
      return '-'
  }
}
