<template>
  <div class="activity-basic">
    <header-cmp :img="require('./images/header-bg.png')" title="账户信息">
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          汇总统计并展示账户相关重要信息
        </div>
      </template>
      <template v-slot:content>
        <div class="financial-content bg-white flex">
          <div class="w100 flex account-left bg-white justify-content-between">
            <div class="xx">
              <div class="zh">
                <div class="flex align-items-center font-size-16 m-t-10">
                  <svg-icon
                    icon-class="account-name"
                    class="font-size-24 color-success m-r-6"
                  ></svg-icon>
                  <span class="income">户名</span>
                </div>

                <div class="acconutname enter m-t-12 font-size-16">
                  <span>{{ EntInfoToutal.cusAcName | noData }}</span>

                  <!-- 310066661013004281158 -->
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="line"></div>
              <div class="tx inline-block"></div>
              <div class="zh">
                <div class="flex align-items-center font-size-16 m-t-10">
                  <svg-icon
                    icon-class="user-circle"
                    class="font-size-24 color-primary m-r-6"
                  ></svg-icon>
                  <span class="income">账号</span>
                </div>

                <div class="acconut-name enter m-t-12 font-size-16">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="EntInfoToutal.cusAc"
                    placement="top"
                  >
                    <span>{{ EntInfoToutal.cusAc | noData }}</span>
                  </el-tooltip>

                  <!-- 上海分行营业部测试五三 -->
                </div>
              </div>
            </div>
            <div class="xx">
              <div class="line"></div>
              <div class="tx inline-block"></div>
              <div class="zh">
                <div class="flex align-items-center font-size-16 m-t-10">
                  <svg-icon
                    icon-class="gross-receipts"
                    class="font-size-24 color-warning m-r-6"
                  ></svg-icon>
                  <span class="income">实时余额(元)</span>
                </div>
                <div class="payname m-t-12 font-size-16">
                  ¥{{ NumFormat(EntInfoToutal.avaBal) }}
                  <!-- ￥145,215.55 -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>

    <div class="lateral-wrapper">
      <basic-card :is-title="false">
        <basic-tab
          ref="basicTab"
          :tabs-data="list"
          :current="current"
          @tabsChange="tabsChange"
        />
        <div class="flex justify-content-between m-b-20">
          <div class="flex" style="width: 45%">
            <div class="m-r-10">
              <el-select v-model="accountTop.year" placeholder="请选择">
                <el-option
                  v-for="item in yearOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div class="m-r-10">
              <el-select v-model="accountTop.quarter" placeholder="请选择">
                <el-option
                  v-for="item in quarterOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
            <div>
              <el-date-picker
                v-model="accountTop.date"
                type="date"
                placeholder="选择日期"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </div>
          </div>
          <!--          <div class="flex">-->
          <!--            <div class="flex justify-content-end">-->
          <!--              <div style="line-height: 32px;">-->
          <!--                <span style="color: rgba(0, 0, 0, 0.90);font-weight: 350" class="font-size-14">同比观察</span>-->
          <!--                <svg-icon icon-class="help-circle" class="font-size-13" class-name="m-l-5 m-r-5 icon-size" />-->
          <!--              </div>-->
          <!--              <div style="width: 40%">-->
          <!--                <el-select v-model="accountTop.day" placeholder="请选择">-->
          <!--                  <el-option-->
          <!--                      v-for="item in dayOptions"-->
          <!--                      :key="item.value"-->
          <!--                      :label="item.label"-->
          <!--                      :value="item.value">-->
          <!--                  </el-option>-->
          <!--                </el-select>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </div>-->
        </div>
        <div v-if="current === 0">
          <finance-column-echarts :dataSource="dataSource" />
        </div>
        <div v-if="current === 1">
          <total-funds :dataSource="dataSource" :info="EntInfoToutal" />
        </div>
      </basic-card>
    </div>
  </div>
</template>

<script>
import HeaderCmp from './components/headerComponent'
import ColumnMixins from './column/list-column'
// import TableSearch from "@/views/enterprise/finance/account/account-basic/components/search";
import { accountGetEntInfo, getEntAccInfo } from './api/index'
import FinanceColumnEcharts from './components/financeColumnEcharts/index'
import BasicTab from './components/BasicTab'
import TotalFunds from './components/TotalFunds'
import { NumFormat } from '@/utils/tools'
export default {
  name: 'AccountBasic',
  components: {
    // TableSearch,
    TotalFunds,
    HeaderCmp,
    BasicTab,
    FinanceColumnEcharts
  },
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      dataSource: {},
      fromAccountInfo: {},
      // 晒选条件
      accountTop: {},
      yearOptions: [
        {
          label: '2022',
          value: 2022
        }
      ],
      quarterOptions: [
        {
          label: '第四季度',
          value: 2022
        }
      ],
      dayOptions: [
        {
          label: '按日',
          value: 2022
        }
      ],
      // tableList: [
      //   {
      //     data: '************',
      //     data2: '**************',
      //     data3: '明珠产业园',
      //     data4: '进账',
      //     data5: '68,468',
      //     data6: '2021-04-27'
      //   }
      // ],
      list: [
        {
          label: '账户信息',
          value: 0
        },
        {
          label: '资金合计',
          value: 1
        }
      ],
      current: 0,
      EntInfoToutal: {}
    }
  },
  // mounted() {
  //
  // },
  created() {
    this.accountGetEntInfo()
  },
  mounted() {
    this.getEntAccInfo()
  },
  methods: {
    getEntAccInfo() {
      getEntAccInfo().then(res => {
        if (res) {
          this.dataSource = JSON.parse(JSON.stringify(res))
        }
      })
    },
    accountGetEntInfo() {
      accountGetEntInfo().then(res => {
        this.EntInfoToutal = res
      })
    },
    //切换
    tabsChange(e) {
      this.current = e
    },
    // resetForm() {
    //   this.$refs.tableSearch && this.$refs.tableSearch.resetForm()
    // },
    // resetSearch() {
    //   this.$refs.driveTable && this.$refs.driveTable.resetSearch()
    // },
    // searchSubmit() {
    //   this.$refs.tableSearch && this.$refs.tableSearch.searchSubmit()
    // },
    triggerSearch(formQuerys) {
      this.$refs.driveTable && this.$refs.driveTable.triggerSearch(formQuerys)
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}
.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}
.form-card-container {
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
.w-25 {
  width: 25%;
}
.financial-content {
  padding-top: 10px;
  height: 96px;
  border-radius: 3px 3px 0 0;
  opacity: 1;
  //border: 1px solid #dcdcdc;
}
.right-title {
  color: #616266;
}
.acconutname {
  font-weight: 700;
  color: #191919;
}
.income {
  font-size: 16px;
  font-weight: 700;
}
.payname {
  font-weight: 700;
  @include font_color(--color-warning);
}
.xx {
  position: relative;
  width: 100%;
  height: 100px;

  .line {
    width: 1px;
    height: 60px;
    background: #ebedf1;
    border-radius: 0 0 0 0;
    opacity: 1;
    position: absolute;
    left: 0;
    top: 10px;
  }
  .zh {
    width: 88%;
    position: absolute;
    top: 0;
    left: 32px;

    .enter {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.sx {
  width: 112px;
  height: 32px;
  line-height: 32px;
  @include background-color(--border-color-light);
}
.xd {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  opacity: 1;
}
</style>
