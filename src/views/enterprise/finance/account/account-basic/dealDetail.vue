<template>
  <div class="dealInfo">
    <module-header
      type="primary"
      title="交易账单详情"
      :desc="dealNo"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    >
      <!--       <span>hello world</span>-->
      <div class="title-container">
        <h1 class="font-strong font-size-20 m-b-36">交易账单详情</h1>
        <div
          class="font-size-14 flex justify-content-between align-items-center"
        >
          <div>
            <span class="span-frist" style="color: rgba(0, 0, 0, 0.6)"
              >交易登记企业&nbsp;</span
            ><span class="span-value">
              {{ PayRecordInfo.entName | noData }}</span
            >
            <span class="span-frist" style="color: rgba(0, 0, 0, 0.6)"
              >交易登记时间&nbsp;</span
            ><span class="span-value"
              >{{ PayRecordInfo.createTime }}&emsp;&emsp;&emsp;&emsp;</span
            >
            <span class="span-frist" style="color: rgba(0, 0, 0, 0.6)"
              >交易登记人&nbsp;</span
            ><span class="span-value">{{ PayRecordInfo.userName }} </span>
          </div>
          <!--          v-if="acStatus === 0"-->
          <el-button
            type="info"
            v-if="PayRecordInfo.acStatus === 0"
            @click="recallHandler"
            >撤回</el-button
          >
        </div>
      </div>
    </module-header>

    <div class="body-detail" v-if="PayRecordInfo">
      <div style="width: 1200px">
        <el-row style="width: 1200px" :gutter="8">
          <el-col :span="PayRecordInfo.acStatus === 0 ? 24 : 18">
            <div class="body-detail-right-01"></div>
            <!--        转账支付详情-->
            <basic-card
              class="w100"
              title="登记内容"
              v-if="PayRecordInfo.type === 1"
            >
              <div>
                <div>
                  <span class="label">线下交易方式</span>
                  <span class="value">{{
                    getPayType(PayRecordInfo.type) | noData
                  }}</span>
                </div>
                <div
                  class="flex justify-content-between align-items-center m-b-24 m-t-10"
                >
                  <div>
                    <span class="label">银行回单</span>
                  </div>
                  <div>
                    <span class="label black-color">回单金额</span>
                    <span class="value mony">{{
                      PayRecordInfo.payStatus == 0
                        ? '待确认'
                        : NumFormat(PayRecordInfo.acAmount)
                    }}</span>
                    <el-button
                      @click="downHandler"
                      type="primary"
                      size="small"
                      class="m-l-24"
                      >下载回单
                    </el-button>
                  </div>
                </div>
              </div>
              <div class="identification-area">
                <el-row :gutter="8">
                  <el-col :span="17">
                    <div
                      class="wh100"
                      v-if="attachList && attachList.length > 0"
                    >
                      <div
                        class="bill-img-area pointer"
                        @click="receiptHandler"
                      >
                        <el-image
                          class="w100 enlarge-img"
                          :src="attachList[imageIndex].path"
                        ></el-image>
                      </div>
                      <div class="img-collective">
                        <div
                          class="bill-img-area-m"
                          v-for="(item, index) in attachList"
                          :key="index"
                        >
                          <el-image
                            class="inline-block"
                            :style="getImageStyle(index)"
                            :src="attachList[index].path"
                            @click="imageIndex = index"
                          ></el-image>
                        </div>
                      </div>
                    </div>
                    <div v-else class="wh100 enlarge-img">
                      <empty-data />
                    </div>
                  </el-col>
                  <el-col :span="7">
                    <div class="result-body">
                      <div class="p-b-24">识别结果</div>
                      <div
                        v-for="(item, index) in billImgList[imageIndex].data
                          .ret"
                        :key="index"
                        class="flex"
                      >
                        <span
                          v-if="'others' != item.word_name"
                          class="spanTitle"
                          >{{ item.word_name }}</span
                        >
                        <span
                          class="w100 inline-block spanDetail"
                          v-if="'others' != item.word_name"
                          >{{ item.word }}</span
                        >
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </basic-card>
            <!--        其他支付详情-->
            <basic-card title="登记内容" v-if="PayRecordInfo.type !== 1">
              <div class="register">
                <div class="offline">
                  <span class="label">线下交易方式</span>
                  <span class="value">{{
                    getPayType(PayRecordInfo.type) | noData
                  }}</span>
                </div>
                <div>
                  <div class="m-b-16">收款人信息</div>
                  <div class="info">
                    <el-row>
                      <el-col :span="10">
                        <div>
                          <span class="label">姓名</span>
                          <span class="value">{{
                            PayRecordInfo.payRecordDetailList &&
                            PayRecordInfo.payRecordDetailList.length > 0
                              ? PayRecordInfo.payRecordDetailList[0].oppUserName
                              : '-'
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="14">
                        <div>
                          <span class="label">联系方式</span>
                          <span class="value">{{
                            PayRecordInfo.payRecordDetailList &&
                            PayRecordInfo.payRecordDetailList.length > 0
                              ? PayRecordInfo.payRecordDetailList[0]
                                  .oppUserPhone
                              : '-'
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
                <div class="m-t-30 m-b-30">
                  <div class="m-b-16">付款人信息</div>
                  <el-row>
                    <el-col :span="10">
                      <div>
                        <span class="label">姓名</span>
                        <span class="value">{{
                          PayRecordInfo.payRecordDetailList &&
                          PayRecordInfo.payRecordDetailList.length > 0
                            ? PayRecordInfo.payRecordDetailList[0].rcvUserName
                            : '-'
                        }}</span>
                      </div>
                    </el-col>
                    <el-col :span="14">
                      <div>
                        <span class="label">联系方式</span>
                        <span class="value">{{
                          PayRecordInfo.payRecordDetailList &&
                          PayRecordInfo.payRecordDetailList.length > 0
                            ? PayRecordInfo.payRecordDetailList[0].rcvUserPhone
                            : '-'
                        }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <div class="w100 m-t-30 m-b-30">
                  <div class="m-b-16">支付信息</div>
                  <div class="m-b-16">
                    <el-row>
                      <el-col :span="10">
                        <div>
                          <span class="label">现金金额</span>
                          <span class="value mony">{{
                            PayRecordInfo.amount | noData
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="14">
                        <div>
                          <span class="label">支付时间</span>
                          <span class="value">{{
                            PayRecordInfo.traceTime | noData
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div class="m-b-16">
                    <el-row>
                      <el-col :span="10">
                        <div class="flex">
                          <span class="label">相关附件</span>
                          <span class="value flex">
                            <span v-if="viewData.length > 0">
                              <Uploader
                                v-model="viewData"
                                type="avatar"
                                mulity
                                onlyForView
                              />
                            </span>
                            <span v-else>-</span>
                          </span>
                        </div>
                      </el-col>
                      <el-col :span="14">
                        <div class="m-b-16">
                          <span class="label inline-block">备注信息</span>
                          <span class="value remarks">{{
                            PayRecordInfo.payRecordDetailList &&
                            PayRecordInfo.payRecordDetailList.length > 0
                              ? PayRecordInfo.payRecordDetailList[0].remark
                              : '-'
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </basic-card>
          </el-col>
          <el-col :span="6" v-if="PayRecordInfo.acStatus">
            <basic-card class="w100" title="最终确认信息">
              <div class="confirmation-info line-height-38">
                <div>
                  <span class="label">确认状态</span>
                  <span class="value">{{
                    getEntryStatus(PayRecordInfo.acStatus)
                  }}</span>
                </div>
                <div>
                  <span class="label">到账状态</span>
                  <span class="value">{{
                    PayRecordInfo.payStatus == 0 ? '未到账' : '已到账' | noData
                  }}</span>
                </div>
                <div>
                  <span class="label">到账时间</span>
                  <span class="value">{{
                    parseTime(PayRecordInfo.traceTime, '{y}-{m}-{d}') | noData
                  }}</span>
                </div>
                <div>
                  <span class="label">到账金额</span>
                  <span class="value">{{
                    NumFormat(PayRecordInfo.acAmount) | noData
                  }}</span>
                </div>
                <div>
                  <span class="label confirm">确认人</span>
                  <span class="value">{{
                    PayRecordInfo.confirmName | noData
                  }}</span>
                </div>
                <div class="flex">
                  <span class="label" style="width: 58px">确认时间</span>
                  <span class="value line-1">{{
                    PayRecordInfo.confirmTime | noData
                  }}</span>
                </div>
                <div>
                  <span class="label">备注信息</span>
                  <span class="value">{{ PayRecordInfo.remark | noData }}</span>
                </div>
                <div>
                  <span class="label">相关附件</span>
                  <span
                    class="value color-primary pointer"
                    v-if="confirmAttachList.length > 0"
                    @click="open"
                    >{{ '查看' }}</span
                  >
                  <span class="value" v-else>{{ '暂无附件' }}</span>
                  <!--                <span class="value">{{ '张三（15256565656）' | noData }}</span>-->
                </div>
              </div>
            </basic-card>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 查看附件详情 -->
    <dialog-cmp
      title="附件详情"
      :visible.sync="visibleSee"
      width="30%"
      :haveOperation="false"
    >
      <div>
        <Uploader v-model="viewList" type="avatar" mulity onlyForView />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import downloads from '@/utils/download'
import ModuleHeader from '@/components/Lateral/ModuleHeader/dealDetailHeader.vue'
import ColumnMixins from './column/dealInfo-column'
import { getPayRecordDetail, getWithdraw } from './api'
import {
  getPayType,
  getacStatusType,
  getPayStatus,
  getEntryStatus
} from './utils/status'
import { NumFormat, parseTime } from '@/utils/tools'
import moment from 'moment'

export default {
  name: 'AccountDealInfo',
  components: {
    ModuleHeader
  },
  mixins: [ColumnMixins],
  data() {
    return {
      getEntryStatus,
      getPayType,
      parseTime,
      NumFormat,
      getacStatusType,
      getPayStatus,
      dealNo: '', //账单编号,
      payType: 0, //支付类型 0为转账支付  1为此外其他
      src: 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg',
      billImgList: [],
      imageIndex: 0,
      imageStyle: "'border': '2px solid #054CE8;'",
      registerList: [], //识别内容
      attachList: [], //识别图片
      dataList: [], //全部数据
      PayRecordInfo: {},
      fromModelTransaction: {}, //确认表单
      transactionVisible: false, //确认弹框
      viewData: [], //附件列表,
      oppUserName: null,
      oppUserPhone: null,
      rcvUserName: null,
      rcvUserPhone: null,
      remark: null,
      viewList: [],
      visibleSee: false,
      confirmAttachList: []
    }
  },
  mounted() {
    this.changeImg(0)
    //数据初始化
    this.getPayRecord()
  },
  methods: {
    open() {
      if (this.confirmAttachList.length > 0) {
        this.viewList = this.confirmAttachList
        this.visibleSee = true
      } else {
        this.$message.warning('暂无附件')
      }
    },
    // 撤回
    async recallHandler() {
      this.$confirm('确定撤回该账单？').then(async () => {
        let id = this.$route.query.id
        await getWithdraw({ id })
        this.$toast.success('撤回成功')
        this.getPayRecord()
      })
    },
    // 下载回执单
    downHandler() {
      if (this.attachList.length === 0) return this.$toast.warning('暂无数据')
      downloads.addressDownload(this.attachList[0])
    },
    // 回执单预览
    receiptHandler() {
      if (this.attachList.length > 0 && this.attachList) {
        console.log(this.attachList)
        this.viewList = this.attachList
        this.visibleSee = true
      } else {
        this.$message.warning('暂无附件')
      }
    },
    //初获取数据
    async getPayRecord() {
      const data = await getPayRecordDetail({ id: this.$route.query.id })
      this.PayRecordInfo = JSON.parse(JSON.stringify(data))
      this.seeAttachDetails(data)
      this.attachList = this.PayRecordInfo.attachMap?.driving
      this.confirmAttachList =
        this.PayRecordInfo.confirmAttachMap?.informationAttach || []
      // this.billImgList = data.payRecordDetailList
      data.payRecordDetailList.forEach(element => {
        this.billImgList.push(JSON.parse(element.identifyResult))
      })
      if (data.payRecordDetailList.length > 0) {
        this.changeImg(0)
      }
    },
    moments(e) {
      if (e === null) {
        return '-'
      }
      return moment(e).format('yyyy-MM-DD hh:mm:ss')
    },
    //切换图片页面
    changeImg(index) {
      this.indexImg = index
    },
    getImageStyle(index) {
      if (index === this.imageIndex) {
        return 'border: 2px solid #054CE8;'
      }
      return ''
    },
    seeAttachDetails(row) {
      if (Object.keys(row.attachMap).length > 0) {
        this.viewData = row.attachMap?.driving || []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.span-value {
  margin-right: 20px;
}
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}

.title-container {
  margin-top: 80px;
  line-height: 22px;
}

.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}
.form-card-container {
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
.body-detail {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  .body-detail-right-01 {
    width: 19%;
  }
  .body-detail-right {
    width: 52%;
    height: 100%;
    margin-bottom: 16px;
    border-right: 1px solid #e9f0ff;
    .body-detail-right-title {
      border-bottom: 1px solid #e9f0ff;
      span {
        display: inline-block;
        height: 42px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.4);
        line-height: 22px;
      }
    }
    .body-detail-right-body {
      height: 550px;
      height: 100%;
      margin-bottom: 16px;
      border-right: 1px solid #e9f0ff;
      span {
        display: inline-block;
        height: 42px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.4);
        line-height: 22px;
      }
    }
    .body-detail-right-body-payType01 {
      background-color: #e7e7e7;
      display: inline-block;
      width: 933px;
      .body-detail-right-body-payType01-left {
        width: 583px;
        height: 524px;
        float: left;
        .el-image {
          display: inline-block;
          width: 137px;
          height: 88px;
          margin-left: 8px;
          margin-top: 8px;
        }
      }
      .body-detail-right-body-payType01-right {
        margin-left: 16px;
        margin-top: 8px;
        width: 326px;
        height: 524px;
        background-color: #fff;
        float: left;

        span {
          display: inline-block;
          height: 42px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          width: 100px; /* 要显示文字的宽度 */
          text-overflow: ellipsis; /* 让截断的文字显示为点点。还有一个值是clip意截断不显示点点 */
          white-space: nowrap; /* 让文字不换行 */
          overflow: hidden; /* 超出要隐藏 */
        }
        .spanDetail {
          display: inline-block;
          height: 42px;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.4);
          width: 140px; /* 要显示文字的宽度 */
          text-overflow: ellipsis; /* 让截断的文字显示为点点。还有一个值是clip意截断不显示点点 */
          white-space: nowrap; /* 让文字不换行 */
          overflow: hidden; /* 超出要隐藏 */
        }
      }
    }
  }
  .body-detail-left {
    margin-top: 10px;
    margin-left: 20px;
    width: 30%;
    height: 30px;
    margin-bottom: 16px;
    p {
      width: 48px;
      height: 20px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.4);
      line-height: 20px;
      display: block;
      margin-top: 16px;
    }
    span {
      width: 220px;
      font-size: 12px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.9);
      line-height: 20px;
      display: block;
      margin-top: 12px;
    }
  }
}

.img-collective {
  width: 100%;
  //  gird一行显示5个
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 20px;
}

.enlarge-img {
  height: 430px;
}

.result-body {
  background-color: #fff;
  border-radius: 3px;
  padding: 24px;
  box-sizing: border-box;
  padding-bottom: 8px;
}

.spanTitle {
  display: inline-block;
  height: 31px;
  text-align: right;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.4);
  width: 28%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.spanDetail {
  display: inline-block;
  margin-left: 10px;
  height: 31px;
  font-size: 14px;
  width: 70%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.label {
  font-size: 14px;
  color: #999;
  margin-right: 8px;
}

.label-t {
  width: 100px;
  font-size: 14px;
  color: #999;
  display: inline-block;
  text-align: right;
  margin-right: 8px;
}

.value {
  font-size: 14px;
  color: #1a1a1a;
}

.basic {
  width: 80%;
}

.confirm {
  width: 56px;
  text-align: left;
  display: inline-block;
}

.mony {
  color: #ed7b2f;
}

.black-color {
  color: #1a1a1a;
}

.identification-area {
  width: 100%;
  //height: 100%;
  background: #f0f2f5;
  border-radius: 3px;
  padding: 8px;
}

.bill-img-area {
  width: 100%;
  border-radius: 3px;
}

.bill-img-area-min {
  width: 100%;
  height: 100px;
  background: #999;
  border-radius: 3px 3px 3px 3px;
  display: inline-block;
  cursor: pointer;
}

.bill-img-area-min-border {
  border: 1px solid #ed7b2f;
}

.bill-img-area-m {
  display: inline-block;

  .el-image {
    margin-top: 4px;
    display: inline-block;
    width: 100%;
    height: 100px;
  }
}

.confirmation-info {
  width: 100%;
  //  高度是可视区域的高度
  height: calc(100vh - 162px);
}

.min-img {
  width: 100%;
  height: 100%;
}

.offline {
  padding-bottom: 16px;
  border-bottom: 1px solid #e7e7e7;
  margin-bottom: 16px;
}

.info {
  width: 100%;
}

.pay-info {
  width: 55%;
}

.remarks {
  width: 86%;
  display: inline-flex;
  line-height: 22px;
}

.register {
  width: 100%;
  height: calc(100vh - 316px);
  // span{
  //   margin-left: 30px;
  // }
}
</style>
