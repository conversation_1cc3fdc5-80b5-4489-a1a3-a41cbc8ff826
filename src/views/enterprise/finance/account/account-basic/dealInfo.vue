<template>
  <div class="dealInfo">
    <module-header
      type="primary"
      title="交易登记"
      desc="详细记录账户余额的收支来往"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
      @getDataFormat="getDataFormat"
    >
    </module-header>

    <div class="body-detail">
      <drive-table
        class="m-t-20"
        ref="drive-table"
        :columns="tableColumnToday"
        :api-fn="getEntRecordList"
        :extralQuerys="queryFromat"
      >
      </drive-table>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader/dealInfoHeader.vue'
import ColumnMixins from './column/dealInfo-column'
import Column from './column/column'
import { getEntRecordList } from './api'
// import getPayType from './utils/status'

export default {
  name: 'AccountDealInfo',
  components: {
    // TableSearch,
    ModuleHeader
  },
  mixins: [ColumnMixins, Column],
  data() {
    return {
      getEntRecordList,
      extralQuerys: {
        dateType: 1
      },
      queryFromat: {},
      typeAll: false //是否显示全部列表
    }
  },
  methods: {
    goTransactionDetails(row) {
      let { id, orderId } = row
      this.$router.push({
        path: '/finance/account/financeDetail',
        query: {
          id,
          orderId
        }
      })
    },
    getDataFormat(dataFormat) {
      this.queryFromat.acStatus = dataFormat.acStatus
      this.queryFromat.payStatus = dataFormat.payStatus
      this.queryFromat.beginTraceTime = dataFormat.timeSelect
        ? dataFormat.timeSelect[0]
        : ''
      this.queryFromat.endTraceTime = dataFormat.timeSelect
        ? dataFormat.timeSelect[1]
        : ''
      this.$refs['drive-table'].refreshTable()
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.module-header) {
  .lateral-wrapper {
    align-items: flex-start;
  }
}
.lateral-wrapper {
  margin-top: 24px;
  margin-bottom: 24px;
  height: calc(100% - 220px);
}
.form-card-container {
  border-radius: 6px 6px 6px 6px;
  border: 1px solid #e9f0ff;
}
.body-detail {
  width: 1200px;
  margin: 0 auto;
}
</style>
