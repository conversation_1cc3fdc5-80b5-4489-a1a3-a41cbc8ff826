<template>
  <div class="accounting-information">
    <header-cmp :img="require('../images/header-bg.png')" title="账务明细">
      <template v-slot:title-right>
        <div class="right-title font-size-14 p-l-16 p-t-6">
          详细记录账户余额的收支往来
        </div>
      </template>
      <template v-slot:content>
        <div class="flex align-items-center w100 info-container">
          <div v-for="(item, index) in infoList" :key="index" class="info-item">
            <div :class="index === 0 ? 'm-r-34' : 'info-item-left-line'"></div>
            <div>
              <div class="m-b-10 info-title">{{ item.name }}</div>
              <div
                :class="index === 0 ? 'info-value' : 'info-value color-warning'"
              >
                {{ item.value }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </header-cmp>
    <div class="lateral-wrapper">
      <basic-card :is-title="false">
        <drive-table
          class="m-t-24"
          ref="drive-table"
          :columns="tableColumn"
          :api-fn="getAccDetailList"
          :search-querys-hook="searchQueryHook"
        />
      </basic-card>
    </div>
  </div>
</template>
<script>
import HeaderCmp from '../components/headerComponent'
import ColumnMixins from '../column/list-column'
// import TableSearch from "@/views/enterprise/finance/account/account-basic/components/search";
import { NumFormat } from '@/utils/tools'
import { getAccDetailList, getEntAccStatic } from '../api/index'
export default {
  name: 'AccountingInformation',
  components: {
    // TableSearch,
    HeaderCmp
  },
  provide() {
    return {
      AccountingInformation: this
    }
  },
  mixins: [ColumnMixins],
  data() {
    return {
      NumFormat,
      getAccDetailList,
      tableData: [],
      infoList: []
    }
  },
  mounted() {
    this.getEntAccStatic()
  },
  methods: {
    getEntAccStatic() {
      getEntAccStatic().then(res => {
        if (res) {
          this.infoList = [
            {
              name: '交易笔数(笔)',
              value: res.count
            },
            {
              props: '',
              name: '交易总金额(元)',
              value: NumFormat(res.total)
            },
            {
              props: '',
              name: '入账总金额(元)',
              value: NumFormat(res.income)
            },
            {
              props: '',
              name: '出账总金额(元)',
              value: NumFormat(res.out)
            }
          ]
        }
      })
    },
    // resetForm() {
    //   this.$refs.tableSearch && this.$refs.tableSearch.resetForm()
    // },
    // resetSearch() {
    //   this.$refs.driveTable && this.$refs.driveTable.resetSearch()
    // },
    // searchSubmit() {
    //   this.$refs.tableSearch && this.$refs.tableSearch.searchSubmit()
    // },
    // 重置搜索参数
    searchQueryHook(e) {
      let [beginTxnTime = '', endTxnTime = ''] = e.txnTime || []
      if (e.txnTime && e.txnTime.length > 1) {
        beginTxnTime = beginTxnTime + ' ' + '00:00:00'
        endTxnTime = endTxnTime + ' ' + '23:59:59'
      }
      delete e.txnTime
      return {
        ...e,
        beginTxnTime,
        endTxnTime
      }
    },
    triggerSearch(formQuerys) {
      this.$refs.driveTable && this.$refs.driveTable.triggerSearch(formQuerys)
    }
  }
}
</script>
<style lang="scss" scoped>
.right-title {
  color: #616266;
}
.info-container {
  background-color: #fff;
}
.info-item {
  padding: 20px;
  width: 25%;
  display: flex;
  align-items: center;
  .info-title {
    font-size: 16px;
    font-weight: 400;
    color: #191919;
    line-height: 24px;
  }
  .info-value {
    font-size: 24px;
    font-weight: 500;
    color: #191919;
    line-height: 28px;
  }
}
.info-item-left-line {
  width: 1px;
  height: 60px;
  background: #ebedf1;
  margin-right: 40px;
}
</style>
