import request from '@/utils/request'

// 获取活动列表
export function getPolicyList(params) {
  return request({
    url: `/hatch/ent/policy/selectPage`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取活动信息
export function getPolicyDetail(id) {
  return request({
    url: `/hatch/ent/policy/get?id=${id}`,
    method: 'get'
  })
}
// 获取活动列表
export function getAccountList(params) {
  return request({
    url: `/pay/record/page`,
    method: 'get',
    isTable: true,
    type: 'admin-api',
    params
  })
}

// 企业端获取交易记录分页
export function getEntRecordList(params) {
  return request({
    url: `/pay/record/pay_page_ent`,
    method: 'get',
    isTable: true,
    params
  })
}
// 获取活动列表
export function getAccountListToday(params) {
  return request({
    url: `/pay/record/page`,
    method: 'get',
    isTable: true,
    type: 'admin-api',
    params
  })
}

//获取登记详情
export function getPayRecordDetail(params) {
  return request({
    url: `/pay/record/get`,
    method: 'get',
    isTable: true,
    type: 'admin-api',
    params
  })
}

// 获取企业端账务信息列表
export function getAccDetailList(params) {
  return request({
    url: `/pay/acc_detail/page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 查询企业账户信息/admin-api/pay/account/get_ent_acc_info
export function accountGetEntInfo(params) {
  return request({
    url: `/pay/account/get_ent_acc_info`,
    method: 'get',
    params
  })
}

// 获取企业交易明细统计信息
export function getEntAccStatic() {
  return request({
    url: `/pay/account/get_ent_acc_static`,
    method: 'get'
  })
}

// 查询企业账户信息
export function getEntAccInfo() {
  return request({
    url: `/pay/account/get_ent_acc_info`,
    method: 'get'
  })
}

// 撤回交易登记
export function getWithdraw(params) {
  return request({
    url: `/pay/record/withdraw`,
    method: 'get',
    params
  })
}
