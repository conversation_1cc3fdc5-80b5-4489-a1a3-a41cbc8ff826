<template>
  <div class="total-funds">
    <el-row :gutter="24">
      <el-col :span="8">
        <div class="capital-echarts">
          <div class="time">截止2022/11-27</div>
          <div ref="capital-echar" class="capital-echar"></div>
          <div class="compare">同比昨日</div>
        </div>
      </el-col>
      <el-col :span="16">
        <div class="total-data">
          <div
            class="item-wrapper pos-relative"
            v-for="(t, i) in totalData"
            :key="i"
          >
            <div
              :class="`total-data-item ${
                i === 2 || i === 3 ? 'total-data-item-b' : 'total-data-item-t'
              }`"
              ref="total-data-item"
            >
              <div class="top p-b-14 flex justify-content-between">
                <div class="">
                  <span class="header_title">{{ t.title }}</span>
                </div>
                <div>
                  <span class="mony font-size-24">¥</span>
                  <span class="mony font-size-24">{{ NumFormat(t.mony) }}</span>
                </div>
              </div>
              <div class="body">
                <div class="flex" v-for="(item, v) in t.eachMony" :key="v">
                  <span class="title">{{ item.title }}</span>
                  <span>
                    <span>
                      <el-tooltip
                        class="item"
                        effect="dark"
                        :content="item.mony"
                        placement="top"
                      >
                        <svg-icon icon-class="prompt" />
                      </el-tooltip>
                    </span>
                    <span class="each-mony">¥{{ NumFormat(item.mony) }}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { NumFormat } from '@/utils/tools'
import * as Echarts from 'echarts'

export default {
  name: 'TotalFunds',
  props: {
    info: {
      type: Object,
      default: () => ({})
    },
    dataSource: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      NumFormat,
      capitalEcharts: null,
      totalData: [],
      echartData: []
    }
  },
  inject: ['AccountBasic'],
  mounted() {
    console.log('dataSource-----', this.dataSource)
    this.totalData = [
      {
        title: '累计总缴',
        mony: this.dataSource.totalPay,
        isExpansion: false,
        eachMony: [
          {
            title: '租赁费总缴',
            mony: this.dataSource.rentPay
          },
          {
            title: '服务费总缴',
            mony: this.dataSource.servicePay
          }
        ]
      },
      {
        title: '交易登记中',
        mony: this.dataSource.recordAmountSum,
        isExpansion: false,
        eachMony: [
          {
            title: '今日登记中',
            mony: this.dataSource.recordAmountToDay
          },
          {
            title: '昨日登记中',
            mony: this.dataSource.recordAmountYesterday
          },
          {
            title: '历史登记中',
            mony: this.dataSource.recordAmountHistory
          }
        ]
      },
      {
        title: '待核销',
        mony: this.dataSource.totalToWriteOff,
        isExpansion: false,
        eachMony: [
          {
            title: '租赁费待核销',
            mony: this.dataSource.rentToWriteOff
          },
          {
            title: '服务费待核销',
            mony: this.dataSource.serviceToWriteOff
          }
        ]
      },
      {
        title: '未出账',
        mony: this.dataSource.totalOutstanding,
        isExpansion: false,
        eachMony: [
          {
            title: '租赁费未出账',
            mony: this.dataSource.rentOutstanding
          },
          {
            title: '服务费未出账',
            mony: this.dataSource.serviceOutstanding
          }
        ]
      }
    ]
    this.initEcharts()
    window.addEventListener('resize', () => {
      this.capitalEcharts.resize()
    })
  },
  created() {
    this.EntInfoToutal(this.AccountBasic.EntInfoToutal)
  },
  methods: {
    EntInfoToutal(e) {
      let arrObj = [
        {
          title: '累计总缴',
          mony: e.totalPay,
          isExpansion: false,
          eachMony: [
            {
              title: '租赁费总缴',
              mony: e.rentPay
            },
            {
              title: '服务费总缴',
              mony: e.servicePay
            }
          ]
        },
        {
          title: '交易登记中',
          mony: e.recordAmountSum,
          isExpansion: false,
          eachMony: [
            {
              title: '今日登记中',
              mony: e.recordAmountToDay
            },
            {
              title: '昨日登记中',
              mony: e.recordAmountYesterday
            },
            {
              title: '历史登记中',
              mony: e.recordAmountHistory
            }
          ]
        },
        {
          title: '待核销',
          mony: e.totalToWriteOff,
          isExpansion: false,
          eachMony: [
            {
              title: '租赁费待核销',
              mony: e.rentToWriteOff
            },
            {
              title: '服务费待核销',
              mony: e.serviceToWriteOff
            }
          ]
        },
        {
          title: '未出账',
          mony: e.totalOutstanding,
          isExpansion: false,
          eachMony: [
            {
              title: '租赁费未出账',
              mony: e.rentOutstanding
            },
            {
              title: '服务费未出账',
              mony: e.serviceOutstanding
            }
          ]
        }
      ]
      this.totalData = arrObj
      console.log(e)
    },
    initEcharts() {
      this.capitalEcharts = Echarts.init(this.$refs['capital-echar'])
      let option = {
        tooltip: {
          trigger: 'item',
          backgroundColor: '#0f1218', //设置背景颜色
          axisPointer: {
            type: 'line'
          },
          textStyle: {
            color: 'white' //设置文字颜色
          },
          borderColor: '#0f1218',
          formatter(params) {
            // console.log(params) // 打印数据
            // let showdata = params[0];
            // 根据自己的需求返回数据
            return `
                   <div style="
                    width: 158px;
                    font-size: 14px;
                    word-break: break-word;">
                    ${params.data.name}：${params.data.value}元
                    </div>
                `
          }
        },
        legend: [
          {
            top: '10%',
            data: ['累计总缴', '交易登记中', '待核销', '未出账'],
            textStyle: {
              //图例文字的样式
              color: '#a1a1a1', //图例文字颜色
              fontSize: 12, //图例文字大小
              rich: {
                height: 14,
                a: {
                  verticalAlign: 'bottom'
                }
              }
            },
            itemHeight: 11,
            itemWidth: 11, //修改icon图形大小
            icon: 'rect',
            formatter: ['{a|{name}}'].join('\n')
          },
          {
            top: '90%',
            data: [
              {
                name: '累计总收',
                icon: 'rect',
                itemStyle: {
                  color: '#d5e2ff'
                }
              },
              {
                name: '交易待确认',
                icon: 'rect',
                itemStyle: {
                  color: '#bcebdc'
                }
              },
              {
                name: '出账未支付',
                icon: 'rect',
                itemStyle: {
                  color: '#f9e0c7'
                }
              },
              {
                name: '未出账',
                icon: 'rect',
                itemStyle: {
                  color: '#f8b9be'
                }
              }
            ],
            textStyle: {
              //图例文字的样式
              color: '#9a9a9a', //图例文字颜色
              fontSize: 12, //图例文字大小
              rich: {
                height: 11,
                a: {
                  verticalAlign: 'bottom'
                }
              }
            },
            itemHeight: 11,
            itemWidth: 11, //修改icon图形大小
            icon: 'rect',
            formatter: ['{a|{name}}'].join('\n')
          }
        ],
        series: [
          {
            name: '大环',
            type: 'pie',
            radius: ['25%', '50%'],
            itemStyle: {
              borderRadius: 4,
              borderColor: '#fff',
              borderWidth: 1
            },
            label: {
              normal: {
                show: false,
                position: 'center'
              },
              emphasis: {
                show: true,
                // position: 'center',
                formatter(content) {
                  console.log(content.percent)
                  return `{a|${content.percent + '%'}} \n {b|占比}`
                },
                rich: {
                  a: {
                    color: '#191919',
                    fontSize: 20,
                    fontWeight: 600,
                    lineHeight: 20,
                    padding: [0, 0, 0, 0]
                  },
                  b: {
                    color: '#191919',
                    fontSize: 12,
                    lineHeight: 20,
                    padding: [0, 0, 0, 0]
                  }
                }
              }
            },

            labelLine: {
              show: false
            },
            data: [
              {
                value: this.dataSource.totalPay,
                name: '累计总缴',
                itemStyle: {
                  color: '#ed7b2f' //该区域对应渐变色
                }
              },
              {
                value: this.dataSource.recordAmountSum,
                name: '交易登记中',
                itemStyle: {
                  color: '#48c79c' //该区域对应渐变色
                }
              },
              {
                value: this.dataSource.totalToWriteOff,
                name: '待核销',
                selected: true,
                itemStyle: {
                  color: '#f2995f' //该区域对应渐变色
                }
              },
              {
                value: this.dataSource.totalOutstanding,
                name: '未出账',
                selected: true,
                itemStyle: {
                  color: '#f36d78' //该区域对应渐变色
                }
              }
            ]
          },
          {
            name: '小环',
            type: 'pie',
            radius: ['65%', '60%'],
            labelLine: {
              show: false
            },
            label: {
              show: false
            },
            data: [
              {
                value: 1548,
                name: '累计总收',
                itemStyle: {
                  color: '#d5e2ff', //该区域对应渐变色
                  shadowBlur: 8,
                  shadowColor: '#d5e2ff',
                  shadowOffsetX: 0,
                  shadowOffsetY: 0
                }
              },
              {
                value: 775,
                name: '交易待确认',
                itemStyle: {
                  color: '#bcebdc', //该区域对应渐变色
                  shadowBlur: 8,
                  shadowColor: '#bcebdc',
                  shadowOffsetX: 0,
                  shadowOffsetY: 0
                }
              },
              {
                value: 679,
                name: '出账未支付',
                itemStyle: {
                  color: '#f9e0c7', //该区域对应渐变色
                  shadowBlur: 8,
                  shadowColor: '#f9e0c7',
                  shadowOffsetX: 0,
                  shadowOffsetY: 0
                }
              },
              {
                value: 679,
                name: '未出账',
                selected: true,
                itemStyle: {
                  color: '#f8b9be', //该区域对应渐变色
                  shadowBlur: 8,
                  shadowColor: '#f8b9be',
                  shadowOffsetX: 0,
                  shadowOffsetY: 0
                }
              }
            ]
          }
        ]
      }
      this.capitalEcharts.setOption(option)
    },

    expansion(i) {
      this.totalData.forEach((item, index) => {
        if (index === i) {
          item.isExpansion = !item.isExpansion
        } else {
          item.isExpansion = false
        }
      })
      this.$nextTick(() => {
        let doms = document.querySelectorAll('.total-data-item')
        for (let i = 0; i < doms.length; i++) {
          if (doms[i].classList.contains('item-expansion')) {
            doms[i].classList.remove('item-expansion')
          }
        }
        if (this.totalData[i].isExpansion) {
          doms[i].classList.add('item-expansion')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.total-funds {
  width: 100%;
  margin-top: 5px;
  .total-data {
    width: 100%;
    height: 100%;
    height: 54vh;
    overflow: auto;
    // display: grid;
    // grid-template-columns: repeat(2, 1fr);
    // grid-column-gap: 24px;
    // grid-row-gap: 24px;
    .item-wrapper {
      height: 25.8vh;
      width: 100%;
      margin-bottom: 20px;
      .total-data-item {
        width: 100%;
        height: 100%;
        border-radius: 6px 6px 6px 6px;
        background-color: #fff;
        opacity: 1;
        border: 1px solid #e7e7e7;
        padding: 14px 24px;
        box-sizing: border-box;
        overflow: hidden;

        .amplifier {
          color: #737579;
          font-size: 14px;
          cursor: pointer;
        }

        .top {
          width: 100%;
          border-bottom: 1px solid #e7e7e7;
        }

        .body {
          width: 100%;
          //height: 10vh;
          line-height: 4.5vh;
        }
        .header_title {
          width: 30%;
          font-size: 14px;
          flex: 1;
          color: rgba(0, 0, 0, 0.6);
        }
        .title {
          width: 30%;
          font-size: 14px;
          flex: 1;
          color: rgba(0, 0, 0, 0.4);
        }

        .each-mony {
          font-size: 18px;
          font-weight: 600;
          margin-left: 7px;
          color: rgba(0, 0, 0, 0.9);
        }

        .mony {
          font-weight: 500;
          color: #ed7b2f;
        }
      }

      .total-data-item-t {
        position: absolute;
        left: 0;
        top: 0;
      }

      .total-data-item-b {
        position: absolute;
        left: 0;
        bottom: 0;
      }

      .item-expansion {
        width: 100%;
        height: 54.2vh;
        transition: all 0.05s;
        box-shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.1);
        z-index: 99;
      }

      .total-data-item:hover {
        background-color: #f3f7ff;
        .amplifier {
          color: #ed7b2f;
        }
      }
    }
  }

  .capital-echarts {
    position: relative;
    width: 100%;
    height: 100%;
    border-radius: 6px 6px 6px 6px;
    opacity: 1;
    border: 1px solid #e7e7e7;

    .capital-echar {
      width: 100%;
      //height: calc(100vh - 435px);
      height: 54vh;
    }

    .time {
      position: absolute;
      top: 6%;
      left: 38.5%;
      font-size: 14px;
      color: #999;
    }

    .compare {
      position: absolute;
      left: 43.5%;
      bottom: 11%;
      font-size: 14px;
      color: #999;
    }
  }
}
</style>
