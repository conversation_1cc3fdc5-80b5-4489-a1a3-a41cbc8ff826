<template>
  <div class="w100 tabs-wrapper">
    <div class="flex align-items-center">
      <div
        v-for="(item, index) in tabsData"
        :key="index"
        class="font-size-14 color-text-regular p-l-16 p-r-16 tabs-item"
        :class="item.value === current ? 'active' : ''"
        @click="tabsChange(index)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="flex align-items-center">
      <i class="el-icon-refresh m-r-4 pointer tb" style="color: #ed7b2f" />
      <div
        class="color-text-secondary pointer update-time"
        @click="refreshInfo"
      >
        更新时间：2019-12-12
      </div>
    </div>
  </div>
</template>

<script>
import { refreshPayAccInfoById } from '@/views/manage/financial/financial-jtyh/account/api'
import { Message } from 'element-ui'

export default {
  name: 'BasicTab',
  props: {
    tabsData: {
      type: Array,
      default: () => []
    },
    current: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  methods: {
    tabsChange(e) {
      this.$emit('tabsChange', e)
    },
    // 刷新查询时间
    refreshInfo() {
      const params = {
        ids: [this.id]
      }
      refreshPayAccInfoById(params).then(() => {
        Message({ message: '刷 新成功' })
        this.getPayAccInfoById()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.tabs-wrapper {
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .tabs-item {
    height: 40px;
    line-height: 40px;
    position: relative;
    cursor: pointer;

    &.active {
      @include font_color(--color-primary);
      &::before {
        content: '';
        width: 100%;
        height: 2px;
        @include background-color(--color-primary);
        position: absolute;
        bottom: -2px;
        left: 0;
      }
    }
  }

  .update-time {
    font-size: 12px;
    color: #000;
  }
}
</style>
