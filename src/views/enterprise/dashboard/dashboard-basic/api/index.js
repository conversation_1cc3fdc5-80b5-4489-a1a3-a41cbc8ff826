import request from '@/utils/request'

// 判断是否可以入园
export function getEnterParkStatus() {
  return request({
    url: `/enter/ent/park-apply/enterParkStatus`,
    method: 'get'
  })
}

//获取通知消息
export function getEnterMessageForNormal(params) {
  return request({
    url: `/msg/message-info/page/normal`,
    method: 'get',
    params,
    hideLoading: true
  })
}

//获取待办消息
export function getEnterMessageForHandle(params) {
  return request({
    url: `/msg/message-info/get/handle`,
    method: 'get',
    params,
    hideLoading: true
  })
}

//获取消息详情
export function getEnterMessageForHandleDetail(params) {
  return request({
    url: `/msg/message-info/get`,
    method: 'get',
    params,
    hideLoading: true
  })
}

//获取待办类型及对应数量

// 获取公告列表
export function getPolicyList(params) {
  return request({
    url: `/policy/column/info/ent/ent_list`,
    method: 'get',
    params,
    isTable: true
  })
}

// 企业查询课程
export function getCourseList(params) {
  return request({
    url: `/hatch/ent/course/selectPage`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取通知公告列表
export function getPoticeList(params) {
  return request({
    url: `/system/notice/page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 企业端首页获取资讯
export function getInformationList(params) {
  return request({
    url: `/hatch/ent/notice/page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取活动列表
export function getActivityList(params) {
  return request({
    url: `/hatch/activity/page`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获取场地列表
export function getFieldList(params) {
  return request({
    url: `/hatch/ent/place-info/page`,
    method: 'get',
    params,
    isTable: true
  })
}

export function getEntpInfo() {
  return request({
    url: `/enterprise/ent/info/currentEntInfo`,
    method: 'get',
    hideLoading: true
  })
}

// 获取公告详情
export function getInformationDetails(id) {
  return request({
    url: `/hatch/notice/get?id=${id}`,
    method: 'get'
  })
}
// 获得场地分享信息
export function getNoticeShareInfo(id) {
  return request({
    url: `/hatch/notice/get_share_info/${id}`,
    method: 'get'
  })
}

// 获取热点资讯
export function getHotList(params) {
  return request({
    url: `/hatch/information/pageEnt`,
    method: 'get',
    params,
    isTable: true
  })
}

// 查询所有企业端信息
export function getEntList(params) {
  return request({
    url: `/msg/ent/list`,
    method: 'get',
    params,
    isTable: true
  })
}

// 获得所有参数
export function readParameter(id) {
  return request({
    url: `/msg/admin/parameter/${id}`,
    method: 'get'
  })
}

// 获得消息类型
export function readAgent(params) {
  return request({
    url: `/msg/ent/agent`,
    method: 'get',
    params
  })
}

// 登录时弹窗
export function getTaskPopUps(params) {
  return request({
    url: `/ts/ent/task/pop_ups`,
    method: 'get',
    params
  })
}

// 处理 控制弹窗
export function getTaskHandle(params) {
  return request({
    url: `/ts/ent/task/handle`,
    method: 'get',
    params
  })
}

// 企业端首页课程
export function getHomeCourses(params) {
  return request({
    url: `/course/enterprise/home_courses`,
    method: 'get',
    params
  })
}
// 获取资讯列表
export function getCapitalNewsList(params) {
  return request({
    url: `/hatch/information/manage/page_ent`,
    method: 'get',
    params
  })
}
// 获取资讯详情
export function getCapitalNewsDetail(id) {
  return request({
    url: `/hatch/ent/information/manage/get?id=${id}`,
    method: 'get'
  })
}
// 资讯分享接口
export function getCapitalNewsShareInfo(id) {
  return request({
    url: `/hatch/ent/information/manage/get_share_info/${id}`,
    method: 'get'
  })
}
