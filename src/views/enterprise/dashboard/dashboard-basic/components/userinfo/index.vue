<template>
  <div class="userinfo-card" v-if="false">
    <div class="userinfo-card-header">
      <span class="userinfo-title">
        <svg-icon icon-class="info-circle-filled" class="color-primary" />
        <span class="p-l-10">账户信息更新</span>
      </span>
      <svg-icon class="close" icon-class="close" @click="closeHandle" />
    </div>
    <!--    <div class="userinfo-content">-->
    <!--      具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供，具体提示文案由产品提供！-->
    <!--    </div>-->
    <el-button type="text" class="m-l-26" @click="clickHandle"
      >去修改</el-button
    >
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { local } from '@/utils/storage'

export default {
  name: 'UserinfoCard',
  data() {
    return {
      updateUserinfo: false
    }
  },
  inject: ['rootMain'],
  computed: {
    ...mapGetters(['id', 'userTips'])
  },
  methods: {
    closeHandle() {
      this.$store.dispatch('UserTips', false)
      const userIds = local.GET_TIPS_USER_ID() || []
      if (!userIds.includes(this.id)) userIds.push(this.id)
      local.SET_TIPS_USER_ID(userIds)
    },
    clickHandle() {
      this.rootMain.$refs.navbar.$refs.userInfo.userDialogVisible = true
      this.closeHandle()
    }
  }
}
</script>

<style scoped lang="scss">
.userinfo-card {
  width: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: #d5e2ff;
  z-index: 10;
  padding: 18px 28px;
  .userinfo-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .userinfo-title {
      font-size: 14px;
      font-weight: 350;
      @include font_color_mix(--color-black, #ffffff, 10%);
      line-height: 22px;
    }
    .close {
      cursor: pointer;
      @include font_color_mix(--color-black, #ffffff, 40%);
    }
  }
  .userinfo-content {
    font-size: 14px;
    font-weight: 350;
    @include font_color_mix(--color-black, #ffffff, 40%);
    line-height: 22px;
    padding: 0 26px;
    margin: 8px 0;
  }
}
</style>
