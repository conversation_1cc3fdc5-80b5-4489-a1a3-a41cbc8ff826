<template>
  <div class="introduction flex align-items-center">
    <div class="title">
      <h2 class="font-size-20 font-strong color-text-primary">
        欢迎您，{{
          userInfo.name !== null && userInfo.name !== ''
            ? userInfo.name
            : '尊敬的客户'
        }}
      </h2>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleHeader',
  props: {
    type: {
      type: String,
      default: 'primary'
    }
  },
  data() {
    return {}
  },
  computed: {
    userInfo() {
      return this.$store.state.user
    }
  }
}
</script>

<style lang="scss" scoped>
.introduction {
  height: 44px;
}
</style>
