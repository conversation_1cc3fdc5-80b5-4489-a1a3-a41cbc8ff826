<template>
  <div class="policy_card bg-white p-24">
    <!--    <div class="flex flex-center-between m-b-8">-->
    <!--      <div class="font-size-16 line-height-24 font-strong m-r-4">政策速递</div>-->
    <!--      <img-->
    <!--        class="icon_img pointer"-->
    <!--        @click="goList"-->
    <!--        src="../../images/more.png"-->
    <!--        alt=""-->
    <!--      />-->
    <!--    </div>-->
    <div class="flex flex-center-between">
      <!--   后期可封装成组件   -->
      <div class="policy_table flex">
        <div
          class="policy_table_item pointer"
          v-for="(item, index) in tableList"
          :key="index"
          @click="selectTable(index)"
        >
          <div
            class="policy_table_item_title font-size-16 line-height-24"
            :class="[tableIndex === index ? 'title_active' : '']"
          >
            {{ item.title }}
          </div>
          <div
            class="policy_table_item_border"
            :class="[tableIndex === index ? 'border_active' : '']"
          ></div>
        </div>
      </div>
      <!--      <img-->
      <!--        class="icon_img pointer"-->
      <!--        @click="goList"-->
      <!--        src="../../images/swap-right.png"-->
      <!--        alt-->
      <!--      />-->
    </div>
    <div v-show="tableIndex === 0" class="policy_List p-r-8">
      <div
        class="policy_item p-t-12 p-b-12 pointer"
        v-for="(item, index) in policyList"
        :key="index"
        @click="skipPolicy(item)"
      >
        <div
          class="font-size-16 line-height-22 font-strong-400 color-text-primary line-1"
        >
          {{ item.policyName }}
        </div>
        <div class="flex flex-center-between m-t-8">
          <div class="flex">
            <!--            <basic-tag-->
            <!--              type="primary"-->
            <!--              :label="item.policyLevelName"-->
            <!--              class="m-r-8"-->
            <!--            />-->
            <basic-tag
              class="m-r-8"
              v-show="index < 2"
              v-for="(val, index) in levelFormatter(item.policyCategoryName)"
              :key="index"
              type="danger"
              :label="val"
            />
          </div>
          <div class="font-size-12 line-height-20 color-text-secondary">
            {{ item.publishingTime }}
          </div>
        </div>
      </div>
      <div
        class="h100 w100 flex flex-center-center"
        v-if="policyList && policyList.length === 0"
      >
        <empty-data />
      </div>
    </div>
    <div v-show="tableIndex === 1" class="policy_List p-r-8">
      <div
        class="policy_item p-t-12 p-b-12 pointer"
        v-for="(item, index) in hotList"
        :key="index"
        @click="previewEvent(item)"
      >
        <div
          class="font-size-16 line-height-22 font-strong-400 color-text-primary line-1"
        >
          {{ item.title }}
        </div>
        <div class="flex flex-center-between m-t-8">
          <div class="flex">
            <basic-tag type="primary" :label="item.noticeTypeStr" />
          </div>
          <div class="font-size-12 line-height-20 color-text-secondary">
            {{ item.publishTime }}
          </div>
        </div>
      </div>
      <div
        class="h100 w100 flex flex-center-center"
        v-if="hotList.length === 0"
      >
        <empty-data />
      </div>
    </div>

    <!-- 公告详情 -->
    <dialog-cmp
      :title="tableIndex === 0 ? '公告详情' : '资讯详情'"
      :visible.sync="visible"
      width="920px"
      :haveOperation="false"
    >
      <div>
        <notice-detail
          v-if="tableIndex === 0"
          :informationData="informationData"
        />
        <capital-news-preview
          v-else
          :id="informationData.id"
          :informationData="informationData"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import NoticeDetail from './../BackLog/noticeDetail.vue'
import {
  getCapitalNewsDetail,
  getCapitalNewsList,
  getPolicyList
} from '../../api'
import CapitalNewsPreview from '@/views/enterprise/dashboard/dashboard-basic/components/PolicyConsultation/preview'

export default {
  name: 'PolicyConsultation',
  components: {
    CapitalNewsPreview,
    NoticeDetail
  },
  data() {
    return {
      tableList: [{ title: '政策速递' }, { title: '热点资讯' }],
      tableIndex: 0,
      policyList: [],
      consultation: [],
      hotList: [],
      visible: false,
      informationData: {} // 资讯详情数据
    }
  },
  created() {
    this.getPolicyList()
    this.getHotList()
  },
  methods: {
    levelFormatter(str) {
      if (!str) return []
      return str.split(',')
    },
    selectTable(index) {
      this.tableIndex = index
    },
    getPolicyList() {
      getPolicyList().then(res => {
        this.policyList = res || []
      })
    },
    goList() {
      this.$router.push({
        path: '/hatch/interact/policy'
      })
    },

    // 去政策详情
    skipPolicy(row) {
      const { id } = row
      this.$router.push({
        path: '/hatch/interact/policy/detail',
        query: {
          id
        }
      })
    },
    getHotList() {
      getCapitalNewsList({ pageNo: 1, pageSize: 10 }).then(res => {
        this.hotList = res.list || []
      })
    },
    // 查看
    previewEvent(row) {
      getCapitalNewsDetail(row.id).then(res => {
        this.informationData = res
        this.visible = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.policy_card {
  height: 684px;
  //height: 380px;
  width: 100%;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
}
.policy_List {
  height: calc(100% - 33px);
  overflow-y: auto;
}
.policy_table {
  height: 33px;
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
}
.policy_table_item {
  margin-left: 16px;
  margin-right: 16px;
  &:first-child {
    margin-left: 0;
  }
  &:last-child {
    margin-right: 0;
  }
}
.policy_table_item_title {
  font-weight: 400;
  @include font_color(--color-text-regular);
}
.title_active {
  font-weight: 550;
  @include font_color(--color-text-primary);
}
.policy_table_item_border {
  margin: 2px 16px 4px;
  width: calc(100% - 32px);
  height: 3px;
  background-color: unset;
}
.border_active {
  @include background_color(--color-primary);
}
.icon_img {
  height: 20px;
  width: 20px;
}
</style>
