<template>
  <!--  信息填报弹窗-->
  <div class="dialog-container">
    <el-dialog
      :visible.sync="visible"
      :modal-append-to-body="false"
      :append-to-body="true"
      :show-close="false"
      width="37%"
    >
      <!--      头部区域-->
      <template v-slot:title>
        <div class="flex align-items-center font-size-18 m-t-10">
          <svg-icon class="color-primary" icon-class="info-circle-filled" />
          <div class="font-size-16 m-l-8" style="font-weight: 400">
            关于2023年在园规上企业信息填报任务开展通知
          </div>
        </div>
      </template>
      <!--      内容区域-->
      <div class="text-s p-l-24 p-r-24">
        <div class="flex p-l-26 line-height-22">
          <div class="m-r-8">
            <span>开始时间：</span>
            <span>{{ objDialog.beginDate }}</span>
          </div>
          <div>
            <span>结束时间：</span>
            <span>{{ objDialog.endDate }}</span>
          </div>
        </div>
        <p class="p-l-26 line-height-22">
          {{ objDialog.taskExplain }}
        </p>
      </div>
      <!--      底部按钮区域-->
      <template v-slot:footer>
        <div class="flex justify-content-between align-items-center">
          <div class="flex p-l-26">
            <div
              class="is-dot"
              :class="index === 0 ? 'active-bg' : 'is-dot'"
              v-for="(item, index) in dotList"
              :key="index"
            ></div>
          </div>
          <div>
            <el-button type="info" @click="processLater">稍后处理</el-button>
            <el-button
              type="primary"
              @click="handleTask(objDialog.id, objDialog.taskId)"
              >立刻处理</el-button
            >
          </div>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTaskHandle,
  getTaskPopUps
} from '@/views/enterprise/dashboard/dashboard-basic/api'

export default {
  name: 'InformationDialog',
  data() {
    return {
      visible: false,
      dotList: [],
      list: [],
      objDialog: {}
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getTaskPopUps()
    })
  },
  methods: {
    async getTaskPopUps() {
      const res = await getTaskPopUps()
      this.dotList = res
      this.list = res
      if (this.list && this.list.length > 0) {
        this.visible = true
      } else {
        this.visible = false
      }
      this.list.forEach((item, index) => {
        if (index === 0) {
          this.objDialog = item
        }
      })
    },
    // 稍后处理
    processLater() {
      this.visible = false
      this.$toast.success('我们会在下次合适的时间继续提醒您')
    },
    // 提交按钮
    async handleTask(id, taskId) {
      await getTaskHandle({ id })
      this.getTaskPopUps()
      let routeData = this.$router.resolve({
        path: '/work/handle/informationDetail',
        query: {
          id: taskId
        }
      })
      window.open(routeData.href, '_blank')
      if (this.dotList && this.dotList.length < 1) {
        this.visible = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-container {
  .text-s {
    color: rgba(0, 0, 0, 0.6);
  }
  .is-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #bed2fe;
    margin-right: 8px;
  }
  .active-bg {
    @include background_color(--color-primary);
  }
}

::v-deep {
  .el-dialog {
    background: linear-gradient(180deg, #e9f0ff 0%, #ffffff 100%) !important;
  }
  .el-dialog .el-dialog__header {
    border-bottom: none;
  }
  .el-dialog__footer {
    padding: 10px 24px 20px;
  }
}
</style>
