<template>
  <div class="activity" @click="goDetail(item)">
    <angle-status
      :type="timeStatus[item.activityStatus].type"
      :text="timeStatus[item.activityStatus].text"
      class="icon_left"
    />
    <div class="activity_bgc">
      <img alt="" class="activity_bgc_img" :src="activityPic" />
      <div class="tag-button_pro">
        <tag-button
          slot="reference"
          type="primary"
          :tag="item.activityType"
          :buttonName="buttonName"
          :disabled="disableSign"
          @tapButton="goDetail(item)"
          hideButton
        />
      </div>
    </div>
    <div class="card_title line-1">
      {{ item.activityName || '-' }}
    </div>

    <div class="card_title_sub">
      <img alt="" class="card_title_img" src="../../images/location.png" />{{
        item.address || '-'
      }}
    </div>
    <div class="card_title_sub">
      <img alt="" class="card_title_img" src="../../images/calendar.png" />{{
        item.startTime
      }}
    </div>
  </div>
</template>

<script>
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'
import dayjs from 'dayjs'

export default {
  name: 'ActivityCard',
  components: {
    AngleStatus,
    TagButton
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      timeStatus: {
        1: {
          type: 'warning',
          text: '未开始'
        },
        2: {
          type: 'primary',
          text: '进行中'
        },
        3: {
          type: 'info',
          text: '已结束'
        }
      }
    }
  },
  computed: {
    disableSign() {
      const { signDeadline, needSign } = this.item
      const endTime = dayjs(signDeadline).unix()
      const currentTime = dayjs(new Date()).unix()
      if (!needSign) return true
      return currentTime >= endTime
    },
    buttonName() {
      return this.item.needSign === 0 ? '无需报名' : '去报名'
    },
    activityPic() {
      return this.item.attachMap.activity
        ? this.item.attachMap.activity[0].path
        : '../../images/example.png'
    }
  },
  methods: {
    goDetail() {
      this.$router.push({
        path: '/hatch/interact/activity/detail',
        query: {
          id: this.item.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.activity {
  position: relative;
  width: 100%;
  @include background_color(--color-white);
  cursor: pointer;
}
.activity_bgc {
  position: relative;
}
.tag-button_pro {
  margin: 0 8px;
  width: calc(100% - 16px);
  position: absolute;
  bottom: 8px;
}

.activity_bgc_img {
  width: calc(100% - 0px);
  height: 163px;
  border-radius: 3px;
}

.card_title {
  margin: 4px 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.card_title_img {
  height: 16px;
  width: 16px;
  margin: 3px 4px 3px 0;
  vertical-align: middle;
}

.card_title_sub {
  margin: 4px 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon_left {
  position: absolute;
  top: 16px;
  left: -4px;
}

.icon_right {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 40px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 3px;
  z-index: 999;

  & .title_text {
    margin: 2px 8px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    line-height: 20px;
  }
}
</style>
