<template>
  <div class="activity_card bg-white p-t-15 p-b-15">
    <div class="flex flex-center-between m-b-8 p-l-15 p-r-15">
      <div class="activity_table flex">
        <div class="activity_table_item">
          <div class="activity_table_item_title font-size-16 line-height-24">
            园区活动
          </div>
        </div>
      </div>
      <img
        class="icon_img pointer"
        @click="goList"
        src="../../images/more.png"
        alt
      />
    </div>
    <div class="activity_list flex">
      <el-carousel indicator-position="none" v-if="list.length > 0">
        <el-carousel-item v-for="(item, index) in list" :key="index">
          <div class="reservation-carousel-item">
            <card :item="item" />
          </div>
        </el-carousel-item>
      </el-carousel>
      <div class="h100 w100 flex flex-center-center" v-if="list.length === 0">
        <empty-data />
      </div>
    </div>
  </div>
</template>

<script>
import card from './Card'
import { getActivityList } from '../../api'
export default {
  name: 'ParkActivity',
  components: {
    card
  },
  data() {
    return {
      timeStatus: {
        1: {
          type: 'warning',
          text: '未开始'
        },
        2: {
          type: 'primary',
          text: '进行中'
        },
        3: {
          type: 'info',
          text: '已结束'
        }
      },
      list: []
    }
  },
  created() {
    this.getActivityList()
  },
  methods: {
    getActivityList() {
      getActivityList({ pageNo: 1, pageSize: 10 }).then(res => {
        this.list = res.list
      })
    },
    goList() {
      this.$router.push({
        path: '/hatch/interact/activity'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.activity_card {
  height: 304px;
  width: 100%;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
}
.activity_table {
  height: 24px;
}
.activity_table_item_title {
  font-weight: 550;
  @include font_color(--color-text-primary);
}
.activity_list {
  height: calc(100% - 24px);
  width: 100%;
}
.el-carousel {
  width: 100% !important;
  overflow-y: hidden;
}
.reservation-carousel-item {
  width: calc(100% - 30px);
  margin: 0 auto;
}
:deep(.el-carousel__arrow) {
  top: 35% !important;
}
</style>
