<template>
  <div class="card" @click="toDetail">
    <div class="card-wrapper">
      <div class="banner">
        <el-image class="w100 h100" :src="coursePic" alt="" />
        <div class="tag-button_pro">
          <tag-button type="success" :tag="courseLabel" hideButton />
        </div>
      </div>
      <div class="content">
        <h3 class="line-1 font-size-16 line-height-24 m-t-8 m-b-8">
          {{ item.title }}
        </h3>
        <div class="icon-list m-b-12">
          <icon-info
            icon-type="primary"
            icon-class="calendar"
            :text="item.createTime"
          />
        </div>
      </div>
    </div>
    <div class="status">
      <angle-status type="primary" :text="item.courseModeStr" />
    </div>
  </div>
</template>

<script>
import IconInfo from '@/components/IconInfo'
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'

export default {
  name: 'CourseCard',
  components: {
    IconInfo,
    AngleStatus,
    TagButton
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  computed: {
    courseLabel() {
      return this.item.labels ? this.item.labels.join('/') : ''
    },
    coursePic() {
      // const attachMap = this.item.attachMap
      // const coursePic =
      //   attachMap && attachMap.coursePic && attachMap.coursePic[0]
      return this.item.titleUrl
        ? this.item.titleUrl
        : '../../images/example.png'
    }
  },
  methods: {
    toDetail() {
      this.$router.push({
        path: '/hatch/interact/course/detail',
        query: {
          id: this.item.id
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  height: 158px;
  width: 266px;
  margin: 8px 11px;
  position: relative;
  cursor: pointer;
  flex: 0 0 266px;
  .card-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    .banner {
      position: relative;
      flex: 0 0 248px;
      height: 148px;
      border-radius: 3px;
      overflow: hidden;
      .tag-button_pro {
        margin: 0 8px;
        width: calc(100% - 16px);
        position: absolute;
        bottom: 8px;
      }
    }
    .content {
      flex: 1;
      overflow: hidden;
    }
  }
  .status {
    position: absolute;
    left: -4px;
    top: 8px;
  }
}
</style>
