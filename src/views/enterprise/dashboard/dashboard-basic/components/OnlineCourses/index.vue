<template>
  <div class="courses_card bg-white">
    <div class="flex flex-center-between">
      <div class="courses_table flex">
        <div class="courses_table_item">
          <div class="courses_table_item_title font-size-16 line-height-24">
            在线课程
          </div>
        </div>
      </div>
      <img
        class="icon_img pointer"
        @click="goList"
        src="../../images/more.png"
        alt
      />
    </div>
    <div class="courses_list flex">
      <courses-card :item="item" v-for="(item, index) in list" :key="index" />
      <div class="h100 w100 flex flex-center-center" v-if="list.length === 0">
        <empty-data />
      </div>
    </div>
  </div>
</template>

<script>
import CoursesCard from './Card'
import { getCourseList, getHomeCourses } from '../../api'
export default {
  name: 'OnlineCourses',
  components: {
    CoursesCard
  },
  data() {
    return {
      list: []
    }
  },
  created() {
    // this.getCourseList()
    this.getHomeCourses()
  },
  methods: {
    async getHomeCourses() {
      const res = await getHomeCourses()
      this.list = res
    },
    getCourseList() {
      getCourseList({ pageNo: 1, pageSize: 10 }).then(res => {
        this.list = res.list
      })
    },
    goList() {
      this.$router.push({
        path: '/hatch/interact/course'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.courses_card {
  height: 304px;
  width: 100%;
  padding: 15px 24px;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
}
.courses_table {
  height: 24px;
  margin-bottom: 8px;
}
.courses_table_item_title {
  font-weight: 550;
  @include font_color(--color-text-primary);
}
.courses_list {
  height: calc(100% - 24px);
  transition: all 0.5s ease-in-out;
  overflow-x: auto;
  display: -webkit-box;
  display: -moz-box;
}
//.courses_list::-webkit-scrollbar {
//  height: 6px;
//}
</style>
