<template>
  <div class="reservation" @click="goDetail(item)">
    <angle-status :text="item.number + '人'" type="primary" class="icon_left" />
    <div class="icon_right">
      <div class="title_text">
        {{ venueFeesType(item.venueFees) }}
      </div>
    </div>
    <div class="reservation_bgc">
      <img alt="" class="reservation_bgc_img" :src="getItemAttachPath(item)" />
      <div class="tag-button_pro" @click.stop>
        <tag-button
          slot="reference"
          type="warning"
          :tag="item.allocationStr"
          hideButton
        />
      </div>
    </div>
    <div class="card_title line-1">
      {{ item.name || '-' }}
    </div>

    <div class="card_title_sub flex align-items-center">
      <img alt="" class="card_title_img" src="../../images/location.png" />{{
        item.address || '-'
      }}
    </div>
    <div class="card_title_sub flex align-items-center">
      <img alt="" class="card_title_img" src="../../images/calendar.png" />{{
        item.createTime
      }}
    </div>
  </div>
</template>

<script>
import AngleStatus from '@/components/Lateral/AngleStatus'
import TagButton from '@/components/Lateral/TagButton'

export default {
  name: 'reservationCard',
  components: {
    AngleStatus,
    TagButton
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {}
  },
  methods: {
    getItemAttachPath(item) {
      let path
      try {
        path = item.coverAttachMap.coursePic[0].path
      } catch (e) {
        path = require('../../images/site-example.png')
      }
      return path
    },
    goDetail() {
      this.$router.push({
        path: '/hatch/interact/field/detail',
        query: {
          id: this.item.id
        }
      })
    }
  },
  computed: {
    venueFeesType() {
      const { price } = this.item
      return val => {
        switch (val) {
          case 0:
            return '免费'
          case 1:
            return `${price}元/每次`
          case 2:
            return `${price}元/小时`
          default:
            return '暂无数据'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.reservation {
  position: relative;
  width: 100%;
  @include background_color(--color-white);
  cursor: pointer;
}
.reservation_bgc {
  position: relative;
}
.tag-button_pro {
  margin: 0 8px;
  width: calc(100% - 16px);
  position: absolute;
  bottom: 8px;
}

.reservation_bgc_img {
  width: calc(100% - 0px);
  height: 163px;
  border-radius: 3px;
}

.card_title {
  margin: 4px 0;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.card_title_img {
  height: 16px;
  width: 16px;
  margin: 3px 4px 3px 0;
}

.card_title_sub {
  margin: 4px 0;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.icon_left {
  position: absolute;
  top: 16px;
  left: -4px;
}

.icon_right {
  position: absolute;
  top: 16px;
  right: 8px;
  height: 24px;
  @include background_color_mix(--color-black, #ffffff, 20%);
  z-index: 999;

  & .title_text {
    margin: 2px 8px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    line-height: 20px;
  }
}
</style>
