<template>
  <div class="reservation_card bg-white p-t-15 p-b-15">
    <div class="flex flex-center-between m-b-8 p-l-15 p-r-15">
      <div class="reservation_table flex">
        <div class="reservation_table_item">
          <div class="reservation_table_item_title font-size-16 line-height-24">
            场地预定
          </div>
        </div>
      </div>
      <img
        class="icon_img pointer"
        @click="goList"
        src="../../images/more.png"
        alt
      />
    </div>
    <div class="reservation_list flex">
      <el-carousel
        :autoplay="false"
        indicator-position="none"
        v-if="list.length > 0"
      >
        <el-carousel-item v-for="(item, index) in list" :key="index">
          <div class="reservation-carousel-item">
            <card :item="item" />
          </div>
        </el-carousel-item>
      </el-carousel>
      <div class="h100 w100 flex flex-center-center" v-if="list.length === 0">
        <empty-data />
      </div>
    </div>
  </div>
</template>

<script>
import card from './Card'
import { getFieldList } from '../../api'
export default {
  name: 'SiteReservation',
  components: {
    card
  },
  data() {
    return {
      list: []
    }
  },
  created() {
    this.getFieldList()
  },
  methods: {
    getFieldList() {
      getFieldList({ pageNo: 1, pageSize: 10 }).then(res => {
        this.list = res.list
      })
    },
    goList() {
      this.$router.push({
        path: '/hatch/interact/field'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.reservation_card {
  height: 304px;
  width: 100%;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
}
.reservation_table {
  height: 24px;
}
.reservation_table_item_title {
  font-weight: 550;
  @include font_color(--color-text-primary);
}
.reservation_list {
  height: calc(100% - 24px);
  width: 100%;
}
.el-carousel {
  width: 100% !important;
  overflow-y: hidden;
}
.reservation-carousel-item {
  width: calc(100% - 30px);
  margin: 0 auto;
}
:deep(.el-carousel__arrow) {
  top: 35% !important;
}
</style>
