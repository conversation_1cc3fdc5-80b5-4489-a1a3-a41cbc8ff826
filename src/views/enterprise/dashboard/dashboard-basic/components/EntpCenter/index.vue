<template>
  <div class="entp_center_card bg-white">
    <div class="entp_head p-16">
      <div class="flex">
        <div class="">
          <img
            class="entp_logo"
            :src="
              entpInfo.logoUrl
                ? entpInfo.logoUrl
                : require('../../images/default-avatar.png')
            "
            alt=""
          />
        </div>
        <!--        logoUrl-->
        <div class="entp_info font-size-16 line-height-24 line-2">
          {{ entpInfo.enterpriseName || '待完善' }}
        </div>
      </div>
      <!--      <div class="flex align-items-center m-t-8">-->
      <!--        <div class="font-size-12 line-heght-20 color-text-secondary m-r-4 tips">-->
      <!--          信息完善度-->
      <!--        </div>-->
      <!--        <div class="p-t-2 p-b-2 w100">-->
      <!--          <el-progress-->
      <!--            :text-inside="true"-->
      <!--            :stroke-width="16"-->
      <!--            :percentage="90"-->
      <!--            :color="customColor"-->
      <!--          ></el-progress>-->
      <!--        </div>-->
      <!--      </div>-->
    </div>
    <div class="entp_body p-16">
      <div class="flex flex-center-between m-b-8">
        <div class="entp_table flex align-items-center">
          <div class="entp_table_item">
            <div class="entp_table_item_title font-size-16 line-height-24">
              常用功能
            </div>
          </div>
        </div>
        <!--        <div class="flex align-items-center pointer">-->
        <!--          <img class="icon_img" src="../../images/menu/menu.png" alt />-->
        <!--          <div class="font-size-14 line-height-22 m-l-4">管理</div>-->
        <!--        </div>-->
      </div>
      <div class="entp_list">
        <div
          class="entp_list_item pointer"
          v-for="route in routeList"
          :key="route.name"
          @click="goView(route)"
        >
          <div class="p-4 entp_list_item_img">
            <svg-icon :icon-class="route.meta.icon" :class="route.meta.type" />
          </div>
          <div class="font-size-14 line-height-22 entp_list_item_text">
            {{ route.meta.title }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getEntpInfo } from '../../api'
import { mapGetters } from 'vuex'
import { _genRouterMap } from '@/router/routerFun'

export default {
  name: 'EntpCenter',
  data() {
    return {
      customColor: '#054CE8',
      entpInfo: {},
      menuList: [
        'EnterprisePolicy', // 政策速递
        'EnterpriseCourse', // 在线课程
        'EnterpriseActivity', // 园区活动
        'EnterpriseField', // 场地预定
        'EnterpriseEnter', // 入园管理
        'EnterpriseContract', // 合同管理
        'EnterpriseWork', // 调房管理
        'EnterpriseLeaveBasic', // 离园管理
        'EnterpriseProblem' // 问题反馈
      ]
    }
  },
  computed: {
    ...mapGetters(['addRouters']),
    routerNameMap() {
      return _genRouterMap(this.addRouters, 'route')
    },
    routeList() {
      return this.mateNameRoute(this.menuList)
    }
  },
  created() {
    this.getEntpInfo()
  },
  methods: {
    mateNameRoute(names) {
      const routes = []
      names.forEach(name => {
        const route = this.routerNameMap[name]
        if (route) {
          routes.push(route)
        }
      })

      return routes
    },
    getEntpInfo() {
      getEntpInfo().then(res => {
        if (res) {
          this.entpInfo = res
        }
      })
    },
    goView(e) {
      this.$router.push({
        path: e.path
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.entp_center_card {
  height: 450px;
  width: 100%;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
}
.entp_head {
  border-bottom-width: 1px;
  border-bottom-style: solid;
  @include border_color(--border-color-lighter);
}
.entp_logo {
  height: 60px;
  width: 60px;
  border-radius: 6px;
}
.entp_info {
  width: calc(100% - 64px);
  margin-left: 10px;
  margin-top: 6px;
  margin-bottom: 6px;
}
.tips {
  width: 60px;
  white-space: nowrap;
}
.el-progress-bar__outer {
  background: #e7e7e7;
}
.entp_body {
  height: calc(100% - 120px);
}
.entp_table {
  height: 24px;
}
.entp_table_item_title {
  font-weight: 550;
  @include font_color(--color-text-primary);
}
.entp_list {
  display: flex;
  flex-flow: wrap;
}
.entp_list_item {
  padding: 16px;
  &:nth-child(3n + 1) {
    padding-left: 10px;
  }
  &:nth-child(3n) {
    padding-right: 10px;
  }
  .entp_list_item_img {
    .svg-icon {
      width: 24px;
      height: 24px;
    }
    .primary {
      @include font_color(--color-primary);
    }
    .warning {
      @include font_color(--color-warning);
    }
    .success {
      @include font_color(--color-success);
    }
    .danger {
      @include font_color(--color-danger);
    }
  }
  &:hover,
  :active {
    .entp_list_item_img {
      @include background_color_mix(--color-text-regular, #ffffff, 90%);
    }
    .entp_list_item_text {
      @include font_color(--color-primary);
    }
  }
}
.entp_list_item_img {
  margin: 0 12px 8px;
  width: 32px;
  height: 32px;
  border-radius: 3px;
}
</style>
