<template>
  <div class="preview-wrapper pos-relative">
    <div class="flex">
      <div class="preview-left p-t-24">
        <div
          class="font-size-16 color-black font-strong line-height-24 preview-title"
        >
          {{ informationData.title }}
        </div>

        <div
          class="m-b-16 m-t-10 font-size-12 flex align-items-center justify-content-center color-text-regular"
        >
          <div class="m-r-30">阅读数：{{ informationData.viewCount }}</div>
          <div class="m-r-30">发布时间：{{ informationData.publishTime }}</div>
          <el-popover
            v-if="isShow"
            placement="top-start"
            width="500"
            popper-class="link-view-popover"
            trigger="click"
          >
            <div class="" v-loading="loading">
              <div class="link-view">
                <div class="image-qr">
                  <img
                    style="width: 120px; height: 120px"
                    v-if="qrData.shareImg"
                    :src="qrData.shareImg"
                    alt="image"
                  />
                </div>
                <div>
                  <p class="m-t-6">复制链接分享公告</p>
                  <div class="flex m-t-10 m-b-10">
                    <el-input
                      ref="copy-input"
                      class="link-input"
                      :value="url"
                      readonly
                    ></el-input>
                    <el-button
                      ref="copy-input"
                      type="info"
                      class="m-l-6"
                      @click="copeData(url)"
                      >复制网页链接</el-button
                    >
                  </div>
                  <div class="flex">
                    <el-input
                      class="link-input"
                      :value="qrData.shareUrl"
                      readonly
                    ></el-input>
                    <el-button
                      type="info"
                      class="m-l-6"
                      @click="copeData(qrData.shareUrl)"
                      >复制公告链接</el-button
                    >
                  </div>
                </div>
              </div>
            </div>
            <div
              class="flex align-items-center share"
              slot="reference"
              @click="shareHander"
            >
              <svg-icon class="font-size-12" icon-class="forward-info" />
              <span class="p-l-4">{{ informationData.shareCount }}</span>
            </div>
          </el-popover>
        </div>

        <div
          v-html="$options.filters.richTextFilter(informationData.content)"
          class="color-black font-size-14 line-height-22 overflow-hidden"
        ></div>
      </div>

      <div class="preview-right p-t-8 p-l-16">
        <div class="m-b-24">
          <div
            class="preview-right-title font-size-14 color-text-primary m-b-16"
          >
            附件信息
          </div>
          <div>
            <files-list
              v-if="informationAttach && informationAttach.length"
              :files="informationAttach"
              onlyForView
            />
            <empty-data v-else />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FilesList from '@/components/Uploader/files'
import { richTextFilter } from '@/filter'
import { getNoticeShareInfo } from '../../api'

export default {
  name: 'informationPreview',
  props: {
    informationData: {
      type: Object,
      default: null
    },
    isShow: {
      type: Boolean,
      default: true
    },
    id: {
      type: Number,
      default: null
    }
  },
  filters: { richTextFilter },
  components: {
    FilesList
  },
  data() {
    return {
      loading: false,
      url: window.location.href,
      qrData: {}
    }
  },
  methods: {
    shareHander() {
      this.loading = true
      getNoticeShareInfo(this.id).then(res => {
        this.qrData = res
        this.loading = false
      })
    },
    copeData(data) {
      //复制功能
      this.copyText(data).then(
        () => {
          this.$message({
            message: '复制成功',
            type: 'success'
          })
        },
        () => {
          this.$message({
            message: '复制失败',
            type: 'error'
          })
        }
      )
    },
    copyText(data) {
      return new Promise((resolve, reject) => {
        let oInput = document.createElement('input')
        oInput.value = data
        document.body.appendChild(oInput)
        oInput.select() // 选择对象
        document.execCommand('Copy') // 执行浏览器复制命令
        oInput.className = 'oInput'
        oInput.style.display = 'none'
        if (oInput.value) {
          resolve()
        } else {
          reject()
        }
      })
    }
  },
  computed: {
    informationAttach() {
      return this.informationData.attachMap.informationAttach || []
    }
  }
}
</script>

<style lang="scss" scoped>
.share {
  cursor: pointer;
}
.link-view-popover .link-view {
  display: grid;
  grid-template-columns: 25% 70%;
  grid-gap: 15px;
  .image-qr {
    img {
      width: 100%;
    }
  }
}
.preview-wrapper {
  .preview-left {
    flex: 0 0 640px;
    padding-right: 15px;
    box-sizing: border-box;
    border-right-width: 1px;
    border-style: solid;
    overflow: hidden;
    @include border_color(--border-color-base);
    .preview-title {
      text-align: center;
    }

    .primary-tag {
      border-radius: 12px;
    }
  }

  .preview-right {
    flex: 1;
  }
  .position-tag {
    left: 0;
    top: 0;
  }

  :deep(.el-tag--warning) {
    border-radius: 0 100px 100px 0;
  }
}
</style>
<style lang="scss">
.link-view-popover {
  .el-loading-spinner {
    top: 50px !important;
    z-index: 99999999;
  }
}
</style>
