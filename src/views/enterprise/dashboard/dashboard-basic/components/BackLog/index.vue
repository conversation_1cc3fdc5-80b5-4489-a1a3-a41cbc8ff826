<template>
  <div
    class="bg-white backlog p-16 border-color-light"
    :class="[list.length === 0 ? 'backlog_sub' : '']"
  >
    <div class="m-b-16" v-if="list.length > 0">
      <el-carousel height="54px" indicator-position="none" direction="vertical">
        <el-carousel-item v-for="(item, index) in list" :key="index">
          <div
            @click="previewEvent(item)"
            class="flex flex-center-between notice-bg font-size-14 p-16"
          >
            <div class="pointer flex align-items-center message">
              <img
                class="icon_img m-r-8"
                src="../../images/message_icon.png"
                alt=""
              />
              <div class="font-size-14 line-height-22 line-1">
                {{ item.title }}
              </div>
            </div>
            <div class="pointer flex align-items-center">
              <div class="font-size-12 line-height-20 color-text-secondary">
                {{ item.createTime }}
              </div>
              <!--              <div class="font-size-14 line-height-22 color-primary m-l-24 more">查看更多</div>-->
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <div class="flex flex-center-between m-b-8">
      <!--      <div class="font-size-16 line-height-24 font-strong m-r-4">-->
      <!--        我的待办 <span class="color-primary">{{ backlogList.length }}</span>-->
      <!--      </div>-->
      <div class="policy_table flex">
        <div
          class="policy_table_item pointer"
          v-for="(item, index) in tableList"
          :key="index"
          @click="selectTable(index, item.key)"
        >
          <div
            class="policy_table_item_title font-size-16 line-height-24 pos-relative"
            :class="[tableIndex === index ? 'title_active' : '']"
          >
            {{ item.label }}
            <div class="red-dot" v-if="item.key === 2 && item.value > 0"></div>
          </div>
          <div
            class="policy_table_item_border"
            :class="[tableIndex === index ? 'border_active' : '']"
          ></div>
        </div>
      </div>
      <!--      <img class="icon_img pointer" src="../../images/swap-right.png" alt="" />-->
    </div>
    <div class="flex flex-wrap w100 back_log_list">
      <div
        v-for="(item, index) in backlogList"
        :key="index"
        class="font-size-14 line-height-22 p-8 work-item"
      >
        <div class="flex justify-content-between align-items-center m-b-4">
          <div class="line-1" style="flex: 1">{{ item.titleStr }}</div>
          <div class="color-primary pointer" @click="getDetails(item)">
            {{ item.buttonStr }}
          </div>
        </div>
        <div class="color-text-placeholder font-size-12 line-height-20 m-b-4">
          {{ parseTime(item.time, '{y}-{m}-{d}') }}
        </div>
        <div class="color-text-secondary line-2">
          {{ item.content }}
        </div>
      </div>
      <div
        class="h100 w100 flex flex-center-center"
        v-if="backlogList.length === 0"
      >
        <empty-data />
      </div>
    </div>

    <!-- 公告详情 -->
    <dialog-cmp
      title="公告详情"
      :visible.sync="visible"
      width="920px"
      :haveOperation="false"
    >
      <div>
        <notice-detail
          :id="informationData.id"
          :informationData="informationData"
        />
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import {
  getInformationList,
  getInformationDetails,
  getEntList,
  readParameter,
  readAgent
} from '../../api'
import NoticeDetail from './noticeDetail'
import { parseTime } from '@/utils/tools'

export default {
  name: 'BackLog',
  components: {
    NoticeDetail
  },
  data() {
    return {
      parseTime,
      tableList: [{ title: '政策速递' }, { title: '热点资讯' }],
      tableIndex: 0,
      backlogList: [],
      list: [],
      count: 0,
      visible: false,
      informationData: {}, // 资讯详情数据
      agent: 1
    }
  },
  created() {
    this.getInformationList()
    this.getEnterMessage()
    this.readAgent()
  },
  methods: {
    async readAgent() {
      const res = await readAgent()
      this.tableList = res.map(item => {
        return { label: item.label, value: item.num, key: item.key }
      })
    },
    selectTable(index, key) {
      this.agent = key
      this.tableIndex = index
      this.getEnterMessage()
    },
    getInformationList() {
      getInformationList({
        pageNo: 1,
        pageSize: 10,
        status: true
      }).then(res => {
        this.list = res.list
      })
    },
    async getEnterMessage() {
      const res = await getEntList({ agent: this.agent })
      this.backlogList = res
    },
    async getDetails(row) {
      const res = await readParameter(row.id)
      console.log(res)
      // if(!row.msgParam) return
      // row.status === 1 && readMsg(row.id)
      const paths = new Map()
      // const msgParam = JSON.parse(row.msgParam)
      paths.set(100, '/company/manage/vehicle') // 车辆管理 router.name: HatchProblemDetail
      paths.set(101, '/company/manage/index') // 员工管理 router.name: HatchFiledApplyDetailsBasic
      paths.set(102, '/work/handle/enterDetails') // 入园信息详情 router.name: ProcessEnterParkDetailListBasic
      paths.set(103, '/work/handle/adjust') // 调房申请 router.name: ProcessContractDetailsBasic
      paths.set(104, '/work/handle/leave') // 离园申请 router.name: ProcessLeaveParkDetailListBasic
      paths.set(105, '/hatch/interact/field/record') // 场地申请详情 router.name: ProcessLeaveParkDetailListBasic
      paths.set(106, '/hatch/interact/problem/detail') // 问题反馈详情 router.name: HatchProblemDetail
      paths.set(107, '/finance/ticket/ticketDetail') // 开票申请详情 router.name: HatchProblemDetail
      paths.set(108, '/finance/fees/payment/detail') // 缴费通知详情 router.name: HatchProblemDetail
      paths.set(109, '/finance/fees/payment/detail') // 缴费通知详情 router.name: HatchProblemDetail
      paths.set(110, '/finance/account/drawMoneyDetail') // 提现详情 router.name: HatchProblemDetail
      paths.set(111, '/work/handle/informationDetail') // 信息填报详情 router.name: HatchProblemDetail
      paths.set(112, '/company/manage/applyDetail') // 公租房租房详情
      paths.set(113, '/company/manage/renewDetail') // 公租房续约详情
      paths.set(114, '/company/manage/quitHouseDetail') // 公租房退房详情
      this.$router.push({
        path: paths.get(res.urlId),
        query: {
          id: res.id,
          orderId: res.orderId
        }
      })
    },
    // 查看
    previewEvent(row) {
      getInformationDetails(row.id).then(res => {
        this.informationData = res
        this.visible = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.backlog {
  height: 338px;
  border-radius: 6px;
  border-width: 1px;
  border-style: solid;
  .policy_table {
    height: 33px;
    user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
  }
  .policy_table_item {
    margin-left: 16px;
    margin-right: 16px;
    &:first-child {
      margin-left: 0;
    }
    &:last-child {
      margin-right: 0;
    }
  }
  .policy_table_item_title {
    font-weight: 400;
    @include font_color(--color-text-regular);
    .red-dot {
      position: absolute;
      top: 4px;
      right: -10px;
      width: 6px;
      height: 6px;
      @include background_color(--color-danger);
      border-radius: 50%;
    }
  }
  .title_active {
    font-weight: 550;
    @include font_color(--color-text-primary);
  }
  .policy_table_item_border {
    margin: 2px 16px 4px;
    width: calc(100% - 32px);
    height: 3px;
    background-color: unset;
  }
  .border_active {
    @include background_color(--color-primary);
  }
  @include border_color(--border-color-lighter);
  .notice-bg {
    @include background_color_mix(--color-primary, #ffffff, 90%);
  }
  .message {
    width: calc(100% - 215px);
  }
  .more {
    &:hover,
    :active {
      @include font_color(--color-text-secondary);
    }
  }
  .work-item {
    width: 50%;
    padding-left: 16px;
    @include font_color(--color-text-primary);
    &:nth-child(2n + 1) {
      padding-left: 8px;
      padding-right: 16px;
      border-right-width: 1px;
      border-style: solid;
      @include border_color(--border-color-lighter);
    }
  }
}
.backlog_sub {
  height: 284px;
}
.icon_img {
  height: 20px;
  width: 20px;
}
.back_log_list {
  height: calc(100% - 102px);
  overflow-y: auto;
}
.el-carousel {
  width: 100% !important;
  overflow-y: hidden;
}
:deep(.el-carousel__arrow) {
  top: 40% !important;
}
</style>
