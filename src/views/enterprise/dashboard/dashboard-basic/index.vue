<template>
  <div class="dashboard-basic">
    <div
      class="flex align-items-center justify-content-center p-t-14 p-b-14 pos-fixed w100 message-info bg-color-primary"
      v-if="enterParkStatus === 0"
    >
      <div class="m-r-4">
        <img src="./images/error-circle-filled.png" alt="" />
      </div>
      <div class="color-danger font-12 line-height-20 m-r-16">
        您的企业已离园，暂时无法体验完整功能
      </div>
      <!--      <el-button type="primary" @click="skipEnterPark" size="mini"-->
      <!--        >入园申请</el-button-->
      <!--      >-->
    </div>
    <userinfo-card v-else />
    <div class="lateral-wrapper pro">
      <img class="pro_bg_img" src="./images/header-bg.png" alt="" />
      <el-row :gutter="16">
        <el-col :span="18">
          <!-- 个人介绍 -->
          <introduction-cmp />
          <!-- 待办事项 -->
          <back-log-cmp />
          <!-- 政策咨询 -->
          <policy-consultation class="m-t-16" />
          <!-- 在线课程 -->
          <!--          <online-courses class="m-t-16" />-->
        </el-col>
        <el-col :span="6">
          <entp-center class="m-t-8" />
          <!-- 热门活动 -->
          <park-activity class="m-t-16" />
          <!-- 场地预定 -->
          <site-reservation class="m-t-16" />
        </el-col>
      </el-row>
    </div>
    <!--    信息填报弹窗-->
    <information-dialog />
  </div>
</template>

<script>
import IntroductionCmp from './components/Introduction'
import BackLogCmp from './components/BackLog'
import PolicyConsultation from './components/PolicyConsultation'
import EntpCenter from './components/EntpCenter'
// import OnlineCourses from './components/OnlineCourses'
import ParkActivity from './components/ParkActivity'
import SiteReservation from './components/SiteReservation'
import { getEnterParkStatus } from './api'
import UserinfoCard from './components/userinfo'
import InformationDialog from '@/views/enterprise/dashboard/dashboard-basic/components/informationDialog'
import { routeExport } from '@/utils/routeExport'
import enterpriseRoutes from '@/router/modules/enterprise'

export default {
  name: 'EnterpriseDashboard',
  components: {
    UserinfoCard,
    InformationDialog,
    IntroductionCmp,
    BackLogCmp,
    PolicyConsultation,
    EntpCenter,
    // OnlineCourses,
    ParkActivity,
    SiteReservation
  },
  data() {
    return {
      enterParkStatus: null
    }
  },
  mounted() {
    // 路由菜单导出
    routeExport(enterpriseRoutes)
  },
  // created() {
  //   this.getEnterParkStatus()
  // },
  methods: {
    skipEnterPark() {
      this.$router.push({
        path: '/work/handle/enter'
      })
    },

    // 判断是否可以入园
    getEnterParkStatus() {
      getEnterParkStatus().then(res => {
        this.enterParkStatus = res
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.message-info {
  top: 56px;
  height: 48px;
  z-index: 999;
  background-image: url('./images/message-bg.png');
  background-size: 100% 100%;
}
.pro {
  padding-top: 44px;
  padding-bottom: 24px;
}
.pro_bg_img {
  position: absolute;
  top: -60px;
  width: 1216px;
}
</style>
