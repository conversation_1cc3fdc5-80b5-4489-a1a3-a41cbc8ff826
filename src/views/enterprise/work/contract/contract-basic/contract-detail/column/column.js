import { getPayStatus } from '@/views/manage/house/contract/contract-basic/utils/status'
import { getPlanType } from '@/views/manage/house/contract/contract-special/utils/status'
import { parseTime } from '@/utils/tools'

export default {
  data() {
    return {
      subsTableColumn: [
        {
          prop: 'contractNo',
          label: '协议编号',
          minWidth: '150px',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <el-link
                style="display:inline"
                type="primary"
                onClick={() => {
                  this.detailHandle(scope.row)
                }}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          prop: 'statusStr',
          label: '状态',
          width: '120px'
        },
        {
          prop: 'park',
          label: '园区',
          minWidth: '150px'
        },
        {
          prop: 'area',
          label: '协议面积(㎡)',
          width: '120px'
        },
        {
          prop: 'typeStr',
          label: '协议类型',
          width: '120px'
        },
        {
          prop: 'signDate',
          label: '签订日期',
          width: '120px'
        },
        {
          prop: 'startTime',
          label: '起始时间',
          width: '120px'
        },
        {
          prop: 'endTime',
          label: '截止时间',
          width: '120px'
        }
      ],
      mainTableColumn: [
        {
          prop: 'contractNo',
          label: '合同编号',
          minWidth: '150px',
          showOverflowTooltip: true,
          render: (h, scope) => {
            return (
              <el-link
                style="display:inline"
                type="primary"
                onClick={() => {
                  this.detailHandle(scope.row)
                }}
              >
                {scope.row.contractNo}
              </el-link>
            )
          }
        },
        {
          prop: 'statusStr',
          label: '状态',
          width: '120px'
        },
        {
          prop: 'park',
          label: '园区',
          minWidth: '150px'
        },
        {
          prop: 'area',
          label: '合同面积(㎡)',
          width: '120px'
        },
        {
          prop: 'typeStr',
          label: '合同类型',
          width: '120px'
        },
        {
          prop: 'signDate',
          label: '签订日期',
          width: '120px'
        },
        {
          prop: 'startTime',
          label: '起始时间',
          width: '120px'
        },
        {
          prop: 'endTime',
          label: '截止时间',
          width: '120px'
        }
      ],
      tableColumn: [
        {
          label: '楼栋',
          prop: 'building'
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '费用类型',
          prop: 'feeName'
        },
        {
          label: '时间',
          prop: 'time'
        },
        {
          renderHeader: () => {
            return (
              <div class="flex align-items-center">
                <span class="m-r-4">租金标准</span>
                {!this.$route.query.applyId &&
                  this.detailCost.cooperationType !== 2 && (
                    <el-tooltip
                      placement="top"
                      effect="dark"
                      content="根据房间的收费标准获取，可修改仅对此份合同生效"
                    >
                      <i class="el-icon-info"></i>
                    </el-tooltip>
                  )}
              </div>
            )
          },
          prop: 'fixAmount',
          render: (h, { row }) => {
            return (
              <div>{row.fixAmount ? `${row.fixAmount} ${row.unit}` : '-'}</div>
            )
          }
        }
      ],
      tableColumn1: [
        {
          label: '楼栋',
          prop: 'building'
        },
        {
          label: '房号',
          prop: 'room'
        },
        {
          label: '合同面积(m²)',
          prop: 'executeArea'
        },
        {
          label: '起租日',
          prop: 'addTime'
        },
        {
          label: '优惠开始日',
          prop: 'freeStartTime'
        },
        {
          label: '优惠结束日',
          prop: 'freeEndTime'
        },
        {
          label: '退租日',
          prop: 'earlyTime'
        },
        {
          label: '是否提前退租',
          prop: 'early',
          render: (h, scope) => {
            return <span>{scope.row.early ? '是' : '否'}</span>
          }
        }
      ],
      tableColumn2: [
        {
          label: '期数',
          prop: 'periodNumber'
        },
        {
          label: '费用类型',
          prop: 'planTypeStr'
        },
        {
          label: '账单应缴时间',
          prop: 'billPayTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.billPayTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '账单周期',
          prop: 'billingCycle',
          width: 200,
          render: (h, scope) => {
            return (
              <div>
                {parseTime(scope.row.periodStartTime, '{y}-{m}-{d}')}-
                {parseTime(scope.row.periodEndTime, '{y}-{m}-{d}')}
              </div>
            )
          }
        },
        {
          label: '账单金额(元)',
          prop: 'amount',
          render: (h, scope) => {
            return (
              <div class="color-warning">{scope.row.amount.toFixed(2)}</div>
            )
          }
        },
        {
          label: '出账状态',
          prop: 'status',
          render: (h, scope) => {
            return (
              <div class="color-primary">{getPlanType(scope.row.status)}</div>
            )
          }
        },
        {
          label: '已收状态',
          prop: 'payStatus',
          render: (h, scope) => {
            return (
              <div class="color-success">
                {getPayStatus(scope.row.payStatus)}
              </div>
            )
          }
        }
      ],
      tableColumnTotalPlan: [
        {
          label: '期数',
          prop: 'period'
        },
        {
          label: '开始时间',
          prop: 'startTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.startTime, '{y}-{m}-{d}')}</div>
          }
        },
        {
          label: '结束时间',
          prop: 'endTime',
          render: (h, scope) => {
            return <div>{parseTime(scope.row.endTime, '{y}-{m}-{d}')}</div>
          }
        }
      ],
      tableColumnCost: [
        {
          label: '费用名称',
          prop: 'name'
        },
        {
          label: '计划单价',
          prop: 'price',
          width: 180,
          render: (h, scope) => {
            return <div>{scope.row.price + scope.row.unit}</div>
          }
        },
        {
          label: '修正后执行单价(元)',
          prop: 'fixPrice',
          width: 180,
          render: (h, scope) => {
            return <div>{scope.row.fixPrice + scope.row.unit}</div>
          }
        },
        {
          label: '同比',
          prop: 'ratio',
          width: 180,
          render: (h, scope) => {
            return (
              <div
                class={
                  scope.row.ratioValue < 0 ? 'color-danger' : 'color-success'
                }
              >
                {scope.row.ratio}
              </div>
            )
          }
        },
        {
          label: '修正原因',
          prop: 'reason'
        }
      ],
      tableColumnHouse: [
        {
          label: '位置',
          prop: 'buildRoom',
          width: 180
        },
        {
          label: '系统面积(m²)',
          prop: 'area',
          width: 180
        },
        {
          label: '修正后面积(m²)',
          prop: 'acArea',
          width: 180
        },
        {
          label: '同比',
          prop: 'percentage',
          width: 180,
          render: (h, scope) => {
            return (
              <div class={'color-success'}>{scope.row.percentage * 100}%</div>
            )
          }
        },
        {
          label: '修正时间',
          prop: 'createTime',
          width: 180
        },
        {
          label: '操作人',
          prop: 'opUserName',
          width: 180
        },
        {
          label: '修正原因',
          prop: 'reason',
          fixed: 'right',
          render: (h, scope) => {
            return (
              <el-popover placement="bottom-end" width="300" trigger="click">
                <div class={'p-8 w100'} style={'border: 1px solid #DCDCDC;'}>
                  {scope.row.reason === '' ? '暂无修正原因~' : scope.row.reason}
                </div>
                {/*<div class={'p-8 w100'} v-show={scope.row.reason === null || scope.row.reason === ''}*/}
                {/*     style={'border: 1px solid #DCDCDC;'}>暂无修正原因~</div>*/}
                <el-button slot="reference" type="text">
                  查看
                </el-button>
              </el-popover>
            )
          }
        }
      ]
    }
  }
}
