import request from '@/utils/request'

// 企业端 状态列表
export function getLeavingEntStatusList(params) {
  return request({
    url: `/leaving/ent/status_list`,
    method: 'get',
    params
  })
}

// 获得离园申请列表
export function getLeavingEntList(params) {
  return request({
    url: `/leaving/ent/list`,
    method: 'get',
    params
  })
}

// 创建离园申请草稿
export function getLeavingEntdDraft(data) {
  return request({
    url: `/leaving/ent/draft`,
    method: 'post',
    data
  })
}

// 提交离园申请
export function getLeavingEntdSumbit(data) {
  return request({
    url: `/leaving/ent/submit`,
    method: 'post',
    data
  })
}

// 获得离园申请
export function getLeavingEntdGet(params) {
  return request({
    url: `/leaving/ent/get`,
    method: 'get',
    params
  })
}

// 删除离园申请
export function getLeavingEntdDelete(id) {
  return request({
    url: `/leaving/ent/delete?id=${id}`,
    method: 'DELETE'
  })
}

// 获得离园申请详情
export function getLeavingEntdDetail(params) {
  return request({
    url: `/leaving/ent/detail`,
    method: 'get',
    params
  })
}

// 详情 - 获得流程进度详情
export function getLeavingEntdProcess(id) {
  return request({
    url: `/leaving/park/process/${id}`,
    method: 'get'
  })
}
// 异常账单
export function getLeavingEntTotalFeeDetail(params) {
  return request({
    url: `/leaving/ent/total_fee_detail`,
    method: 'get',
    params
  })
}
