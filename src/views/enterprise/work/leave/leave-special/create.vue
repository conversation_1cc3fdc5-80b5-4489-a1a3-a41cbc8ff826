<template>
  <div class="lateral-wrapper p-t-32">
    <div class="detail-wrapper p-32">
      <div class="m-b-42"><Breadcrumb /></div>
      <div class="m-b-16">基本信息</div>
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
        label-position="top"
      />
    </div>
    <detail-footer ref="footer" @submitHandler="submitHandler" />
  </div>
</template>

<script>
import descriptorMixins from './column/descriptor'
import DetailFooter from './components/detailFooter'
import { getLeavingEntdSumbit } from './api'
import { parseTime } from '@/utils/tools'
import Breadcrumb from '@/components/Breadcrumb'
import { getByTenantDictType } from '@/api/common'
export default {
  name: 'EnterpriseLeaveCreate',
  components: { DetailFooter, Breadcrumb },
  mixins: [descriptorMixins],
  data() {
    return {
      fromModel: {},
      parseTime
    }
  },
  created() {
    this.getByTenantDictType()
  },
  methods: {
    getByTenantDictType() {
      getByTenantDictType('leaving_reason_type').then(res => {
        this.formConfigure.descriptors.leavingReasonType.options = res || []
      })
    },
    // 提交办理
    submitHandler() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          await getLeavingEntdSumbit({
            ...this.fromModel
          })
          this.$toast.success('提交办理成功')
          this.$router.go(-1)
        }
      })
    }
  }
}
</script>

<style scoped>
.detail-wrapper {
  border-radius: 6px 6px 0 0;
  opacity: 1;
  border: 1px solid #e9f0ff;
  border-bottom: none;
}
</style>
