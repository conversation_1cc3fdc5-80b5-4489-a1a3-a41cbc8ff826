<template>
  <div>
    <div class="font-size-14 m-b-16">基本信息</div>
    <div class="p-l-16">
      <el-row class="info-item">
        <el-col class="m-b-16" :span="12">
          <span class="item-label">离园日期</span>
          <span class="item-content">{{
            parseTime(objDetail.leavingDate, '{y}-{m}-{d}')
          }}</span>
        </el-col>
        <el-col class="m-b-16" :span="24">
          <span class="item-label">离园原因</span>
          <span class="item-content">
            {{ noData(objDetail.leavingReason) }}
          </span>
        </el-col>
        <el-col class="m-b-16" :span="12">
          <span class="item-label">银行账号</span>
          <span class="item-content">{{
            noData(objDetail.leavingAccount)
          }}</span>
        </el-col>
        <el-col class="m-b-16" :span="12">
          <span class="item-label">开户行</span>
          <span class="item-content">{{ noData(objDetail.leavingBank) }}</span>
        </el-col>
        <el-col class="m-b-16" :span="12">
          <span class="item-label">账户名称</span>
          <span class="item-content">{{ noData(objDetail.accountName) }}</span>
        </el-col>
        <el-col :span="24">
          <span class="item-label">意见和建议</span>
          <span class="item-content inline-block">
            {{ noData(objDetail.advice) }}
          </span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { parseTime } from '@/utils/tools'
import { noData } from '@/filter'

export default {
  name: 'Information',
  props: {
    objDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      parseTime,
      noData
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.info-item) {
  font-size: 14px;
  line-height: 22px;
  margin-bottom: 16px;
  &:last-child {
    margin-bottom: 0;
  }
  .el-col {
    display: flex;
    .item-label {
      color: rgba(0, 0, 0, 0.4);
      margin-right: 16px;
      flex-shrink: 0;
    }
    .item-content {
      color: rgba(0, 0, 0, 0.9);
    }
    .item-user {
      display: flex;
      align-items: center;
      font-size: 12px;
      .user-icon {
        color: #00a870;
      }
      .user-content {
        color: rgba(0, 0, 0, 0.6);
        line-height: 20px;
        margin-left: 4px;
        margin-right: 18px;
      }
      .calendar-icon {
        color: #ed7b2f;
      }
    }
  }
}
</style>
