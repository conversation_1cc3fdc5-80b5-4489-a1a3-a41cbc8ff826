import request from '@/utils/request'

// 获取入园申请列表
export function getLeavePage(params) {
  return request({
    url: `/leave/record/pageEnt`,
    method: 'get',
    params
  })
}

// 获取企业正在执行中的合同
export function getContract() {
  return request({
    url: `/leave/record/getContract`,
    method: 'get'
  })
}

// 企业新增离园申请记录
export function leaveAdd(data) {
  return request({
    url: `/leave/record/add`,
    method: 'post',
    data
  })
}

// 获取离园申请详情
export function getLeaveParkApplyInfo(data) {
  return request({
    url: `/leave/record/detail?id=${data}`,
    method: 'get'
  })
}

// 终止合同
export function getTermination(data) {
  return request({
    url: `/leave/record/termination?contractId=${data.contractId}&leaveId=${data.leaveId}`,
    method: 'get'
  })
}

// 撤回离园申请
export function getRevocation(data) {
  return request({
    url: `/leave/record/withdraw?leaveId=${data}`,
    method: 'get'
  })
}
