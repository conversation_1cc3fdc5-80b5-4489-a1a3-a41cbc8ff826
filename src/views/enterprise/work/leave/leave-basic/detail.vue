<template>
  <div class="leave-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      :img="require('./images/header-bgc.png')"
      :imgOpacity="1"
    >
      <div class="steps">
        <div>
          <breadcrumb />
        </div>
        <div class="flex flex-center-center">
          <steps :active="active" />
        </div>
      </div>
    </module-header>

    <approval-pend
      v-if="active === 1"
      :applyData="applyData"
      :list="list"
      @nextStep="nextStep"
    />
    <review v-if="active === 0" :applyData="applyData" @nextStep="nextStep" />
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Review from './review'
import ApprovalPend from './approval-pend'
import Breadcrumb from '@/components/Breadcrumb'
import Steps from './steps'
import { getLeaveParkApplyInfo } from './api'
export default {
  name: 'EnterpriseLeavetDetail',
  components: {
    ModuleHeader,
    Steps,
    Review,
    ApprovalPend,
    Breadcrumb
  },
  data() {
    return {
      active: 0,
      applyData: {},
      list: []
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getLeaveParkApplyInfo()
      this.active = this.active + 1
    }
  },
  methods: {
    // 上一步
    previousStep() {
      this.getLeaveParkApplyInfo()
      this.active = this.active - 1
    },

    // 下一步
    nextStep() {
      this.active = this.active + 1
    },
    // 获取离园申请详情
    getLeaveParkApplyInfo() {
      getLeaveParkApplyInfo(this.$route.query.id).then(res => {
        this.applyData = res || {}
        if (res.status === 5 || res.status === 7) {
          this.active = 0
        }
        this.list = res.leaveContractRespVO.map(item => {
          return {
            ...item,
            ...JSON.parse(item.contractImage)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.steps {
  padding-top: 30px;
}
</style>
