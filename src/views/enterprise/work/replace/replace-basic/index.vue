<template>
  <div class="activity-basic">
    <!-- 头部 -->
    <module-header
      type="primary"
      title="调房申请"
      desc="在线向园区提交房间变更申请，一键办理所有业务"
      :img="require('./images/header-bg.png')"
      :imgOpacity="1"
    />

    <div class="lateral-wrapper">
      <!-- 内容区域 -->
      <div class="module-list">
        <module-list ref="ModuleList" :api-fn="getReviseApplyPage">
          <el-button
            v-show="showApply && enterParkStatus !== 0"
            @click="getCheckEnterpriseAuth"
            slot="right"
            type="text"
            size="mini"
          >
            <svg-icon icon-class="add" />
            <span class="color-text-primary line-height-22">新增申请</span>
          </el-button>
          <template slot-scope="scope">
            <div class="card-wrapper">
              <div
                class="replace"
                v-for="data in scope.data"
                :key="data.id"
                @click="goDetail(data)"
              >
                <angle-status
                  class="icon_left"
                  :type="getReplaceEntStatus('', data.entStatus).type"
                  :text="getReplaceEntStatus('', data.entStatus).label"
                />
                <div class="icon_right">
                  <div class="title_text">{{ data.area + 'm²' }}</div>
                </div>
                <div class="replace_bgc">
                  <img
                    class="replace_bgc_img"
                    src="./images/bg-image.png"
                    alt=""
                  />
                </div>
                <div class="card_title">{{ getReplaceType(data.type) }}</div>
                <div class="card_title_sub flex align-items-center">
                  <img
                    class="card_title_img"
                    src="./images/location.png"
                    alt=""
                  />{{
                    (data.type === 3 ? '所属园区：' : '意向园区：') +
                    (data.expectPark || '-')
                  }}
                </div>

                <div class="card_title_sub flex align-items-center">
                  <img
                    class="card_title_img"
                    src="./images/calendar.png"
                    alt=""
                  />{{ '申请时间：' + (data.applyDate || '-') }}
                </div>
              </div>
            </div>
          </template>
        </module-list>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ModuleList from '@/components/Lateral/ModuleList'
import AngleStatus from '@/components/Lateral/AngleStatus'
import {
  getReviseApplyPage,
  checkEnterpriseAuth,
  checkCountUnderWay,
  getEnterParkStatus
} from './api'
import { getReplaceEntStatus, getReplaceType } from './utils/status'
export default {
  name: 'EnterpriseReplace',
  components: {
    ModuleHeader,
    ModuleList,
    AngleStatus
  },
  data() {
    return {
      getReviseApplyPage,
      getReplaceEntStatus,
      getReplaceType,
      isEnterpriseAuth: false,
      showApply: false,
      enterParkStatus: null
    }
  },
  created() {
    this.getEnterParkStatus()
    this.checkCountUnderWay()
    this.initData()
  },
  methods: {
    async initData() {
      const res = await getEnterParkStatus()
      this.addPlace = res
    },
    goDetail(val) {
      this.$router.push({
        path: '/work/handle/replace/detail',
        query: {
          id: val.id,
          entStatus: val.entStatus
        }
      })
    },
    // 判断是否可以入园
    getEnterParkStatus() {
      getEnterParkStatus().then(res => {
        this.enterParkStatus = res
      })
    },
    checkCountUnderWay() {
      checkCountUnderWay().then(res => {
        if (res === 0) {
          this.showApply = true
        }
      })
    },
    getCheckEnterpriseAuth() {
      checkEnterpriseAuth().then(res => {
        if (res) {
          this.$router.push({
            path: '/work/handle/replace/detail'
          })
        } else {
          this.$confirm('调房申请前，请先完成企业认证', '完善企业信息', {
            confirmButtonText: '去填报'
          }).then(() => {
            console.info('======')
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.module-filter {
  margin-top: 24px;
}
.module-list {
  margin-top: 16px;
}
:deep(.card-wrapper) {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .card-list {
    width: 50%;
    margin-bottom: 32px;
    &:nth-child(odd) {
      padding-right: 14px;
    }
    &:nth-child(even) {
      padding-left: 14px;
    }
  }
}
.replace {
  position: relative;
  width: 276px;
  height: 336px;
  @include background_color('--color-white');
  border-radius: 6px;
  border: 1px solid #e9f0ff;
  backdrop-filter: blur(6px);
  margin-right: 32px;
  margin-bottom: 32px;
  &:nth-child(4n) {
    margin-right: 0;
  }
}

.replace_bgc {
  margin: 8px;
  width: 260px;
  height: 220px;
  background: #e9f0ff;
  border-radius: 3px;
  opacity: 0.9;
  border: 1px solid;
  // prettier-ignore
  border-image: linear-gradient(
    34deg,
    rgba(255, 255, 255, 0.4),
    rgba(255, 255, 255, 0)
  )
    1 1;
  backdrop-filter: blur(4px);
}
.replace_bgc_img {
  margin: 52px 56px 0;
  width: 148px;
  height: 168px;
}
.card_title {
  margin: 8px 16px;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}
.card_title_img {
  height: 16px;
  width: 16px;
  margin: 3px 4px 3px 0;
}
.card_title_sub {
  margin: 8px 16px;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}
.icon_left {
  position: absolute;
  top: 16px;
  left: -4px;
}
.icon_right {
  position: absolute;
  top: 16px;
  right: 16px;
  min-width: 40px;
  height: 24px;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 3px;
  z-index: 999;
  & .title_text {
    margin: 2px 8px;
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    line-height: 20px;
  }
}
</style>
