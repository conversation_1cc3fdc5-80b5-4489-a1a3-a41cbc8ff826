<template>
  <div>
    <module-header
      type="primary"
      title="调房申请详情"
      :img="require('./images/header-bg-2.png')"
      :imgOpacity="1"
    >
      <div class="m-t-24"><Breadcrumb /></div>
    </module-header>
    <div class="lateral-wrapper">
      <div class="m-t-24 m-b-24">
        <div class="m-b-24">基本信息</div>
        <table
          v-if="objData.type === 0"
          style="width: 100%"
          border="1"
          Cellspacing="0"
          Cellpadding="0"
        >
          <tr>
            <th>申请时间</th>
            <td>{{ objData.applyDate | noData }}</td>
            <th>申请类型</th>
            <td>{{ objData.typeStr | noData }}</td>
          </tr>
          <tr>
            <th>意向园区</th>
            <td>{{ objData.intendedPark | noData }}</td>
            <th>需求面积(㎡)</th>
            <td>{{ objData.area | noData }}</td>
          </tr>
          <tr>
            <th>期望日期</th>
            <td>{{ objData.occupancyTime | noData }}</td>
            <th>办理状态</th>
            <td>
              <basic-tag :label="objData.statusStr" :type="getStatusType()" />
            </td>
          </tr>
          <tr>
            <th>主要联系人</th>
            <td>{{ objData.contact | noData }}</td>
            <th>联系方式</th>
            <td>{{ objData.phone | noData }}</td>
          </tr>
          <tr>
            <th>增房原因</th>
            <td :colspan="3">{{ objData.cause | noData }}</td>
          </tr>
          <tr>
            <th>企业基本情况</th>
            <td :colspan="3">{{ objData.entBasic | noData }}</td>
          </tr>
        </table>
        <table
          v-else
          style="width: 100%"
          border="1"
          Cellspacing="0"
          Cellpadding="0"
        >
          <tr>
            <th>申请时间</th>
            <td>{{ objData.applyDate | noData }}</td>
            <th>申请类型</th>
            <td>
              <div class="flex align-items-center">
                <span>{{ objData.typeStr | noData }}</span>
                <span
                  v-if="objData.changeRoom === false"
                  class="font-size-12 tip-item refund"
                  >退</span
                >
                <span
                  v-if="objData.changeRoom === true"
                  class="font-size-12 tip-item"
                  >换</span
                >
              </div>
            </td>
          </tr>
          <tr>
            <th>退租合同</th>
            <td>
              <el-link
                type="primary"
                v-if="objData.contractId"
                @click="contractDetail"
                >{{ objData.contractNo }}</el-link
              >
              <span v-else>{{ objData.contractNo | noData }}</span>
            </td>
            <th>退租房间</th>
            <td>{{ objData.quitRoom | noData }}</td>
          </tr>
          <tr>
            <th>期望日期</th>
            <td>{{ objData.quitDate | noData }}</td>
            <th>办理状态</th>
            <td>
              <basic-tag :label="objData.statusStr" :type="getStatusType()" />
            </td>
          </tr>
          <tr>
            <th>需求面积(㎡)</th>
            <td :colspan="3">{{ objData.needArea | noData }}</td>
          </tr>
          <tr>
            <th>主要联系人</th>
            <td>{{ objData.contact | noData }}</td>
            <th>联系方式</th>
            <td>{{ objData.phone | noData }}</td>
          </tr>
          <tr>
            <th>调整原因</th>
            <td :colspan="3">{{ objData.reason | noData }}</td>
          </tr>
          <tr>
            <th>企业基本情况</th>
            <td :colspan="3">{{ objData.entBasic | noData }}</td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import Breadcrumb from '@/components/Breadcrumb'
import { getReviseApplyDetail } from './api'

export default {
  name: 'ReplaceDetail',
  components: {
    ModuleHeader,
    Breadcrumb
  },
  data() {
    return {
      objData: {}
    }
  },
  created() {
    this.getReviseApplyDetail()
  },
  methods: {
    contractDetail() {
      this.$router.push({
        path: '/work/handle/contract/detail',
        query: {
          id: this.objData.contractId
        }
      })
    },
    getStatusType() {
      if (this.objData.reject) return 'danger'
      const obj = {
        2: 'primary',
        3: 'primary',
        4: 'success'
      }
      return obj[this.objData.status]
    },
    // 详情 - 获得详情
    async getReviseApplyDetail() {
      let id = this.$route.query.id
      const res = await getReviseApplyDetail(id)
      this.objData = res
    }
  }
}
</script>

<style lang="scss" scoped>
table {
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  font-size: 14px;
}

th {
  width: 15%;
  @include background_color_mix(--color-primary, #ffffff, 96%);
  padding: 10px;
  font-weight: 400;
  text-align: left;
}

td {
  width: 35%;
  border-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  word-break: break-all;
  padding: 10px 10px 11px 10px;
  line-height: 1.6em;
  word-wrap: break-word;
}

.tip-item {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  @include font_color(--color-primary);
  @include background_color_mix(--color-primary, #ffffff, 80%);
  margin-left: 4px;
  &.refund {
    @include font_color(--color-danger);
    @include background_color_mix(--color-danger, #ffffff, 80%);
  }
}
</style>
