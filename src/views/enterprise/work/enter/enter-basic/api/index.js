import request from '@/utils/request'

// 获取入园申请列表
export function getApplyPage(params) {
  return request({
    url: `/enter/ent/park-apply/getApplyPage`,
    method: 'get',
    isTable: true,
    params
  })
}

// 判断是否可以入园
export function getEnterParkStatus() {
  return request({
    url: `/enter/ent/park-apply/enterParkStatus`,
    method: 'get'
  })
}

// 获得项目信息
export function getProjectInfo(params) {
  return request({
    url: `/pjct/project_info/get`,
    method: 'get',
    params
  })
}
// 获取通知记录
export function projectNoticeList(params) {
  return request({
    url: `/pjct/project_notice/notice_user_list`,
    method: 'get',
    params
  })
}
// 收到通知
export function projectNoticeReceived(params) {
  return request({
    url: `/pjct/project_notice/received`,
    method: 'get',
    params
  })
}

// 企业端获得项目信息分页
export function getEntPage(params) {
  return request({
    url: `/pjct/admin/project_info/ent_page`,
    method: 'get',
    params
  })
}
