<template>
  <div class="details-container">
    <div class="details-wrapper">
      <settled-directory
        style="width: 17%"
        ref="directory"
        containerRef="detailsContainer"
      />
      <el-scrollbar id="el-scroll" class="details-content" ref="elScrollbar">
        <details-container style="width: 79%" ref="detailsContainer" />
        <details-notice style="width: 21%" ref="detailsNotice" />
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import SettledDirectory from './settledDirectory'
import DetailsContainer from './detailsContainer'
import DetailsNotice from './detailsNotice'
import { getProjectInfo } from '../api'
export default {
  name: 'SettledDetails',
  components: {
    DetailsNotice,
    DetailsContainer,
    SettledDirectory
  },
  provide() {
    return {
      SettledCreate: this
    }
  },
  async mounted() {
    await this.initData()
  },
  methods: {
    async initData() {
      try {
        const id = this.$route.query.id
        const res = await getProjectInfo({ id })
        await this.$refs.detailsContainer.initData(res)
        await this.$refs.detailsNotice.initData(res)
      } catch (e) {
        console.error(e)
      }
    },
    // dom滚动
    navPageHandle(dom) {
      this.$refs.elScrollbar.wrap.scrollTo({
        top: dom.offsetTop,
        behavior: 'smooth'
      })
    }
  }
}
</script>

<style scoped lang="scss">
.details-container {
  position: fixed;
  left: 0;
  top: 60px;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    180deg,
    rgba(234, 240, 255, 0.2) 0%,
    rgba(246, 249, 255, 0.2) 100%
  );
  .details-wrapper {
    display: flex;
    width: 100%;
    height: calc(100% - 40px);
    .details-content {
      width: 100%;
      height: 100%;
    }
  }
}
::v-deep {
  .el-scrollbar__wrap {
    overflow-x: hidden;
    .el-scrollbar__view {
      padding: 32px 40px 0 0;
      display: flex;
      width: 100%;
    }
  }
  .is-horizontal {
    display: none;
  }
}
</style>
