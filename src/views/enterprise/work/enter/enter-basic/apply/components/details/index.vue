<template>
  <div>
    <div>
      <!-- 头部 -->
      <module-header
        type="primary"
        :img="require('../../images/header-bg.png')"
        :imgOpacity="1"
      >
        <enter-header
          :applyData="applyData"
          @getEnterParkApplyInfo="getEnterParkApplyInfo"
        />
      </module-header>
    </div>

    <div class="m-t-32 m-b-32">
      <div class="lateral-wrapper">
        <div class="intention">
          <!-- 意向园区工商成员信息 -->
          <intention-garden :applyData="applyData" />
        </div>

        <div>
          <!-- 企业诉求 -->
          <enterprise-demand :applyData="applyData" />
        </div>

        <div>
          <!-- 项目基础信息 -->
          <project-basis-info :applyData="applyData" />
        </div>

        <div>
          <!-- 经济指标 -->
          <development-potential
            :parkData="parkData.developData"
            :parkList="parkData.economicData"
            :applyData="applyData"
          />
        </div>

        <div>
          <!-- 申请材料 -->
          <application-material :applyData="applyData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import IntentionGarden from './intention-garden'
import EnterpriseDemand from './enterprise-demand'
import ProjectBasisInfo from './project-basis-info'
import DevelopmentPotential from './development-potential'
import ApplicationMaterial from './application-material'
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import EnterHeader from './enter-header'
import { getEnterParkInfo } from './api'

export default {
  name: 'EnterParkDetails',
  components: {
    IntentionGarden,
    EnterpriseDemand,
    ProjectBasisInfo,
    DevelopmentPotential,
    ApplicationMaterial,
    ModuleHeader,
    EnterHeader
  },
  data() {
    return {
      id: null,
      parkData: {
        intentionParkInfo: {}, //意向园区信息
        demandInfo: {}, //企业诉求信息
        projectBasisInfo: {}, //项目基础信息
        stockRights: [], //股权信息
        economicData: [] //股权信息
      },
      applyData: {} // 全都申请数据
    }
  },
  created() {
    const { id } = this.$route.query
    this.id = id
    this.getEnterParkApplyInfo()
  },
  methods: {
    getEnterParkApplyInfo() {
      getEnterParkInfo(this.id).then(res => {
        this.applyData = res
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.lateral-wrapper {
  .intention {
    margin-bottom: 8px;
    margin-top: 32px;
  }

  :deep(.el-card) {
    box-shadow: none;
    border: none;
  }
}
</style>
