export default {
  data() {
    return {
      tableColumn: [
        {
          prop: 'index',
          label: '序号',
          width: 60,
          render: (h, scope) => {
            return <div>{scope.$index + 1}</div>
          }
        },
        {
          prop: 'attachName',
          label: '附件名称'
        },
        {
          prop: 'attachmentNum',
          label: '附件数',
          width: 120,
          render: (h, scope) => {
            return (
              <div
                class="flex align-items-center pointer"
                onClick={() => {
                  this.seeAttachDetails(scope)
                }}
              >
                <span>
                  {scope.row.list?.length ? scope.row.list?.length : 0}
                </span>
                <svg-icon
                  class="color-primary"
                  icon-class="caret-right-small"
                />
              </div>
            )
          }
        }
      ]
    }
  }
}
