import { enterTypeOptions, whetherOptions } from '../../units/status'

export default {
  data() {
    return {
      formConfigure: {
        descriptors: {
          parkId: {
            form: 'select',
            label: '意向园区',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择意向园区'
              }
            ],
            options: [],
            customTips: () => {
              return <div>请选择您想要入住的园区</div>
            }
          },
          a: {
            form: 'select',
            label: '入园须知',
            span: 12,
            render: () => {
              return (
                <a
                  class="color-primary underline"
                  onclick={() => {
                    this.openEnterInfo()
                  }}
                >
                  点击查看入园须知
                </a>
              )
            }
          },
          b: {
            form: 'select',
            label: '招商电话',
            span: 12,
            render: () => {
              return (
                <div class="color-primary">
                  {this.currentParkData.contact || this.currentParkData.mobile
                    ? (this.currentParkData.contact || '') +
                      ' ' +
                      (this.currentParkData.mobile || '')
                    : '-'}
                </div>
              )
            },
            customTips: () => {
              return <div>建议提前与园区招商人员取得联系，提高入园成功率</div>
            }
          },
          enterType: {
            form: 'select',
            label: '入驻方式',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择入驻方式'
              }
            ],
            options: enterTypeOptions,
            customTips: () => {
              return (
                <div>
                  仅工商注册：在该园区进行工商地址注册，但实际办公地址不在该地
                </div>
              )
            }
          }
        }
      },
      formConfigureRecommend: {
        descriptors: {
          recommend: {
            form: 'radio',
            label: '推荐企业',
            span: 12,
            rule: [
              {
                required: true,
                type: 'number',
                message: '请选择是否为推荐企业'
              }
            ],
            options: whetherOptions,
            customTips: () => {
              return <div>是否为当地管委会推荐企业</div>
            }
          },
          seat: { span: 12 },
          recommendOrganization: {
            form: 'input',
            label: '推荐机构',
            span: 12,
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请填写推荐机构的完整名称'
              }
            ]
          },
          recommendContacts: {
            form: 'input',
            label: '推荐人',
            hidden: true,
            span: 12,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入推荐人姓名及联系方式'
              }
            ]
          },
          attachList: {
            form: 'component',
            label: '推荐文件',
            hidden: true,
            rule: [
              {
                type: 'array',
                message: '请上传推荐文件'
              }
            ],
            componentName: 'uploader',
            customTips: () => {
              return (
                <div>
                  仅支持 PDF、Word、Excel、PNG/JPG图片，最大文件尺寸 10 MB
                </div>
              )
            },
            props: {
              uploadData: {
                type: 'enterParkRecommend'
              },
              maxSize: 10
            }
          }
        }
      }
    }
  }
}
