import request from '@/utils/request'

// 获取园区无分页
export function getPark() {
  return request({
    url: `/housing/park/listAll`,
    method: 'get',
    hideLoading: true
  })
}

// 入园申请保存园区选择
export function saveParkSelection(data) {
  return request({
    url: `/enter/ent/park-apply/apply/stepOne`,
    method: 'post',
    data
  })
}

// 获取入园申请详情
export function getEnterParkApplyInfo(id) {
  return request({
    url: `/enter/ent/park-apply/getApply?id=${id}`,
    method: 'get'
  })
}

//获取主要经济指标季度
export function getEconomicQuarter(date) {
  return request({
    url: `/enter/ent/park-apply/apply/getEconomicQuarter?date=${date}`,
    method: 'get',
    hideLoading: true
  })
}

// 入园申请提交表单-第二步
export function saveEnterpriseInfo(data) {
  return request({
    url: `/enter/ent/park-apply/apply/stepTwo`,
    method: 'post',
    data
  })
}

// 入园申请提交表单-第三步
export function saveProjectInfo(data) {
  return request({
    url: `/enter/ent/park-apply/apply/stepThree`,
    method: 'post',
    data
  })
}

// 入园申请提交表单-第四步
export function saveDemandInfo(data) {
  return request({
    url: `/enter/ent/park-apply/apply/stepFour`,
    method: 'post',
    data
  })
}

// 入园申请提交表单-第五步
export function saveFileInfo(data) {
  return request({
    url: `/enter/ent/park-apply/apply/stepFive`,
    method: 'post',
    data
  })
}

// 提交审批
export function submitApply(applyId) {
  return request({
    url: `/enter/ent/park-apply/apply/submit?applyId=${applyId}`,
    method: 'get'
  })
}
