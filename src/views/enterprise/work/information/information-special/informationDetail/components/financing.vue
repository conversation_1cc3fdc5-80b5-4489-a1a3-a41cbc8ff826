<template>
  <!--  融资信息-->
  <div class="financing-container">
    <drive-table
      ref="drive-table"
      :table-data="tableList"
      height="calc(100vh - 450px)"
      :columns="tableFinancing"
    >
      <template v-slot:operate-right>
        <el-button :disabled="isDisabled" type="primary" @click="openHandler"
          ><svg-icon icon-class="add" /><span>新增</span></el-button
        >
      </template>
    </drive-table>
    <!--    融资信息弹窗-->
    <dialog-cmp
      :title="title"
      :visible.sync="visible"
      width="35%"
      @confirmDialog="confirmDialog"
    >
      <driven-form
        v-if="visible"
        ref="driven-form"
        label-position="top"
        v-model="fromModel"
        :formConfigure="formFinancing"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import ColumnMixins from '../column/column'
import descriptorMixins from '../descriptor/descriptor'
import {
  getFinancingCreate,
  getFinancingDelete,
  getFinancingInfo,
  getFinancingList,
  getFinancingUpdate
} from '@/views/enterprise/work/information/information-special/api'

export default {
  name: 'Financing',
  mixins: [ColumnMixins, descriptorMixins],
  props: {
    objDetail: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableList: [],
      visible: false,
      title: '融资历史',
      fromModel: {},
      id: '',
      isDisabled: false
    }
  },
  created() {
    this.getFinancingList()
    this.init()
    // if (this.$route.query.status === '3') {
    //   this.isDisabled = true
    // } else {
    //   this.isDisabled = false
    // }
  },
  methods: {
    init() {
      let bufferDate =
        new Date(this.$parent.objDetail.bufferDate).getTime() + 86400000
      let nowDate = new Date().getTime()
      if (nowDate > bufferDate || this.$route.query.status === '3') {
        this.isDisabled = true
      } else {
        this.isDisabled = false
      }
    },
    async getFinancingList() {
      let taskId = this.$route.query.id
      const res = await getFinancingList({ taskId })
      this.tableList = res
    },
    // 打开弹窗
    openHandler() {
      this.visible = true
      this.fromModel = {}
    },
    // 弹框提交按钮
    confirmDialog() {
      this.$refs['driven-form'].validate(async valid => {
        if (valid) {
          let taskId = this.$route.query.id
          if (this.id) {
            await getFinancingUpdate({
              ...this.fromModel,
              taskId,
              dateSource: 1,
              id: this.id
            })
            this.$toast.success('编辑成功')
            this.id = ''
          } else {
            await getFinancingCreate({
              ...this.fromModel,
              taskId,
              dataSource: 1
            })
            this.$toast.success('新增成功')
          }
          this.visible = false
          this.getFinancingList()
        }
      })
    },
    // 编辑
    async editHandler(row) {
      this.id = row.id
      this.visible = true
      const res = await getFinancingInfo({ id: row.id })
      let { approvedTime, apLevel, apName, certificateNo } = res
      this.fromModel = {
        approvedTime,
        apLevel,
        apName: String(apName),
        certificateNo
      }
    },
    // 删除
    delHandler(row) {
      this.$confirm('确定删除该任务？').then(async () => {
        await getFinancingDelete(row.id)
        this.$toast.success('删除成功')
        this.getFinancingList()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.financing-container {
  padding: 0 24px 24px;
}
</style>
