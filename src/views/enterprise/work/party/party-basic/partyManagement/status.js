export function applyTypeStatus(h, val) {
  switch (val) {
    case 1:
      return '申请成立党组织(支部)'
    case 2:
      return '申请成立群团组织'
    case 3:
      return '申请党员需要接转组织关系'
    case 4:
      return '申请发展党员'
    case 5:
      return '其他需求'
    default:
      return '-'
  }
}

export function applyStatus(h, val) {
  switch (val) {
    case 0:
      return <basic-tag isDot type="info" label="待分配" />
    case 1:
      return <basic-tag isDot type="warning" label="待对接" />
    case 2:
      return <basic-tag isDot type="primary" label="对接中" />
    case 3:
      return <basic-tag isDot type="success" label="通过" />
    case 4:
      return <basic-tag isDot type="danger" label="退回" />
    default:
      return '-'
  }
}
