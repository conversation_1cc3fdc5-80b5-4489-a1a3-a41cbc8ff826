// 正则校验座机、手机号
const telRules = (rule, value, callback) => {
  if (/^((0\d{2,3}-\d{7,8})|(1[3456789]\d{9}))$/.test(value) || !value) {
    callback()
  } else {
    callback('请输入正确格式的座机、手机号')
  }
}

export default {
  data() {
    return {
      formConfigure: {
        labelWidth: '110px',
        descriptors: {
          applyType: {
            form: 'select',
            span: 24,
            label: '需求类型',
            options: [
              {
                label: '申请成立党组织(支部)',
                value: 1
              },
              {
                label: '申请成立群团组织',
                value: 2
              },
              {
                label: '申请党员需要接转组织关系',
                value: 3
              },
              {
                label: '申请发展党员',
                value: 4
              }
            ],
            rule: [
              {
                required: true,
                type: 'number',
                message: '请输入需求类型'
              }
            ]
          },
          entPerson: {
            form: 'input',
            span: 24,
            label: '企业联系人',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业联系人'
              },
              {
                max: 20,
                message: '最多输入20个字符'
              }
            ]
          },
          entPersonPhone: {
            form: 'input',
            span: 24,
            label: '企业联系方式',
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入企业联系方式'
              },
              {
                validator: telRules
              }
            ]
          },
          content: {
            form: 'input',
            span: 24,
            label: '需求内容',
            hidden: true,
            rule: [
              {
                required: true,
                type: 'string',
                message: '请输入需求内容'
              }
            ],
            props: {
              type: 'textarea'
            }
          }
        }
      }
    }
  }
}
