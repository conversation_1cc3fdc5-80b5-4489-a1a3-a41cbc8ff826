<template>
  <div class="partyManagement">
    <module-header
      type="primary"
      title="党建管理/党建需求"
      :img="require('../images/header-bg.png')"
      :imgOpacity="0.2"
      desc="党建管理/党建需求，党建管理/党建需求"
    />
    <div class="lateral-wrapper p-t-24" style="height: 100%">
      <div style="text-align: end">
        <el-button class="btn-admin" type="primary" @click="toDetailHandler">
          <div class="flex align-items-center">
            <svg-icon icon-class="add" /><span style="display: inline-block"
              >需求登记</span
            >
          </div>
        </el-button>
      </div>
      <div class="lateral-wrapper p-t-24">
        <drive-table
          ref="drive-table"
          :columns="tableColumn"
          :api-fn="partyPageList"
        >
        </drive-table>
      </div>
    </div>
    <basic-drawer
      title="需求登记"
      :visible.sync="drawerVisible"
      :haveFooter="true"
      :size="500"
      :haveOperation="true"
      @confirmDrawer="confirmDrawer"
    >
      <div v-if="drawerVisible">
        <driven-form
          ref="drive-form"
          v-model="fromModel"
          :formConfigure="formConfigure"
        />
      </div>
    </basic-drawer>
  </div>
</template>

<script>
import { partyPageList, addCreate } from '../api/index'
import descriptorMixins from './descriptor'
import ModuleHeader from '@/components/Lateral/ModuleHeader'
import ColumnMixins from './column'
export default {
  name: 'index',
  mixins: [ColumnMixins, descriptorMixins],
  components: {
    ModuleHeader
  },
  data() {
    return {
      partyPageList,
      drawerVisible: false,
      fromModel: {}
    }
  },
  watch: {
    'fromModel.applyType': {
      handler(val) {
        if (val === 5) {
          this.formConfigure.descriptors.content.hidden = false
        } else {
          this.formConfigure.descriptors.content.hidden = true
        }
      }
    }
  },
  methods: {
    toDetailHandler() {
      this.fromModel = {}
      this.drawerVisible = true
    },
    confirmDrawer() {
      console.log()
      // addCreate
      this.$refs['drive-form'].validate(valid => {
        if (valid) {
          let params = {
            ...this.fromModel,
            userId: this.$store.getters.userInfo.userId
          }
          addCreate(params).then(res => {
            console.log(res)
            this.$toast.success('添加成功')
            this.drawerVisible = false
            this.$refs['drive-table'].refreshTable()
          })
        }
      })
    }
  }
}
</script>

<style scoped></style>
