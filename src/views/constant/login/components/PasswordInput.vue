<template>
  <el-input
    :class="{ password: pwdType === 'password' }"
    :value="value"
    @input="input"
    size="large"
    :placeholder="placeholder"
    autocomplete="off"
  >
    <svg-icon slot="prefix" icon-class="lock-on" class="font-size-18" />
    <svg-icon
      slot="suffix"
      :icon-class="browseIcon"
      class="font-size-18 pointer"
      @click="toggleShowPwd"
    />
  </el-input>
</template>

<script>
export default {
  name: 'PasswordInput',
  props: {
    value: String,
    placeholder: {
      type: String,
      default: '请输入密码'
    }
  },
  data() {
    return {
      syncValue: this.value,
      pwdType: 'password',
      browseIcon: 'browse',
      password: ''
    }
  },
  methods: {
    input(value) {
      this.syncValue = value
    },
    // 切换密码显示类型
    toggleShowPwd() {
      this.pwdType = this.pwdType === 'text' ? 'password' : 'text'
      this.browseIcon = this.browseIcon === 'browse' ? 'browse-off' : 'browse'
    }
  },
  watch: {
    syncValue(value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style lang="scss" scoped>
.password {
  :deep(input) {
    font-family: 'password';
  }
}
</style>
