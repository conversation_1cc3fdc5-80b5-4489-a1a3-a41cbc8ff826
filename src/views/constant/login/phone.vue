<template>
  <div class="phone-login">
    <!-- 表单区域 -->
    <div class="form-content">
      <phone-code ref="PhoneCodeForm" v-model="form" type="01" />
    </div>
    <!-- 协议 -->
    <div class="protocol flex">
      <!-- <el-checkbox v-model="agree">
        <span>已阅读并接受</span>
        <a class="color-primary" href="#">《用户协议》</a>
        <span>和</span>
        <a class="color-primary" href="#">《隐私协议》</a>
      </el-checkbox> -->
    </div>
  </div>
</template>

<script>
import PhoneCode from './components/PhoneCode.vue'

export default {
  name: 'PhoneLogin',
  inject: ['login'],
  components: { PhoneCode },
  props: {
    redirect: String
  },
  data() {
    return {
      form: {},
      rules: {}
    }
  },
  methods: {
    // 登录
    handleLogin() {
      // 表单验证通过后触发登录
      this.$refs.PhoneCodeForm.validate(() => {
        this.login.openLoading()
        this.$store
          .dispatch('MobileLogin', this.form)
          .then(() => {
            this.$router.push('/')
            // this.$router.push(this.redirect || '/')
            this.login.closeLoading()
          })
          .catch(() => {
            this.login.closeLoading()
          })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.protocol {
  padding-top: 16px;
}
</style>
