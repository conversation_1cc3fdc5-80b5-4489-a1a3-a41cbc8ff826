import handleClipboard from '@/utils/clipboard'

const copyEl = (el, binding) => {
  const { value } = binding
  const elSpan = document.createElement('span')
  const copySpan = document.createElement('span')
  elSpan.innerHTML = value
  copySpan.innerHTML = '<i class="el-icon-document-copy"></i>复制'
  copySpan.style.marginLeft = '15px'
  copySpan.style.color = '#ED7B2F'
  copySpan.style.cursor = 'pointer'
  copySpan.style.fontWeight = 'normal'
  copySpan.style.fontSize = '14px'
  copySpan.style.lineHeight = '14px'
  copySpan.style.opacity = '0'
  copySpan.onclick = e => {
    handleClipboard(value, e)
  }
  el.innerHTML = ''
  el.appendChild(elSpan)
  el.appendChild(copySpan)
  el.onmouseover = () => {
    elSpan.style.background = '#e9f0fe'
    copySpan.style.opacity = '1'
  }
  el.onmouseout = () => {
    elSpan.style.background = 'transparent'
    copySpan.style.opacity = '0'
  }
}
export default {
  inserted(el, binding) {
    copyEl(el, binding)
  },
  update(el, binding) {
    copyEl(el, binding)
  }
}
