/*
 * @Descripttion: 按钮权限处理指令
 * @Author: 田柱
 * @Date: 2021-04-13 16:08:13
 * @LastEditTime: 2021-09-09 10:07:59
 */

import store from '@/store'
import { ALL_PERMISSION } from '@/settings'

export default {
  inserted(el, binding) {
    const { value } = binding
    const permissions = store.getters && store.getters.permissions

    if (permissions && permissions[0] === ALL_PERMISSION) {
      return true
    }

    if (value && value instanceof Array && value.length > 0) {
      const hasPermission = permissions.some(permission => {
        return permission === ALL_PERMISSION || value.includes(permission)
      })

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      return true
    }
  }
}
