/*
 * @Descripttion: 文字超出显示tooltip
 * @Author: 田柱
 * @Date: 2021-04-13 16:08:39
 * @LastEditTime: 2021-04-13 16:13:35
 */

import Vue from 'vue'
import { Tooltip } from 'element-ui'
import { debounce } from '@/utils/tools'

let tooltipInstanceArr = []

// 显示tooltip防抖
const _debounceShowPopper = debounce(tooltip => tooltip.handleShowPopper(), 50)

// 显示tooltip
function _showTooltip(el, content, placement) {
  const TooltipCmp = Vue.extend(Tooltip)
  const tooltipInstance = new TooltipCmp({
    propsData: {
      placement: placement || 'top'
    }
  }).$mount()
  tooltipInstance.content = content || undefined
  tooltipInstance.referenceElm = el
  tooltipInstance.$refs.popper &&
    (tooltipInstance.$refs.popper.style.display = 'none')
  tooltipInstance.doDestroy()
  tooltipInstance.setExpectedState(true)
  _debounceShowPopper(tooltipInstance)

  if (!tooltipInstanceArr.includes(tooltipInstance)) {
    tooltipInstanceArr.push(tooltipInstance)
  }

  return tooltipInstance
}

// 隐藏tootip
function _hideTooltip(tooltip) {
  if (tooltip) {
    tooltip.setExpectedState(false)
    tooltip.handleClosePopper()
  }
}

function _computedEl(el, binding) {
  let tooltipInstance

  const { value, arg: placement = 'top' } = binding

  let elLabel = ''
  // cascader下特殊处理
  let elWidth = 0
  if (value.indexOf('@@@&&&') > -1) {
    const [label, width] = value.split('@@@&&&')
    elWidth = parseFloat(width)
    el.textContent = label
    elLabel = label
  } else {
    el.textContent = elLabel = binding.value
  }
  const elComputed = document.defaultView.getComputedStyle(el, '')
  const padding =
    parseInt(elComputed.paddingLeft.replace('px', '')) +
    parseInt(elComputed.paddingRight.replace('px', ''))

  const range = document.createRange()
  range.setStart(el, 0)
  range.setEnd(el, el.childNodes.length)
  const rangeWidth = range.getBoundingClientRect().width

  if (
    rangeWidth + padding > el.offsetWidth ||
    el.scrollWidth > el.offsetWidth ||
    (elWidth && elWidth < el.textContent.length * 14)
  ) {
    el.onmouseenter = () => {
      tooltipInstance = _showTooltip(el, elLabel, placement)
    }

    el.onmouseleave = () => {
      _hideTooltip(tooltipInstance)
    }
  } else {
    el.onmouseenter = null
    el.onmouseleave = null
  }

  return tooltipInstance
}

export default {
  inserted(el, binding) {
    el.className = el.className + ' line-1'
    _computedEl(el, binding)
  },
  update(el, binding) {
    _computedEl(el, binding)
    if (tooltipInstanceArr.length > 0) {
      tooltipInstanceArr.forEach(tooltipInstance =>
        _hideTooltip(tooltipInstance)
      )
      tooltipInstanceArr = []
    }
  }
}
