/*
 * @Descripttion: 小数指令
 * @Author: 田柱
 * @Date: 2021-04-13 16:08:13
 * @LastEditTime: 2021-04-13 16:11:58
 */

export default {
  bind: (el, { value = 2 }) => {
    el = el.nodeName === 'INPUT' ? el : el.children[0]
    const RegStr =
      value === 0
        ? `^[\\+\\-]?\\d+\\d{0,0}`
        : `^[\\+\\-]?\\d+\\.?\\d{0,${value}}`

    el.addEventListener('keyup', () => {
      if (el.value !== '-') {
        let newRegStr = el.value.replace(/,/g, '')
        el.value = newRegStr.match(new RegExp(RegStr, 'g'))
      }
      el.dispatchEvent(new Event('input'))
    })

    el.addEventListener('blur', () => {
      if (el.value === '-') {
        el.value = 0
      }
      el.dispatchEvent(new Event('input'))
    })
  }
}
