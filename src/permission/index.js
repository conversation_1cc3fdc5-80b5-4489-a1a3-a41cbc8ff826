/*
 * @Descripttion: 系统权限核心逻辑
 * @Author: 田柱
 * @Date: 2021-04-17 12:39:27
 * @LastEditTime: 2021-09-27 10:19:54
 */

// 全局路由
import router from '@/router'
// 系统仓库
import store from '@/store'
// 顶部页面加载进度条
// import NProgress from 'nprogress'
// import 'nprogress/nprogress.css'
// 消息方法
// import { Toast } from '@/utils/message'
// 获取token
import { getAccessToken } from '@/utils/auth'
// 不重定向白名单
import whiteRoutes from './whiteRoutes'
// 获取当前平台路由
import _genCurrentRoutes from './_genCurrentRoutes'
// 生成权限路由方法
import _genAccessedRoutes from './_genAccessedRoutes'
// 跳转逻辑判断方法
import jumpJudge from './jumpJudge'

// 当前平台路由路径数组
let _currentRoutesArray
// 当前平台路由权限路径数组
let _accessedRoutesArray

// 全局路由守卫
router.beforeEach(async (to, from, next) => {
  // 前往页面的name
  const toName = to.name
  // 前往页面的path
  const toPath = to.path
  // 开启进度条
  // NProgress.start()
  // 判断是否存在token
  if (getAccessToken()) {
    // 判断是否去向登录页
    if (to.name === 'Login') {
      // 去向默认页
      next('/')
      // 关闭进度条
      // NProgress.done()
    } else {
      try {
        // 初始化状态下，首次登录或刷新页面时处理逻辑
        /**
         * 实现思路：
         * 1、根据路由表和用户信息，生成权限路由，渲染菜单
         * 2、多module模式下挂在全部路由，在初始化module时，生成当前module的权限菜单，渲染菜单
         */
        if (!store.getters.userType) {
          // 获取用户信息
          const { platform, permissions } = await store.dispatch('GetInfo')

          // 获取当前平台路由
          const {
            isSingleModule,
            currentRoutes,
            currentRoutesMap,
            currentRoutesArray
          } = await _genCurrentRoutes(platform, permissions)

          _currentRoutesArray = currentRoutesArray

          // 权限菜单生成
          const { accessedRoutes, accessedRoutesArray } =
            await _genAccessedRoutes(isSingleModule, permissions, currentRoutes)
          _accessedRoutesArray = accessedRoutesArray

          // 前往页面的role
          const toRole = currentRoutesMap[toPath]
          /**
           * 单module模式，动态挂在路由
           * 多module模式下，在InitModule的actions中已经挂载
           */
          isSingleModule && router.addRoutes(accessedRoutes)
          if (whiteRoutes.includes(toName)) return next()
          if (!to.meta.allow) {
            // 首次加载，跳转逻辑判断
            jumpJudge({
              permissions,
              toRole,
              accessedRoutes,
              to,
              from,
              next,
              platform,
              _accessedRoutesArray
            })
          } else {
            next()
          }
          // 关闭进度条
          // NProgress.done()
        } else {
          /**
           * 403页面判别
           * 实现思路：
           * 根据toPath判断在不在路由映射表中
           * 不在：直接404
           * 在：判断是否在权限路由中，不在直接403,在next
           */
          if (toPath) {
            if (toPath !== '/') {
              // 判断路由是否时系统路由
              if (_currentRoutesArray.includes(toPath)) {
                // 判断是否存在权限路由,存在直接next
                if (_accessedRoutesArray.includes(toPath)) {
                  next()
                }
                // 不存在情况下，跳转403
                else {
                  next('/403')
                }
              }
              // 不是系统路由，直接next，404
              else {
                next()
              }
            } else {
              next(_accessedRoutesArray[0])
            }
          } else {
            next()
          }

          // 关闭进度条
          // NProgress.done()
        }
      } catch (e) {
        // 异常捕获，如遇到错误情况，强制登出
        // Toast.error('用户信息验证失败，请重新登录')
        setTimeout(() => {
          store.dispatch('FedLogOut')
          // 刷新页面，重置系统
          // location.reload()
          // 关闭进度条
          // NProgress.done()
        }, 3000)
        throw new Error(e)
      }
    }
  }
  // 不存在token情况下
  else {
    // 判断去向的页面是否在白名单中
    if (whiteRoutes.includes(toName)) {
      next()
    }
    // 不存在跳转到Login
    else {
      next('/login')
    }
    // 关闭进度条
    // NProgress.done()
  }
})
