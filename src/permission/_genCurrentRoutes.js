/*
 * @Descripttion: 获取当前平台路由
 * @Author: 田柱
 * @Date: 2021-04-17 13:09:07
 * @LastEditTime: 2022-05-09 16:14:23
 */

// 系统仓库
import store from '@/store'
// 当前系统路由映射表
import PlatformRouterMap from '@/router/modules/index'
// 路由权限与路径映射方法
import { _genRouterMap } from '@/router/routerFun'
import { GenAccessedRouters } from '@/store/modules/permission'

/**
 * @name: 获取当前平台路由
 * @param {String} platform 用户所属平台
 * @param {Array} permissions 用户所具备的权限role集合
 * @return {*}
 */
async function getCurrentRoutes(platform, permissions) {
  // 获取当前平台路由
  const currentPlateformRoutesMap = PlatformRouterMap[platform]

  // 当前平台路由
  let currentRoutes
  // 当前平台路由映射关系 path -> role
  let currentRoutesMap
  // 多模块权限路由树
  let platformRoutesMap = {}

  // 判断是否为单module平台
  const isSingleModule = Array.isArray(currentPlateformRoutesMap)
  store.dispatch('SetSingleModule', isSingleModule)

  // 单module平台
  if (isSingleModule) {
    currentRoutes = currentPlateformRoutesMap
    currentRoutesMap = _genRouterMap(currentRoutes)
  }
  // 多module平台
  else {
    // 初始化module，渲染页面多module模块菜单
    await store.dispatch('InitModules', currentPlateformRoutesMap)

    if (store.getters.curModule) {
      currentRoutes = currentPlateformRoutesMap[store.getters.curModule].routes
    } else {
      currentRoutes = store.getters.wholeRoutes
    }
    currentRoutesMap = _genRouterMap(store.getters.wholeRoutes)

    for (const key in currentPlateformRoutesMap) {
      // 获取权限路由
      const accessedRouters = GenAccessedRouters(
        currentPlateformRoutesMap[key].routes,
        permissions
      )
      platformRoutesMap[key] = {
        ...currentPlateformRoutesMap[key],
        accessedRouters
      }
    }
    await store.dispatch('PlatformModulesRoutesMap', platformRoutesMap)
  }

  return {
    isSingleModule,
    currentRoutes,
    currentRoutesMap,
    currentRoutesArray: Object.keys(currentRoutesMap)
  }
}

export default getCurrentRoutes
