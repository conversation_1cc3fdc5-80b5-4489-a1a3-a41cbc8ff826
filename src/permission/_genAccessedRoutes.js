/*
 * @Descripttion: 生成权限路由
 * @Author: 田柱
 * @Date: 2021-04-17 13:18:33
 * @LastEditTime: 2021-09-15 10:07:39
 */

// 系统仓库
import store from '@/store'
import { _genRouterMap } from '@/router/routerFun'

/**
 * @name: 生成权限菜单
 * @param {Boolean} isSingleModule 是否为单模块系统
 * @param {Array} permissions 权限表
 * @param {Array} routes 路由表
 * @return {Object}
 */
async function _genAccessedRoutes(isSingleModule, permissions, routes) {
  // 获取权限路由表
  const accessedRoutes = await store.dispatch('GenerateRoutes', {
    permissions,
    routes
  })

  // 权限路由map path -> role
  let accessedRoutesMap

  // 单module平台
  if (isSingleModule) {
    accessedRoutesMap = _genRouterMap(accessedRoutes)
  }
  // 多module平台
  else {
    accessedRoutesMap = _genRouterMap(store.getters.wholeAccessedRoutes)
  }

  return {
    accessedRoutes,
    accessedRoutesMap,
    accessedRoutesArray: Object.keys(accessedRoutesMap)
  }
}

export default _genAccessedRoutes
