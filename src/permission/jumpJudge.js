/*
 * @Descripttion: 跳转逻辑判断
 * @Author: 田柱
 * @Date: 2021-04-17 15:12:13
 * @LastEditTime: 2021-09-15 10:38:56
 */

import { ALL_PERMISSION } from '@/settings'
import { whetherSwitchOwnerJudge } from './enterpriseOwnerJudge'
import { deepClone } from '@/utils/tools'
import { isAllHiddenRoutes } from '@/store/modules/module'
import workspaceRoutes from '@/router/modules/workspace'

/**
 * @name: 跳转逻辑函数
 * @param {Array} permissions 权限表
 * @param {String} toRole 前往页面的role
 * @param {Array} accessedRoutes 权限路由表
 * @param {Object} to 前往页面对象
 * @param {Function} next 下一步方法
 * @return {void}
 */
function jumpJudge({
  permissions,
  toRole,
  accessedRoutes,
  to,
  next,
  platform,
  _accessedRoutesArray
}) {
  // 前往页面的path
  const toPath = to.path

  if (whetherSwitchOwnerJudge({ platform })) {
    if (toPath === '/owner/index') {
      next({ ...to, replace: true })
    } else {
      // 前往业主选择页面
      next('/owner/index')
    }
  } else {
    const filterAccessRoutes = isAllHiddenRoutes(
      deepClone(accessedRoutes)
    ).filter(route => !route.hidden)

    const firstMatchRoutePath = filterAccessRoutes[0]
      ? filterAccessRoutes[0].redirect || filterAccessRoutes[0].path
      : workspaceRoutes.path
    // permissions为['*:*:*']情况下，代表全部路由权限开放
    if (permissions.includes(ALL_PERMISSION)) {
      if (toPath === '/dashboard/index') {
        next({ ...to, replace: true })
      } else if (_accessedRoutesArray.includes(toPath)) {
        next({ ...to, replace: true })
      } else {
        next({ path: firstMatchRoutePath, replace: true })
      }
    }
    // permissions存在真实权限，
    // 去往页面的role不存在permissions中
    // 则强制跳转到权限路由第一个
    else if (
      !toRole ||
      !permissions.map(permission => permission.trim()).includes(toRole.trim())
    ) {
      if (toPath === '/dashboard/index') {
        next({ path: toPath, replace: true })
      } else {
        next({ path: firstMatchRoutePath, replace: true })
      }
    }
    // 其他情况，直接跳转到指定页面
    else {
      next({ ...to, replace: true })
    }
  }
}

export default jumpJudge
