<template>
  <keep-alive :include="cachedViews">
    <router-view :key="key" />
  </keep-alive>
</template>

<script>
export default {
  name: 'EmptyView',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.fullPath
    }
  },
  // 后续tagview放开，删掉下面代码
  watch: {
    $route(route) {
      const { name } = route
      if (name) {
        this.$store.dispatch('tagsView/addView', route)
      }
    }
  }
}
</script>

<style></style>
