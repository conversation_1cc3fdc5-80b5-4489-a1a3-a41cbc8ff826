<template>
  <section ref="appMain" class="app-main">
    <div class="lateral-mian-content" :style="lateralMinHeight">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="cachedViews">
          <router-view :key="key" />
        </keep-alive>
      </transition>
    </div>
    <back-to-top :scroll-distance="scrollDistance" :elem="$refs.appMain" />
    <copyright ref="Copyright" />
  </section>
</template>

<script>
import BackToTop from '@/components/BackToTop'
import Copyright from '@/components/Copyright'

export default {
  name: 'AppMain',
  provide() {
    return {
      appMain: this
    }
  },
  components: {
    BackToTop,
    Copyright
  },
  data() {
    return {
      scrollDistance: 0,
      keepAlive: '',
      CopyrightHeight: '0px'
    }
  },
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      this.$refs.appMain && this.$refs.appMain.scrollTo(0, 0)
      return this.$route.fullPath
    },
    lateralMinHeight() {
      return { minHeight: 'calc(100% - ' + this.CopyrightHeight + ')' }
    }
  },
  mounted() {
    window.$appMain = this.$refs.appMain
    this.$refs.appMain.addEventListener('scroll', e => {
      const scrollDistance = e.target.scrollTop
      this.scrollDistance = scrollDistance
    })
    this.CopyrightHeight = this.$refs.Copyright.$el.clientHeight + 1 + 'px'
  },
  // 后续tagview放开，删掉下面代码
  watch: {
    $route: {
      handler(route) {
        const { name } = route
        if (name) {
          this.$store.dispatch('tagsView/addView', route)
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  overflow: hidden;
  overflow-y: auto;
  background-image: url('./app-main-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
}
</style>
