<template>
  <!--修改密码弹窗-->
  <middle-dialog
    title="修改密码"
    :visible.sync="dialogVisible"
    width="25%"
    @confirmDialog="confirmDialog"
  >
    <el-form
      ref="modifyForm"
      status-icon
      :model="modifyForm"
      :rules="rules"
      label-width="80px"
    >
      <el-form-item label="原密码" prop="oldPwd">
        <el-input
          v-model="modifyForm.oldPwd"
          type="password"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input
          v-model="modifyForm.newPwd"
          type="password"
          autocomplete="off"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPwd">
        <el-input
          v-model="modifyForm.confirmPwd"
          type="password"
          autocomplete="off"
        />
      </el-form-item>
    </el-form>
  </middle-dialog>
</template>

<script>
import MiddleDialog from '@/components/BasicDialog'
import { modifyPwd } from '@/api/user'
import { mapActions } from 'vuex'

const validateOldPass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入原密码'))
  } else {
    if (value.length < 5) {
      callback(new Error('密码长度需大于等于5个字符'))
    } else {
      callback()
    }
  }
}

export default {
  name: 'ModifyPassword',
  components: { MiddleDialog },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      modifyForm: {
        oldPwd: '',
        newPwd: '',
        confirmPwd: ''
      },
      rules: {
        oldPwd: [{ validator: validateOldPass, trigger: 'blur' }],
        newPwd: [
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请输入密码'))
              } else {
                if (value.length < 5) {
                  callback(new Error('密码长度需大于等于5个字符'))
                } else {
                  if (this.modifyForm.confirmPwd !== '') {
                    this.$refs.modifyForm.validateField('confirmPwd')
                  }
                  callback()
                }
              }
            },
            trigger: 'blur'
          }
        ],
        confirmPwd: [
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请再次输入密码'))
              } else if (value !== this.modifyForm.newPwd) {
                callback(new Error('两次输入密码不一致!'))
              } else {
                callback()
              }
            },
            trigger: 'blur'
          }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    ...mapActions(['OpenGuide']),
    // 提交修改密码
    confirmDialog() {
      console.log(1)
      this.$refs.modifyForm.validate(valid => {
        if (valid) {
          console.log(2)
          modifyPwd({
            oldPassword: this.modifyForm.oldPwd,
            newPassword: this.modifyForm.newPwd
          }).then(() => {
            this.$toast({
              message: '密码修改成功',
              onClose: () => {
                this.logout()
              }
            })
          })
        } else {
          return false
        }
      })
    },

    logout() {
      this.$store.dispatch('Logout').then(() => {
        location.reload()
      })
    }
  }
}
</script>

<style></style>
