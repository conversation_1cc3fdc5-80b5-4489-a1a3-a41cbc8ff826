<template>
  <div>
    <!--    <el-button type="primary" size="mini" @click="dialogVisible = true">-->
    <!--      <svg-icon icon-class="swap" />-->
    <!--      <span>业主切换</span>-->
    <!--    </el-button>-->

    <switch-dialog
      title="业主切换"
      :visible.sync="dialogVisible"
      width="400px"
      :haveOperation="false"
    >
      <div class="switch-wrapper">
        <div class="switch-header border-color-light">
          <div
            v-for="(item, index) in alreadyTenantData"
            :key="index"
            class="switch-owner-item flex align-items-center"
            @click="changeTenant(item.tenantId)"
          >
            <div class="flex left">
              <!-- <div class="owner-icon">
                <img
                  src=""
                  alt=""
                />
              </div> -->
              <div
                class="font-size-14 color-text-primary line-height-22 owner-name line-1"
              >
                <span class="m-r-10">{{ item.tenantName }}</span>
                <el-tag
                  size="mini"
                  v-if="item.enterParkStatus === 1"
                  type="success"
                  >已入园</el-tag
                >
                <el-tag
                  size="mini"
                  v-if="item.enterParkStatus === 2"
                  type="info"
                  >未入园</el-tag
                >
              </div>
            </div>
            <div class="right">
              <svg-icon icon-class="caret-right-small" />
            </div>
          </div>
        </div>
        <div
          class="switch-footer flex justify-content-between align-items-center"
        >
          <div
            ref="add-button-ref"
            class="color-primary font-size-14 add-wrapper"
            @click="skipOwnerSwitchPage()"
          >
            <svg-icon icon-class="add" /> <span>添加业主</span>
          </div>
          <el-button type="info" @click="dialogVisible = false">取消</el-button>
        </div>
      </div>
    </switch-dialog>
  </div>
</template>

<script>
import SwitchDialog from '@/components/BasicDialog'
import { getTenantPage, changeTenant } from '@/api/user'
import { setToken, setTenant } from '@/utils/auth'

export default {
  name: 'OwnerSwitch',
  components: {
    SwitchDialog
  },
  data() {
    return {
      dialogVisible: false,
      alreadyTenantData: []
    }
  },
  created() {
    this.getTenantPage()
  },
  methods: {
    // 获取当前用户所有租户
    getTenantPage() {
      getTenantPage().then(res => {
        this.alreadyTenantData = res
      })
    },

    // 去业主切换页面
    skipOwnerSwitchPage() {
      this.dialogVisible = false
      this.$router.push({
        path: '/owner/index'
      })
    },

    changeTenant(tenantId) {
      changeTenant(tenantId).then(response => {
        const { accessToken, refreshToken, tenantId } = response
        setToken({ accessToken, refreshToken })
        // 设置租户信息
        setTenant(tenantId)
        this.dialogVisible = false
        this.$router.push({
          path: '/dashboard/index'
        })
        setTimeout(() => {
          location.reload()
        }, 400)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.switch-wrapper {
  .switch-header {
    border: 1px solid;
    margin-bottom: 24px;
    padding: 8px;

    .switch-owner-item {
      padding: 8px 10px;
      margin-bottom: 8px;
      transition: all 0.2s;
      cursor: pointer;
      .left {
        flex: 1;
        min-width: 0;

        .owner-icon {
          flex: 0 0 20px;
          height: 20px;
          border-radius: 50%;
          overflow: hidden;
          margin-right: 4px;

          > img {
            width: 100%;
            height: 100%;
          }
        }
      }
      .right {
        flex: 0 0 16px;
        margin-left: 10px;
      }
      &:hover {
        @include background_color_mix(--color-text-placeholder, #ffffff, 80%);
      }

      &:nth-last-child(1) {
        margin-bottom: 0;
      }
    }
  }

  .switch-footer {
    .add-wrapper {
      width: 100px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      cursor: pointer;
      &:hover {
        @include background_color_mix(--color-text-placeholder, #ffffff, 80%);
      }
    }
  }
}
</style>
