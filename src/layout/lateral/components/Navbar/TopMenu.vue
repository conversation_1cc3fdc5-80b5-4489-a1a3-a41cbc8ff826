<template>
  <div class="top-menu h100" @mouseleave="changeCurrent(-1)">
    <!-- 一级菜单 -->
    <div
      class="menu h100"
      v-for="(route, index) in platformRoutes"
      :key="route.path"
      @click="toPath(route)"
      @mouseenter="changeCurrent(index)"
    >
      <div class="route-link h100">
        <svg-icon :icon-class="route.meta.icon" />
        <span>{{ route.meta.title }}</span>
      </div>
      <div class="active-bg" v-show="$route.path.startsWith(route.path)"></div>
    </div>
    <!-- hover状态底部滑块 -->
    <div
      v-show="current > -1"
      class="hover-block"
      :style="blockTransformX"
      @click="toPath(platformRoutes[current])"
    />
    <!-- 二级菜单 -->
    <transition name="fade-transform" mode="out-in">
      <div
        class="sub-menu"
        v-if="(showChildrenRoutes && !hideSubMenu) || hideSubMenuTemp"
      >
        <div class="lateral-wrapper">
          <div
            class="submenu"
            :class="{ active: $route.path === child.path }"
            v-for="child in childrenRoutes"
            :key="child.path"
          >
            <div class="menu-list">
              <h2 class="font-size-14 line-height-28 color-text-primary">
                {{ child.meta.title }}
              </h2>
              <div
                class="sub"
                :class="{ active: $route.path === sub.path }"
                v-for="sub in filterRoutes(child.children)"
                :key="sub.path"
                @click="toPath(sub)"
              >
                <div class="icon">
                  <svg-icon
                    :icon-class="sub.meta.icon"
                    :class="sub.meta.type"
                  />
                </div>
                <div class="info">
                  <h3 class="font-size-14 line-height-28 color-text-primary">
                    {{ sub.meta.title }}
                  </h3>
                  <p
                    class="font-size-12 line-height-20 color-text-secondary mt-8"
                  >
                    {{ sub.meta.desc }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { deepClone } from '@/utils/tools'

export default {
  name: 'TopMenu',
  data() {
    return {
      current: -1,
      active: 0,
      activeRoute: null,
      platformRoutes: null,
      hideSubMenu: false,
      hideSubMenuTemp: false
    }
  },
  computed: {
    ...mapGetters(['addRouters', 'platform']),
    blockTransformX() {
      const x = 128 * this.current + 'px'
      return `transform: translateX(${x})`
    },
    childrenRoutes() {
      const index = this.current > -1 ? this.current : this.active
      let currentRoute = this.platformRoutes[index]

      return this.filterRoutes(currentRoute.children)
    },
    showChildrenRoutes() {
      return (
        this.childrenRoutes.length > 1 ||
        this.childrenRoutes.every(route => !!route.children)
      )
    }
  },
  methods: {
    filterRoutes(routes) {
      if (routes) {
        return routes.filter(r => !r.meta.hideSubMenu)
      } else {
        return []
      }
    },
    changeCurrent(index) {
      this.current = index
      if (this.hideSubMenu) {
        this.hideSubMenuTemp = this.showChildrenRoutes
      }
      if (index === -1) {
        this.hideSubMenuTemp = false
      }
    },
    formatePlatformRoutes() {
      const addRouters = deepClone(this.addRouters)
      const routes = addRouters.filter(route => !route.hidden)

      function formate(route) {
        let path = route.path
        let children = route.children

        if (children && children.length) {
          route.redirect = `${path}/${children[0].path}`
          children.forEach(child => {
            child.path = `${path}/${child.path}`

            if (child.children && child.children.length) {
              formate(child)
            }
          })
        }

        return route
      }

      this.platformRoutes = routes.map(route => formate(route))
    },
    toPath(route) {
      this.$router.push(route.path)
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.hideSubMenu = !!route.meta.hideSubMenu
      },
      immediate: true
    }
  },
  created() {
    this.formatePlatformRoutes()
  }
}
</script>

<style lang="scss" scoped>
.top-menu {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  .menu {
    position: relative;
    padding: 8px 24px;
    z-index: 1;
    cursor: pointer;
    .route-link {
      display: flex;
      align-items: center;
      position: relative;
      z-index: 1;
      .svg-icon {
        width: 20px;
        height: 20px;
      }
      span {
        font-size: 14px;
        display: inline-block;
        margin-left: 4px;
        width: 4em;
      }
    }
    .active-bg {
      position: absolute;
      left: -26px;
      bottom: -2px;
      width: 180px;
      height: 56px;
      background-image: url('./images/active-menu-bg.png');
      background-repeat: no-repeat;
      background-size: cover;
      z-index: 0;
    }
  }
  .hover-block {
    transition: all 0.3s;
    position: absolute;
    width: 128px;
    height: 12px;
    left: 0;
    bottom: 8px;
    background-image: url('./images/undeline-block.png');
    background-repeat: no-repeat;
    background-position: 15px 0;
    cursor: pointer;
    z-index: 2;
  }
  .sub-menu {
    position: fixed;
    width: 100vw;
    min-height: 100px;
    top: 55px;
    left: 0;
    @include background_color(--color-white);
    // todo
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    .lateral-wrapper {
      padding-top: 32px;
      .submenu {
        width: 100%;
        height: 100%;
        padding-bottom: 32px;
        .menu-list {
          width: 100%;
          height: 100%;
          h2 {
            margin-bottom: 10px;
          }
          .sub {
            width: 25%;
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            padding: 20px 0 20px 20px;
            cursor: pointer;
            .icon {
              .primary {
                @include font_color(--color-primary);
              }
              .warning {
                @include font_color(--color-warning);
              }
              .success {
                @include font_color(--color-success);
              }
              .danger {
                @include font_color(--color-danger);
              }
              .svg-icon {
                width: 24px;
                height: 24px;
              }
            }
            .info {
              padding-left: 8px;
              h3 {
                font-size: 14px;
              }
            }
            &:hover,
            &.active {
              @include background_color_mix(--color-primary, #ffffff, 94%);
              .icon {
                .svg-icon {
                  @include font_color(--color-primary);
                }
              }
              .info {
                @include font_color(--color-primary);
                h3,
                p {
                  @include font_color(--color-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
