<template>
  <div class="sidebar-logo-container">
    <router-link
      key="expand"
      class="sidebar-logo-link h100"
      tag="div"
      :to="defaultFirstRouter"
    >
      <img
        width="232px"
        height="56px"
        src="../../../../assets/images/layout/layout-platform-logo-full.svg"
        class="sidebar-logo"
        alt=""
      />
    </router-link>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Logo',
  props: {
    collapse: {
      type: Boolean
    }
  },
  computed: {
    ...mapGetters(['addRouters']),
    defaultFirstRouter() {
      return this.addRouters[0].path
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-logo-container {
  position: relative;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  & .sidebar-logo-link {
    & .sidebar-logo {
      vertical-align: middle;
      display: inline-block;
    }
  }
}
</style>
