<template>
  <div class="navbar-message">
    <el-dropdown trigger="click">
      <el-button type="primary" size="mini">
        <el-badge :value="0" class="item">
          <svg-icon icon-class="notification" />
        </el-badge>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <!--        <el-dropdown-item>黄金糕</el-dropdown-item>-->
        <!--        <el-dropdown-item>狮子头</el-dropdown-item>-->
        <!--        <el-dropdown-item>螺蛳粉</el-dropdown-item>-->
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: 'Message'
}
</script>

<style lang="scss" scoped></style>
