<template>
  <div class="navbar">
    <logo />

    <div v-if="isOwner" class="top-menu h100">
      <top-menu />
    </div>

    <div class="right-menu">
      <!--      <template v-if="isOwner">-->
      <!--        <div class="right-meun-item">-->
      <!--          <message />-->
      <!--        </div>-->
      <!--        <div class="right-meun-item">-->
      <!--          <helper />-->
      <!--        </div>-->
      <!--        <div class="right-meun-item">-->
      <!--          <owner-switch />-->
      <!--        </div>-->
      <!--      </template>-->
      <div class="right-meun-item">
        <user-info ref="userInfo" />
      </div>
    </div>
  </div>
</template>

<script>
import Logo from './Logo'

import TopMenu from './TopMenu'

// import Message from './Message'
// import Helper from './Helper'
import UserInfo from './UserInfo'
// import OwnerSwitch from './OwnerSwitch'

export default {
  name: 'Navbar',
  components: {
    Logo,
    TopMenu,
    // Message,
    // Helper,
    UserInfo
    // OwnerSwitch
  },
  computed: {
    // 业主选择页面不展示菜单
    isOwner() {
      return this.$route.path !== '/owner/index'
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
  position: relative;
  user-select: none;
  .right-menu {
    height: 100%;
    display: flex;
    align-items: right;
    text-align: right;
    padding: 8px 14px;

    .right-meun-item {
      height: 100%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      margin-right: 10px;
      &:last-of-type {
        margin-right: 0;
      }
      :deep(.svg-icon) {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
