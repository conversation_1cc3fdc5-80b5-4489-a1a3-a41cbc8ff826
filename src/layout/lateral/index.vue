<template>
  <div class="lateral-layout">
    <div class="fixed-header">
      <navbar ref="navbar" />
    </div>
    <div class="main-container">
      <app-main />
    </div>
  </div>
</template>

<script>
import { Navbar, AppMain } from './components'

export default {
  name: 'LateralLayout',
  components: {
    Navbar,
    AppMain
  },
  provide() {
    return {
      rootMain: this
    }
  }
}
</script>

<style lang="scss" scoped></style>
