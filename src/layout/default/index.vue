<template>
  <div :class="classObj" class="app-wrapper">
    <sidebar class="sidebar-container" id="sidebarContainer" />
    <div class="main-container pos-relative">
      <div class="fixed-header">
        <navbar
          ref="navbar"
          @moveEnterHandler="moveEnterHandler"
          @mouseenterHandler="mouseenterHandler"
          @mouseleaveHandler="mouseleaveHandler"
        />
      </div>
      <tags-view />
      <app-main />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { Sidebar, Navbar, AppMain } from './components'
import TagsView from './components/TagsView/tagsView'
import PlatformRouterMap from '@/router/modules/index'

export default {
  name: 'DefaultLayout',
  components: {
    Sidebar,
    Navbar,
    AppMain,
    TagsView
  },
  data() {
    return {
      fundScreenShow: false
    }
  },
  provide() {
    return {
      rootMain: this
    }
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'curModule',
      'platform',
      'token',
      'singleModule',
      'moduleLists'
    ]),
    classObj() {
      return {
        // hideSidebar: true,
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        noMenu: this.$route.meta.noSidebar
      }
    }
  },

  watch: {
    $route: {
      handler(to) {
        // 判断是否为单module模式
        if (this.singleModule || this.moduleLists.length < 2) {
          return
        }
        // 获取路径path和query参数
        const { path, query } = to
        // 获取当前平台路由
        const routeMap = PlatformRouterMap[this.platform]
        // 编辑路由表
        for (let k in routeMap) {
          // 判断路由存在于哪个module中
          if (routeMap[k].routerArray.includes(path)) {
            // 如果当前module于去往的module有差异，切换module
            if (this.curModule && k !== this.curModule) {
              this.$store.dispatch('SetCurModule', {
                module: k,
                path,
                query
              })
            }
            break
          }
        }
      },
      immediate: true
    }
  },
  mounted() {
    // this.OpenGuide()
  },
  methods: {
    ...mapActions(['OpenGuide']),
    // 鼠标移入 - 平移
    moveEnterHandler(module, val) {
      this.$refs.heavyCurtain &&
        this.$refs.heavyCurtain.moveEnterHandler(module, val)
    },
    // 鼠标移入 - 展开帘幕
    mouseenterHandler() {
      let heavyCurtainContainer = document.getElementById(
        'heavyCurtainContainer'
      )
      heavyCurtainContainer.style.height = '100%'
      heavyCurtainContainer.style.border = '1px solid rgba(0,0,0,0.1)'
    },
    // 鼠标移出 - 收起帘幕
    mouseleaveHandler() {
      this.$refs.navbar.showFlag = true
      let heavyCurtainContainer = document.getElementById(
        'heavyCurtainContainer'
      )
      heavyCurtainContainer.style.height = '0'
      heavyCurtainContainer.style.border = 'none'
    },
    showExamineScreen() {
      this.fundScreenShow = true
    },
    hideExamineScreen() {
      this.fundScreenShow = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 0;
}
.fixed-header {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9;
  width: 100%;
  transition: width 0.28s;
}
.heavy-curtain-container {
  position: absolute;
  top: 55px;
  left: 0;
  width: 100%;
  height: 0;
  background: rgba(247, 249, 251, 0.8);
  backdrop-filter: blur(10px);
  opacity: 1;
  transition: all 0.3s;
  overflow: hidden;
  z-index: 999;
}
</style>
