<template>
  <div v-if="!item.hidden && hasShowItem(item)" class="menu-wrapper">
    <template
      v-if="
        hasOneShowingChild(item.children, item) &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link :to="reslovePath(onlyOneChild.path)">
        <el-menu-item :index="reslovePath(onlyOneChild.path)">
          <item :meta="Object.assign({}, item.meta, onlyOneChild.meta)" />
        </el-menu-item>
      </app-link>
    </template>

    <el-submenu
      v-else
      ref="subMenu"
      :index="reslovePath(item.path)"
      popper-append-to-body
    >
      <template slot="title">
        <item :meta="item.meta" />
      </template>
      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="reslovePath(child.path)"
        class="nest-menu"
      />
    </el-submenu>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item'
import AppLink from './Link'
import { deepClone } from '@/utils/tools'

export default {
  name: 'SidebarItem',
  components: {
    AppLink,
    Item
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  data() {
    this.onlyOneChild = null
    return {}
  },
  methods: {
    hasShowItem(routes) {
      if (!routes.children) return true
      if (routes.children.length === 0) return false
      const children = deepClone(routes.children)
      const showChildren = children.filter(item => !item.hidden)
      return showChildren && showChildren.length > 0
    },
    hasChildren(routes) {
      return routes.children && routes.children.length > 0
    },
    // 判断是否存在二级路由
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          this.onlyOneChild = item
          return true
        }
      })
      if (showingChildren.length === 1) {
        return true
      }
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: false }
        return true
      }
      return false
    },
    reslovePath(routePath) {
      if (isExternal(routePath)) {
        return routePath
      }
      return path.resolve(this.basePath, routePath)
    }
  }
}
</script>
