<template>
  <div class="has-logo" @mouseenter="sidebarEnter" @mouseleave="sidebarLeave">
    <Logo :collapse="isCollapse"></Logo>
    <div class="scrollbar-container">
      <el-scrollbar wrap-class="scrollbar-wrapper">
        <el-menu
          :default-active="$route.path"
          :unique-opened="true"
          :collapse="isCollapse"
          background-color="transparent"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          :collapse-transition="false"
          mode="vertical"
        >
          <div class="dashboard-menu-container">
            <div class="dashboard-sidebar">
              <sidebar-item
                :item="workspaceRoutes"
                :base-path="workspaceRoutes.path"
              />
            </div>
            <div class="line" v-if="isCollapse && hasRoutes">
              <i></i>
            </div>
          </div>
          <sidebar-item
            v-for="route in filterPermissionRoutes"
            :key="route.path"
            :item="route"
            :base-path="route.path"
          />
        </el-menu>
      </el-scrollbar>
    </div>

    <!-- 折叠区域 -->
    <!-- <hamburger :is-active="sidebar.opened" @toggleClick="toggleSideBar" /> -->
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import Logo from '../Navbar/Logo'
import { debounce, deepClone } from '@/utils/tools'
import { isAllHiddenRoutes } from '@/store/modules/module'
import workspaceRoutes from '@/router/modules/workspace'

let resizeTimer = null

export default {
  name: 'Sidebar',
  components: {
    SidebarItem,
    Logo
    // Hamburger
  },
  data() {
    return {
      screenWidth: '',
      workspaceRoutes
    }
  },
  computed: {
    ...mapGetters(['permission_routers', 'sidebar']),
    filterPermissionRoutes() {
      return isAllHiddenRoutes(deepClone(this.permission_routers))
    },
    hasRoutes() {
      return (
        this.filterPermissionRoutes.filter(route => !route.hidden).length > 0
      )
    },
    isCollapse() {
      return !this.sidebar.opened
    },
    routes() {
      return this.$router.options.routes
    }
  },
  watch: {
    screenWidth(val) {
      if (resizeTimer) {
        clearTimeout(resizeTimer)
      }
      resizeTimer = setTimeout(() => {
        if (val < 1400) {
          this.$store.dispatch('CloseSidebar', false)
        } else {
          this.$store.dispatch('OpenSidebar')
        }
      }, 300)
    }
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('ToggleSidebar')
    },
    sidebarEnter() {
      debounce(this.$store.dispatch('OpenSidebar'), 300)
    },
    sidebarLeave() {
      debounce(this.$store.dispatch('CloseSidebar', false), 300)
    }
  },
  mounted() {
    // this.screenWidth = document.body.clientWidth
    // window.onresize = () => {
    //   return (() => {
    //     this.screenWidth = document.body.clientWidth
    //   })()
    // }
  }
}
</script>

<style lang="scss" scoped>
.scrollbar-container {
  padding: 8px;
  height: calc(100% - 56px);
}
.dashboard-menu-container {
  .dashboard-sidebar {
    padding: 20px 0;
  }
  .line {
    padding: 0 12px;
    i {
      display: inline-block;
      width: 100%;
      height: 1px;
      @include background_color_mix(--color-text-regular, #ffffff, 5%);
      margin-bottom: 24px;
    }
  }
}
</style>
