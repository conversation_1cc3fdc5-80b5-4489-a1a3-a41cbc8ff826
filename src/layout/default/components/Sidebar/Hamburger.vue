<template>
  <div class="collapse-area">
    <div class="icon-area" @click="toggleClick">
      <svg-icon
        class="hamburger"
        :class="{ 'is-active': isActive }"
        icon-class="menu-fold"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: '<PERSON><PERSON>',
  props: {
    isActive: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    toggleClick() {
      this.$emit('toggleClick')
    }
  }
}
</script>

<style scoped>
.hamburger.is-active {
  transform: rotate(180deg);
}
</style>
