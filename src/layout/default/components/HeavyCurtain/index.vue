<template>
  <div class="heavy-curtain-wrapper flex p-b-32 p-l-24" id="heavyCurtain">
    <div
      class="heavy-curtain-main m-r-8"
      :class="key === bgColor ? 'active' : ''"
      :ref="key"
      v-for="(val, key) in platformRoutesMap"
      :key="key"
    >
      <el-scrollbar style="height: 100%">
        <div class="heavy-curtain-item">
          <div
            v-for="(item, index) in val.accessedRouters"
            :key="index"
            @click="jumpCardHandler(item)"
          >
            <card
              v-if="!item.hidden"
              ref="card"
              :item="item"
              @jumpHandler="jumpHandler"
            />
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import Card from '@/layout/default/components/HeavyCurtain/card'
import { mapGetters } from 'vuex'

export default {
  name: 'HeavyCurtain',
  components: { Card },
  data() {
    return {
      bgColor: null
    }
  },
  computed: {
    ...mapGetters(['platformRoutesMap'])
  },
  methods: {
    jumpCardHandler(val) {
      this.$router.push({
        path: `${val.redirect}`
      })
      this.$emit('jumpHandler')
    },
    jumpHandler() {
      this.$emit('jumpHandler')
    },
    // 鼠标移入 - 平移到指定位置
    moveEnterHandler(module, val) {
      const keys = Object.keys(this.platformRoutesMap)
      const idx = keys.indexOf(val.module)
      const refDom = this.$refs[val.module][0]
      let heavyCurtain = document.getElementById('heavyCurtain')
      this.bgColor = val.module
      const moduleSetLeft = idx === 0 ? 24 : module.offsetLeft
      let distance = refDom.offsetLeft - moduleSetLeft
      heavyCurtain.style.transform = `translate(-${distance}px, 0)`
    }
  }
}
</script>

<style lang="scss" scoped>
.heavy-curtain-wrapper {
  width: 100%;
  height: 100%;
  transition: all 0.3s;
  .heavy-curtain-main {
    border: 1px solid transparent;
    .heavy-curtain-item {
      padding: 24px 16px;
    }
    &:hover {
      height: 100%;
      background: linear-gradient(
        180deg,
        rgba(212, 229, 246, 0.1) 0%,
        rgba(212, 229, 246, 0.3) 46%,
        rgba(212, 229, 246, 0.1) 100%
      );
      opacity: 1;
      border: 1px solid;
      border-image: linear-gradient(
          180deg,
          rgba(255, 255, 255, 0),
          rgba(
            26.000000350177288,
            90.00000223517418,
            234.00000125169754,
            0.10000000149011612
          ),
          rgba(255, 255, 255, 0)
        )
        1 1;
    }
  }
  .active {
    height: 100%;
    background: linear-gradient(
      180deg,
      rgba(212, 229, 246, 0.1) 0%,
      rgba(212, 229, 246, 0.3) 46%,
      rgba(212, 229, 246, 0.1) 100%
    );
    opacity: 1;
    border: 1px solid;
    border-image: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0),
        rgba(
          26.000000350177288,
          90.00000223517418,
          234.00000125169754,
          0.10000000149011612
        ),
        rgba(255, 255, 255, 0)
      )
      1 1;
  }
}

::v-deep {
  .el-scrollbar {
    .el-scrollbar__wrap {
      overflow-x: hidden;
      margin-bottom: 0 !important;
    }

    .is-horizontal {
      display: none;
    }
  }
}
</style>
