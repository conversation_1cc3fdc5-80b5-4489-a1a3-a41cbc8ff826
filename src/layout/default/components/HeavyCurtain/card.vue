<template>
  <div class="navigation-card pointer m-b-8" :class="moreFlag ? 'active' : ''">
    <div class="color-s font-size-14 line-height-20 m-b-8">
      {{ item.meta.title }}
      <svg-icon v-if="false" icon-class="hot" />
    </div>
    <div class="color-text font-size-12 line-height-17 m-b-8">
      {{ item.meta.desc }}
    </div>
    <div class="more-content">
      <div class="flex justify-content-between">
        <div class="flex align-items-center">
          <div
            v-for="(val, index) in secondLevelRouters"
            :key="index"
            @click.stop="jumpHandler(val)"
          >
            <div
              v-if="index < 3"
              class="color-n font-size-14 line-height-20 pointer m-r-8"
            >
              {{ val.meta.title }}
            </div>
          </div>
        </div>
        <el-dropdown
          @visible-change="moreHandler"
          v-if="secondLevelRouters && secondLevelRouters.length > 3"
        >
          <el-button type="text">
            更多
            <i v-if="moreFlag" class="el-icon-arrow-up el-icon--right"></i>
            <i v-else class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown" :append-to-body="false">
            <el-dropdown-item
              v-show="index > 2"
              v-for="(val, index) in secondLevelRouters"
              :key="index"
            >
              <div class="more-menu" @click.stop="jumpHandler(val)">
                {{ val.meta.title }}
              </div>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'card',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      moreFlag: false
    }
  },
  computed: {
    secondLevelRouters() {
      return this.item.children.filter(list => !list.hidden)
    }
  },
  methods: {
    moreHandler(val) {
      this.moreFlag = val
    },
    jumpHandler(val) {
      this.$router.push({
        path: `${this.item.path}/${val.path}`
      })
      this.$emit('jumpHandler')
    }
  }
}
</script>

<style lang="scss" scoped>
.navigation-card {
  width: 324px;
  padding: 16px;
  border: 1px solid transparent;
  &:hover {
    .color-s {
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .color-n {
    color: rgba(0, 0, 0, 0.9);
    &:hover {
      @include font_color(--color-primary);
    }
  }
  .color-s {
    color: rgba(0, 0, 0, 0.9);
  }
  .color-text {
    color: rgba(0, 0, 0, 0.26);
    padding-bottom: 8px;
    border-bottom: 1px solid;
    border-image: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0.20000000298023224),
        rgba(0, 0, 0, 0)
      )
      1 1;
  }
  .more-content {
    display: none;
  }

  &.active,
  &:hover {
    background: #ffffff
      linear-gradient(180deg, rgba(5, 76, 232, 0.07) 0%, rgba(0, 0, 0, 0) 100%) !important;
    box-shadow: 0px 8px 12px 0px rgba(0, 78, 248, 0.05);
    border-radius: 3px 3px 3px 3px;
    opacity: 1;
    border: 1px solid rgba(26, 90, 234, 0.1);
    .color-text {
      border-image: none;
      border-bottom: 1px solid rgba(26, 90, 234, 0.2);
    }
    .more-content {
      display: block;
    }
  }
  .more-menu {
    max-width: 100px;
    white-space: nowrap;
  }
}
</style>
