<template>
  <section ref="appMain" id="appMain" class="app-main">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view :key="key" />
      </keep-alive>
    </transition>
    <back-to-top :scroll-distance="scrollDistance" :elem="$refs.appMain" />
  </section>
</template>

<script>
import BackToTop from '@/components/BackToTop'

export default {
  name: 'AppMain',
  provide() {
    return {
      appMain: this
    }
  },
  components: {
    BackToTop
  },
  data() {
    return {
      scrollDistance: 0,
      keepAlive: ''
    }
  },
  computed: {
    cachedViews() {
      // return process.env.NODE_ENV !== 'development'
      //   ? this.$store.state.tagsView.cachedViews
      //   : []
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      this.$refs.appMain && this.$refs.appMain.scrollTo(0, 0)
      return this.$route.fullPath
    }
  },
  mounted() {
    window.$appMain = this.$refs.appMain
    this.$refs.appMain.addEventListener('scroll', e => {
      const scrollDistance = e.target.scrollTop
      this.scrollDistance = scrollDistance
    })
  },
  // 后续tagview放开，删掉下面代码
  watch: {
    $route: {
      handler(route) {
        const { name } = route
        if (name) {
          this.$store.dispatch('tagsView/addView', route)
        }
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  width: 100%;
  height: 100%;
  height: calc(100% - 56px - 32px);
  position: absolute;
  //top: 56px;
  top: 88px;
  left: 0;
  z-index: 10;
  overflow: hidden;
  overflow-y: auto;
}
</style>
