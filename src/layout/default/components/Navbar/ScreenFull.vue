<template>
  <div class="screen-full">
    <el-tooltip
      class="item"
      effect="dark"
      :content="isFullscreen ? '退出全屏' : '全屏'"
      placement="bottom"
    >
      <svg-icon
        :icon-class="isFullscreen ? 'fullscreen-exit' : 'fullscreen'"
        @click="toggleSceenfull"
      />
    </el-tooltip>
  </div>
</template>

<script>
import screenfull from 'screenfull'

export default {
  name: 'ScreenFull',
  data() {
    return {
      isFullscreen: false
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    this.destory()
  },
  methods: {
    toggleSceenfull() {
      if (!screenfull.isEnabled) {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
        return false
      }
      screenfull.toggle()
    },
    change() {
      this.isFullscreen = screenfull.isFullscreen
    },
    init() {
      if (screenfull.isEnabled) {
        screenfull.on('change', this.change)
      }
    },
    destory() {
      if (!screenfull.isEnabled) {
        screenfull.off('change', this.change)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.screen-full {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
