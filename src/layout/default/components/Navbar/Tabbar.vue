<!--
 * @Descripttion:
 * @Author: 田柱
 * @Date: 2021-04-20 08:52:03
 * @LastEditTime: 2021-09-14 14:40:16
-->
<template>
  <div v-if="fileterModuleLists.length" class="tab-bar-container">
    <div
      v-for="module in fileterModuleLists"
      :key="module.id"
      :ref="`ref${module.module}`"
      class="tab-bar-item font-14"
      :class="{ current: curModule === module.module }"
      @click="skipToModule(module.module)"
      @mouseenter="moveEnterHandler(module)"
    >
      <svg-icon v-if="module.iconName" :icon-class="module.iconName" />
      <span>{{ module.name }}</span>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'TabBar',
  components: {},
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['curModule', 'moduleLists']),
    fileterModuleLists() {
      return this.moduleLists.filter(list => !list.hidden)
    }
  },
  methods: {
    // 跳转到指定平台
    skipToModule(module) {
      if (module === this.curModule) return
      this.$store.dispatch('SetCurModule', { module })
      this.$emit('mouseleaveHandler')
    },
    // 鼠标移入 - 平移
    moveEnterHandler(val) {
      let module = this.$refs[`ref${val.module}`][0]
      this.$emit('moveEnterHandler', module, val)
    }
  }
}
</script>

<style lang="scss" scoped>
.tab-bar-container {
  display: flex;
  align-items: center;
  height: 100%;
  .tab-bar-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-right: 4px;
    cursor: pointer;
    padding: 8px 24px;
    border-radius: 3px;
    &:last-of-type {
      margin-right: 0;
    }
    span {
      font-size: 14px;
      line-height: 22px;
    }
    .svg-icon {
      font-size: 18px;
      margin-bottom: 6px;
    }
  }
}
</style>
