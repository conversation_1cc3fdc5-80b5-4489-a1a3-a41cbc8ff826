<template>
  <div class="navbar-search">
    <div @click.stop="openSearch">
      <svg-icon icon-class="search" />
    </div>
    <div
      :class="{ collapse: collapse }"
      class="search-input flex align-items-center justify-content-center"
    >
      <svg-icon slot="prepend" icon-class="search" />
      <el-autocomplete
        ref="input"
        v-model="searchValue"
        placeholder="请输入搜索内容"
        :fetch-suggestions="querySearch"
        @keyup.enter.native="search"
        clearable
      >
      </el-autocomplete>
    </div>
  </div>
</template>

<script>
import { local } from '@/utils/storage'
import { GLOBAL_SEARCH_SAVE_LENGTH } from '@/settings'

export default {
  name: 'Search',
  data() {
    return {
      historySearchValues: [],
      searchValue: '',
      collapse: false
    }
  },
  watch: {
    collapse(val) {
      if (!val) {
        if (this.timer) clearTimeout(this.timer)
        this.resetSearch()
      } else {
        this.getSearcgValue()
      }
    }
  },
  created() {
    document.onclick = e => {
      e.stopPropagation()
      const name = e.target.className
      if (
        ![
          'el-input__inner',
          'el-icon-search',
          'el-input-group__append',
          'font-16 el-link el-link--default is-underline',
          'el-input__icon el-icon-circle-close el-input__clear'
        ].includes(name)
      ) {
        this.closeSearch()
      }
    }
  },
  methods: {
    toggleSearch() {
      this.collapse = !this.collapse
    },
    openSearch() {
      this.collapse = true
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.$refs.input.focus()
      }, 500)
    },
    closeSearch() {
      this.collapse = false
    },
    querySearch(queryString, cb) {
      const resluts = !queryString ? this.historySearchValues : []
      cb(resluts)
    },
    resetSearch() {
      this.searchValue = ''
    },
    saveSearchValue() {
      // 判断是否已经存在
      const searchValue = this.searchValue
      const values = this.getSearcgValue()
      if (!values.some(v => v.value === searchValue)) {
        // 头部新增
        values.unshift({ value: searchValue })
        // 限制长度
        values.length > 5 && (values.length = GLOBAL_SEARCH_SAVE_LENGTH)
        // 存储
        local.SET_GLOBAL_SEARCH(values)
      } else {
        const idx = values.findIndex(v => v.value === searchValue)
        const temp = values[idx]
        values.splice(idx, 1)
        values.unshift(temp)
        local.SET_GLOBAL_SEARCH(values)
      }
    },
    getSearcgValue() {
      const values = local.GET_GLOBAL_SEARCH() || []
      this.historySearchValues = values
      return values
    },
    search() {
      const searchValue = this.searchValue.trim()
      if (!searchValue) {
        return this.$toast.warning('请先输入搜索内容')
      } else {
        this.saveSearchValue()
        this.resetSearch()
        this.closeSearch()
        // 存储历史数据
        this.$router.push({
          path: '/search/index',
          query: {
            searchValue
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar-search {
  position: relative;
  .icon,
  .search-input {
    transition: all 0.3s;
  }
  .icon {
    height: 100%;
  }

  .search-input {
    position: absolute;
    right: -8px;
    border-radius: 20px;
    top: -8px;
    width: 0;
    height: 40px;
    overflow: hidden;
    border: none;
    :deep(.el-autocomplete) {
      width: 100%;
      .el-input__inner {
        padding: 0;
        border: none;
        border-radius: 0;
        font-size: 14px;
      }
      .el-input__suffix {
        .el-input__clear {
          font-size: 16px;
        }
      }
    }
    .svg-icon {
      background: transparent;
      border: none;
      border-radius: 0;
      padding: 0 10px;
    }
    &.collapse {
      width: 248px;
    }
  }
  .hidden {
    display: none;
  }
}
</style>
