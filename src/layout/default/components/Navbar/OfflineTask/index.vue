<template>
  <div>
    <div
      class="flex flex-direction-column align-items-center pointer"
      @click="dialogVisible = true"
    >
      <svg-icon icon-class="icon-color-download" class-name="font-size-22" />
      <span class="m-t-8 color-text-regular font-size-12 line-height-20"
        >下载任务</span
      >
    </div>
    <dialog-cmp
      custom-class="offline-task-dialog"
      title="下载中心"
      :visible.sync="dialogVisible"
      width="900px"
      :have-operation="false"
    >
      <el-table
        :data="tableData"
        :header-cell-style="tableStyle"
        :cell-style="tableStyle"
        height="522px"
      >
        <el-table-column
          prop="businessTypeStr"
          label="任务类型"
        ></el-table-column>
        <el-table-column prop="operatorName" label="操作人"></el-table-column>
        <el-table-column
          prop="operatorTime"
          label="操作时间"
          min-width="120px"
        ></el-table-column>
        <el-table-column prop="statusStr" label="状态" min-width="140px">
          <template slot-scope="scope">
            <div class="flex align-items-center">
              <el-progress
                style="width: 140px"
                :percentage="getPercentage(scope.row.status)"
                :show-text="false"
              ></el-progress>
              <span :class="colorHandle(scope.row.status)" class="m-l-24">{{
                scope.row.statusStr
              }}</span>
              <svg-icon
                v-if="scope.row.status === 2"
                icon-class="cloud-download"
                class-name="pointer m-l-24 download-icon"
                @click="exportHandle(scope.row)"
              />
            </div>
          </template>
        </el-table-column>
        <empty-data slot="empty" description="暂无下载任务" />
      </el-table>
      <el-pagination
        :current-page="formModel.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="formModel.pageSize"
        :layout="'total, sizes, prev, pager, next, jumper'"
        :total="total"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </dialog-cmp>
  </div>
</template>

<script>
import { attachmentDetail, fileOfflinePage } from '@/api/common'
import download from '@/utils/download'
let timer = null

export default {
  name: 'OfflineTask',
  data() {
    return {
      tableStyle: {
        border: 'none'
      },
      dialogVisible: false,
      tableData: [],
      formModel: {
        pageNo: 1,
        pageSize: 10
      },
      total: 0
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.initData()
      } else {
        this.formModel = this.$options.data().formModel
        this.tableData = []
        this.total = 0
        timer && clearInterval(timer)
      }
    }
  },
  methods: {
    getPercentage(status) {
      return status === 2 ? 100 : 0
    },
    colorHandle(status) {
      // 状态( 0 - 未开始 1 - 进行中 2 - 已完成 3 - 失败 ))
      const colorClass = ['', 'color-primary', 'color-success', 'color-danger']
      return colorClass[status]
    },
    initData() {
      this.fileOfflinePage()
      timer && clearInterval(timer)
      timer = setInterval(() => {
        this.fileOfflinePage()
      }, 2000)
    },
    fileOfflinePage() {
      fileOfflinePage(this.formModel).then(res => {
        this.tableData = res?.list || []
        this.total = res?.total || 0
      })
    },
    handleSizeChange(val) {
      this.formModel.pageSize = val
      this.initData()
    },
    handleCurrentChange(val) {
      this.formModel.pageNo = val
      this.initData()
    },
    exportHandle(row) {
      if (!row.attaId) return false
      attachmentDetail({ id: row.attaId }).then(res => {
        download.addressDownload(res)
      })
    }
  }
}
</script>
<style scoped lang="scss">
.download-icon {
  &:hover {
    @include font_color(--color-primary);
  }
}
</style>
<style lang="scss">
.offline-task-dialog {
  .el-table::before {
    display: none;
  }
  .el-pagination {
    text-align: right;
    padding: 16px 16px 0;
    .el-pagination__total {
      float: left;
    }
    &::after {
      content: '';
      font-size: 0;
      clear: both;
    }
  }
  .el-dialog__footer {
    display: none;
  }
}
</style>
