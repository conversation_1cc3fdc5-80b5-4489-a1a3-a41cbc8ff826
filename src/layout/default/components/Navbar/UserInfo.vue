<template>
  <div class="user-info-wrapper">
    <el-dropdown
      class="avatar-container"
      trigger="click"
      @visible-change="visibleChange"
    >
      <div class="user-info">
        <img
          v-if="userInfo.logoType === 2"
          width="30px"
          height="30px"
          :src="userInfo.logoUrl"
          alt=""
        />
        <div v-else class="default-avatar">{{ userInfo.logoStr }}</div>
        <div class="info">
          <span class="name">{{ userInfo.nickname }}</span>
          <svg-icon
            icon-class="caret-down-small"
            class="drop-caret"
            :class="{ transform: visible }"
          />
        </div>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item class="dropdown-un-hover-item">
          <div class="icon-group user-wrapper-header">
            <div class="header-content">
              <img
                class="logo-img"
                v-if="userInfo.logoType === 2"
                width="48px"
                height="48px"
                :src="userInfo.logoUrl"
                alt=""
              />
              <span v-else class="default-avatar">
                {{ userInfo.logoStr }}
              </span>
              <div class="header-content-right">
                <div class="right-title">{{ name }}</div>
                <div class="right-phone">手机号{{ userInfo.mobile }}</div>
              </div>
            </div>
            <!--            <el-button-->
            <!--              class="header-btn-text"-->
            <!--              type="text"-->
            <!--              @click="userDialogVisible = true"-->
            <!--              >管理您的个人账号</el-button-->
            <!--            >-->
            <el-button
              class="header-btn-text"
              type="text"
              @click="editPasswordVisible = true"
              >修改密码</el-button
            >
          </div>
        </el-dropdown-item>
        <el-dropdown-item class="go-website">
          <div class="icon-group p-t-6 p-b-6 p-l-8 p-r-8" @click="goWebsite">
            <svg-icon icon-class="logo-chrome" />
            <span class="dropdown-span">进入官网</span>
          </div>
        </el-dropdown-item>
        <!--        <el-dropdown-item>-->
        <!--          <div class="icon-group" @click="visibleHandle(1)">-->
        <!--            <svg-icon icon-class="wechat-fill" />-->
        <!--            <span class="dropdown-span">关注公众号</span>-->
        <!--          </div>-->
        <!--        </el-dropdown-item>-->
        <el-dropdown-item class="dp-item">
          <div
            class="icon-group p-t-6 p-b-6 p-l-8 p-r-8"
            @click="visibleHandle(2)"
          >
            <svg-icon icon-class="mini-program" />
            <span class="dropdown-span">微信小程序</span>
          </div>
        </el-dropdown-item>
        <!--        <el-dropdown-item>-->
        <!--          <div class="icon-group" @click="toUpdateLog">-->
        <!--            <svg-icon icon-class="refresh" />-->
        <!--            <span class="dropdown-span">更新日志</span>-->
        <!--          </div>-->
        <!--        </el-dropdown-item>-->
        <el-dropdown-item class="dropdown-un-hover-item logout-item">
          <div class="icon-group p-t-6 p-b-6 p-l-8 p-r-8" @click="logout">
            <svg-icon icon-class="poweroff" />
            <span class="dropdown-span">退出登录</span>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 用户信息弹窗 -->
    <user-center :visible.sync="userDialogVisible" />
    <dialog-cmp
      :title="codeTitle"
      :visible.sync="codeVisible"
      width="420px"
      :haveOperation="false"
    >
      <div class="w100" style="text-align: center">
        <img
          style="margin: 36px auto 0"
          width="200px"
          height="200px"
          :src="codeImg"
          alt=""
        />
        <div class="text-center m-t-8 m-b-32">{{ codeTips }}</div>
      </div>
    </dialog-cmp>

    <!-- 修改密码弹窗 -->
    <modify-password
      :visible.sync="editPasswordVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
  </div>
</template>

<script>
import UserCenter from '@/components/UserCenter/index'
import { mapGetters, mapActions } from 'vuex'
import ModifyPassword from '@/components/UserCenter/ModifyPassword.vue'

export default {
  name: 'UserInfo',
  components: {
    ModifyPassword,
    UserCenter
  },
  data() {
    return {
      visible: false,
      defaultAvatar: require('@/assets/images/layout/default-user-avatar.png'),
      userDialogVisible: false,
      editPasswordVisible: false,
      codeTitle: '企业公众号',
      codeImg: '',
      codeTips: '请使用微信扫码关注公众号',
      codeVisible: false
    }
  },
  computed: {
    ...mapGetters(['name', 'avatar', 'userInfo']),
    userAvatar() {
      return this.avatar || this.defaultAvatar
    }
  },
  methods: {
    ...mapActions(['OpenGuide']),
    getUserInfo() {
      this.$store.dispatch('GetUserInfo')
    },
    visibleChange(visible) {
      this.visible = visible
    },
    logout() {
      this.$store.dispatch('Logout').then(() => {
        this.$router.push(`/login`)
        setTimeout(() => {
          location.reload()
        }, 300)
      })
    },
    openGuide() {
      this.OpenGuide(true)
    },
    toUpdateLog() {
      let routeUrl = this.$router.resolve({
        path: '/changelog'
      })
      window.open(routeUrl.href, '_blank')
    },
    // type 1 公众号 2 小程序
    visibleHandle(type) {
      this.codeTitle = type === 1 ? '企业公众号' : '微信小程序'
      this.codeImg = type === 1 ? this.userInfo.wxCode : this.userInfo.mpCode
      this.codeTips =
        type === 1
          ? '请使用微信扫码关注公众号'
          : '请使用微信小程序扫码查看小程序'
      this.codeVisible = true
    },
    goWebsite() {
      window.open(this.userInfo.website, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.user-info-wrapper {
  height: 100%;
  padding: 8px;
}
.avatar-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  .user-info {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .default-avatar {
      width: 30px;
      height: 30px;
      border-radius: 50%;
      @include background_color(--color-primary);
      font-size: 12px;
      font-weight: 350;
      color: #ffffff !important;
      text-align: center;
      line-height: 30px;
    }
    .info {
      display: flex;
      align-items: center;
      .name {
        margin: 0 8px;
        display: inline-block;
        line-height: 24px;
      }
      .drop-caret {
        transition: all 0.3s;
        transform: rotate(0deg);
        &.transform {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.dp-item {
  margin: 0 8px 8px;
}
.go-website {
  margin: 8px !important;
}
::v-deep {
  .dropdown-un-hover-item:hover {
    background: transparent !important;
  }
  .user-wrapper-header {
    width: 212px;
    padding: 8px 16px 16px 16px;
    position: relative;
    flex-direction: column;
    cursor: default;
    .header-content {
      display: flex;
      width: 100%;
      .logo-img {
        margin-right: 8px;
        flex-shrink: 0;
      }
      .default-avatar {
        display: inline-block;
        width: 48px;
        height: 48px;
        background: #ed7b2f;
        border-radius: 24px;
        border: 1px solid #fff8f3;
        font-size: 14px;
        font-weight: 350;
        color: #ffffff;
        line-height: 48px;
        text-align: center;
        flex-shrink: 0;
      }
      .header-content-right {
        width: calc(100% - 48px);
        padding-left: 8px;
        .right-title {
          width: 100%;
          font-size: 16px;
          font-weight: 350;
          color: rgba(0, 0, 0, 0.9);
          line-height: 24px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .right-phone {
          font-size: 12px;
          font-weight: 350;
          color: rgba(0, 0, 0, 0.4);
          line-height: 20px;
          margin-top: 4px;
        }
      }
    }
    .header-btn-text {
      padding: 0;
      margin-top: 8px;
      margin-left: -9px;
    }
    &:after {
      display: block;
      content: '';
      width: 100%;
      height: 1px;
      background: #dcdcdc;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .logout-item {
    padding: 12px 0 !important;
    position: relative;
    margin: 8px 8px 0 8px;
    &:before {
      display: block;
      content: '';
      width: calc(100% + 16px);
      height: 1px;
      background: #dcdcdc;
      position: absolute;
      top: 0;
      left: -8px;
    }
    .icon-group {
      padding: 6px 8px;
      &:hover {
        background: #f3f3f3;
      }
    }
  }
}

::v-deep {
  .el-dropdown-menu {
    padding: 8px !important;
  }
}
</style>
