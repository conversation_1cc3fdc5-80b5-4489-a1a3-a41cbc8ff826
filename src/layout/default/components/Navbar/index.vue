<template>
  <div class="navbar">
    <div class="left">
      <!-- <Logo /> -->
      <!--            <div-->
      <!--              class="color-text-placeholder m-l-16 font-size-22 pointer"-->
      <!--              @click="gridHandle"-->
      <!--              :class="{ 'color-active': !showFlag }"-->
      <!--            >-->
      <!--              <svg-icon icon-class="grid" />-->
      <!--            </div>-->
      <div class="tab-bar-wrapper h100">
        <tab-bar ref="tabBar" key="0" />
      </div>
    </div>

    <div class="right-menu">
      <!-- 不能删除，全局任务调用 -->
      <!--      <div v-show="false" class="right-meun-item">-->
      <!--        <offline-task ref="offlineTask" />-->
      <!--      </div>-->
      <!--      <div class="right-meun-item">-->
      <!--        <search />-->
      <!--      </div>-->
      <!--      <div class="right-meun-item">-->
      <!--        <message />-->
      <!--      </div>-->
      <!--      <div class="right-meun-item">-->
      <!--        <helper />-->
      <!--      </div>-->
      <div class="right-meun-item">
        <shortcut />
      </div>
      <!--      <div class="right-meun-item">-->
      <!--        <screen-full />-->
      <!--      </div>-->
      <div class="right-meun-item">
        <user-info ref="userInfo" />
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
// import Logo from './Logo'
import TabBar from './Tabbar'
// import Search from './Search'
// import Message from './Message'
// import Helper from './Helper'
import Shortcut from './Shortcut'
import UserInfo from './UserInfo'
// import OfflineTask from './OfflineTask'
// import ScreenFull from './ScreenFull'

export default {
  name: 'Navbar',
  components: {
    // OfflineTask,
    // Logo,
    TabBar,
    Shortcut,
    UserInfo
    // Helper,
    // Message,
    // Search
    // ScreenFull
  },
  data() {
    return {
      showFlag: true
    }
  },
  computed: {
    ...mapGetters(['sidebar', 'platform', 'module']),
    isCollapse() {
      return !this.sidebar.opened
    },
    noMenu() {
      return !this.$route.meta.noMenu
    }
  },
  methods: {
    // 九宫格菜单
    gridHandle() {
      if (this.showFlag) {
        this.mouseenterHandler()
      } else {
        this.mouseleaveHandler()
      }
    },
    // 鼠标移入 - 平移
    moveEnterHandler(module, val) {
      this.$emit('moveEnterHandler', module, val)
    },
    // 鼠标移入 - 展开帘幕
    mouseenterHandler() {
      const filterModuleLists = this.$refs.tabBar.fileterModuleLists || []
      const curModule = this.$refs.tabBar.curModule || ''
      const val = filterModuleLists.find(item => item.module === curModule)
      const module = this.$refs.tabBar.$refs[`ref${val.module}`][0]
      this.showFlag = false
      this.$emit('mouseenterHandler')
      this.moveEnterHandler(module, val)
    },
    // 鼠标移出 - 收起帘幕
    mouseleaveHandler() {
      this.showFlag = true
      this.$emit('mouseleaveHandler')
    },
    toggleSideBar() {
      this.$store.dispatch('ToggleSidebar')
    },
    // 跳转到指定平台
    skipToModule(module) {
      this.$store.dispatch('SetModule', { module })
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  width: 100%;
  height: 100%;
  background-size: cover;
  overflow: hidden;
  position: relative;
  user-select: none;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .left {
    height: 100%;
    display: flex;
    align-items: center;
    .color-active {
      @include font_color(--color-text-secondary);
    }
    .tab-bar-wrapper {
      padding: 8px 16px;
    }
  }
  .right-menu {
    height: 100%;
    display: flex;
    text-align: right;
    padding: 8px 24px;

    .right-meun-item {
      height: 100%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
      border-radius: 3px;
      margin-right: 16px;
      &:last-of-type {
        margin-right: 0;
      }
      :deep(.svg-icon) {
        width: 24px;
        height: 24px;
      }
    }
  }
}
</style>
