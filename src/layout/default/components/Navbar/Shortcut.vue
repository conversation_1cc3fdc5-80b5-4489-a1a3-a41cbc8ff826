<template>
  <div class="navbar-shortcut">
    <el-dropdown trigger="click">
      <span>
        <el-tooltip effect="dark" content="快捷菜单" placement="bottom">
          <svg-icon icon-class="view-list" />
        </el-tooltip>
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item>
          <div class="menu-container">
            <div class="menu-wrapper">
              <menu-list :routes="topRoutes" />
            </div>

            <div
              class="menu-wrapper"
              v-if="bottomRoutes && bottomRoutes.length > 0"
            >
              <div class="line"></div>
              <menu-list :routes="bottomRoutes" />
            </div>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { _genRouterMap } from '@/router/routerFun'

// 快捷菜单组件
const MenuList = {
  props: {
    routes: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    toPath({ path }) {
      if (path) {
        this.$router.push(path)
      }
    }
  },
  // eslint-disable-next-line no-unused-vars
  render(h) {
    return (
      <div>
        {this.routes.map(route => {
          return (
            <div
              class="menu-list"
              key={route.name}
              onClick={this.toPath.bind(this, route)}
            >
              <svg-icon
                icon-class={route.meta.iconShort || route.meta.icon}
                class={route.meta.type}
              />
              <span>{route.meta.title}</span>
            </div>
          )
        })}
      </div>
    )
  }
}

export default {
  name: 'Shortcut',
  components: {
    MenuList
  },
  data() {
    return {
      topNames: [
        'SettleInProjectBasic', // 项目库
        'SettleInProposedBasic', // 拟入园库
        'ContractSpecial', // 合同库
        'AssetsHouseMaintainBasic', // 房源库
        'EnterpriseList', // 企业库
        'StaffBasic', // 员工库
        'VehicleList', // 车辆库
        'PolicyCenter' // 政策库
      ],
      bottomNames: [
        'AccountsReceivable', // 应收账单
        'ReplaceSpecial', // 调房申请
        'LeaveSpecial', // 离园申请
        'FiledApply', // 场地申请
        'ProblemBasic', // 问题反馈
        'Information' // 信息填报
      ]
    }
  },
  computed: {
    ...mapGetters(['wholeAccessedRoutes']),
    routerNameMap() {
      return _genRouterMap(this.wholeAccessedRoutes, 'route')
    },
    topRoutes() {
      return this.mateNameRoute(this.topNames)
    },
    bottomRoutes() {
      return this.mateNameRoute(this.bottomNames)
    }
  },
  methods: {
    mateNameRoute(names) {
      const routes = []
      names.forEach(name => {
        const route = this.routerNameMap[name]
        if (route) {
          routes.push(route)
        }
      })

      return routes
    }
  }
}
</script>

<style lang="scss" scoped>
.menu-container {
  .menu-wrapper {
    div {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: 220px;
      :deep(.menu-list) {
        width: 33.33%;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 6px 0;
        cursor: pointer;
        border-radius: 3px;
        margin-bottom: 8px;
        &:hover {
          @include background_color_mix(--color-text-placeholder, #ffffff, 80%);
        }
        .svg-icon {
          width: 24px;
          height: 24px;
        }
        .primary {
          @include font_color(--color-primary);
        }
        .warning {
          @include font_color(--color-warning);
        }
        .success {
          @include font_color(--color-success);
        }
        .danger {
          @include font_color(--color-danger);
        }
        span {
          display: inline-block;
          font-size: 12px;
        }
      }
    }
  }
  .line {
    width: 100%;
    height: 1px;
    margin-top: 6px;
    margin-bottom: 14px;
    @include background_color(--border-color-lighter);
  }
}

.el-dropdown-menu {
  padding-bottom: 10px !important;
  padding-top: 18px !important;
}

.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: transparent !important;
  cursor: default !important;
}

.el-dropdown-menu .el-dropdown-menu__item:last-of-type {
  padding: 0 !important;
}
</style>
