<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link
        v-if="collapse"
        key="collapse"
        class="sidebar-logo-link"
        :to="defaultFirstRouter"
      >
        <img
          width="56px"
          height="100%"
          src="../../../../assets/images/layout/layout-platform-logo.svg"
          class="sidebar-logo"
          alt=""
        />
      </router-link>
      <router-link
        v-else
        key="expand"
        class="sidebar-logo-link"
        :to="defaultFirstRouter"
      >
        <img
          width="232px"
          height="100%"
          src="../../../../assets/images/layout/layout-platform-logo-white.svg"
          class="sidebar-logo"
          alt=""
        />
      </router-link>
    </transition>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Logo',
  props: {
    collapse: {
      type: Boolean
    }
  },
  computed: {
    ...mapGetters(['wholeAccessedRoutes']),
    defaultFirstRouter() {
      return this.wholeAccessedRoutes[0]
        ? this.wholeAccessedRoutes[0].path
        : '/'
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebar-logo-container {
  position: relative;
  height: 56px;
  overflow: hidden;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #4b4b4b;
  & .sidebar-logo-link {
    & .sidebar-logo {
      vertical-align: middle;
      display: inline-block;
    }
  }
}
</style>
