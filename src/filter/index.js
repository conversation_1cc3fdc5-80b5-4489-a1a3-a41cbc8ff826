/*
 * @Descripttion: 全局过滤器
 * @Author: 田柱
 * @Date: 2021-04-13 16:08:13
 * @LastEditTime: 2021-04-13 16:11:58
 */

export function richTextFilter(richText) {
  if (!richText) {
    return ''
  }
  return (
    richText
      .replace(/&(?!#?\w+;)/g, '&amp;')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;;/g, '')
      .replace(/amp;/g, '')
      .replace(/&quot;/g, '"')
      .replace(/quot;/g, '"')
      .replace(/&#39;/g, "'")
      // .replace(/&nbsp;/g, ' ')
      // .replace(/nbsp;/g, ' ')
      .replace(/&ldquo;/g, '')
      .replace(/ldquo;/g, '')
      .replace(/&rdquo;/g, '')
      .replace(/rdquo;/g, '')
      .replace(/ensp;/g, '')
      .replace(/<img[^>]*>/gi, match => {
        return match
          .replace(/style="[^"]+"/gi, '')
          .replace(/style='[^']+'/gi, '')
          .replace(/width="[^"]+"/gi, '')
          .replace(/width='[^']+'/gi, '')
          .replace(/height="[^"]+"/gi, '')
          .replace(/height='[^']+'/gi, '')
      })
      .replace(/<img/gi, '<img style="max-width:100%;height:auto;"')
      .replace(/<a/gi, '<a target="_blank" ')
  )
  // .replace(/&/g, '')
}

export function noData(value) {
  if (value === '' || value === 'null' || value === null || value === undefined || value === '-')
    return '--'
  return value
}

export function noDataAmount(value) {
  if (value === '' || value === 'null' || value === null || value === undefined)
    return '-'
  return value
}

//写一个获取url的函数
export function getQueryVariable(variable) {
  let query = window.location.search.substring(1)
  let vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    let pair = vars[i].split('=')
    if (pair[0] === variable) {
      return pair[1]
    }
  }
  return false
}

//过滤字符串只展示年月日
export function filterDate(value) {
  if (!value) return ''
  return value.split(' ')[0]
}

//过滤两个字符串并提出不同的部分
export function filterDiff(value1, value2) {
  if (!value1 || !value2) return ''
  let diff = value1.split('').filter((item, index) => {
    return item !== value2[index]
  })
  let data = diff.findIndex(item => {
    return item === '-'
  })
  if (data > -1) {
    return diff.slice(0, data).join('')
  } else {
    return diff.join('')
  }
}
