<template>
  <el-autocomplete
    v-model="modelObj.contact"
    :fetch-suggestions="querySearch"
    :debounce="500"
    placeholder="请输入联系人姓名"
    @select="handleSelect"
    @input="handleChange"
    :popper-append-to-body="false"
  >
    <template slot-scope="{ item }">
      <div class="line-1" v-html="item.text"></div>
    </template>
  </el-autocomplete>
</template>

<script>
import { getContacts } from '@/api/common'
export default {
  name: 'AutocompleteCom',
  props: {
    value: {
      required: true
    },
    entId: {
      type: [Number, String],
      default: ''
    }
  },
  data() {
    return {
      modelObj: {
        id: '',
        contact: '',
        contactPhone: ''
      },
      restaurants: [],
    }
  },
  watch: {
    value(val) {
      if (!val) return false
      try {
        this.modelObj = JSON.parse(val)
        this.entId && this.getContacts(this.entId)
      } catch (error) {
        this.modelObj = {
          id: '',
          contact: val,
          contactPhone: ''
        }
      }
    }
  },
  methods: {
    getContacts(row) {
      getContacts(row).then(res => {
        this.restaurants = res.map(item => {
          return {
            contact: item.contact,
            contactPhone: item.phone,
            id: item.id,
            text: `${item.contact}/(${item.phone})`
          }
        })
      })
    },
    async querySearch(queryString, cb) {
      if (!queryString) return cb([])
      let restaurants = this.restaurants;
      let results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
      cb(results);
    },
    createFilter(queryString) {
      return (restaurant) => {
        const contact = restaurant.contact || '';
        return contact.toLowerCase().indexOf(queryString.toLowerCase()) === 0;
      };
    },
    handleChange(e) {
      this.modelObj.id = ''
      const model = e ? JSON.stringify(this.modelObj) : ''
      this.$emit('input', model)
    },
    handleSelect(item) {
      this.modelObj.id = item.id
      this.modelObj.contact = item.contact
      this.modelObj.contactPhone = item.contactPhone
      this.$emit('input', JSON.stringify(this.modelObj))
    }
  }
}
</script>

<style scoped lang="scss">
:deep(.el-autocomplete-suggestion) {
  width: 600px !important;
  .el-autocomplete-suggestion__wrap {
    max-height: 240px !important;
  }
}
</style>
