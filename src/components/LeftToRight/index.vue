<template>
  <div class="arrowhead-container">
    <el-button
      class="arrowhead pointer"
      @click="pageHandle('back')"
      :disabled="!!!backId"
    >
      <svg-icon icon-class="chevron-left" class="font-size-21" />
    </el-button>
    <el-button
      class="arrowhead arrowhead-right pointer"
      @click="pageHandle('next')"
      :disabled="!!!nextId"
    >
      <svg-icon icon-class="chevron-right" class="font-size-21" />
    </el-button>
  </div>
</template>

<script>
export default {
  name: 'LeftToRight',
  props: {
    backId: {
      type: Number,
      default: 0
    },
    nextId: {
      type: Number,
      default: 0
    },
    params: {
      type: Object,
      default: () => ({})
    },
    path: {
      type: String,
      default: ''
    }
  },
  methods: {
    pageHandle(type) {
      if (type === 'back' && !this.backId) return
      if (type === 'next' && !this.nextId) return
      let cid = type === 'back' ? this.backId : this.nextId
      this.$router.replace({
        path: this.path,
        query: {
          ...this.params,
          id: cid
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.arrowhead-container {
  .arrowhead {
    width: 32px;
    height: 32px;
    border-radius: 16px;
    position: fixed;
    top: 528px;
    left: calc(50% - 680px);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    &.arrowhead-right {
      left: inherit;
      right: calc(50% - 680px);
    }
    &:hover {
      background: #fff8f3;
      color: #ed7b2f;
    }
  }
}
</style>
