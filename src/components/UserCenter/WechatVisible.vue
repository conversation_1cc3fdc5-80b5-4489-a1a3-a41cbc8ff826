<template>
  <dialog-cmp
    title="微信绑定"
    :visible.sync="wechatVisible"
    width="560px"
    @confirmDialog="wechatConfirmDialog"
  >
    <div id="qrCodeContainer" class="qr-code-container">
      <div class="qr-code-wrapper" v-if="!bindSuccess">
        <div class="qr-code-wrapper" v-loading="codeLoading">
          <el-image v-show="codeImg" class="qr-code-img" :src="codeImg" />
        </div>
        <div v-if="expireStatus" class="reset-code">
          <span>二维码已过期</span>
          <el-button type="primary" class="m-t-8" @click="getWechatCode"
            >刷新</el-button
          >
        </div>
        <div class="wechat-tips">请使用微信扫码绑定</div>
      </div>
      <template v-else>
        <svg-icon icon-class="check-circle" class="color-success" />
        <div class="wechat-tips bind-success">微信绑定成功</div>
      </template>
    </div>
  </dialog-cmp>
</template>

<script>
import { getBindCode, getWechatInfo } from '@/api/user'
let wechatTimer = null
// 接口轮询时间: 单位ms
const wechatTimerCount = 1500

export default {
  name: 'WechatVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      wechatVisible: false,
      bindSuccess: false,
      expireStatus: false, // 二维码是否过期
      codeImg: '',
      codeLoading: false,
      ticket: ''
    }
  },
  watch: {
    visible(val) {
      this.wechatVisible = val
      if (val) this.getWechatCode()
    },
    wechatVisible(val) {
      if (!val) {
        this.wechatConfirmDialog()
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 清除轮播微信扫码接口
    clearWechatInfoInterval() {
      if (wechatTimer) clearInterval(wechatTimer)
    },
    // 获取扫码状态
    async getWechatInfo() {
      // authLoginRespVO: 认证信息; loginStatus: 是否允许登录; openId: 微信openId; sign: 手机号绑定状态; userList: 绑定多用户列表; ticket: 二维码票据，作为唯一标识符
      const { openId, expireStatus, bindingStatus } = await getWechatInfo({
        ticket: this.ticket
      })
      this.expireStatus = expireStatus
      this.bindSuccess = bindingStatus
      if (expireStatus || bindingStatus) return this.clearWechatInfoInterval()
      if (!openId) return false
    },
    // 获取微信二维码
    async getWechatCode() {
      try {
        this.expireStatus = false
        this.codeLoading = true
        const { codeUrl, ticket } = await getBindCode()
        this.codeLoading = false
        this.codeImg = codeUrl
        this.ticket = ticket
        wechatTimer = setInterval(() => {
          this.getWechatInfo()
        }, wechatTimerCount)
      } catch (e) {
        this.codeLoading = false
        console.log(e)
      }
    },
    wechatConfirmDialog() {
      this.clearWechatInfoInterval()
      this.wechatVisible = false
      this.bindSuccess = false
      this.expireStatus = false
      this.codeLoading = false
      this.codeImg = ''
      this.ticket = ''
      this.$emit('update')
    }
  }
}
</script>

<style scoped lang="scss">
.qr-code-container {
  padding: 80px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  .qr-code-wrapper {
    width: 200px;
    height: 200px;
    position: relative;
    .qr-code-img {
      width: 100%;
      height: 100%;
    }
    .reset-code {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      background: rgba(0, 0, 0, 0.5);
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      //backdrop-filter: blur(3px);
    }
  }
  .wechat-tips {
    font-size: 16px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.6);
    line-height: 24px;
    margin-top: 8px;
    text-align: center;
  }
  .color-success {
    font-size: 105px;
  }
  .bind-success {
    margin-top: 47px;
  }
}
</style>
