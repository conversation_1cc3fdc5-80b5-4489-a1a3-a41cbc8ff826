<template>
  <VueCropper
    ref="cropper"
    :img="option.img"
    :outputSize="option.size"
    :outputType="option.outputType"
    :info="option.info"
    :full="option.full"
    :canMove="option.canMove"
    :canMoveBox="option.canMoveBox"
    :original="option.original"
    :autoCrop="option.autoCrop"
    :fixed="option.fixed"
    :fixedNumber="option.fixedNumber"
    :centerBox="option.centerBox"
    :infoTrue="option.infoTrue"
    :autoCropWidth="option.autoCropWidth ? option.autoCropWidth : 0"
    :autoCropHeight="option.autoCropHeight ? option.autoCropHeight : 0"
    :fixedBox="option.fixedBox"
  ></VueCropper>
</template>
<script>
import { VueCropper } from 'vue-cropper'

export default {
  name: 'ImgCropper',
  components: { VueCropper },
  props: {
    // dialogPar: {
    //   type: Object,
    //   required: false,
    //   default: () => {
    //     return {}
    //   }
    // },
    /**新增props参数如下**/
    graphicsState: {
      // 此参数为新增参数，设置裁剪图形形状 square:方形、circular:圆形(默认：square(方形))
      type: String,
      required: false,
      default: () => {
        return 'circular'
      }
    },
    autoCropOption: {
      // 此参数为新增参数，设置裁剪框参数，参数属性配置参照下面option
      type: Object,
      required: false,
      default: () => {
        return {
          info: false, // 是否显示裁剪框的宽高信息
          autoCropWidth: 320,
          autoCropHeight: 320
        }
      }
    }
  },
  data() {
    return {
      // 裁剪组件基础配置option，更多属性或更多具体说明参考官方文档
      option: {
        img: '', // 裁剪图片的地址
        info: false, // 裁剪框的大小信息(即是否显示裁剪框的宽高信息)
        outputSize: 0.9, // 裁剪生成图片的质量(0.1~1之间)
        outputType: 'png', // 裁剪生成图片的格式(jpg(jpg 需要传入jpeg))
        canScale: false, // 图片是否允许滚轮缩放(这个属性貌似没得用，不管设置true，false都可以滚轮缩放)
        autoCrop: true, // 是否默认生成截图框
        // 注：这里需要注意，如果是裁剪成圆形图片，那么截图框的宽高就是必须设置的，且最好宽高一样
        autoCropWidth: 320, // 默认生成截图框宽度
        autoCropHeight: 320, // 默认生成截图框高度
        fixedBox: true, // 固定截图框大小 不允许改变
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [1, 1], // 截图框的宽高比例(这是比例，按两个值的比值大小进行截图框的宽高的设置，[1,1]和[100,100]是一样的)
        full: true, // 是否输出原图比例的截图
        canMove: true, // 上传图片是否可以移动
        canMoveBox: false, // 截图框能否拖动
        original: false, // 上传图片按照原始比例渲染
        centerBox: true, // 截图框是否被限制在图片里面
        infoTrue: true // true 为展示真实输出图片宽高 false 展示看到的截图框宽高
      },
      dialogPar: {}
    }
  },
  methods: {
    init(info) {
      this.dialogPar = info
      this.option = {
        ...this.option,
        ...this.autoCropOption,
        ...this.dialogPar,
        img: this.dialogPar.imgUrl
      }
      this.$refs.cropper.refresh()
      this.$refs.cropper.reload()
      this.$refs.cropper.rotateClear()
    },
    // 向左边旋转90度
    rotateLeft() {
      this.$refs.cropper.rotateLeft() // 只能固定向左边旋转90度，不接受设定旋转角度
    },
    // 向右边旋转90度
    rotateRight() {
      this.$refs.cropper.rotateRight() // 只能固定向右边旋转90度，不接受设定旋转角度
    },
    // 获取截图的 blob 数据
    getCropBlob() {
      this.$refs.cropper.getCropBlob(data => {
        if (this.graphicsState === 'circular') {
          // 此时的blob数据实际上是一个autoCropWidth*autoCropHeight的方形图片数据
          // 因此需要在此图的基础上进一步裁剪
          this.drawAndClipImage(data, this.dialogPar.name)
        } else {
          // 由于服务端文件上传接口统一接收file对象，从而数据在提交前需要将blob数据转为file对象，转换方法如下：
          const file = new window.File([data], this.dialogPar.name, {
            type: data.type
          })
          // 获取图片数据后，将数据上传给父组件(imgUrl:图片本地预览 blob数据地址，fileData:截取的图片数据 转换成的file对象)
          this.$emit('emitPar', {
            imgUrl: URL.createObjectURL(data),
            fileData: file
          })
        }
      })
    },

    /**
     * 新增方法(截圆形图时使用)
     * 将截图的 blob 数据绘制成图片，然后进一步根据需要裁剪成圆形图片
     */
    drawAndClipImage(file, fileName) {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = e => {
        const src = e.target.result
        const image = new Image()
        image.src = src
        image.onload = () => {
          const canvas = document.createElement('canvas')
          const width = image.width
          const height = image.height
          canvas.width = width
          canvas.height = height
          // 计算圆形图片的圆心及图片半径
          const circle = {
            x: width / 2,
            y: height / 2,
            r: width / 2
          }
          const context = canvas.getContext('2d')
          context.clearRect(0, 0, width, height)
          // 在canvas开始绘制前填充白色透明背景并设置透明度，用以清除图片裁剪后透明区域变成黑色的问题
          context.fillStyle = 'rgba(255, 255, 255, 0)'
          context.fillRect(0, 0, width, height)

          // 开始路径画圆，剪切处理
          context.save() // 保存当前canvas的状态
          context.beginPath()
          context.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2, false) // 创建弧/曲线(用于创建圆形或部分圆)
          context.clip() // 从原始画布剪切任意形状和尺寸的区域
          context.drawImage(image, 0, 0)
          context.restore() // 返回之前保存过的路径状态和属性，恢复状态

          // 将canvas图片转换成 blob数据
          canvas.toBlob(blob => {
            // 内部注释参考 getCropBlob方法 else部分
            const file = new File([blob], fileName, { type: blob.type })
            this.$emit('emitPar', {
              imgUrl: URL.createObjectURL(blob),
              fileData: file
            })
          })
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
::v-deep {
  .cropper-box {
    width: 320px;
    height: 320px;
  }
  .cropper-view-box {
    border-radius: 50%; // 将裁剪框由方形调整为圆形
  }
  .cropper-face {
    background-color: transparent; // 清除裁剪框填充背景色
  }
}
</style>
