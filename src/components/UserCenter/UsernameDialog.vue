<template>
  <dialog-cmp
    title="修改昵称"
    :visible.sync="usernameVisible"
    width="560px"
    @confirmDialog="usernameConfirmDialog"
  >
    <el-form
      ref="usernameForm"
      :model="usernameForm"
      :rules="usernameRules"
      label-position="top"
      status-icon
    >
      <el-form-item label="昵称" prop="nickname">
        <el-input
          v-model.trim="usernameForm.nickname"
          minlength="2"
          maxlength="10"
          placeholder="请输入新的昵称（2-10个字符）"
        />
      </el-form-item>
    </el-form>
  </dialog-cmp>
</template>

<script>
import { modifyNickname } from '@/api/user'

export default {
  name: 'UsernameDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) return callback(new Error('请输入新的昵称（2-10个字符）'))
      if (value.length > 10)
        return callback(new Error('新的昵称不能超过10个字符'))
      if (value.length < 2) return callback(new Error('新的昵称至少2个字符'))
      callback()
    }
    return {
      usernameVisible: false,
      usernameForm: {
        nickname: ''
      },
      usernameRules: {
        nickname: [{ validator: validateUsername, trigger: ['blur', 'change'] }]
      }
    }
  },
  watch: {
    visible(val) {
      this.usernameVisible = val
      if (val) this.usernameForm.nickname = this.info.nickname || ''
    },
    usernameVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.usernameForm = this.$options.data().usernameForm
        this.$refs.usernameForm && this.$refs.usernameForm.resetFields()
      }
    }
  },
  methods: {
    usernameConfirmDialog() {
      this.$refs.usernameForm.validate(valid => {
        if (!valid) return false
        modifyNickname(this.usernameForm.nickname).then(() => {
          this.$message.success('编辑成功')
          this.usernameVisible = false
          this.$emit('update')
        })
      })
    }
  }
}
</script>

<style scoped></style>
