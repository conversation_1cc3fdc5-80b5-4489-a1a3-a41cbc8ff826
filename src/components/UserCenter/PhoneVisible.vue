<template>
  <dialog-cmp
    title="修改手机号"
    :visible.sync="phoneVisible"
    width="560px"
    :haveOperation="false"
    :before-close="handleClose"
    @confirmDialog="phoneConfirmDialog"
  >
    <el-form
      ref="phoneForm"
      :model="phoneForm"
      :rules="phoneRules"
      label-position="top"
    >
      <el-form-item
        v-if="step === 1"
        label="手机号"
        prop="oldPhoneNumber"
        key="oldPhoneNumber"
      >
        <el-input v-model="phoneForm.oldPhoneNumber" disabled>
          <span slot="prepend" class="prepend">+86</span>
        </el-input>
      </el-form-item>
      <el-form-item
        v-else
        class="new-phone"
        label="新的手机号"
        prop="newPhoneNumber"
        key="newPhoneNumber"
      >
        <el-input
          v-model="phoneForm.newPhoneNumber"
          placeholder="请输入新的手机号"
        >
          <span slot="prefix" class="prepend">+86</span>
        </el-input>
      </el-form-item>
      <el-form-item label="验证码" prop="smsCode">
        <el-input v-model="phoneForm.smsCode" placeholder="请输入验证码">
          <el-button
            type="text"
            slot="suffix"
            :disabled="disabled"
            @click="getCode"
            >{{ codeText }}</el-button
          >
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog">取 消</el-button>
      <el-button v-if="step === 1" type="primary" @click="nextHandle">
        <span>下一步</span>
      </el-button>
      <el-button v-else type="primary" @click="phoneConfirmDialog">
        <span>确 定</span>
      </el-button>
    </div>
  </dialog-cmp>
</template>

<script>
import { SMS_CODE_TIME } from '@/settings'
import { Phone_Regexp, Smscode_Regexp_Validate } from '@/utils/validate'
import { getSmsCaptcha } from '@/api/common'
import { checkModifyPhone, isExitPhone, modifyPhone } from '@/api/user'
// 验证码
const UN_SEND_TEXT = '发送验证码'
const SEND_ING = '验证码发送中'
const COUNT_DOWN = time => `${time}s后再次获取`
let countDownTimer = null

export default {
  name: 'PhoneVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const newPhoneNumberValidate = async (rule, value, callback) => {
      if (!value) return callback(new Error('请输入手机号'))
      if (!Phone_Regexp.test(value))
        return callback(new Error('请输入正确的11位手机号'))
      // 校验手机号已经注册
      const hasSamePhone = await isExitPhone(value)
      if (hasSamePhone) return callback(new Error('手机号已被绑定'))
      callback()
    }
    return {
      phoneVisible: false,
      phoneForm: {
        code: '', // 校验原始手机号后端返回，修改新手机号时需要
        oldPhoneNumber: '',
        newPhoneNumber: '',
        smsCode: ''
      },
      phoneRules: {
        newPhoneNumber: [
          { validator: newPhoneNumberValidate, trigger: ['blur', 'change'] }
        ],
        smsCode: [
          { message: '请输入手机验证码', trigger: ['blur', 'change'] },
          {
            validator: Smscode_Regexp_Validate(),
            trigger: ['blur', 'change']
          }
        ]
      },
      codeText: UN_SEND_TEXT,
      timeCount: SMS_CODE_TIME, // 默认倒计时时间
      disabled: false,
      type: '18',
      code: 'pass',
      step: 1
    }
  },
  watch: {
    visible(val) {
      this.phoneVisible = val
      if (val) this.phoneForm.oldPhoneNumber = this.info.mobile
    },
    phoneVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.phoneForm = this.$options.data().phoneForm
        this.$refs.phoneForm && this.$refs.phoneForm.resetFields()
        this.step = 1
        this.disabled = false
        this.codeText = UN_SEND_TEXT
        this.timeCount = SMS_CODE_TIME
      }
    }
  },
  methods: {
    nextHandle() {
      this.$refs.phoneForm.validate(valid => {
        if (!valid) return false
        checkModifyPhone({
          phone: this.phoneForm.oldPhoneNumber,
          smsCode: this.phoneForm.smsCode
        }).then(res => {
          this.phoneForm.code = res
          this.step = 2
          if (countDownTimer) clearInterval(countDownTimer)
          this.disabled = false
          this.codeText = UN_SEND_TEXT
          this.phoneForm.smsCode = ''
          this.$refs.phoneForm.resetFields()
        })
      })
    },
    // 接口请求
    sendCode() {
      // 接口请求
      this.loading = true
      this.text = SEND_ING
      let phoneNumber = this.phoneForm.oldPhoneNumber
      if (this.step === 2) phoneNumber = this.phoneForm.newPhoneNumber
      getSmsCaptcha({
        code: this.code,
        phoneNumber,
        type: this.type
      })
        .then(() => {
          this.$message.success('手机验证码发送成功')
          this.startCountDown()
        })
        .catch(() => {
          this.loading = false
          this.text = UN_SEND_TEXT
        })
    },
    // 倒计时开始
    startCountDown() {
      const that = this
      if (countDownTimer) {
        clearInterval(countDownTimer)
      }
      let count = this.timeCount
      this.disabled = true
      countDownTimer = setInterval(
        (function countDown() {
          if (count > 0 && count <= that.timeCount) {
            that.codeText = COUNT_DOWN(count)
            count--
          } else {
            clearInterval(countDownTimer)
            that.disabled = false
            that.codeText = UN_SEND_TEXT
          }
          return countDown
        })(),
        1000
      )
    },
    // 手机号校验
    validatePhoneNumber() {
      let phoneNumber = this.phoneForm.oldPhoneNumber
      if (this.step === 2) phoneNumber = this.phoneForm.newPhoneNumber
      if (Phone_Regexp.test(phoneNumber)) {
        return true
      } else {
        this.$message.error('请输入正确的11位手机号')
        return false
      }
    },
    // 发送验证码
    getCode() {
      if (this.validatePhoneNumber()) this.sendCode()
    },
    phoneConfirmDialog() {
      this.$refs.phoneForm.validate(valid => {
        if (!valid) return false
        modifyPhone({
          code: this.phoneForm.code,
          phone: this.phoneForm.newPhoneNumber,
          smsCode: this.phoneForm.smsCode
        }).then(() => {
          this.$message.success('绑定成功')
          this.phoneVisible = false
          this.$emit('update')
        })
      })
    },
    handleClose(done) {
      done()
      this.closeDialog()
    },
    closeDialog() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped lang="scss">
.prepend {
  @include font_color(--color-text-placeholder);
}
:deep(.new-phone) {
  .el-input-group__prepend {
    background: transparent;
  }
  .el-input__prefix {
    padding-left: 8px;
  }
  .el-input__inner {
    padding-left: 64px;
  }
}
</style>
