<template>
  <dialog-cmp
    title="修改头像"
    :visible.sync="avatarVisible"
    width="560px"
    @confirmDialog="confirmDialog"
  >
    <div class="avatar-header">
      <div class="left">
        <div class="label">预览头像</div>
        <el-image
          class="avatar-img"
          v-if="radioType === 2 && info.logoUrl"
          :src="info.logoUrl"
        />
        <div v-else class="default-avatar">{{ info.logoStr }}</div>
      </div>
      <div class="right">
        <div class="label">头像方式</div>
        <el-radio-group v-model="radioType" @change="radioChange">
          <el-radio :label="1">跟随姓名生成默认文字头像</el-radio>
          <el-radio :label="2">
            自定义上传本地头像
            <el-button
              v-if="radioType === 2"
              style="margin-left: 114px"
              type="text"
              :disabled="!cropperShow"
              @click.s.stop="reUploadHandle"
              >重新上传
            </el-button>
          </el-radio>
        </el-radio-group>
      </div>
    </div>
    <div v-if="radioType === 2" class="img-cropper">
      <el-upload
        v-show="!cropperShow"
        class="avatar-upload"
        drag
        action="#"
        :limit="1"
        :show-file-list="false"
        :auto-upload="false"
        :on-change="imgSaveToUrl"
        accept="image/*"
        :key="uploadKey"
      >
        <svg-icon icon-class="cloud-upload" class="font-size-24" />
        <div class="el-upload__text m-t-10"><em>点击上传头像</em></div>
        <div class="el-upload__tip m-t-8">
          支持jpg、png格式的图片，大小不超过3M
        </div>
      </el-upload>
      <img-cropper
        ref="imgCropper"
        v-show="cropperShow"
        :dialogPar="avatarFile"
        @emitPar="emitPar"
      />
    </div>
  </dialog-cmp>
</template>

<script>
import ImgCropper from './ImgCropper'
import { getBaseHeader } from '@/utils/request'
import { uploadUrl } from '@/api/common'
import axios from 'axios'
import { modifyLogo } from '@/api/user'

export default {
  name: 'avatarDialog',
  components: {
    ImgCropper
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      avatarVisible: false,
      radioType: 1,
      avatarFile: {},
      cropperShow: false,
      uploadKey: Math.random()
    }
  },
  watch: {
    visible(val) {
      this.avatarVisible = val
      if (val) this.avatarInit()
    },
    avatarVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.avatarFile = {}
        this.radioType = 1
        this.cropperShow = false
      }
    }
  },
  methods: {
    radioChange(e) {
      if (e === 2) {
        this.cropperShow = !!this.avatarFile.imgUrl
        this.$refs.imgCropper && this.$refs.imgCropper.init(this.avatarFile)
      }
    },
    async avatarInit() {
      this.radioType = this.info.logoType || 1
      this.avatarFile.imgUrl = this.info.logoUrl || ''
      this.avatarFile.name = this.info.logoName || ''
      await this.$nextTick()
      if (this.radioType === 2) {
        this.cropperShow = true
        this.$refs.imgCropper && this.$refs.imgCropper.init(this.avatarFile)
      }
    },
    async reUploadHandle() {
      const dom = document.querySelector('.el-upload__text')
      dom.click()
    },
    confirmDialog() {
      if (this.radioType === 2 && !this.cropperShow)
        return this.$message.error('请上传自定义头像')
      if (this.radioType === 2) return this.$refs.imgCropper.getCropBlob()
      this.modifyLogo()
    },
    emitPar(row) {
      this.uploadImage(row)
    },
    modifyLogo(res) {
      const params = {
        logoType: this.radioType,
        logoAttach: res && res.id
      }
      modifyLogo(params).then(() => {
        this.$message.success('修改成功')
        this.avatarVisible = false
        this.$emit('update')
      })
    },
    // 自定义上传方法
    uploadImage(file) {
      const formData = new FormData()
      formData.append('file', file.fileData)
      formData.append('type', 'avatar')
      const requestConfig = {
        headers: {
          ...axios.defaults.headers,
          ...getBaseHeader()
        }
      }
      axios.post(uploadUrl, formData, requestConfig).then(res => {
        const { data = {} } = res
        if (data.code === 200) return this.modifyLogo(data.data)
        this.$toast.error('上传失败')
      })
    },
    imgSaveToUrl(file) {
      const fileType = file.raw.type
      const fileSize = file.size
      if (fileType.indexOf('image') === -1)
        return this.$toast.warning('不支持的图片格式，请重新上传')
      if (fileSize / 1024 / 1024 > 3)
        return this.$toast.warning('图片大小超出3MB，请重新上传')
      const reader = new FileReader()
      reader.onload = e => {
        this.avatarFile = {
          size: file.size,
          imgUrl: e.target.result,
          name: file.name
        }
        this.cropperShow = true
        this.$refs.imgCropper.init(this.avatarFile)
      }
      reader.readAsDataURL(file.raw)
      this.uploadKey = Math.random()
    }
  }
}
</script>

<style scoped lang="scss">
.avatar-header {
  display: flex;
  .label {
    font-size: 14px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
    margin-bottom: 8px;
  }
  .left {
    width: 56px;
    height: 56px;
    flex-shrink: 0;
    .avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 28px;
    }
    .default-avatar {
      width: 100%;
      height: 100%;
      @include background_color(--color-primary);
      border: 1px solid #fff8f3;
      border-radius: 28px;
      font-size: 14px;
      font-weight: 350;
      color: #fff;
      line-height: 56px;
      text-align: center;
      cursor: pointer;
    }
  }
  .right {
    width: 100%;
    margin-left: 32px;
  }
}
.img-cropper {
  width: 320px;
  height: 320px;
  margin-left: 90px;
}
:deep(.el-radio) {
  display: block;
  margin-bottom: 16px;
  .el-radio__input {
    .el-radio__inner {
      width: 16px;
      height: 16px;
    }
    &.is-checked .el-radio__inner {
      width: 16px;
      height: 16px;
      background: transparent;
      &:after {
        width: 8px;
        height: 8px;
        background: #ed7b2f;
      }
    }
  }
  .el-radio__label {
    font-size: 14px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.9);
    line-height: 22px;
  }
  &.is-checked .el-radio__label {
    color: rgba(0, 0, 0, 0.9);
  }
}
:deep(.el-upload-dragger) {
  width: 320px;
  height: 320px;
  padding-top: 120px;
  background: #eee;
  border-color: #dcdcdc;
  .el-upload__tip {
    color: rgba(0, 0, 0, 0.4);
    line-height: 20px;
  }
}
</style>
