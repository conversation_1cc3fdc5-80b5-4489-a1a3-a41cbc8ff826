<template>
  <dialog-cmp
    title="修改真实姓名"
    :visible.sync="remarkNameVisible"
    width="560px"
    @confirmDialog="remarkNameConfirmDialog"
  >
    <el-form
      ref="remarkNameForm"
      :model="remarkNameForm"
      :rules="remarkNameRules"
      label-position="top"
      status-icon
    >
      <el-form-item label="真实姓名" prop="acName">
        <el-input
          v-model.trim="remarkNameForm.acName"
          minlength="2"
          maxlength="10"
          placeholder="请输入您的真实姓名（2-10个字符）"
        />
      </el-form-item>
    </el-form>
  </dialog-cmp>
</template>

<script>
import { modifyAcName } from '@/api/user'

export default {
  name: 'RemarkNameVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!value) return callback(new Error('请输入您的真实姓名（2-10个字符）'))
      if (value.length > 10)
        return callback(new Error('您的真实姓名不能超过10个字符'))
      if (value.length < 2)
        return callback(new Error('您的真实姓名至少2个字符'))
      callback()
    }
    return {
      remarkNameVisible: false,
      remarkNameForm: {
        acName: ''
      },
      remarkNameRules: {
        acName: [{ validator: validateUsername, trigger: ['blur', 'change'] }]
      }
    }
  },
  watch: {
    visible(val) {
      this.remarkNameVisible = val
      if (val) this.remarkNameForm.acName = this.info.acName || ''
    },
    remarkNameVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.remarkNameForm = this.$options.data().remarkNameForm
        this.$refs.remarkNameForm && this.$refs.remarkNameForm.resetFields()
      }
    }
  },
  methods: {
    remarkNameConfirmDialog() {
      this.$refs.remarkNameForm.validate(valid => {
        if (!valid) return false
        modifyAcName(this.remarkNameForm.acName).then(() => {
          this.$message.success('编辑成功')
          this.remarkNameVisible = false
          this.$emit('update')
        })
      })
    }
  }
}
</script>

<style scoped></style>
