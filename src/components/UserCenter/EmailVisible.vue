<template>
  <dialog-cmp
    title="修改邮箱"
    :visible.sync="emailVisible"
    width="560px"
    @confirmDialog="emailConfirmDialog"
  >
    <el-form
      ref="emailForm"
      :model="emailForm"
      :rules="emailRules"
      label-position="top"
      status-icon
    >
      <el-form-item label="邮箱" prop="email">
        <el-input v-model="emailForm.email" placeholder="请输入邮箱账号" />
      </el-form-item>
    </el-form>
  </dialog-cmp>
</template>

<script>
import { modifyEmail } from '@/api/user'

export default {
  name: 'EmailVisible',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    const validateEmail = (rule, value, callback) => {
      if (!value) return callback(new Error('请输入邮箱地址'))
      const isPass =
        /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/.test(
          value
        )
      if (isPass) return callback()
      callback(new Error('请输入正确的邮箱地址'))
    }
    return {
      emailVisible: false,
      emailForm: {
        email: ''
      },
      emailRules: {
        email: [{ validator: validateEmail, trigger: ['blur', 'change'] }]
      }
    }
  },
  watch: {
    visible(val) {
      this.emailVisible = val
      if (val) this.emailForm.email = this.info.email || ''
    },
    emailVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
        this.emailForm = this.$options.data().emailForm
        this.$refs.emailForm && this.$refs.emailForm.resetFields()
      }
    }
  },
  methods: {
    emailConfirmDialog() {
      this.$refs.emailForm.validate(valid => {
        if (!valid) return false
        modifyEmail(this.emailForm.email).then(() => {
          this.$message.success('编辑成功')
          this.emailVisible = false
          this.$emit('update')
        })
      })
    }
  }
}
</script>

<style scoped></style>
