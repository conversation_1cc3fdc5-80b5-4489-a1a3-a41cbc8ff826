<template>
  <!--修改密码弹窗-->
  <middle-dialog
    title="修改密码"
    :visible.sync="dialogVisible"
    width="560px"
    @confirmDialog="confirmDialog"
  >
    <el-form
      ref="modifyForm"
      status-icon
      :model="modifyForm"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="原密码" prop="oldPwd">
        <el-input
          v-model="modifyForm.oldPwd"
          type="password"
          autocomplete="off"
          placeholder="请输入原始密码"
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input
          v-model="modifyForm.newPwd"
          type="password"
          autocomplete="off"
          placeholder="请输入新密码"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPwd">
        <el-input
          v-model="modifyForm.confirmPwd"
          type="password"
          autocomplete="off"
          placeholder="请再次输入新密码"
        />
      </el-form-item>
    </el-form>
  </middle-dialog>
</template>

<script>
import MiddleDialog from '@/components/BasicDialog'
import { modifyPwd } from '@/api/user'
import { mapActions } from 'vuex'
import { Password_Regexp_Validate } from '@/utils/validate'

export default {
  name: 'ModifyPassword',
  components: { MiddleDialog },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      modifyForm: {
        oldPwd: '',
        newPwd: '',
        confirmPwd: ''
      },
      rules: {
        oldPwd: [
          { validator: Password_Regexp_Validate(), trigger: ['blur', 'change'] }
        ],
        newPwd: [
          {
            validator: Password_Regexp_Validate(),
            trigger: ['blur', 'change']
          }
        ],
        confirmPwd: [
          {
            validator: (rule, value, callback) => {
              if (value === '') {
                callback(new Error('请再次输入密码'))
              } else if (value !== this.modifyForm.newPwd) {
                callback(new Error('两次输入密码不一致!'))
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change']
          }
        ]
      }
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    ...mapActions(['OpenGuide']),
    // 提交修改密码
    confirmDialog() {
      this.$refs.modifyForm.validate(valid => {
        if (valid) {
          modifyPwd({
            oldPassword: this.modifyForm.oldPwd,
            newPassword: this.modifyForm.newPwd
          }).then(() => {
            this.$toast({
              message: '密码修改成功',
              onClose: () => {
                this.logout()
              }
            })
          })
        } else {
          return false
        }
      })
    },

    logout() {
      this.$store.dispatch('Logout').then(() => {
        location.reload()
      })
    }
  }
}
</script>

<style></style>
