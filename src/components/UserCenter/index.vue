<template>
  <div>
    <el-dialog
      title="账户信息"
      :visible.sync="userDialogVisible"
      fullscreen
      :append-to-body="true"
    >
      <div slot="title" class="flex align-items-center">
        <svg-icon
          icon-class="arrow-left"
          class="pointer"
          @click="userDialogVisible = false"
        />
        <span class="m-l-8">账户信息</span>
      </div>
      <div class="user-container">
        <div>
          <div class="user-item">
            <div class="item-left flex align-items-center">
              <div class="avatar-wrapper">
                <el-image
                  v-if="userInfo.logoType === 2"
                  class="avatar-img"
                  :src="userInfo.logoUrl"
                />
                <div v-else class="default-avatar">{{ userInfo.logoStr }}</div>
                <div class="edit" @click="avatarVisible = true">编辑</div>
              </div>
              <div class="m-l-16">
                <div class="item-label">昵称</div>
                <div class="item-content m-t-8">{{ userInfo.nickname }}</div>
              </div>
            </div>
            <el-button type="text" @click="usernameVisible = true"
              >编辑</el-button
            >
          </div>
          <div class="user-item">
            <div class="item-left flex flex-direction-column">
              <div class="item-label">真实姓名</div>
              <div class="item-content m-t-8">
                <span v-if="userInfo.acName">{{ userInfo.acName }}</span>
                <span v-else class="empty-text">待填写</span>
              </div>
            </div>
            <el-button type="text" @click="remarkNameVisible = true">{{
              userInfo.acName ? '修改' : '填写'
            }}</el-button>
          </div>
          <div class="user-item">
            <div class="item-left flex flex-direction-column">
              <div class="item-label">登录密码</div>
              <div class="item-content m-t-8">*********</div>
            </div>
            <el-button type="text" @click="passwordVisible = true"
              >修改</el-button
            >
          </div>
          <div class="user-item">
            <div class="item-left flex flex-direction-column">
              <div class="item-label">绑定手机号</div>
              <div class="item-content m-t-8">
                <span v-if="userInfo.mobile">{{ userInfo.mobile }}</span>
                <span v-else class="empty-text">待绑定</span>
              </div>
            </div>
            <el-button type="text" @click="phoneVisible = true">{{
              userInfo.mobile ? '更换' : '绑定'
            }}</el-button>
          </div>
          <div class="user-item">
            <div class="item-left flex flex-direction-column">
              <div class="item-label">绑定邮箱</div>
              <div class="item-content m-t-8">
                <span v-if="userInfo.email">{{ userInfo.email }}</span>
                <span v-else class="empty-text">待绑定</span>
              </div>
            </div>
            <el-button type="text" @click="emailVisible = true">{{
              userInfo.email ? '更换' : '绑定'
            }}</el-button>
          </div>
          <div class="user-item">
            <div class="item-left flex flex-direction-column">
              <div class="item-label">
                <span>微信绑定</span>
                <svg-icon
                  icon-class="wechat"
                  class="color-success m-l-8 font-size-20"
                />
              </div>
              <div class="item-content m-t-8">
                <span v-if="userInfo.wechatStatus"
                  >方便下次微信扫码一键登录</span
                >
                <span v-else class="empty-text">待绑定</span>
              </div>
            </div>
            <el-button
              v-if="userInfo.wechatStatus"
              type="text"
              @click="wechatUnbind"
              >解绑</el-button
            >
            <el-button v-else type="text" @click="wechatVisible = true"
              >绑定</el-button
            >
          </div>
        </div>
        <div class="logout">
          <el-button class="logout-btn" @click="logout">退出登录</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 头像编辑 -->
    <avatar-dialog
      :visible.sync="avatarVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
    <!-- 昵称编辑 -->
    <username-dialog
      :visible.sync="usernameVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
    <!-- 真实姓名 -->
    <remark-name-visible
      :visible.sync="remarkNameVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
    <!-- 修改密码弹窗 -->
    <modify-password
      :visible.sync="passwordVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
    <!-- 修改手机号弹窗 -->
    <phone-visible
      :visible.sync="phoneVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
    <!-- 邮箱绑定 -->
    <email-visible
      :visible.sync="emailVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
    <!-- 微信绑定 -->
    <wechat-visible
      :visible.sync="wechatVisible"
      :info="userInfo"
      @update="getUserInfo"
    />
  </div>
</template>

<script>
import AvatarDialog from './AvatarDialog'
import UsernameDialog from './UsernameDialog'
import RemarkNameVisible from './RemarkNameVisible'
import ModifyPassword from './ModifyPassword'
import PhoneVisible from './PhoneVisible'
import WechatVisible from './WechatVisible'
import EmailVisible from './EmailVisible'
import { wechatUnbind } from '@/api/user'
import { mapGetters } from 'vuex'

export default {
  name: 'UserCenter',
  components: {
    EmailVisible,
    WechatVisible,
    PhoneVisible,
    ModifyPassword,
    RemarkNameVisible,
    UsernameDialog,
    AvatarDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // userInfo: {},
      userDialogVisible: false,
      avatarVisible: false,
      usernameVisible: false,
      remarkNameVisible: false,
      passwordVisible: false,
      phoneVisible: false,
      wechatVisible: false,
      emailVisible: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    visible(val) {
      this.userDialogVisible = val
      if (val) this.getUserInfo()
    },
    userDialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    wechatUnbind() {
      this.$confirm('确定解除绑定?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        wechatUnbind().then(() => {
          this.$message.success('解绑成功')
          this.getUserInfo()
        })
      })
    },
    getUserInfo() {
      this.$store.dispatch('GetUserInfo')
    },
    logout() {
      this.$confirm('确定退出登录 ?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('Logout').then(() => {
          this.$router.push(`/login`)
          setTimeout(() => {
            location.reload()
          }, 300)
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.user-container {
  width: 100%;
  max-width: 864px;
  margin: 0 auto;
  padding-top: 40px;
  .user-item {
    padding: 16px 24px;
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-light);
    display: flex;
    justify-content: space-between;
    align-items: center;
    &:last-child {
      border: none;
    }
    .avatar-wrapper {
      width: 56px;
      height: 56px;
      position: relative;
      .avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 28px;
      }
      .default-avatar {
        width: 100%;
        height: 100%;
        @include background_color(--color-primary);
        border-radius: 28px;
        font-size: 14px;
        font-weight: 350;
        color: #ffffff;
        line-height: 56px;
        text-align: center;
        cursor: pointer;
      }
      .edit {
        display: none;
        width: 56px;
        height: 56px;
        background: #242424;
        border-radius: 28px;
        font-size: 14px;
        font-weight: 350;
        color: #fff;
        line-height: 56px;
        text-align: center;
        position: absolute;
        left: 0;
        top: 0;
        cursor: pointer;
      }
      &:hover .edit {
        display: block;
      }
    }
    .item-label {
      font-size: 16px;
      font-weight: 350;
      color: rgba(0, 0, 0, 0.9);
      line-height: 24px;
    }
    .item-content {
      font-size: 14px;
      font-weight: 350;
      color: rgba(0, 0, 0, 0.9);
      line-height: 22px;
      .empty-text {
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }
  .logout {
    margin-top: 60px;
    text-align: center;
    .logout-btn {
      width: 320px;
      height: 32px;
      background: #fff;
      border: 1px solid;
      @include border_color(--color-danger);
      @include font_color(--color-danger);
      text-align: center;
      :deep(span) {
        justify-content: center;
      }
    }
  }
}
:deep(.el-dialog) {
  margin-top: -1px !important;
  .el-dialog__headerbtn {
    display: none;
  }
  .el-dialog__title {
    font-size: 16px;
    font-weight: 350;
    color: rgba(0, 0, 0, 0.9);
  }
  .el-dialog__body {
    max-height: max-content;
    padding-bottom: 40px;
  }
}
</style>
