<template>
  <div class="uploader-wrapper" :style="Uploader._genWrapperSize">
    <div class="default-wrapper">
      <div class="default-container">
        <i class="el-icon-plus upload-icon" />
        <!--<div class="bor b-t-l t-0 l-0"></div>-->
        <!--<div class="bor b-t-r t-0 r-0"></div>-->
        <!--<div class="bor b-b-l b-0 l-0"></div>-->
        <!--<div class="bor b-b-r b-0 r-0"></div>-->

        <!--<div class="icon-wrapper">-->
        <!--<img src="./images/icon-carmera.png" alt="">-->
        <!--&lt;!&ndash;<p>点击上传</p>&ndash;&gt;-->
        <!--</div>-->
      </div>
      <p class="uploader-text">{{ Uploader.uploaderText }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'UploaderDefault',
  inject: ['Uploader']
}
</script>

<style lang="scss" scoped>
.default-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  padding: 10% 10% 20% 10%;
  box-sizing: border-box;
  .default-container {
    width: 100%;
    height: 100%;
    position: relative;
    padding: 10%;
    box-sizing: border-box;
    .upload-icon {
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 40px;
      color: #d6d6d6;
    }
    .bor {
      position: absolute;
      border-style: solid;
      border-color: #6299db;
      width: 10%;
      height: 10%;
      &.t-0 {
        top: 0;
      }
      &.b-0 {
        bottom: 0;
      }
      &.l-0 {
        left: 0;
      }
      &.r-0 {
        right: 0;
      }
      &.b-t-l {
        border-width: 2px 0 0 2px;
      }
      &.b-t-r {
        border-width: 2px 2px 0 0;
      }
      &.b-b-l {
        border-width: 0 0 2px 2px;
      }
      &.b-b-r {
        border-width: 0 2px 2px 0;
      }
    }
    .icon-wrapper {
      width: 100%;
      height: 100%;
      background-color: #fff;
      border-radius: 8px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        height: 50%;
        width: 50%;
      }
    }
  }
  .uploader-text {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    margin-top: 4%;
    line-height: 18px !important;
    color: #b6b6b6;
  }
}
</style>
