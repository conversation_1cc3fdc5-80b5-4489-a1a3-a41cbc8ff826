<template>
  <div
    class="files-list-wrapper"
    :class="preview ? '' : 'flex'"
    :style="{ marginTop: onlyForView && '0px' }"
  >
    <div class="file m-r-6" v-for="file in _formateFiles" :key="file.id">
      <list
        :file="file"
        :files="_formateFiles"
        :onlyForView="onlyForView"
        :preview="preview"
      />
    </div>
  </div>
</template>

<script>
import List from './list.vue'
import formatterFile from './type'

export default {
  name: 'FilesList',
  components: { List },
  props: {
    files: {
      type: Array,
      default: () => []
    },
    from: {
      type: String,
      default: ''
    },
    onlyForView: {
      type: Boolean,
      default: false
    },
    preview: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    _formateFiles() {
      if (this.from && this.from === 'uploader') {
        return this.files
      } else {
        return this.files.map(file => {
          if (typeof file === 'object') {
            return formatterFile(file)
          } else {
            return {}
          }
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.files-list-wrapper {
  margin-top: 10px;
  width: 100%;
  .file {
    margin-bottom: 16px;
    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
