<template>
  <div
    class="mask-wrapper"
    @mouseenter="isMask = true"
    @mouseleave="isMask = false"
  >
    <img :src="file.filePath" alt="" />

    <transition name="fade">
      <div
        v-if="
          (Uploader.showPreview ||
            Uploader.showReupload ||
            Uploader.showDelete) &&
          isMask &&
          Uploader.showMask
        "
        class="mask"
        :class="'has-' + maskChildLength"
      >
        <el-link
          v-if="Uploader.showPreview"
          type="primary"
          :underline="false"
          @click.stop="priviewFile(file)"
          >预览文件</el-link
        >
        <!--        <el-link-->
        <!--          v-if="Uploader.showReupload && !Uploader.onlyForView"-->
        <!--          type="warning"-->
        <!--          :underline="false"-->
        <!--          @click="reuploadFile"-->
        <!--          >重新上传</el-link-->
        <!--        >-->
        <el-link
          v-if="Uploader.showDelete && !Uploader.onlyForView"
          type="danger"
          :underline="false"
          @click.stop="deleteFile(file)"
          >删除文件</el-link
        >
      </div>
    </transition>

    <img-viewer ref="viewer" />
  </div>
</template>

<script>
import ImgViewer from '@/components/ImgViewer'
import operateMixin from './operate'

export default {
  name: 'UploaderMask',
  inject: ['Uploader'],
  mixins: [operateMixin],
  components: { ImgViewer },
  props: {
    file: {
      required: true,
      type: Object
    },
    index: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      isMask: false,
      maskClass: ''
    }
  },
  computed: {
    maskChildLength() {
      let i = 0
      this.Uploader.showPreview && i++
      if (!this.Uploader.onlyForView) {
        this.Uploader.showReupload && i++
        this.Uploader.showDelete && i++
      }
      return i
    },
    files() {
      return this.Uploader.files
    }
  },
  methods: {
    // 重新上传
    reuploadFile() {
      if (this.Uploader.mulity) {
        if (this.index > -1) {
          this.Uploader.reuploadFile(this.index)
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mask-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  img {
    width: 100%;
    height: 100%;
  }
  .mask {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.8);
    .el-link {
      width: 100%;
      flex: 1;
      font-size: 14px;
      text-decoration: none;
      display: flex;
      justify-content: center;
      box-sizing: border-box;
    }
    &.has-2 {
      .el-link {
        &:nth-child(1) {
          padding-bottom: 5px;
          align-items: flex-end;
        }
        &:nth-child(2) {
          padding-top: 5px;
          align-items: flex-start;
        }
      }
    }
    &.has-3 {
      .el-link {
        &:nth-child(1) {
          padding-bottom: 5px;
          align-items: flex-end;
        }
        &:nth-child(3) {
          padding-top: 5px;
          align-items: flex-start;
        }
      }
    }
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
