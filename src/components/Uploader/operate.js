// import { deleteAttach } from '@/api/common'

export default {
  methods: {
    // 预览图片
    priviewFile(file) {
      const { path } = file
      if (file.type === 'pic') {
        const otherPic = this.files
          .filter(file => file.type === 'pic' && file.path !== path)
          .map(file => file.path)
        const pics = [path].concat(otherPic)
        this.$refs.viewer.show(pics)
      } else {
        window.open(path)
      }
    },
    // 删除图片
    deleteFile(file) {
      this.$confirm('附件删除后将不可恢复，是否继续？').then(() => {
        // 删除有问题这里前端做假删除，由后端处理
        // return deleteAttach(file.id).then(() => {
        this.Uploader.deleteSingleFile(file.id)
        // })
      })
    }
  }
}
