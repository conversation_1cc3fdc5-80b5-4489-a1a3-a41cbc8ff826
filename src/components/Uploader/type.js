/*
 * @Author: your name
 * @Date: 2021-07-22 10:31:04
 * @LastEditTime: 2022-05-08 18:45:41
 * @LastEditors: manyellipses <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \uranus-park-web\src\components\Uploader\type.js
 */
// 图片信息
import DocPic from './images/file-type-icon-doc.png'
import NonePic from './images/file-type-icon-none.png'
import PdfPic from './images/file-type-icon-pdf.png'
import PptPic from './images/file-type-icon-ppt.png'
import XlsPic from './images/file-type-icon-xls.png'
import ZipPic from './images/file-type-icon-zip.png'
import OfdPic from './images/file-type-icon-ofd.png'

// 文件类型映射表
const fileTypeMap = {
  ofd: ['ofd', 'OFD'],
  doc: ['doc', 'docx'],
  xls: ['xls', 'xlsx'],
  ppt: ['ppt', 'ppts'],
  pdf: ['pdf', 'PDF'],
  pic: ['jpg', 'jpeg', 'png', 'bmp', 'tif', 'gif', 'webp'],
  zip: ['zip', 'rar', 'tar', '7z', 'cab', 'iso']
}

// 类型图片地址
const filePicMap = {
  doc: DocPic,
  xls: XlsPic,
  ppt: PptPic,
  pdf: PdfPic,
  zip: ZipPic,
  none: NonePic,
  ofd: OfdPic
}

// 截取文件后缀
function cilpType(filePath) {
  const pathArr = filePath.split('.')
  return pathArr[pathArr.length - 1]
}

// 大写转小写
function convertToLowercase(str) {
  return str.toUpperCase().toLowerCase()
}

// 文件类型对应关系
function typeMap(fileType) {
  for (let k in fileTypeMap) {
    if (fileTypeMap[k].indexOf(fileType) > -1) {
      return k
    }
  }
  return 'none'
}

// 格式化文件大小
function renderSize(value) {
  if (!value) return '0b'
  const unitArr = ['b', 'Kb', 'Mb', 'Gb', 'Tb', 'Pb', 'Eb', 'Zb', 'Yb']
  let index = 0
  const srcsize = parseFloat(value)
  index = Math.floor(Math.log(srcsize) / Math.log(1024))
  let size = srcsize / Math.pow(1024, index)
  size = size.toFixed(2)
  return size + unitArr[index]
}

// 格式化文件数据
function formatterFile(fileResponse) {
  const prefix = cilpType(fileResponse.extentionName)
  const str = convertToLowercase(prefix)
  const type = typeMap(str)
  if (typeof fileResponse.size === 'number') {
    const size = renderSize(fileResponse.size)
    fileResponse.size = size
  }
  fileResponse.type = type
  if (type === 'pic') {
    fileResponse.filePath = fileResponse.path
  } else {
    fileResponse.filePath = filePicMap[type]
  }
  return fileResponse
}

export default formatterFile
