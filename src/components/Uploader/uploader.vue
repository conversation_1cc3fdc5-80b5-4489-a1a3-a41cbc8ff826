<template>
  <el-upload
    ref="uploader"
    class="uploader-area"
    :style="Uploader._isAvatarType ? Uploader._genWrapperSize : ''"
    :action="Uploader.uploadUrl"
    :headers="Uploader.headers"
    :data="Uploader._uploadData"
    :limit="Uploader.limit"
    :before-upload="beforeUpload"
    :http-request="uploadFiles"
    :on-exceed="uploadExceed"
    :multiple="Uploader.mulity"
    :file-list="Uploader.files"
    :show-file-list="false"
    :accept="Uploader.accept"
    :disabled="Uploader.disabled"
  >
    <template v-if="Uploader._isButtonType">
      <el-button :type="Uploader.uploaderType">
        {{ Uploader.uploaderText }}
      </el-button>
    </template>
    <template v-if="Uploader._isAvatarType">
      <u-default class="defalut-bg" />
      <slot />
    </template>
    <template v-if="Uploader._isCustomType">
      <slot />
    </template>
  </el-upload>
</template>

<script>
import UDefault from './default'
import { cosUploadComplete, getCosGenKey } from '@/api/common'
import COS from 'cos-js-sdk-v5'
import { cosConfig } from '@/settings'
let Loading

export default {
  name: 'UploaderArea',
  inject: ['Uploader'],
  components: { UDefault },
  data() {
    return {
      cosFileInfo: []
    }
  },
  methods: {
    loading() {
      Loading = this.$loading({
        lock: true,
        text: '正在上传...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.6)'
      })
    },
    // 关闭loading
    closeLoading() {
      Loading && Loading.close()
    },
    // 初始化腾讯云对象
    async initCOS(file) {
      try {
        const params = {
          fileName: file.name,
          ...this.Uploader.uploadData
        }
        const res = await getCosGenKey(params)
        const cos = new COS({
          getAuthorization: async (options, callback) => {
            try {
              callback({
                TmpSecretId: res.tmpSecretId,
                TmpSecretKey: res.tmpSecretKey,
                SecurityToken: res.sessionToken,
                StartTime: res.startTime,
                ExpiredTime: res.expiredTime,
                ScopeLimit: true
              })
            } catch (error) {
              console.error('Error fetching temporary credentials:', error)
            }
          }
        })
        this.cosFileInfo.push({
          file,
          uid: file.uid,
          cos,
          ...res
        })
      } catch (e) {
        console.error(e)
        this.closeLoading()
      }
    },
    // 自定义接口上传
    async uploadFiles({ file }) {
      if (this._checkFileLength() && this._checkFileSize(file)) {
        this.loading()
        this.Uploader._uploadData.name = file.name
        const cosFileItemInit = this.cosFileInfo.find(
          item => item.uid === file.uid
        )
        if (!cosFileItemInit || !cosFileItemInit.cos) await this.initCOS(file)
        const cosFileItem = this.cosFileInfo.find(item => item.uid === file.uid)
        cosFileItem.cos.uploadFile(
          {
            ...cosConfig,
            Key: cosFileItem.objectName,
            Body: file // 上传文件对象
          },
          (err, data) => {
            this.closeLoading()
            console.log(data, 'data')
            if (err) {
              console.log('上传失败', err)
              this.$refs.uploader.clearFiles()
              return this.$message({
                message: '上传失败',
                type: 'error'
              })
            } else {
              const params = {
                ...this.Uploader.uploadData,
                fileName: file.name,
                objectName: cosFileItem.objectName,
                size: file.size
              }
              cosUploadComplete(params).then(res => {
                const fileList = this.cosFileInfo.map(item => item.file)
                this.Uploader.uploadSuccess(res, file, fileList)
              })
            }
          }
        )
      }
    },
    // 参数携带文件名称
    beforeUpload(file) {
      return this._checkFileLength() && this._checkFileSize(file)
    },
    // 文件超出个数限制
    uploadExceed() {
      const { limit } = this.Uploader
      this.$message({
        message: `文件上传数量超过限制${limit}个`,
        type: 'error'
      })
    },
    // 校验最大上传文件数
    _checkFileLength() {
      const { limit, files = [] } = this.Uploader
      if (limit) {
        if (limit <= files.length) {
          this.$message({
            message: `文件上传数量超过限制${limit}个`,
            type: 'error'
          })
          return false
        }
        return true
      }
      return true
    },
    // 校验最大上传文件尺寸
    _checkFileSize(file) {
      const maxSize = this.Uploader.maxSize
      if (maxSize) {
        if (file.size > maxSize * 1024 * 1024) {
          this.$message({
            message: `文件大小超过限制${maxSize}M`,
            type: 'error'
          })
          return false
        }
        return true
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader-area {
  position: relative;
  .defalut-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}
:deep(.el-upload) {
  width: 100%;
  height: 100%;
}
</style>
