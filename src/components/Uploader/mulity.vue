<template>
  <div class="mulity-wrapper">
    <div v-if="files && files.length > 0" class="mulity-wrapper">
      <u-uploader
        v-for="(file, index) in files"
        :key="file.attachId"
        :style="Uploader._genWrapperSize"
      >
        <div class="uploader-wrapper" :style="Uploader._genWrapperSize">
          <el-tooltip
            v-if="Uploader.onlyForView"
            effect="dark"
            :content="file.name"
            placement="top"
          >
            <u-mask :file="file" :index="index" />
          </el-tooltip>
          <u-mask v-else :file="file" :index="index" />
        </div>
      </u-uploader>
    </div>
    <u-uploader v-if="!Uploader.onlyForView" />
  </div>
</template>
<script>
import UUploader from './uploader'
import UMask from './mask'

export default {
  name: 'UploaderMulity',
  components: { UUploader, UMask },
  inject: ['Uploader'],
  props: {
    files: {
      type: Array,
      default: () => []
    }
  },
  computed: {}
}
</script>

<style lang="scss" scoped>
.mulity-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .uploader-wrapper,
  :deep(.uploader-area) {
    margin: 0 10px 10px 0;
  }
}
</style>
