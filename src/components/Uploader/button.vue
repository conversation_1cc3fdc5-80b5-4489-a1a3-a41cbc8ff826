<template>
  <div>
    <!-- 上传区域 -->
    <div v-if="!Uploader.onlyForView" class="uploader-button">
      <u-uploader>
        <slot />
      </u-uploader>
    </div>
    <!-- 文件列表区域 -->
    <div class="files-list" v-if="!Uploader.onlyForUpload">
      <files-list :files="Uploader.files" from="uploader" />
    </div>
  </div>
</template>

<script>
import UUploader from './uploader'
import FilesList from './files'

export default {
  name: 'UploaderButton',
  inject: ['Uploader'],
  components: {
    UUploader,
    FilesList
  }
}
</script>

<style lang="scss" scoped>
.uploader-button {
  display: flex;
  justify-content: flex-start;
}
</style>
