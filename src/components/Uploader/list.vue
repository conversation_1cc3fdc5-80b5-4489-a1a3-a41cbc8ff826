<template>
  <div class="file-list" @click="priviewFile(file)">
    <div class="img" :style="isPic ? '' : 'padding: 4px;'">
      <img :src="file.filePath" alt="" />
      <div class="cover">
        <svg-icon v-if="isPic" icon-class="browse" />
        <svg-icon v-else icon-class="download" />
      </div>
    </div>
    <div class="info" v-if="preview">
      <div class="name">
        <span class="text" :style="textMaxWidth">
          {{ _transformFileName(file.name) }}
        </span>
        <span ref="extRef" class="ext">{{ file.extentionName }}</span>
      </div>
      <div class="size-operate font-size-12 line-height-20">
        <span class="color-text-secondary">文件大小: {{ file.size }}</span>
        <div class="operate">
          <!-- <span>
            <el-link
              type="warning"
              class="font-size-12 line-height-20"
              :underline="false"
            >
              重新上传
            </el-link>
          </span> -->
          <span>
            <el-link
              v-if="showDelete"
              type="danger"
              class="font-size-12 line-height-20"
              :underline="false"
              @click.stop="deleteFile(file)"
            >
              删除
            </el-link>
          </span>
        </div>
      </div>
    </div>

    <img-viewer ref="viewer" />
  </div>
</template>

<script>
import ImgViewer from '@/components/ImgViewer'
import operateMixin from './operate'

export default {
  name: 'List',
  mixins: [operateMixin],
  components: { ImgViewer },
  props: {
    file: {
      type: Object,
      default: () => ({})
    },
    onlyForView: {
      type: Boolean,
      default: false
    },
    files: {
      type: Array,
      default: () => []
    },
    preview: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      extWidth: '',
      Uploader: null
    }
  },
  computed: {
    showDelete() {
      if (this.Uploader) {
        return this.Uploader.showDelete && !this.Uploader.onlyForView
      }
      return !this.onlyForView
    },
    isPic() {
      return this.file.type === 'pic'
    },
    textMaxWidth() {
      const extWidth = this.extWidth
      return {
        maxWidth: `calc(100% - ${extWidth})`
      }
    }
  },
  methods: {
    _transformFileName(name) {
      return name.substr(0, name.lastIndexOf('.'))
    },
    _findUploader() {
      let parent = this.$parent
      let name = parent.$options.name
      while (parent && (!name || name !== 'Uploader')) {
        parent = parent.$parent
        if (parent) {
          name = parent.$options.name
        }
      }
      return parent
    }
  },
  mounted() {
    this.Uploader = this._findUploader()
    this.$nextTick(() => {
      this.extWidth = this.$refs?.extRef?.clientWidth + 'px'
    })
  }
}
</script>

<style lang="scss" scoped>
.file-list {
  display: flex;
  align-items: center;
  cursor: pointer;
  .img {
    transition: all 0.3s;
    position: relative;
    flex: 0 0 42px;
    width: 42px;
    height: 42px;
    border-radius: 3px;
    overflow: hidden;
    border-width: 1px;
    border-style: solid;
    @include border_color(--border-color-light);
    img {
      width: 100%;
      height: 100%;
    }
    .cover {
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      bottom: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(255, 255, 255, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      .svg-icon {
        width: 16px;
        height: 16px;
        @include font_color(--color-primary);
      }
    }
  }
  .info {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    height: 100%;
    padding-left: 8px;
    width: 0;
    .name {
      font-size: 14px;
      line-height: 22px;
      width: 100%;
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      .text {
        white-space: nowrap;
        overflow: hidden;
        display: inline-block;
        text-overflow: ellipsis;
      }
      .ext {
        white-space: nowrap;
      }
    }
    .size-operate {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      .operate {
        span {
          &:last-of-type {
            padding-left: 4px;
          }
        }
      }
    }
  }
  &:hover {
    .img {
      @include border_color(--color-primary);
      .cover {
        opacity: 1;
      }
    }
    .info {
      .name {
        @include font_color(--color-primary);
      }
    }
  }
}
</style>
