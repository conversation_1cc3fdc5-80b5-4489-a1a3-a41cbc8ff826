<template>
  <u-uploader :style="Uploader._genWrapperSize">
    <div
      v-if="files && files.length > 0"
      class="uploader-wrapper"
      :style="Uploader._genWrapperSize"
    >
      <!--<img :src="files[0].filePath" alt="" :style="_genWrapperSize">-->
      <u-mask :file="files[0]" />
    </div>
  </u-uploader>
</template>

<script>
import UUploader from './uploader'
import UMask from './mask'

export default {
  name: 'UploaderSingle',
  inject: ['Uploader'],
  components: { UUploader, UMask },
  props: {
    files: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped></style>
