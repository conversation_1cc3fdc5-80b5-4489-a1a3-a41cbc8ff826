<template>
  <div class="uploader-container">
    <template v-if="_isButtonType">
      <u-button />
    </template>
    <template v-if="_isAvatarType">
      <div class="avatar-wrapper">
        <!--多文件展示区域-->
        <u-mulity v-if="mulity" :files="files" />
        <!--单文件区域-->
        <u-single v-else :files="files" />
      </div>
    </template>
    <template v-if="_isCustomType">
      <u-button>
        <slot />
      </u-button>
    </template>
  </div>
</template>

<script>
import { shardUploadUrl } from '@/api/common'
import { getBaseHeader } from '@/utils/request'

import UButton from './button'
import USingle from './single'
import UMulity from './mulity'

import formatterFile from './type'

export default {
  name: 'Uploader',
  components: {
    UButton,
    USingle,
    UMulity
  },
  provide() {
    return {
      Uploader: this
    }
  },
  props: {
    // v-model 支持 初始值
    value: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 上传组件类型
    type: {
      type: String,
      default: 'button' // button avatar
    },
    // 上传容器宽度
    width: {
      type: [String, Number],
      default: 120
    },
    // 上传容器高度
    height: {
      type: [String, Number],
      default: 120
    },
    // 上传地址
    uploadUrl: {
      type: String,
      default: shardUploadUrl
    },
    // 上传请求头
    headers: {
      type: Object,
      default: () => getBaseHeader()
    },
    // 上传的附带参数
    uploadData: {
      type: Object,
      default: () => ({})
    },
    //  是否多文件
    mulity: {
      type: Boolean,
      default: false
    },
    // 接收的文件类型
    accept: {
      type: String,
      default: [
        'application/pdf', // pdf
        'application/msword', // doc
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
        'application/vnd.ms-excel', // xls
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
        'application/vnd.ms-powerpoint', // ppt
        'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
        'image/*', // image
        'text/plain' //txt
      ].join(',')
    },
    // 禁用状态
    disabled: {
      type: Boolean,
      default: false
    },
    // 只为预览状态
    onlyForView: {
      type: Boolean,
      default: false
    },
    // 显示删除按钮
    showDelete: {
      type: Boolean,
      default: true
    },
    // 显示预览按钮
    showPreview: {
      type: Boolean,
      default: true
    },
    // 显示重新上传按钮
    showReupload: {
      type: Boolean,
      default: true
    },
    // 最大上传个数
    maxLength: {
      type: Number,
      default: 1
    },
    // 最大上传大小，单位MB
    maxSize: {
      type: Number,
      default: 30
    },
    // 是否显示图层
    showMask: {
      type: Boolean,
      default: true
    },
    // 上传组件文本
    uploaderText: {
      type: String,
      default: '点击上传'
    },
    // 上传组件type
    uploaderType: {
      type: String,
      default: 'default'
    },
    // 上传限制选择的文件数
    limit: {
      type: Number,
      default: 1
    },
    // 只为上传状态
    onlyForUpload: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 是否支持多选文件
      multiple: false,
      files: [],
      temp: '',
      index: null
    }
  },
  computed: {
    _isButtonType() {
      return this.type === 'button'
    },
    _isAvatarType() {
      return this.type === 'avatar'
    },
    _isCustomType() {
      return this.type === 'custom'
    },
    _uploadData() {
      return this.uploadData
    },
    // 生成容器大小内联样式
    _genWrapperSize() {
      return {
        width: typeof this.width === 'number' ? `${this.width}px` : this.width,
        height:
          typeof this.height === 'number' ? `${this.height}px` : this.height
      }
    }
  },
  watch: {
    // 当多图上传是，limit修改为9
    mulity: {
      handler(val) {
        if (val) {
          // 多传有问题，较复杂，todo
          this.multiple = false
        }
      },
      immediate: true
    },
    // 上传派发文件
    files: {
      handler(val) {
        if (val) {
          // v-model 支持
          this.$emit('input', val)
          this.temp = JSON.stringify(
            val.map(item => {
              return formatterFile(item)
            })
          )
        }
      },
      deep: true
    },
    // 监听传入数据变化
    value: {
      handler(val) {
        if (val && val.length > 0) {
          const valFormatrer = val.map(item => {
            return formatterFile(item)
          })
          const str = JSON.stringify(valFormatrer)
          if (this.temp !== str) {
            this.files = valFormatrer
          }
          this.temp = str
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    // 上传成功回调
    uploadSuccess(response) {
      const fileObj = formatterFile(response)
      this.mulity
        ? this._genMulityUploaderSuccess(fileObj)
        : this._genSingleUploaderSuccess(fileObj)
    },
    // 单张图片上传逻辑
    _genSingleUploaderSuccess(file) {
      this.files = [file]
      this.$emit('success', this.files)
    },
    // 多图重新上传index
    reuploadFile(index) {
      this.index = index
    },
    // 多图上传逻辑
    _genMulityUploaderSuccess(file) {
      if (this.index === null) {
        this.files = (this.files || []).concat([file])
      } else {
        this.$set(this.files, this.index, file)
      }
      this.index = null
      this.$emit('success', this.files)
    },
    // 清除图片
    clearFiles() {
      this.files = null
    },
    // 删除单个图片
    deleteSingleFile(id) {
      const index = this.files.findIndex(file => file.id === id)
      const deleteFiles = this.files.splice(index, 1)
      this.$emit('delete', deleteFiles)
    }
  }
}
</script>

<style lang="scss" scoped>
.uploader-container {
  width: 100%;
}

.avatar-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

:deep(.uploader-wrapper) {
  border: 1px dashed #ddd;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
