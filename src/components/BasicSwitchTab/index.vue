<template>
  <div class="switch-tab-container">
    <div class="switch-tab-wrapper">
      <div :style="moveBgcStyle" class="move-bgc"></div>
      <div
        class="item-btn"
        v-for="(item, i) in tabs"
        :key="i"
        :style="dynamicWidthStyle"
        @click="changeTab(i, item.value)"
        :class="{ active: i === active }"
        v-permission="routeButtonsPermission[item.permission]"
      >
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicSwitchTab',
  props: {
    width: {
      type: String,
      default: ''
    },
    current: {
      default: 1
    },
    tabs: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      active: 0,
      moveBgcWidth: 0,
      moveBgcTranslateX: 0
    }
  },
  computed: {
    dynamicWidthStyle() {
      return {
        width: this.width + 'px'
      }
    },
    moveBgcStyle() {
      return {
        width: this.moveBgcWidth + 'px',
        transform: `translateX(${this.moveBgcTranslateX}px)`
      }
    }
  },
  watch: {
    tabs: {
      immediate: true,
      handler() {
        this.updateMoveBgc()
      },
      deep: true
    }
  },
  methods: {
    updateMoveBgc() {
      this.$nextTick(() => {
        const firstTab = this.$el.querySelector('.item-btn')
        if (firstTab) {
          this.moveBgcWidth = parseFloat(
            window.getComputedStyle(firstTab).width
          )
        }
      })
    },
    changeTab(index, value) {
      if (this.disabled) return

      const nodeList = this.$el.querySelectorAll('.item-btn')
      let allWidth = 0

      for (let i = 0; i < nodeList.length; i++) {
        if (i <= index) {
          allWidth += parseFloat(window.getComputedStyle(nodeList[i]).width)
        }
      }

      this.active = index
      this.moveBgcWidth = parseFloat(
        window.getComputedStyle(nodeList[index]).width
      )
      this.moveBgcTranslateX = allWidth - this.moveBgcWidth
      this.$emit('change', value)
    }
  },
  mounted() {
    this.updateMoveBgc()
  }
}
</script>

<style lang="scss" scoped>
.switch-tab-container {
  .switch-tab-wrapper {
    width: fit-content;
    height: 32px;
    background: #e7e7e7;
    border-radius: 3px;
    padding: 2px;
    font-size: 14px;
    display: flex;
    position: relative;
    .item-btn {
      padding: 0 8px;
      height: 100%;
      line-height: 28px;
      border-radius: 3px;
      text-align: center;
      cursor: pointer;
      z-index: 99;
      color: #000;
      font-weight: 350;
      &.active {
        color: #fff;
      }
    }
    .move-bgc {
      position: absolute;
      left: 2px;
      top: 2px;
      width: 71px;
      height: 87%;
      background: #ed7b2f;
      @include background_color(--color-primary);
      border-radius: 3px;
      //过渡
      transition: all 0.3s;
      transform: translateX(0%);
    }
  }
}
</style>
