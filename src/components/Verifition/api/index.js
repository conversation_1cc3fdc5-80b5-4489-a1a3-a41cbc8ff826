/*
 * @Author: manyellipses <EMAIL>
 * @Date: 2022-06-09 09:05:45
 * @LastEditors: manyellipses <EMAIL>
 * @LastEditTime: 2022-06-09 10:39:50
 * @FilePath: \zacg-manage\src\components\Verifition\api\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

//获取验证图片
export function reqGet(data) {
  return request({
    url: '/system/captcha/captchaImage',
    method: 'post',
    data,
    hideLoading: true
  })
}

//滑动或者点选验证
export function reqCheck(data) {
  return request({
    url: '/system/captcha/check',
    method: 'post',
    data,
    hideLoading: true
  })
}
