<template>
  <div class="flow-form-wrapper">
    <div v-if="!isCustom">
      <el-button v-if="currentTask" type="primary" @click="examineEvent" :disabled="btnDisabled">
        <svg-icon icon-class="attach" />
        <span>{{ buttonText }}</span>
      </el-button>
      <el-button v-if="!currentTask" @click="openHistory" type="primary" :disabled="btnDisabled">
        <svg-icon icon-class="attach" />
        <span>{{ recordText }}</span>
      </el-button>
    </div>

    <div @click="examineEvent" v-else>
      <slot />
    </div>

    <dialog-cmp
      :title="dialogTitle"
      :visible.sync="visible"
      width="720px"
      :haveOperation="false"
      :isShowBtns="false"
      custom-class="no-footer-dialog"
    >
      <div class="dialog-container flex">
        <div
          v-if="currentTask"
          class="dialog-left flex flex-direction-column justify-content-between"
        >
          <div>
            <work-flow-field
              ref="work-flow-field"
              :worKFlowField="worKFlowField"
              v-model="formModel"
            />
          </div>
          <div class="flex align-items-center justify-content-end p-b-24">
            <el-button @click="visible = false">取消</el-button>
            <el-button type="primary" @click="confirmDialog">
              <span>确定</span>
            </el-button>
          </div>
        </div>
        <div class="dialog-right">
          <work-flow-record :historyList="recordList" />
        </div>
      </div>
    </dialog-cmp>
  </div>
</template>

<script>
import WorkFlowField from './form/index.vue'
import WorkFlowRecord from './record/index.vue'
import DialogCmp from '@/components/BasicDialog/index.vue'

import {
  getCurrentTask,
  getWorkFlowFields,
  findHistoryTask,
  findBackTask,
  examineFlow
} from './api'

export default {
  name: 'InvoiceForm',
  components: {
    WorkFlowField,
    WorkFlowRecord,
    DialogCmp
  },
  provide() {
    return {
      dataSource: this
    }
  },
  props: {
    propsOrderId: {
      type: String,
      default: ''
    },
    buttonText: {
      type: String,
      default: '审批'
    },
    isCustom: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    recordText: {
      type: String,
      default: '审核记录'
    },
    additionalData: {
      type: Array,
      default: () => []
    },
    isCustomFunction: {
      type: Boolean,
      default: false
    },
    isFlushed: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      invoiceId: '',
      currentTask: null,
      worKFlowField: [], // 流程表单
      formModel: {},
      historyList: [], // 历史任务
      backList: [], // 退回节点
      visible: false,
      btnDisabled: true
    }
  },
  mounted() {
    const { invoiceId } = this.$route.query
    this.invoiceId = invoiceId || this.propsOrderId
    this.getCurrentTask()
    this.findHistoryTask()
    this.findBackTask()
  },
  computed: {
    dialogTitle() {
      const dialogText = this.currentTask ? '审核' : this.recordText
      return this.title + dialogText
    },
    // 合同操作记录合并
    recordList() {
      return [...this.historyList, ...this.additionalData]
    }
  },
  methods: {
    uploadHandle() {
      const { invoiceId } = this.$route.query
      this.invoiceId = invoiceId
      this.getCurrentTask()
      this.findHistoryTask()
      this.findBackTask()
    },
    getCurrentTask() {
      if(!this.invoiceId) return false
      getCurrentTask(this.invoiceId).then(res => {
        this.currentTask = res
        this.btnDisabled = false
      })
    },

    // 获取表单字段
    getWorkFlowFields() {
      if(!this.invoiceId) return false
      getWorkFlowFields(this.invoiceId).then(res => {
        this.worKFlowField = res.map(item => {
          return {
            field: item.fieldName,
            type: JSON.parse(item.fieldAttr)
          }
        })
      })
    },

    // 审核弹框
    examineEvent() {
      if (this.isCustomFunction) {
        this.$emit('customFunction')
      } else {
        this.getWorkFlowFields()
        this.visible = true
      }
    },

    // 查看审核日志
    openHistory() {
      this.visible = true
    },

    // 获取历史节点
    findHistoryTask() {
      if(!this.invoiceId) return false
      findHistoryTask(this.invoiceId).then(res => {
        this.historyList = res
      })
    },

    // 获取退回节点
    findBackTask() {
      if(!this.invoiceId) return false
      findBackTask(this.invoiceId).then(res => {
        this.backList = res
      })
    },

    // 提交
    confirmDialog() {
      this.$refs['work-flow-field'].$refs['workflow-form'].validate(valid => {
        if (valid) {
          let attachIds = []
          if (this.formModel.file && this.formModel.file.length > 0) {
            attachIds = this.formModel.file.map(item => {
              return item.id
            })
          }
          const params = {
            ...this.formModel,
            orderId: this.invoiceId,
            attachIds
          }
          this.$confirm('确认提交审核意见？').then(() => {
            examineFlow(params).then(() => {
              this.$toast.success('审核成功')
              this.visible = false
              if (this.isFlushed) {
                this.$router.go(-1)
              } else {
                this.getCurrentTask()
                this.findHistoryTask()
                this.findBackTask()
                this.$emit('reset')
              }
            })
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.flow-form-wrapper {
  display: inline-block;
  overflow-y: auto;
}

.dialog-container {
  height: 380px;

  .dialog-left {
    flex: 1;
    padding-right: 16px;
    border-right: 1px solid;
    border-color: #e7e7e7;
  }

  .dialog-right {
    flex: 1;
  }
}
</style>
