<template>
  <div>
    <el-select v-model="_value" placeholder="请选择退回节点">
      <el-option
        v-for="item in dataSource.backList"
        :key="item.taskId"
        :label="item.displayName"
        :value="item.taskId"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: 'SelectCmp',
  inject: ['dataSource'],
  props: {
    value: {
      required: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$forceUpdate()
        this.$emit('input', val)
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
