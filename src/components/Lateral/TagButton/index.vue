<template>
  <div class="tag-button">
    <div class="tag">
      <el-tag :type="type" :style="tagPadding">{{ tag }}</el-tag>
    </div>
    <div class="button" ref="Button" v-if="!hideButton">
      <el-popover
        v-if="!disabled"
        placement="bottom"
        width="200"
        trigger="hover"
        @show="popoverShowHandle"
        @after-leave="popoverHideHandle"
      >
        <div style="width: 174px; height: 174px" v-loading="loading">
          <img class="w100 h100" :src="genCode" alt="" />
        </div>
        <el-button
          slot="reference"
          :type="type"
          size="mini"
          :disabled="disabled"
        >
          {{ buttonName }}
        </el-button>
      </el-popover>
      <el-button
        v-else
        :type="type"
        size="mini"
        :disabled="disabled"
        @click.stop="clickButton"
      >
        {{ buttonName }}
      </el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TagButton',
  props: {
    type: {
      type: String,
      default: 'primary'
    },
    tag: {
      type: String,
      default: ''
    },
    buttonName: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    },
    hideButton: {
      type: Boolean,
      default: false
    },
    genCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      buttonWidth: 0
    }
  },
  computed: {
    tagPadding() {
      return {
        paddingRight: this.buttonWidth + 8 + 'px'
      }
    },
    loading() {
      return !this.genCode
    }
  },
  methods: {
    popoverHideHandle() {
      this.$emit('popoverHideHandle')
    },
    popoverShowHandle() {
      this.$emit('popoverShowHandle')
    },
    clickButton() {
      this.$emit('tapButton')
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.buttonWidth = !this.hideButton ? this.$refs.Button.clientWidth : 0
    })
  }
}
</script>

<style lang="scss" scoped>
.tag-button {
  position: relative;
  .tag {
    :deep(.el-tag) {
      border: none;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 100%;
      &.el-tag--danger {
        @include font_color_mix(--color-danger, #ffffff, 30%);
      }
    }
  }
  .button {
    position: absolute;
    display: inline-block;
    right: 0;
    top: 0;
  }
}
:deep(.el-loading-mask) {
  display: flex;
  align-items: center;
  justify-content: center;
  .el-loading-spinner {
    position: initial;
    margin: 0 !important;
  }
}
</style>
