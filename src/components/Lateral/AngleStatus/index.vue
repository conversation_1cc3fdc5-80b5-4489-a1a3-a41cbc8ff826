<template>
  <div class="angle-status" :class="type">
    <span class="font-size-12 line-height-20 color-white" :class="'bg-' + type">
      {{ text }}
    </span>
    <div class="angle"></div>
  </div>
</template>

<script>
export default {
  name: 'AngleStatus',
  props: {
    type: {
      type: String,
      default: 'danger'
    },
    text: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.angle-status {
  position: relative;
  display: inline-block;
  span {
    position: relative;
    z-index: 2;
    padding: 4px 8px;
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
  }
  .angle {
    position: absolute;
    z-index: 1;
    width: 0;
    height: 0;
    left: -4px;
    top: 17px;
    border-top: 4px solid transparent !important;
    border-bottom: 4px solid transparent !important;
    border-left: 4px solid transparent !important;
    border-right-width: 4px;
    border-right-style: solid;
  }
  &.primary {
    .angle {
      @include border_color_mix(--color-primary, #000000, 20%);
    }
  }
  &.success {
    .angle {
      @include border_color_mix(--color-success, #000000, 20%);
    }
  }
  &.danger {
    .angle {
      @include border_color_mix(--color-danger, #000000, 20%);
    }
  }
  &.warning {
    .angle {
      @include border_color_mix(--color-warning, #000000, 20%);
    }
  }
  &.info {
    .angle {
      @include border_color_mix(--color-info, #000000, 20%);
    }
  }
}
</style>
