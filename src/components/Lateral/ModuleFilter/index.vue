<template>
  <div class="module-filter">
    <div
      v-show="filter.prop === 'courseModel' ? isShow : true"
      class="filter-list"
      v-for="(filter, idx) in formatFilters"
      :key="filter.prop + Math.random()"
    >
      <label style="height: 32px; line-height: 32px">{{ filter.label }}</label>
      <div class="m-b-8" :class="{ 'list-content': !filter.isRotate }">
        <div :id="'list' + filter.prop" class="list">
          <span
            v-for="(list, index) in filter.list"
            :key="list.dictType + index + Math.random()"
            :class="{
              active:
                current[filter.prop] ===
                (list.isAll ? '' : !flag ? list.dictType : list.label)
            }"
            @click="
              setFilterCurrent(
                filter.prop,
                list.label,
                list.isAll,
                list.dictType
              )
            "
          >
            {{ list.label }}
          </span>
        </div>
      </div>
      <div
        v-if="filter.isFold"
        class="arrowhead pointer"
        :class="{ 'arrowhead-rotate': filter.isRotate }"
        @click="handoff(idx, filter.isRotate)"
      >
        <svg-icon icon-class="chevron-down" class="font-size-21" />
      </div>
    </div>
  </div>
</template>

<script>
const all = { label: '全部', value: '', isAll: true }

export default {
  name: 'ModuleFilter',
  props: {
    filters: {
      type: Array,
      default: () => []
    },
    value: {
      type: Object,
      default: () => ({})
    },
    flag: {
      type: Boolean,
      default: true
    },
    isShow: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      formatFilters: [],
      current: {}
    }
  },
  methods: {
    async foldHandle() {
      await this.$nextTick()
      this.formatFilters.forEach((item, index) => {
        const id = `#list${item.prop}`
        const filterDom = document.querySelector(id)
        const hei = filterDom && filterDom.clientHeight
        this.$set(this.formatFilters, index, {
          ...this.formatFilters[index],
          isFold: hei > 44,
          isRotate: false
        })
      })
    },
    handoff(index, isRotate) {
      this.$set(this.formatFilters, index, {
        ...this.formatFilters[index],
        isRotate: !isRotate
      })
    },
    setFilterCurrent(prop, label, isAll, value) {
      if (this.disabled) return false
      const current = {
        ...this.current,
        selfProp: prop
      }
      if (this.flag) {
        current[prop] = !isAll ? label : ''
      } else {
        current[prop] = !isAll ? value : ''
      }
      this.current = current
    },
    _value() {
      return this.value
    }
  },
  watch: {
    filters: {
      handler(value) {
        const filters = JSON.parse(JSON.stringify(value))
        const list = filters.filter(item => !item.hidden)
        this.formatFilters = list.map(filter => {
          filter.list.unshift(all)
          this.$set(
            this.current,
            filter.prop,
            this.value[filter.prop] || all.value
          )
          return {
            ...filter
          }
        })
        this.foldHandle()
      },
      deep: true,
      immediate: true
    },
    current: {
      handler(value) {
        this.$emit('input', value)
        this.$emit('change', value)
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
.module-filter {
  position: relative;
  padding-bottom: 16px;
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-lighter);
  .filter-list {
    @include font_color(--color-text-primary);
    display: flex;
    align-items: flex-start;
    margin-top: 8px;
    position: relative;
    &:first-of-type {
      margin-top: 0;
    }
    label {
      font-size: 14px;
      line-height: 24px;
      flex: 0 0 80px;
      text-align: left;
      @include font_color(--color-text-regular);
    }
    .list-content {
      height: 36px;
      overflow: hidden;
    }
    .list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      flex: 1;
      span {
        font-size: 14px;
        height: 32px;
        line-height: 32px;
        margin-right: 40px;
        cursor: pointer;
        display: inline-block;
        margin-bottom: 12px;
        padding: 0 16px;
        &.active {
          @include font_color(--color-primary);
          background: #f3f3f3;
          opacity: 1;
          border-radius: 128px;
        }
        &:hover {
          @include font_color(--color-primary);
        }
      }
    }
    .arrowhead {
      width: 32px;
      height: 32px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.1s linear;
      position: absolute;
      right: 0;
      top: 0;
      &:hover {
        background: #fff8f3;
        color: #ed7b2f;
      }
      &.arrowhead-rotate {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
