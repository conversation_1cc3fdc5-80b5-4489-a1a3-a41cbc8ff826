<template>
  <div class="module-header">
    <div class="lateral-wrapper">
      <div class="title" v-if="!hasSlot">
        <h2
          class="flex align-items-center font-size-20 line-height-28 font-strong color-text-primary"
        >
          {{ title }}
          <span
            class="font-size-12 line-height-22 color-text-regular"
            style="margin-left: 15px"
          >
            {{ desc }}
          </span>
          <slot name="title-right"></slot>
        </h2>
        <div class="header-select">
          <div class="select-option">
            <el-select
              class="select"
              v-model="dataFormat.acStatus"
              @change="changeTime"
              clearable
              size="mini"
              :popper-append-to-body="true"
              placeholder="确认状态"
            >
              <el-option
                v-for="item in checkStatus"
                :key="item.key"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>

            <el-select
              v-model="dataFormat.payStatus"
              size="mini"
              :popper-append-to-body="true"
              collapse-tags
              clearable
              style="margin-left: 20px"
              @change="changeTime"
              placeholder="到账状态"
            >
              <el-option
                v-for="item in reachStatus"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>

          <div class="select-data-picker">
            <el-date-picker
              v-model="dataFormat.timeSelect"
              type="daterange"
              start-placeholder="请选择开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              clearable
              @change="changeTime"
            >
            </el-date-picker>

            <el-popover placement="bottom" width="120" trigger="hover">
              <div v-if="registerUrl !== null">
                <div style="width: 120px; height: 120px" class="m-b-10">
                  <el-image :src="registerUrl"></el-image>
                </div>
                <div
                  class="font-size-12 color-text-secondary text-align-center"
                >
                  使用小程序仅需1分钟
                </div>
              </div>
              <div v-else>
                <empty-data description="暂无二维码"></empty-data>
              </div>
              <el-button slot="reference" type="primary" class="m-l-8"
                >登记交易记录</el-button
              >
            </el-popover>
          </div>
        </div>
      </div>
      <div class="w100" v-else>
        <slot />
      </div>
      <img
        width="360px"
        height="180px"
        :src="img"
        :style="imgOpacityStyle"
        alt=""
      />
    </div>
    <div class="module-header-bg" :class="type"></div>
  </div>
</template>

<script>
import { getTenant } from '@/utils/auth'
import { getRegisterPng } from '@/views/manage/financial/financial-basic/payment/financialInformation-basic/api'

export default {
  name: 'ModuleHeader',
  props: {
    type: {
      type: String,
      default: 'primary'
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    img: {
      type: String,
      default: ''
    },
    imgOpacity: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      hasSlot: false,
      checkStatus: [
        { label: '全部', value: -1 },
        { label: '未确认', value: 0 },
        { label: '已确认', value: 1 },
        { label: '未确认', value: 2 }
      ],
      reachStatus: [
        { label: '全部', value: -1 },
        { label: '未到账', value: 0 },
        { label: '已到账', value: 1 }
      ],
      dataFormat: {
        acStatus: '',
        payStatus: '',
        timeSelect: []
      },
      registerUrl: null
    }
  },
  computed: {
    imgOpacityStyle() {
      return {
        opacity: this.imgOpacity
      }
    }
  },
  created() {
    this.hasSlot = !!this.$slots.default
    this.registerPng()
  },
  methods: {
    changeTime() {
      this.$emit('getDataFormat', this.dataFormat)
    },
    async registerPng() {
      // 判断是否有租户信息
      const tenantId = getTenant()
      this.registerUrl = await getRegisterPng({ tenantId })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.el-date-editor) {
  display: flex;
  justify-content: center;
}

::v-deep(.el-range-editor--small .el-range__close-icon) {
  opacity: 1;
}

.module-header {
  height: 230px;
  position: relative;

  .lateral-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    .title {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-top: 80px;
      flex: 1;
      overflow: hidden;
      .header-select {
        display: flex;
        justify-content: space-around;
        align-items: center;
        background-color: #fff;
        height: 100px;
        width: 1196px;
        .select-option {
          margin-left: -11px;
        }
        .select-data-picker {
          display: flex;
          margin-left: 91px;
        }
      }

      h2 {
        margin-bottom: 8px;
      }
      p {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    img {
      position: absolute;
      right: 0;
      top: 0;
      width: 360px;
      height: 180px;
      z-index: -1;
    }
  }
  .module-header-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 1;
    &.primary {
      @include background_color_mix(--color-primary, #ffffff, 95%);
    }
    &.success {
      @include background_color_mix(--color-success, #ffffff, 95%);
    }
    &.warning {
      @include background_color_mix(--color-warning, #ffffff, 95%);
    }
    &.danger {
      @include background_color_mix(--color-danger, #ffffff, 95%);
    }
  }
}
</style>
