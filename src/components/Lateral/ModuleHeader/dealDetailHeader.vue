<template>
  <div class="module-header">
    <div class="lateral-wrapper">
      <div class="title" v-if="!hasSlot">
        <h2
          class="flex align-items-center font-size-20 line-height-28 font-strong color-text-primary"
        >
          {{ title }}
          <slot name="title-right"></slot>
        </h2>
        <div class="dealNO-buttom">
          <p
            class="flex align-items-center font-size-20 line-height-28 font-strong color-text-primary"
            style="display: inline"
          >
            {{ desc }}
            &emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;
          </p>
          <!--          v-if="acStatus === 0"-->
          <!--          <el-button type="info">撤回</el-button>-->
          <!--          <el-button type="primary">重新编辑</el-button>-->
        </div>
        <div>
          <span class="span-frist" style="color: rgba(0, 0, 0, 0.6)"
            >交易登记企业&nbsp;</span
          ><span> {{ entName }}&emsp;&emsp;&emsp;&emsp;</span>
          <span class="span-frist" style="color: rgba(0, 0, 0, 0.6)"
            >交易登记时间&nbsp;</span
          ><span>{{ createTime }}&emsp;&emsp;&emsp;&emsp;</span>
          <span class="span-frist" style="color: rgba(0, 0, 0, 0.6)"
            >交易登记人&nbsp;</span
          ><span>{{ userName }} </span>
        </div>
      </div>
      <div class="w100" v-else>
        <slot />
      </div>
      <img
        width="360px"
        height="180px"
        :src="img"
        :style="imgOpacityStyle"
        alt=""
      />
    </div>
    <div class="module-header-bg" :class="type"></div>
  </div>
</template>

<script>
export default {
  name: 'ModuleHeader',
  props: {
    type: {
      type: String,
      default: 'primary'
    },
    title: {
      type: String,
      default: ''
    },
    desc: {
      type: String,
      default: ''
    },
    img: {
      type: String,
      default: ''
    },
    imgOpacity: {
      type: Number,
      default: 1
    },
    entName: {
      type: String,
      default: ''
    },
    userName: {
      type: String,
      default: ''
    },
    dataList: {
      type: Object
    },
    createTime: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      hasSlot: false
    }
  },
  computed: {
    imgOpacityStyle() {
      return {
        opacity: this.imgOpacity
      }
    }
  },
  created() {
    this.hasSlot = !!this.$slots.default
  }
}
</script>

<style lang="scss" scoped>
.module-header {
  height: 210px;
  position: relative;
  .lateral-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    .title {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-top: 80px;
      flex: 1;
      overflow: hidden;
      h2 {
        margin-bottom: 8px;
      }
      p {
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      span {
        width: 72px;
        height: 20px;
        font-size: 12px;
        font-weight: 350;
        //
        line-height: 20px;
      }
    }
    img {
      position: absolute;
      right: 0;
      top: 0;
      width: 360px;
      height: 180px;
      z-index: -1;
    }
  }
  .module-header-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    z-index: 1;
    &.primary {
      @include background_color_mix(--color-primary, #ffffff, 95%);
    }
    &.success {
      @include background_color_mix(--color-success, #ffffff, 95%);
    }
    &.warning {
      @include background_color_mix(--color-warning, #ffffff, 95%);
    }
    &.danger {
      @include background_color_mix(--color-danger, #ffffff, 95%);
    }
  }
}
</style>
