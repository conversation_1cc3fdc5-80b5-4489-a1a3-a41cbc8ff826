<template>
  <div class="module-list">
    <!-- 统计数 -->
    <div class="flex flex-center-between">
      <div class="total flex align-items-center font-size-14 line-height-22px">
        <template v-if="total">
          <span>共</span>
          <span
            class="count font-size-16 line-height-24px color-primary font-strong"
          >
            {{ total }}
          </span>
          <span>条</span>
        </template>
      </div>
      <slot name="right"></slot>
    </div>
    <!-- 列表区域 -->
    <div class="table-wrapper">
      <drive-table
        ref="driveTable"
        :api-fn="apiFn"
        :table-data="tableData"
        :columns="[]"
        isCustomTemplate
        layout="prev, pager, next"
        @getTotal="getTotal"
        :extral-querys="extraQuery"
      >
        <template slot="content" slot-scope="scope">
          <slot :data="scope.data" />
        </template>
      </drive-table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModuleList',
  props: {
    apiFn: {
      type: Function,
      default: null
    },
    tableData: {
      type: Array || null,
      default: () => null
    },
    extraQuery: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      total: 0
      // extralQuerys: {}
    }
  },
  methods: {
    getTotal(total) {
      this.total = total
      this.$emit('getTotal', total)
    },
    triggerSearch(filter) {
      this.$refs.driveTable.triggerSearch(filter)
    },
    refresh() {
      this.$refs.driveTable.refreshTable()
    }
  }
}
</script>

<style lang="scss" scoped>
.total {
  .count {
    margin: 0 4px;
  }
}
.table-wrapper {
  margin-top: 16px;
  margin-bottom: 32px;
}
</style>
