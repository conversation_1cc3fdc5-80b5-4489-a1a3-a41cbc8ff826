<template>
  <transition name="fade">
    <div v-if="scrollVisible" class="scroll-top" @click="backToTop">
      <!--      <svg-icon-->
      <!--        :style-obj="{ width: '2em', height: '2em', fill: '#fff' }"-->
      <!--        icon-class="backtop"-->
      <!--      />-->
      <!--      <span class="backto-text">回到顶部</span>-->
      <svg-icon icon-class="backtop" />
    </div>
  </transition>
</template>

<script>
const visibleHeight = 400

export default {
  name: 'Index',
  props: {
    scrollDistance: {
      type: Number,
      required: true
    },
    backToPosition: {
      type: Number,
      default: 0
    },
    elem: {
      type: HTMLElement,
      default: null
    },
    duration: {
      type: Number,
      default: 300
    }
  },
  computed: {
    scrollVisible() {
      return this.scrollDistance > visibleHeight
    }
  },
  methods: {
    backToTop() {
      let element = this.elem
      if (!element) {
        element = window
      }
      const start = element === window ? window.pageYOffset : element.scrollTop
      let i = 0
      this.interval = setInterval(() => {
        const next = Math.floor(
          this.easeInOutQuad(10 * i, start, -start, this.duration)
        )
        if (next <= 0) {
          element.scrollTo(0, 0)
          clearInterval(this.interval)
        } else {
          element.scrollTo(0, next)
        }
        i++
      }, 16.7)
    },
    easeInOutQuad(t, b, c, d) {
      const state = (t /= d / 2) < 1
      if (state) {
        return (c / 2) * t * t + b
      }
      return (-c / 2) * (--t * (t - 2) - 1) + b
    }
  }
}
</script>

<style lang="scss" scoped>
.scroll-top {
  position: fixed;
  bottom: 80px;
  right: 30px;
  z-index: 9999;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 3px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  text-align: center;
  box-shadow: 0 6px 30px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid #dcdcdc;
  //.backto-text {
  //  display: none;
  //  font-size: 14px;
  //  line-height: 16px;
  //}
  &:hover {
    background-color: #f3f3f3;
    //color: #fff;
    //.svg-icon {
    //  display: none;
    //}
    //.backto-text {
    //  display: inline;
    //}
  }
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s;
}
.fade-enter,
.fade-leave-to {
  transform: translateX(70px);
}
</style>
