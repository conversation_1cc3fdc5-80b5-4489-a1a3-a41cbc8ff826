<template>
  <div class="icon-info">
    <svg-icon v-if="iconClass" :class="'color-' + iconType" :icon-class="iconClass" />
    <label
      v-if="label"
      class="font-size-14 line-height-22 color-text-secondary"
      :class="[nowrap ? 'word-nowrap' : '']"
    >
      {{ label }}
    </label>
    <el-link
      :class="[nowrap ? 'word-nowrap' : '']"
      type="primary"
      v-if="type === 'link'"
      @click="tapText"
    >
      {{ text }}
    </el-link>
    <span
      v-else
      style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
      class="font-size-14 line-height-22 color-text-secondary"
      :class="[nowrap ? 'word-nowrap' : '']"
      v-tooltip="text"
    >
      {{ text }}
    </span>
  </div>
</template>

<script>
export default {
  name: 'IconInfo',
  props: {
    iconType: {
      type: String,
      default: 'primary'
    },
    iconClass: {
      type: String,
      default: ''
    },
    label: {
      type: String,
      default: ''
    },
    text: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: ''
    },
    nowrap: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    tapText() {
      this.$emit('tapText')
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-info {
  width: 100%;
  display: flex;
  align-items: center;
  .svg-icon {
    width: 16px;
    height: 16px;
    flex: 0 0 16px;
  }
  span {
    flex: 1;
    padding-left: 4px;
  }
}
</style>
