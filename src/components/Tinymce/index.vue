<template>
  <div class="tinymce-box">
    <editor
      ref="tinymceEditor"
      v-model="myValue"
      :id="id"
      api-key="mvwvj094dzkrplkvg9maj6cdtkko3si2pqzpnw4kzgafjpih"
      :init="init"
      :disabled="disabled"
      @onClick="onClick"
    />
    <span>{{ characterCount }} 字</span>
  </div>
</template>

<script>
import tinymce from 'tinymce/tinymce' // tinymce默认hidden，不引入不显示
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/plugins/image' // 插入上传图片插件
import 'tinymce/plugins/media' // 插入视频插件
import 'tinymce/plugins/table' // 插入表格插件
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/wordcount' // 字数统计插件
import 'tinymce/plugins/link' // 链接插件
import 'tinymce/plugins/quickbars' // 快速栏插件
import 'tinymce/plugins/fullscreen' // 全屏插件
import 'tinymce/plugins/hr' // hr插件
import 'tinymce/plugins/preview' // 预览插件
import 'tinymce/plugins/autolink' // 自动链接插件
import 'tinymce/plugins/advlist' // 高级列表插件
// import 'tinymce/icons/default'

import { cosUploadComplete, getCosGenKey } from '@/api/common'
import COS from 'cos-js-sdk-v5'
import { cosConfig } from '@/settings'
let Loading

const path = '/tinymce'
const processPath =
  process.env.NODE_ENV === 'development'
    ? `${path}`
    : `${process.env.VUE_APP_ASSET_CONTEXT + process.env.BASE_URL + path}`

/* function getImgWH(blob) {
  const image = new Image()
  image.crossOrigin = ''
  image.src = blob
  return new Promise((resolve) => {
    image.onload = function () {
      const { width, height } = image
      resolve({ width, height })
    }
  })
} */

export default {
  name: 'Tinymce',
  components: {
    Editor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: () => {
        return (
          'vue-tinymce-' +
          +new Date() +
          ((Math.random() * 1000).toFixed(0) + '')
        )
      }
    },
    height: {
      type: Number,
      default: 400
    },
    disabled: {
      type: Boolean,
      default: false
    },
    plugins: {
      type: [String, Array],
      default:
        'lists, advlist image wordcount media table link fullscreen quickbars hr preview autolink'
    },
    toolbar: {
      type: [String, Array],
      default:
        'fullscreen | image | formatselect fontsizeselect forecolor backcolor alignleft  lineheight hr | link unlink | numlist bullist | media table | bold italic underline strikethrough | preview'
    },
    max: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      init: {
        language_url: `${processPath}/langs/zh_CN.js`,
        language: 'zh_CN',
        skin_url: `${processPath}/skins/ui/oxide`,
        content_css: `${processPath}/skins/content/default/content.css`,
        fontsize_formats: '12px 14px 16px 18px 20px 24px 36px 48px',
        height: this.height,
        plugins: this.plugins,
        toolbar: this.toolbar,
        branding: false,
        // menubar: false,
        external_plugins: {
          powerpaste: `${processPath}/plugins/powerpaste/plugin.min.js`, // ${this.baseUrl}
          lineheight: `${processPath}/plugins/lineheight/plugin.min.js`
        },
        powerpaste_word_import: 'merge', // 参数:propmt, merge, clear
        powerpaste_html_import: 'merge', // propmt, merge, clear
        powerpaste_allow_local_images: false, // 粘贴图片
        end_container_on_empty_block: true,
        paste_data_images: false,
        convert_urls: false,
        allow_unsafe_link_target: true,
        images_upload_handler: async (blobInfo, success) => {
          if (!blobInfo.blob().name) return
          const img = await this.uploadImage(blobInfo)
          // const wh = await getImgWH(blobInfo.blobUri())
          success(img)
        },
        init_instance_callback: editor => {
          this.wordcount = this.$refs.tinymceEditor.editor.plugins.wordcount

          const wordcountStatusbar = editor.editorContainer.querySelector(
            '.tox-statusbar__wordcount'
          )
          wordcountStatusbar.remove()
          this.setCharacterCount()
          editor.on('FullscreenStateChanged', e => {
            const sidebarContainer = document.querySelector('#sidebarContainer')
            sidebarContainer.style.display = e.state ? 'none' : 'block'
          })
        },
        paste_preprocess: (plugin, args) => {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = args.content;

          const images = tempDiv.getElementsByTagName('img');
          while (images.length > 0) {
            images[0].parentNode.removeChild(images[0]);
          }

          args.content = tempDiv.innerHTML;
        },
      },
      myValue: this.value,
      characterCount: 0
    }
  },
  watch: {
    value(newValue) {
      this.myValue = newValue
    },
    myValue(newValue) {
      this.setCharacterCount()
      this.$emit('input', newValue)
    }
  },
  deactivated() {
    this.resetToolHandle()
  },
  beforeDestroy() {
    this.resetToolHandle()
  },
  methods: {
    loading() {
      Loading = this.$loading({
        lock: true,
        text: '正在上传...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.6)'
      })
    },
    // 关闭loading
    closeLoading() {
      Loading && Loading.close()
    },
    resetToolHandle() {
      const sidebarContainer = document.querySelector('#sidebarContainer')
      sidebarContainer.style.display = 'block'
      const editor = tinymce.get(this.id)
      if (!editor) return false
      const isFullScreen =
        editor.plugins.fullscreen && editor.plugins.fullscreen.isFullscreen()
      if (isFullScreen) {
        // 退出全屏模式
        editor.execCommand('mceFullScreen')
      }
    },
    setCharacterCount() {
      this.characterCount = this.wordcount
        ? this.wordcount.body.getCharacterCount()
        : 0
    },
    uploadImage(blobInfo) {
      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        this.loading()
        const file = blobInfo.blob()
        const params = {
          fileName: file.name,
          type: 'Tinymce'
        }
        const res = await getCosGenKey(params)
        const cos = new COS({
          getAuthorization: async (options, callback) => {
            try {
              callback({
                TmpSecretId: res.tmpSecretId,
                TmpSecretKey: res.tmpSecretKey,
                SecurityToken: res.sessionToken,
                StartTime: res.startTime,
                ExpiredTime: res.expiredTime,
                ScopeLimit: true
              })
            } catch (error) {
              this.closeLoading()
              reject(error)
            }
          }
        })
        cos.uploadFile(
          {
            ...cosConfig,
            Key: res.objectName,
            Body: file // 上传文件对象
          },
          (err, data) => {
            console.log(data, 'data')
            if (err) {
              this.errorMsg()
              console.log('上传失败', err)
              this.closeLoading()
              reject()
            } else {
              const params = {
                type: 'Tinymce',
                fileName: file.name,
                objectName: res.objectName,
                size: file.size
              }
              cosUploadComplete(params)
                .then(res => {
                  resolve(res.path)
                  this.closeLoading()
                })
                .catch(e => {
                  this.errorMsg(e)
                  reject()
                })
            }
          }
        )
      })
    },
    errorMsg(msg) {
      this.closeLoading()
      this.$toast.error(msg || '图片上传失败')
    },
    onClick(e) {
      this.$emit('onClick', e, tinymce)
    },
    // 可以添加一些自己的自定义事件，如清空内容
    clear() {
      this.myValue = ''
    }
  }
}
</script>

<style lang="scss" scoped>
.tinymce-box {
  position: relative;
  span {
    font-size: 12px;
    line-height: 16px;
    position: absolute;
    right: 20px;
    bottom: 2px;
    color: #999;
  }
}
</style>
