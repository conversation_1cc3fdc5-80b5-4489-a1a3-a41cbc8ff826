<template>
  <div class="wh100">
    <div
      v-if="!hasData || !hasSlot"
      class="empty-wrapper flex flex-center-center wh100"
    >
      <img :width="imgWidth" :height="imgHeight" :src="img" alt="" />
      <p>{{ description }}</p>
    </div>
    <slot v-else />
  </div>
</template>

<script>
export default {
  name: 'EmptyData',
  props: {
    img: {
      type: String,
      default: require('./empty.png')
    },
    imgWidth: {
      type: Number,
      default: 120
    },
    imgHeight: {
      type: Number,
      default: 120
    },
    description: {
      type: String,
      default: '暂无数据'
    },
    hasData: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      hasSlot: false
    }
  },
  created() {
    this.hasSlot = !!this.$slots.default
  }
}
</script>

<style lang="scss" scoped>
.empty-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  p {
    font-size: 12px;
    line-height: 20px;
    margin-top: 4px;
    text-align: center;
    @include font_color(--color-text-secondary);
  }
}
</style>
