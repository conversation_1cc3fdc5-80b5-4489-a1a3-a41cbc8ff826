<script>
export default {
  name: 'DriveTableRender',
  functional: true,
  props: {
    scope: {
      type: Object,
      default: () => ({})
    },
    render: {
      type: Function,
      default: null
    },
    // 扩展search的render
    options: {
      type: Array
    }
  },
  render: (h, ctx) => {
    const { scope, render, options } = ctx.props
    if (options) {
      return render ? render(h, options, scope) : ''
    }
    return render ? render(h, scope) : ''
  }
}
</script>
