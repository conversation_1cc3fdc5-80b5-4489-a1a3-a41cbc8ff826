<template>
  <div class="driven-table-search" v-if="hasSomeSearch">
    <div class="search-content">
      <!-- 表单区域 -->
      <div class="search-form">
        <el-form ref="searchForm" :inline="true" :model="formQuerys">
          <el-row :gutter="36">
            <template v-for="column in filterSearchColumn">
              <el-col
                :key="column.prop"
                :xs="24"
                :sm="24"
                :md="12"
                :lg="8"
                :xl="8"
                :hidden="column.search.hidden"
              >
                <el-form-item
                  class="search-item"
                  :label="column.label"
                  :prop="confirmProp(column)"
                >
                  <component
                    :is="inputMap[column.search.type].type"
                    :filterable="column.search.filterable || false"
                    v-model.trim="formQuerys[confirmProp(column)]"
                    v-bind="_compareAttrs(column, inputMap)"
                    v-on="column.search.on"
                  >
                    <template
                      v-if="
                        column.search.slotRender ||
                        inputMap[column.search.type].slotRender
                      "
                      slot-scope="scope"
                    >
                      <drive-table-render
                        :scope="scope || {}"
                        :render="
                          column.search.slotRender ||
                          inputMap[column.search.type].slotRender
                        "
                        :options="column.search.options"
                      />
                    </template>
                  </component>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>
      <!-- 按钮区域 -->
      <div class="search-button">
        <el-button type="info" size="small" @click="resetForm">
          <svg-icon icon-class="refresh" />
          <span>重置</span>
        </el-button>
        <el-button
          ref="search-button"
          type="primary"
          size="small"
          @click="searchSubmit"
        >
          <svg-icon icon-class="search" />
          <span>搜索</span>
        </el-button>
      </div>
    </div>
    <!-- 分割线 -->
    <div class="line"></div>
  </div>
</template>

<script>
import DriveTableRender from './render'
import { inputMap, inputDefautConfig } from './input'
import InputRange from '@/components/InputRange'

export default {
  name: 'DriveTableSearch',
  inject: ['DriveTable'],
  components: { DriveTableRender, InputRange },
  props: {
    columns: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      inputMap,
      inputDefautConfig,
      formQuerys: {},
      formQuerysStr: '',
      rangeProp: [],
      resetEmptyOptionsProp: []
    }
  },
  computed: {
    // 判断是否有搜索数据
    hasSomeSearch() {
      return this.columns.some(v => v.search)
    },
    // 筛选出有search属性的column
    filterSearchColumn() {
      return this.columns.filter(v => v.search)
    }
  },
  watch: {
    formQuerys: {
      handler(val) {
        if (val) {
          this.formQuerysStr = JSON.stringify(val)
        }
      },
      deep: true
    },
    formQuerysStr(newVal, oldVal) {
      this.$emit('formQuerys', {
        newVal,
        oldVal
      })
    },
    columns: {
      handler(columns) {
        if (!columns) return
        columns
          .filter(column => column.search)
          .forEach(item => {
            if (
              item.search.type === 'range' ||
              item.search.type === 'daterange' ||
              item.search.type === 'timeSelect'
            ) {
              this.$set(this.formQuerys, item.prop, [])
              this.rangeProp.push(item)
            }
            if (item.search.resetEmptyOptions) {
              this.resetEmptyOptionsProp.push(item)
            }
            if (item.search.default || item.search.default === '') {
              if (this.formQuerys[item.prop]) {
                this.$set(
                  this.formQuerys,
                  item.prop,
                  this.formQuerys[item.prop]
                )
              } else {
                this.$set(this.formQuerys, item.prop, item.search.default)
              }
            }
          })
      },
      deep: true,
      immediate: true
    }
  },
  created() {
    /*document.onkeydown = e => {
      let key
      if (window.event === undefined || window.event === null) {
        key = e.keyCode
      } else {
        key = window.event.keyCode
      }
      if (key === 13) {
        this.DriveTable.isEnter && this.searchSubmit()
      }
    }*/
  },
  methods: {
    // 默认属性合并
    _compareAttrs(column, inputMap) {
      const { type } = column.search
      const { placeholderPrefix } = inputMap[type]
      return {
        ...inputDefautConfig[type],
        ...column.search,
        ...column.search.attrs,
        placeholder: placeholderPrefix + column.label
      }
    },
    // 确定查询时的prop
    confirmProp(column) {
      if (column.search && column.search.propAlias) {
        return column.search.propAlias
      } else {
        return column.prop
      }
    },
    // select切换
    selectChange(e) {
      this.$emit('selectChange', e)
    },
    // 重置
    resetForm() {
      const loading = this.DriveTable.loading
      if (loading) return false
      this.$refs.searchForm.resetFields()
      this.formQuerys = {}
      if (this.rangeProp.length > 0) {
        this.rangeProp.forEach(item => {
          this.$set(this.formQuerys, item.prop, [])
        })
      }
      if (this.resetEmptyOptionsProp.length > 0) {
        this.resetEmptyOptionsProp.forEach(item => {
          item.search.options = []
        })
      }
      this.DriveTable.resetSearch()
    },
    // 搜索
    searchSubmit() {
      const loading = this.DriveTable.loading
      if (loading) return false
      const formQuerys = JSON.parse(JSON.stringify(this.formQuerys))
      for (let key in formQuerys) {
        const value = formQuerys[key]
        if (
          value === null ||
          JSON.stringify(value) === '[]' ||
          JSON.stringify(value) === '{}'
        ) {
          delete formQuerys[key]
        }
      }
      this.DriveTable.triggerSearch(formQuerys)
    }
  }
}
</script>

<style lang="scss" scoped>
.driven-table-search {
  .search-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .search-form {
      flex: 1;
    }
    .search-button {
      flex: 0 0 200px;
      text-align: right;
    }
    :deep(.el-form) {
      .el-form-item {
        width: 100%;
        height: 32px;
        display: flex;
        align-items: center;
        .el-form-item__label {
          flex: 0 0 68px;
        }
        .el-form-item__content {
          flex: 1;
          .el-select,
          .el-cascader,
          .el-autocomplete,
          .el-date-editor {
            width: 100%;
          }
          .el-date-editor {
            .el-range-separator {
              width: 14px;
              text-align: center;
              padding: 0;
            }
          }
        }
      }
    }
  }

  .line {
    width: 100%;
    height: 1px;
    margin-bottom: 10px;
    @include background_color(--border-color-lighter);
  }
}
</style>
