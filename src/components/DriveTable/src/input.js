const inputMap = {
  autocomplete: {
    type: 'el-autocomplete',
    slotRender: false,
    placeholderPrefix: '请输入'
  },
  input: {
    type: 'el-input',
    slotRender: false,
    placeholderPrefix: '请输入'
  },
  select: {
    type: 'el-select',
    slotRender: (h, options) => {
      return options.map(option => {
        return (
          <el-option
            key={option.value}
            label={option.label}
            value={option.value}
          />
        )
      })
    },
    placeholderPrefix: '请选择'
  },
  cascader: {
    type: 'el-cascader',
    slotRender: false,
    placeholderPrefix: '请选择'
  },
  date: {
    type: 'el-date-picker',
    slotRender: false,
    placeholderPrefix: '请选择'
  },
  daterange: {
    type: 'el-date-picker',
    slotRender: false,
    placeholderPrefix: '请选择'
  },
  InputRange: {
    type: 'InputRange'
  }
}

const inputDefautConfig = {
  input: {
    clearable: true
  },
  select: {
    multipleLimit: 0,
    clearable: true
  },
  cascader: {
    showAllLevels: false,
    clearable: true
  },
  date: {
    type: 'date'
  },
  daterange: {
    type: 'daterange',
    rangeSeparator: '至',
    startPlaceholder: '开始日期',
    endPlaceholder: '结束日期',
    valueFormat: 'yyyy-MM-dd',
    pickerOptions: {
      shortcuts: [
        {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        },
        {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }
      ]
    }
  }
}

export { inputMap, inputDefautConfig }
