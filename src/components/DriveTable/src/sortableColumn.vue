<template>
  <el-table-column width="20px" class-name="tips-cell">
    <template>
      <svg-icon icon-class="sortable-tips" class="color-primary" />
    </template>
  </el-table-column>
</template>

<script>
export default {
  name: 'sortableColumn'
}
</script>

<style lang="scss">
.tips-cell {
  .cell {
    padding: 0 !important;
    margin-left: 8px;
    cursor: pointer;
    display: none;
  }
}
.el-table__row:hover {
  .tips-cell .cell {
    display: block;
  }
}
.sortable-chosen {
  .el-table__cell {
    border-top: 2px solid #ed7b2f !important;
    background: #fff8f3;
  }
}
</style>
