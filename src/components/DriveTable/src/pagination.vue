<template>
  <div class="pagination-card">
    <el-pagination
      :current-page="DriveTable.PAGE_NUMBER || 1"
      :page-sizes="PAGE_SIZES"
      :page-size="DriveTable.defaultPageSize || PAGE_SIZE"
      :layout="DriveTable.layout"
      :total="total"
      background
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
import { PAGE_NUMBER, PAGE_SIZE, PAGE_SIZES } from './config'

export default {
  name: 'DriveTablePagination',
  inject: ['DriveTable'],
  props: {
    total: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      PAGE_NUMBER,
      PAGE_SIZE,
      PAGE_SIZES
    }
  },
  methods: {
    // 页码容器数变化
    handleSizeChange(pageSize) {
      this.DriveTable.paginationChange({ pageSize })
      this.DriveTable.scrollTop && this.scrollToTop()
    },
    // 分页变化
    handleCurrentChange(pageNumber) {
      this.DriveTable.paginationChange({ pageNumber })
      this.DriveTable.scrollTop && this.scrollToTop()
    },
    // 页面滚动到顶部
    scrollToTop() {
      window.$appMain.scrollTop = 0
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-pagination) {
  text-align: right;
  padding: 16px;
  .el-pagination__total {
    float: left;
  }
  &::after {
    content: '';
    font-size: 0;
    clear: both;
  }
}
</style>
