<template>
  <div>
    <!--搜索区域-->
    <div v-if="!noSearch" class="search-card">
      <drive-table-search
        ref="drive-table-search"
        :columns="columns"
        @formQuerys="formQuerysChange"
      />
    </div>

    <!-- 操作区域插槽 -->
    <div class="operate">
      <div class="operate-left font-size-16 color-dark">
        <slot name="operate-left" />
      </div>
      <div class="operate-right">
        <slot name="operate-right" />
      </div>
    </div>

    <!--表体区域-->
    <!-- 自定义渲染内容 -->
    <template v-if="isCustomTemplate">
      <div
        class="custom-template-wrapper"
        v-if="data.length > 0 || !flag"
        v-loading="loading"
      >
        <slot name="content" :data="data" :total="total" />
      </div>
      <div v-else class="empty-content">
        <empty-data />
      </div>
    </template>
    <!-- 默认表格渲染内容 -->
    <el-table
      v-else
      ref="driveTable"
      v-loading="loading"
      v-bind="$attrs"
      :data="data"
      :element-loading-text="LOADING_TEXT"
      :header-cell-style="headerRowStyle"
      border
      :stripe="stripe"
      @sort-change="sortChange"
      v-on="$listeners"
      :key="tableKey"
      :row-class-name="rowClassName"
    >
      <!-- 空数据状态 -->
      <template slot="empty">
        <slot v-if="hasSlotsEmpty" name="empty" />
        <div v-else class="empty-content">
          <empty-data :description="emptyData" />
        </div>
      </template>
      <!-- 表格拖拽列样式 -->
      <drive-sortable-column v-if="isSortable" />
      <!-- 表格列 -->
      <drive-table-column
        v-for="(cloumn, index) in columns.filter(column => !column.hidden)"
        :key="index"
        :column="cloumn"
        :isSortable="isSortable"
        :dragTableLen="dragTable.length"
        @moveDown="moveDown"
        @moveUp="moveUp"
        @moveTop="moveTop"
        @moveBottom="moveBottom"
      />
    </el-table>
    <!--分页区域-->
    <template v-if="pagination">
      <drive-table-pagination
        v-show="showPagination"
        :total="total"
        :scroll-top="scrollTop"
      />
    </template>
  </div>
</template>

<script>
import { LOADING_TEXT, QUERY_ALIAS, PAGE_NUMBER, PAGE_SIZE } from './config'
import DriveTableSearch from './search'
import DriveTableColumn from './cloumn'
import DriveTablePagination from './pagination'
import DriveSortableColumn from './sortableColumn'
import Sortable from 'sortablejs'

export default {
  name: 'DriveTable',
  components: {
    DriveTableSearch, // 搜索组件
    DriveTableColumn, // 表列组件
    DriveSortableColumn, // 表列组件
    DriveTablePagination // 分页组件
  },
  provide() {
    return {
      DriveTable: this
    }
  },
  props: {
    emptyData: {
      type: String,
      default: '暂无数据'
    },
    // 接口方法, 与直接数据二选一
    apiFn: {
      type: Function,
      default: null
    },
    // 直接数据
    tableData: {
      type: Array,
      default: null
    },
    // 表格参数别名
    queryAlias: {
      type: Object,
      default: () => ({})
    },
    // 额外初始参数
    extralQuerys: {
      type: Object,
      default: () => ({})
    },
    // 表列数据
    columns: {
      type: Array,
      required: true
    },
    // 表头样式
    headerRowStyle: {
      type: Object,
      default: () => ({})
    },
    // 查询参数格式化钩子
    searchQuerysHook: {
      type: Function,
      default: null
    },
    // 排序参数格式化钩子
    sortQuerysHook: {
      type: Function,
      default: null
    },
    // 查询参数变化监听钩子
    formQuerysChangeHook: {
      type: Function,
      default: null
    },
    // 是否自定义显示模板
    isCustomTemplate: {
      type: Boolean,
      default: false
    },
    // 是否需要搜索
    noSearch: {
      type: Boolean,
      default: false
    },
    // 点击分页是否到顶部
    scrollTop: {
      type: Boolean,
      default: true
    },
    // 是否需要分页
    isNeedPagination: {
      type: Boolean,
      default: false
    },
    // 每页数据量
    defaultPageSize: {
      type: Number,
      default: 0
    },
    rowClassName: {
      type: Function,
      default: null
    },
    // 是否支持回车键
    isEnter: {
      type: Boolean,
      default: true
    },
    // 分页器选项
    layout: {
      type: String,
      default: 'total, sizes, prev, pager, next, jumper'
    },
    // 处理table数据的钩子
    handlerDataHook: {
      type: Function,
      default: val => {
        return val
      }
    },
    // 拖拽排序保存方法
    sortFn: {
      type: Function,
      default: null
    },
    // 开启拖拽排序
    isSortable: {
      type: Boolean,
      default: false
    },
    // 是否开启斑马纹表格
    stripe: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // 表格初始配置项
      LOADING_TEXT,
      PAGE_NUMBER,
      PAGE_SIZE,
      QUERY_ALIAS,
      flag: false,
      // 表格数据
      data: [],
      // 查询参数
      querys: {},
      // 查询参数
      searchQuerys: {},
      // 表格加载动态图
      loading: false,
      // 数据总数
      total: 0,
      // 是否需要分页
      pagination: false,
      // 拖拽排序
      sort_table: null,
      // 拖拽初始化数据
      dragTable: [],
      // 多表头混乱问题
      tableKey: new Date() + ((Math.random() * 1000).toFixed(0) + ''),
      showPagination: true
    }
  },
  computed: {
    // 是否存在apiFn和tableData共存
    apiAndData() {
      return this.apiFn && this.tableData
    },
    hasSlotsEmpty() {
      return !!this.$slots.empty
    }
  },
  watch: {
    apiFn: {
      handler() {
        this.initData()
      },
      deep: true
    },
    // 监听传入数据的变化
    tableData: {
      handler(val) {
        if (val && val.length >= 0) {
          this.initData()
        }
      },
      deep: true
    },
    extralQuerys: {
      handler(val) {
        console.info(val)
      },
      deep: true
    },
    isSortable(val) {
      val ? this.rowDrop() : this.sortSave()
    },
    columns: {
      handler() {
        this.tableKey = new Date() + ((Math.random() * 1000).toFixed(0) + '')
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    resetTableKeyHandle() {
      this.tableKey = this.$options.data().tableKey
    },
    resetSearchParameters() {
      this.$refs['drive-table-search'].resetForm()
    },
    // 初始化拖拽数据
    initDrag(list) {
      this.data = list
      this.dragTable = JSON.parse(JSON.stringify(list))
    },
    // 拖拽保存
    sortSave() {
      const ids = this.data.map(item => item.id)
      const initIds = this.dragTable.map(item => item.id)
      if (JSON.stringify(ids) !== JSON.stringify(initIds) && ids.length) {
        this.sortFn({
          ids: JSON.stringify(ids)
        }).then(() => {
          this.refreshTable()
          this.destroyDrop()
          this.$emit('saveDrag')
        })
      } else {
        this.refreshTable()
        this.destroyDrop()
      }
    },
    // 置底
    moveBottom(index, row) {
      this.data.splice(index, 1)
      this.data.push(row)
    },
    //置顶
    moveTop(index, row) {
      this.data.splice(index, 1)
      this.data.unshift(row)
    },
    // 上移
    moveUp(index) {
      const upDate = this.data[index - 1]
      this.data.splice(index - 1, 1)
      this.data.splice(index, 0, upDate)
    },
    // 下移
    moveDown(index) {
      const downDate = this.data[index + 1]
      this.data.splice(index + 1, 1)
      this.data.splice(index, 0, downDate)
    },
    // 销毁拖拽排序
    destroyDrop() {
      this.showPagination = true
      this.sort_table && this.sort_table.destroy()
      this.sort_table = null
    },
    // 拖拽排序
    rowDrop() {
      this.showPagination = false
      const tbody = this.$refs.driveTable.$el.querySelector(
        '.el-table__body-wrapper tbody'
      )
      const _this = this
      this.sort_table = Sortable.create(tbody, {
        animation: 180,
        delay: 0,
        chosenClass: 'sortable-chosen', // 选中classname
        onUpdate: event => {
          const oldIndex = event.oldIndex,
            newIndex = event.newIndex,
            $child = tbody.children[newIndex],
            $oldChild = tbody.children[oldIndex]
          // 先删除移动的节点
          tbody.removeChild($child)
          // 再插入移动的节点到原有节点，还原了移动的操作
          if (newIndex > oldIndex) {
            tbody.insertBefore($child, $oldChild)
          } else {
            tbody.insertBefore($child, $oldChild.nextSibling)
          }
          // 更新items数组
          const item = _this.data.splice(oldIndex, 1)
          _this.data.splice(newIndex, 0, item[0])
        }
      })
    },
    // 清除选择项
    clearSelection() {
      this.$refs.driveTable.clearSelection()
    },
    // 切换行选择
    toggleRowSelection(row, selected) {
      this.$refs.driveTable.toggleRowSelection(row, selected)
    },
    // 切换所有选择项
    toggleAllSelection() {
      this.$refs.driveTable.toggleAllSelection()
    },
    // 展开表格与树形表格，切换某一行的展开状
    toggleRowExpansion(row, expanded) {
      this.$refs.driveTable.toggleRowExpansion(row, expanded)
    },
    // 设定某一行为选中行
    setCurrentRow(row) {
      this.$refs.driveTable.setCurrentRow(row)
    },
    // 清除排序
    clearSort() {
      this.$refs.driveTable.clearSort()
    },
    // 清除筛选
    clearFilter(columnKey) {
      this.$refs.driveTable.clearFilter(columnKey)
    },
    // 重新渲染，防止布局混乱
    doLayout() {
      this.$refs.driveTable.doLayout()
    },
    // 排序
    sort(prop, order) {
      this.$refs.driveTable.sort(prop, order)
    },
    // 分页器改变方法触发
    paginationChange(temp) {
      if (temp) {
        for (let k in temp) {
          const val = temp[k]
          if (k === 'pageNumber') {
            this.PAGE_NUMBER = val
          }
          if (k === 'pageSize') {
            this.PAGE_SIZE = val
          }
        }
      }
      const orderBy = this.querys.orderBy
      this.initQuerys()
      this.querys.orderBy = orderBy
      this.refreshTable()
    },
    // 开启loading
    openLoading() {
      this.loading = true
    },
    // 关闭loading
    closeLoading() {
      this.loading = false
    },
    // 获取查询参数
    getRequestQuerys() {
      return { ...this.extralQuerys }
    },
    // 生成alias配置
    _genQueryAlias() {
      return {
        pageNumber: this.queryAlias.pageNo || QUERY_ALIAS.pageNo,
        pageSize: this.queryAlias.pageSize || QUERY_ALIAS.pageSize,
        querys: this.queryAlias.querys || QUERY_ALIAS.querys
      }
    },
    // 初始化查询参数及默认配置
    initQuerys() {
      const { pageNumber, pageSize } = this._genQueryAlias()
      this.querys = {
        [pageNumber]: this.PAGE_NUMBER,
        [pageSize]: this.resetDefaultPageSize(),
        ...this.searchQuerys,
        ...this.getRequestQuerys()
      }
    },

    // 默认表格pageSize重置
    resetDefaultPageSize() {
      return this.defaultPageSize ? this.defaultPageSize : this.PAGE_SIZE
    },
    // 重置页码刷新表格
    resetPageNoRefreshTable() {
      this.PAGE_NUMBER = 1
      const { pageNumber } = this._genQueryAlias()
      this.querys[pageNumber] = 1
      this.refreshTable()
    },
    // 重置参数
    resetQuerys() {
      this.PAGE_NUMBER = 1
      this.searchQuerys = {}
      // 清除排序
      this.$refs.driveTable && this.$refs.driveTable.clearSort()
      this.initQuerys()
    },
    // 刷新表格
    refreshTable() {
      this.initData()
    },
    // 重置刷新表格
    refreshResetTable() {
      this.resetQuerys()
      this.refreshTable()
    },
    // 触发查询
    triggerSearch(querys) {
      this.resetQuerys()
      let searchQuerys
      searchQuerys = Object.assign({}, this.getRequestQuerys(), querys)
      if (this.searchQuerysHook) {
        searchQuerys = this.searchQuerysHook(searchQuerys)
      }
      this.searchQuerys = searchQuerys
      this.querys = { ...this.querys, ...searchQuerys }
      this.refreshTable()
    },
    // 重置搜索条件
    async resetSearch() {
      await this.$emit('resetSearch')
      this.resetQuerys()
      this.refreshTable()
    },
    // 初始化数据
    initData() {
      // api方式表格
      if (this.apiFn) {
        this.data = []
        // 开启分页
        this.pagination = true
        this.openLoading()
        this.$emit('setLoading', true)
        this.apiFn({
          ...this.querys,
          ...this.getRequestQuerys(),
          isTable: true
        })
          .then(res => {
            this.flag = true
            this.total = res.total
            this.$emit('getData', res)
            if (this.handlerDataHook) {
              let list = res.list || res.data
              this.data = list.map((item, index) => {
                item.index = index
                item.pageNo = this.querys.pageNo
                item.pageSize = this.querys.pageSize
                return this.handlerDataHook(item)
              })
            } else {
              this.data = res.list || res.data
            }
            this.$emit('getTotal', res.total)
            this.$emit('setLoading', false)
            this.doLayout()
            this.closeLoading()
          })
          .catch(() => {
            this.flag = true
            this.closeLoading()
            this.$emit('setLoading', false)
          })
        return
      }
      // 静态数据方式
      if (this.tableData && this.tableData.length >= 0) {
        this.pagination = this.isNeedPagination || false
        this.flag = true
        if (this.isNeedPagination) {
          this.total = this.tableData.length
          const params = { ...this.querys, ...this.getRequestQuerys() }
          const { pageNumber, pageSize } = this._genQueryAlias()
          const number = params[pageNumber]
          const size = this.defaultPageSize || params[pageSize]
          this.data = this.tableData.slice((number - 1) * size, number * size)
          this.$emit('getTotal', this.tableData.length)
          this.doLayout()
        } else {
          this.data = this.tableData
        }
      }
    },
    // 初始化
    init() {
      if (this.apiAndData) {
        throw new Error('apiFn and tableData can not prop at the same time')
      }
      this.initQuerys()
      this.initData()
    },
    // 排序
    sortChange({ prop, order }) {
      switch (order) {
        case 'ascending':
          order = 'asc'
          break
        case 'descending':
          order = 'desc'
          break
        default:
          order = ''
      }
      let orderQuery = { [prop]: order }
      if (this.sortQuerysHook) {
        orderQuery = this.sortQuerysHook({ [prop]: order })
      }
      this.querys = {
        ...this.querys,
        orderBy: JSON.stringify(orderQuery)
      }
      this.refreshTable()
    },
    // 查询参数变化时暴露钩子
    formQuerysChange({ newVal, oldVal }) {
      typeof this.formQuerysChangeHook === 'function' &&
        this.formQuerysChangeHook(newVal, oldVal)
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-content {
  padding: 120px 0;
}

.operate {
  display: flex;
  align-items: center;
  justify-content: space-between;
  div {
    * {
      margin-bottom: 10px;
    }
  }
}

.custom-template-wrapper {
  min-height: 384px;
}
:deep(.el-table) {
  width: 100%;
  border-radius: 3px;
}
</style>
