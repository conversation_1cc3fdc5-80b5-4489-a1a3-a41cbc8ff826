<template>
  <div>
    <el-tooltip
      v-if="scope.$index !== 0"
      class="sort-item"
      effect="dark"
      content="上移"
      placement="top"
    >
      <svg-icon
        icon-class="arrow-up"
        @click="moveUp(scope.$index, scope.row)"
      />
    </el-tooltip>
    <el-tooltip
      v-if="scope.$index !== dragTableLen - 1"
      class="sort-item"
      effect="dark"
      content="下移"
      placement="top"
    >
      <svg-icon
        icon-class="arrow-down"
        @click="moveDown(scope.$index, scope.row)"
      />
    </el-tooltip>
    <el-tooltip
      v-if="scope.$index === dragTableLen - 1"
      class="sort-item"
      effect="dark"
      content="置顶"
      placement="top"
    >
      <svg-icon
        icon-class="backtop"
        @click="moveTop(scope.$index, scope.row)"
      />
    </el-tooltip>
    <el-tooltip
      v-if="scope.$index === 0"
      class="sort-item"
      effect="dark"
      content="置底"
      placement="top"
    >
      <svg-icon
        class-name="rotate-180"
        icon-class="backtop"
        @click="moveBottom(scope.$index, scope.row)"
      />
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'DriveSortTableOperation',
  props: {
    scope: {
      type: Object,
      default: () => ({})
    },
    dragTableLen: {
      type: Number,
      default: 0
    }
  },
  methods: {
    moveDown(index, row) {
      this.$emit('moveDown', index, row)
    },
    moveUp(index, row) {
      this.$emit('moveUp', index, row)
    },
    moveTop(index, row) {
      this.$emit('moveTop', index, row)
    },
    moveBottom(index, row) {
      this.$emit('moveBottom', index, row)
    }
  }
}
</script>

<style scoped lang="scss">
.sort-item {
  color: #ed7b2f;
  cursor: pointer;
  margin-right: 15px !important;
}
.rotate-180 {
  transform: rotate(180deg);
}
</style>
