<template>
  <el-table-column
    v-bind="$attrs"
    :prop="column.prop"
    :label="column.label"
    :type="column.type"
    :index="column.index"
    :column-key="column.columnKey"
    :width="column.width"
    :min-width="column.minWidth"
    :fixed="column.fixed"
    :render-header="column.renderHeader"
    :sortable="column.sortable || false"
    :sort-method="column.sortMethod"
    :sort-by="column.sortBy"
    :sort-orders="column.sortOrders"
    :resizable="column.resizable || true"
    :formatter="column.formatter"
    :show-overflow-tooltip="column.showOverflowTooltip || false"
    :align="column.align || 'left'"
    :header-align="column.headerAlign || column.align || 'left'"
    :class-name="column.className"
    :label-class-name="column.labelClassName"
    :selectable="column.selectable"
    :reserve-selection="column.reserveSelection || false"
    :row-key="column.rowKey || ''"
    :filters="column.filters"
    :filter-placement="column.filterPlacement"
    :filter-multiple="column.filterMultiple"
    :filter-method="column.filterMethod"
    :filtered-value="column.filteredValue"
    v-on="$listeners"
  >
    <!--自定义表头渲染-->
    <template slot="header" slot-scope="scope">
      <drive-table-render
        v-if="column.renderHeader"
        :scope="scope"
        :render="column.renderHeader"
      />
      <span>{{ scope.column.label }}</span>
    </template>
    <!--自定义表列内容渲染-->
    <template slot-scope="scope">
      <drive-table-render
        v-if="!isSortable"
        :scope="scope"
        :render="column.render"
      />

      <!--拖拽排序内容渲染-->
      <template v-else>
        <drive-table-render
          v-if="column.prop !== 'operation'"
          :scope="scope"
          :render="column.render"
        />
        <drive-sort-table-operation
          v-if="column.prop === 'operation'"
          @moveDown="moveDown"
          @moveUp="moveUp"
          @moveTop="moveTop"
          @moveBottom="moveBottom"
          :scope="scope"
          :dragTableLen="dragTableLen"
        />
      </template>
    </template>
    <!--多层级表头-->
    <template v-if="column.children">
      <DriveTableCloumn
        v-for="(col, index) in column.children.filter(column => !column.hidden)"
        :key="index"
        :column="col"
      />
    </template>
  </el-table-column>
</template>

<script>
import DriveTableRender from './render'
import forced from './forced.js'
import { noData } from '@/filter'
import DriveSortTableOperation from './sortTableOperation'

export default {
  name: 'DriveTableCloumn',
  components: { DriveSortTableOperation, DriveTableRender },
  props: {
    // 表列元素
    column: {
      required: true,
      type: Object
    },
    // 拖拽数据长度
    dragTableLen: {
      type: Number,
      default: 0
    },
    // 开始拖拽
    isSortable: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    column: {
      handler() {
        this.setColumn()
      },
      immediate: true
    }
  },
  methods: {
    moveDown(index, row) {
      this.$emit('moveDown', index, row)
    },
    moveUp(index, row) {
      this.$emit('moveUp', index, row)
    },
    moveTop(index, row) {
      this.$emit('moveTop', index, row)
    },
    moveBottom(index, row) {
      this.$emit('moveBottom', index, row)
    },
    setColumn() {
      if (this.column.type) {
        this.column.renderHeader = forced[this.column.type].renderHeader
        this.column.render =
          this.column.render || forced[this.column.type].renderCell
      }
      if (this.column.formatter) {
        this.column.render = (h, scope) => {
          return (
            <span>
              {scope.column.formatter(
                scope.row,
                scope.column,
                scope.row,
                scope.$index
              )}
            </span>
          )
        }
      }
      if (!this.column.render) {
        this.column.render = (h, scope) => {
          return <span>{noData(scope.row[scope.column.property])}</span>
        }
      }
    }
  }
}
</script>

<style scoped></style>
