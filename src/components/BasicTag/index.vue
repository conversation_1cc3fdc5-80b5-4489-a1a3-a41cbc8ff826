<template>
  <div>
    <div
      v-if="isDot"
      :class="docTextClass"
      class="dot-wrapper flex align-items-center font-size-14"
    >
      <span class="dot" :class="docClass"></span>
      <span>{{ label }}</span>
    </div>
    <el-tag
      v-else
      :type="type"
      :effect="effect"
      :closable="closable"
      @close="closeEvent"
      >{{ label }}</el-tag
    >
  </div>
</template>

<script>
export default {
  name: 'BasicTag',
  props: {
    // el-tag标签type
    type: {
      type: String,
      default: 'primary'
    },
    // el-tag标签主题
    effect: {
      type: String,
      default: 'light'
    },
    // 是否可关闭
    closable: {
      type: Boolean,
      default: false
    },
    // tag值
    label: {
      type: String,
      default: ''
    },
    // 是否为dot标签
    isDot: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  computed: {
    docClass() {
      return 'dot-' + this.type
    },
    docTextClass() {
      return 'dot-text-' + this.type
    }
  },
  methods: {
    closeEvent() {
      this.$emit('closeEvent')
    }
  }
}
</script>

<style lang="scss" scoped>
@import '../../styles/theme/handle.scss';
.dot-wrapper {
  @include font_color(--color-primary);

  &.dot-text-danger {
    @include font_color(--color-danger);
  }

  &.dot-text-warning {
    @include font_color(--color-warning);
  }

  &.dot-text-success {
    @include font_color(--color-success);
  }

  &.dot-text-info {
    @include font_color(--color-info);
  }

  .dot {
    width: 8px;
    height: 8px;
    display: inline-block;
    border-radius: 50%;
    margin-right: 7px;
    @include background_color(--color-primary);

    &.dot-danger {
      @include background_color(--color-danger);
    }

    &.dot-warning {
      @include background_color(--color-warning);
    }

    &.dot-success {
      @include background_color(--color-success);
    }

    &.dot-info {
      @include background_color(--color-info);
    }
  }
}
</style>
