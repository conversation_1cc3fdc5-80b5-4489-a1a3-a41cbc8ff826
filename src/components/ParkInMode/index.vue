<template>
  <dialog-cmp
    title="请选择入园方式"
    :visible.sync="dialogVisible"
    width="666px"
    :haveOperation="false"
  >
    <div class="park-in-container">
      <div class="park-options">
        <div>
          <div class="park-options-title">项目入园</div>
          <div class="park-options-tips">
            需填写完整的入园信息，更多步骤和流程
          </div>
        </div>
        <div class="m-t-21">
          <div
            v-for="item in leftList"
            :key="item.value"
            class="flex align-items-center line-height-22 m-b-8"
          >
            <svg-icon
              class="font-size-16 color-warning"
              icon-class="check-rectangle"
            />
            <div class="font-size-14 m-l-4">{{ item.label }}</div>
          </div>
        </div>
        <div class="btn-main">
          <div class="color-white" @click="parkInTypeHandle(2)">
            <span class="p-r-8">确定</span>
            <svg-icon icon-class="swap-right" />
          </div>
        </div>
      </div>
      <div class="park-options">
        <div>
          <div class="park-options-title">快速入园</div>
          <div class="park-options-tips">直接签订入园合同，流程更短</div>
        </div>
        <div class="m-t-21" style="flex: 1">
          <div
            v-for="item in rightList"
            :key="item.value"
            class="flex align-items-center line-height-22 m-b-8"
          >
            <svg-icon
              class="font-size-16 color-warning"
              icon-class="check-rectangle"
            />
            <div class="font-size-14 m-l-4">{{ item.label }}</div>
          </div>
        </div>
        <div class="btn-main warning-info">
          <div class="color-warning" @click="parkInTypeHandle(3)">
            <span class="p-r-8">确定</span>
            <svg-icon icon-class="swap-right" />
          </div>
        </div>
      </div>
    </div>
  </dialog-cmp>
</template>

<script>
export default {
  name: 'ParkInMode',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      leftList: [
        {
          label: '企业入驻申请表和相关材料',
          value: 1
        },
        {
          label: '项目上会',
          value: 2
        },
        {
          label: '创建账号',
          value: 3
        },
        {
          label: '签订合同',
          value: 4
        },
        {
          label: '办理交房',
          value: 5
        }
      ],
      rightList: [
        {
          label: '创建账号',
          value: 1
        },
        {
          label: '签订合同',
          value: 2
        }
      ]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    parkInTypeHandle(val) {
      this.$emit('parkInTypeHandle', val)
    }
  }
}
</script>

<style lang="scss" scoped>
.park-in-container {
  display: flex;
  justify-content: space-between;
  .park-options {
    width: 300px;
    //height: 185px;
    padding: 16px;
    border: 1px solid;
    @include border_color(--border-color-base);
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: inherit;
    border-radius: 6px;
    .btn-main {
      width: 100%;
      height: 32px;
      margin: 0 auto;
      line-height: 32px;
      background: #ed7b2f;
      border-radius: 3px;
      text-align: center;
      cursor: pointer;
      &.warning-info {
        background: #f9e0c7;
      }
    }
    .park-options-title {
      font-size: 14px;
      font-weight: 600;
      line-height: 22px;
      @include font_color_mix(--color-black, #ffffff, 10%);
    }
    .park-options-tips {
      margin-top: 13px;
      font-size: 14px;
      font-weight: 350;
      @include font_color_mix(--color-black, #ffffff, 40%);
      line-height: 22px;
    }
  }
}
</style>
