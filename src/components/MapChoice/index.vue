<template>
  <div class="map-wrapper w100 h100 relative">
    <!--<div class="tip">-->
    <!--  <input-->
    <!--    v-model="address"-->
    <!--    class="custom-componet-input"-->
    <!--    id="custom-componet-input"-->
    <!--    :placeholder="placeholder"-->
    <!--  />-->
    <!--</div>-->
    <div id="MapContainer"></div>
  </div>
</template>

<script>
import AMapLoader from '@amap/amap-jsapi-loader'
// 安全密钥
window._AMapSecurityConfig = {
  securityJsCode: 'eca92fbd39008698f728c2676c47ed53'
}

export default {
  name: 'MapChoice',
  props: {
    lnglat: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请输入关键词'
    }
  },
  data() {
    return {
      address: '',
      lnglatData: []
    }
  },
  methods: {
    // 初始化地图
    initMap() {
      return new Promise((resolve, reject) => {
        AMapLoader.load({
          key: '942540f0d48c3b481a3578b7568d56dd',
          version: '2.0',
          plugins: [
            'AMap.AutoComplete',
            // 'AMap.PlaceSearch',
            'AMap.Geocoder',
            'AMap.Geolocation'
          ]
        })
          .then(AMap => {
            this.map = new AMap.Map('MapContainer', {
              zoom: 16,
              center: [117.097872, 31.83292]
            })

            // 地图点击监听
            this.mapClickEventlistener()

            // 关键词补全
            // this.autoComplete = new AMap.AutoComplete({
            //   input: 'custom-componet-input'
            // })
            // 自动补全事件监听
            // this.autoCompleteEventlistener()

            // 构造地点查询
            // this.placeSearch = new AMap.PlaceSearch({
            //   map: this.map,
            //   showCover: true
            // })
            // 构造地点查询marker点击
            // this.placeSearchMarkerClickEventlistener()

            // 逆地址解析
            this.geocoder = new AMap.Geocoder({})

            // 自动定位
            this.geolocation = new AMap.Geolocation({
              timeout: 10000, //超过10秒后停止定位，默认：无穷大
              showMarker: false,
              showCircle: false, //定位成功后用圆圈表示定位精度范围，默认：true
              zoomToAccuracy: true, //定位成功后调整地图视野范围使定位位置及精度范围视野内可见，默认：false
              showButton: false //显示定位按钮，默认：true
            })
            // 增加定位按钮控件
            this.map.addControl(this.geolocation)
            // 执行定位
            this.geolocation.getCurrentPosition()

            resolve()
          })
          .catch(e => {
            reject(e)
          })
      })
    },
    // 地图点击事件
    mapClickEventlistener() {
      this.map.on('click', e => {
        const lnglat = [e.lnglat.getLng(), e.lnglat.getLat()]
        this.getAddress(lnglat)
      })
    },
    //  添加标记
    setMarker(lnglat) {
      this.removeMarker()
      let marker = new AMap.Marker({
        position: lnglat,
        label: {
          content: `<div>
            <div>${this.address}</div>
          </div>`,
          offset: [-100, -40]
        }
      })
      marker.setMap(this.map)
      this.markers ? this.markers.push(marker) : (this.markers = [marker])
      // this.map.setFitView()
    },
    // 删除之前后的标记点
    removeMarker() {
      if (this.markers) {
        this.map.remove(this.markers)
      }
    },
    // 自动补全选择完成事件
    // autoCompleteEventlistener() {
    //   this.autoComplete.on('select', e => {
    //     const { adcode, name } = e.poi
    //     this.placeSearch.setCity(adcode)
    //     this.placeSearch.search(name)
    //   })
    // },
    // 构造地点查询marker点击事件
    // placeSearchMarkerClickEventlistener() {
    //   this.placeSearch.on('markerClick', e => {
    //     const { pname, cityname, adname, address } = e.data
    //     const { lng, lat } = e.data.location
    //     const formattedAddress = `${pname}${cityname}${adname}${address}`
    //     this.address = formattedAddress
    //     this.lnglatData = [lng, lat]
    //     this.emitTapMap()
    //   })
    // },
    // 获取逆地址解析信息
    getAddress(lnglat) {
      return new Promise((resolve, reject) => {
        this.geocoder.getAddress(lnglat, (status, result) => {
          if (status === 'complete' && result.regeocode) {
            this.address = result.regeocode.formattedAddress
            this.setMarker(lnglat)
            this.lnglatData = lnglat
            this.emitTapMap()
            resolve()
          } else {
            console.log('查询地址失败，请稍后再试')
            reject()
          }
        })
      })
    },
    // 派发事件
    emitTapMap() {
      this.$emit('tapMap', {
        lnglat: this.lnglatData,
        address: this.address
      })
    }
  },
  computed: {
    hasPropLnglat() {
      return (
        this.lnglat &&
        Array.isArray(this.lnglat) &&
        this.lnglat.length === 2 &&
        // eslint-disable-next-line eqeqeq
        this.lnglat[0] != null &&
        // eslint-disable-next-line eqeqeq
        this.lnglat[1] != null
      )
    }
  },
  watch: {
    lnglat: {
      async handler(value) {
        if (this.hasPropLnglat) {
          await this.initMap()
          await this.getAddress(value)
          this.setMarker(value)
          this.map.setCenter(value)
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    if (!this.hasPropLnglat) {
      this.initMap()
    }
  }
}
</script>

<style lang="scss" scoped>
#MapContainer {
  padding: 0;
  margin: 0;
  width: 100%;
  height: 100%;
}

.tip {
  background-color: #ddf;
  color: #333;
  border: 1px solid silver;
  box-shadow: 3px 4px 3px 0 silver;
  position: absolute;
  top: 10px;
  right: 10px;
  border-radius: 5px;
  overflow: hidden;
  line-height: 20px;
  z-index: 990000000;
  input {
    height: 32px;
    border: 0;
    padding-left: 5px;
    width: 280px;
    border-radius: 3px;
    outline: none;
  }
}

::v-deep .amap-content-body {
  max-width: 320px !important;
}

::v-deep .slot-content {
  padding: 0 !important;
}
</style>
