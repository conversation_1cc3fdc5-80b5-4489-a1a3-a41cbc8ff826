<template>
  <div>
    <el-popover
      placement="top-start"
      :title="title"
      width="352"
      trigger="manual"
      v-model="visible"
      ref="popoverRef"
    >
      <driven-form
        ref="driven-form"
        v-model="fromModel"
        :formConfigure="formConfigure"
      />
      <div class="flex align-items-center justify-content-end">
        <el-button type="info" size="mini" @click="visible = false"
          >取消</el-button
        >
        <el-button type="primary" @click="confirmPopover" size="mini"
          >确定</el-button
        >
      </div>
      <el-button slot="reference" @click="addDict">新增</el-button>
    </el-popover>
  </div>
</template>

<script>
import descriptorMixins from './descriptor'
import { addDict } from '@/api/common'
export default {
  name: 'DictPopover',
  mixins: [descriptorMixins],
  props: {
    type: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      fromModel: {}
    }
  },
  computed: {
    label() {
      const dialogName = Object.create(null)
      dialogName.policy_level = '政策级别'
      dialogName.policy_label = '政策标签'
      dialogName.course_type = '课程类型'
      dialogName.course_label = '课程标签'
      dialogName.activity_type = '活动类型'
      dialogName.park_facility = '配套设施'
      dialogName.org_info_type = '机构类型'
      dialogName.org_product_type = '产品类型'
      dialogName.place_allocation = '场地配置'
      dialogName.information_type = '公告类型'
      dialogName.stadium_type = '项目类型'
      dialogName.stadium_black_type = '违规原因'
      return dialogName[this.type] || '-'
    },

    title() {
      return '新增' + this.label
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.fromModel = {}
      }
    }
  },
  methods: {
    addDict() {
      this.formConfigure.descriptors.label.label = this.label
      this.formConfigure.descriptors.label.rule[0].message =
        '请输入' + this.label
      this.visible = true
    },

    // 新增
    confirmPopover() {
      this.$refs['driven-form'].validate(valid => {
        if (valid) {
          addDict({
            ...this.fromModel,
            dictType: this.type
          }).then(() => {
            this.$toast.success('新增成功')
            this.$emit('refreshDict', this.fromModel.label)
            this.visible = false
          })
        }
      })
    }
  }
}
</script>
