function defaultFormItemConfigure(e) {
  const configure = Object.create(null)
  configure.textarea = {
    resize: 'vertical',
    'show-word-limit': true,
    autosize: { minRows: 2, maxRows: 6 }
  }

  configure['input-number'] = {
    min: 18,
    max: 100
  }

  configure.date = {
    'value-format': 'yyyy-MM-dd',
    type: 'date'
  }

  configure.dateRange = {
    'value-format': 'yyyy-MM-dd',
    'start-placeholder': '开始日期',
    'end-placeholder': '结束日期',
    'range-separator': '至',
    'unlink-panels': true,
    type: 'daterange'
  }

  configure.timeRange = {
    'value-format': 'HH:mm',
    'start-placeholder': '开始日期',
    'end-placeholder': '结束日期',
    'is-range': '至',
    'range-separator': '至'
  }

  configure.transfer = {
    filterable: false,
    'filter-placeholder': '请输入'
  }

  return configure[e] || {}
}

function defaultFormConfigure() {
  const formConfigure = Object.create(null)
  formConfigure.labelWidth = '80px'
  formConfigure.showMessage = true
  formConfigure.showToast = false

  return formConfigure
}

export { defaultFormItemConfigure, defaultFormConfigure }
