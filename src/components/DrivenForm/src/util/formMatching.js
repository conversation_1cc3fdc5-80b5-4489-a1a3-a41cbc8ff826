export function formMatching(form) {
  const items = Object.create(null)
  items.input = 'el-input'
  items.cascader = 'el-cascader'
  items.radio = 'el-radio-group'
  items['input-number'] = 'el-input-number'
  items.select = 'el-select'
  items.checkbox = 'el-checkbox-group'
  items.switch = 'el-switch'
  items.suggestion = 'el-autocomplete'
  items.date = 'el-date-picker'
  items.dateRange = 'el-date-picker'
  items.timeRange = 'el-time-picker'
  items.transfer = 'el-transfer'
  items.component = 'component'
  try {
    return items[form]
  } catch (e) {
    new Error('not supported at the moment')
  }
}
