// 输入数字
const validateNumber = (rule, value, callback, label) => {
  const { required = false } = rule
  if (required) {
    if (!value && value !== 0) callback(new Error(`请输入${label}`))
    if (isNaN(value)) callback(new Error(`${label}必须是数字`))
    callback()
  } else {
    callback()
  }
}

// 手机号
const validatePhone = (rule, value, callback, label) => {
  if (value === '') {
    callback(new Error(`请输入${label || '手机号'}`))
  } else {
    if (/^[1][3,4,5,6,7,8,9][0-9]{9}$/.test(+value)) {
      callback()
    } else {
      callback(new Error(`请输入正确的${label || '手机号'}`))
    }
  }
}

// 车牌号
const validateCar = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入车牌号'))
  } else {
    if (
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}[A-Z0-9挂学警港澳]?$/.test(
        value
      )
    ) {
      callback()
    } else {
      callback(new Error('请输入正确的车牌号'))
    }
  }
}

// 邮箱地址
const validateEmail = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入邮箱地址'))
  } else {
    if (
      /^[A-Za-z\d]+([-_.][A-Za-z\d]+)*@([A-Za-z\d]+[-.])+[A-Za-z\d]{2,4}$/.test(
        value
      )
    ) {
      callback()
    } else {
      callback(new Error('请输入正确的邮箱地址'))
    }
  }
}

// 身份证验证
const validateIdCard = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入身份证'))
  } else {
    if (
      /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(
        +value
      )
    ) {
      callback()
    } else {
      callback(new Error('请输入身份证'))
    }
  }
}

// 银行卡验证
const validateBankCard = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入银行卡号'))
  } else {
    if (/^([1-9]{1})(\d{15}|\d{18}|\d{19})$/.test(value)) {
      callback()
    } else {
      callback(new Error('请输入正确的16、19或20位银行卡号'))
    }
  }
}

// 两位小数验证
const validateDecimal = (rule, value, callback) => {
  if (!value) return callback()
  const val = Number(value)
  if (isNaN(val)) return callback(new Error('请输入数字'))
  if (val > *************) return callback(new Error('数字超出有效范围'))
  if (
    /(^[0-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
      +val
    )
  ) {
    callback()
  } else {
    callback(new Error('最多保留两位小数'))
  }
}

// 三位小数验证
const validateThreeDecimal = (rule, value, callback) => {
  if (!value) return callback()
  const val = Number(value)
  if (isNaN(val)) return callback(new Error('请输入数字'))
  if (
    /(^[0-9]([0-9]+)?(\.[0-9]{1,3})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/.test(
      +val
    )
  ) {
    callback()
  } else {
    callback(new Error('最多保留三位小数'))
  }
}

export default {
  validateNumber,
  validatePhone,
  validateCar,
  validateEmail,
  validateIdCard,
  validateBankCard,
  validateDecimal,
  validateThreeDecimal
}
