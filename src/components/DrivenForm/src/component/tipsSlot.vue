<script>
export default {
  name: 'TipsSlot',
  functional: true,
  props: {
    customTips: {
      type: Function || String
    }
  },
  render: (h, ctx) => {
    const customTips = ctx.props.customTips
    const tipIsFn = typeof customTips === 'function'
    return h(
      'div',
      {
        style: {
          'font-size': '12px',
          color: 'rgba(0, 0, 0, .4)',
          'margin-top': '7px'
        }
      },
      [tipIsFn ? customTips(h, ctx) : customTips]
    )
  }
}
</script>
