<script>
import { formMatching } from '../util/formMatching'
import { defaultFormItemConfigure } from '../util/defaultFormConfigure'
import { findTypeDescriptor } from '../util/findTypeDescriptor'

export default {
  name: 'FormComponent',
  props: {
    // v-model 支持
    value: {
      required: true
    },
    // 表单元素数据
    descriptor: {
      required: true
    }
  },
  inject: ['dataSource'],
  data() {
    return {
      // form表单子元素
      nestingComponentName: {
        select: 'option',
        radio: 'radio',
        checkbox: 'checkbox'
      }
    }
  },
  computed: {
    // v-model 支持
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 组件name
    componentName() {
      const { form, componentName } = this.descriptor
      return form === 'component' ? componentName : formMatching(form)
    }
  },
  methods: {
    // 嵌套
    nestingComponent(h) {
      const { form, slots = null, props = {} } = this.descriptor
      const nestingName =
        (this.nestingComponentName[form] === 'radio'
          ? props.button
            ? 'radio-button'
            : 'radio'
          : this.nestingComponentName[form]) || null

      let children = []

      if (this.$slots.default) {
        children = children.concat(this.$slots.default)
      }

      if (slots) {
        children = children.concat(slots(h, this))
      }

      return nestingName ? this._genComponentOptions(h, nestingName) : children
    },

    // 生成radioOptions
    _genComponentOptions(h, nestingName) {
      const { options = [] } = this.descriptor
      return options.map(item => {
        const { label, value, disabled = false, labelHtml } = item
        return h(
          `el-${nestingName}`,
          {
            props: {
              label: nestingName === 'option' ? label : value,
              value,
              key: value,
              disabled
            }
          },
          [
            h('span', {
              domProps: {
                innerHTML: labelHtml || label
              }
            })
          ]
        )
      })
    }
  },
  render(h) {
    const {
      placeholder,
      rule = {},
      attrs = {},
      props = {},
      directives = [],
      expandKey = {},
      events = {},
      options = [],
      render,
      form,
      disabled: singleDisabled,
      slot = {},
      scopedSlots = {}
    } = this.descriptor

    const { message = '' } = findTypeDescriptor(rule)
    const formPlaceholder = placeholder || message
    const isTextarea = form === 'input' && props.type === 'textarea'
    const defaultConfig = defaultFormItemConfigure(isTextarea ? 'textarea' : form)

    const { disabled: dataSourceDisabled } = this.dataSource
    const disabled = singleDisabled || dataSourceDisabled

    let children = this.nestingComponent(h)

    const appendSlot = slot.append
    const prependSlot = slot.prepend

    if (appendSlot || prependSlot) {
      if (prependSlot) {
        children.unshift(
          h(
            'template',
            { slot: 'prepend' },
            [typeof prependSlot === 'string' ? prependSlot : h(prependSlot)]
          )
        )
      }

      if (appendSlot) {
        children.push(
          h(
            'template',
            { slot: 'append' },
            [typeof appendSlot === 'string' ? appendSlot : h(appendSlot)]
          )
        )
      }
    }

    return render
      ? render(h, this, disabled)
      : h(
        this.componentName,
        {
          props: {
            value: this._value,
            ...defaultConfig,
            ...props,
            options,
            disabled
          },
          attrs: {
            placeholder: formPlaceholder,
            ...attrs
          },
          on: {
            input: value => {
              this._value = value
            },
            ...events
          },
          directives,
          ...expandKey,
          scopedSlots
        },
        children
      )
  }
  // render(h) {
  //   const {
  //     placeholder,
  //     rule = {},
  //     attrs = {},
  //     props = {},
  //     directives = [],
  //     expandKey = {},
  //     events = {},
  //     options = [],
  //     render,
  //     form,
  //     disabled: singleDisabled
  //   } = this.descriptor
  //   // placeholder 默认rule里的message
  //   const { message = '' } = findTypeDescriptor(rule)
  //   const formPlaceholder = placeholder || message
  //   // 日期范围选择器 type
  //   const isTextarea = form === 'input' && props.type === 'textarea'
  //   const defaultConfig = defaultFormItemConfigure(
  //     isTextarea ? 'textarea' : form
  //   )
  //   // 禁用
  //   const { disabled: dataSourceDisabled } = this.dataSource
  //   const disabled = singleDisabled || dataSourceDisabled
  //
  //   return render
  //     ? render(h, this, disabled)
  //     : h(
  //         this.componentName,
  //         {
  //           props: {
  //             value: this._value,
  //             ...defaultConfig,
  //             ...props,
  //             options,
  //             disabled
  //           },
  //           attrs: {
  //             placeholder: formPlaceholder,
  //             ...attrs
  //           },
  //           on: {
  //             input: value => {
  //               this._value = value
  //             },
  //             ...events
  //           },
  //           directives,
  //           ...expandKey
  //         },
  //         this.nestingComponent(h)
  //       )
  // }
}
</script>
<style lang="scss" scoped>
.el-cascader,
.el-select {
  width: 100% !important;
}

:deep(.el-range-separator) {
  width: 19px !important;
}

.el-date-editor {
  width: 100%;
}
.el-input-number--small {
  width: 240px;
}
</style>
