<template>
  <div class="flex">
    <el-form-item class="flex-item" v-bind="attrs">
      <form-component v-model="_value" :descriptor="$attrs.descriptor" />
      <form-item-slot
        v-if="customLabel"
        slot="label"
        :customLabel="customLabel"
      />
    </el-form-item>

    <form-button-slot v-if="customRight" :customRight="customRight" />
  </div>
</template>

<script>
import FormComponent from './formComponent'
import FormItemSlot from './formItemSlot'
import FormButtonSlot from './formButton'

export default {
  name: 'FormItem',
  inject: ['dataSource'],
  props: {
    // v-model 支持
    value: {
      required: true
    }
  },
  components: {
    FormComponent,
    FormItemSlot,
    FormButtonSlot
  },
  data() {
    return {}
  },
  computed: {
    attrs() {
      const { prop, descriptor } = this.$attrs
      const { label } = descriptor
      return { label, prop }
    },
    // v-model 支持
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    // 自定义label
    customLabel() {
      return this.$attrs.descriptor.customLabel || null
    },
    // 自定义按钮
    customRight() {
      return this.$attrs.descriptor.customRight || null
    }
  },
  watch: {
    _value: {
      handler() {
        // 处理blur校验
        const { descriptor } = this.$attrs
        const { rule } = descriptor
        if (!rule)
          return this.dataSource.drivenForm.validateField(this.$attrs.prop)
        if (rule instanceof Array) {
          rule.forEach(item => {
            if (
              !item.trigger ||
              item.trigger === 'change' ||
              item.trigger.includes('change')
            ) {
              this.dataSource.drivenForm.validateField(this.$attrs.prop)
            }
          })
        } else {
          if (
            !rule.trigger ||
            rule.trigger === 'change' ||
            rule.trigger.includes('change')
          ) {
            this.dataSource.drivenForm.validateField(this.$attrs.prop)
          }
        }
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item__error) {
  position: static;
}
.el-form-item {
  margin-bottom: 0;
}

.flex-item {
  flex: 1;
}
</style>
