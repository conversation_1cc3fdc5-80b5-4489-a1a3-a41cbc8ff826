<template>
  <div>
    <el-form
      ref="driven-form"
      :model="_value"
      :rules="drivenFormRules"
      :label-position="labelPosition"
      :validate-on-rule-change="false"
      v-bind="formAttrs"
    >
      <el-row :gutter="gutter" type="flex">
        <el-col
          v-for="(descriptor, key) in descriptorsCopy"
          :class="key + 'Key'"
          :key="key"
          :span="_genFormSpan(descriptor)"
        >
          <div class="form-item-container">
            <driven-form-item
              :descriptor="descriptor"
              v-model="_value[key]"
              :prop="key"
            />
            <div class="font-14 custom-tips" :style="tipStyle">
              <tips-slot
                v-if="descriptor.customTips"
                :customTips="descriptor.customTips"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { defaultFormConfigure } from '../util/defaultFormConfigure'
import { humpTurnHyphen } from '../util/transformation'
import validateData from '../util/validate'
import { findTypeDescriptor } from '../util/findTypeDescriptor'
import DrivenFormItem from './formItem'
import TipsSlot from './tipsSlot'

export default {
  name: 'DrivenForm',
  components: {
    DrivenFormItem,
    TipsSlot
  },
  props: {
    // v-model 支持
    value: {
      type: Object,
      required: true
    },
    // 各表单元素数据
    formConfigure: {
      type: Object,
      required: true,
      default: () => ({})
    },
    // 左右间距
    gutter: {
      type: Number,
      default: 20
    },
    // 全部禁用
    disabled: {
      type: Boolean,
      default: false
    },
    labelPosition: {
      type: String,
      default: 'right'
    }
  },
  provide() {
    return {
      dataSource: this
    }
  },
  data() {
    return {
      drivenForm: null, // 表单实例
      drivenFormRules: {}, //表单规则
      formAttrs: {}, // 白哦但form的props
      descriptors: {} //表单结构
    }
  },
  watch: {
    formConfigure: {
      handler(val) {
        // 初始化form参数
        val && this.splitFormConfigure()
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    // v-model 支持
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    descriptorsCopy() {
      const copyData = {}
      for (let key in this.descriptors) {
        const descriptor = this.descriptors[key]
        const { hidden, rule = {} } = descriptor
        if (!hidden) {
          copyData[key] = descriptor
        } else {
          const { type } = findTypeDescriptor(rule)
          if (type === 'array') this.$set(this._value, key, [])
          if (type === 'object') this.$set(this._value, key, {})
          if (type === 'string' || type === 'number') {
            delete this._value[key]
          }
        }
      }

      // this.clearValidate()
      return copyData
    },

    // tips的样式
    tipStyle() {
      return {
        'margin-left':
          this.labelPosition === 'top' ? 0 : this.formAttrs['label-width'],
        'min-height': '16px'
      }
    }
  },
  created() {
    this.setValue()
  },
  mounted() {
    this.drivenForm = this.$refs['driven-form']
  },
  methods: {
    // 初始化value值
    setValue() {
      const { descriptors } = this.formConfigure
      for (let k in descriptors) {
        const { rule = {} } = descriptors[k]
        const { type } = findTypeDescriptor(rule)
        if (this._value[k]) return
        if (type === 'array') this.$set(this._value, k, [])
        if (type === 'object') this.$set(this._value, k, {})
      }
    },
    // 拆分表单配置
    splitFormConfigure() {
      const { descriptors, ...args } = this.formConfigure
      const formAttrs = {
        ...defaultFormConfigure(),
        ...args
      }
      this._genFormProps(formAttrs)
      this.$set(this, 'drivenFormRules', this.setTrigger(descriptors))
      this.$set(this, 'descriptors', descriptors)
    },

    // 生产form的props
    _genFormProps(formAttrs) {
      const attrs = {}
      for (let key in formAttrs) {
        attrs[humpTurnHyphen(key)] = formAttrs[key]
      }
      this.$set(this, 'formAttrs', attrs)
    },

    // 初始化表单规则
    setTrigger(descriptors) {
      const drivenFormRules = {}
      if (!descriptors) return drivenFormRules
      for (let key in descriptors) {
        const descriptor = descriptors[key]
        drivenFormRules[key] = this._genFormRules(descriptor)
      }
      return drivenFormRules
    },

    // 生成表单验证
    _genFormRules(descriptor) {
      const { rule = {}, form, label = '' } = descriptor
      if (rule instanceof Array) {
        return rule.map(item => {
          return {
            ...this._genSingleFormRule(item, form, label)
          }
        })
      } else {
        return {
          ...this._genSingleFormRule(rule, form, label)
        }
      }
    },

    // 生产表单宽度span
    _genFormSpan(descriptor) {
      const { span = 24 } = descriptor
      return span
    },

    // 单个fromRule
    _genSingleFormRule(rule, form, label) {
      const { type, validator, trigger } = rule || {}
      const defaultTrigger = ['blur', 'change']
      const isInput = type === 'number'
      const isNumberInput = form === 'input'
      const isNumberSelect = form === 'select'
      const singleRule = { ...rule }
      if (!trigger) {
        singleRule.trigger = defaultTrigger
      }
      const isValidatorStr = typeof validator === 'string'
      // 数字类型添加检验 和 内置的validator
      if (isInput && isNumberInput && !validator) {
        delete singleRule.message
        singleRule.validator = (rule, value, callback) => {
          validateData.validateNumber(rule, value, callback, label)
        }
      }
      // 字数类型非必填select跳过校验
      if (isInput && isNumberSelect && !singleRule.required && !validator) {
        delete singleRule.message
        singleRule.validator = (rule, value, callback) => {
          callback()
        }
      }
      // 内置的validator
      if (isValidatorStr) {
        try {
          delete singleRule.message
          singleRule.validator = (rule, value, callback) => {
            validateData[validator](rule, value, callback, label)
          }
        } catch (e) {
          new Error(e)
        }
      }
      if (isInput) singleRule.transform = Number
      return singleRule
    },

    // 手动重置参数值为number
    resetParamsToNumber() {
      return new Promise(resolve => {
        for (let key in this._value) {
          const { type, hidden = false } = this.descriptors[key]
            ? findTypeDescriptor(this.descriptors[key].rule)
            : {}
          if (!hidden) {
            const typeIsNumber = type === 'number'
            const value = this._value[key]
            const valueIsNumber = typeof value === 'string'
            if (typeIsNumber && valueIsNumber && !isNaN(this._value[key])) {
              // this._value[key]可能是个空的字符串
              this._value[key] =
                this._value[key] === ''
                  ? this._value[key]
                  : parseFloat(this._value[key])
            }
          }
        }
        resolve()
      })
    },

    // 验证方法
    validate(func) {
      this.resetParamsToNumber().then(() => {
        if (typeof func === 'function') {
          // 回调函数形式
          this.drivenForm.validate((valid, err) => {
            !valid &&
              this.formAttrs['show-toast'] &&
              this.$message.error(err[Object.keys(err)[0]][0].message)
            func(valid)
          })
        } else {
          // promise形式
          return new Promise(resolve => {
            this.drivenForm.validate((valid, err) => {
              !valid &&
                this.formAttrs['show-toast'] &&
                this.$message.error(err[Object.keys(err)[0]][0].message)
              resolve(valid)
            })
          })
        }
      })
    },
    // 重置表单
    resetFields() {
      this.drivenForm.resetFields()
    },
    // 清除验证规则内容
    clearValidate() {
      this.drivenForm && this.drivenForm.clearValidate()
    },
    validateField(props, callback) {
      this.drivenForm.validateField(props, callback)
    }
  }
}
</script>

<style lang="scss" scoped>
.form-item-container {
  margin-bottom: 8px;
}

.el-row--flex {
  flex-wrap: wrap;
}
</style>
