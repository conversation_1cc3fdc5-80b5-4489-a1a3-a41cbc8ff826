<template>
  <div class="dots-container">
    <el-button
      class="operate-item operator-item-left"
      @click="moveChange('pre')"
      :disabled="btnDisabled"
    >
      <svg-icon icon-class="chevron-left" />
    </el-button>
    <div class="dots-wrapper" ref="dotsWrapper">
      <div class="dots-content" ref="dotsContent">
        <div
          v-for="(item, index) in sliders"
          :key="index"
          :ref="index"
          class="dots-page"
          :class="{ active: currentPageIndex === index }"
          @click="sliderClick(index)"
        >
          <el-image class="item-img" :src="item.path" fit="contain"></el-image>
        </div>
      </div>
    </div>
    <el-button
      class="operate-item operator-item-right"
      @click="moveChange('next')"
      :disabled="btnDisabled"
    >
      <svg-icon icon-class="chevron-right" />
    </el-button>
  </div>
</template>

<script>
import BScroll from 'better-scroll'

export default {
  name: 'ImgSliderDots',
  props: {
    sliders: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      slider: null,
      currentPageX: 0,
      currentPageIndex: 0,
      btnDisabled: true
    }
  },
  watch: {
    sliders: {
      async handler() {
        await this.$nextTick()
        this.currentPageX = 0
        this.currentPageIndex = 0
        this.initSlider()
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    this.slider && this.slider.destroy()
    this.slider = null
  },
  methods: {
    sliderDestroy() {
      this.slider && this.slider.destroy()
      this.slider = null
    },
    sliderClick(index) {
      this.currentPageIndex = index
      this.$emit('sliderClick', index)
    },
    // 左右切换
    moveChange(type) {
      const maxScrollX = this.slider.maxScrollX
      const onceScroll = 148
      this.currentPageX =
        type === 'pre'
          ? this.currentPageX + onceScroll
          : this.currentPageX - onceScroll
      if (this.currentPageX >= onceScroll) this.currentPageX = 0
      if (this.currentPageX <= maxScrollX) this.currentPageX = maxScrollX
      this.slider.scrollTo(this.currentPageX, 0, 300)
    },
    // 滚动初始化
    initSlider() {
      this.sliderDestroy()
      if (!this.sliders || !this.sliders.length || !this.$refs.dotsWrapper)
        return false
      this.slider = new BScroll(this.$refs.dotsWrapper, {
        click: true,
        scrollX: true,
        scrollY: false,
        momentum: false,
        bounce: false,
        stopPropagation: true, //取消冒泡
        probeType: 2
      })
      this.slider.on('scrollEnd', page => {
        this.currentPageX = page.x
      })
      const dotsWrapperWidth = this.$refs.dotsWrapper.offsetWidth
      const dotsContentWidth = this.$refs.dotsContent.offsetWidth
      this.btnDisabled = dotsWrapperWidth >= dotsContentWidth
    }
  }
}
</script>

<style scoped lang="scss">
.dots-container {
  height: 92px;
  display: flex;
  justify-content: space-between;
  .operate-item {
    width: 16px;
    height: 100%;
    background: #ffffff;
    border-radius: 3px;
    border: 1px solid #e7e7e7;
    color: rgba(0, 0, 0, 0.6);
    font-size: 16px;
    padding: 0;
    &.is-disabled {
      opacity: 0.5;
    }
  }
  .dots-wrapper {
    width: calc(100% - 48px);
    height: 100%;
    flex-shrink: 0;
    overflow: hidden;
    .dots-content {
      width: max-content;
      height: 100%;
      position: relative;
      .dots-page {
        display: inline-block;
        width: 140px;
        height: 100%;
        border-radius: 3px;
        overflow: hidden;
        transform: translate3d(0, 0, 0);
        backface-visibility: hidden;
        cursor: pointer;
        position: relative;
        &::after {
          display: block;
          content: '';
          width: 100%;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.4);
        }
        &.active::after {
          display: none;
        }
        & + .dots-page {
          margin-left: 8px;
        }
        .item-img {
          width: 100%;
          height: 100%;
          display: block;
        }
      }
    }
  }
}
</style>
