<template>
  <div class="img-viewer-container" ref="imgViewerContainer">
    <div
      class="img-viewer-wrapper"
      :class="{ 'is-full': isFullscreen || isFill }"
    >
      <div class="tabs-wrapper" v-if="list && list.length > 1">
        <basic-switch-tab :tabs="list" @change="changeTab" :current="current" />
      </div>
      <div class="full-wrapper" v-if="!isFill">
        <svg-icon
          :icon-class="isFullscreen ? 'fullscreen-exit' : 'fullscreen'"
          @click="toggleScreenFull"
        />
      </div>
      <div
        class="slide-operator slide-left"
        @click="moveNext(0)"
        :class="{ disabled: carouselBtnDisabled }"
      >
        <svg-icon icon-class="chevron-left" />
      </div>
      <div class="slide-wrapper" ref="sliderWrapper" :key="sliderWrapperKey">
        <div class="slide-content">
          <div
            v-for="(item, index) in sliders"
            :key="index"
            :ref="index"
            class="slide-page"
          >
            <el-image
              class="item-img"
              :src="item.path"
              fit="contain"
            ></el-image>
          </div>
        </div>
      </div>
      <div
        class="slide-operator slide-right"
        @click="moveNext(1)"
        :class="{ disabled: carouselBtnDisabled }"
      >
        <svg-icon icon-class="chevron-right" />
      </div>
      <div
        class="copy-tips"
        @click="copyHandle($event)"
        :class="{ disabled: copyDisabled }"
      >
        复制图片链接
      </div>
    </div>
    <div class="m-t-8">
      <img-slider-dots
        ref="dotsSlider"
        :sliders="sliders"
        @sliderClick="sliderClick"
      />
    </div>
  </div>
</template>

<script>
import BScroll from 'better-scroll'
import ImgSliderDots from './imgSliderDots'
import BasicSwitchTab from '@/components/BasicSwitchTab'
import screenfull from 'screenfull'

export default {
  name: 'SliderImgViewer',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    current: {
      default: 1
    },
    isFill: {
      type: Boolean,
      default: false
    }
  },
  components: { BasicSwitchTab, ImgSliderDots },
  data() {
    return {
      sliders: [],
      currentPageIndex: 0,
      slider: null,
      isFullscreen: false,
      sliderWrapperKey: Math.random()
    }
  },
  computed: {
    copyDisabled() {
      const length = this.list.filter(
        item => item.pic && item.pic.length
      ).length
      return !length
    },
    carouselBtnDisabled() {
      return !this.sliders.length || this.sliders.length < 2
    }
  },
  watch: {
    currentPageIndex() {
      // 跳转到对应数字的div区域
      this.slider.scrollToElement(this.$refs[this.currentPageIndex][0], 300)
    },
    list: {
      handler() {
        this.changeTab(this.current)
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.initScreenFull()
  },
  beforeDestroy() {
    this.sliderDestroy()
    this.destroyScreenFull()
  },
  methods: {
    copyHandle($event) {
      if (this.copyDisabled) return false
      this.$emit('copyHandle', $event)
    },
    sliderDestroy() {
      this.slider && this.slider.destroy()
      this.slider = null
    },
    toggleScreenFull() {
      if (!screenfull.isEnabled) {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
        return false
      }
      if (!screenfull.isFullscreen) {
        const element = this.$refs.imgViewerContainer
        screenfull.request(element)
      } else {
        screenfull.exit()
      }
    },
    screenFullChange() {
      this.isFullscreen = screenfull.isFullscreen
      this.initSlider()
    },
    initScreenFull() {
      if (screenfull.isEnabled) {
        screenfull.on('change', this.screenFullChange)
      }
    },
    destroyScreenFull() {
      if (!screenfull.isEnabled) {
        screenfull.off('change', this.screenFullChange)
      }
    },
    changeTab(e) {
      const row = this.list.find(item => item.value === e)
      if (!row) return false
      this.sliders = row.pic || []
      this.$emit('tabChange', e)
      this.sliderWrapperKey = Math.random()
      this.initSlider()
    },
    sliderClick(index) {
      this.currentPageIndex = index
    },
    // 左右切换
    moveNext(val) {
      if (this.carouselBtnDisabled) return false
      if (val === 0) {
        if (this.currentPageIndex === 0) {
          this.currentPageIndex = this.sliders.length
        }
        this.currentPageIndex = this.currentPageIndex - 1
      } else {
        if (this.sliders.length - 1 === this.currentPageIndex) {
          this.currentPageIndex = -1
        }
        this.currentPageIndex = this.currentPageIndex + 1
      }
    },
    async initSlider() {
      await this.$nextTick()
      this.sliderDestroy()
      if (!this.sliders || !this.sliders.length || !this.$refs.sliderWrapper)
        return false
      this.slider = new BScroll(this.$refs.sliderWrapper, {
        scrollX: true,
        scrollY: false,
        probeType: 3,
        slide: {
          threshold: 100,
          autoplay: false
        },
        useTransition: true,
        momentum: false,
        bounce: false,
        stopPropagation: true
      })
      this.currentPageIndex = 0
      this.slider.on('scrollEnd', this._onScrollEnd)
    },
    _onScrollEnd() {
      this.currentPageIndex = this.slider.getCurrentPage().pageX
      this.$refs.dotsSlider.sliderClick(this.currentPageIndex)
    }
  }
}
</script>

<style scoped lang="scss">
.img-viewer-container {
  user-select: none;
  height: 100%;
  .img-viewer-wrapper {
    background: #000;
    padding: 0 140px;
    position: relative;
    height: 489px;
    border-radius: 6px;
    &.is-full {
      height: calc(100vh - 100px);
    }
    .tabs-wrapper {
      position: absolute;
      top: 8px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 1;
    }
    .full-wrapper {
      position: absolute;
      right: 14px;
      top: 14px;
      color: #fff;
      cursor: pointer;
    }
    .slide-operator {
      color: #ffffff;
      cursor: pointer;
      position: absolute;
      font-size: 16px;
      top: 50%;
      transform: translateY(-50%);
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
      &.slide-left {
        left: 16px;
      }
      &.slide-right {
        right: 16px;
      }
    }
    .slide-wrapper {
      height: 100%;
      overflow: hidden;
      .slide-content {
        width: max-content;
        height: 100%;
        position: relative;
        .slide-page {
          display: inline-block;
          width: 100%;
          height: 100% !important;
          transform: translate3d(0, 0, 0);
          backface-visibility: hidden;
          .item-img {
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }
    .copy-tips {
      position: absolute;
      bottom: 8px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.5);
      border-radius: 3px;
      padding: 3px 6px;
      font-weight: 350;
      font-size: 14px;
      color: #ffffff;
      line-height: 22px;
      cursor: pointer;
      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}
:deep(.switch-tab-wrapper) {
  background: rgba(0, 0, 0, 0.5) !important;
  .item-btn {
    color: #fff !important;
  }
}
</style>
