import request from '@/utils/request'

// 查询当前用户任务
export function getCurrentTask(orderId) {
  return request({
    url: `/common/flow/getCurrentTask?orderId=${orderId}`,
    method: 'get'
  })
}

// 查询当前任务节点表单字段
export function getWorkFlowFields(orderId) {
  return request({
    url: `/common/flow/getWorkFlowFields?orderId=${orderId}`,
    method: 'get'
  })
}

// 查询历史流程节点
export function findHistoryTask(orderId) {
  return request({
    url: `/common/flow/findHistoryTask?orderId=${orderId}`,
    method: 'get'
  })
}

// 获取退回流程节点
export function findBackTask(orderId) {
  return request({
    url: `/common/flow/findBackNode?orderId=${orderId}`,
    method: 'get'
  })
}

// 流程审核
export function examineFlow(data) {
  return request({
    url: `/common/flow/examine`,
    method: 'post',
    data
  })
}
// 短信通知
export function sendSmsMsg(params) {
  return request({
    url: `/msg/message-info/send_sms_msg`,
    method: 'get',
    params
  })
}
