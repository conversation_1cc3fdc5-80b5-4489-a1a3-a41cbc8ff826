<template>
  <el-form-item v-if="!itemData.hidden" :label="label" :prop="prop">
    <component :is="componentName" v-model="_value" v-bind="itemData.type" />
  </el-form-item>
</template>

<script>
import WorkflowFormInput from './inputCmp.vue'
import WorkflowFormRadio from './radioCmp.vue'
import WorkflowFormUpload from './uploadCmp.vue'
import WorkflowFormDownload from './downloadcmp.vue'
import WorkflowFormSelect from './selectCmp.vue'

export default {
  name: 'workflowItem',
  inject: ['FlowForm'],
  props: {
    value: {
      required: true
    },
    itemData: {
      type: Object
    }
  },
  components: {
    WorkflowFormInput,
    WorkflowFormRadio,
    WorkflowFormUpload,
    WorkflowFormDownload,
    WorkflowFormSelect
  },
  data() {
    return {}
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
        this.FlowForm.workflowForm.validateField(this.itemData.field)
      }
    },
    // formItem的prop
    prop() {
      return this.itemData.field
    },
    // label 提示
    label() {
      return this.itemData.type.label
    },
    // 组件name
    componentName() {
      const componentObject = Object.create(null)
      const { form } = this.itemData.type
      componentObject.input = 'WorkflowFormInput'
      componentObject.textarea = 'WorkflowFormInput'
      componentObject.upload = 'WorkflowFormUpload'
      componentObject.radio = 'WorkflowFormRadio'
      componentObject.download = 'WorkflowFormDownload'
      componentObject.select = 'WorkflowFormSelect'
      return componentObject[form]
    }
  }
}
</script>
