<template>
  <el-input
    :type="inputType"
    v-model="_value"
    :placeholder="message"
    :maxlength="maxlength"
    :show-word-limit="isTextarea === 'textarea'"
    :autosize="autosize"
  ></el-input>
</template>

<script>
export default {
  name: 'InputCmp',
  props: {
    value: {
      required: true
    },
    message: String
  },
  data() {
    return {}
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    isTextarea() {
      const { form } = this.$attrs
      return form === 'textarea' ? 'textarea' : 'text'
    },
    // 输入框类型
    inputType() {
      return this.isTextarea ? 'textarea' : 'text'
    },
    // textarea最大字数
    maxlength() {
      return this.isTextarea ? 200 : 0
    },
    // textarea高度限制
    autosize() {
      return this.isTextarea ? { minRows: 5, maxRows: 10 } : {}
    }
  }
}
</script>
