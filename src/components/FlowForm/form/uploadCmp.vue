<template>
  <el-upload
    class="upload-demo"
    :action="action"
    multiple
    :file-list="fileList"
    :headers="headers"
    :before-upload="beforeUpload"
    :on-success="uploadSuccess"
    :data="_uploadData"
    :on-preview="previewAttach"
    :on-remove="handleRemove"
  >
    <el-button size="small" type="primary">点击上传</el-button>
  </el-upload>
</template>

<script>
import { uploadUrl } from '@/api/common'
import { getBaseHeader } from '@/utils/request'

export default {
  name: 'UploadCmp',
  props: {
    value: {
      required: true
    },
    uploadData: {
      type: Object
    }
  },
  data() {
    return {
      fileList: this.value || [],
      action: uploadUrl,
      headers: getBaseHeader()
    }
  },
  computed: {
    // 输入框类型
    _uploadData() {
      return { ...this.uploadData }
    }
  },
  methods: {
    // 参数携带文件名称
    beforeUpload(file) {
      const { name } = file
      this._uploadData.name = name
    },
    // 预览
    previewAttach(file) {
      const winOpen = window.open()
      winOpen.location.href = file.url
    },
    // 上传成功
    uploadSuccess(file) {
      if (file.code === 200 && !!file.data) {
        this.fileList.push(file.data)
        this.$emit('input', this.fileList)
      } else {
        // todo
        this.$toast.error('附件上传失败')
      }
    },
    // 删除附件
    handleRemove(file) {
      const index = this.fileList.findIndex(item => item.id === file.id)
      this.fileList.splice(index, 1)
      this.$emit('input', this.fileList)
    }
  }
}
</script>
