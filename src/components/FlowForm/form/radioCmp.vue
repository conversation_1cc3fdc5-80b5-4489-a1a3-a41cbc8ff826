<template>
  <el-radio-group v-model="_value">
    <el-radio
      v-for="(item, index) in optionsCopy"
      :key="index"
      :label="item.value"
      >{{ item.label }}</el-radio
    >
  </el-radio-group>
</template>

<script>
export default {
  name: 'RadioCmp',
  props: {
    value: {
      required: true
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    },
    optionsCopy() {
      return this.options.map(item => {
        return {
          label: item.label,
          value: Number(item.value)
        }
      })
    }
  }
}
</script>
