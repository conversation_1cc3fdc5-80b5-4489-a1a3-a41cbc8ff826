<template>
  <div>
    <el-form
      ref="workflow-form"
      :model="_value"
      :rules="rules"
      :validate-on-rule-change="false"
      label-position="top"
      v-bind="$attrs"
    >
      <workflow-item
        v-for="(item, index) in worKFlowFieldCopy"
        :key="index"
        v-model="_value[item.field]"
        :item-data="item"
      />
    </el-form>
  </div>
</template>

<script>
import WorkflowItem from './item.vue'
export default {
  name: 'workflowForm',
  props: {
    value: {
      required: true
    },
    worKFlowField: {
      type: Array,
      default: () => []
    }
  },
  provide() {
    return {
      FlowForm: this
    }
  },
  components: {
    WorkflowItem
  },
  data() {
    return {
      workflowForm: null,
      worKFlowFieldCopy: [],
      returnOptions: {
        field: 'jumpStep',
        hidden: true,
        type: {
          label: '退回节点',
          form: 'select',
          required: true,
          message: '请选择退回节点',
          type: 'string',
          options: [
            {
              label: '通过',
              value: 1
            },
            {
              label: '退回',
              value: 2
            },
            {
              label: '拒绝',
              value: 3
            }
          ]
        }
      },
      // renderData: [
      //   {
      //     field: 'a',
      //     type: {
      //       label: '审核意见',
      //       form: 'radio',
      //       required: true,
      //       message: '请选择审核意见',
      //       type: 'number',
      //       options: [
      //         {
      //           label: '通过',
      //           value: 1
      //         },
      //         {
      //           label: '退回',
      //           value: 2
      //         },
      //         {
      //           label: '拒绝',
      //           value: 2
      //         }
      //       ]
      //     }
      //   },
      //   {
      //     field: 'name',
      //     type: {
      //       label: '审核意见',
      //       form: 'input',
      //       required: true,
      //       message: '请输入审核意见'
      //     }
      //   },
      //   {
      //     field: 'note',
      //     type: {
      //       label: '备注',
      //       form: 'textarea',
      //       required: true,
      //       message: '请输入备注'
      //     }
      //   },
      //   {
      //     field: 'upload',
      //     type: {
      //       label: '上传',
      //       form: 'upload',
      //       required: true,
      //       message: '请上传附件',
      //       uploadData: { a: 1 }
      //     }
      //   },
      //   {
      //     field: 'download',
      //     type: {
      //       form: 'download',
      //       url: 'demo'
      //     }
      //   }
      // ],
      rules: {}
    }
  },
  computed: {
    _value: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
    worKFlowField: {
      handler(val) {
        if (val && val.length) {
          this.worKFlowFieldCopy = JSON.parse(JSON.stringify(val))
          // this.worKFlowFieldCopy.splice(1, 0, this.returnOptions)
          this.setRules()
        }
      },
      immediate: true,
      deep: true
    }
    // '_value.result': {
    //   handler(val) {
    //     const returnData = this.worKFlowFieldCopy[1]
    //     if (val === 2) {
    //       returnData.hidden = false
    //     } else {
    //       returnData.hidden = true
    //       this._value[returnData.field] = ''
    //     }
    //   }
    // }
  },
  methods: {
    // 初始化表校验
    setRules() {
      const rules = {}
      this.worKFlowFieldCopy.forEach(item => {
        rules[item.field] = { ...item.type }
        item.trigger = ['blur', 'change']
        item.type.default &&
          this.$set(this._value, item.field, item.type.default)
      })
      this.rules = rules
    }
  },
  mounted() {
    this.workflowForm = this.$refs['workflow-form']
  }
}
</script>
