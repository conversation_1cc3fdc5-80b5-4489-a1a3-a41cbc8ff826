<template>
  <div class="record-container">
    <div v-if="historyListCopy.length">
      <div
        v-for="(item, index) in historyListCopy"
        :key="index"
        class="record-wrapper flex"
        :class="{ light: item.lightFlag }"
      >
        <div class="left">
          <div
            v-if="index < historyListCopy.length - 1"
            class="record-line"
          ></div>
          <div
            class="record-circle"
            :class="{ 'padding-user': item.activityTask }"
          >
            <span v-if="item.readyUserList && item.readyUserList.length">
              <span v-if="item.readyUserList.length > 1">群</span>
              <span v-else>{{ item.readyUserList[0].nickname.charAt(0) }}</span>
            </span>
            <span v-else>{{ item.user?.nickname.charAt(0) }}</span>
          </div>
        </div>
        <div class="right">
          <div
            class="right-header font-size-12 color-info line-height-20 m-b-6 flex justify-content-between"
          >
            <span>{{ item.taskName }}</span>
            <span v-if="!item.activityTask">{{ item.finishDate }}</span>
            <span v-else class="color-warning">正在处理中</span>
            <span v-if="item.undoFlag">等待上一步完成</span>
          </div>

          <template v-if="item.readyUserList && item.readyUserList.length">
            <div
              v-for="row in item.readyUserList"
              :key="row.id"
              class="flex justify-content-between color-text-secondary font-size-14 line-height-22 m-b-8"
            >
              <span class="username">
                <span :class="{ 'padding-username': item.activityTask }">{{
                  (row.deptName ? row.deptName + '-' : '') + row.nickname
                }}</span>
                <span v-if="item.result && index !== 0">
                  <span :class="getColor(item)">({{ getVale(item) }})</span>
                </span>
              </span>
              <el-link
                type="primary"
                v-if="item.activityTask"
                class="font-size-12"
                @click="sendMsgHandle(item, row)"
                >发送短信提醒</el-link
              >
            </div>
          </template>
          <template v-else>
            <div
              class="username color-text-secondary font-size-14 line-height-22 m-b-8"
            >
              <span :class="{ 'padding-username': item.activityTask }">{{
                (item.user?.deptName ? item.user?.deptName + '-' : '') +
                item.user?.nickname
              }}</span>
              <span v-if="item.result && index !== 0">
                <span :class="getColor(item)">({{ getVale(item) }})</span>
              </span>
            </div>
            <div
              class="item-remark color-text-secondary font-size-14 line-height-22 m-b-8"
            >
              {{ item.comment }}
            </div>
          </template>

          <div v-if="item.taskForm" class="m-b-8">
            <Uploader
              type="avatar"
              width="42px"
              height="42px"
              onlyForView
              :value="item.taskForm.attachMap.leaveExamineFiles"
            />
          </div>
        </div>
      </div>
    </div>
    <div v-else>
      <empty-data description="暂无审核记录" />
    </div>
  </div>
</template>

<script>
import EmptyData from '@/components/EmptyData/index.vue'
import { sendSmsMsg } from '@/components/FlowForm/api'

export default {
  name: 'FlowRecord',
  components: {
    EmptyData
  },
  props: {
    historyList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      historyListCopy: []
    }
  },
  watch: {
    historyList: {
      handler(val) {
        if (val.length) {
          this.historyListCopy = JSON.parse(JSON.stringify(val))
        }
      },
      immediate: true
    }
  },
  methods: {
    sendMsgHandle(item, row) {
      this.$confirm('确认发送短信提醒？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const params = {
          userId: row.id,
          orderId: item.orderId
        }
        sendSmsMsg(params).then(() => {
          this.$toast.success('发送成功')
        })
      })
    },
    getColor(e) {
      const colors = new Map()
      colors.set(1, 'color-success')
      colors.set(2, 'color-warning')
      colors.set(3, 'color-danger')
      return colors.get(e.result)
    },
    getCircleBg(e) {
      const bgs = new Map()
      bgs.set(1, 'bg-primary')
      bgs.set(2, 'bg-warning')
      bgs.set(3, 'bg-danger')
      return bgs.get(e.result)
    },

    getVale(e) {
      const bgs = new Map()
      bgs.set(1, '已同意')
      bgs.set(2, '已退回')
      bgs.set(3, '已拒绝')
      return bgs.get(e.result)
    }
  }
}
</script>

<style lang="scss" scoped>
.record-container {
  height: 100%;
  overflow-x: hidden;
  overflow-y: scroll;
  padding-right: 12px;
  .record-wrapper {
    .left {
      flex: 0 0 32px;
      position: relative;

      .record-line {
        position: absolute;
        width: 2px;
        height: calc(100% - 24px);
        top: 32px;
        left: 19px;
        background-color: #dcdcdc;
      }

      .record-circle {
        position: absolute;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: #f7f7f7;
        @include border_color(--border-color-base);
        border: 1px solid;
        top: 8px;
        left: 8px;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        &.padding-user {
          @include background_color(--color-warning);
          @include font_color(--color-white);
          border: none;
        }
      }
    }
    .right {
      flex: 1;
      padding-left: 4px;
      padding-top: 10px;
      .item-remark {
        background: #f7f7f7;
        padding: 6px 8px;
        border-radius: 3px;
      }
      .padding-username {
        @include font_color(--color-black);
      }
    }
    &.light {
      .record-circle {
        @include background_color(--color-primary);
        @include font_color(--color-white);
        border: none;
      }
      .username {
        @include font_color(--color-black);
      }
    }
  }
  &::-webkit-scrollbar-track-piece {
    background: transparent;
  }
}
</style>
