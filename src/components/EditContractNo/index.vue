<template>
  <dialog-cmp
    :title="title"
    :visible.sync="visible"
    width="400px"
    @close="close"
    @confirmDialog="confirmDialog"
  >
    <driven-form
      v-if="_visible"
      ref="driven-form"
      v-model="fromModel"
      :formConfigure="formConfigure"
    />
  </dialog-cmp>
</template>
<script>
import { modifyContractNo } from "@/api/common";
export default {
  name: "EditContractNo",
  props: {
    title: {
      type: String,
      default: ""
   },
    visible: {
      type: Boolean,
      default: false
    }
  },
  inject: ['contractDetail'],
  data() {
    return {
      fromModel: {},
      formConfigure: {
        labelWidth: '60px',
        descriptors: {
          contractNo: {
            form: 'input',
            label: '编号',
            rule: [
              {
                type: 'string',
                required: true,
                message: '请输入编号',
                trigger: 'blur'
              }
            ]
          },
        }
      }
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.fromModel = {};
      }
    }
  },
  computed: {
    _visible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      }
    }
  },
  methods: {
    close(){
      this._visible = false;
    },
    confirmDialog() {
      this.$refs['driven-form'].validate((valid) => {
        if (valid) {
          const { contractNo } = this.fromModel;

          if (contractNo === this.contractDetail.detailData.contractNo){
            return this.$message.warning('与合同现编号无变化');
          }
          const data = {
            id:this.$route.query.id,
            contractNo
          }
          modifyContractNo(data).then(() => {
            this.$message.success("修改成功");
            this._visible = false;
            this.contractDetail.init();
            this.contractDetail.$refs?.detailLeft?.getOpRecord();
          })
        }
      });
    }
  }
};
</script>
