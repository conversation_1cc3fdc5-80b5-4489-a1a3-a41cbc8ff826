<template>
  <el-drawer
    append-to-body
    :size="size"
    :visible.sync="visible"
    :before-close="closeDrawer"
    destroy-on-close
    v-bind="$attrs"
    v-on="$listeners"
    :wrapperClosable="wrapperClosable"
  >
    <slot name="title" slot="title" />
    <div class="drawer-container pos-relative wh100">
      <div class="drawer-content w100" :class="{ hei100: !haveFooter }">
        <slot />
      </div>
      <div
        v-if="haveFooter"
        class="drawer-footer pos-absolute w100 flex align-items-center justify-content-end"
      >
        <slot name="footer" />
        <template v-if="haveOperation">
          <el-button type="info" @click="closeDrawer">取消</el-button>
          <el-button type="primary" @click="confirmDrawer">
            <span>确定</span>
          </el-button>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: 'BasicDrawer',
  inheritAttrs: false,
  props: {
    // 是否显示 Drawer
    visible: {
      type: Boolean,
      default: false
    },
    // Drawer 打开的方向
    direction: {
      type: String,
      default: 'rtl'
    },
    //Drawer 窗体的大小, 当使用 number 类型时, 以像素为单位, 当使用 string 类型时, 请传入 'x%', 否则便会以 number 类型解释
    size: {
      type: [String, Number],
      default: '820px'
    },
    // 是否有操作按钮
    haveOperation: {
      type: Boolean,
      default: true
    },
    // 有底部
    haveFooter: {
      type: Boolean,
      default: true
    },
    // 点击遮罩层是否可以关闭 Drawer
    wrapperClosable: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    // 关闭抽屉
    closeDrawer() {
      this.$emit('update:visible', false)
    },
    // 提交
    confirmDrawer() {
      this.$emit('confirmDrawer')
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-container {
  .hei100 {
    height: 100% !important;
  }
  .drawer-content {
    height: calc(100% - 56px);
    overflow-y: scroll;
    overflow-x: hidden;
    padding: 20px;
    padding-left: 32px;
    box-sizing: border-box;
  }
  .drawer-footer {
    bottom: 0;
    left: 0;
    height: 56px;
    border-top-width: 1px;
    border-style: solid;
    padding: 0 16px;
    @include border_color(--border-color-base);
    @include background_color(--color-white);
  }
}
</style>
