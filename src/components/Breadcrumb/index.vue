<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb" class="m-r-12">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <span
          v-if="item.redirect === 'noredirect' || index == levelList.length - 1"
          class="no-redirect"
          >{{ item.meta.title }}</span
        >
        <a v-else @click.prevent="handleLink(item)">{{ item.meta.title }}</a>
      </el-breadcrumb-item>
    </transition-group>
    <slot></slot>
  </el-breadcrumb>
</template>

<script>
import * as pathToRegexp from 'path-to-regexp'
import { mapGetters } from 'vuex'
import { isAllHiddenRoutes } from '@/store/modules/module'
import { deepClone } from '@/utils/tools'

export default {
  name: 'Breadcrumb',
  data() {
    return {
      levelList: [],
      tiledRoutes: [] // 当前一级路由的平铺
    }
  },
  computed: {
    ...mapGetters(['permission_routers']),
    filterPermissionRoutes() {
      return isAllHiddenRoutes(deepClone(this.permission_routers))
    }
  },
  watch: {
    $route(route) {
      if (route.path.startsWith('/redirect/')) {
        return
      }
      this.getBreadcrumb()
    }
  },
  created() {
    this.getBreadcrumb()
  },
  methods: {
    // 平铺当前的路由
    tiledRouteHandle(routes, parentPath) {
      routes.forEach(item => {
        if (item.path !== parentPath) {
          item.path =
            item.path === '/'
              ? parentPath + item.path
              : parentPath + '/' + item.path
        }
        // 判断是否存在相同路径
        const existObj = this.tiledRoutes.find(row => row.path === item.path)
        if (!existObj) {
          this.tiledRoutes.push(item)
        }
        if (item.children && item.children.length) {
          return this.tiledRouteHandle(item.children, item.path)
        }
      })
    },
    getBreadcrumb() {
      let matched = this.$route.matched.filter(
        item => item.meta && item.meta.title
      )
      // 获取当前一级路由
      const parentPath = matched[0].path
      const parentRoute = this.filterPermissionRoutes.filter(
        item => item.path === parentPath
      )
      this.tiledRoutes = []
      this.tiledRouteHandle(deepClone(parentRoute), parentPath)
      // 跟据path获取当前路径所在数组
      const currentPath = this.$route.path
      let arr = currentPath.split('/').filter(item => item)
      const pathList = []
      const len = currentPath.split('/').length - 1
      for (let i = 0; i < len; i++) {
        pathList.push(`/${arr.join('/')}`)
        arr = arr.slice(0, -1)
      }
      pathList.reverse()
      const titleRoutes = this.tiledRoutes.filter(
        item => item.meta && item.meta.title && item.meta.breadcrumb !== false
      )
      // 匹配当前路径的面包屑
      this.levelList = []
      pathList.forEach(path => {
        titleRoutes.forEach(route => {
          if (route.path === path) {
            this.levelList.push(route)
          }
        })
      })
      // this.levelList = matched.filter(
      //   item => item.meta && item.meta.title && item.meta.breadcrumb !== false
      // )
    },
    pathCompile(path) {
      const { params } = this.$route
      const toPath = pathToRegexp.compile(path)
      return toPath(params)
    },
    handleLink(item) {
      const { redirect, path } = item
      if (redirect) {
        this.$router.push(redirect)
        return
      }
      this.$router.push(this.pathCompile(path))
    }
  }
}
</script>

<style lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  font-size: 16px;
  line-height: 24px;
  user-select: none;
  height: 24px;
  display: flex;
  align-items: center;
  .no-redirect {
    color: #97a8be;
    cursor: text;
    font-size: 16px;
    line-height: 24px;
    @include font_color(--color-text-primary);
  }
}
</style>
