<template>
  <el-cascader v-model="region" class="w100" :props="props"></el-cascader>
</template>

<script>
import { getProvice, getCity, getCountry } from '@/api/common'

export default {
  name: 'RegionCascader',
  props: {
    value: {
      type: String,
      default: ''
    },
    valueType: {
      type: String,
      default: 'label'
    }
  },
  data() {
    return {
      region: [],
      props: {
        lazy: true,
        lazyLoad: async (node, resolve) => {
          const { level } = node
          let nodes = []
          let typeId = ''

          try {
            switch (level) {
              case 0:
                nodes = await getProvice()
                typeId = 'provinceId'
                break
              case 1:
                nodes = await getCity(node.data.regionId)
                typeId = 'cityId'
                break
              case 2:
                nodes = await getCountry(node.data.regionId)
                typeId = ''
                break
              default:
                nodes = []
                typeId = ''
            }
          } catch (error) {
            nodes = []
          }

          const resolveNodes = nodes.map(item => {
            const node = {
              value: this.valueType === 'id' ? item.id : item.name,
              label: item.name,
              leaf: level >= 2
            }
            node.regionId = item[typeId]
            return node
          })

          resolve(resolveNodes)
        }
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        if (!val) val = ''
        this.region = val.split(',')
      },
      immediate: true
    },
    region: {
      handler(val) {
        this.$emit('input', val.join(','))
      },
      deep: true
    }
  }
}
</script>

<style lang="scss" scoped></style>
