<template>
  <div class="statistics">
    <div :class="`left ${chartData.length > 0 ? '' : 'left-w'}`">
      <template v-if="data && data.length > 0">
        <div
          :class="`box ${backgroundColor ? 'back-g' : ''}`"
          v-for="item in data"
          :key="item.id"
        >
          <div>
            <span class="m-r-8">{{ item.name }}</span>
            <span v-if="item.prompt">
              <el-tooltip
                class="item"
                effect="dark"
                :content="item.prompt"
                placement="top"
              >
                <svg-icon icon-class="help-circle" />
              </el-tooltip>
            </span>
          </div>
          <div class="m-b-8 m-t-8">
            <span class="number">{{ item.value }}</span>
            <span class="unit">{{ item.unit }}</span>
          </div>
          <div v-if="item.compareLabel">
            <span class="m-r-6 color-dark">{{ item.compareLabel }}</span>
            <span :class="`m-r-6 color-${item.type}`">{{
              item.compareData
            }}</span>
            <img
              v-if="item.type === 'decline'"
              class="m-r-6"
              src="./images/decline.png"
              alt="decline"
            />
            <img v-else class="m-r-6" src="./images/rise.png" alt="rise" />
            <span :class="`m-r-6 color-${item.type}`">
              <span>{{ item.compareItem }}</span>
              <span>{{ item.compareItemLabel }}</span>
            </span>
          </div>
        </div>
      </template>
    </div>
    <div class="right" v-if="chartData.length > 0">
      <div class="title">{{ title }}</div>
      <div id="chart"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
export default {
  name: 'Statistics',
  props: {
    backgroundColor: {
      type: Boolean,
      default: true
    },
    title: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      required: true,
      default: () => []
    },
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      option: {}
    }
  },
  methods: {
    init() {
      let chartDom = document.getElementById('chart')
      let myChart = echarts.init(chartDom)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
      let that = this
      this.option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          },
          borderWidth: 0,
          formatter(params) {
            let html = ''
            for (let i = 0; i < params.length; i++) {
              let param = params[i]
              html += param.marker + param.seriesName + '<br/>'
            }
            return html
          },
          backgroundColor: '#000000',
          textStyle: {
            color: '#fff'
          }
        },
        legend: {
          bottom: 6,
          left: 15,
          itemWidth: 10,
          itemHeight: 10,
          icon: 'circle',
          textStyle: {
            color: '#cacbcd'
          }
        },
        grid: {
          left: '20px',
          top: '0px',
          right: '10px',
          bottom: '0px',
          containLabel: true,
          splitLine: {
            // 设置竖线
            show: false
          },
          borderWidth: 0
        },
        xAxis: {
          type: 'value',
          boundaryGap: false,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'category',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        series: this.chartData?.map((item, index) => {
          return {
            name: item.name + item.value + item.unit,
            type: 'bar',
            stack: 'total',
            //改变每一根柱子的颜色
            itemStyle: {
              //改变主子的高度
              color:
                index === 0 ? '#f7c797' : index === 1 ? '#bcebdc' : '#99b9ff'
            },
            barWidth: 12,
            label: {
              normal: {
                show: true,
                position: 'top',
                aline: 'center',
                formatter(item) {
                  //求出每一项在总数中的占比
                  let total = 0
                  that.chartData?.forEach(t => {
                    total += t.value
                  })
                  let percent = ((item.value / total) * 100).toFixed(2)
                  if (Number(percent) > 0) {
                    return `${percent}%`
                  } else {
                    return ''
                  }
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: [item.value],
            showBackground: !(item.value > 0)
          }
        })
      }
      this.option && myChart.setOption(this.option)
      window.addEventListener('resize', () => {
        myChart.resize()
      })
    }
  },
  updated() {
    if (this.chartData.length > 0) {
      this.init()
    }
  },

  beforeDestroy() {
    this.option = null
  }
}
</script>

<style lang="scss" scoped>
.color-rise {
  color: #f95355;
}
.color-dark {
  color: #c7c7c7;
}
.color-decline {
  color: #48c79c;
}

.back-g {
  background-color: #ffffff;
}
.statistics {
  display: flex;
  background-color: #f7f9fb;
  padding: 16px;
  border-radius: 4px;
  .left {
    //使用flex布局,一行展示，不管有多少个，都是一行展示，宽度自适应，间隙16px
    display: flex;
    flex-wrap: wrap;
    flex: 0.75;
    width: 100%;
    max-height: 128px;
    .box {
      padding: 24px 16px;
      flex: 1;
      margin: 0 8px;
      color: rgba(0, 0, 0, 0.4);
      font-size: 14px;

      & > div:nth-child(2) {
        color: #000000;
        font-size: 28px;
        font-weight: bold;
        .unit {
          font-size: 14px;
          font-weight: normal;
          margin-left: 4px;
        }
      }
    }
    .box:first-child {
      margin-left: 0;
    }
    .box:last-child {
      margin-right: 0;
    }
  }
  .left-w {
    flex: 1;
  }
  .right {
    flex: 0.25;
    width: 100%;
    height: 100%;
    margin-left: 16px;
    #chart {
      width: 100%;
      height: 77px;
    }
    .title {
      margin-top: 23px;
      color: rgba(0, 0, 0, 0.4);
      font-size: 14px;
    }
  }
}
</style>
