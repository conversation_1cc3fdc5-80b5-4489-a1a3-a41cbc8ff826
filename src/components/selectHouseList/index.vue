<template>
  <div>
    <div class="top-wrapper flex align-items-center justify-content-between">
      <div class="tips-wrapper flex align-items-center font-size-14 text-black">
        <div
          v-for="(item, index) in tipsList"
          :key="index"
          class="tips-item flex align-items-center m-r-24"
        >
          <div
            v-if="item.label === '问题房源'"
            class="problem-circle color-white flex align-items-center justify-content-center m-r-4"
            :style="{ background: item.color }"
          >
            <svg-icon icon-class="info-circle" class />
          </div>
          <span
            v-else
            class="tip-circle"
            :style="{ background: item.color }"
          ></span>
          <span>{{ item.label }}</span>
        </div>
      </div>

      <!-- 选择房源个数统计 -->
      <div
        class="h100 w100 flex align-items-center justify-content-end font-size-14 line-height-22 color-text-regular"
        style="width: 150px"
      >
        {{ '已选中' + selectDataList.length + '个房间' }}
      </div>
    </div>

    <div class="board-list-wrapper">
      <div v-for="(item, index) in boardData" :key="index">
        <div
          v-for="(build, buildIndex) in item.buildingList"
          :key="buildIndex"
          class="board-list-item m-b-16"
        >
          <div
            class="board-list-title bg-base font-size-14 color-text-regular m-b-8"
          >
            {{ item.parkName }} {{ build.building }}
          </div>

          <div
            class="board-container flex m-b-16"
            v-for="(floor, floorIndex) in build.housingBordFloorList"
            :key="floorIndex"
          >
            <div
              class="board-list-floor bg-base font-size-14 color-text-regular m-r-16"
            >
              <div class="h100 flex align-items-center">
                <span class="text-center">{{ floor.floor }}层</span>
              </div>
            </div>

            <div class="board-list-room">
              <el-row :gutter="16">
                <el-col
                  v-for="(room, roomIndex) in floor.housingBordRoomList"
                  :key="roomIndex"
                  :span="30"
                  class="m-b-16"
                >
                  <div
                    class="room-item border-color-base"
                    @click="
                      getHouseHistory({
                        ...room,
                        parkName: item.parkName,
                        building: build.building,
                        floor: floor.floor
                      })
                    "
                  >
                    <div
                      class="room-title pos-relative font-strong flex align-items-center justify-content-between"
                      :class="_genRoomClass(room.status)"
                      :style="_genRoomStyle(room.status)"
                    >
                      <div class="room-wrapper p-r-10 line-1">
                        {{ room.room }}
                      </div>
                      <div
                        class="problem-house p-l-8 p-r-8 color-white flex align-items-center justify-content-between"
                        :style="{
                          background: IsProblem(room.problemStatus)
                            ? problem
                            : 'unset'
                        }"
                      >
                        <div>
                          <svg-icon
                            v-if="IsProblem(room.problemStatus)"
                            icon-class="info-circle"
                          />
                        </div>
                        <div @click.stop="selectData(room)">
                          <el-checkbox
                            @change="selectDataChange"
                            :value="isSelectHouse(room)"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="room-info">
                      <div
                        class="flex align-items-center font-size-14 line-height-22 m-b-8"
                      >
                        <div class="m-r-6 label color-text-secondary">
                          房间面积
                        </div>
                        <div class="value color-text-primary line-1">
                          {{ room.area }}m²
                        </div>
                      </div>

                      <div
                        v-if="room.status === 2"
                        class="flex align-items-center font-size-14 line-height-22"
                      >
                        <div class="m-r-6 label color-text-secondary">
                          锁定原因
                        </div>
                        <div class="value color-black line-1">
                          {{ room.lockReason }}
                        </div>
                      </div>

                      <div
                        v-if="room.problemStatus === 2"
                        class="flex align-items-center font-size-14 line-height-22"
                      >
                        <div class="m-r-6 label color-text-secondary">
                          问题描述
                        </div>
                        <div class="value color-black line-1">
                          {{ room.problemDesc }}
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 房源历史 -->
    <house-history ref="house-history" />
  </div>
</template>

<script>
import request from '@/utils/request'
import HouseHistory from './houseHistory'
let basic = ['#054CE8', '#48C79C', '#BED2FE']
let problem = ['#E34D59']
export default {
  name: 'selectHouseList',
  components: {
    HouseHistory
  },
  data() {
    return {
      tipsList: [
        {
          label: '空置房源',
          color: basic[1]
        },
        {
          label: '问题房源',
          color: problem
        }
      ],
      problem,
      problemStatus: [2, 3], //问题房源状态
      fromQuerys: {
        parkId: null,
        status: [3]
      },
      // 获取房源数据
      findParkBoardApi: data => {
        return request({
          url: `/housing/park/findParkBord`,
          method: 'post',
          data
        })
      },
      boardData: [],
      // 工具数据
      selectDataTemp: {},
      selectDataList: []
    }
  },
  methods: {
    selectData(val) {
      this.selectDataTemp = val
    },
    selectDataChange(val) {
      if (val) {
        let index = this.findIndexSelectDataList(this.selectDataTemp)
        if (-1 === index) {
          this.selectDataList.push(this.selectDataTemp)
        }
      } else {
        let index = this.findIndexSelectDataList(this.selectDataTemp)
        if (-1 !== index) this.selectDataList.splice(index, 1)
      }
    },
    isSelectHouse(val) {
      return -1 !== this.findIndexSelectDataList(val)
    },
    findIndexSelectDataList(val) {
      return this.selectDataList.findIndex(e => {
        return e.roomId === val.roomId
      })
    },
    // 获取背景
    _genRoomStyle(status) {
      const types = new Map()
      types.set(4, {
        backgroundColor: basic[0]
      })
      types.set(1, {
        backgroundColor: basic[0]
      })
      types.set(2, {
        backgroundColor: basic[0]
      })
      types.set(3, {
        backgroundColor: basic[1]
      })

      return types.get(status || 4)
    },

    // 获取字体颜色
    _genRoomClass(status) {
      const types = new Map()
      types.set(4, 'color-dark')
      types.set(3, 'color-dark')
      types.set(2, 'color-white')
      types.set(1, 'color-white')
      return types.get(status || 4)
    },

    // 是否为问题房源
    IsProblem(status) {
      return this.problemStatus.includes(status)
    },

    findParkBoard(parkId, list) {
      this.selectDataList = list
      this.fromQuerys.parkId = parkId
      this.findParkBoardApi(this.fromQuerys).then(res => {
        for (let re of res) {
          if (re.buildingList && re.buildingList.length > 0) {
            for (let build of re.buildingList) {
              if (
                build.housingBordFloorList &&
                build.housingBordFloorList.length > 0
              ) {
                for (let housingBordFloor of build.housingBordFloorList) {
                  if (
                    housingBordFloor.housingBordRoomList &&
                    housingBordFloor.housingBordRoomList.length > 0
                  ) {
                    for (let housingBordRoom of housingBordFloor.housingBordRoomList) {
                      // 冗余一些必要的字段数据
                      housingBordRoom.building =
                        build.building + housingBordFloor.floor + '层'
                      housingBordRoom.correctArea = housingBordRoom.area
                      housingBordRoom.hasSurveying = 0
                    }
                  }
                }
              }
            }
          }
        }
        this.$set(this, 'boardData', res)
      })
    },
    // 查看房源历史
    getHouseHistory(room) {
      this.$refs['house-history'].getRoomLog(room)
    }
  }
}
</script>

<style lang="scss" scoped>
//
.top-wrapper {
  height: 40px;
  line-height: 40px;
  border-bottom-width: 1px;
  border-style: solid;
  @include border_color(--border-color-base);
  .tips-wrapper {
    .tips-item {
      .tip-circle {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
        background: #bed2fe;
      }

      .problem-circle {
        width: 20px;
        height: 20px;
        border-radius: 3px;
      }
    }
  }
}
//
.board-list-wrapper {
  margin-top: 16px;
  .board-list-item {
    .board-list-title {
      width: 100%;
      height: 40px;
      background: #f3f3f3;
      border-radius: 3px;
      line-height: 40px;
      text-align: center;
    }

    .board-container {
      width: 100%;
      .board-list-floor {
        flex: 0 0 30px;
        border-radius: 3px;
        padding: 0 7px;

        .text-center {
          text-align: center;
        }
      }

      .board-list-room {
        flex: 1;
        .room-item {
          height: 152px;
          border-radius: 3px;
          padding: 8px;
          border: 1px solid;
          .room-title {
            width: 100%;
            height: 30px;
            border-radius: 3px;
            line-height: 30px;
            padding-left: 8px;

            .room-wrapper {
              flex: 1;
            }

            .problem-house {
              flex: 0 0 60px;
              width: 60px;
              height: 30px;
              border-radius: 3px;
              backdrop-filter: blur(4px);
            }

            .more-icon {
              right: 0;
            }
          }
        }

        .room-info {
          padding: 16px 8px 0;

          .label {
            flex: 0 0 56px;
          }

          .value {
            flex: 1;
          }
        }
        .el-col-30 {
          width: 33.3%;
          height: 152px;
          padding: 8px;
        }
      }
    }
  }
}
</style>
