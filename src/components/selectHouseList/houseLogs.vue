<template>
  <div>
    <div class="font-size-14 color-text-primary font-strong m-b-8">
      操作历史
    </div>

    <div class="logs-container">
      <div
        v-for="(item, index) in logsList"
        :key="index"
        class="log-item p-16 border-color-base m-b-24"
      >
        <div
          class="logs-header flex align-items-center justify-content-between line-height-22 m-b-20"
        >
          <div class="header-left font-size-14 color-text-primary">
            {{ _genType(item.type) }}
          </div>
          <div
            class="header-right flex align-items-center font-size-12 color-text-regular"
          >
            <div class="logs-operation m-r-20 flex align-items-center">
              <svg-icon
                icon-class="user"
                class="font-size-16 color-success m-r-4"
              />
              <span>操作人：{{ item.creator || '-' }}</span>
            </div>
            <div class="logs-time flex align-items-center">
              <svg-icon
                icon-class="calendar"
                class="font-size-16 color-primary m-r-4"
              />
              <span>{{ item.createTime }}</span>
            </div>
          </div>
        </div>

        <div class="log-item-wrapper">
          <div class="flex font-size-14 m-b-8">
            <div class="color-text-secondary label m-r-15">
              {{ _genLabel(item.type) }}
            </div>
            <div class="color-text-primary">
              {{ item.reason }}
            </div>
          </div>

          <div v-if="item.entName" class="flex font-size-14 m-b-8">
            <div class="color-text-secondary label m-r-15">入住公司</div>
            <div class="color-primary">{{ item.entName }}</div>
          </div>

          <div v-if="item.contractNumber" class="flex font-size-14 m-b-8">
            <div class="color-text-secondary label m-r-15">关联合同</div>
            <div class="color-primary">{{ item.contractNumber }}</div>
          </div>

          <div
            v-if="item.attachMap && item.attachMap.houseBoard"
            class="flex font-size-14"
          >
            <div class="color-text-secondary label m-r-15">附件</div>
            <div class="color-primary">
              <Uploader
                type="avatar"
                width="60px"
                height="60px"
                onlyForView
                :value="item.attachMap.houseBoard"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'HouseLogsBasic',
  props: {
    logsList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {
    _genType(e) {
      const types = new Map()
      types.set(1, '房源锁定')
      types.set(2, '解除房源锁定')
      types.set(3, '标记为问题房源')
      types.set(4, '解除问题状态')
      types.set(5, '已用房源')
      types.set(6, '空置房源')
      types.set(7, '已用房源')
      types.set(8, '空置房源')
      return types.get(e) || '-'
    },

    _genLabel(e) {
      const labels = new Map()
      labels.set(1, '锁定原因')
      labels.set(2, '变更原因')
      labels.set(3, '问题原因')
      labels.set(4, '变更原因')
      labels.set(5, '-')
      labels.set(6, '-')
      labels.set(7, '-')
      labels.set(8, '-')
      return labels.get(e) || '-'
    }
  }
}
</script>

<style lang="scss" scoped>
.logs-container {
  .log-item {
    width: 100%;
    border-radius: 3px;
    border: 1px solid;
  }

  .log-item-wrapper {
    line-height: 22px;
    .label {
      flex: 0 0 56px;
      text-align: right;
    }
  }
}
</style>
