<template>
  <basic-drawer
    title="房源历史"
    :visible.sync="drawerVisible"
    :haveFooter="false"
  >
    <div class="house-history-container">
      <ul class="house-info-wrapper">
        <li class="flex house-info font-size-14 line-height-22 m-b-20">
          <div class="color-text-regular label m-r-16">园区名称</div>
          <div class="color-text-primary">{{ roomData.parkName }}</div>
        </li>
        <li class="flex house-info font-size-14 line-height-22 m-b-20">
          <div class="color-text-regular label m-r-16">房间号码</div>
          <div class="color-text-primary">
            {{ roomData.building }} {{ roomData.floor }}层 {{ roomData.roomId }}
          </div>
        </li>
        <li class="flex house-info font-size-14 line-height-22 m-b-20">
          <div class="color-text-regular label m-r-16">房间面积</div>
          <div class="color-text-primary">{{ roomData.area }}m²</div>
        </li>
      </ul>

      <div class="p-t-24">
        <div v-if="logsList.length">
          <house-logs :logsList="logsList" />
        </div>
        <div v-else>
          <empty-data description="暂无房源历史" />
        </div>
      </div>
    </div>
  </basic-drawer>
</template>

<script>
import HouseLogs from './houseLogs'
import { getRoomLog } from '@/views/manage/house/board/board-basic/api'

export default {
  name: 'HouseHistoryBasic',
  components: {
    HouseLogs
  },
  data() {
    return {
      drawerVisible: false,
      logsList: [],
      roomData: {}
    }
  },
  watch: {
    drawerVisible(val) {
      if (!val) {
        this.logsList = []
        this.roomData = {}
      }
    }
  },
  methods: {
    getRoomLog(room) {
      const { roomId } = room
      this.roomData = room
      getRoomLog(roomId).then(res => {
        this.logsList = res
        this.drawerVisible = true
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.house-history-container {
  .house-info-wrapper {
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-base);
    .house-info {
      .label {
        flex: 0 0 56px;
      }
    }
  }
}
</style>
