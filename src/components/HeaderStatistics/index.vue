<template>
  <el-row class="header-statistics-container">
    <el-col :span="12" class="header-statistics-left">
      <div
        class="statistics-item"
        v-for="(item, index) in dataList"
        :key="index"
      >
        <div class="statistics-item-name">
          <span
            class="item-name-dot"
            :style="{ background: item.color }"
          ></span>
          <span class="item-name">{{ item.name }}</span>
        </div>
        <div class="statistics-item-num" :style="{ color: item.color }">
          {{ item.num }}
        </div>
      </div>
    </el-col>
    <el-col :span="12" class="header-statistics-right">
      <div class="wh100" ref="lineChart"></div>
    </el-col>
  </el-row>
</template>

<script>
import * as Echarts from 'echarts'
import { formatGetParams } from '@/utils/tools'
import downloads from '@/utils/download'
import dayjs from 'dayjs'
export default {
  name: 'HeaderStatistics',
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    xUint: {
      type: String,
      default: ''
    },
    yUint: {
      type: String,
      default: ''
    },
    time: {
      type: Number,
      default: 1
    },
    chartData: {
      type: Object,
      default: () => ({
        xData: [],
        yData: []
      })
    },
    getExportExcel: {
      type: Function,
      default: () => ({})
    }
  },
  data() {
    return {
      params: [],
      lineChart: ''
    }
  },
  mounted() {
    window.onExportStatisticsFun = this.exportStatisticsFn
    window.onExportFun = this.exportFn
  },
  methods: {
    exportFn() {
      console.log('导出', this.params)
      const params = {
        time: this.time
      }
      let url = this.getExportExcel() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '申请明细.xls')
    },
    exportStatisticsFn() {
      console.log('导出统计', this.params)
      const params = {
        time: this.time
      }
      let url = this.getExportExcel() + '?'
      url += formatGetParams(params)
      downloads.requestDownload(url, 'excel', dayjs().format('YYYY-MM-DD') + '申请明细.xls')
    },
    listenerHandle() {
      this.lineChart && this.lineChart.resize()
    },
    initLineChart() {
      const { yData } = this.chartData
      const that = this

      const names = yData.map(item => {
        return item.name
      })
      let series = []
      let dom = `
          <div style="display: flex;justify-content: space-between;">
            <button id="exportBtn" style="margin: 8px 6px 3px 0;background:#67c23a;color: #fff; border: none; border-radius: 2px; padding: 3px 12px; cursor: pointer;"  onclick="onExportStatisticsFun()">导出统计</button>
            <button id="exportBtn" style="margin: 8px 0 3px 0;background:#ed7b2f;color: #fff; border: none; border-radius: 2px; padding: 3px 12px; cursor: pointer;" onclick="onExportFun()">导出</button>
          </div>
      `
      yData.forEach(item => {
        series.push({
          name: item.name,
          data: item.data || [],
          type: 'line',
          showSymbol: false,
          smooth: true,
          color: item.color
        })
      })
      const option = {
        legend: {
          data: names, //图例名称
          left: 'center', //调整图例位置
          top: 0, //调整图例位置
          itemHeight: 7, //修改icon图形大小
          icon: 'circle', //图例前面的图标形状
          textStyle: {
            //图例文字的样式
            color: '#a1a1a1', //图例文字颜色
            fontSize: 12 //图例文字大小
          }
        },
        grid: {
          left: '5%',
          right: '10%',
          bottom: '16%',
          top: '12%',
          containLabel: true
        },
        tooltip: {
          trigger: 'axis',
          // triggerOn: 'click',//可点击
          position: point => [point[0], point[1]],
          enterable: true,
          axisPointer: {
            type: 'line'
          },
          formatter: params => {
            that.params = params
            let text = ''
            params.forEach(item => {
              text += `${item.marker} ${item.seriesName}： ${item.value} <br>`
            })
            text += dom
            return text
          },
          backgroundColor: '#000',
          borderWidth: 0,
          padding: 5,
          textStyle: {
            color: 'rgba(255, 255, 255, 0.90)',
            lineHeight: 22,
            fontSize: 14
          }
        },
        xAxis: {
          name: `/${this.xUint}`,
          type: 'category',
          data: this.chartData.xData || [],
          boundaryGap: false, // 不留白，从原点开始
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#a8acb5',
            formatter: '{value}',
            fontSize: 12
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#A0A1A7'
            }
          }
        },
        yAxis: {
          name: `/${this.yUint}`,
          type: 'value',
          minInterval: 1,
          splitLine: {
            // 网格线
            show: false,
            lineStyle: {
              color: '#E1E7F1'
            }
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#A0A1A7'
            }
          },
          axisLabel: {
            color: '#a8acb5',
            formatter: '{value}'
          }
        },
        series,
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100,
            zoomOnMouseWheel: true, // 关闭滚轮缩放
            moveOnMouseWheel: true, // 开启滚轮平移
            moveOnMouseMove: true // 鼠标移动能触发数据窗口平移
          },
          {
            brushSelect: false,
            height: 20,
            start: 0,
            end: 100
          }
        ]
      }
      this.lineChart = Echarts.init(this.$refs.lineChart)
      this.lineChart.setOption(option)
      const sliderZoom = this.lineChart._componentsViews.find(
        view => view.type === 'dataZoom.slider'
      )
      sliderZoom._displayables.handleLabels[0].x = 100
      sliderZoom._displayables.handleLabels[1].x = -100
      this.lineChart.on('datazoom', () => {
        sliderZoom._displayables.handleLabels[0].x = 100
        sliderZoom._displayables.handleLabels[1].x = -100
      })
    }
  },
  watch: {
    chartData: {
      async handler() {
        await this.$nextTick()
        this.initLineChart()
        window.addEventListener('resize', this.listenerHandle)
      },
      deep: true,
      immediate: true
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.listenerHandle)
  }
}
</script>

<style scoped lang="scss">
.header-statistics-container {
  display: flex;
  align-items: center;
  .header-statistics-left {
    display: grid;
    justify-content: space-between;
    grid-template-columns: repeat(auto-fill, 210px);
    grid-gap: 20px 0;
    padding: 20px;
    .statistics-item {
      display: flex;
      flex-direction: column;
      .statistics-item-name {
        display: flex;
        align-items: center;
        justify-content: center;
        @include font_color(--icon-color);
        .item-name-dot {
          width: 6px;
          height: 6px;
          margin-right: 6px;
          flex-shrink: 0;
          background: #666;
        }
        .item-name {
          width: calc(100% - 12px);
        }
      }
      .statistics-item-num {
        font-size: 24px;
        font-weight: bold;
        margin-top: 14px;
        padding-left: 12px;
      }
    }
  }
  .header-statistics-right {
    height: 260px;
  }
}
</style>
