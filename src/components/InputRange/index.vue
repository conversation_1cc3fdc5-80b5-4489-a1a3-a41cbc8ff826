<template>
  <div class="flex align-items-center">
    <el-input
      v-model="_value[0]"
      @input="changeValue"
      :placeholder="startPlaceholder"
    ></el-input>
    <span class="m-l-6 m-r-6">-</span>
    <el-input
      v-model="_value[1]"
      @input="changeValue"
      :placeholder="endPlaceholder"
    ></el-input>
  </div>
</template>

<script>
export default {
  name: 'InputRange',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    startPlaceholder: {
      type: String,
      default: '请输入'
    },
    endPlaceholder: {
      type: String,
      default: '请输入'
    }
  },
  computed: {
    _value() {
      return this.value
    }
  },
  methods: {
    changeValue() {
      this.$emit('input', this._value)
    }
  }
}
</script>
