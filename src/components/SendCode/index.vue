<template>
  <div>
    <el-button
      type="primary"
      size="large"
      class="w100"
      :loading="loading"
      :disabled="disabled"
      @click="getCode"
    >
      {{ text }}
    </el-button>

    <!-- 图形验证码 -->
    <verify
      @success="capctchaCheckSuccess"
      mode="pop"
      captchaType="blockPuzzle"
      ref="verify"
    />
  </div>
</template>

<script>
import Verify from '@/components/Verifition/Verify'
import { Phone_Regexp } from '@/utils/validate'
import { getSmsCaptcha } from '@/api/common'

import { SMS_CODE_TIME } from '@/settings'

const UN_SEND_TEXT = '发送验证码'
const SEND_ING = '验证码发送中'
const COUNT_DOWN = time => `${time}s后再次获取`

let countDownTimer = null

export default {
  name: 'SendCode',
  components: { Verify },
  props: {
    // 手机号
    phoneNumber: {
      type: String,
      default: ''
    },
    // 需要获取验证码的模块类型
    type: {
      type: String,
      default: ''
    },
    // 默认倒计时时间
    timeCount: {
      type: Number,
      default: SMS_CODE_TIME
    },
    // 是否需要滑块验证码
    needVerify: {
      type: Boolean,
      default: true
    },
    sendClick: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      text: UN_SEND_TEXT,
      loading: false,
      disabled: false
    }
  },
  watch: {
    sendClick: {
      handler(val) {
        this.disabled = !val
      }
    }
  },
  inject: ['PhoneCode'],
  methods: {
    delay(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    // 获取验证码
    getCode() {
      this.PhoneCode.validatePhoneHandle(valid => {
        if (!valid) return
        // if (this.validatePhoneNumber()) {
        if (this.needVerify) {
          this.$refs.verify.show()
        } else {
          this.sendCode()
        }
        // }
      })
    },
    // 手机号校验
    validatePhoneNumber() {
      if (Phone_Regexp.test(this.phoneNumber)) {
        return true
      } else {
        this.$toast.error('请输入正确的11位手机号')
        return false
      }
    },
    // 图形验证成功
    capctchaCheckSuccess(params) {
      const code = params.captchaVerification
      this.sendCode(code)
    },
    // 接口请求
    sendCode(code) {
      // 接口请求
      this.loading = true
      this.text = SEND_ING
      getSmsCaptcha({
        code,
        phoneNumber: this.phoneNumber,
        type: this.type
      })
        .then(() => {
          this.$toast.success('手机验证码发送成功')
          this.startCountDown()
        })
        .catch(() => {
          this.loading = false
          this.text = UN_SEND_TEXT
        })
    },
    // 倒计时开始
    startCountDown() {
      const that = this
      if (countDownTimer) {
        clearInterval(countDownTimer)
      }
      let count = this.timeCount
      this.loading = false
      this.disabled = true

      countDownTimer = setInterval(
        (function countDown() {
          if (count > 0 && count <= that.timeCount) {
            that.text = COUNT_DOWN(count)
            count--
          } else {
            clearInterval(countDownTimer)
            that.disabled = false
            that.text = UN_SEND_TEXT
          }
          return countDown
        })(),
        1000
      )
    }
  }
}
</script>

<style lang="scss" scoped></style>
