<template>
  <div class="steps-container">
    <div
      class="steps-item"
      v-for="(item, index) in steps"
      :key="'step' + index"
      :class="{ 'is-active': item.value <= current }"
    >
      <div class="item-header">
        <div class="header-serial">{{ index + 1 }}</div>
        <div class="header-title">{{ item.title }}</div>
        <div class="header-line"></div>
      </div>
      <div class="item-desc">{{ item.desc }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BasicSteps',
  props: {
    steps: {
      type: Array,
      default: () => []
    },
    current: {
      default: 0
    }
  }
}
</script>

<style scoped lang="scss">
.steps-container {
  display: flex;
  .steps-item {
    color: rgba(0, 0, 0, 0.4);
    .item-header {
      font-size: 16px;
      line-height: 24px;
      display: flex;
      align-items: center;
      .header-serial {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        border: 1px solid rgba(0, 0, 0, 0.4);
        text-align: center;
      }
      .header-title {
        padding: 0 15px 0 16px;
      }
      .header-line {
        width: 108px;
        height: 2px;
        background: #dcdcdc;
      }
    }
    .item-desc {
      font-size: 14px;
      line-height: 22px;
      margin-top: 8px;
      padding-left: 40px;
    }
    &:last-child .item-header .header-line {
      display: none;
    }
    &.is-active {
      .item-header {
        .header-serial {
          @include border_color(--color-primary);
          @include background_color(--color-primary);
          @include font_color(--color-white);
        }
        .header-title {
          @include font_color(--color-primary);
        }
        .header-line {
          @include background_color(--color-primary);
        }
      }
      .item-desc {
        color: rgba(0, 0, 0, 0.6);
      }
    }
    & + .steps-item {
      padding-left: 16px;
    }
  }
}
</style>
