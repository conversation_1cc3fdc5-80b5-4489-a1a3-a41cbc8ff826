<template>
  <div id="app" class="wh100">
    <router-view />
  </div>
</template>

<script>
import WaterMark from '@/utils/watermark'
import { mapGetters } from 'vuex'
import dayjs from 'dayjs'
import { PLATFORM_NAME } from '@/settings'

export default {
  computed: {
    ...mapGetters(['name', 'userType'])
  },
  watch: {
    name(val) {
      // 管理端加水印
      if (this.userType === '01') {
        console.log(1)
        const text = `${val || ''} ${dayjs().format(
          'YYYY-MM-DD'
        )} ${PLATFORM_NAME}`
        WaterMark.set(text)
      }
    }
  }
}
</script>
