/*
 * @Author: 田柱
 * @Date: 2022-04-13 22:51:26
 * @LastEditTime: 2022-04-20 15:45:02
 * @Description: 配置文件
 */

// 网页标题
export const PLATFORM_NAME = '中安创谷CG+'

// 项目编号
export const PLATFORM_PREFIX = 'ZACG_'

// 默认主题类型
export const THEME = 'default'

// cookie过期时间，单位： 天
export const cookieOutTime = 365

// 代表最高权限标识
export const ALL_PERMISSION = '*:*:*'
//全部权限 *:*:*

// 全局搜索配置
export const GLOBAL_SEARCH_SAVE_LENGTH = 5

// 用户平台字典
export const platformMap = {
  '00': 'operate',
  '01': 'manage',
  '02': 'enterprise',
  '03': 'mechanism',
  '08': 'enterprise',
  '09': 'enterprise'
}

// 短信倒计时时间
export const SMS_CODE_TIME = 60

// 腾讯云文件上传配置
export const cosConfig = {
  Bucket: 'zacg-platform-1257783518' /* 填写自己的 bucket，必须字段 */,
  Region: 'ap-nanjing' /* 存储桶所在地域，必须字段 */,
  SliceSize:
    1024 *
    1024 *
    5 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
  Headers: {
    'x-cos-traffic-limit': 838860800
  },
  AsyncLimit: 10
}
