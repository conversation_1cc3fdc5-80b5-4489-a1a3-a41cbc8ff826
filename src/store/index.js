/*
 * @Author: 田柱
 * @Date: 2022-04-15 16:55:42
 * @LastEditTime: 2022-04-15 17:04:00
 * @Description: 项目数据仓库入口文件
 */

import Vue from 'vue'
import vueX from 'vuex'
import app from './modules/app'
import user from './modules/user'
import tagsView from './modules/tagsView'
import module from './modules/module'
import permission from './modules/permission'
import getters from './getters'

Vue.use(vueX)

const store = new vueX.Store({
  modules: {
    app,
    user,
    tagsView,
    module,
    permission
  },
  getters
})

export default store
