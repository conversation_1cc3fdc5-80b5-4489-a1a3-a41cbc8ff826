import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 主题html属性初始化
import { changeHtmlThemeAttr } from './utils/theme'
import { THEME } from './settings'
changeHtmlThemeAttr(THEME)

// Element-ui
import ElementUI from 'element-ui'

// 全局样式
import '@/styles/index.scss'
import '@/styles/theme/default.scss'

// svg-icons
import './icons'

// Element-ui插件注册
Vue.use(ElementUI, {
  size: 'small',
  zIndex: 1300
})

// 版本更新检查
if (process.env.NODE_ENV !== 'development') {
  require('@/utils/versionUpdate')
}

// 路由失败刷新重载
import '@/utils/reload'

// 消息提醒
import Message from '@/utils/message'
Vue.use(Message)

// 卡片
import BasicCard from './components/BasicCard'
Vue.component('basic-card', BasicCard)

// 表格
import DriveTable from './components/DriveTable'
Vue.component('drive-table', DriveTable)

// 抽屉
import BasicDrawer from './components/BasicDrawer'
Vue.component('basic-drawer', BasicDrawer)

// 表单
import DrivenForm from '@/components/DrivenForm'
Vue.component('driven-form', DrivenForm)

// 上传组件
import Uploader from '@/components/Uploader'
Vue.component('Uploader', Uploader)

// 弹框
import DialogCmp from '@/components/BasicDialog/index'
Vue.component('dialog-cmp', DialogCmp)

// 标签
import BasicTag from '@/components/BasicTag/index'
Vue.component('basic-tag', BasicTag)

// 空数据
import EmptyData from '@/components/EmptyData/index'
Vue.component('empty-data', EmptyData)

// 内容
import Tinymce from '@/components/Tinymce'
Vue.component('Tinymce', Tinymce)

// 会话及权限管理
import '@/permission'

// 数据埋点
// import '@/utils/pagePoint'

// 全局指令注册
import * as directives from '@/directive'
Object.keys(directives).forEach(k => {
  Vue.use(directives[k])
})

import * as filters from '@/filter'

Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

// 拖拽
import VueDND from 'awe-dnd'
// 添加至全局
Vue.use(VueDND)

// 全局按钮权限处理
import routeButtonsPermissions from '@/utils/btnsPermissions'
Vue.mixin(routeButtonsPermissions)

// 全局created执行和activated执行重复调用
import executeActivated from '@/utils/executeActivated'
Vue.mixin(executeActivated)

// Event Bus
Vue.prototype.$bus = new Vue()

Vue.config.productionTip = false

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
