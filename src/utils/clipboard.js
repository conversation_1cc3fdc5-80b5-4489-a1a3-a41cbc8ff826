/*
 * @Author: 田柱
 * @Date: 2022-04-14 14:58:37
 * @LastEditTime: 2022-04-14 14:58:37
 * @Description: 复制功能方法
 */

import { Message } from 'element-ui'
import Clipboard from 'clipboard'

function clipboardSuccess() {
  Message({
    message: '复制成功',
    type: 'success',
    duration: 1500
  })
}

function clipboardError() {
  Message({
    message: '复制失败',
    type: 'error'
  })
}

export default function handleClipboard(text, e) {
  const clipboard = new Clipboard(e.target, {
    text: () => text
  })
  clipboard.on('success', () => {
    clipboardSuccess()
    clipboard.off('error')
    clipboard.off('success')
    clipboard.destroy()
  })
  clipboard.on('error', () => {
    clipboardError()
    clipboard.off('error')
    clipboard.off('success')
    clipboard.destroy()
  })
  clipboard.onClick(e)
}
