/*
 * @Author: 田柱
 * @Date: 2022-04-13 22:56:59
 * @LastEditTime: 2022-04-14 11:09:45
 * @Description: 主题设置
 */

/**
 * TODO:
 * 暂时不考虑实现主题切换
 * 后续迭代版本实现
 */

/**
 * @description: 改变html的data-theme属性
 * @param {string} type 主题类型
 */
export function changeHtmlThemeAttr(type) {
  // 给html设置data-theme属性
  window.document.documentElement.setAttribute('data-theme', type)
}

/**
 * @description: 改变主题css引用，css比较大，建议在permission中根据缓存来初始化
 * @param {string} type 主题类型
 */
export function changeThemeLink(type) {
  // 创建link
  const link = document.createElement('link')
  link.href = `./theme/${type}/index.css`
  link.rel = 'stylesheet'
  // 删除default
  document.head.remove(document.querySelector('#ThemeLink'))
  // 插入
  document.head.insertBefore(link, document.querySelector('#Favicon'))
}

/**
 * @description: 设置主题
 * @param {string} type 主题类型
 */
export default function setTheme(type) {
  changeHtmlThemeAttr()
  // 给html的head中增加css引用
  changeThemeLink(type)
}
