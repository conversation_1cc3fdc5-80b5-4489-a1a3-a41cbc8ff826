/**
 * @description 查找组件方法
 * <AUTHOR>
 */

/**
 * 查找当前组件指定name的父级
 * @param 当前组件的vue实例
 * @param 指定向上查找的组件名称
 * @returns 查询到的组件实例
 */
export function findComponentUpward(context, componentName) {
  let parent = context.$parent
  let name = parent.$options.name
  while (parent && (!name || [componentName].indexOf(name) < 0)) {
    parent = parent.$parent
    if (parent) name = parent.$options.name
  }
  return parent
}

/**
 * 查找当前组件指定name的子级
 * @param 当前组件的vue实例
 * @param 指定向上查找的组件名称
 * @returns 查询到的组件实例
 */
export function findComponentDownward(context, componentName) {
  const childrens = context.$children
  let children = null

  if (childrens.length) {
    for (let child of childrens) {
      const name = child.$options.name
      if (name === componentName) {
        children = child
        break
      } else {
        children = findComponentDownward(child, componentName)
        if (children) break
      }
    }
  }

  return children
}
