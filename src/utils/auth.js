/*
 * @Descripttion: cookie操作token方法
 * @Author: 田柱
 * @Date: 2021-04-13 14:43:20
 * @LastEditTime: 2022-04-15 17:18:54
 */

import Cookies from 'js-cookie'
import { PLATFORM_PREFIX, cookieOutTime } from '@/settings'

// 设置cookie domain信息
// const isProduction = process.env.NODE_ENV === 'production'
let cookieParams = {}

// if (isProduction) {
//   cookieParams = {
//     domain: '.ipark.link'
//   }
// }

/* Token相关 */

// token标识
export const AccessTokenKey = `${PLATFORM_PREFIX}ACCESS_TOKEN`
export const RefreshTokenKey = `${PLATFORM_PREFIX}REFRESH_TOKEN`

// 获取 access token
export function getAccessToken() {
  return Cookies.get(AccessTokenKey, { ...cookieParams })
}

// 获取 refresh token
export function getRefreshToken() {
  return Cookies.get(RefreshTokenKey, { ...cookieParams })
}

// 设置token
export function setToken({ accessToken = '', refreshToken = '' }) {
  Cookies.set(AccessTokenKey, accessToken, {
    expires: cookieOutTime,
    ...cookieParams
  })
  Cookies.set(RefreshTokenKey, refreshToken, {
    expires: cookieOutTime,
    ...cookieParams
  })
}

// 移出token
export function removeToken() {
  Cookies.remove(AccessTokenKey, { ...cookieParams })
  Cookies.remove(RefreshTokenKey, { ...cookieParams })
}

/* tenant租户id相关 */

// tenant标识
export const TenantKey = `${PLATFORM_PREFIX}TENANT_KEY`

// 获取 tenant
export function getTenant() {
  return Cookies.get(TenantKey, { ...cookieParams })
}

// 设置tenant
export function setTenant(tenant) {
  Cookies.set(TenantKey, tenant, { expires: cookieOutTime, ...cookieParams })
}

// 移出tenant
export function removeTenant() {
  Cookies.remove(TenantKey, { ...cookieParams })
}
