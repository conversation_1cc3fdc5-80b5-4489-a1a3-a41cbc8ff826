/*
 * @Author: 田柱
 * @Date: 2022-04-20 15:05:35
 * @LastEditTime: 2022-04-20 15:47:15
 * @Description: storage公共方法
 */

import { PLATFORM_PREFIX } from '@/settings'

// 需要存储的方法名
const STORAGE_KEYS = [
  'USER_INFO', // 用户信息
  'GLOBAL_SEARCH', // 全局查询
  'HISTORY_VISIT', // 历史访问菜单
  'TIPS_USER_ID', // userId
  'HIGH_SEARCH', // 高级搜索参数
  'LOGIN_ERR_COUNT' // 登录错误次数
]

/* local storage方法 */

/**
 * @description: local storage set方法
 * @param {string} key 存储的键名
 * @param {string | object} value 存储的数据
 */
function set(key, value) {
  value = typeof value === 'object' ? JSON.stringify(value) : value
  window.localStorage.setItem(key, value)
}

/**
 * @description: local storage get方法
 * @param {string} key 获取的键名
 */
function get(key) {
  let value = window.localStorage.getItem(key)
  try {
    value = JSON.parse(value)
    return value
  } catch (e) {
    return value
  }
}

/**
 * @description: local storage remove方法
 * @param {string} key 清除的键名
 */
function remove(key) {
  return window.localStorage.removeItem(key)
}

// local storage 方法聚合
const LOCAL_METHODS = {
  SET: set,
  GET: get,
  REMOVE: remove
}

// 暴露的方法
const local = {}
/**
 * 遍历生成方法
 * 生成 SET_USER_INFO GET_USER_INFO REMOVE_USER_INFO
 */
STORAGE_KEYS.forEach(key => {
  for (const method in LOCAL_METHODS) {
    local[`${method}_${key}`] = LOCAL_METHODS[method].bind(
      null,
      `${PLATFORM_PREFIX}${key}`
    )
  }
})

export { local }
