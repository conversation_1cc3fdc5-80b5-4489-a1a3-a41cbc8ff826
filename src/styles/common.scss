@import './theme/_handle.scss';

$--global-font-family: helvetica neue, tahoma, pingfangsc-regular,
  microsoft yahei, myriad pro, hiragino sans gb, sans-serif;

/*纯数字或英文换行*/
* {
  word-break: break-all;
}

html,
body {
  width: 100%;
  height: 100%;
  background-color: #fff;
  font-family: $--global-font-family;
}

/* 定宽情况下样式 */
.lateral-wrapper {
  width: 1200px;
  margin: 0 auto;
}

/* element 样式 */

/* tabs */
.el-tabs__nav-wrap {
  &::after {
    height: 1px !important;
  }
}

/* input */
.el-input.is-active .el-input__inner,
.el-input__inner:focus {
  + .el-input__prefix {
    @include font_color('--color-primary');
  }
}
.el-input-group__append {
  @include font_color('--color-text-primary');
}

// 错误情况下，图标也变色
.el-form-item.is-error .el-input__inner,
.el-form-item.is-error .el-input__inner:focus,
.el-form-item.is-error .el-textarea__inner,
.el-form-item.is-error .el-textarea__inner:focus,
.el-message-box__input input.invalid,
.el-message-box__input input.invalid:focus {
  + .el-input__prefix {
    @include font_color('--color-danger');
  }
}

// 顶部信息message样式
.el-message {
  min-width: auto;
  @include background_color('--color-text-primary');
  border: 0;
  padding: 15px 16px;
  top: 60px !important;
}

// 标记文字不居中
.el-badge__content {
  line-height: 16px !important;
}

.el-dropdown-menu {
  padding: 0 !important;

  .el-dropdown-menu__item {
    border-radius: 3px;
    padding: 0;
    @include font_color('--color-text-primary');
    .el-link {
      width: 100%;
      padding: 6px 16px;
      &:hover:after {
        border: none;
      }
    }
    .icon-group {
      display: flex;
      align-items: center;
      font-size: 14px;
      .svg-icon {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }

    &:last-of-type {
      margin-bottom: 0;
    }
  }
  // 自定义设置padding间距
  &.p-dropdown-wrapper {
    .el-dropdown-menu__item {
      padding: 6px 16px;
    }
  }
}

/* dialog */
.el-dialog__wrapper {
  display: flex;
  align-items: center;
}
.el-dialog {
  position: fixed;
  margin-top: 0 !important;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .el-dialog__header {
    height: 48px;
    margin-bottom: 0;
    padding: 0 24px;
    box-sizing: border-box;
    font-size: 16px;
    line-height: 48px;
    font-weight: 900;
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-base);
  }

  .el-dialog__body {
    display: flex;
    min-height: 30px;
    max-height: 78vh;
    overflow: hidden;
    padding: 0;
  }
}

.no-footer-dialog {
  .el-dialog__footer {
    display: none;
  }
}
.drag-dialog {
  position: relative;
  display: flex;
  justify-content: center;

  .el-dialog {
    position: fixed;

    .el-dialog__header {
      user-select: none;
    }
  }
}

/* drawer */
.el-drawer__container {
  .el-drawer__header {
    height: 56px;
    margin-bottom: 0;
    padding: 0 16px;
    box-sizing: border-box;
    font-size: 14px;
    border-bottom-width: 1px;
    border-style: solid;
    @include border_color(--border-color-base);
    color: rgba(0, 0, 0, 0.9);

    .el-drawer__close-btn {
      font-size: 16px;
    }
  }

  //.el-drawer__body {
  //  overflow: hidden;
  //}
}

/* 面包屑 */
.el-breadcrumb__separator {
  font-weight: normal;
  margin: 0 4px;
  font-size: 12px;
  position: relative;
  top: -2px;
}

.el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
  font-weight: normal;
  @include font_color(--color-text-secondary);
}

/* 表单form */
.el-form-item__label {
  @include font_color(--color-text-primary);
}

/* 表格 */
.no-hover {
  .el-table .el-table__body tr.el-table__row:hover {
    background-color: inherit;
  }
  .el-table--striped
    .el-table__body
    tr.el-table__row--striped
    td.el-table__cell {
    background-color: inherit;
  }
  .el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
    background-color: inherit;
  }
}
.el-table {
  thead {
    th.el-table__cell {
      padding: 10px 0;
      border-right-width: 0;

      .cell {
        @include font_color(--color-text-secondary);
      }
    }
  }

  tbody {
    .el-table__cell {
      padding: 13px 0;
      border-right-width: 0;

      .cell {
        @include font_color(--color-text-primary);
      }
    }
  }

  .cell {
    font-size: 14px;
    line-height: 22px;
  }

  .caret-wrapper {
    height: 17px;

    .sort-caret {
      &.ascending {
        top: -4px;
      }

      &.descending {
        bottom: -2px;
      }
    }
  }
}

// fix: 解决表格数据请求时出现边框跳动问题
.el-table--border.el-loading-parent--relative {
  border-top-width: 0;

  thead {
    th.el-table__cell {
      border-top-width: 1px;
      border-left-width: 0;
      border-style: solid;
      @include border_color(--border-color-light);

      &:first-of-type {
        border-left-width: 1px;
      }
    }
  }

  tbody {
    tr td.el-table__cell {
      &:first-of-type {
        border-left-width: 1px;
        border-top-width: 0;
        border-style: solid;
        @include border_color(--border-color-light);
      }
    }
  }

  .el-table__empty-block {
    border-left-width: 1px;
    border-style: solid;
    @include border_color(--border-color-light);
  }
}

/* 按钮button */
.el-button {
  &.el-button--text {
    font-size: 14px;
  }
  &.el-button--mini,
  &.el-button--small {
    span {
      display: flex;
      align-items: center;

      .svg-icon {
        width: 16px;
        height: 16px;
      }

      span {
        display: inline-block;
        margin-left: 8px;
      }
    }
  }
}

/* 树样式tree */
.el-tree {
  .el-tree-node {
    position: relative;
  }

  .el-tree-node__content {
    padding-left: 0; // 缩进量
    width: 100%;
    overflow: hidden;

    .el-tree-node__label {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  .el-tree-node__children {
    padding-left: 16px; // 缩进量
  }

  .el-tree-node::before {
    content: '';
    height: 100%;
    width: 1px;
    position: absolute;
    left: -3px;
    top: -26px;
    border-width: 1px;
    border-left: 1px solid;
    @include border_color(--border-color-base);
  }

  // 当前层最后⼀个节点的竖线⾼度固定
  .el-tree-node:last-child::before {
    height: 38px; // 可以⾃⼰调节到合适数值
  }

  .el-tree-node::after {
    content: '';
    width: 24px;
    height: 20px;
    position: absolute;
    left: -3px;
    top: 12px;
    border-width: 1px;
    border-top: 1px solid;
    @include border_color(--border-color-base);
  }

  & > .el-tree-node::after {
    border-top: none;
  }

  & > .el-tree-node::before {
    border-left: none;
  }

  .el-tree-node__expand-icon {
    font-size: 16px;
    // 叶⼦节点（⽆⼦节点）
    &.is-leaf {
      color: transparent;
      // display: none; // 也可以去掉
    }
  }
}

/* tag */
.el-tag {
  border-width: 0;
  &.el-tag--info {
    background-color: #f4f4f5;
    border-color: #e9e9eb;
    color: #909399;
  }
  &.round {
    border-radius: 100px;
  }
}

/* textarea */
.el-textarea {
  .el-input__count {
    @include font_color(--color-text-secondary);
    background: transparent;
  }
  .el-textarea__inner {
    resize: none;
    font-family: '';
  }
}

.el-tooltip__popper {
  max-width: 30%;
  &.is-light {
    border: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    .popper__arrow {
      border: none;
      &:after {
        border: none;
        width: 10px;
        height: 10px;
        background: #fff;
        transform: rotate(45deg);
        margin-left: calc(50% - 5px);
      }
    }
  }
}

/* 富文本统一样式 */
.rich-text {
  * {
    //font-family: $--global-font-family !important;
    max-width: 100%;
    margin: revert;
    padding: revert;
    font: revert;
    font-weight: revert;
    line-height: normal;
    white-space: pre-wrap;
  }
  p {
    background: transparent !important;
  }
  strong {
    font-weight: bold !important;
  }
  em {
    font-style: italic;
  }
  video {
    width: 100%;
  }
}

/* loading样式 */
.el-loading-spinner {
  top: 100px !important;
  margin-top: 0 !important;
}

/* nprogress样式 */
$--nprogress-mix-color: mix(themed(--color-success), #fff, 80%);
#nprogress {
  .bar {
    background: $--nprogress-mix-color !important; //自定义颜色
    height: 3px;
    .peg {
      box-shadow: 0 0 10px $--nprogress-mix-color,
        0 0 5px $--nprogress-mix-color;
    }
  }
  .spinner {
    top: 18px;
    .spinner-icon {
      border-top-color: themed(--color-primary);
      border-left-color: themed(--color-primary);
    }
  }
}

/* nprogress样式 */
$--nprogress-mix-color: mix(themed(--color-success), #fff, 80%);
#nprogress {
  .bar {
    background: $--nprogress-mix-color !important; //自定义颜色
    height: 3px;
    .peg {
      box-shadow: 0 0 10px $--nprogress-mix-color,
        0 0 5px $--nprogress-mix-color;
    }
  }
  .spinner {
    top: 18px;
    .spinner-icon {
      border-top-color: themed(--color-primary);
      border-left-color: themed(--color-primary);
    }
  }
}

// toast样式
.el-message__content {
  @include font_color(--color-white);
}

.el-message-box__status.el-icon-warning {
  color: #e34d59;
}
// el-button-info禁用样式
.el-button--info.is-disabled,
.el-button--info.is-disabled:active,
.el-button--info.is-disabled:focus,
.el-button--info.is-disabled:hover {
  color: rgba(0, 0, 0, 0.7);
}

// 联级选择器选中样式
.el-cascader-menu {
  padding: 8px 0;
}
.el-cascader-node {
  width: 240px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  background-color: #fff8f3;
  font-weight: normal;
  &:hover {
    background-color: #fff8f3;
  }
}
// el-select下拉框选中样式
.el-select-dropdown__item.selected {
  font-weight: normal;
}
.el-select-dropdown__empty {
  padding: 10px;
  text-align: left;
}
// 去除清空图标
.el-range-editor--small .el-range__icon {
  //width: 0;
  opacity: 0;
}
// 日期选择器分隔字宽度自适应
.el-range-editor--small .el-range-separator {
  width: auto;
}
// el-table fixed导致边框丢失问题
thead th:not(.is-hidden):last-child {
  right: 1px;
}
.el-table__row {
  td:not(.is-hidden):last-child {
    right: 1px;
  }
}

/* 详情表格样式 */
.detail-table-wrapper {
  .el-table {
    border-radius: 0;
  }
  .el-table thead th.el-table__cell {
    font-weight: 400;
    .cell {
      @include font_color(--color-text-regular);
    }
  }
  .el-table--striped {
    .el-table__body {
      tr.el-table__row--striped {
        td.el-table__cell {
          @include background_color_mix(--color-primary, #ffffff, 98%);
        }
      }
    }
  }
  .el-pagination {
    border: none;
  }
}

// 左侧菜单滚动条白边问题
.scrollbar-container ::-webkit-scrollbar-track-piece {
  background-color: transparent;
}
