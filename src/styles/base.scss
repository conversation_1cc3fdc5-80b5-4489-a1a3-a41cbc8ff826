/*
 * @Author: 田柱
 * @Date: 2022-04-14 00:39:41
 * @LastEditTime: 2022-04-14 23:15:56
 * @Description: 规范性基础类目->样式
 */

@import './theme/_handle.scss';

// 字体
@font-face {
  font-family: 'password';
  src: url(../assets/font/Password.ttf);
}
.font-password {
  font-family: 'password';
}

// 字体大小
@for $i from 6 through 42 {
  .font-size-#{$i} {
    font-size: $i + px;
  }
  .line-height-#{$i} {
    line-height: $i + px;
  }
}

// 间距
@for $i from 1 through 44 {
  .m-#{$i} {
    margin: $i + px;
  }
  .m-t-#{$i} {
    margin-top: $i + px;
  }
  .m-b-#{$i} {
    margin-bottom: $i + px;
  }
  .m-l-#{$i} {
    margin-left: $i + px;
  }
  .m-r-#{$i} {
    margin-right: $i + px;
  }

  .p-#{$i} {
    padding: $i + px;
  }
  .p-t-#{$i} {
    padding-top: $i + px;
  }
  .p-b-#{$i} {
    padding-bottom: $i + px;
  }
  .p-l-#{$i} {
    padding-left: $i + px;
  }
  .p-r-#{$i} {
    padding-right: $i + px;
  }
  b-r-#{$i} {
    border-radius: $i + px;
  }
}

// 字体颜色配置 color
// prettier-ignore
@each $type,
  $color
  in (
    -primary: --color-primary,
    -success: --color-success,
    -warning: --color-warning,
    -danger: --color-danger,
    -info: --color-info,
    -white: --color-white,
    -black: --color-black,
    -text-primary: --color-text-primary,
    -text-regular: --color-text-regular,
    -text-secondary: --color-text-secondary,
      -text-placeholder: --color-text-placeholder,
      -text-disabled: --font-color-disabled-base
    )
{
  .color#{$type} {
    @include font_color($color);
  }
}

// 背景颜色配置 background-color
// prettier-ignore
@each $type,
  $color
  in (
    -primary: --color-primary,
    -success: --color-success,
    -warning: --color-warning,
    -danger: --color-danger,
    -info: --color-info,
    -white: --color-white,
    -black: --color-black,
    -base: --background-color-base
  )
{
  .bg#{$type} {
    @include background-color($color);
  }
}

// 边框颜色配置 border-color
// prettier-ignore
@each $type,
  $color
  in (
    -base: --border-color-base,
    -light: --border-color-light,
    -lighter: --border-color-lighter,
    -extra-light: --border-color-extra-light
  )
{
  .border-color#{$type} {
    @include border_color($color);
  }
}

// flex布局
.flex {
  display: flex;
  &.flex-direction-column {
    flex-direction: column;
  }
  &.align-items-start {
    align-items: flex-start;
  }
  &.align-items-center {
    align-items: center;
  }
  &.align-items-end {
    align-items: flex-end;
  }
  &.justify-content-start {
    justify-content: flex-start;
  }
  &.justify-content-center {
    justify-content: center;
  }
  &.justify-content-end {
    justify-content: flex-end;
  }
  &.justify-content-between {
    justify-content: space-between;
  }
  &.justify-content-around {
    justify-content: space-around;
  }
  &.flex-nowrap {
    flex-wrap: nowrap;
  }
  &.flex-wrap {
    flex-wrap: wrap;
  }
  &.flex-center-center {
    align-items: center;
    justify-content: center;
  }
  &.flex-center-between {
    align-items: center;
    justify-content: space-between;
  }
}

// 宽高
.w100 {
  width: 100%;
}
.h100 {
  height: 100%;
}
.wh100 {
  @extend .w100;
  @extend .h100;
}
.min-w100 {
  min-width: 100%;
}
.min-h100 {
  min-height: 100%;
}
.min-wh100 {
  @extend .min-w100;
  @extend .min-h100;
}

@for $i from 1 through 20 {
  .w#{$i*5} {
    width: $i * 5%;
  }
  .h#{$i*5} {
    height: $i * 5%;
  }
}

// 字体加粗
.font-strong {
  font-weight: 700;
}

// 鼠标小手
.pointer {
  cursor: pointer;
}

// 定位相关
.pos-relative {
  position: relative;
}
.pos-absolute {
  position: absolute;
}
.pos-fixed {
  position: fixed;
}

// 元素类型
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}

// 隐藏
.overflow-hidden {
  overflow: hidden;
}

// 文字一行省略
.line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  /*! autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}
.line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /*! autoprefixer: ignore next */
  -webkit-box-orient: vertical;
}
/* 高德地图 */
.amap-sug-result {
  z-index: 100000;
}

/* 文字换行 */
.word-nowrap {
  white-space: nowrap;
}

/* 盒子模型 */
.border-box {
  box-sizing: border-box;
}
