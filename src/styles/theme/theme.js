/*
 * @Author: 田柱
 * @Date: 2022-04-13 15:37:02
 * @LastEditTime: 2022-04-14 11:44:12
 * @Description: element主题生成
 */

const path = require('path')
const et = require('element-themex')

/**
 * 主题生成这块的问题比较多
 * element-theme主要使用了node-sass，并采用gulp进行编译
 * 所以这两个库与nodejs的版本有很大关联，经测试，采用node v12.22.10版本
 * 同时element-themex存在bug，components选项不生效
 * 所以需要在项目yarn install后修改element-themex源码
 * 目录：\node_modules\element-themex\lib\task.js exports.build方法下 30行
 * 代码增加：
 *
 * // 修改build逻辑
 * // 解决build下传入components不生效问题
 * if(opts.components) {
 *   const components = opts.components.concat(['base'])
 *   cssFiles = '{' + components.join(',') + '}'
 * }
 */

/**
 * 主题样式
 * 为后续做主题切换做准备
 * 思路还是，生成多主题样式，js动态引入主题类型
 */
const themeObj = {
  default: 'default.scss'
}

/**
 * et.run为异步函数
 * 包装成promise，方便顺序执行
 * 防止变量污染
 * @param {string} theme 主题名称
 * @param {object} themeObj 主题集合
 * @returns
 */
function etRun(theme, themeObj) {
  return new Promise(resolve => {
    et.run(
      {
        config: path.resolve(__dirname, `./${themeObj[theme]}`),
        out: path.resolve(__dirname, `../../../public/theme/${theme}`),
        theme: 'element-theme-chalk',
        browsers: ['> 1%', 'last 2 versions', 'not dead'],
        minimize: true, // 是否压缩
        components: ['index'] // 考虑到全量导入，只需要生成index.css即可
      },
      () => {
        resolve()
      }
    )
  })
}

// 编译自定义主题
etRun('default', themeObj)
// ;(async () => {
//   for (const theme in themeObj) {
//     await etRun(theme, themeObj)
//   }
// })()
