/*
 * @Author: 田柱
 * @Date: 2022-04-13 16:14:22
 * @LastEditTime: 2022-04-14 19:16:40
 * @Description: default element-ui配置项
 */

/**
 * 配置好了之后，运行 yarn run theme
 * 将编译后的文件输出到public对应的theme文件中
 */

/* 可配置的颜色区域 */
/* 主要颜色配置 */
$--color-primary: #ed7b2f !default;
$--color-white: #fff !default;
$--color-black: #000 !default;
$--color-success: #00a870 !default;
$--color-warning: #ed7b2f !default;
$--color-danger: #e34d59 !default;
$--color-info: #e7e7e7 !default;

$--color-text-primary: #191919 !default;
$--color-text-regular: #666 !default;
$--color-text-secondary: #999 !default;
$--color-text-placeholder: #bdbdbd !default;

$--border-color-base: #dcdcdc !default;
$--border-color-light: #e7e7e7 !default;
$--border-color-lighter: #ebeef5 !default;
$--border-color-extra-light: #f2f6fc !default;

$--background-color-regular: #e7e7e7 !default;
$--background-color-base: #eee !default;
$--background-color-light: #f3f3f3 !default;

$--font-color-disabled-base: rgba(0, 0, 0, 0.7) !default;
$--icon-color: #666 !default;

/* 主要颜色配置 结束 */
$--box-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04) !default;
$--box-shadow-dark: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.12) !default;
$--box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !default;
$--checkbox-disabled-input-fill: #edf2fc !default;
$--select-multiple-input-color: #666 !default;
$--select-dropdown-empty-color: #999 !default;
$--message-background-color: #edf2fc !default;
$--cascader-tag-background: #f0f2f5;
$--tab-border-line: 1px solid #e4e4e4 !default;
$--dialog-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3) !default;
$--table-fixed-box-shadow: 0 0 10px rgba(0, 0, 0, 0.12) !default;
$--datepicker-inner-border-color: #e4e4e4 !default;
$--datepicker-cell-hover-color: #fff !default;
$--carousel-arrow-background: rgba(31, 45, 61, 0.11) !default;
$--carousel-arrow-hover-background: rgba(31, 45, 61, 0.23) !default;
$--calendar-selected-background-color: #f2f8fe !default;
$--avatar-font-color: #fff !default;
$--avatar-background-color: #c0c4cc !default;
$--descriptions-item-bordered-label-background: #fafafa !default;
$--skeleton-color: #f2f2f2 !default;
$--skeleton-to-color: #e6e6e6 !default;
$--svg-monochrome-grey: #dcdde0 !default;

/* 可配置的颜色结束 */

/* layout 颜色值 */

/* navbar */
// navbar 高度
$--navbar-height: 56px;
// navbar背景色
$--navbar-background-color: #fff;
// tabbar hover时背景色
$--navbar-tabbar-hover: $--background-color-regular;
// tabbar active时背景色
$--navbar-tabbar-active: $--background-color-base;
// navbar字体图标颜色
$--navbar-text-icon-color: $--color-text-regular;
// navbar字体图标颜色 active下
$--navbar-text-icon-color-active: $--color-text-primary;

/* lateral */
// navbar 高度
$--lateral-navbar-height: 56px;
// navbar背景色
$--lateral-navbar-background-color: $--color-primary;
// navbar border颜色
$--lateral-navbar-border-color: #fff0e5;
// navbar字体图标颜色
$--lateral-navbar-text-icon-color: $--color-white;

/* sidebar */
// sidebar宽度
$--sidebar-width: 232px;
// sidebar缩放宽度
$--sidebar-collapse-width: 56px;
// sidebar背景色
$--sidebar-background-color: #242424;
// sidebar菜单选项文字颜色
$--sidebar-text-color: #878787;
// sidebar菜单选项文字hover颜色
$--sidebar-text-hover-color: mix(#fff, #242424, 90%);
// sidebar菜单选项文字active颜色
$--sidebar-text-active-color: $--color-white;
// sidebar菜单选项文字click颜色
$--sidebar-text-click-color: mix(#fff, #242424, 90%);
// sidebar菜单选项hover颜色
$--sidebar-menu-hover: #777;
// sidebar菜单选项click时颜色
$--sidebar-menu-click: #666;
// sidebar菜单选项active颜色
$--sidebar-menu-active: $--color-primary;

/* 内容区域背景 */
$--app-main-bg: #f0f2f5;
$--lateral-app-main-bg: #fff;

/* sidebar 颜色值 结束 */

/* layout scss */
#app {
  // default layout
  .main-container {
    // height: calc(100% - #{$--navbar-height});
    height: 100%;
    position: absolute;
    width: calc(100% - #{$--sidebar-collapse-width});
    // width: calc(100% - #{$--sidebar-width});
    left: $--sidebar-collapse-width;
    // left: $--sidebar-width;
    bottom: 0;
    right: 0;
    transition: all 0.28s;
    overflow: hidden;
    z-index: 10;
  }
  .sidebar-container {
    transition: width 0.28s;
    width: $--sidebar-width !important;
    background-color: $--sidebar-background-color;
    // height: calc(100% - #{$--navbar-height});
    height: 100%;
    top: 0;
    background-repeat: no-repeat;
    position: fixed;
    font-size: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    user-select: none;
    padding: 0;
    // padding: 14px 8px 48px 8px;
    // 折叠区域样式
    .collapse-area {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 16px;
      .icon-area {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $--sidebar-text-color;
        cursor: pointer;
        border-radius: 3px;
        .svg-icon {
          width: 20px;
          height: 20px;
          margin-right: 0;
        }
        &:hover {
          background-color: $--sidebar-menu-hover !important;
          color: $--sidebar-text-hover-color !important;
        }
        &:active {
          background-color: $--sidebar-menu-click !important;
          color: $--sidebar-text-click-color !important;
        }
      }
    }
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out,
        0s padding-right ease-in-out;
    }
    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }
    .el-scrollbar__bar.is-vertical {
      right: 0;
    }
    .el-scrollbar {
      height: 100%;
    }
    &.has-logo {
      .el-scrollbar {
        height: 100%;
      }
    }
    .is-horizontal {
      display: none;
    }
    a {
      display: inline-block;
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
    .svg-icon {
      margin-right: 8px;
      width: 20px;
      height: 20px;
    }
    .el-menu {
      border: none;
      height: 100%;
      width: 100% !important;
    }
    .el-submenu.is-opened {
      .submenu-title-noDropdown,
      .el-submenu__title {
        display: flex;
        align-items: center;
        height: 36px;
        line-height: 36px;
        margin-bottom: 4px;
        color: $--sidebar-text-hover-color !important;
      }
    }
    .submenu-title-noDropdown,
    .el-submenu__title {
      background-color: transparent;
      color: $--sidebar-text-color !important;
      box-sizing: border-box;
      height: 36px;
      line-height: 36px;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      border-radius: 3px;
      padding-left: 16px !important;
      .svg-icon {
        color: #dfdfdf;
      }
      .el-submenu__icon-arrow {
        color: $--sidebar-text-hover-color !important;
      }
      &:hover {
        background-color: $--sidebar-menu-hover !important;
        color: $--sidebar-text-hover-color !important;
      }
      &:active {
        background-color: $--sidebar-menu-click !important;
        color: $--sidebar-text-click-color !important;
      }
    }
    .el-menu-item {
      background-color: $--sidebar-menu-hover;
      color: $--sidebar-text-color !important;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      height: 36px;
      line-height: 36px;
      margin-bottom: 4px;
      border-radius: 3px;
      padding-left: 16px !important;
      .svg-icon {
        color: #dfdfdf;
      }
      span {
        line-height: 20px;
      }
      &:hover {
        background-color: $--sidebar-menu-hover !important;
        color: $--sidebar-text-hover-color !important;
      }
      &:active {
        background-color: $--sidebar-menu-click !important;
        color: $--sidebar-text-click-color !important;
      }
      &.is-active {
        background-color: $--sidebar-menu-active !important;
        color: $--sidebar-text-active-color !important;
        border-radius: 3px;
        .svg-icon {
          color: #fff;
        }
      }
    }
    .is-active > .el-submenu__title {
      height: 36px;
      line-height: 36px;
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      color: $--sidebar-text-hover-color !important;
      font-weight: 700;
      span {
        line-height: 20px;
      }
    }
    & .nest-menu .el-submenu > .el-submenu__title,
    & .el-submenu .el-menu-item {
      margin-bottom: 4px;
      height: 36px;
      line-height: 36px;
      padding-left: 44px !important;
      // min-width: $--sidebar-width !important;
      background-color: transparent !important;
      display: flex;
      align-items: center;
      border-radius: 3px;
      .svg-icon {
        display: none;
      }
      &:hover {
        background-color: $--sidebar-menu-hover !important;
        color: $--sidebar-text-hover-color !important;
      }
      &:active {
        background-color: $--sidebar-menu-click !important;
        color: $--sidebar-text-click-color !important;
      }
      &.is-active {
        background-color: $--sidebar-menu-active !important;
        color: $--sidebar-text-active-color !important;
        border-radius: 3px;
      }
    }
  }
  .hideSidebar {
    .sidebar-container {
      width: $--sidebar-collapse-width !important;
      .collapse-area {
        padding: 0 16px;
      }
      .menu-wrapper {
        margin-bottom: 10px;
      }
    }
    .main-container {
      left: $--sidebar-collapse-width;
      width: calc(100% - #{$--sidebar-collapse-width});
    }
    .svg-icon {
      margin-right: 0;
    }
    .submenu-title-noDropdown {
      padding: 0 !important;
      position: relative;
      .el-tooltip {
        padding: 0 !important;
        .svg-icon {
          margin-left: 8px;
        }
      }
    }
    .el-submenu {
      overflow: hidden;
      & > .el-submenu__title {
        height: 36px;
        line-height: 36px;
        margin-bottom: 4px;
        padding: 0 !important;
        display: flex;
        align-items: center;
        span,
        i {
          display: none;
        }
        .svg-icon {
          width: 20px;
          height: 20px;
          margin-left: 8px;
        }
      }
    }
    .el-menu--collapse {
      .el-menu-item {
        .el-tooltip {
          padding: 0 !important;
          display: flex !important;
          align-items: center;
          justify-content: center;
        }
      }
      .el-submenu {
        & > .el-submenu__title {
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 36px;
          line-height: 36px;
          .svg-icon {
            margin-left: 0;
          }
        }
        &.is-active {
          .el-submenu__title {
            background-color: $--sidebar-menu-active !important;
            color: $--sidebar-text-active-color !important;
          }
          .svg-icon {
            color: #fff;
          }
        }
      }
    }
  }
  .noMenu {
    .sidebar-container {
      width: 0 !important;
    }
    .main-container {
      left: 0;
      width: 100%;
    }
  }
  .el-menu--collapse .el-menu .el-submenu {
    // min-width: $--sidebar-width !important;
  }
  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
  .fixed-header {
    height: $--navbar-height;
  }
  .app-main {
    background-color: $--app-main-bg;
    padding: 8px 16px 16px;
  }
  .navbar {
    background-color: $--navbar-background-color;
    border-bottom: 1px solid $--border-color-light;
    .right-menu {
      .right-meun-item {
        div {
          color: $--color-text-primary;
        }
        &:hover {
          background-color: $--background-color-light;
        }
        &:active {
          background-color: $--background-color-regular;
        }
      }
    }
  }
  .screen-full {
    .svg-icon {
      color: $--color-text-primary;
    }
  }
  .tab-bar-container {
    color: $--navbar-text-icon-color;
    .tab-bar-item {
      color: $--navbar-text-icon-color;
      &:hover {
        background-color: $--navbar-tabbar-hover;
      }
      &.current {
        background-color: $--navbar-tabbar-active;
        color: $--navbar-text-icon-color-active;
      }
    }
  }
  .avatar-container {
    .user-info {
      color: $--navbar-text-icon-color;
    }
  }
  .navbar-search {
    .search-input {
      background-color: $--color-white;
      .el-autocomplete {
        .el-input__suffix {
          .el-input__clear {
            color: $--color-primary;
          }
        }
      }
      .svg-icon {
        color: $--color-primary;
      }
      &.collapse {
        border: 1px solid $--color-primary;
      }
    }
  }
  .tags-view-container {
    background: $--color-white;
    border: none;
    //border-bottom: 1px solid $--border-color-light;
    //box-shadow: $--box-shadow-base;
    .tags-view-wrapper {
      .tags-view-item {
        border: 1px solid $--border-color-base;
        //color: $--color-text-primary;
        color: $--navbar-text-icon-color;
        background: $--color-white;
        &.active {
          background-color: $--color-primary !important;
          //color: $--color-primary;
          color: $--color-white !important;
          border: 1px solid $--color-primary;
          // border-color: $--color-primary;
          &::before {
            background: $--color-white !important;
          }
        }
      }
    }
    .contextmenu {
      background: $--color-white;
    }
  }

  // lateral layout
  .lateral-layout {
    width: 100%;
    height: 100%;
    .fixed-header {
      position: relative;
      height: $--lateral-navbar-height;
      z-index: 11;
      .navbar {
        height: 100%;
        background-color: $--lateral-navbar-background-color;
        border-bottom: 1px solid $--lateral-navbar-border-color;
        .top-menu {
          .menu {
            color: $--lateral-navbar-text-icon-color;
          }
        }
        .right-menu {
          .right-meun-item {
            div {
              color: $--lateral-navbar-text-icon-color;
            }
            &:hover {
              background-color: transparent;
            }
            &:active {
              background-color: transparent;
            }
          }
        }
      }
    }
    .main-container {
      width: 100%;
      left: 0;
      height: calc(100% - 56px);
      .app-main {
        padding: 0;
        background-color: $--lateral-app-main-bg;
      }
    }
  }
}

// 菜单折叠下样式
.el-menu--vertical {
  .el-menu {
    margin-left: 16px;
    background-color: $--sidebar-background-color !important;
    margin-right: 0;
    border-radius: 3px;
    padding: 8px !important;
    .svg-icon {
      margin-right: 8px;
    }
  }
  .nest-menu .el-submenu > .el-submenu__title,
  .el-menu-item {
    display: flex;
    align-items: center;
    height: 36px;
    line-height: 36px;
    margin-bottom: 4px;
    border-radius: 3px;
    padding: 0 16px !important;
    color: $--sidebar-text-color !important;
    &:hover {
      background-color: $--sidebar-menu-hover !important;
      color: $--sidebar-text-hover-color !important;
    }
    &:active {
      background-color: $--sidebar-menu-click !important;
      color: $--sidebar-text-click-color !important;
    }
    &.is-active {
      background-color: $--sidebar-menu-active !important;
      color: $--sidebar-text-active-color !important;
      border-radius: 3px;
    }
  }
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;
    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}

.el-input.is-disabled .el-input__inner,
.el-textarea.is-disabled .el-textarea__inner,
.el-select.is-disabled .el-input__inner,
.el-date-editor.is-disabled input,
.el-radio__input.is-disabled + .el-radio__label,
.el-checkbox__input.is-disabled + .el-checkbox__label,
.el-switch.is-disabled .el-switch__label,
.el-cascader .el-input.is-disabled .el-input__inner,
.el-slider.is-disabled .el-slider__bar,
.el-slider.is-disabled .el-slider__button,
.el-radio-button__orig-radio:disabled:checked+.el-radio-button__inner{
  color: $--font-color-disabled-base;
}


/* sidebar scss 结束 */

$--all-transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1) !default;
$--fade-transition: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--fade-linear-transition: opacity 200ms linear !default;
$--md-fade-transition: transform 300ms cubic-bezier(0.23, 1, 0.32, 1),
  opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) !default;
$--border-transition-base: border-color 0.2s
  cubic-bezier(0.645, 0.045, 0.355, 1) !default;
$--color-transition-base: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1) !default;
$--color-primary-light-1: mix($--color-white, $--color-primary, 10%) !default;
$--color-primary-light-2: mix($--color-white, $--color-primary, 20%) !default;
$--color-primary-light-3: mix($--color-white, $--color-primary, 30%) !default;
$--color-primary-light-4: mix($--color-white, $--color-primary, 40%) !default;
$--color-primary-light-5: mix($--color-white, $--color-primary, 50%) !default;
$--color-primary-light-6: mix($--color-white, $--color-primary, 60%) !default;
$--color-primary-light-7: mix($--color-white, $--color-primary, 70%) !default;
$--color-primary-light-8: mix($--color-white, $--color-primary, 80%) !default;
$--color-primary-light-9: mix($--color-white, $--color-primary, 90%) !default;

$--color-success-light: mix($--color-white, $--color-success, 80%) !default;
$--color-warning-light: mix($--color-white, $--color-warning, 80%) !default;
$--color-danger-light: mix($--color-white, $--color-danger, 80%) !default;
$--color-info-light: mix($--color-white, $--color-info, 80%) !default;
$--color-success-lighter: mix($--color-white, $--color-success, 90%) !default;
$--color-warning-lighter: mix($--color-white, $--color-warning, 90%) !default;
$--color-danger-lighter: mix($--color-white, $--color-danger, 90%) !default;
$--color-info-lighter: mix($--color-white, $--color-info, 90%) !default;
$--link-color: $--color-primary-light-2 !default;
$--link-hover-color: $--color-primary !default;
$--border-width-base: 1px !default;
$--border-style-base: solid !default;
$--border-color-hover: $--color-text-placeholder !default;
$--border-base: $--border-width-base $--border-style-base $--border-color-base !default;
$--border-radius-base: 3px !default;
$--border-radius-small: 2px !default;
$--border-radius-circle: 100% !default;
$--border-radius-zero: 0 !default;
$--fill-base: $--color-white !default;
$--font-path: 'fonts' !default;
$--font-display: 'auto' !default;
$--font-size-extra-large: 20px !default;
$--font-size-large: 18px !default;
$--font-size-medium: 16px !default;
$--font-size-base: 14px !default;
$--font-size-small: 13px !default;
$--font-size-extra-small: 12px !default;
$--font-weight-primary: 500 !default;
$--font-weight-secondary: 100 !default;
$--font-line-height-primary: 24px !default;
$--font-line-height-secondary: 16px !default;
$--size-base: 14px !default;
$--index-normal: 1 !default;
$--index-top: 1000 !default;
$--index-popper: 2000 !default;
$--disabled-fill-base: $--background-color-base !default;
$--disabled-color-base: $--color-text-placeholder !default;
$--disabled-border-base: $--border-color-light !default;
$--icon-color-base: $--color-info !default;
$--checkbox-font-size: 14px !default;
$--checkbox-font-weight: $--font-weight-primary !default;
$--checkbox-font-color: $--color-text-regular !default;
$--checkbox-input-height: 14px !default;
$--checkbox-input-width: 14px !default;
$--checkbox-border-radius: $--border-radius-small !default;
$--checkbox-background-color: $--color-white !default;
$--checkbox-input-border: $--border-base !default;
$--checkbox-disabled-border-color: $--border-color-base !default;
$--checkbox-disabled-icon-color: $--color-text-placeholder !default;
$--checkbox-disabled-checked-input-fill: $--border-color-extra-light !default;
$--checkbox-disabled-checked-input-border-color: $--border-color-base !default;
$--checkbox-disabled-checked-icon-color: $--color-text-placeholder !default;
$--checkbox-checked-font-color: $--color-primary !default;
$--checkbox-checked-input-border-color: $--color-primary !default;
$--checkbox-checked-background-color: $--color-primary !default;
$--checkbox-checked-icon-color: $--fill-base !default;
$--checkbox-input-border-color-hover: $--color-primary !default;
$--checkbox-bordered-height: 40px !default;
$--checkbox-bordered-padding: 9px 20px 9px 10px !default;
$--checkbox-bordered-medium-padding: 7px 20px 7px 10px !default;
$--checkbox-bordered-small-padding: 5px 15px 5px 10px !default;
$--checkbox-bordered-mini-padding: 3px 15px 3px 10px !default;
$--checkbox-bordered-medium-input-height: 14px !default;
$--checkbox-bordered-medium-input-width: 14px !default;
$--checkbox-bordered-medium-height: 36px !default;
$--checkbox-bordered-small-input-height: 12px !default;
$--checkbox-bordered-small-input-width: 12px !default;
$--checkbox-bordered-small-height: 32px !default;
$--checkbox-bordered-mini-input-height: 12px !default;
$--checkbox-bordered-mini-input-width: 12px !default;
$--checkbox-bordered-mini-height: 28px !default;
$--checkbox-button-checked-background-color: $--color-primary !default;
$--checkbox-button-checked-font-color: $--color-white !default;
$--checkbox-button-checked-border-color: $--color-primary !default;
$--radio-font-size: $--font-size-base !default;
$--radio-font-weight: $--font-weight-primary !default;
$--radio-font-color: $--color-text-regular !default;
$--radio-input-height: 14px !default;
$--radio-input-width: 14px !default;
$--radio-input-border-radius: $--border-radius-circle !default;
$--radio-input-background-color: $--color-white !default;
$--radio-input-border: $--border-base !default;
$--radio-input-border-color: $--border-color-base !default;
$--radio-icon-color: $--color-white !default;
$--radio-disabled-input-border-color: $--disabled-border-base !default;
$--radio-disabled-input-fill: $--disabled-fill-base !default;
$--radio-disabled-icon-color: $--disabled-fill-base !default;
$--radio-disabled-checked-input-border-color: $--disabled-border-base !default;
$--radio-disabled-checked-input-fill: $--disabled-fill-base !default;
$--radio-disabled-checked-icon-color: $--color-text-placeholder !default;
$--radio-checked-font-color: $--color-primary !default;
$--radio-checked-input-border-color: $--color-primary !default;
$--radio-checked-input-background-color: $--color-white !default;
$--radio-checked-icon-color: $--color-primary !default;
$--radio-input-border-color-hover: $--color-primary !default;
$--radio-bordered-height: 40px !default;
$--radio-bordered-padding: 12px 20px 0 10px !default;
$--radio-bordered-medium-padding: 10px 20px 0 10px !default;
$--radio-bordered-small-padding: 8px 15px 0 10px !default;
$--radio-bordered-mini-padding: 6px 15px 0 10px !default;
$--radio-bordered-medium-input-height: 14px !default;
$--radio-bordered-medium-input-width: 14px !default;
$--radio-bordered-medium-height: 36px !default;
$--radio-bordered-small-input-height: 12px !default;
$--radio-bordered-small-input-width: 12px !default;
$--radio-bordered-small-height: 32px !default;
$--radio-bordered-mini-input-height: 12px !default;
$--radio-bordered-mini-input-width: 12px !default;
$--radio-bordered-mini-height: 28px !default;
$--radio-button-font-size: $--font-size-base !default;
$--radio-button-checked-background-color: $--color-primary !default;
$--radio-button-checked-font-color: $--color-white !default;
$--radio-button-checked-border-color: $--color-primary !default;
$--radio-button-disabled-checked-fill: $--border-color-extra-light !default;
$--select-border-color-hover: $--border-color-hover !default;
$--select-disabled-border: $--disabled-border-base !default;
$--select-font-size: $--font-size-base !default;
$--select-close-hover-color: $--color-text-secondary !default;
$--select-input-color: $--color-text-placeholder !default;
$--select-input-focus-border-color: $--color-primary !default;
$--select-input-font-size: 14px !default;
$--select-option-color: $--color-text-regular !default;
$--select-option-disabled-color: $--color-text-placeholder !default;
$--select-option-disabled-background: $--color-white !default;
$--select-option-height: 34px !default;
$--select-option-hover-background: $--background-color-base !default;
$--select-option-selected-font-color: $--color-primary !default;
$--select-option-selected-hover: $--background-color-base !default;
$--select-group-color: $--color-info !default;
$--select-group-height: 30px !default;
$--select-group-font-size: 12px !default;
$--select-dropdown-background: $--color-white !default;
$--select-dropdown-shadow: $--box-shadow-light !default;
$--select-dropdown-max-height: 274px !default;
$--select-dropdown-padding: 6px 0 !default;
$--select-dropdown-empty-padding: 10px 0 !default;
$--select-dropdown-border: solid 1px $--border-color-light !default;
$--alert-padding: 8px 16px !default;
$--alert-border-radius: $--border-radius-base !default;
$--alert-title-font-size: 13px !default;
$--alert-description-font-size: 12px !default;
$--alert-close-font-size: 12px !default;
$--alert-close-customed-font-size: 13px !default;
$--alert-success-color: $--color-success-lighter !default;
$--alert-info-color: $--color-info-lighter !default;
$--alert-warning-color: $--color-warning-lighter !default;
$--alert-danger-color: $--color-danger-lighter !default;
$--alert-icon-size: 16px !default;
$--alert-icon-large-size: 28px !default;
$--messagebox-title-color: $--color-text-primary !default;
$--msgbox-width: 420px !default;
$--msgbox-border-radius: 4px !default;
$--messagebox-font-size: $--font-size-large !default;
$--messagebox-content-font-size: $--font-size-base !default;
$--messagebox-content-color: $--color-text-regular !default;
$--messagebox-error-font-size: 12px !default;
$--msgbox-padding-primary: 15px !default;
$--messagebox-success-color: $--color-success !default;
$--messagebox-info-color: $--color-info !default;
$--messagebox-warning-color: $--color-warning !default;
$--messagebox-danger-color: $--color-danger !default;
$--message-shadow: $--box-shadow-base !default;
$--message-min-width: 380px !default;
$--message-padding: 15px 15px 15px 20px !default;
$--message-close-icon-color: $--color-text-placeholder !default;
$--message-close-size: 16px !default;
$--message-close-hover-color: $--color-text-secondary !default;
$--message-success-font-color: $--color-success !default;
$--message-info-font-color: $--color-info !default;
$--message-warning-font-color: $--color-warning !default;
$--message-danger-font-color: $--color-danger !default;
$--notification-width: 330px !default;
$--notification-padding: 14px 26px 14px 13px !default;
$--notification-radius: 8px !default;
$--notification-shadow: $--box-shadow-light !default;
$--notification-border-color: $--border-color-lighter !default;
$--notification-icon-size: 24px !default;
$--notification-close-font-size: $--message-close-size !default;
$--notification-group-margin-left: 13px !default;
$--notification-group-margin-right: 8px !default;
$--notification-content-font-size: $--font-size-base !default;
$--notification-content-color: $--color-text-regular !default;
$--notification-title-font-size: 16px !default;
$--notification-title-color: $--color-text-primary !default;
$--notification-close-color: $--color-text-secondary !default;
$--notification-close-hover-color: $--color-text-regular !default;
$--notification-success-icon-color: $--color-success !default;
$--notification-info-icon-color: $--color-info !default;
$--notification-warning-icon-color: $--color-warning !default;
$--notification-danger-icon-color: $--color-danger !default;
$--input-font-size: $--font-size-base !default;
$--input-font-color: $--color-text-regular !default;
$--input-width: 140px !default;
$--input-height: 40px !default;
$--input-border: $--border-base !default;
$--input-border-color: $--border-color-base !default;
$--input-border-radius: $--border-radius-base !default;
$--input-border-color-hover: $--border-color-hover !default;
$--input-background-color: $--color-white !default;
$--input-fill-disabled: $--disabled-fill-base !default;
$--input-color-disabled: $--font-color-disabled-base !default;
$--input-icon-color: $--color-text-placeholder !default;
$--input-placeholder-color: $--color-text-placeholder !default;
$--input-max-width: 314px !default;
$--input-hover-border: $--border-color-hover !default;
$--input-clear-hover-color: $--color-text-secondary !default;
$--input-focus-border: $--color-primary !default;
$--input-focus-fill: $--color-white !default;
$--input-disabled-fill: $--disabled-fill-base !default;
$--input-disabled-border: $--disabled-border-base !default;
$--input-disabled-color: $--disabled-color-base !default;
$--input-disabled-placeholder-color: $--color-text-placeholder !default;
$--input-medium-font-size: 14px !default;
$--input-medium-height: 36px !default;
$--input-small-font-size: 13px !default;
$--input-small-height: 32px !default;
$--input-mini-font-size: 12px !default;
$--input-mini-height: 28px !default;
$--cascader-menu-font-color: $--color-text-regular !default;
$--cascader-menu-selected-font-color: $--color-primary !default;
$--cascader-menu-fill: $--fill-base !default;
$--cascader-menu-font-size: $--font-size-base !default;
$--cascader-menu-radius: $--border-radius-base !default;
$--cascader-menu-border: solid 1px $--border-color-light !default;
$--cascader-menu-shadow: $--box-shadow-light !default;
$--cascader-node-background-hover: $--background-color-base !default;
$--cascader-node-color-disabled: $--color-text-placeholder !default;
$--cascader-color-empty: $--color-text-placeholder !default;
$--group-option-flex: 0 0 (1/5) * 100% !default;
$--group-option-offset-bottom: 12px !default;
$--group-option-fill-hover: rgba($--color-black, 0.06) !default;
$--group-title-color: $--color-black !default;
$--group-title-font-size: $--font-size-base !default;
$--group-title-width: 66px !default;
$--tab-font-size: $--font-size-base !default;
$--tab-header-color-active: $--color-text-secondary !default;
$--tab-header-color-hover: $--color-text-regular !default;
$--tab-header-color: $--color-text-regular !default;
$--tab-header-fill-active: rgba($--color-black, 0.06) !default;
$--tab-header-fill-hover: rgba($--color-black, 0.06) !default;
$--tab-vertical-header-width: 90px !default;
$--tab-vertical-header-count-color: $--color-white !default;
$--tab-vertical-header-count-fill: $--color-text-secondary !default;
$--button-font-size: $--font-size-base !default;
$--button-font-weight: $--font-weight-primary !default;
$--button-border-radius: $--border-radius-base !default;
$--button-padding-vertical: 12px !default;
$--button-padding-horizontal: 20px !default;
$--button-medium-font-size: $--font-size-base !default;
$--button-medium-border-radius: $--border-radius-base !default;
$--button-medium-padding-vertical: 10px !default;
$--button-medium-padding-horizontal: 20px !default;
$--button-small-font-size: 12px !default;
$--button-small-border-radius: #{$--border-radius-base - 1} !default;
$--button-small-padding-vertical: 8px !default;
$--button-small-padding-horizontal: 15px !default;
$--button-mini-font-size: 12px !default;
$--button-mini-border-radius: #{$--border-radius-base - 1} !default;
$--button-mini-padding-vertical: 5px !default;
$--button-mini-padding-horizontal: 8px !default;
$--button-default-font-color: $--color-text-regular !default;
$--button-default-background-color: $--color-white !default;
$--button-default-border-color: $--border-color-base !default;
$--button-disabled-font-color: $--color-text-placeholder !default;
$--button-disabled-background-color: $--color-white !default;
$--button-disabled-border-color: $--border-color-lighter !default;
$--button-primary-border-color: $--color-primary !default;
$--button-primary-font-color: $--color-white !default;
$--button-primary-background-color: $--color-primary !default;
$--button-success-border-color: $--color-success !default;
$--button-success-font-color: $--color-white !default;
$--button-success-background-color: $--color-success !default;
$--button-warning-border-color: $--color-warning !default;
$--button-warning-font-color: $--color-white !default;
$--button-warning-background-color: $--color-warning !default;
$--button-danger-border-color: $--color-danger !default;
$--button-danger-font-color: $--color-white !default;
$--button-danger-background-color: $--color-danger !default;
$--button-info-border-color: $--color-info !default;
$--button-info-font-color: $--color-text-primary !default;
$--button-info-background-color: $--color-info !default;
$--button-hover-tint-percent: 20% !default;
$--button-active-shade-percent: 10% !default;
$--cascader-height: 200px !default;
$--switch-on-color: $--color-primary !default;
$--switch-off-color: $--border-color-base !default;
$--switch-font-size: $--font-size-base !default;
$--switch-core-border-radius: 10px !default;
// height||Other|4 TODO: width 代码写死的40px 所以下面这三个属性都没意义
$--switch-width: 40px !default;
$--switch-height: 20px !default;
$--switch-button-size: 16px !default;
$--dialog-background-color: $--color-white !default;
$--dialog-title-font-size: $--font-size-large !default;
$--dialog-content-font-size: 14px !default;
$--dialog-font-line-height: $--font-line-height-primary !default;
$--dialog-padding-primary: 20px !default;
$--table-border-color: $--border-color-light !default;
$--table-border: 1px solid $--table-border-color !default;
$--table-font-color: $--color-text-regular !default;
$--table-header-font-color: $--color-text-secondary !default;
$--table-row-hover-background-color: $--background-color-base !default;
$--table-current-row-background-color: $--color-primary-light-9 !default;
$--table-header-background-color: $--color-white !default;
$--pagination-font-size: 13px !default;
$--pagination-background-color: $--color-white !default;
$--pagination-font-color: $--color-text-primary !default;
$--pagination-border-radius: 3px !default;
$--pagination-button-color: $--color-text-primary !default;
$--pagination-button-width: 35.5px !default;
$--pagination-button-height: 28px !default;
$--pagination-button-disabled-color: $--color-text-placeholder !default;
$--pagination-button-disabled-background-color: $--color-white !default;
$--pagination-hover-color: $--color-primary !default;
$--popup-modal-background-color: $--color-black !default;
$--popup-modal-opacity: 0.5 !default;
$--popover-background-color: $--color-white !default;
$--popover-font-size: $--font-size-base !default;
$--popover-border-color: $--border-color-lighter !default;
$--popover-arrow-size: 6px !default;
$--popover-padding: 12px !default;
$--popover-padding-large: 18px 20px !default;
$--popover-title-font-size: 16px !default;
$--popover-title-font-color: $--color-text-primary !default;
$--tooltip-fill: $--color-text-primary !default;
$--tooltip-color: $--color-white !default;
$--tooltip-font-size: 12px !default;
$--tooltip-border-color: $--color-text-primary !default;
$--tooltip-arrow-size: 6px !default;
$--tooltip-padding: 10px !default;
$--tag-info-color: $--color-info !default;
$--tag-primary-color: $--color-primary !default;
$--tag-success-color: $--color-success !default;
$--tag-warning-color: $--color-warning !default;
$--tag-danger-color: $--color-danger !default;
$--tag-font-size: 12px !default;
$--tag-border-radius: 4px !default;
$--tag-padding: 0 10px !default;
$--tree-node-hover-background-color: $--background-color-base !default;
$--tree-font-color: $--color-text-regular !default;
$--tree-expand-icon-color: $--color-text-placeholder !default;
$--dropdown-menu-box-shadow: $--box-shadow-light !default;
$--dropdown-menuItem-hover-fill: $--background-color-light !default;
$--dropdown-menuItem-hover-color: $--color-text-primary !default;
$--badge-background-color: $--color-danger !default;
$--badge-radius: 10px !default;
$--badge-font-size: 12px !default;
$--badge-padding: 6px !default;
$--badge-size: 18px !default;
$--card-border-color: $--border-color-lighter !default;
$--card-border-radius: 4px !default;
$--card-padding: 20px !default;
$--slider-main-background-color: $--color-primary !default;
$--slider-runway-background-color: $--border-color-light !default;
$--slider-button-hover-color: mix($--color-primary, black, 97%) !default;
$--slider-stop-background-color: $--color-white !default;
$--slider-disable-color: $--color-text-placeholder !default;
$--slider-margin: 16px 0 !default;
$--slider-border-radius: 3px !default;
$--slider-height: 6px !default;
$--slider-button-size: 16px !default;
$--slider-button-wrapper-size: 36px !default;
$--slider-button-wrapper-offset: -15px !default;
$--steps-border-color: $--disabled-border-base !default;
$--steps-border-radius: 4px !default;
$--steps-padding: 20px !default;
$--menu-item-font-size: $--font-size-base !default;
$--menu-item-font-color: $--color-text-primary !default;
$--menu-background-color: $--color-white !default;
$--menu-item-hover-fill: $--color-primary-light-9 !default;
$--rate-height: 20px !default;
$--rate-font-size: $--font-size-base !default;
$--rate-icon-size: 18px !default;
$--rate-icon-margin: 6px !default;
$--rate-icon-color: $--color-text-placeholder !default;
$--datepicker-font-color: $--color-text-regular !default;
$--datepicker-off-font-color: $--color-text-placeholder !default;
$--datepicker-header-font-color: $--color-text-regular !default;
$--datepicker-icon-color: $--color-text-primary !default;
$--datepicker-border-color: $--disabled-border-base !default;
$--datepicker-inrange-background-color: $--border-color-extra-light !default;
$--datepicker-inrange-hover-background-color: $--border-color-extra-light !default;
$--datepicker-active-color: $--color-primary !default;
$--datepicker-hover-font-color: $--color-primary !default;
$--loading-spinner-size: 42px !default;
$--loading-fullscreen-spinner-size: 50px !default;
$--scrollbar-background-color: rgba($--color-text-secondary, 0.3) !default;
$--scrollbar-hover-background-color: rgba(
  $--color-text-secondary,
  0.5
) !default;
$--carousel-arrow-font-size: 12px !default;
$--carousel-arrow-size: 36px !default;
$--carousel-indicator-width: 30px !default;
$--carousel-indicator-height: 2px !default;
$--carousel-indicator-padding-horizontal: 4px !default;
$--carousel-indicator-padding-vertical: 12px !default;
$--carousel-indicator-out-color: $--border-color-hover !default;
$--collapse-border-color: $--border-color-lighter !default;
$--collapse-header-height: 48px !default;
$--collapse-header-background-color: $--color-white !default;
$--collapse-header-font-color: $--color-text-primary !default;
$--collapse-header-font-size: 13px !default;
$--collapse-content-background-color: $--color-white !default;
$--collapse-content-font-size: 13px !default;
$--collapse-content-font-color: $--color-text-primary !default;
$--transfer-border-color: $--border-color-lighter !default;
$--transfer-border-radius: $--border-radius-base !default;
$--transfer-panel-width: 200px !default;
$--transfer-panel-header-height: 40px !default;
$--transfer-panel-header-background-color: $--background-color-base !default;
$--transfer-panel-footer-height: 40px !default;
$--transfer-panel-body-height: 246px !default;
$--transfer-item-height: 30px !default;
$--transfer-filter-height: 32px !default;
$--header-padding: 0 20px !default;
$--footer-padding: 0 20px !default;
$--main-padding: 20px !default;
$--timeline-node-size-normal: 12px !default;
$--timeline-node-size-large: 14px !default;
$--timeline-node-color: $--border-color-light !default;
$--backtop-background-color: $--color-white !default;
$--backtop-font-color: $--color-primary !default;
$--backtop-hover-background-color: $--border-color-extra-light !default;
$--link-font-size: $--font-size-base !default;
$--link-font-weight: $--font-weight-primary !default;
$--link-default-font-color: $--color-text-regular !default;
$--link-default-active-color: $--color-primary !default;
$--link-disabled-font-color: $--color-text-placeholder !default;
$--link-primary-font-color: $--color-primary !default;
$--link-success-font-color: $--color-success !default;
$--link-warning-font-color: $--color-warning !default;
$--link-danger-font-color: $--color-danger !default;
$--link-info-font-color: $--color-info !default;
$--calendar-border: $--table-border !default;
$--calendar-cell-width: 85px !default;
$--form-label-font-size: $--font-size-base !default;
$--avatar-text-font-size: 14px !default;
$--avatar-icon-font-size: 18px !default;
$--avatar-border-radius: $--border-radius-base !default;
$--avatar-large-size: 40px !default;
$--avatar-medium-size: 36px !default;
$--avatar-small-size: 28px !default;
$--empty-padding: 40px 0 !default;
$--empty-image-width: 160px !default;
$--empty-description-margin-top: 20px !default;
$--empty-bottom-margin-top: 20px !default;
$--descriptions-header-margin-bottom: 20px !default;
$--descriptions-title-font-size: 16px !default;
$--descriptions-table-border: 1px solid $--border-color-lighter !default;
$--result-padding: 40px 30px !default;
$--result-icon-font-size: 64px !default;
$--result-title-font-size: 20px !default;
$--result-title-margin-top: 20px !default;
$--result-subtitle-margin-top: 10px !default;
$--result-extra-margin-top: 30px !default;
$--result-info-color: $--color-info !default;
$--result-success-color: $--color-success !default;
$--result-warning-color: $--color-warning !default;
$--result-danger-color: $--color-danger !default;
$--sm: 768px !default;
$--md: 992px !default;
$--lg: 1200px !default;
$--xl: 1920px !default;
$--breakpoints: (
  'xs': (
    max-width: $--sm - 1
  ),
  'sm': (
    min-width: $--sm
  ),
  'md': (
    min-width: $--md
  ),
  'lg': (
    min-width: $--lg
  ),
  'xl': (
    min-width: $--xl
  )
);
$--breakpoints-spec: (
  'xs-only': (
    max-width: $--sm - 1
  ),
  'sm-and-up': (
    min-width: $--sm
  ),
  'sm-only': '(min-width: #{$--sm}) and (max-width: #{$--md - 1})',
  'sm-and-down': (
    max-width: $--md - 1
  ),
  'md-and-up': (
    min-width: $--md
  ),
  'md-only': '(min-width: #{$--md}) and (max-width: #{$--lg - 1})',
  'md-and-down': (
    max-width: $--lg - 1
  ),
  'lg-and-up': (
    min-width: $--lg
  ),
  'lg-only': '(min-width: #{$--lg}) and (max-width: #{$--xl - 1})',
  'lg-and-down': (
    max-width: $--xl - 1
  ),
  'xl-only': (
    min-width: $--xl
  )
);
