@import './_themes.scss';

// fix: As of Dart Sass 2.0.0, !global assignments won't be able to declare new variables.
// Consider adding `$theme-map: null` at the root of the stylesheet.
$theme-map: null;

// 遍历主题map
@mixin themeify {
  @each $theme-name, $theme-map in $themes {
    //!global 把局部变量强升为全局变量
    $theme-map: $theme-map !global;
    //判断html的data-theme的属性值  #{}是sass的插值表达式
    //& sass嵌套里的父容器标识   @content是混合器插槽，像vue的slot
    [data-theme='#{$theme-name}'] & {
      @content;
    }
  }
}

// 声明一个根据Key获取颜色的function
@function themed($key) {
  @return map-get($theme-map, $key);
}

// 获取背景颜色
@mixin background_color($color) {
  @include themeify {
    background-color: themed($color) !important;
  }
}

// 获取背景mix颜色
@mixin background_color_mix($color, $source, $percent) {
  @include themeify {
    background-color: mix($source, themed($color), $percent) !important;
  }
}

// 获取字体颜色
@mixin font_color($color) {
  @include themeify {
    color: themed($color) !important;
  }
}

// 获取字体mix颜色
@mixin font_color_mix($color, $source, $percent) {
  @include themeify {
    color: mix($source, themed($color), $percent);
  }
}

// 获取边框颜色
@mixin border_color($color) {
  @include themeify {
    border-color: themed($color) !important;
  }
}

// 获取边框mix颜色
@mixin border_color_mix($color, $source, $percent) {
  @include themeify {
    border-color: mix($source, themed($color), $percent);
  }
}
