/*
 * @Author: 田柱
 * @Date: 2022-04-13 22:42:59
 * @LastEditTime: 2022-04-14 17:24:18
 * @Description: 系统主题配置出口文件
 */

/**
 * 只涉及到业务页面编写过程中的样式定义
 * 由于scss的局限性，样式主要是拷贝对应主题的element配置项
 * 同时去除 $ ，和scss的变量名冲突
 */

$themes: (
  default: (
    --color-primary: #ed7b2f,
    --color-white: #fff,
    --color-black: #000,
    --color-success: #00a870,
    --color-warning: #ed7b2f,
    --color-danger: #e34d59,
    --color-info: #909399,
    --color-text-primary: #191919,
    --color-text-regular: #666,
    --color-text-secondary: #999,
    --color-text-placeholder: #bdbdbd,
    --border-color-base: #dcdcdc,
    --border-color-light: #e7e7e7,
    --border-color-lighter: #ebeef5,
    --border-color-extra-light: #f2f6fc,
    --background-color-regular: #e7e7e7,
    --background-color-base: #f5f7fa,
    --background-color-light: #f3f3f3,
    --font-color-disabled-base: rgba(0,0,0,0.7),
    --icon-color: #666
  )
);
