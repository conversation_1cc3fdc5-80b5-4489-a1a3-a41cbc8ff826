{"extends": ["stylelint-config-standard", "stylelint-config-prettier", "stylelint-config-html"], "plugins": ["stylelint-order", "stylelint-scss"], "customSyntax": "postcss-html", "ignorePath": ".<PERSON><PERSON><PERSON><PERSON>", "rules": {"indentation": 2, "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["v-deep"]}], "selector-pseudo-element-colon-notation": null, "number-leading-zero": null, "no-descending-specificity": null, "font-family-no-missing-generic-family-keyword": null, "selector-type-no-unknown": null, "at-rule-no-unknown": null, "no-duplicate-selectors": null, "declaration-empty-line-before": null, "no-empty-source": null, "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "deep"]}], "max-nesting-depth": null, "max-line-length": null, "selector-max-compound-selectors": null, "selector-no-qualifying-type": null, "selector-class-pattern": null, "function-parentheses-newline-inside": null, "color-function-notation": "legacy", "alpha-value-notation": "number", "rule-empty-line-before": null, "comment-empty-line-before": null}}