/*
 * @Author: 田柱
 * @Date: 2022-04-13 11:50:14
 * @LastEditTime: 2022-11-08 17:34:32
 * @Description: vue配置文件
 */
const path = require('path')
function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  publicPath: './',
  productionSourceMap: false,
  devServer: {
    disableHostCheck: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        // target: 'http://***********:48080/admin-api',
        // target: 'http://***********:48080/admin-api',
        // target: 'http://**************:9099/zacg-test/admin-api',
        // target: 'https://zacg.cgplus.com.cn/zacg/admin-api',
        // target: 'http://***********:48080/admin-api',
        // target: 'http://***********:48080/admin-api',
        // target: 'http://***********:48080/admin-api',
        // target: 'http://***********:48080/admin-api',
        // target: 'http://**********:48080/admin-api',
        target: 'http://**********:30000/zacg-serve/admin-api',
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      },
      '/finance': {
        target: 'http://***********:48080/admin-api',
        changeOrigin: true,
        pathRewrite: {
          '^/finance': ''
        }
      },
      '/appapi': {
        target: 'http://***********:48080/admin-api',
        changeOrigin: true,
        pathRewrite: {
          '^/appapi': ''
        }
      }
    }
  },
  css: {
    sourceMap: false
  },
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'scss',
      patterns: [resolve('src/styles/theme/_handle.scss')]
    }
  },
  configureWebpack: config => {
    config.devtool = 'source-map'
    // config.output.filename = `[name].[hash].js`
    // config.output.chunkFilename = `[name].[hash].js`
  },
  chainWebpack: config => {
    // preload配置
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // 忽略runtime类js文件
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // 删除预加载，防止项目增大，文件集中加载导致的首屏加载慢
    config.plugins.delete('prefetch')

    // svg-sprite-loader 处理svg icon
    config.module.rule('svg').exclude.add(resolve('src/icons')).end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    // 增加stylelint插件webpack配置
    // config.plugin('StylelintPlugin').use('stylelint-webpack-plugin', [
    //   {
    //     files: ['**/*.{html,vue,css,sass}'],
    //     fix: false,
    //     cache: false,
    //     emitError: true,
    //     failOnError: false
    //   }
    // ])
    // 增加prettier插件
    // config.plugin('PrettierPlugin').use('prettier-webpack-plugin')

    config.when(process.env.NODE_ENV !== 'development', config => {
      /**
       * runtime 代码由于只是驱动不同路由页面的关系，代码量比较少
       * 使用 script-ext-html-webpack-plugin 插件将其内链在 index.html 中比较友好
       */
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            inline: /runtime\..*\.js$/
          }
        ])
        .end()

      // 定义代码分割规则
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial' // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/ // in order to adapt to cnpm
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true
          }
        }
      })
      config.optimization.runtimeChunk('single')

      // gzip压缩
      config
        .plugin('CompressionWebpackPlugin')
        .use('compression-webpack-plugin', [
          {
            filename: '[path][base].gz', // 目标资源名称
            algorithm: 'gzip',
            test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i, // 处理所有匹配此 {RegExp} 的资源
            threshold: 10240, // 只处理比这个值大的资源，按字节计算
            minRatio: 0.8 // 只有压缩率比这个值小的资源才会被处理
          }
        ])
    })
  },
  // enabled by default if the machine has more than 1 cores
  parallel: require('os').cpus().length > 1
}
